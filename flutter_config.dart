// config.dart
class AppConfig {
  // Server Configuration
  static const String baseUrl = 'http://your-server-url.com'; // Replace with your actual server URL
  static const String webSocketUrl = 'http://your-server-url.com'; // Same as baseUrl for Socket.IO
  
  // API Endpoints
  static const String apiPrefix = '/api/auth/mobile';
  
  // WebSocket Events (matching your backend)
  static const String wsEventNewMessage = 'newMessage';
  static const String wsEventConversationStarted = 'conversationStarted';
  static const String wsEventMessageRead = 'messageRead';
  static const String wsEventMessageReadReceipt = 'messageReadReceipt';
  static const String wsEventQueueUpdate = 'queueUpdate';
  static const String wsEventRequestQueueStatus = 'requestQueueStatus';
  static const String wsEventNotifyQueueChange = 'notifyQueueChange';
  
  // Firebase Configuration
  static const String fcmVapidKey = 'your-vapid-key'; // Replace with your VAPID key
  
  // Local Notification Configuration
  static const String notificationChannelId = 'yachfin_notifications';
  static const String notificationChannelName = 'YachFin Notifications';
  static const String notificationChannelDescription = 'Notifications for appointments, messages, and queue updates';
  
  // App Configuration
  static const int defaultNotificationLimit = 20;
  static const int defaultMessageLimit = 50;
  static const Duration webSocketReconnectDelay = Duration(seconds: 5);
  static const Duration webSocketTimeout = Duration(seconds: 30);
  
  // Queue Configuration
  static const Duration queueStatusRefreshInterval = Duration(minutes: 1);
  static const int maxQueueMembers = 100;
  
  // Message Configuration
  static const int maxMessageLength = 1000;
  static const Duration messageTimeout = Duration(seconds: 30);
  
  // Development/Debug flags
  static const bool enableDebugLogging = true; // Set to false in production
  static const bool enableWebSocketLogging = true; // Set to false in production
}

// API Endpoints Helper
class ApiEndpoints {
  static const String _base = AppConfig.apiPrefix;
  
  // Authentication
  static const String login = '$_base/auth/login';
  static const String register = '$_base/auth/register';
  static const String refreshToken = '$_base/auth/refresh';
  
  // Conversations
  static const String conversations = '$_base/conversations';
  static String conversationMessages(int conversationId) => '$_base/conversations/$conversationId/messages';
  static const String startConversation = '$_base/conversations/start';
  
  // Messages
  static const String sendMessage = '$_base/messages';
  static const String markMessageRead = '$_base/messages/read';
  
  // Notifications
  static const String notifications = '$_base/notifications';
  static String markNotificationRead(int notificationId) => '$_base/notifications/$notificationId/read';
  static const String markAllNotificationsRead = '$_base/notifications/read-all';
  static const String saveFcmToken = '$_base/fcm-token';
  
  // Queue
  static String queueMembers(int queueId) => '$_base/queues/$queueId/members';
  static const String swapRequest = '$_base/queue/swap-request';
  
  // Provider specific (if needed)
  static const String providerProfile = '/api/auth/providers/profile';
  static const String providerServices = '/api/auth/providers/services';
}

// Error Messages
class ErrorMessages {
  static const String networkError = 'Network connection error. Please check your internet connection.';
  static const String serverError = 'Server error. Please try again later.';
  static const String authenticationError = 'Authentication failed. Please log in again.';
  static const String webSocketConnectionError = 'Failed to connect to real-time services.';
  static const String notificationPermissionDenied = 'Notification permission denied. Enable notifications in settings.';
  static const String messageFailedToSend = 'Failed to send message. Please try again.';
  static const String conversationLoadError = 'Failed to load conversations.';
  static const String notificationLoadError = 'Failed to load notifications.';
  static const String queueStatusError = 'Failed to get queue status.';
  static const String swapRequestError = 'Failed to request position swap.';
}

// Success Messages
class SuccessMessages {
  static const String messageSent = 'Message sent successfully';
  static const String notificationMarkedRead = 'Notification marked as read';
  static const String allNotificationsMarkedRead = 'All notifications marked as read';
  static const String swapRequestSent = 'Position swap request sent';
  static const String webSocketConnected = 'Connected to real-time services';
}

// WebSocket Connection States
enum WebSocketState {
  disconnected,
  connecting,
  connected,
  reconnecting,
  error,
}

// Message Status
enum MessageStatus {
  sending,
  sent,
  delivered,
  read,
  failed,
}

// Queue Status
enum QueueStatusType {
  pending,
  confirmed,
  inProgress,
  completed,
  cancelled,
  noShow,
}

// Notification Types
enum NotificationType {
  message,
  appointment,
  queueUpdate,
  swapRequest,
  general,
}

// App Themes (if you want to support dark/light mode)
class AppThemes {
  static const primaryColor = Color(0xFF19727F); // Matching your backend color
  static const secondaryColor = Color(0xFF2196F3);
  static const errorColor = Color(0xFFE53E3E);
  static const successColor = Color(0xFF38A169);
  static const warningColor = Color(0xFFD69E2E);
  
  // Light theme colors
  static const lightBackgroundColor = Color(0xFFFFFFFF);
  static const lightSurfaceColor = Color(0xFFF7FAFC);
  static const lightTextColor = Color(0xFF2D3748);
  
  // Dark theme colors
  static const darkBackgroundColor = Color(0xFF1A202C);
  static const darkSurfaceColor = Color(0xFF2D3748);
  static const darkTextColor = Color(0xFFE2E8F0);
}

// Local Storage Keys
class StorageKeys {
  static const String authToken = 'auth_token';
  static const String refreshToken = 'refresh_token';
  static const String userId = 'user_id';
  static const String fcmToken = 'fcm_token';
  static const String userProfile = 'user_profile';
  static const String lastQueueUpdate = 'last_queue_update';
  static const String notificationSettings = 'notification_settings';
  static const String appSettings = 'app_settings';
}
