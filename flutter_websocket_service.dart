// websocket_service.dart
import 'dart:async';
import 'dart:convert';
import 'package:socket_io_client/socket_io_client.dart' as IO;
import 'package:flutter/foundation.dart';

class WebSocketService {
  static final WebSocketService _instance = WebSocketService._internal();
  factory WebSocketService() => _instance;
  WebSocketService._internal();

  IO.Socket? _socket;
  bool _isConnected = false;
  String? _authToken;
  String? _userId;

  // Event streams for different message types
  final StreamController<Map<String, dynamic>> _messageController = 
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _queueUpdateController = 
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _conversationController = 
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _notificationController = 
      StreamController<Map<String, dynamic>>.broadcast();

  // Getters for streams
  Stream<Map<String, dynamic>> get messageStream => _messageController.stream;
  Stream<Map<String, dynamic>> get queueUpdateStream => _queueUpdateController.stream;
  Stream<Map<String, dynamic>> get conversationStream => _conversationController.stream;
  Stream<Map<String, dynamic>> get notificationStream => _notificationController.stream;

  bool get isConnected => _isConnected;

  /// Initialize WebSocket connection
  Future<void> connect({
    required String serverUrl,
    required String authToken,
    required String userId,
  }) async {
    if (_isConnected) {
      debugPrint('WebSocket already connected');
      return;
    }

    _authToken = authToken;
    _userId = userId;

    try {
      _socket = IO.io(serverUrl, 
        IO.OptionBuilder()
          .setTransports(['websocket'])
          .enableAutoConnect()
          .setAuth({
            'token': authToken,
          })
          .build()
      );

      _setupEventListeners();
      
      debugPrint('WebSocket connecting to: $serverUrl');
    } catch (e) {
      debugPrint('WebSocket connection error: $e');
      rethrow;
    }
  }

  /// Setup all event listeners
  void _setupEventListeners() {
    if (_socket == null) return;

    // Connection events
    _socket!.onConnect((_) {
      _isConnected = true;
      debugPrint('WebSocket connected successfully');
      
      // Join user room and request initial queue status
      _joinUserRoom();
      requestQueueStatus();
    });

    _socket!.onDisconnect((_) {
      _isConnected = false;
      debugPrint('WebSocket disconnected');
    });

    _socket!.onConnectError((error) {
      _isConnected = false;
      debugPrint('WebSocket connection error: $error');
    });

    // Message events
    _socket!.on('newMessage', (data) {
      debugPrint('New message received: $data');
      _messageController.add(Map<String, dynamic>.from(data));
    });

    _socket!.on('conversationStarted', (data) {
      debugPrint('New conversation started: $data');
      _conversationController.add(Map<String, dynamic>.from(data));
    });

    _socket!.on('messageRead', (data) {
      debugPrint('Message read receipt: $data');
      _messageController.add({
        'type': 'messageRead',
        'data': Map<String, dynamic>.from(data)
      });
    });

    _socket!.on('messageReadReceipt', (data) {
      debugPrint('Message read receipt: $data');
      _messageController.add({
        'type': 'messageReadReceipt',
        'data': Map<String, dynamic>.from(data)
      });
    });

    // Queue events
    _socket!.on('queueUpdate', (data) {
      debugPrint('Queue update received: $data');
      _queueUpdateController.add(Map<String, dynamic>.from(data));
    });

    // Generic error handling
    _socket!.onError((error) {
      debugPrint('WebSocket error: $error');
    });
  }

  /// Join user-specific room for targeted messages
  void _joinUserRoom() {
    if (_socket != null && _userId != null) {
      // The server automatically joins users to their room on connection
      debugPrint('User $_userId joined their room');
    }
  }

  /// Request current queue status
  void requestQueueStatus() {
    if (_socket != null && _isConnected) {
      _socket!.emit('requestQueueStatus');
      debugPrint('Requested queue status');
    }
  }

  /// Notify server of queue changes
  void notifyQueueChange(int queueId) {
    if (_socket != null && _isConnected) {
      _socket!.emit('notifyQueueChange', {'queueId': queueId});
      debugPrint('Notified queue change for queue: $queueId');
    }
  }

  /// Join conversation room for real-time messaging
  void joinConversationRoom(int conversationId) {
    if (_socket != null && _isConnected) {
      _socket!.emit('joinConversation', {'conversationId': conversationId});
      debugPrint('Joined conversation room: $conversationId');
    }
  }

  /// Leave conversation room
  void leaveConversationRoom(int conversationId) {
    if (_socket != null && _isConnected) {
      _socket!.emit('leaveConversation', {'conversationId': conversationId});
      debugPrint('Left conversation room: $conversationId');
    }
  }

  /// Disconnect WebSocket
  void disconnect() {
    if (_socket != null) {
      _socket!.disconnect();
      _socket!.dispose();
      _socket = null;
      _isConnected = false;
      debugPrint('WebSocket disconnected and disposed');
    }
  }

  /// Dispose resources
  void dispose() {
    disconnect();
    _messageController.close();
    _queueUpdateController.close();
    _conversationController.close();
    _notificationController.close();
  }
}
