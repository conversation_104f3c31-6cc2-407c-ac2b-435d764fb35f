// queue_service.dart
import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import 'websocket_service.dart';

class QueueService {
  static final QueueService _instance = QueueService._internal();
  factory QueueService() => _instance;
  QueueService._internal();

  final WebSocketService _webSocketService = WebSocketService();
  String? _baseUrl;
  String? _authToken;

  // Queue update streams
  final StreamController<QueueUpdate> _queueUpdateController = 
      StreamController<QueueUpdate>.broadcast();

  Stream<QueueUpdate> get queueUpdateStream => _queueUpdateController.stream;

  // Local cache for queue status
  Map<int, QueueStatus> _queueStatusCache = {};

  void initialize({
    required String baseUrl,
    required String authToken,
  }) {
    _baseUrl = baseUrl;
    _authToken = authToken;
    _setupWebSocketListeners();
  }

  void _setupWebSocketListeners() {
    // Listen for queue updates from WebSocket
    _webSocketService.queueUpdateStream.listen((data) {
      _handleQueueUpdate(data);
    });
  }

  /// Request current queue status for all user's queues
  void requestQueueStatus() {
    _webSocketService.requestQueueStatus();
  }

  /// Notify server of queue changes
  void notifyQueueChange(int queueId) {
    _webSocketService.notifyQueueChange(queueId);
  }

  /// Get queue status for a specific queue
  QueueStatus? getQueueStatus(int queueId) {
    return _queueStatusCache[queueId];
  }

  /// Get all cached queue statuses
  Map<int, QueueStatus> getAllQueueStatuses() {
    return Map.from(_queueStatusCache);
  }

  /// Handle queue update from WebSocket
  void _handleQueueUpdate(Map<String, dynamic> data) {
    try {
      final queueUpdate = QueueUpdate.fromJson(data);
      
      // Update cache
      _queueStatusCache[queueUpdate.queueId] = QueueStatus(
        queueId: queueUpdate.queueId,
        position: queueUpdate.position,
        estimatedStartTime: queueUpdate.estimatedStartTime,
        estimatedWaitMinutes: queueUpdate.estimatedWaitMinutes,
        currentUserPosition: queueUpdate.currentUserPosition,
        status: queueUpdate.status,
        serviceName: queueUpdate.serviceName,
        currentUserAppointmentId: queueUpdate.currentUserAppointmentId,
        appointmentDate: queueUpdate.appointmentDate,
        appointmentTime: queueUpdate.appointmentTime,
        currentUserEstimatedWaitMinutes: queueUpdate.currentUserEstimatedWaitMinutes,
        currentUserEstimatedWaitSeconds: queueUpdate.currentUserEstimatedWaitSeconds,
        timeUntilStartSeconds: queueUpdate.timeUntilStartSeconds,
        totalActiveInQueue: queueUpdate.totalActiveInQueue,
        professionalDetails: queueUpdate.professionalDetails,
        queueMembers: queueUpdate.queueMembers,
        initiatedSwapRequests: queueUpdate.initiatedSwapRequests,
        involvedSwapRequests: queueUpdate.involvedSwapRequests,
        error: queueUpdate.error,
        lastUpdated: DateTime.now(),
      );

      // Emit update
      _queueUpdateController.add(queueUpdate);
      
      debugPrint('Queue update received for queue ${queueUpdate.queueId}: '
          'Position ${queueUpdate.currentUserPosition}, '
          'Wait time ${queueUpdate.currentUserEstimatedWaitMinutes} minutes');
    } catch (e) {
      debugPrint('Error handling queue update: $e');
    }
  }

  /// Get queue members for a specific queue
  Future<List<QueueMember>> getQueueMembers(int queueId) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/api/auth/mobile/queues/$queueId/members'),
        headers: {
          'Authorization': 'Bearer $_authToken',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((json) => QueueMember.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load queue members: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching queue members: $e');
      rethrow;
    }
  }

  /// Request position swap with another queue member
  Future<void> requestPositionSwap({
    required int currentAppointmentId,
    required int targetAppointmentId,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/api/auth/mobile/queue/swap-request'),
        headers: {
          'Authorization': 'Bearer $_authToken',
          'Content-Type': 'application/json',
        },
        body: json.encode({
          'currentAppointmentId': currentAppointmentId,
          'targetAppointmentId': targetAppointmentId,
        }),
      );

      if (response.statusCode == 200) {
        // Notify queue change to update all clients
        final queueStatus = _queueStatusCache.values.firstWhere(
          (status) => status.currentUserAppointmentId == currentAppointmentId,
        );
        notifyQueueChange(queueStatus.queueId);
      } else {
        throw Exception('Failed to request position swap: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error requesting position swap: $e');
      rethrow;
    }
  }

  void dispose() {
    _queueUpdateController.close();
  }
}

// Data models
class QueueUpdate {
  final int queueId;
  final int? position;
  final String? estimatedStartTime;
  final int? estimatedWaitMinutes;
  final int? currentUserPosition;
  final String? status;
  final String? serviceName;
  final int? currentUserAppointmentId;
  final String? appointmentDate;
  final String? appointmentTime;
  final int? currentUserEstimatedWaitMinutes;
  final int? currentUserEstimatedWaitSeconds;
  final int? timeUntilStartSeconds;
  final int? totalActiveInQueue;
  final ProfessionalDetails? professionalDetails;
  final List<QueueMember>? queueMembers;
  final List<SwapRequest>? initiatedSwapRequests;
  final List<SwapRequest>? involvedSwapRequests;
  final String? error;

  QueueUpdate({
    required this.queueId,
    this.position,
    this.estimatedStartTime,
    this.estimatedWaitMinutes,
    this.currentUserPosition,
    this.status,
    this.serviceName,
    this.currentUserAppointmentId,
    this.appointmentDate,
    this.appointmentTime,
    this.currentUserEstimatedWaitMinutes,
    this.currentUserEstimatedWaitSeconds,
    this.timeUntilStartSeconds,
    this.totalActiveInQueue,
    this.professionalDetails,
    this.queueMembers,
    this.initiatedSwapRequests,
    this.involvedSwapRequests,
    this.error,
  });

  factory QueueUpdate.fromJson(Map<String, dynamic> json) {
    return QueueUpdate(
      queueId: json['queueId'],
      position: json['position'],
      estimatedStartTime: json['estimatedStartTime'],
      estimatedWaitMinutes: json['estimatedWaitMinutes'],
      currentUserPosition: json['currentUserPosition'],
      status: json['status'],
      serviceName: json['serviceName'],
      currentUserAppointmentId: json['currentUserAppointmentId'],
      appointmentDate: json['appointmentDate'],
      appointmentTime: json['appointmentTime'],
      currentUserEstimatedWaitMinutes: json['currentUserEstimatedWaitMinutes'],
      currentUserEstimatedWaitSeconds: json['currentUserEstimatedWaitSeconds'],
      timeUntilStartSeconds: json['timeUntilStartSeconds'],
      totalActiveInQueue: json['totalActiveInQueue'],
      professionalDetails: json['professionalDetails'] != null 
          ? ProfessionalDetails.fromJson(json['professionalDetails']) 
          : null,
      queueMembers: json['queueMembers'] != null 
          ? (json['queueMembers'] as List).map((e) => QueueMember.fromJson(e)).toList()
          : null,
      initiatedSwapRequests: json['initiatedSwapRequests'] != null 
          ? (json['initiatedSwapRequests'] as List).map((e) => SwapRequest.fromJson(e)).toList()
          : null,
      involvedSwapRequests: json['involvedSwapRequests'] != null 
          ? (json['involvedSwapRequests'] as List).map((e) => SwapRequest.fromJson(e)).toList()
          : null,
      error: json['error'],
    );
  }
}

class QueueStatus {
  final int queueId;
  final int? position;
  final String? estimatedStartTime;
  final int? estimatedWaitMinutes;
  final int? currentUserPosition;
  final String? status;
  final String? serviceName;
  final int? currentUserAppointmentId;
  final String? appointmentDate;
  final String? appointmentTime;
  final int? currentUserEstimatedWaitMinutes;
  final int? currentUserEstimatedWaitSeconds;
  final int? timeUntilStartSeconds;
  final int? totalActiveInQueue;
  final ProfessionalDetails? professionalDetails;
  final List<QueueMember>? queueMembers;
  final List<SwapRequest>? initiatedSwapRequests;
  final List<SwapRequest>? involvedSwapRequests;
  final String? error;
  final DateTime lastUpdated;

  QueueStatus({
    required this.queueId,
    this.position,
    this.estimatedStartTime,
    this.estimatedWaitMinutes,
    this.currentUserPosition,
    this.status,
    this.serviceName,
    this.currentUserAppointmentId,
    this.appointmentDate,
    this.appointmentTime,
    this.currentUserEstimatedWaitMinutes,
    this.currentUserEstimatedWaitSeconds,
    this.timeUntilStartSeconds,
    this.totalActiveInQueue,
    this.professionalDetails,
    this.queueMembers,
    this.initiatedSwapRequests,
    this.involvedSwapRequests,
    this.error,
    required this.lastUpdated,
  });
}

class QueueMember {
  final int id;
  final String status;
  final String displayName;
  final int? serviceDurationMinutes;
  final String? serviceName;
  final String? appointmentDate;
  final String? appointmentTime;
  final List<SwapRequest>? initiatedSwapRequests;
  final List<SwapRequest>? involvedSwapRequests;

  QueueMember({
    required this.id,
    required this.status,
    required this.displayName,
    this.serviceDurationMinutes,
    this.serviceName,
    this.appointmentDate,
    this.appointmentTime,
    this.initiatedSwapRequests,
    this.involvedSwapRequests,
  });

  factory QueueMember.fromJson(Map<String, dynamic> json) {
    return QueueMember(
      id: json['id'],
      status: json['status'],
      displayName: json['displayName'],
      serviceDurationMinutes: json['serviceDurationMinutes'],
      serviceName: json['serviceName'],
      appointmentDate: json['appointmentDate'],
      appointmentTime: json['appointmentTime'],
      initiatedSwapRequests: json['initiatedSwapRequests'] != null 
          ? (json['initiatedSwapRequests'] as List).map((e) => SwapRequest.fromJson(e)).toList()
          : null,
      involvedSwapRequests: json['involvedSwapRequests'] != null 
          ? (json['involvedSwapRequests'] as List).map((e) => SwapRequest.fromJson(e)).toList()
          : null,
    );
  }
}

class ProfessionalDetails {
  final String id;
  final String title;
  final String? phone;

  ProfessionalDetails({
    required this.id,
    required this.title,
    this.phone,
  });

  factory ProfessionalDetails.fromJson(Map<String, dynamic> json) {
    return ProfessionalDetails(
      id: json['id'],
      title: json['title'],
      phone: json['phone'],
    );
  }
}

class SwapRequest {
  final int id;
  final String status;
  final String createdAt;
  final String updatedAt;
  final String requestedBy;
  final int appointment1;
  final int appointment2;

  SwapRequest({
    required this.id,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    required this.requestedBy,
    required this.appointment1,
    required this.appointment2,
  });

  factory SwapRequest.fromJson(Map<String, dynamic> json) {
    return SwapRequest(
      id: json['id'],
      status: json['status'],
      createdAt: json['createdAt'],
      updatedAt: json['updatedAt'],
      requestedBy: json['requestedBy'],
      appointment1: json['appointment1'],
      appointment2: json['appointment2'],
    );
  }
}
