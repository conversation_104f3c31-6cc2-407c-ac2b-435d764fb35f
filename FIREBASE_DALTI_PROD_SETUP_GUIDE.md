# Firebase dalti-prod Configuration Guide

## ✅ What's Been Fixed

### 1. Backend Code Updated
- ✅ Fixed `app/src/server/firebaseAdmin.ts` to properly parse JSON from environment variable
- ✅ Added validation for required service account fields
- ✅ Added logging to show which Firebase project is being used

### 2. Configuration Files Updated
- ✅ Updated `app/.env.server` with placeholder for dalti-prod credentials
- ✅ Updated `app/src/client/firebase.ts` with dalti-prod placeholders
- ✅ Updated `app/public/firebase-messaging-sw.js` with dalti-prod placeholders
- ✅ Updated `app/src/client/notifications/useFcmTokenManager.ts` with VAPID placeholder

## 🔧 Required Actions

### Step 1: Get dalti-prod Service Account JSON

1. **Go to Firebase Console**: https://console.firebase.google.com/
2. **Select your `dalti-prod` project**
3. **Navigate to**: Project Settings (⚙️) → **Service accounts** tab
4. **Click**: "Generate new private key"
5. **Download**: The JSON file
6. **Copy**: The entire JSON content

### Step 2: Update Backend Environment Variable

Replace the placeholder in `app/.env.server`:

```bash
# Replace this line:
FIREBASE_ADMIN_SDK_CONFIG='REPLACE_WITH_DALTI_PROD_SERVICE_ACCOUNT_JSON'

# With your actual service account JSON (as a single line):
FIREBASE_ADMIN_SDK_CONFIG='{"type":"service_account","project_id":"dalti-prod","private_key_id":"...","private_key":"-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n","client_email":"<EMAIL>",...}'
```

### Step 3: Get dalti-prod Client Configuration

1. **Go to Firebase Console**: https://console.firebase.google.com/
2. **Select your `dalti-prod` project**
3. **Navigate to**: Project Settings (⚙️) → **General** tab
4. **Scroll down to**: "Your apps" section
5. **Find your web app** or **click "Add app"** if none exists
6. **Copy the config object** that looks like:

```javascript
const firebaseConfig = {
  apiKey: "AIza...",
  authDomain: "dalti-prod.firebaseapp.com",
  projectId: "dalti-prod",
  storageBucket: "dalti-prod.appspot.com",
  messagingSenderId: "*********",
  appId: "1:*********:web:abcdef123456",
  measurementId: "G-XXXXXXXXXX"
};
```

### Step 4: Update Client Configuration Files

**File 1: `app/src/client/firebase.ts`**
Replace these placeholders:
- `REPLACE_WITH_DALTI_PROD_API_KEY` → Your `apiKey`
- `REPLACE_WITH_DALTI_PROD_SENDER_ID` → Your `messagingSenderId`
- `REPLACE_WITH_DALTI_PROD_APP_ID` → Your `appId`
- `REPLACE_WITH_DALTI_PROD_MEASUREMENT_ID` → Your `measurementId`

**File 2: `app/public/firebase-messaging-sw.js`**
Replace the same placeholders with the **exact same values** as firebase.ts

### Step 5: Get VAPID Key

1. **In Firebase Console** → Project Settings → **Cloud Messaging** tab
2. **Scroll to**: "Web configuration"
3. **Generate or copy**: Your VAPID key
4. **Update**: `app/src/client/notifications/useFcmTokenManager.ts`
   - Replace `REPLACE_WITH_DALTI_PROD_VAPID_KEY` with your VAPID key

### Step 6: Update Kubernetes Configuration (Production)

If deploying to Kubernetes, update `k8s-wasp/backend/02-secrets.yaml`:

```yaml
# Base64 encode your service account JSON:
echo -n '{"type":"service_account",...}' | base64 -w 0

# Then add to secrets:
FIREBASE_ADMIN_SDK_CONFIG: "eyJ0eXBlIjoic2VydmljZV9hY2NvdW50Ii..." # base64 encoded
```

## 🧪 Testing

After updating all configurations:

1. **Restart your development server**:
   ```bash
   wasp start
   ```

2. **Check backend logs** for:
   ```
   Using Firebase project: dalti-prod
   Firebase Admin SDK initialized successfully.
   ```

3. **Test FCM token registration** in browser console
4. **Test notification sending** using your test endpoints

## 🔍 Troubleshooting

### "Firebase Admin SDK not initialized"
- Check that `FIREBASE_ADMIN_SDK_CONFIG` is valid JSON
- Ensure all required fields are present in the service account

### "Invalid registration token"
- Ensure client config matches the backend project
- Check that VAPID key is from the same project

### "Permission denied"
- Verify service account has proper permissions
- Check that project IDs match everywhere

## 📁 Files Modified

- ✅ `app/src/server/firebaseAdmin.ts` - Backend Firebase Admin SDK
- 🔄 `app/.env.server` - Environment variables (needs your values)
- 🔄 `app/src/client/firebase.ts` - Client Firebase config (needs your values)
- 🔄 `app/public/firebase-messaging-sw.js` - Service worker (needs your values)
- 🔄 `app/src/client/notifications/useFcmTokenManager.ts` - VAPID key (needs your value)

## ✅ Success Criteria

When properly configured, you should see:
- Backend logs showing "Using Firebase project: dalti-prod"
- No Firebase initialization errors
- FCM tokens successfully saved to database
- Push notifications working in browser/mobile
