#!/bin/bash

# Quick script to show full Firebase FCM tokens
# Usage: ./show_full_firebase_tokens.sh [number_of_tokens]

# Default number of tokens to show
NUM_TOKENS=${1:-10}

echo "🔥 Full Firebase FCM Tokens"
echo "==========================="
echo "⚠️  WARNING: These are sensitive tokens - handle with care!"
echo ""

# Find the Docker container
CONTAINER_NAME=$(docker ps --format "table {{.Names}}" | grep wasp-dev-db)

if [ -z "$CONTAINER_NAME" ]; then
    echo "❌ No Wasp PostgreSQL container found. Make sure you run 'wasp start db' first."
    exit 1
fi

# Database credentials
DB_USER="postgresWaspDevUser"
DB_NAME=$(echo $CONTAINER_NAME | sed 's/wasp-dev-db-//')

echo "📊 Showing last $NUM_TOKENS tokens from database: $DB_NAME"
echo ""

# Function to run SQL queries
run_query() {
    docker exec $CONTAINER_NAME psql -U $DB_USER -d $DB_NAME -c "$1" 2>/dev/null
}

# Show full tokens with user information
echo "📱 Full FCM Tokens with User Info:"
echo "=================================="
run_query "SELECT 
    udt.\"id\" as token_id,
    u.\"firstName\" || ' ' || COALESCE(u.\"lastName\", '') as user_name,
    u.\"email\",
    udt.\"deviceType\",
    udt.\"token\" as full_fcm_token,
    udt.\"createdAt\",
    udt.\"updatedAt\"
FROM \"UserDeviceToken\" udt
JOIN \"User\" u ON udt.\"userId\" = u.\"id\"
ORDER BY udt.\"createdAt\" DESC 
LIMIT $NUM_TOKENS;"

echo ""
echo "📋 Token Summary:"
echo "================"
run_query "SELECT 
    \"deviceType\",
    COUNT(*) as count
FROM \"UserDeviceToken\" 
GROUP BY \"deviceType\";"

echo ""
echo "💡 Usage Tips:"
echo "============="
echo "• Copy tokens for testing with Firebase Console or testing tools"
echo "• Use these tokens with your mobile app for push notification testing"
echo "• Keep tokens secure - they allow sending notifications to specific devices"
echo ""
echo "🎯 Test Commands:"
echo "================"
echo "# Test with curl (replace TOKEN and YOUR_JWT):"
echo "curl -X POST \\"
echo "  -H 'Authorization: Bearer YOUR_JWT' \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -d '{\"title\":\"Test\",\"body\":\"Test notification\"}' \\"
echo "  http://localhost:3001/api/auth/notifications/test/send"
echo ""
echo "✅ Full token display complete!"
