// message_service.dart
import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import 'websocket_service.dart';

class MessageService {
  static final MessageService _instance = MessageService._internal();
  factory MessageService() => _instance;
  MessageService._internal();

  final WebSocketService _webSocketService = WebSocketService();
  String? _baseUrl;
  String? _authToken;

  // Message and conversation streams
  final StreamController<List<Message>> _messagesController = 
      StreamController<List<Message>>.broadcast();
  final StreamController<List<Conversation>> _conversationsController = 
      StreamController<List<Conversation>>.broadcast();

  Stream<List<Message>> get messagesStream => _messagesController.stream;
  Stream<List<Conversation>> get conversationsStream => _conversationsController.stream;

  // Local cache
  Map<int, List<Message>> _messageCache = {};
  List<Conversation> _conversationCache = [];

  void initialize({
    required String baseUrl,
    required String authToken,
  }) {
    _baseUrl = baseUrl;
    _authToken = authToken;
    _setupWebSocketListeners();
  }

  void _setupWebSocketListeners() {
    // Listen for new messages
    _webSocketService.messageStream.listen((data) {
      if (data['type'] == 'messageRead' || data['type'] == 'messageReadReceipt') {
        _handleMessageReadReceipt(data['data']);
      } else {
        _handleNewMessage(data);
      }
    });

    // Listen for new conversations
    _webSocketService.conversationStream.listen((data) {
      _handleNewConversation(data);
    });
  }

  /// Get all conversations
  Future<List<Conversation>> getConversations() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/api/auth/mobile/conversations'),
        headers: {
          'Authorization': 'Bearer $_authToken',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        _conversationCache = data.map((json) => Conversation.fromJson(json)).toList();
        _conversationsController.add(_conversationCache);
        return _conversationCache;
      } else {
        throw Exception('Failed to load conversations: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching conversations: $e');
      rethrow;
    }
  }

  /// Get messages for a specific conversation
  Future<List<Message>> getMessages(int conversationId) async {
    try {
      // Join conversation room for real-time updates
      _webSocketService.joinConversationRoom(conversationId);

      final response = await http.get(
        Uri.parse('$_baseUrl/api/auth/mobile/conversations/$conversationId/messages'),
        headers: {
          'Authorization': 'Bearer $_authToken',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        final messages = data.map((json) => Message.fromJson(json)).toList();
        _messageCache[conversationId] = messages;
        _messagesController.add(messages);
        return messages;
      } else {
        throw Exception('Failed to load messages: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching messages: $e');
      rethrow;
    }
  }

  /// Send a message
  Future<Message> sendMessage({
    required int conversationId,
    required String content,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/api/auth/mobile/messages'),
        headers: {
          'Authorization': 'Bearer $_authToken',
          'Content-Type': 'application/json',
        },
        body: json.encode({
          'conversationId': conversationId,
          'content': content,
        }),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final messageData = json.decode(response.body);
        return Message.fromJson(messageData);
      } else {
        throw Exception('Failed to send message: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error sending message: $e');
      rethrow;
    }
  }

  /// Mark message as read
  Future<void> markMessageAsRead({
    required int messageId,
    required int conversationId,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/api/auth/mobile/messages/read'),
        headers: {
          'Authorization': 'Bearer $_authToken',
          'Content-Type': 'application/json',
        },
        body: json.encode({
          'messageId': messageId,
          'conversationId': conversationId,
        }),
      );

      if (response.statusCode != 200) {
        throw Exception('Failed to mark message as read: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error marking message as read: $e');
      rethrow;
    }
  }

  /// Start new conversation
  Future<Conversation> startConversation({
    required List<String> otherUserIds,
    required bool isGroup,
    String? name,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/api/auth/mobile/conversations/start'),
        headers: {
          'Authorization': 'Bearer $_authToken',
          'Content-Type': 'application/json',
        },
        body: json.encode({
          'otherUserIds': otherUserIds,
          'isGroup': isGroup,
          if (name != null) 'name': name,
        }),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final conversationData = json.decode(response.body);
        return Conversation.fromJson(conversationData);
      } else {
        throw Exception('Failed to start conversation: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error starting conversation: $e');
      rethrow;
    }
  }

  /// Handle new message from WebSocket
  void _handleNewMessage(Map<String, dynamic> data) {
    try {
      final message = Message.fromJson(data);
      final conversationId = message.conversationId;

      // Update message cache
      if (_messageCache.containsKey(conversationId)) {
        _messageCache[conversationId]!.add(message);
        _messagesController.add(_messageCache[conversationId]!);
      }

      // Update conversation cache (last message, unread count)
      _updateConversationCache(message);
    } catch (e) {
      debugPrint('Error handling new message: $e');
    }
  }

  /// Handle new conversation from WebSocket
  void _handleNewConversation(Map<String, dynamic> data) {
    try {
      final conversation = Conversation.fromJson(data);
      _conversationCache.add(conversation);
      _conversationsController.add(_conversationCache);
    } catch (e) {
      debugPrint('Error handling new conversation: $e');
    }
  }

  /// Handle message read receipts
  void _handleMessageReadReceipt(Map<String, dynamic> data) {
    try {
      final messageId = data['messageId'] as int;
      final conversationId = data['conversationId'] as int;

      // Update message status in cache
      if (_messageCache.containsKey(conversationId)) {
        final messages = _messageCache[conversationId]!;
        final messageIndex = messages.indexWhere((m) => m.id == messageId);
        if (messageIndex != -1) {
          // Update message read status
          _messagesController.add(messages);
        }
      }
    } catch (e) {
      debugPrint('Error handling message read receipt: $e');
    }
  }

  /// Update conversation cache with new message info
  void _updateConversationCache(Message message) {
    final conversationIndex = _conversationCache.indexWhere(
      (c) => c.id == message.conversationId
    );
    
    if (conversationIndex != -1) {
      // Update last message and increment unread count
      _conversationCache[conversationIndex] = _conversationCache[conversationIndex].copyWith(
        lastMessage: message,
        updatedAt: message.createdAt,
      );
      _conversationsController.add(_conversationCache);
    }
  }

  void dispose() {
    _messagesController.close();
    _conversationsController.close();
  }
}

// Data models
class Message {
  final int id;
  final int conversationId;
  final String senderId;
  final String content;
  final DateTime createdAt;
  final User? sender;
  final String status;

  Message({
    required this.id,
    required this.conversationId,
    required this.senderId,
    required this.content,
    required this.createdAt,
    this.sender,
    required this.status,
  });

  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      id: json['id'],
      conversationId: json['conversationId'],
      senderId: json['senderId'],
      content: json['content'],
      createdAt: DateTime.parse(json['createdAt']),
      sender: json['sender'] != null ? User.fromJson(json['sender']) : null,
      status: json['status'] ?? 'SENT',
    );
  }
}

class Conversation {
  final int id;
  final String? name;
  final bool isGroup;
  final String displayName;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Message? lastMessage;
  final int unreadCount;

  Conversation({
    required this.id,
    this.name,
    required this.isGroup,
    required this.displayName,
    required this.createdAt,
    required this.updatedAt,
    this.lastMessage,
    required this.unreadCount,
  });

  factory Conversation.fromJson(Map<String, dynamic> json) {
    return Conversation(
      id: json['id'],
      name: json['name'],
      isGroup: json['isGroup'],
      displayName: json['displayName'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      lastMessage: json['lastMessage'] != null 
          ? Message.fromJson(json['lastMessage']) 
          : null,
      unreadCount: json['unreadCount'] ?? 0,
    );
  }

  Conversation copyWith({
    Message? lastMessage,
    DateTime? updatedAt,
    int? unreadCount,
  }) {
    return Conversation(
      id: id,
      name: name,
      isGroup: isGroup,
      displayName: displayName,
      createdAt: createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastMessage: lastMessage ?? this.lastMessage,
      unreadCount: unreadCount ?? this.unreadCount,
    );
  }
}

class User {
  final String id;
  final String? firstName;
  final String? lastName;

  User({
    required this.id,
    this.firstName,
    this.lastName,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      firstName: json['firstName'],
      lastName: json['lastName'],
    );
  }

  String get displayName {
    if (firstName != null && lastName != null) {
      return '$firstName $lastName';
    }
    return firstName ?? lastName ?? 'Unknown User';
  }
}
