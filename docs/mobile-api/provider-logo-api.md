# Provider Logo Mobile API Documentation

This document describes the mobile APIs for managing provider logos in the Flutter app.

## Base URL
```
https://dapi-test.adscloud.org:8443
```

## Authentication
All endpoints require authentication. Include the session token in the Authorization header:
```
Authorization: Bearer YOUR_SESSION_TOKEN
```

---

## API Endpoints

### 1. Get Current Provider Logo

**GET** `/api/auth/providers/mobile/logo`

Returns the current provider's logo information.

#### Response Format:
```json
{
  "success": true,
  "data": {
    "provider": {
      "id": 123,
      "title": "My Medical Clinic",
      "hasLogo": true
    },
    "logo": {
      "id": "uuid-here",
      "name": "clinic-logo.png",
      "type": "image/png",
      "key": "file-key-in-s3",
      "downloadUrl": "https://signed-s3-url.com/logo.png",
      "createdAt": "2024-01-15T10:30:00Z"
    }
  }
}
```

#### Response (No Logo):
```json
{
  "success": true,
  "data": {
    "provider": {
      "id": 123,
      "title": "My Medical Clinic",
      "hasLogo": false
    },
    "logo": null
  }
}
```

#### Flutter Usage Example:
```dart
Future<ProviderLogo?> getCurrentLogo() async {
  final response = await http.get(
    Uri.parse('$baseUrl/api/auth/providers/mobile/logo'),
    headers: {
      'Authorization': 'Bearer $sessionToken',
      'Content-Type': 'application/json',
    },
  );

  if (response.statusCode == 200) {
    final data = json.decode(response.body);
    if (data['success'] && data['data']['logo'] != null) {
      return ProviderLogo.fromJson(data['data']['logo']);
    }
  }
  return null;
}
```

---

### 2. Upload Provider Logo

**POST** `/api/auth/providers/mobile/logo`

Initiates logo upload process and returns S3 upload URL.

#### Request Body:
```json
{
  "fileName": "my-logo.png",
  "fileType": "image/png",
  "fileSize": 1024000
}
```

#### Response:
```json
{
  "success": true,
  "message": "Logo upload URL generated successfully",
  "data": {
    "uploadUrl": "https://s3-upload-url.com",
    "uploadFields": {
      "key": "file-key",
      "policy": "...",
      "x-amz-signature": "...",
      // ... other S3 fields
    },
    "file": {
      "id": "file-uuid",
      "name": "my-logo.png",
      "type": "image/png",
      "key": "file-key"
    },
    "instructions": {
      "method": "POST",
      "url": "https://s3-upload-url.com",
      "fields": { /* S3 fields */ },
      "fileFieldName": "file"
    }
  }
}
```

#### Flutter Usage Example:
```dart
Future<bool> uploadLogo(File imageFile) async {
  try {
    // Step 1: Get upload URL
    final initResponse = await http.post(
      Uri.parse('$baseUrl/api/auth/providers/mobile/logo'),
      headers: {
        'Authorization': 'Bearer $sessionToken',
        'Content-Type': 'application/json',
      },
      body: json.encode({
        'fileName': path.basename(imageFile.path),
        'fileType': 'image/png', // or detect from file
        'fileSize': await imageFile.length(),
      }),
    );

    if (initResponse.statusCode != 200) return false;

    final initData = json.decode(initResponse.body);
    final uploadUrl = initData['data']['uploadUrl'];
    final uploadFields = initData['data']['uploadFields'];

    // Step 2: Upload to S3
    final request = http.MultipartRequest('POST', Uri.parse(uploadUrl));
    
    // Add all S3 fields
    uploadFields.forEach((key, value) {
      request.fields[key] = value.toString();
    });
    
    // Add the file
    request.files.add(
      await http.MultipartFile.fromPath('file', imageFile.path)
    );

    final uploadResponse = await request.send();
    
    if (uploadResponse.statusCode == 204) {
      // Step 3: Optional - Confirm upload
      await confirmLogoUpload(initData['data']['file']['id']);
      return true;
    }
    
    return false;
  } catch (e) {
    print('Upload error: $e');
    return false;
  }
}
```

---

### 3. Remove Provider Logo

**DELETE** `/api/auth/providers/mobile/logo`

Removes the current provider logo.

#### Response:
```json
{
  "success": true,
  "message": "Provider logo removed successfully",
  "data": {
    "providerId": 123,
    "removedLogoId": "uuid-here"
  }
}
```

#### Flutter Usage Example:
```dart
Future<bool> removeLogo() async {
  final response = await http.delete(
    Uri.parse('$baseUrl/api/auth/providers/mobile/logo'),
    headers: {
      'Authorization': 'Bearer $sessionToken',
      'Content-Type': 'application/json',
    },
  );

  return response.statusCode == 200;
}
```

---

### 4. Confirm Logo Upload (Optional)

**POST** `/api/auth/providers/mobile/logo/confirm`

Confirms that the logo was successfully uploaded to S3 and returns the final logo information.

#### Request Body:
```json
{
  "fileId": "file-uuid-from-upload-response"
}
```

#### Response:
```json
{
  "success": true,
  "message": "Logo upload confirmed successfully",
  "data": {
    "provider": {
      "id": 123,
      "title": "My Medical Clinic",
      "hasLogo": true
    },
    "logo": {
      "id": "uuid-here",
      "name": "my-logo.png",
      "type": "image/png",
      "key": "file-key",
      "downloadUrl": "https://signed-s3-url.com/logo.png",
      "createdAt": "2024-01-15T10:30:00Z"
    }
  }
}
```

---

## Error Responses

All endpoints return errors in this format:

```json
{
  "success": false,
  "error": "ERROR_CODE",
  "message": "Human readable error message"
}
```

### Common Error Codes:
- `AUTHENTICATION_REQUIRED` (401): Missing or invalid session token
- `PROVIDER_NOT_FOUND` (404): User doesn't have a provider profile
- `VALIDATION_ERROR` (400): Invalid request data
- `FILE_TOO_LARGE` (400): File exceeds 5MB limit
- `NO_LOGO_FOUND` (404): No logo to remove
- `UPLOAD_ERROR` (500): S3 upload initialization failed
- `INTERNAL_SERVER_ERROR` (500): Unexpected server error

---

## File Constraints

- **Supported formats**: JPEG, PNG only
- **Maximum size**: 5MB
- **Recommended dimensions**: 512x512px or 1024x1024px (square format works best)

---

## Flutter Data Models

```dart
class ProviderLogo {
  final String id;
  final String name;
  final String type;
  final String key;
  final String? downloadUrl;
  final DateTime createdAt;

  ProviderLogo({
    required this.id,
    required this.name,
    required this.type,
    required this.key,
    this.downloadUrl,
    required this.createdAt,
  });

  factory ProviderLogo.fromJson(Map<String, dynamic> json) {
    return ProviderLogo(
      id: json['id'],
      name: json['name'],
      type: json['type'],
      key: json['key'],
      downloadUrl: json['downloadUrl'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

class ProviderInfo {
  final int id;
  final String title;
  final bool hasLogo;

  ProviderInfo({
    required this.id,
    required this.title,
    required this.hasLogo,
  });

  factory ProviderInfo.fromJson(Map<String, dynamic> json) {
    return ProviderInfo(
      id: json['id'],
      title: json['title'],
      hasLogo: json['hasLogo'],
    );
  }
}
```

---

## Complete Flutter Service Class

```dart
class ProviderLogoService {
  final String baseUrl;
  final String sessionToken;

  ProviderLogoService({
    required this.baseUrl,
    required this.sessionToken,
  });

  Map<String, String> get _headers => {
    'Authorization': 'Bearer $sessionToken',
    'Content-Type': 'application/json',
  };

  Future<ProviderLogo?> getCurrentLogo() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/auth/providers/mobile/logo'),
        headers: _headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] && data['data']['logo'] != null) {
          return ProviderLogo.fromJson(data['data']['logo']);
        }
      }
      return null;
    } catch (e) {
      print('Error getting logo: $e');
      return null;
    }
  }

  Future<bool> uploadLogo(File imageFile) async {
    try {
      // Get upload URL
      final initResponse = await http.post(
        Uri.parse('$baseUrl/api/auth/providers/mobile/logo'),
        headers: _headers,
        body: json.encode({
          'fileName': path.basename(imageFile.path),
          'fileType': lookupMimeType(imageFile.path) ?? 'image/png',
          'fileSize': await imageFile.length(),
        }),
      );

      if (initResponse.statusCode != 200) return false;

      final initData = json.decode(initResponse.body);
      
      // Upload to S3
      final request = http.MultipartRequest(
        'POST', 
        Uri.parse(initData['data']['uploadUrl'])
      );
      
      initData['data']['uploadFields'].forEach((key, value) {
        request.fields[key] = value.toString();
      });
      
      request.files.add(
        await http.MultipartFile.fromPath('file', imageFile.path)
      );

      final uploadResponse = await request.send();
      return uploadResponse.statusCode == 204;
    } catch (e) {
      print('Error uploading logo: $e');
      return false;
    }
  }

  Future<bool> removeLogo() async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl/api/auth/providers/mobile/logo'),
        headers: _headers,
      );
      return response.statusCode == 200;
    } catch (e) {
      print('Error removing logo: $e');
      return false;
    }
  }
}
```

---

## Testing the APIs

You can test these endpoints using curl or Postman:

```bash
# Get current logo
curl -X GET "https://dapi-test.adscloud.org:8443/api/auth/providers/mobile/logo" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Initialize upload
curl -X POST "https://dapi-test.adscloud.org:8443/api/auth/providers/mobile/logo" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"fileName":"test.png","fileType":"image/png","fileSize":100000}'

# Remove logo
curl -X DELETE "https://dapi-test.adscloud.org:8443/api/auth/providers/mobile/logo" \
  -H "Authorization: Bearer YOUR_TOKEN"
``` 