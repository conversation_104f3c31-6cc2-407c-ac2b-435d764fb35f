# Provider Logo Upload API Workflow Explanation

## Overview
The provider logo upload system uses a **3-step process** with **direct S3 upload** to ensure efficient file handling and better performance. This document explains the complete workflow for Flutter developers.

## 🔄 Complete Upload Workflow

### **Step 1: Initialize Upload**
```
Flutter App → Backend API → Response with S3 Upload URL
```

**What happens:**
1. <PERSON>lutter sends logo metadata to `/api/auth/providers/mobile/logo`
2. <PERSON><PERSON> creates a file record in the database
3. Backend generates a signed S3 upload URL with security fields
4. Backend updates the provider's `logoId` to link the new file
5. Backend returns S3 upload instructions to Flutter

**Request:**
```json
POST /api/auth/providers/mobile/logo
{
  "fileName": "clinic-logo.png",
  "fileType": "image/png", 
  "fileSize": 1024000
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "uploadUrl": "https://s3-bucket.amazonaws.com/...",
    "uploadFields": {
      "key": "providers/user-123/logo-456.png",
      "policy": "base64-encoded-policy",
      "x-amz-signature": "signature",
      "x-amz-algorithm": "AWS4-HMAC-SHA256",
      "x-amz-date": "20240101T000000Z",
      "x-amz-credential": "credentials"
    },
    "file": {
      "id": "file-uuid",
      "name": "clinic-logo.png", 
      "type": "image/png",
      "key": "providers/user-123/logo-456.png"
    },
    "instructions": {
      "method": "POST",
      "url": "https://s3-bucket.amazonaws.com/...",
      "fields": { /* S3 fields */ },
      "fileFieldName": "file"
    }
  }
}
```

### **Step 2: Direct Upload to S3**
```
Flutter App → Amazon S3 → Success/Failure Response
```

**What happens:**
1. Flutter uses the S3 URL and fields from Step 1
2. Flutter creates a multipart form request with all S3 fields
3. Flutter adds the actual image file to the request
4. Flutter uploads directly to S3 (bypassing our server)
5. S3 returns success (204) or error response

**Flutter Implementation:**
```dart
// Create multipart request to S3
final request = http.MultipartRequest('POST', Uri.parse(uploadUrl));

// Add all S3 security fields FIRST
uploadFields.forEach((key, value) {
  request.fields[key] = value.toString();
});

// Add the actual file LAST
request.files.add(
  await http.MultipartFile.fromPath('file', imageFile.path)
);

// Send to S3
final response = await request.send();
// Success = 204 status code (NOT 200!)
```

### **Step 3: Confirmation (Optional)**
```
Flutter App → Backend API → Final Logo Info
```

**What happens:**
1. Flutter calls `/api/auth/providers/mobile/logo/confirm` with file ID
2. Backend verifies the file exists and belongs to the user
3. Backend generates a download URL for immediate display
4. Backend returns complete logo information

**Request:**
```json
POST /api/auth/providers/mobile/logo/confirm
{
  "fileId": "file-uuid-from-step-1"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Logo upload confirmed successfully",
  "data": {
    "provider": {
      "id": 123,
      "title": "My Medical Clinic",
      "hasLogo": true
    },
    "logo": {
      "id": "file-uuid",
      "name": "clinic-logo.png",
      "type": "image/png",
      "key": "providers/user-123/logo-456.png",
      "downloadUrl": "https://signed-s3-url.com/logo.png",
      "createdAt": "2024-01-15T10:30:00Z"
    }
  }
}
```

## 🔍 Key Technical Details

### **Why This Architecture?**
1. **Performance**: Large files don't go through our server
2. **Scalability**: S3 handles file storage and delivery
3. **Security**: Signed URLs with expiration and user-specific paths
4. **Reliability**: S3's 99.999999999% durability
5. **Cost Efficiency**: Reduces server bandwidth costs

### **Security Features:**
- Session token authentication required
- User-specific file paths (`providers/user-123/`)
- Signed URLs with expiration times
- File type validation (JPEG/PNG only)
- File size limits (5MB max)
- Policy-based upload restrictions

### **File Organization:**
```
S3 Bucket Structure:
├── providers/
│   ├── user-123/
│   │   ├── logo-456.png
│   │   └── logo-789.jpg
│   └── user-456/
│       └── logo-101.png
└── customers/
    └── user-789/
        └── profile-pic-101.jpg
```

## 📱 Flutter Implementation Guide

### **1. Complete Service Class:**
```dart
import 'dart:io';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:path/path.dart' as path;
import 'package:mime/mime.dart';

class ProviderLogoService {
  static const String baseUrl = 'https://dapi-test.adscloud.org:8443';
  final String sessionToken;

  ProviderLogoService(this.sessionToken);

  Map<String, String> get _headers => {
    'Authorization': 'Bearer $sessionToken',
    'Content-Type': 'application/json',
  };

  /// Complete logo upload workflow
  Future<bool> uploadLogo(File imageFile) async {
    try {
      print('Starting logo upload for: ${path.basename(imageFile.path)}');
      
      // Step 1: Initialize upload
      final initResponse = await _initializeUpload(imageFile);
      if (initResponse == null) {
        print('Failed to initialize upload');
        return false;
      }

      print('Upload initialized, uploading to S3...');
      
      // Step 2: Upload to S3
      final uploadSuccess = await _uploadToS3(
        imageFile, 
        initResponse['uploadUrl'], 
        initResponse['uploadFields']
      );

      if (uploadSuccess) {
        print('S3 upload successful, confirming...');
        // Step 3: Optional confirmation
        await _confirmUpload(initResponse['file']['id']);
        print('Logo upload completed successfully');
      } else {
        print('S3 upload failed');
      }

      return uploadSuccess;
    } catch (e) {
      print('Upload error: $e');
      return false;
    }
  }

  /// Step 1: Initialize upload and get S3 URL
  Future<Map<String, dynamic>?> _initializeUpload(File file) async {
    try {
      final fileSize = await file.length();
      final fileName = path.basename(file.path);
      final mimeType = lookupMimeType(file.path) ?? 'image/png';

      print('Initializing upload: $fileName ($fileSize bytes, $mimeType)');

      final response = await http.post(
        Uri.parse('$baseUrl/api/auth/providers/mobile/logo'),
        headers: _headers,
        body: json.encode({
          'fileName': fileName,
          'fileType': mimeType,
          'fileSize': fileSize,
        }),
      );

      print('Initialize response: ${response.statusCode}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success']) {
          return data['data'];
        } else {
          print('API returned success=false: ${data['message']}');
        }
      } else {
        print('Initialize failed: ${response.body}');
      }
    } catch (e) {
      print('Initialize error: $e');
    }
    return null;
  }

  /// Step 2: Upload file directly to S3
  Future<bool> _uploadToS3(File file, String uploadUrl, Map<String, dynamic> fields) async {
    try {
      print('Uploading to S3: $uploadUrl');
      
      final request = http.MultipartRequest('POST', Uri.parse(uploadUrl));
      
      // IMPORTANT: Add S3 fields FIRST, in the correct order
      fields.forEach((key, value) {
        request.fields[key] = value.toString();
        print('S3 field: $key = ${value.toString().substring(0, math.min(50, value.toString().length))}...');
      });
      
      // Add file LAST
      request.files.add(await http.MultipartFile.fromPath('file', file.path));
      print('Added file to request: ${path.basename(file.path)}');
      
      final response = await request.send();
      print('S3 response status: ${response.statusCode}');
      
      // S3 returns 204 for successful upload (not 200!)
      if (response.statusCode == 204) {
        return true;
      } else {
        final responseBody = await response.stream.bytesToString();
        print('S3 upload failed: ${response.statusCode} - $responseBody');
        return false;
      }
    } catch (e) {
      print('S3 upload error: $e');
      return false;
    }
  }

  /// Step 3: Confirm upload (optional)
  Future<ProviderLogo?> _confirmUpload(String fileId) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/auth/providers/mobile/logo/confirm'),
        headers: _headers,
        body: json.encode({'fileId': fileId}),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] && data['data']['logo'] != null) {
          return ProviderLogo.fromJson(data['data']['logo']);
        }
      }
    } catch (e) {
      print('Confirm upload error: $e');
    }
    return null;
  }

  /// Get current provider logo
  Future<ProviderLogo?> getCurrentLogo() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/auth/providers/mobile/logo'),
        headers: _headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] && data['data']['logo'] != null) {
          return ProviderLogo.fromJson(data['data']['logo']);
        }
      }
      return null;
    } catch (e) {
      print('Error getting logo: $e');
      return null;
    }
  }

  /// Remove current provider logo
  Future<bool> removeLogo() async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl/api/auth/providers/mobile/logo'),
        headers: _headers,
      );
      return response.statusCode == 200;
    } catch (e) {
      print('Error removing logo: $e');
      return false;
    }
  }
}
```

### **2. Data Models:**
```dart
class ProviderLogo {
  final String id;
  final String name;
  final String type;
  final String key;
  final String? downloadUrl;
  final DateTime createdAt;

  ProviderLogo({
    required this.id,
    required this.name,
    required this.type,
    required this.key,
    this.downloadUrl,
    required this.createdAt,
  });

  factory ProviderLogo.fromJson(Map<String, dynamic> json) {
    return ProviderLogo(
      id: json['id'],
      name: json['name'],
      type: json['type'],
      key: json['key'],
      downloadUrl: json['downloadUrl'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'key': key,
      'downloadUrl': downloadUrl,
      'createdAt': createdAt.toIso8601String(),
    };
  }
}

class ProviderInfo {
  final int id;
  final String title;
  final bool hasLogo;

  ProviderInfo({
    required this.id,
    required this.title,
    required this.hasLogo,
  });

  factory ProviderInfo.fromJson(Map<String, dynamic> json) {
    return ProviderInfo(
      id: json['id'],
      title: json['title'],
      hasLogo: json['hasLogo'],
    );
  }
}
```

### **3. Error Handling:**
```dart
class LogoUploadException implements Exception {
  final String message;
  final String? errorCode;
  final int? statusCode;

  LogoUploadException(this.message, {this.errorCode, this.statusCode});

  @override
  String toString() => 'LogoUploadException: $message';
}

extension LogoServiceErrorHandling on ProviderLogoService {
  String handleError(http.Response response) {
    switch (response.statusCode) {
      case 401:
        return 'Authentication required. Please login again.';
      case 404:
        return 'Provider profile not found.';
      case 400:
        try {
          final data = json.decode(response.body);
          switch (data['error']) {
            case 'FILE_TOO_LARGE':
              return 'File too large. Maximum size is 5MB.';
            case 'VALIDATION_ERROR':
              return 'Invalid file format. Only JPEG and PNG are supported.';
            default:
              return data['message'] ?? 'Invalid request.';
          }
        } catch (e) {
          return 'Invalid request data.';
        }
      case 500:
        return 'Server error. Please try again later.';
      default:
        return 'Upload failed. Please try again.';
    }
  }
}
```

### **4. Usage Example:**
```dart
class LogoUploadWidget extends StatefulWidget {
  @override
  _LogoUploadWidgetState createState() => _LogoUploadWidgetState();
}

class _LogoUploadWidgetState extends State<LogoUploadWidget> {
  final ProviderLogoService _logoService = ProviderLogoService('your-session-token');
  bool _isUploading = false;
  ProviderLogo? _currentLogo;

  @override
  void initState() {
    super.initState();
    _loadCurrentLogo();
  }

  Future<void> _loadCurrentLogo() async {
    final logo = await _logoService.getCurrentLogo();
    setState(() {
      _currentLogo = logo;
    });
  }

  Future<void> _pickAndUploadLogo() async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(
      source: ImageSource.gallery,
      maxWidth: 1024,
      maxHeight: 1024,
      imageQuality: 85,
    );

    if (image != null) {
      setState(() {
        _isUploading = true;
      });

      final file = File(image.path);
      final success = await _logoService.uploadLogo(file);

      setState(() {
        _isUploading = false;
      });

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Logo uploaded successfully!')),
        );
        _loadCurrentLogo(); // Refresh the logo
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to upload logo. Please try again.')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Display current logo
        if (_currentLogo != null && _currentLogo!.downloadUrl != null)
          CircleAvatar(
            radius: 50,
            backgroundImage: NetworkImage(_currentLogo!.downloadUrl!),
          )
        else
          CircleAvatar(
            radius: 50,
            child: Icon(Icons.business, size: 50),
          ),
        
        SizedBox(height: 16),
        
        // Upload button
        ElevatedButton(
          onPressed: _isUploading ? null : _pickAndUploadLogo,
          child: _isUploading 
            ? CircularProgressIndicator()
            : Text('Upload Logo'),
        ),
        
        // Remove button
        if (_currentLogo != null)
          TextButton(
            onPressed: () async {
              final success = await _logoService.removeLogo();
              if (success) {
                setState(() {
                  _currentLogo = null;
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Logo removed successfully!')),
                );
              }
            },
            child: Text('Remove Logo'),
          ),
      ],
    );
  }
}
```

## 🎯 Important Notes for Flutter Developer

### **Critical Success Factors:**

1. **S3 Upload Response Code:**
   - Success returns HTTP **204** (not 200!)
   - Any other status code means failure
   - No JSON response body on success

2. **Field Order in Multipart Request:**
   - Add all S3 fields FIRST
   - Add the file field LAST
   - This order is critical for S3 acceptance

3. **Authentication:**
   - Must include `Authorization: Bearer TOKEN` header
   - Token should be the session token from login
   - Token must be valid and not expired

4. **File Constraints:**
   - Only JPEG and PNG formats accepted
   - Maximum 5MB file size
   - Square images (512x512 or 1024x1024) work best
   - Validate file size before upload

### **Network Handling:**

1. **Timeout Configuration:**
   ```dart
   final client = http.Client();
   try {
     final response = await client.send(request).timeout(Duration(minutes: 2));
   } finally {
     client.close();
   }
   ```

2. **Progress Tracking:**
   ```dart
   final request = http.MultipartRequest('POST', Uri.parse(uploadUrl));
   final response = await request.send();
   
   // Listen to upload progress
   response.stream.listen(
     (value) {
       // Handle progress updates
       final progress = value.length / totalBytes;
     },
   );
   ```

3. **Error Recovery:**
   - Implement retry logic for network failures
   - Handle S3 errors gracefully
   - Provide user feedback for all error states

### **Testing Checklist:**

- [ ] Test with different image formats (JPEG, PNG)
- [ ] Test with various file sizes (small, medium, large)
- [ ] Test with poor network conditions
- [ ] Test authentication failures
- [ ] Test S3 upload failures
- [ ] Test logo removal functionality
- [ ] Test logo display with signed URLs

### **Security Considerations:**

- Never log or store the session token in plain text
- Handle signed URL expiration gracefully
- Validate file types on the client side
- Implement proper error messages without exposing sensitive info

This architecture ensures your Flutter app can efficiently upload provider logos while maintaining security, performance, and user experience! 🚀

## 📚 Additional Resources

- [AWS S3 Multipart Upload Documentation](https://docs.aws.amazon.com/AmazonS3/latest/dev/mpuoverview.html)
- [Flutter HTTP Package Documentation](https://pub.dev/packages/http)
- [Flutter Image Picker Documentation](https://pub.dev/packages/image_picker)
- [MIME Type Detection in Flutter](https://pub.dev/packages/mime) 