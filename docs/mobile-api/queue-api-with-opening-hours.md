# Queue API with Opening Hours

This document describes the updated Queue API that now supports opening hours management alongside the existing title, providing place, and services functionality.

## Overview

The Queue API has been enhanced to support:
- **Title**: Queue name/title
- **Providing Place**: Location where the queue operates
- **Services**: Services offered through this queue
- **Opening Hours**: Schedule when the queue is operational

## API Endpoints

### 1. Create Queue

**POST** `/api/providers/queues`

Creates a new queue with title, location, services, and optional opening hours.

#### Request Body

```json
{
  "title": "General Queue",
  "sProvidingPlaceId": 1,
  "isActive": true,
  "serviceIds": [1, 2],
  "openingHours": [
    {
      "dayOfWeek": "Monday",
      "isActive": true,
      "hours": [
        {
          "timeFrom": "09:00",
          "timeTo": "12:00"
        },
        {
          "timeFrom": "14:00",
          "timeTo": "18:00"
        }
      ]
    },
    {
      "dayOfWeek": "Tuesday",
      "isActive": true,
      "hours": [
        {
          "timeFrom": "09:00",
          "timeTo": "17:00"
        }
      ]
    }
  ]
}
```

#### Response

```json
{
  "success": true,
  "data": {
    "id": 5,
    "title": "General Queue",
    "isActive": true,
    "sProvidingPlaceId": 1,
    "services": [
      {
        "id": 1,
        "title": "Service 1"
      },
      {
        "id": 2,
        "title": "Service 2"
      }
    ],
    "openingHours": [
      {
        "dayOfWeek": "Monday",
        "isActive": true,
        "hours": [
          {
            "timeFrom": "09:00",
            "timeTo": "12:00"
          },
          {
            "timeFrom": "14:00",
            "timeTo": "18:00"
          }
        ]
      },
      {
        "dayOfWeek": "Tuesday",
        "isActive": true,
        "hours": [
          {
            "timeFrom": "09:00",
            "timeTo": "17:00"
          }
        ]
      }
    ]
  },
  "message": "Provider queue created successfully"
}
```

### 2. Update Queue

**PUT** `/api/providers/queues/:id`

Updates an existing queue. All fields are optional.

#### Request Body

```json
{
  "title": "Updated Queue Name",
  "isActive": false,
  "serviceIds": [1, 3],
  "openingHours": [
    {
      "dayOfWeek": "Monday",
      "isActive": true,
      "hours": [
        {
          "timeFrom": "08:00",
          "timeTo": "16:00"
        }
      ]
    }
  ]
}
```

#### Response

```json
{
  "success": true,
  "data": {
    "id": 5,
    "title": "Updated Queue Name",
    "isActive": false,
    "sProvidingPlaceId": 1,
    "services": [
      {
        "id": 1,
        "title": "Service 1"
      },
      {
        "id": 3,
        "title": "Service 3"
      }
    ],
    "openingHours": [
      {
        "dayOfWeek": "Monday",
        "isActive": true,
        "hours": [
          {
            "timeFrom": "08:00",
            "timeTo": "16:00"
          }
        ]
      }
    ]
  },
  "message": "Provider queue updated successfully"
}
```

### 3. Get All Provider Queues

**GET** `/api/providers/queues`

Retrieves all queues for the authenticated provider, including opening hours.

#### Response

```json
{
  "success": true,
  "data": [
    {
      "id": 5,
      "title": "General Queue",
      "isActive": true,
      "sProvidingPlaceId": 1,
      "services": [
        {
          "id": 1,
          "title": "Service 1"
        }
      ],
      "openingHours": [
        {
          "dayOfWeek": "Monday",
          "isActive": true,
          "hours": [
            {
              "timeFrom": "09:00",
              "timeTo": "17:00"
            }
          ]
        }
      ]
    }
  ],
  "message": "Provider queues retrieved successfully"
}
```

### 4. Get Queues by Location

**GET** `/api/providers/locations/:locationId/queues`

Retrieves queues for a specific location, including opening hours.

#### Query Parameters

- `isActive` (boolean, optional): Filter by active status
- `search` (string, optional): Search by queue title
- `includeServices` (boolean, optional): Include detailed service information

#### Response

```json
{
  "success": true,
  "data": [
    {
      "id": 5,
      "title": "General Queue",
      "isActive": true,
      "sProvidingPlaceId": 1,
      "services": [
        {
          "id": 1,
          "title": "Service 1"
        }
      ],
      "openingHours": [
        {
          "dayOfWeek": "Monday",
          "isActive": true,
          "hours": [
            {
              "timeFrom": "09:00",
              "timeTo": "17:00"
            }
          ]
        }
      ]
    }
  ],
  "message": "Queues retrieved successfully"
}
```

### 5. Delete Queue

**DELETE** `/api/providers/queues/:id`

Deletes a queue. This operation is only allowed if there are no future appointments.

#### Response

```json
{
  "success": true,
  "data": {
    "id": 5
  },
  "message": "Queue deleted successfully"
}
```

### 6. Get Queue Limits

**GET** `/api/providers/queues/limits`

Retrieves the current queue usage and limits for the authenticated provider.

#### Response

```json
{
  "success": true,
  "data": {
    "currentCount": 1,
    "maxAllowed": 1,
    "canCreateMore": false,
    "remaining": 0,
    "usagePercentage": 100
  },
  "message": "Queue limit information retrieved successfully"
}
```

### 7. Check Queue Creation Eligibility

**GET** `/api/providers/queues/can-create`

Checks if the authenticated provider can create a new queue.

#### Response

```json
{
  "success": true,
  "data": {
    "canCreate": false,
    "reason": "Queue limit reached. You can create up to 1 queue(s). Please upgrade your subscription to create more queues.",
    "currentCount": 1,
    "maxAllowed": 1
  },
  "message": "Queue creation not allowed"
}
```

## Data Structure

### OpeningHoursDay

```typescript
interface OpeningHoursDay {
  dayOfWeek: string;      // e.g., "Monday", "Tuesday", etc.
  isActive: boolean;      // Whether the queue operates on this day
  hours: OpeningHoursInterval[];
}
```

### OpeningHoursInterval

```typescript
interface OpeningHoursInterval {
  timeFrom: string;       // HH:MM format, e.g., "09:00"
  timeTo: string;         // HH:MM format, e.g., "17:00"
}
```

### QueueResponse

```typescript
interface QueueResponse {
  id: number;
  title: string;
  isActive: boolean;
  sProvidingPlaceId: number;
  services: Array<{
    id: number;
    title: string;
  }>;
  openingHours?: OpeningHoursDay[];
}
```

## Validation Rules

### Opening Hours Validation

- **dayOfWeek**: Required string (e.g., "Monday", "Tuesday")
- **isActive**: Boolean, defaults to `true`
- **hours**: Array of time intervals
  - **timeFrom**: Required, HH:MM format (00:00 to 23:59)
  - **timeTo**: Required, HH:MM format (00:00 to 23:59)
  - Must be valid time range (timeFrom < timeTo)

### Queue Validation

- **title**: Required string, minimum 1 character
- **sProvidingPlaceId**: Required positive integer
- **isActive**: Boolean, defaults to `true`
- **serviceIds**: Required array, minimum 1 service
- **openingHours**: Optional array of opening hours

## Important Notes

1. **Queue Limit Enforcement**: Users can only create queues up to their subscription limit. The default limit is 1 queue. If the limit is reached, users must upgrade their subscription to create additional queues.

2. **Opening Hours Replacement**: When updating opening hours, all existing opening hours for the queue are replaced with the new ones provided.

3. **Service Validation**: All service IDs must belong to the authenticated provider.

4. **Location Ownership**: The provider must own the specified location.

5. **Deletion Constraints**: Queues with future appointments cannot be deleted.

6. **Time Format**: All times must be in HH:MM format (24-hour notation).

7. **Day Names**: Use full day names in English (Monday, Tuesday, Wednesday, etc.).

## Error Responses

### Validation Errors

```json
{
  "success": false,
  "message": "Invalid request data",
  "errors": [
    {
      "field": "openingHours[0].hours[0].timeFrom",
      "message": "Invalid time format"
    }
  ]
}
```

### Authorization Errors

```json
{
  "success": false,
  "message": "Queue not found or you do not have permission to update it."
}
```

### Business Logic Errors

```json
{
  "success": false,
  "message": "Cannot delete queue: It has 3 future appointment(s). Please cancel or reschedule the appointments first."
}
```

### Subscription Limit Errors

```json
{
  "success": false,
  "message": "Queue limit reached. You can create up to 1 queue(s). Please upgrade your subscription to create more queues."
}
``` 