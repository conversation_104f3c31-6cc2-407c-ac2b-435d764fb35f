# Admin API Documentation

## Overview
Admin API endpoints for platform administration. Admins cannot register - they must be created manually in the database with `isAdmin: true`.

## Base URL
- Development: `https://dapi-test.adscloud.org:8443`
- Production: `https://dapi.adscloud.org`

## Authentication
All admin endpoints (except login) require admin authentication via J<PERSON><PERSON> token in Authorization header:
```
Authorization: Bearer <jwt_token>
```

---

## Authentication Endpoints

### Admin Login
**POST** `/api/admin/login`

Login for admin users only.

**Request Body:**
```json
{
  "identifier": "<EMAIL>", // Email or phone number
  "password": "admin_password"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "sessionId": "session_token",
    "user": {
      "id": "user_id",
      "email": "<EMAIL>",
      "firstName": "Admin",
      "lastName": "User",
      "isAdmin": true
    }
  },
  "message": "Admin login successful"
}
```

---

## Provider Management

### Get Providers List
**GET** `/api/admin/providers`

Fetch all providers with pagination and filtering.

**Query Parameters:**
- `page` (number, default: 1) - Page number
- `limit` (number, default: 20, max: 100) - Items per page
- `search` (string, optional) - Search in provider title, user name, email
- `status` (string, optional) - Filter by status: "verified", "pending"

**Response:**
```json
{
  "success": true,
  "data": {
    "providers": [
      {
        "id": 1,
        "title": "Dr. Smith Clinic",
        "isVerified": false,
        "isSetupComplete": true,
        "user": {
          "id": "user_id",
          "firstName": "John",
          "lastName": "Smith",
          "email": "<EMAIL>",
          "mobileNumber": "+**********",
          "createdAt": "2024-01-01T00:00:00.000Z"
        },
        "category": {
          "id": 1,
          "title": "Doctor"
        },
        "providingPlaces": [
          {
            "id": 1,
            "name": "Main Clinic",
            "city": "New York"
          }
        ],
        "_count": {
          "services": 5,
          "customerFolders": 12
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 50,
      "totalPages": 3
    }
  },
  "message": "Providers fetched successfully"
}
```

### Approve/Reject Provider
**PUT** `/api/admin/providers/:id/approve`

Approve or reject a provider account.

**Request Body:**
```json
{
  "isVerified": true, // true to approve, false to reject
  "notes": "Approved after document verification" // Optional admin notes
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "isVerified": true,
    "user": {
      "id": "user_id",
      "firstName": "John",
      "lastName": "Smith",
      "email": "<EMAIL>"
    }
  },
  "message": "Provider approved successfully"
}
```

---

## Customer Management

### Get Customers List
**GET** `/api/admin/customers`

Fetch all customers with pagination and filtering.

**Query Parameters:**
- `page` (number, default: 1) - Page number
- `limit` (number, default: 20, max: 100) - Items per page
- `search` (string, optional) - Search in customer name, email, phone

**Response:**
```json
{
  "success": true,
  "data": {
    "customers": [
      {
        "id": "customer_id",
        "firstName": "Jane",
        "lastName": "Doe",
        "email": "<EMAIL>",
        "mobileNumber": "+**********",
        "credits": 3,
        "createdAt": "2024-01-01T00:00:00.000Z",
        "isPhoneVerified": true,
        "isEmailVerified": true,
        "_count": {
          "customerFolders": 2
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "totalPages": 5
    }
  },
  "message": "Customers fetched successfully"
}
```

---

## Provider Category Management

### Get Provider Categories
**GET** `/api/admin/provider-categories`

Fetch all provider categories with hierarchy.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "title": "Healthcare",
      "parentId": null,
      "parent": null,
      "children": [
        {
          "id": 2,
          "title": "Doctor",
          "parentId": 1
        }
      ],
      "_count": {
        "providers": 15
      }
    }
  ],
  "message": "Provider categories fetched successfully"
}
```

### Create Provider Category
**POST** `/api/admin/provider-categories`

Create a new provider category.

**Request Body:**
```json
{
  "title": "New Category",
  "parentId": 1 // Optional - for subcategories
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 3,
    "title": "New Category",
    "parentId": 1,
    "parent": {
      "id": 1,
      "title": "Healthcare"
    },
    "children": []
  },
  "message": "Provider category created successfully"
}
```

### Update Provider Category
**PUT** `/api/admin/provider-categories/:id`

Update an existing provider category.

**Request Body:**
```json
{
  "title": "Updated Category Name",
  "parentId": 2 // Optional - can change parent
}
```

### Delete Provider Category
**DELETE** `/api/admin/provider-categories/:id`

Delete a provider category. Cannot delete if:
- Category has associated providers
- Category has subcategories

**Response:**
```json
{
  "success": true,
  "message": "Provider category deleted successfully"
}
```

---

## Error Responses

All endpoints return errors in this format:
```json
{
  "success": false,
  "message": "Error description",
  "errors": {} // Optional validation errors
}
```

**Common HTTP Status Codes:**
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (authentication required)
- `403` - Forbidden (admin access required)
- `404` - Not Found
- `409` - Conflict (duplicate data)
- `500` - Internal Server Error

---

## Notes

1. **Admin Creation**: Admins must be created manually in the database with `isAdmin: true`
2. **Authentication**: Use the JWT token from login response in subsequent requests
3. **Pagination**: All list endpoints support pagination with `page` and `limit` parameters
4. **Search**: Search functionality is case-insensitive and searches across relevant fields
5. **Provider Approval**: Approving/rejecting providers can trigger notifications (implement as needed)
