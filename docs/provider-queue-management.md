# Provider Queue Management System Documentation

## Overview

The Provider Queue Management system in the Wasp.js application provides comprehensive functionality for healthcare providers to manage their service queues, including CRUD operations, service assignments, queue-specific operating hours, real-time state tracking, capacity management, and integration with appointment booking systems.

## Table of Contents

1. [Database Schema](#database-schema)
2. [Provider Queue CRUD Operations](#provider-queue-crud-operations)
3. [API Endpoints](#api-endpoints)
4. [Authentication & Authorization](#authentication--authorization)
5. [Business Rules & Workflows](#business-rules--workflows)
6. [Queue-Specific Operating Hours](#queue-specific-operating-hours)
7. [Service Assignment Management](#service-assignment-management)
8. [Real-Time Queue State Tracking](#real-time-queue-state-tracking)
9. [Queue Capacity Management](#queue-capacity-management)
10. [Error Handling](#error-handling)

## Database Schema

### Core Models

#### Queue Model
```prisma
model Queue {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  title String

  sProvidingPlace   SProvidingPlace @relation(fields: [sProvidingPlaceId], references: [id])
  sProvidingPlaceId Int

  isActive Boolean @default(true)

  // Queue-specific opening schedule
  openings     QueueOpening[]
  // Services offered by this specific queue
  services     Service[]      @relation("ServiceQueues")
  // Appointments booked for this specific queue
  appointments Appointment[]
  SProvider    SProvider?     @relation(fields: [sProviderId], references: [id])
  sProviderId  Int?

  // A queue name should be unique within its place
  @@unique([title, sProvidingPlaceId])
}
```

#### QueueOpening Model (Queue-Specific Operating Hours)
```prisma
model QueueOpening {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  queue   Queue @relation(fields: [queueId], references: [id])
  queueId Int

  dayOfWeek String // e.g., "Monday", "Tuesday"
  type      String  @default("regular") // e.g., "regular", "exception"
  isActive  Boolean @default(true)

  // Time intervals for this opening
  hours QueueOpeningHours[]

  // An opening for a queue on a specific day/type should be unique
  @@unique([queueId, dayOfWeek, type])
}
```

#### QueueOpeningHours Model
```prisma
model QueueOpeningHours {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  queueOpening   QueueOpening @relation(fields: [queueOpeningId], references: [id])
  queueOpeningId Int

  timeFrom String // HH:mm format
  timeTo   String // HH:mm format
}
```

### Queue Status Values
- **Active**: Queue is operational and accepting appointments
- **Inactive**: Queue is temporarily disabled but not deleted
- **Maintenance**: Queue is under maintenance or configuration
- **Temporarily Closed**: Queue is temporarily closed for specific reasons

## Provider Queue CRUD Operations

### 1. Create Queue

#### Wasp Operation
```typescript
// File: app/src/provider/operations.ts
export const createProviderQueue: CreateProviderQueue<CreateQueueData, Queue>
```

#### Validation Schema
```typescript
const createQueueInputSchema = z.object({
  title: z.string().min(1, "Queue title is required"),
  sProvidingPlaceId: z.number().int().positive("Valid location is required"),
  isActive: z.boolean().default(true),
  serviceIds: z.array(z.number().int().positive()).min(1, "At least one service must be assigned"),
  openingHours: z.array(z.object({
    dayOfWeek: z.string().min(1, "Day of week is required"),
    isActive: z.boolean().default(true),
    hours: z.array(z.object({
      timeFrom: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format"),
      timeTo: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format"),
    })),
  })).optional(),
});
```

#### Business Logic
- **Queue Limit Validation**: Provider must not exceed subscription queue limits
- **Location Ownership**: Queue must be created in provider-owned location
- **Service Assignment**: At least one service must be assigned to the queue
- **Unique Title**: Queue title must be unique within the location
- **Default Operating Hours**: Creates default 7-day schedule if not provided

#### Transaction Flow
1. Validate provider authentication and queue limits
2. Verify location ownership and service ownership
3. Check queue title uniqueness within location
4. Create queue record with service assignments
5. Generate default or custom opening hours
6. Create translation records for internationalization
7. Return complete queue with all relations

### 2. Read/Retrieve Queues

#### Wasp Query
```typescript
// File: app/src/provider/operations.ts
export const getProviderQueues: GetProviderQueues<void, Queue[]>
```

#### Response Structure
```typescript
interface QueueResponse {
  id: number;
  title: string;
  isActive: boolean;
  sProvidingPlaceId: number;
  services: Array<{
    id: number;
    title: string;
  }>;
  openingHours?: OpeningHoursDay[];
}
```

#### Filtering Options
- **Location**: Filter by specific location (sProvidingPlaceId)
- **Status**: Filter by active/inactive status
- **Service**: Filter by assigned services
- **Provider**: Automatic filtering by authenticated provider
- **Pagination**: Support for page-based pagination

#### Included Relations
- **Location Details**: Associated SProvidingPlace information
- **Service Assignments**: All services assigned to the queue
- **Opening Hours**: Complete queue-specific operating schedule
- **Appointment Counts**: Statistics on appointments per queue

### 3. Update Queue

#### Wasp Operation
```typescript
// File: app/src/queue/operations.ts
export const updateQueue: UpdateQueue<UpdateQueueData, Queue>
```

#### Validation Schema
```typescript
const updateQueueInputSchema = z.object({
  queueId: z.number().int().positive(),
  title: z.string().min(1, "Queue title is required").optional(),
  isActive: z.boolean().optional(),
  serviceIds: z.array(z.number().int().positive()).min(1, "At least one service must be assigned").optional(),
  openingHours: z.array(z.object({
    dayOfWeek: z.string().min(1, "Day of week is required"),
    isActive: z.boolean().default(true),
    hours: z.array(z.object({
      timeFrom: z.string().regex(/^\d{2}:\d{2}$/, "Time must be in HH:mm format"),
      timeTo: z.string().regex(/^\d{2}:\d{2}$/, "Time must be in HH:mm format"),
    })),
  })).optional(),
});
```

#### Editable Fields
- **Title**: Queue name (must remain unique within location)
- **Status**: Active/inactive status
- **Service Assignments**: Add/remove services from queue
- **Operating Hours**: Complete queue-specific schedule management

#### Business Rules
- **Ownership Verification**: Only queue owner can update
- **Title Uniqueness**: Updated title must be unique within location
- **Service Validation**: All assigned services must belong to provider
- **Active Appointments**: Certain changes restricted if active appointments exist

### 4. Delete/Deactivate Queue

#### Wasp Operation
```typescript
// File: app/src/queue/operations.ts
export const deleteQueue: DeleteQueue<DeleteQueueData, Queue>
```

#### Deletion Policies
- **Active Appointments Check**: Cannot delete if non-terminal appointments exist
- **Cascade Deletion**: Removes all opening hours and related data
- **Translation Cleanup**: Removes all translation records
- **Service Reassignment**: Services become unassigned from deleted queue

#### Pre-Deletion Validation
```typescript
const validateQueueDeletion = async (queueId: number, context: any) => {
  const queue = await context.entities.Queue.findFirst({
    where: { id: queueId },
    include: {
      appointments: {
        where: { status: { notIn: ['canceled', 'completed', 'noshow'] } },
        select: { id: true }
      }
    }
  });
  
  if (queue?.appointments.length > 0) {
    throw new HttpError(409, 
      `Cannot delete queue: It has ${queue.appointments.length} active appointments. ` +
      `Please complete or cancel these appointments first.`
    );
  }
};
```

#### Deactivation vs Deletion
```typescript
// Soft deactivation (recommended)
const deactivateQueue = async (queueId: number) => {
  await updateQueue({
    queueId,
    isActive: false
  });
  // Queue remains in database but stops accepting new appointments
};

// Hard deletion (only when no dependencies)
const deleteQueue = async (queueId: number) => {
  // Verify no active appointments
  // Delete opening hours
  // Delete queue record
};
```

## API Endpoints

### REST API Endpoints

#### Provider Queue Management
```http
GET    /api/auth/providers/queues              # Get all provider queues
POST   /api/auth/providers/queues              # Create new queue
PUT    /api/auth/providers/queues/:id          # Update queue
DELETE /api/auth/providers/queues/:id          # Delete queue
```

#### Queue Capacity Management
```http
GET    /api/auth/providers/queues/can-create   # Check if can create more queues
GET    /api/auth/providers/queues/limits       # Get queue limits and usage
```

#### Queue Service Assignment
```http
GET    /api/auth/providers/queues/:id/services           # Get queue services
POST   /api/auth/providers/queues/:id/services          # Assign service to queue
DELETE /api/auth/providers/queues/:id/services/:serviceId # Remove service from queue
```

#### Queue Operating Hours
```http
GET    /api/auth/providers/queues/:id/hours     # Get queue operating hours
PUT    /api/auth/providers/queues/:id/hours     # Update queue operating hours
```

### Wasp Operations

#### Queries
```typescript
// Get all provider queues
query getProviderQueues {
  fn: import { getProviderQueues } from "@src/provider/operations",
  entities: [User, SProvider, SProvidingPlace, Queue, Service, QueueOpening, QueueOpeningHours],
  auth: true
}

// Get queue availability
query getQueueAvailability {
  fn: import { getQueueAvailability } from "@src/queue/operations",
  entities: [Queue, Service, QueueOpening, QueueOpeningHours, Appointment, SProvidingPlace]
}

// Get queue openings
query getQueueOpenings {
  fn: import { getQueueOpenings } from "@src/queue/operations",
  entities: [QueueOpening, QueueOpeningHours, Queue, SProvidingPlace, SProvider],
  auth: true
}
```

#### Actions
```typescript
// Create queue
action createProviderQueue {
  fn: import { createProviderQueue } from "@src/provider/operations",
  entities: [User, SProvider, SProvidingPlace, Queue, Service, QueueOpening, QueueOpeningHours],
  auth: true
}

// Update queue
action updateQueue {
  fn: import { updateQueue } from "@src/queue/operations",
  entities: [Queue, Service, QueueOpening, QueueOpeningHours, SProvidingPlace, SProvider],
  auth: true
}

// Delete queue
action deleteQueue {
  fn: import { deleteQueue } from "@src/queue/operations",
  entities: [Queue, Appointment, QueueOpening, QueueOpeningHours, SProvidingPlace, SProvider],
  auth: true
}

// Update queue openings
action updateQueueOpenings {
  fn: import { updateQueueOpenings } from "@src/queue/operations",
  entities: [QueueOpening, QueueOpeningHours, Queue, SProvidingPlace, SProvider],
  auth: true
}
```

## Authentication & Authorization

### Authentication Requirements
- All provider queue endpoints require `auth: true`
- JWT token validation via Wasp's built-in auth system
- User must have `role: 'CLIENT'` (provider role)

### Authorization Patterns
```typescript
// Queue ownership verification
const verifyQueueOwnership = async (queueId: number, userId: string, entities: any) => {
  const queue = await entities.Queue.findFirst({
    where: {
      id: queueId,
      sProvidingPlace: {
        provider: { userId: userId }
      }
    },
    select: { id: true, title: true }
  });

  if (!queue) {
    throw new HttpError(404, 'Queue not found or you do not have permission to access it');
  }

  return queue;
};
```

### Queue Limit Enforcement
```typescript
// Check queue creation limits
export const checkQueueCreationLimit = async (userId: string, context: any) => {
  const user = await context.entities.User.findUnique({
    where: { id: userId },
    select: { queues: true }
  });

  const provider = await context.entities.SProvider.findUnique({
    where: { userId: userId },
    select: { id: true }
  });

  const currentCount = await context.entities.Queue.count({
    where: {
      sProvidingPlace: {
        sProviderId: provider.id
      }
    }
  });

  const canCreate = currentCount < user.queues;
  const message = canCreate 
    ? undefined 
    : `Queue limit reached. You can create up to ${user.queues} queue(s). Please upgrade your subscription to create more queues.`;

  return {
    canCreate,
    currentCount,
    maxAllowed: user.queues,
    message
  };
};
```

### Context Structure
```typescript
interface QueueApiContext {
  user: {
    id: string;
    role: string;
    queues: number; // Subscription-based queue limit
  };
  entities: {
    Queue: PrismaQueueDelegate;
    QueueOpening: PrismaQueueOpeningDelegate;
    QueueOpeningHours: PrismaQueueOpeningHoursDelegate;
    Service: PrismaServiceDelegate;
    // ... other entities
  };
}
```

## Business Rules & Workflows

### Queue Creation Workflow

#### 1. Pre-Creation Validation
- Provider must have active account and profile
- Provider must not exceed subscription queue limits
- Target location must be owned by provider
- At least one service must be assigned to queue
- Queue title must be unique within location

#### 2. Queue Creation Process
```typescript
// Queue creation transaction flow
const createQueueFlow = async (queueData, context) => {
  return await prisma.$transaction(async (tx) => {
    // 1. Validate provider and queue limits
    const provider = await tx.sProvider.findUnique({
      where: { userId: context.user.id },
      select: { id: true }
    });

    if (!provider) {
      throw new HttpError(404, 'Provider profile not found');
    }

    // 2. Check queue creation limits
    const queueLimitCheck = await checkQueueCreationLimit(context.user.id, context);
    if (!queueLimitCheck.canCreate) {
      throw new HttpError(403, queueLimitCheck.message);
    }

    // 3. Verify location ownership
    const location = await tx.sProvidingPlace.findFirst({
      where: {
        id: queueData.sProvidingPlaceId,
        sProviderId: provider.id
      }
    });

    if (!location) {
      throw new HttpError(403, 'Location not found or not owned by provider');
    }

    // 4. Validate service ownership
    const services = await tx.service.findMany({
      where: {
        id: { in: queueData.serviceIds },
        provider: { userId: context.user.id }
      }
    });

    if (services.length !== queueData.serviceIds.length) {
      throw new HttpError(403, 'One or more services not found or not owned by provider');
    }

    // 5. Create the queue
    const newQueue = await tx.queue.create({
      data: {
        title: queueData.title,
        sProvidingPlaceId: queueData.sProvidingPlaceId,
        isActive: queueData.isActive,
        services: {
          connect: services.map(service => ({ id: service.id }))
        }
      },
      include: {
        sProvidingPlace: { select: { id: true, name: true } },
        services: { select: { id: true, title: true } }
      }
    });

    // 6. Create default opening hours for all 7 days
    const daysOfWeek = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

    for (const day of daysOfWeek) {
      const opening = await tx.queueOpening.create({
        data: {
          queueId: newQueue.id,
          dayOfWeek: day,
          type: 'regular',
          isActive: true
        }
      });

      // Create default hours (9:00 AM - 5:00 PM) for weekdays
      if (!['Saturday', 'Sunday'].includes(day)) {
        await tx.queueOpeningHours.create({
          data: {
            queueOpeningId: opening.id,
            timeFrom: '09:00',
            timeTo: '17:00'
          }
        });
      }
    }

    // 7. Apply custom opening hours if provided
    if (queueData.openingHours) {
      for (const daySchedule of queueData.openingHours) {
        const opening = await tx.queueOpening.findFirst({
          where: {
            queueId: newQueue.id,
            dayOfWeek: daySchedule.dayOfWeek
          }
        });

        if (opening) {
          // Clear existing hours
          await tx.queueOpeningHours.deleteMany({
            where: { queueOpeningId: opening.id }
          });

          // Add new hours
          for (const hours of daySchedule.hours) {
            await tx.queueOpeningHours.create({
              data: {
                queueOpeningId: opening.id,
                timeFrom: hours.timeFrom,
                timeTo: hours.timeTo
              }
            });
          }

          // Update active status
          await tx.queueOpening.update({
            where: { id: opening.id },
            data: { isActive: daySchedule.isActive }
          });
        }
      }
    }

    // 8. Create translation records
    if (newQueue.title) {
      await translateAndStore(
        context.entities,
        String(newQueue.id),
        'Queue',
        'title',
        newQueue.title
      );
    }

    return newQueue;
  });
};
```

#### 3. Post-Creation Actions
- Initialize queue state tracking
- Set up real-time WebSocket connections
- Configure default notification settings
- Update provider dashboard statistics

### Queue Status Management

#### Status Transitions
```
active → inactive → active
active → maintenance → active
inactive → maintenance → active
active → temporarily_closed → active
```

#### Status Change Business Rules

##### Active → Inactive
- Queue stops accepting new appointments
- Existing appointments remain scheduled
- Queue becomes invisible in booking interfaces
- Real-time updates notify connected clients

##### Active → Maintenance
- Queue temporarily unavailable for booking
- Maintenance reason and estimated duration recorded
- Existing appointments may be rescheduled
- Automatic reactivation when maintenance complete

##### Active → Temporarily Closed
- Short-term closure for specific reasons
- Estimated reopening time provided
- Appointments within closure period rescheduled
- Automatic status updates when reopened

### Queue Deactivation Impact

#### Impact on Appointments
```typescript
const handleQueueDeactivation = async (queueId: number, context: any) => {
  // 1. Get all future appointments for this queue
  const futureAppointments = await context.entities.Appointment.findMany({
    where: {
      queueId: queueId,
      expectedAppointmentStartTime: { gte: new Date() },
      status: { notIn: ['canceled', 'completed', 'noshow'] }
    }
  });

  // 2. Handle appointment rescheduling or cancellation
  for (const appointment of futureAppointments) {
    // Try to reschedule to another queue with same service
    const alternativeQueues = await findAlternativeQueues(
      appointment.serviceId,
      appointment.placeId,
      context
    );

    if (alternativeQueues.length > 0) {
      // Reschedule to alternative queue
      await rescheduleToAlternativeQueue(appointment, alternativeQueues[0], context);
    } else {
      // Cancel appointment if no alternatives
      await context.entities.Appointment.update({
        where: { id: appointment.id },
        data: {
          status: 'canceled',
          canceledAt: new Date()
        }
      });

      // Create appointment history
      await context.entities.AppointmentHistory.create({
        data: {
          appointmentId: appointment.id,
          changedByUserId: context.user.id,
          changeReason: 'Queue deactivated',
          previousStatus: appointment.status,
          newStatus: 'canceled',
          // ... other history fields
        }
      });
    }
  }

  // 3. Update queue status
  await context.entities.Queue.update({
    where: { id: queueId },
    data: { isActive: false }
  });

  // 4. Broadcast queue state changes
  const io = getIoInstance();
  if (io) {
    await broadcastQueueStateUpdate(queueId, context, io);
  }

  // 5. Send notifications to affected customers
  await notifyCustomersOfQueueClosure(futureAppointments);
};
```

#### Impact on Service Availability
- Services assigned only to deactivated queue become unavailable
- Services assigned to multiple queues remain available through other queues
- Service-specific booking interfaces updated automatically
- Provider dashboard reflects reduced capacity

### Queue Capacity Management Rules

#### Subscription-Based Limits
- **Free Tier**: 1 queue maximum per provider
- **Pro Tier**: 5 queues maximum per provider
- **Enterprise**: Unlimited queues

#### Queue Limit Enforcement
```typescript
interface QueueLimits {
  currentCount: number;
  maxAllowed: number;
  canCreate: boolean;
  upgradeRequired: boolean;
  nextTierLimit?: number;
}

const enforceQueueLimits = async (userId: string, context: any): Promise<QueueLimits> => {
  const user = await context.entities.User.findUnique({
    where: { id: userId },
    select: { queues: true, subscriptionPlan: true }
  });

  const provider = await context.entities.SProvider.findUnique({
    where: { userId: userId },
    select: { id: true }
  });

  const currentCount = await context.entities.Queue.count({
    where: {
      sProvidingPlace: { sProviderId: provider.id }
    }
  });

  return {
    currentCount,
    maxAllowed: user.queues,
    canCreate: currentCount < user.queues,
    upgradeRequired: currentCount >= user.queues,
    nextTierLimit: getNextTierQueueLimit(user.subscriptionPlan)
  };
};
```

## Queue-Specific Operating Hours

### Operating Hours Structure

#### Queue vs Location Hours
- **Location Hours**: General facility operating hours
- **Queue Hours**: Specific resource availability within location hours
- **Independence**: Queue hours can be subset of location hours
- **Validation**: Queue hours cannot exceed location hours

#### Queue Schedule Management
```typescript
interface QueueSchedule {
  queueId: number;
  schedule: QueueDaySchedule[];
}

interface QueueDaySchedule {
  dayOfWeek: string; // "Monday", "Tuesday", etc.
  isActive: boolean; // Is the queue available on this day?
  hours: QueueTimeInterval[]; // Multiple time intervals per day
  type: string; // "regular", "exception", "holiday"
}

interface QueueTimeInterval {
  timeFrom: string; // "09:00"
  timeTo: string;   // "17:00"
}
```

#### Operating Hours Validation
```typescript
const validateQueueOperatingHours = (queueSchedule: QueueSchedule, locationSchedule: LocationSchedule) => {
  for (const queueDay of queueSchedule.schedule) {
    const locationDay = locationSchedule.schedule.find(d => d.dayOfWeek === queueDay.dayOfWeek);

    if (!locationDay || !locationDay.isActive) {
      if (queueDay.isActive && queueDay.hours.length > 0) {
        throw new HttpError(400, `Queue cannot be active on ${queueDay.dayOfWeek} when location is closed`);
      }
    }

    // Validate queue hours are within location hours
    for (const queueInterval of queueDay.hours) {
      const isWithinLocationHours = locationDay.hours.some(locationInterval =>
        queueInterval.timeFrom >= locationInterval.timeFrom &&
        queueInterval.timeTo <= locationInterval.timeTo
      );

      if (!isWithinLocationHours) {
        throw new HttpError(400,
          `Queue hours ${queueInterval.timeFrom}-${queueInterval.timeTo} on ${queueDay.dayOfWeek} ` +
          `exceed location operating hours`
        );
      }
    }

    // Check for overlapping intervals within the same day
    for (let i = 0; i < queueDay.hours.length; i++) {
      for (let j = i + 1; j < queueDay.hours.length; j++) {
        if (intervalsOverlap(queueDay.hours[i], queueDay.hours[j])) {
          throw new HttpError(400, `Overlapping queue hours on ${queueDay.dayOfWeek}`);
        }
      }
    }
  }
};
```

### Queue Opening Hours Management

#### Update Queue Operating Hours
```typescript
export const updateQueueOpenings = async (queueId: number, schedule: QueueDaySchedule[], context: any) => {
  return await prisma.$transaction(async (tx) => {
    // 1. Verify queue ownership
    await verifyQueueOwnership(queueId, context.user.id, tx);

    // 2. Validate new schedule against location hours
    const locationSchedule = await getLocationSchedule(queueId, tx);
    validateQueueOperatingHours({ queueId, schedule }, locationSchedule);

    // 3. Update each day's schedule
    for (const daySchedule of schedule) {
      // Find or create opening record
      let opening = await tx.queueOpening.findFirst({
        where: {
          queueId: queueId,
          dayOfWeek: daySchedule.dayOfWeek,
          type: daySchedule.type || 'regular'
        }
      });

      if (!opening) {
        opening = await tx.queueOpening.create({
          data: {
            queueId: queueId,
            dayOfWeek: daySchedule.dayOfWeek,
            type: daySchedule.type || 'regular',
            isActive: daySchedule.isActive
          }
        });
      } else {
        await tx.queueOpening.update({
          where: { id: opening.id },
          data: { isActive: daySchedule.isActive }
        });
      }

      // 4. Clear existing hours
      await tx.queueOpeningHours.deleteMany({
        where: { queueOpeningId: opening.id }
      });

      // 5. Create new hours
      for (const hours of daySchedule.hours) {
        await tx.queueOpeningHours.create({
          data: {
            queueOpeningId: opening.id,
            timeFrom: hours.timeFrom,
            timeTo: hours.timeTo
          }
        });
      }
    }

    // 6. Validate impact on existing appointments
    await validateScheduleChangeImpact(queueId, schedule, tx);

    // 7. Broadcast queue state changes
    const io = getIoInstance();
    if (io) {
      await broadcastQueueStateUpdate(queueId, context, io);
    }
  });
};
```

#### Schedule Change Impact Validation
```typescript
const validateScheduleChangeImpact = async (queueId: number, newSchedule: QueueDaySchedule[], tx: any) => {
  // Get future appointments for this queue
  const futureAppointments = await tx.appointment.findMany({
    where: {
      queueId: queueId,
      expectedAppointmentStartTime: { gte: new Date() },
      status: { notIn: ['canceled', 'completed', 'noshow'] }
    }
  });

  // Check if any appointments fall outside new operating hours
  const conflictingAppointments = [];

  for (const appointment of futureAppointments) {
    const appointmentDay = appointment.expectedAppointmentStartTime.toLocaleDateString('en-US', { weekday: 'long' });
    const appointmentTime = appointment.expectedAppointmentStartTime.toTimeString().slice(0, 5);

    const daySchedule = newSchedule.find(d => d.dayOfWeek === appointmentDay);

    if (!daySchedule || !daySchedule.isActive) {
      conflictingAppointments.push(appointment);
      continue;
    }

    const isWithinHours = daySchedule.hours.some(interval =>
      appointmentTime >= interval.timeFrom && appointmentTime <= interval.timeTo
    );

    if (!isWithinHours) {
      conflictingAppointments.push(appointment);
    }
  }

  if (conflictingAppointments.length > 0) {
    throw new HttpError(409,
      `Schedule change conflicts with ${conflictingAppointments.length} existing appointments. ` +
      `Please reschedule these appointments first.`
    );
  }
};

## Service Assignment Management

### Service-Queue Relationship

#### Many-to-Many Relationship
- **Multiple Services per Queue**: A queue can offer multiple services
- **Multiple Queues per Service**: A service can be offered by multiple queues
- **Location Constraint**: Services and queues must belong to same location
- **Provider Ownership**: Both services and queues must belong to same provider

#### Service Assignment Validation
```typescript
const validateServiceAssignment = async (queueId: number, serviceIds: number[], context: any) => {
  // 1. Verify queue ownership
  const queue = await context.entities.Queue.findFirst({
    where: {
      id: queueId,
      sProvidingPlace: {
        provider: { userId: context.user.id }
      }
    },
    include: {
      sProvidingPlace: { select: { id: true, sProviderId: true } }
    }
  });

  if (!queue) {
    throw new HttpError(404, 'Queue not found or not owned by provider');
  }

  // 2. Verify service ownership and location match
  const services = await context.entities.Service.findMany({
    where: {
      id: { in: serviceIds },
      provider: { userId: context.user.id }
    }
  });

  if (services.length !== serviceIds.length) {
    throw new HttpError(403, 'One or more services not found or not owned by provider');
  }

  // 3. Validate services can be offered at this location
  // (Additional business logic can be added here)

  return { queue, services };
};
```

### Service Assignment Operations

#### Assign Service to Queue
```typescript
export const assignServiceToQueue = async (queueId: number, serviceId: number, context: any) => {
  // 1. Validate assignment
  await validateServiceAssignment(queueId, [serviceId], context);

  // 2. Check if already assigned
  const existingAssignment = await context.entities.Queue.findFirst({
    where: {
      id: queueId,
      services: {
        some: { id: serviceId }
      }
    }
  });

  if (existingAssignment) {
    throw new HttpError(409, 'Service is already assigned to this queue');
  }

  // 3. Assign service to queue
  const updatedQueue = await context.entities.Queue.update({
    where: { id: queueId },
    data: {
      services: {
        connect: { id: serviceId }
      }
    },
    include: {
      services: {
        select: { id: true, title: true, duration: true, color: true }
      }
    }
  });

  // 4. Broadcast queue state changes
  const io = getIoInstance();
  if (io) {
    await broadcastQueueStateUpdate(queueId, context, io);
  }

  return updatedQueue;
};
```

#### Remove Service from Queue
```typescript
export const removeServiceFromQueue = async (queueId: number, serviceId: number, context: any) => {
  // 1. Validate ownership
  await verifyQueueOwnership(queueId, context.user.id, context.entities);

  // 2. Check if service is assigned
  const queue = await context.entities.Queue.findFirst({
    where: {
      id: queueId,
      services: {
        some: { id: serviceId }
      }
    },
    include: {
      services: true
    }
  });

  if (!queue) {
    throw new HttpError(404, 'Service is not assigned to this queue');
  }

  // 3. Prevent removal if it's the last service
  if (queue.services.length <= 1) {
    throw new HttpError(409, 'Cannot remove the last service from a queue. At least one service must be assigned.');
  }

  // 4. Check for active appointments with this service
  const activeAppointments = await context.entities.Appointment.count({
    where: {
      queueId: queueId,
      serviceId: serviceId,
      status: { notIn: ['canceled', 'completed', 'noshow'] },
      expectedAppointmentStartTime: { gte: new Date() }
    }
  });

  if (activeAppointments > 0) {
    throw new HttpError(409,
      `Cannot remove service: ${activeAppointments} active appointments exist for this service in this queue. ` +
      `Please complete or reschedule these appointments first.`
    );
  }

  // 5. Remove service from queue
  const updatedQueue = await context.entities.Queue.update({
    where: { id: queueId },
    data: {
      services: {
        disconnect: { id: serviceId }
      }
    },
    include: {
      services: {
        select: { id: true, title: true, duration: true, color: true }
      }
    }
  });

  // 6. Broadcast queue state changes
  const io = getIoInstance();
  if (io) {
    await broadcastQueueStateUpdate(queueId, context, io);
  }

  return updatedQueue;
};
```

#### Bulk Service Assignment
```typescript
export const updateQueueServices = async (queueId: number, serviceIds: number[], context: any) => {
  return await prisma.$transaction(async (tx) => {
    // 1. Validate all service assignments
    await validateServiceAssignment(queueId, serviceIds, { ...context, entities: tx });

    // 2. Check for active appointments with services being removed
    const currentQueue = await tx.queue.findUnique({
      where: { id: queueId },
      include: { services: { select: { id: true } } }
    });

    const currentServiceIds = currentQueue.services.map(s => s.id);
    const removedServiceIds = currentServiceIds.filter(id => !serviceIds.includes(id));

    if (removedServiceIds.length > 0) {
      const activeAppointments = await tx.appointment.count({
        where: {
          queueId: queueId,
          serviceId: { in: removedServiceIds },
          status: { notIn: ['canceled', 'completed', 'noshow'] },
          expectedAppointmentStartTime: { gte: new Date() }
        }
      });

      if (activeAppointments > 0) {
        throw new HttpError(409,
          `Cannot remove services: ${activeAppointments} active appointments exist for the services being removed. ` +
          `Please complete or reschedule these appointments first.`
        );
      }
    }

    // 3. Update service assignments
    const updatedQueue = await tx.queue.update({
      where: { id: queueId },
      data: {
        services: {
          set: serviceIds.map(id => ({ id }))
        }
      },
      include: {
        services: {
          select: { id: true, title: true, duration: true, color: true }
        }
      }
    });

    return updatedQueue;
  });
};
```

### Service-Specific Queue Configuration

#### Service Duration Impact
```typescript
interface ServiceQueueConfig {
  serviceId: number;
  queueId: number;
  customDuration?: number; // Override default service duration for this queue
  bufferTime?: number; // Additional time between appointments for this service
  maxConcurrent?: number; // Maximum concurrent appointments for this service
  priority?: number; // Service priority within the queue
}
```

#### Queue Capacity Calculation
```typescript
const calculateQueueCapacity = async (queueId: number, date: Date, context: any) => {
  // 1. Get queue operating hours for the date
  const queueHours = await getQueueOperatingHours(queueId, date, context);

  // 2. Get assigned services and their durations
  const queue = await context.entities.Queue.findUnique({
    where: { id: queueId },
    include: {
      services: {
        select: { id: true, duration: true, title: true }
      }
    }
  });

  // 3. Calculate total available minutes
  const totalMinutes = queueHours.reduce((total, interval) => {
    const start = dayjs(`${date.toISOString().split('T')[0]}T${interval.timeFrom}`);
    const end = dayjs(`${date.toISOString().split('T')[0]}T${interval.timeTo}`);
    return total + end.diff(start, 'minutes');
  }, 0);

  // 4. Calculate capacity for each service
  const serviceCapacities = queue.services.map(service => ({
    serviceId: service.id,
    serviceName: service.title,
    duration: service.duration,
    maxAppointments: Math.floor(totalMinutes / service.duration),
    totalMinutes
  }));

  // 5. Get existing appointments
  const existingAppointments = await context.entities.Appointment.count({
    where: {
      queueId: queueId,
      expectedAppointmentStartTime: {
        gte: dayjs(date).startOf('day').toDate(),
        lte: dayjs(date).endOf('day').toDate()
      },
      status: { notIn: ['canceled', 'noshow'] }
    }
  });

  return {
    queueId,
    date: date.toISOString().split('T')[0],
    totalMinutes,
    existingAppointments,
    availableMinutes: totalMinutes - (existingAppointments * 30), // Assuming 30min average
    serviceCapacities
  };
};
```

## Real-Time Queue State Tracking

### WebSocket Integration

#### Queue State Broadcasting
```typescript
interface QueueStateUpdate {
  queueId: number;
  position: number | null; // Current position (1-based) or null if not in queue
  estimatedStartTime: string | null; // ISO string or null
  estimatedWaitMinutes: number | null; // Wait time in minutes or null
  error?: string; // Optional error message
}
```

#### Real-Time Event Broadcasting
```typescript
export async function broadcastQueueStateUpdate(
  queueId: number,
  context: any,
  io: Server,
  triggeringSocket?: Socket
) {
  try {
    // 1. Get all users with appointments in this queue
    const queueAppointments = await context.entities.Appointment.findMany({
      where: {
        queueId: queueId,
        status: { in: ['pending', 'confirmed', 'InProgress'] },
        expectedAppointmentStartTime: { gte: new Date() }
      },
      include: {
        customerFolder: {
          select: { userId: true }
        }
      },
      orderBy: {
        expectedAppointmentStartTime: 'asc'
      }
    });

    // 2. Calculate queue positions and wait times
    for (let i = 0; i < queueAppointments.length; i++) {
      const appointment = queueAppointments[i];
      const targetUserId = appointment.customerFolder.userId;

      try {
        // Calculate position and wait time
        const result = await calculateQueuePosition(appointment, queueAppointments, context);

        const isSelf = triggeringSocket && triggeringSocket.data.user?.id === targetUserId;
        const roomName = `user_${targetUserId}`;

        // Emit update to specific user
        io.to(roomName).emit('queueUpdate', { queueId, ...result });

        console.log(`Queue ${queueId}: Emitted update to user ${targetUserId} (position: ${result.position})`);
      } catch (userUpdateError: any) {
        console.error(`Failed to update user ${targetUserId} for queue ${queueId}:`, userUpdateError);
      }
    }

    // 3. Notify providers about queue changes
    const queue = await context.entities.Queue.findUnique({
      where: { id: queueId },
      include: {
        sProvidingPlace: {
          include: {
            provider: {
              select: { userId: true }
            }
          }
        }
      }
    });

    if (queue?.sProvidingPlace?.provider?.userId) {
      const providerRoomName = `user_${queue.sProvidingPlace.provider.userId}`;
      io.to(providerRoomName).emit('queueUpdate', {
        queueId,
        type: 'provider_update',
        appointmentCount: queueAppointments.length,
        nextAppointment: queueAppointments[0] || null
      });
    }

  } catch (error: any) {
    console.error('Error broadcasting queue state:', error);
  }
}
```

#### Queue Position Calculation
```typescript
const calculateQueuePosition = async (appointment: any, allAppointments: any[], context: any) => {
  // 1. Find position in queue (1-based)
  const position = allAppointments.findIndex(apt => apt.id === appointment.id) + 1;

  // 2. Calculate estimated start time
  let estimatedStartTime = appointment.expectedAppointmentStartTime;

  // If there are appointments before this one, calculate cumulative delay
  const appointmentsBefore = allAppointments.slice(0, position - 1);
  let cumulativeDelay = 0;

  for (const prevAppointment of appointmentsBefore) {
    if (prevAppointment.status === 'InProgress' && prevAppointment.realAppointmentStartTime) {
      // Calculate how much this appointment is running over
      const expectedDuration = prevAppointment.serviceDuration || 30; // Default 30 minutes
      const actualDuration = (new Date().getTime() - prevAppointment.realAppointmentStartTime.getTime()) / (1000 * 60);
      const overrun = Math.max(0, actualDuration - expectedDuration);
      cumulativeDelay += overrun;
    }
  }

  // 3. Adjust estimated start time with cumulative delay
  if (cumulativeDelay > 0) {
    estimatedStartTime = new Date(estimatedStartTime.getTime() + (cumulativeDelay * 60 * 1000));
  }

  // 4. Calculate wait time in minutes
  const now = new Date();
  const estimatedWaitMinutes = Math.max(0, Math.round((estimatedStartTime.getTime() - now.getTime()) / (1000 * 60)));

  return {
    position: position > 0 ? position : null,
    estimatedStartTime: estimatedStartTime.toISOString(),
    estimatedWaitMinutes
  };
};
```

### Client-Side Integration

#### React Hook for Queue Updates
```typescript
const useQueueUpdates = (queueId: number) => {
  const [queueState, setQueueState] = useState<QueueStateUpdate | null>(null);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    const socket = io();

    socket.on('connect', () => {
      setIsConnected(true);
      // Request initial queue status
      socket.emit('requestQueueStatus');
    });

    socket.on('disconnect', () => {
      setIsConnected(false);
    });

    // Listen for queue updates
    socket.on('queueUpdate', (data: QueueStateUpdate) => {
      if (data.queueId === queueId) {
        setQueueState(data);
      }
    });

    // Notify server of queue changes (for providers)
    const notifyQueueChange = () => {
      socket.emit('notifyQueueChange', { queueId });
    };

    return () => {
      socket.disconnect();
    };
  }, [queueId]);

  return { queueState, isConnected };
};
```

#### Provider Dashboard Integration
```typescript
const ProviderQueueDashboard = ({ queueId }: { queueId: number }) => {
  const { queueState, isConnected } = useQueueUpdates(queueId);
  const [appointments, setAppointments] = useState<Appointment[]>([]);

  // Update appointments when queue state changes
  useEffect(() => {
    if (queueState?.type === 'provider_update') {
      // Refresh appointment list
      fetchQueueAppointments(queueId).then(setAppointments);
    }
  }, [queueState, queueId]);

  return (
    <div className="queue-dashboard">
      <div className="queue-status">
        <h3>Queue: {queueId}</h3>
        <div className={`connection-status ${isConnected ? 'connected' : 'disconnected'}`}>
          {isConnected ? 'Connected' : 'Disconnected'}
        </div>
      </div>

      <div className="appointment-list">
        {appointments.map((appointment, index) => (
          <div key={appointment.id} className="appointment-item">
            <span className="position">#{index + 1}</span>
            <span className="customer">{appointment.customerFolder.customer.firstName}</span>
            <span className="service">{appointment.service.title}</span>
            <span className="time">{formatTime(appointment.expectedAppointmentStartTime)}</span>
            <span className={`status ${appointment.status}`}>{appointment.status}</span>
          </div>
        ))}
      </div>
    </div>
  );
};
```

## Queue Capacity Management

### Subscription-Based Queue Limits

#### Queue Limit Tiers
```typescript
interface QueueLimitTier {
  name: string;
  maxQueues: number;
  maxAppointmentsPerQueue: number;
  realTimeUpdates: boolean;
  advancedAnalytics: boolean;
  prioritySupport: boolean;
}

const QUEUE_LIMIT_TIERS: Record<string, QueueLimitTier> = {
  free: {
    name: 'Free',
    maxQueues: 1,
    maxAppointmentsPerQueue: 50,
    realTimeUpdates: false,
    advancedAnalytics: false,
    prioritySupport: false
  },
  pro: {
    name: 'Pro',
    maxQueues: 5,
    maxAppointmentsPerQueue: 200,
    realTimeUpdates: true,
    advancedAnalytics: true,
    prioritySupport: false
  },
  enterprise: {
    name: 'Enterprise',
    maxQueues: -1, // Unlimited
    maxAppointmentsPerQueue: -1, // Unlimited
    realTimeUpdates: true,
    advancedAnalytics: true,
    prioritySupport: true
  }
};
```

#### Queue Limit Enforcement
```typescript
export const enforceQueueLimits = async (userId: string, context: any) => {
  const user = await context.entities.User.findUnique({
    where: { id: userId },
    select: {
      queues: true,
      subscriptionPlan: true,
      subscriptionStatus: true
    }
  });

  if (!user) {
    throw new HttpError(404, 'User not found');
  }

  const tier = QUEUE_LIMIT_TIERS[user.subscriptionPlan || 'free'];

  // Check subscription status
  if (user.subscriptionStatus !== 'active' && user.subscriptionPlan !== 'free') {
    throw new HttpError(403, 'Subscription required. Please update your payment method.');
  }

  // Get current queue count
  const provider = await context.entities.SProvider.findUnique({
    where: { userId: userId },
    select: { id: true }
  });

  const currentQueueCount = await context.entities.Queue.count({
    where: {
      sProvidingPlace: { sProviderId: provider.id }
    }
  });

  const canCreateMore = tier.maxQueues === -1 || currentQueueCount < tier.maxQueues;

  return {
    currentCount: currentQueueCount,
    maxAllowed: tier.maxQueues,
    canCreate: canCreateMore,
    tier: tier.name,
    upgradeRequired: !canCreateMore,
    features: {
      realTimeUpdates: tier.realTimeUpdates,
      advancedAnalytics: tier.advancedAnalytics,
      prioritySupport: tier.prioritySupport
    }
  };
};

### Queue Overflow Handling

#### Overflow Detection
```typescript
interface QueueOverflowConfig {
  queueId: number;
  maxCapacityPercentage: number; // 100% = normal capacity, 120% = 20% overflow
  overflowThresholdMinutes: number; // Minutes beyond normal hours
  autoRescheduleEnabled: boolean;
  notificationEnabled: boolean;
}

const detectQueueOverflow = async (queueId: number, date: Date, context: any) => {
  // 1. Calculate normal queue capacity
  const normalCapacity = await calculateQueueCapacity(queueId, date, context);

  // 2. Get all appointments for the date
  const appointments = await context.entities.Appointment.findMany({
    where: {
      queueId: queueId,
      expectedAppointmentStartTime: {
        gte: dayjs(date).startOf('day').toDate(),
        lte: dayjs(date).endOf('day').toDate()
      },
      status: { notIn: ['canceled', 'noshow'] }
    },
    orderBy: { expectedAppointmentStartTime: 'asc' }
  });

  // 3. Calculate if appointments extend beyond normal hours
  const lastAppointment = appointments[appointments.length - 1];
  const queueEndTime = getQueueEndTime(queueId, date);

  const isOverflowing = lastAppointment &&
    lastAppointment.expectedAppointmentEndTime > queueEndTime;

  if (isOverflowing) {
    const overflowMinutes = dayjs(lastAppointment.expectedAppointmentEndTime)
      .diff(dayjs(queueEndTime), 'minutes');

    // Mark appointments as overflowed
    await markAppointmentsAsOverflowed(appointments, queueEndTime, context);

    return {
      isOverflowing: true,
      overflowMinutes,
      affectedAppointments: appointments.filter(apt =>
        apt.expectedAppointmentStartTime >= queueEndTime
      ).length
    };
  }

  return { isOverflowing: false };
};
```

#### Appointment Shifting Logic
```typescript
const shiftAppointmentsAfterDelay = async (queueId: number, delayMinutes: number, fromTime: Date, context: any) => {
  return await prisma.$transaction(async (tx) => {
    // 1. Get all appointments after the delay point
    const appointmentsToShift = await tx.appointment.findMany({
      where: {
        queueId: queueId,
        expectedAppointmentStartTime: { gte: fromTime },
        status: { notIn: ['canceled', 'completed', 'noshow'] }
      },
      orderBy: { expectedAppointmentStartTime: 'asc' }
    });

    // 2. Shift each appointment
    const shiftedAppointments = [];

    for (const appointment of appointmentsToShift) {
      const newStartTime = new Date(
        appointment.expectedAppointmentStartTime.getTime() + (delayMinutes * 60 * 1000)
      );
      const newEndTime = new Date(
        appointment.expectedAppointmentEndTime.getTime() + (delayMinutes * 60 * 1000)
      );

      // Update appointment times
      const updatedAppointment = await tx.appointment.update({
        where: { id: appointment.id },
        data: {
          expectedAppointmentStartTime: newStartTime,
          expectedAppointmentEndTime: newEndTime
        }
      });

      // Create appointment history
      await tx.appointmentHistory.create({
        data: {
          appointmentId: appointment.id,
          changedByUserId: context.user.id,
          changeReason: `Shifted due to ${delayMinutes} minute delay`,
          previousStartTime: appointment.expectedAppointmentStartTime,
          previousEndTime: appointment.expectedAppointmentEndTime,
          newStartTime: newStartTime,
          newEndTime: newEndTime,
          previousStatus: appointment.status,
          newStatus: appointment.status,
          previousMotifId: appointment.serviceId,
          newMotifId: appointment.serviceId,
          previousAgendaId: appointment.queueId,
          newAgendaId: appointment.queueId
        }
      });

      shiftedAppointments.push(updatedAppointment);
    }

    // 3. Check for overflow after shifting
    const overflowCheck = await detectQueueOverflow(queueId, fromTime, { ...context, entities: tx });

    // 4. Handle overflow appointments
    if (overflowCheck.isOverflowing) {
      await handleOverflowAppointments(queueId, shiftedAppointments, { ...context, entities: tx });
    }

    return {
      shiftedCount: shiftedAppointments.length,
      delayMinutes,
      overflowDetected: overflowCheck.isOverflowing,
      affectedAppointments: shiftedAppointments
    };
  });
};
```

## Error Handling

### HTTP Status Codes

#### Success Responses
- **200 OK**: Successful GET, PUT operations
- **201 Created**: Successful POST operations (queue creation)
- **204 No Content**: Successful DELETE operations

#### Client Error Responses
- **400 Bad Request**: Invalid request data, validation errors
- **401 Unauthorized**: Authentication required
- **403 Forbidden**: Queue limit exceeded, insufficient permissions
- **404 Not Found**: Queue not found
- **409 Conflict**: Queue title conflicts, active appointments prevent deletion
- **422 Unprocessable Entity**: Business logic validation failures

#### Server Error Responses
- **500 Internal Server Error**: Unexpected server errors
- **503 Service Unavailable**: WebSocket service unavailable

### Error Response Format
```typescript
interface QueueErrorResponse {
  success: false;
  message: string;
  code?: string;
  statusCode: number;
  details?: {
    field?: string;
    value?: any;
    constraint?: string;
    queueId?: number;
    currentCount?: number;
    maxAllowed?: number;
  };
  timestamp: Date;
  path: string;
}
```

### Common Error Scenarios

#### Queue Limit Errors
```typescript
// Queue creation limit exceeded
{
  "success": false,
  "message": "Queue limit reached. You can create up to 1 queue(s). Please upgrade your subscription to create more queues.",
  "statusCode": 403,
  "code": "QUEUE_LIMIT_EXCEEDED",
  "details": {
    "currentCount": 1,
    "maxAllowed": 1,
    "upgradeRequired": true
  }
}
```

#### Business Rule Violations
```typescript
// Cannot delete queue with active appointments
{
  "success": false,
  "message": "Cannot delete queue: It has 3 active appointments. Please complete or cancel these appointments first.",
  "statusCode": 409,
  "code": "QUEUE_HAS_ACTIVE_APPOINTMENTS",
  "details": {
    "queueId": 5,
    "activeAppointments": 3,
    "appointmentIds": [123, 124, 125]
  }
}
```

#### Service Assignment Errors
```typescript
// Service assignment validation error
{
  "success": false,
  "message": "Cannot remove the last service from a queue. At least one service must be assigned.",
  "statusCode": 409,
  "code": "QUEUE_REQUIRES_SERVICE",
  "details": {
    "queueId": 5,
    "currentServiceCount": 1,
    "minimumRequired": 1
  }
}
```

### Error Handling Patterns

#### Try-Catch with HttpError
```typescript
export const createProviderQueue = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Validate authentication
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Check queue limits
    const queueLimitCheck = await checkQueueCreationLimit(context.user.id, context);
    if (!queueLimitCheck.canCreate) {
      return sendError(res, {
        statusCode: 403,
        message: queueLimitCheck.message,
        code: 'QUEUE_LIMIT_EXCEEDED',
        details: {
          currentCount: queueLimitCheck.currentCount,
          maxAllowed: queueLimitCheck.maxAllowed
        }
      });
    }

    // Validate request data
    const queueData = validateAndExtract(createQueueSchema, req.body);

    // Call operation
    const newQueue = await createProviderQueueOp(queueData, context);

    return sendCreated(res, newQueue, 'Queue created successfully');

  } catch (error: any) {
    console.error('[createProviderQueue] Error:', error);

    if (error instanceof HttpError) {
      return sendError(res, {
        statusCode: error.statusCode,
        message: error.message,
        code: error.code
      });
    }

    return sendError(res, error, 'Failed to create queue');
  }
});
```

---

## API Reference Summary

### Complete Endpoint List

#### Provider Queue Management
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/auth/providers/queues` | Get all provider queues | Yes |
| POST | `/api/auth/providers/queues` | Create new queue | Yes |
| PUT | `/api/auth/providers/queues/:id` | Update queue | Yes |
| DELETE | `/api/auth/providers/queues/:id` | Delete queue | Yes |

#### Queue Capacity Management
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/auth/providers/queues/can-create` | Check if can create more queues | Yes |
| GET | `/api/auth/providers/queues/limits` | Get queue limits and usage | Yes |

#### Queue Service Assignment
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/auth/providers/queues/:id/services` | Get queue services | Yes |
| POST | `/api/auth/providers/queues/:id/services` | Assign service to queue | Yes |
| DELETE | `/api/auth/providers/queues/:id/services/:serviceId` | Remove service from queue | Yes |

#### Queue Operating Hours
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/auth/providers/queues/:id/hours` | Get queue operating hours | Yes |
| PUT | `/api/auth/providers/queues/:id/hours` | Update queue operating hours | Yes |

### Request/Response Examples

#### Create Queue Request
```json
POST /api/auth/providers/queues
{
  "title": "General Consultation Queue",
  "sProvidingPlaceId": 15,
  "isActive": true,
  "serviceIds": [5, 6, 7],
  "openingHours": [
    {
      "dayOfWeek": "Monday",
      "isActive": true,
      "hours": [
        {
          "timeFrom": "09:00",
          "timeTo": "12:00"
        },
        {
          "timeFrom": "14:00",
          "timeTo": "17:00"
        }
      ]
    },
    {
      "dayOfWeek": "Tuesday",
      "isActive": true,
      "hours": [
        {
          "timeFrom": "09:00",
          "timeTo": "17:00"
        }
      ]
    }
  ]
}
```

#### Create Queue Response
```json
{
  "success": true,
  "data": {
    "id": 8,
    "title": "General Consultation Queue",
    "isActive": true,
    "sProvidingPlaceId": 15,
    "services": [
      {
        "id": 5,
        "title": "General Consultation"
      },
      {
        "id": 6,
        "title": "Follow-up Visit"
      },
      {
        "id": 7,
        "title": "Health Checkup"
      }
    ],
    "openingHours": [
      {
        "dayOfWeek": "Monday",
        "isActive": true,
        "hours": [
          {
            "timeFrom": "09:00",
            "timeTo": "12:00"
          },
          {
            "timeFrom": "14:00",
            "timeTo": "17:00"
          }
        ]
      },
      {
        "dayOfWeek": "Tuesday",
        "isActive": true,
        "hours": [
          {
            "timeFrom": "09:00",
            "timeTo": "17:00"
          }
        ]
      }
    ]
  },
  "message": "Queue created successfully"
}
```

#### Update Queue Request
```json
PUT /api/auth/providers/queues/8
{
  "title": "Priority Consultation Queue",
  "isActive": true,
  "serviceIds": [5, 6],
  "openingHours": [
    {
      "dayOfWeek": "Monday",
      "isActive": true,
      "hours": [
        {
          "timeFrom": "08:00",
          "timeTo": "18:00"
        }
      ]
    }
  ]
}
```

#### Get Provider Queues Response
```json
{
  "success": true,
  "data": [
    {
      "id": 8,
      "title": "Priority Consultation Queue",
      "isActive": true,
      "sProvidingPlaceId": 15,
      "services": [
        {
          "id": 5,
          "title": "General Consultation"
        },
        {
          "id": 6,
          "title": "Follow-up Visit"
        }
      ],
      "openingHours": [
        {
          "dayOfWeek": "Monday",
          "isActive": true,
          "hours": [
            {
              "timeFrom": "08:00",
              "timeTo": "18:00"
            }
          ]
        }
      ]
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 1,
    "totalPages": 1,
    "hasNext": false,
    "hasPrev": false
  }
}
```

#### Check Queue Creation Limits Response
```json
{
  "success": true,
  "data": {
    "canCreate": false,
    "reason": "Queue limit reached. You can create up to 1 queue(s). Please upgrade your subscription to create more queues.",
    "currentCount": 1,
    "maxAllowed": 1,
    "tier": "Free",
    "upgradeRequired": true,
    "features": {
      "realTimeUpdates": false,
      "advancedAnalytics": false,
      "prioritySupport": false
    }
  }
}
```

---

## Implementation Notes

### Performance Considerations
- **Database Indexing**: Index on `sProvidingPlaceId`, `isActive`, and service relationships
- **Real-time Optimization**: Efficient WebSocket broadcasting with room-based targeting
- **Queue State Caching**: Cache queue states for frequently accessed queues
- **Batch Operations**: Use transactions for multi-step queue operations

### Security Best Practices
- **Input Validation**: Comprehensive validation using Zod schemas
- **Ownership Verification**: Strict queue ownership checks for all operations
- **Rate Limiting**: Implement rate limiting on queue creation and updates
- **Audit Logging**: Maintain comprehensive audit trails for queue changes

### Monitoring & Observability
- **Queue Analytics**: Track queue utilization, wait times, and overflow incidents
- **Real-time Monitoring**: Monitor WebSocket connections and broadcast performance
- **Capacity Planning**: Track queue limits and subscription usage
- **Performance Metrics**: Monitor queue operation response times and error rates

### Testing Strategy
- **Unit Tests**: Test queue CRUD operations and validation logic
- **Integration Tests**: Test complete queue management workflows
- **Real-time Tests**: Test WebSocket broadcasting and queue state updates
- **Load Testing**: Verify system performance under high queue activity

This documentation provides a comprehensive overview of the Provider Queue Management system, covering all aspects from database schema to API implementation, business rules, real-time state tracking, service assignment management, and technical considerations. The system supports subscription-based queue limits, real-time updates, comprehensive service assignment, and robust overflow handling capabilities.
```
```
