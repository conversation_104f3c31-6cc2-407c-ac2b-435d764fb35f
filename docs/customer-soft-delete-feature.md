# Customer Soft Delete Feature

## Overview

The Customer Soft Delete feature allows providers to delete customers without permanently removing them from the database. This provides a safety mechanism for data recovery and maintains referential integrity with related records like appointments.

## Features Implemented

### 1. Database Schema Changes
- Added `deleted` field to `CustomerFolder` model (Boolean, default: false)
- Maintains all existing relationships and constraints

### 2. API Endpoints

#### Soft Delete Customer
- **Endpoint**: `DELETE /api/auth/providers/customers/:id`
- **Description**: Marks a customer as deleted without removing from database
- **Authentication**: Required
- **Response**: Returns the deleted customer data

#### Restore Customer
- **Endpoint**: `POST /api/auth/providers/customers/restore`
- **Description**: Restores a previously soft-deleted customer using customer identifiers
- **Authentication**: Required
- **Payload**: Requires at least one identifier (email, mobileNumber, or nationalId)
- **Response**: Returns the restored customer data

### 3. Business Logic

#### Customer Listing
- `GET /api/auth/providers/customers` now excludes soft-deleted customers by default
- Only active customers (deleted = false) are returned in normal queries

#### Customer Creation
- When creating a new customer, the system checks for existing soft-deleted customers
- If a soft-deleted customer exists with the same identifiers, returns error message:
  ```
  "Customer exists in archive. If you want to restore it, please use the restore customer endpoint."
  ```

#### Validation Rules
- Cannot soft delete an already deleted customer
- Cannot restore a customer that is not deleted
- Customer must belong to the authenticated provider

## API Usage Examples

### Soft Delete a Customer
```bash
curl -X DELETE \
  https://api.example.com/api/auth/providers/customers/customer-uuid-123 \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**Response:**
```json
{
  "success": true,
  "message": "Provider customer deleted successfully",
  "data": {
    "id": "customer-uuid-123",
    "firstName": "John",
    "lastName": "Doe",
    "mobileNumber": "+************",
    "email": "<EMAIL>",
    "nationalId": "1234567890123",
    "notes": "Regular customer",
    "appointmentCount": 0,
    "createdAt": "2024-01-15T10:30:00Z"
  }
}
```

### Restore a Customer

#### Using Email
```bash
curl -X POST \
  https://api.example.com/api/auth/providers/customers/restore \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>"
  }'
```

#### Using Mobile Number
```bash
curl -X POST \
  https://api.example.com/api/auth/providers/customers/restore \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "mobileNumber": "+************"
  }'
```

#### Using National ID
```bash
curl -X POST \
  https://api.example.com/api/auth/providers/customers/restore \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "nationalId": "1234567890123"
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "Provider customer restored successfully",
  "data": {
    "id": "customer-uuid-123",
    "firstName": "John",
    "lastName": "Doe",
    "mobileNumber": "+************",
    "email": "<EMAIL>",
    "nationalId": "1234567890123",
    "notes": "Regular customer",
    "appointmentCount": 0,
    "createdAt": "2024-01-15T10:30:00Z"
  }
}
```

### Error Scenarios

#### Trying to Create Duplicate Customer
```bash
curl -X POST \
  https://api.example.com/api/auth/providers/customers \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "firstName": "John",
    "lastName": "Doe",
    "mobileNumber": "+************"
  }'
```

**Response (when archived customer exists):**
```json
{
  "success": false,
  "message": "Customer exists in archive. If you want to restore it, please use the restore customer endpoint.",
  "statusCode": 409
}
```

## Implementation Details

### Database Migration
```sql
ALTER TABLE "CustomerFolder" ADD COLUMN "deleted" BOOLEAN NOT NULL DEFAULT false;
```

### Key Files Modified
1. `app/schema.prisma` - Added deleted field to CustomerFolder model
2. `app/src/provider/operations.ts` - Added soft delete and restore operations
3. `app/src/provider/mobile/handlers/customers.ts` - Added API handlers
4. `app/main.wasp` - Registered new API endpoints
5. `app/docs/provider-mobile-api.yaml` - Updated API documentation

### Operations Added
- `softDeleteProviderCustomer` - Marks customer as deleted
- `restoreProviderCustomer` - Restores deleted customer
- Updated `getProviderCustomers` - Excludes deleted customers
- Updated `createProviderCustomer` - Checks for archived customers

## Testing

### Test Coverage
- Soft delete functionality
- Restore functionality
- Duplicate customer prevention
- Error handling for edge cases
- Data isolation between providers

### Test File
- `app/src/provider/mobile/handlers/customers.test.ts`

## Benefits

1. **Data Safety**: Customers are never permanently lost
2. **Referential Integrity**: Related appointments and history are preserved
3. **User Experience**: Clear error messages guide users to restore functionality
4. **Audit Trail**: Maintains complete customer interaction history
5. **Compliance**: Supports data retention policies and regulations

## Migration Notes

- Existing customers automatically have `deleted = false`
- No breaking changes to existing API endpoints
- Backward compatible with existing client applications
- New endpoints follow existing authentication and authorization patterns

## Future Enhancements

1. **Admin Interface**: Add admin panel to view and manage archived customers
2. **Bulk Operations**: Support bulk delete and restore operations
3. **Retention Policies**: Implement automatic permanent deletion after specified periods
4. **Audit Logging**: Track who deleted/restored customers and when
5. **Search Integration**: Allow searching within archived customers
