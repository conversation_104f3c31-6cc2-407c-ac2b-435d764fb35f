# Provider Locations Management System Documentation

## Overview

The Provider Locations Management system in the Wasp.js application provides comprehensive functionality for healthcare providers to manage their physical locations, including CRUD operations, operating hours management, address validation, multi-location support, and integration with queues and services.

## Table of Contents

1. [Database Schema](#database-schema)
2. [Provider Location CRUD Operations](#provider-location-crud-operations)
3. [API Endpoints](#api-endpoints)
4. [Authentication & Authorization](#authentication--authorization)
5. [Business Rules & Workflows](#business-rules--workflows)
6. [Operating Hours Management](#operating-hours-management)
7. [Address Validation & Geocoding](#address-validation--geocoding)
8. [Multi-Location Provider Support](#multi-location-provider-support)
9. [File Upload Support](#file-upload-support)
10. [Error Handling](#error-handling)

## Database Schema

### Core Models

#### SProvidingPlace (Location) Model
```prisma
model SProvidingPlace {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  provider    SProvider @relation(fields: [sProviderId], references: [id])
  sProviderId Int

  name           String
  shortName      String?
  address        String?
  city           String?
  mobile         String?
  isMobileHidden Boolean @default(false)
  fax            String?
  floor          String?
  parking        Boolean @default(false)
  elevator       Boolean @default(false)
  handicapAccess Boolean @default(false)
  timezone       String?

  appointments Appointment[]
  openings     Opening[]
  queues       Queue[]

  // Structured Address relation
  detailedAddress   Address? @relation(fields: [detailedAddressId], references: [id], onDelete: SetNull)
  detailedAddressId Int?     @unique
}
```

#### Address Model
```prisma
model Address {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  address    String
  city       String
  state      String? // Optional as not all countries use states
  postalCode String
  country    String  @default("Algeria")

  latitude  Float // For geolocation
  longitude Float // For geolocation

  description String? // e.g., "Home", "Work", "Clinic Main Entrance"
  isPrimary   Boolean @default(false)

  // Relations
  user   User?   @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId String?

  sProvidingPlace SProvidingPlace?
}
```

#### Opening Model (Operating Hours)
```prisma
model Opening {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  place             SProvidingPlace @relation(fields: [sProvidingPlaceId], references: [id])
  sProvidingPlaceId Int

  dayOfWeek String  // "Monday", "Tuesday", etc.
  type      String  @default("regular") // "regular", "holiday", "special"
  isActive  Boolean @default(true)

  hours OpeningHours[]

  @@unique([sProvidingPlaceId, dayOfWeek, type])
}
```

#### OpeningHours Model
```prisma
model OpeningHours {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  opening   Opening @relation(fields: [openingId], references: [id])
  openingId Int

  timeFrom String // "09:00"
  timeTo   String // "17:00"

  @@unique([openingId, timeFrom, timeTo])
}
```

### Location Status Values
- **Active**: Location is operational and accepting appointments
- **Inactive**: Location is temporarily closed but not deleted
- **Under Maintenance**: Location is undergoing maintenance or renovation

## Provider Location CRUD Operations

### 1. Create Location

#### Wasp Operation
```typescript
// File: app/src/provider/operations.ts
export const addSProvidingPlace: AddSProvidingPlace<AddSProvidingPlaceData, SProvidingPlace>
```

#### Validation Schema
```typescript
const addSProvidingPlaceInputSchema = z.object({
  name: z.string().min(1, "Location name is required"),
  shortName: z.string().optional(),
  mobile: z.string().optional(),
  fax: z.string().optional(),
  floor: z.string().optional(),
  parking: z.boolean().default(false),
  elevator: z.boolean().default(false),
  handicapAccess: z.boolean().default(false),
  timezone: z.string().optional(),
  // Address details
  address: z.string().optional(),
  city: z.string().optional(),
  postalCode: z.string().optional(),
  country: z.string().optional(),
  latitude: z.number().optional(),
  longitude: z.number().optional(),
  // Opening hours
  openingHours: z.array(z.object({
    dayOfWeek: z.string().min(1, "Day of week is required"),
    isActive: z.boolean().default(true),
    hours: z.array(z.object({
      timeFrom: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format"),
      timeTo: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format"),
    })),
  })).optional(),
});
```

#### Business Logic
- **Provider Ownership**: Location automatically linked to authenticated provider
- **Address Validation**: Validates address structure and geocoding coordinates
- **Default Opening Hours**: Creates default 7-day schedule if not provided
- **Timezone Handling**: Validates timezone format and sets default if not provided
- **Accessibility Features**: Tracks parking, elevator, and handicap access

#### Transaction Flow
1. Validate provider authentication and ownership
2. Create SProvidingPlace record with basic information
3. Create linked Address record with geocoding data
4. Generate default opening hours for all 7 days
5. Create opening hours records for each day
6. Return complete location with all relations

### 2. Read/Retrieve Locations

#### Wasp Query
```typescript
// File: app/src/provider/operations.ts
export const getSProvidingPlaces: GetSProvidingPlaces<void, SProvidingPlace[]>
```

#### Response Structure
```typescript
interface LocationResponse {
  id: number;
  name: string;
  shortName?: string;
  address?: string;
  city?: string;
  mobile?: string;
  isMobileHidden: boolean;
  fax?: string;
  floor?: string;
  parking: boolean;
  elevator: boolean;
  handicapAccess: boolean;
  timezone?: string;
  queues: QueueResponse[];
  // Geographic and postal information
  country?: string;
  postalCode?: string;
  latitude?: number;
  longitude?: number;
  // Opening hours
  openingHours?: OpeningHoursDay[];
}
```

#### Filtering Options
- **Search**: Filter by location name or address
- **City**: Filter by city name
- **Status**: Filter by active/inactive status
- **Provider**: Automatic filtering by authenticated provider
- **Pagination**: Support for page-based pagination

#### Included Relations
- **Address Details**: Complete address with geocoding
- **Opening Hours**: All operating hours for each day
- **Queues**: Associated queues and their status
- **Appointment Counts**: Statistics on appointments per location

### 3. Update Location

#### Wasp Operation
```typescript
// File: app/src/provider/operations.ts
export const updateSProvidingPlace: UpdateSProvidingPlace<UpdateSProvidingPlaceData, SProvidingPlace>
```

#### Validation Schema
```typescript
const updateSProvidingPlaceInputSchema = z.object({
  placeId: z.number().int().positive(),
  name: z.string().min(1, "Location name is required").optional(),
  shortName: z.string().optional(),
  mobile: z.string().optional(),
  fax: z.string().optional(),
  floor: z.string().optional(),
  parking: z.boolean().optional(),
  elevator: z.boolean().optional(),
  handicapAccess: z.boolean().optional(),
  timezone: z.string().optional(),
  // Address updates
  address: z.string().optional(),
  city: z.string().optional(),
  postalCode: z.string().optional(),
  country: z.string().optional(),
  latitude: z.number().optional(),
  longitude: z.number().optional(),
  // Opening hours updates
  openingHours: z.array(z.object({
    dayOfWeek: z.string().min(1, "Day of week is required"),
    isActive: z.boolean().default(true),
    hours: z.array(z.object({
      timeFrom: z.string().regex(/^\d{2}:\d{2}$/, "Time must be in HH:mm format"),
      timeTo: z.string().regex(/^\d{2}:\d{2}$/, "Time must be in HH:mm format"),
    })),
  })).optional(),
});
```

#### Editable Fields
- **Basic Information**: Name, short name, contact details
- **Accessibility**: Parking, elevator, handicap access
- **Address**: Complete address with geocoding updates
- **Operating Hours**: Full schedule management
- **Timezone**: Location-specific timezone settings

#### Business Rules
- **Ownership Verification**: Only location owner can update
- **Address Validation**: Geocoding validation for address changes
- **Operating Hours**: Validates time format and logical constraints
- **Active Appointments**: Prevents certain changes if active appointments exist

### 4. Delete/Deactivate Location

#### Wasp Operation
```typescript
// File: app/src/provider/operations.ts
export const deleteSProvidingPlace: DeleteSProvidingPlace<DeleteSProvidingPlaceData, any>
```

#### Deletion Policies
- **Active Queues Check**: Cannot delete if location has active queues
- **Future Appointments**: Cannot delete if future appointments exist
- **Cascade Deletion**: Removes all opening hours and related data
- **Translation Cleanup**: Removes all translation records

#### Deactivation vs Deletion
```typescript
// Soft deactivation (recommended)
const deactivateLocation = async (locationId: number) => {
  // Mark all queues as inactive
  await updateQueues({ isActive: false });
  // Cancel future appointments
  await cancelFutureAppointments();
  // Mark location as inactive (custom field)
  await updateLocation({ status: 'inactive' });
};

// Hard deletion (only when no dependencies)
const deleteLocation = async (locationId: number) => {
  // Verify no active queues
  // Verify no future appointments
  // Delete opening hours
  // Delete location record
};
```

#### Pre-Deletion Validation
```typescript
const validateLocationDeletion = async (locationId: number) => {
  const location = await getLocationWithDependencies(locationId);
  
  const activeQueues = location.queues.filter(q => q.isActive);
  const futureAppointments = location.appointments.filter(
    apt => apt.expectedAppointmentStartTime > new Date() && 
           !['canceled', 'completed', 'noshow'].includes(apt.status)
  );
  
  if (activeQueues.length > 0) {
    throw new HttpError(409, `Cannot delete: ${activeQueues.length} active queues exist`);
  }
  
  if (futureAppointments.length > 0) {
    throw new HttpError(409, `Cannot delete: ${futureAppointments.length} future appointments exist`);
  }
};
```

## API Endpoints

### REST API Endpoints

#### Provider Location Management
```http
GET    /api/auth/providers/locations              # Get all provider locations
GET    /api/auth/providers/locations/:id          # Get specific location
POST   /api/auth/providers/locations              # Create new location
PUT    /api/auth/providers/locations/:id          # Update location
DELETE /api/auth/providers/locations/:id          # Delete location
```

#### Location-Specific Operations
```http
GET    /api/auth/providers/locations/:id/queues   # Get location queues
GET    /api/auth/providers/locations/:id/services # Get location services
PUT    /api/auth/providers/locations/:id/hours    # Update operating hours
```

### Wasp Operations

#### Queries
```typescript
// Get all provider locations
query getSProvidingPlaces {
  fn: import { getSProvidingPlaces } from "@src/provider/operations",
  entities: [SProvider, SProvidingPlace, Address, Opening, OpeningHours, Queue],
  auth: true
}

// Get provider openings (operating hours)
query getSProviderOpenings {
  fn: import { getSProviderOpenings } from "@src/provider/operations",
  entities: [SProvider, SProvidingPlace, Opening, OpeningHours],
  auth: true
}
```

#### Actions
```typescript
// Create location
action addSProvidingPlace {
  fn: import { addSProvidingPlace } from "@src/provider/operations",
  entities: [SProvider, SProvidingPlace, Address, Opening, OpeningHours],
  auth: true
}

// Update location
action updateSProvidingPlace {
  fn: import { updateSProvidingPlace } from "@src/provider/operations",
  entities: [SProvider, SProvidingPlace, Address, Opening, OpeningHours],
  auth: true
}

// Delete location
action deleteSProvidingPlace {
  fn: import { deleteSProvidingPlace } from "@src/provider/operations",
  entities: [SProvider, SProvidingPlace, Address, Opening, OpeningHours, Queue, Appointment],
  auth: true
}

// Update operating hours
action updateSProviderOpenings {
  fn: import { updateSProviderOpenings } from "@src/provider/operations",
  entities: [SProvider, SProvidingPlace, Opening, OpeningHours],
  auth: true
}
```

## Authentication & Authorization

### Authentication Requirements
- All provider location endpoints require `auth: true`
- JWT token validation via Wasp's built-in auth system
- User must have `role: 'CLIENT'` (provider role)

### Authorization Patterns
```typescript
// Provider location ownership verification
export async function verifyLocationOwnership(
  providerId: number,
  locationId: number,
  entities: any
): Promise<any> {
  const location = await entities.SProvidingPlace.findFirst({
    where: {
      id: locationId,
      sProviderId: providerId
    },
    select: { id: true, sProviderId: true, name: true }
  });

  if (!location) {
    throw new HttpError(404, 'Location not found or you do not have permission to access it');
  }

  return location;
}
```

### Provider Context Validation
```typescript
// Middleware for location access
export function requireLocationAccess(req: Request, res: Response, next: Function, context: any) {
  if (!context.user) {
    return sendUnauthorized(res, 'Authentication required');
  }
  
  if (context.user.role !== 'CLIENT') {
    return sendForbidden(res, 'Provider access required');
  }
  
  // Verify provider profile exists
  if (!context.provider) {
    return sendForbidden(res, 'Provider profile required');
  }
  
  next();
}
```

### Location-Specific Authorization
- **Location Ownership**: Verified through `sProviderId` relationship
- **Queue Access**: Queues can only be accessed through owned locations
- **Appointment Access**: Appointments filtered by location ownership
- **File Upload**: Location photos/documents linked to provider ownership

## Business Rules & Workflows

### Location Creation Workflow

#### 1. Pre-Creation Validation
- Provider must have active account and profile
- Provider must not exceed location limits (if applicable)
- Address information must be valid and geocodable
- Operating hours must follow logical time constraints

#### 2. Location Creation Process
```typescript
// Location creation transaction flow
const createLocationFlow = async (locationData, context) => {
  return await prisma.$transaction(async (tx) => {
    // 1. Validate provider exists and is active
    const provider = await tx.sProvider.findUnique({
      where: { userId: context.user.id },
      select: { id: true }
    });

    if (!provider) {
      throw new HttpError(404, 'Provider profile not found');
    }

    // 2. Create the location
    const newLocation = await tx.sProvidingPlace.create({
      data: {
        provider: { connect: { id: provider.id } },
        name: locationData.name,
        shortName: locationData.shortName,
        mobile: locationData.mobile,
        fax: locationData.fax,
        floor: locationData.floor,
        parking: locationData.parking,
        elevator: locationData.elevator,
        handicapAccess: locationData.handicapAccess,
        timezone: locationData.timezone,
        detailedAddress: {
          create: {
            address: locationData.address,
            city: locationData.city,
            country: locationData.country || "Algeria",
            postalCode: locationData.postalCode,
            latitude: locationData.latitude,
            longitude: locationData.longitude,
          }
        }
      },
      include: { detailedAddress: true }
    });

    // 3. Create default opening hours for all 7 days
    const daysOfWeek = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

    for (const day of daysOfWeek) {
      const opening = await tx.opening.create({
        data: {
          sProvidingPlaceId: newLocation.id,
          dayOfWeek: day,
          type: 'regular',
          isActive: true
        }
      });

      // Create default hours (9:00 AM - 5:00 PM) for weekdays
      if (!['Saturday', 'Sunday'].includes(day)) {
        await tx.openingHours.create({
          data: {
            openingId: opening.id,
            timeFrom: '09:00',
            timeTo: '17:00'
          }
        });
      }
    }

    // 4. Apply custom opening hours if provided
    if (locationData.openingHours) {
      for (const daySchedule of locationData.openingHours) {
        const opening = await tx.opening.findFirst({
          where: {
            sProvidingPlaceId: newLocation.id,
            dayOfWeek: daySchedule.dayOfWeek
          }
        });

        if (opening) {
          // Clear existing hours
          await tx.openingHours.deleteMany({
            where: { openingId: opening.id }
          });

          // Add new hours
          for (const hours of daySchedule.hours) {
            await tx.openingHours.create({
              data: {
                openingId: opening.id,
                timeFrom: hours.timeFrom,
                timeTo: hours.timeTo
              }
            });
          }

          // Update active status
          await tx.opening.update({
            where: { id: opening.id },
            data: { isActive: daySchedule.isActive }
          });
        }
      }
    }

    return newLocation;
  });
};
```

#### 3. Post-Creation Actions
- Generate default queue for the location
- Set up basic service offerings
- Initialize location-specific settings
- Send confirmation notifications

### Location Status Management

#### Status Transitions
```
active → inactive → active
active → under_maintenance → active
inactive → under_maintenance → active
```

#### Status Change Business Rules

##### Active → Inactive
- All queues become inactive
- Future appointments are cancelled or rescheduled
- Location stops accepting new bookings
- Existing appointments can still be completed

##### Active → Under Maintenance
- Temporary closure for maintenance
- All appointments are rescheduled
- Estimated reopening date is set
- Customers are notified of closure

##### Inactive/Maintenance → Active
- Location reopens for business
- Queues can be reactivated
- New appointments can be scheduled
- Operating hours are validated

### Location Deactivation Impact

#### Impact on Appointments
```typescript
const handleLocationDeactivation = async (locationId: number) => {
  // 1. Get all future appointments
  const futureAppointments = await prisma.appointment.findMany({
    where: {
      placeId: locationId,
      expectedAppointmentStartTime: { gte: new Date() },
      status: { notIn: ['canceled', 'completed', 'noshow'] }
    }
  });

  // 2. Cancel or reschedule appointments
  for (const appointment of futureAppointments) {
    await prisma.appointment.update({
      where: { id: appointment.id },
      data: {
        status: 'canceled',
        canceledAt: new Date()
      }
    });

    // Create appointment history
    await prisma.appointmentHistory.create({
      data: {
        appointmentId: appointment.id,
        changedByUserId: context.user.id,
        changeReason: 'Location deactivated',
        previousStatus: appointment.status,
        newStatus: 'canceled',
        // ... other history fields
      }
    });
  }

  // 3. Deactivate all queues
  await prisma.queue.updateMany({
    where: { sProvidingPlaceId: locationId },
    data: { isActive: false }
  });

  // 4. Send notifications to affected customers
  await notifyCustomersOfLocationClosure(futureAppointments);
};
```

#### Impact on Queues
- All queues become inactive
- Queue-specific appointments are cancelled
- Queue opening hours are preserved
- Queues can be reactivated when location reopens

### Multi-Location Provider Rules

#### Location Limits
- **Free Tier**: 1 location maximum
- **Pro Tier**: 5 locations maximum
- **Enterprise**: Unlimited locations

#### Default Location Management
```typescript
interface LocationHierarchy {
  primaryLocation: SProvidingPlace; // Main business location
  secondaryLocations: SProvidingPlace[]; // Additional branches
  mobileServices: SProvidingPlace[]; // Mobile/at-home services
}
```

#### Cross-Location Operations
- **Service Sharing**: Services can be offered at multiple locations
- **Queue Independence**: Each location has independent queues
- **Staff Assignment**: Staff can work at multiple locations
- **Appointment Routing**: Customers choose specific location for appointments

## Operating Hours Management

### Opening Hours Structure

#### Day-Based Schedule
```typescript
interface DaySchedule {
  dayOfWeek: string; // "Monday", "Tuesday", etc.
  isActive: boolean; // Is the location open on this day?
  hours: TimeInterval[]; // Multiple time intervals per day
}

interface TimeInterval {
  timeFrom: string; // "09:00"
  timeTo: string;   // "17:00"
}
```

#### Schedule Types
- **Regular Hours**: Standard weekly schedule
- **Holiday Hours**: Special hours for holidays
- **Seasonal Hours**: Different hours for seasons
- **Emergency Hours**: Extended hours for emergencies

### Operating Hours Validation

#### Time Format Validation
```typescript
const timeFormatSchema = z.string().regex(
  /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/,
  "Time must be in HH:mm format (24-hour)"
);

const timeIntervalSchema = z.object({
  timeFrom: timeFormatSchema,
  timeTo: timeFormatSchema
}).refine(data => data.timeFrom < data.timeTo, {
  message: "Start time must be before end time",
  path: ["timeTo"]
});
```

#### Business Logic Validation
```typescript
const validateOperatingHours = (schedule: DaySchedule[]) => {
  for (const day of schedule) {
    // Check for overlapping intervals
    for (let i = 0; i < day.hours.length; i++) {
      for (let j = i + 1; j < day.hours.length; j++) {
        const interval1 = day.hours[i];
        const interval2 = day.hours[j];

        if (intervalsOverlap(interval1, interval2)) {
          throw new HttpError(400, `Overlapping hours on ${day.dayOfWeek}`);
        }
      }
    }

    // Check for minimum break between intervals
    const sortedHours = day.hours.sort((a, b) => a.timeFrom.localeCompare(b.timeFrom));
    for (let i = 0; i < sortedHours.length - 1; i++) {
      const gap = calculateTimeGap(sortedHours[i].timeTo, sortedHours[i + 1].timeFrom);
      if (gap < 30) { // Minimum 30-minute break
        throw new HttpError(400, `Insufficient break between intervals on ${day.dayOfWeek}`);
      }
    }
  }
};
```

### Schedule Management Operations

#### Update Operating Hours
```typescript
export const updateLocationSchedule = async (locationId: number, schedule: DaySchedule[], context: any) => {
  return await prisma.$transaction(async (tx) => {
    // 1. Verify location ownership
    await verifyLocationOwnership(context.provider.id, locationId, tx);

    // 2. Validate new schedule
    validateOperatingHours(schedule);

    // 3. Update each day's schedule
    for (const daySchedule of schedule) {
      // Find or create opening record
      let opening = await tx.opening.findFirst({
        where: {
          sProvidingPlaceId: locationId,
          dayOfWeek: daySchedule.dayOfWeek,
          type: 'regular'
        }
      });

      if (!opening) {
        opening = await tx.opening.create({
          data: {
            sProvidingPlaceId: locationId,
            dayOfWeek: daySchedule.dayOfWeek,
            type: 'regular',
            isActive: daySchedule.isActive
          }
        });
      } else {
        await tx.opening.update({
          where: { id: opening.id },
          data: { isActive: daySchedule.isActive }
        });
      }

      // 4. Clear existing hours
      await tx.openingHours.deleteMany({
        where: { openingId: opening.id }
      });

      // 5. Create new hours
      for (const hours of daySchedule.hours) {
        await tx.openingHours.create({
          data: {
            openingId: opening.id,
            timeFrom: hours.timeFrom,
            timeTo: hours.timeTo
          }
        });
      }
    }

    // 6. Validate impact on existing appointments
    await validateScheduleChangeImpact(locationId, schedule, tx);
  });
};
```

#### Schedule Change Impact Validation
```typescript
const validateScheduleChangeImpact = async (locationId: number, newSchedule: DaySchedule[], tx: any) => {
  // Get future appointments
  const futureAppointments = await tx.appointment.findMany({
    where: {
      placeId: locationId,
      expectedAppointmentStartTime: { gte: new Date() },
      status: { notIn: ['canceled', 'completed', 'noshow'] }
    }
  });

  // Check if any appointments fall outside new operating hours
  const conflictingAppointments = [];

  for (const appointment of futureAppointments) {
    const appointmentDay = appointment.expectedAppointmentStartTime.toLocaleDateString('en-US', { weekday: 'long' });
    const appointmentTime = appointment.expectedAppointmentStartTime.toTimeString().slice(0, 5);

    const daySchedule = newSchedule.find(d => d.dayOfWeek === appointmentDay);

    if (!daySchedule || !daySchedule.isActive) {
      conflictingAppointments.push(appointment);
      continue;
    }

    const isWithinHours = daySchedule.hours.some(interval =>
      appointmentTime >= interval.timeFrom && appointmentTime <= interval.timeTo
    );

    if (!isWithinHours) {
      conflictingAppointments.push(appointment);
    }
  }

  if (conflictingAppointments.length > 0) {
    throw new HttpError(409,
      `Schedule change conflicts with ${conflictingAppointments.length} existing appointments. ` +
      `Please reschedule these appointments first.`
    );
  }
};
```

## Address Validation & Geocoding

### Address Structure

#### Structured Address Format
```typescript
interface AddressData {
  address: string;      // Street address
  city: string;         // City name
  state?: string;       // State/Province (optional)
  postalCode: string;   // ZIP/Postal code
  country: string;      // Country name
  latitude: number;     // GPS latitude
  longitude: number;    // GPS longitude
  description?: string; // Address description
}
```

#### Address Validation Schema
```typescript
const addressValidationSchema = z.object({
  address: z.string().min(1, "Street address is required").max(500, "Address too long"),
  city: z.string().min(1, "City is required").max(100, "City name too long"),
  state: z.string().max(100, "State name too long").optional(),
  postalCode: z.string().min(1, "Postal code is required").max(20, "Postal code too long"),
  country: z.string().min(1, "Country is required").max(100, "Country name too long"),
  latitude: z.number().min(-90, "Invalid latitude").max(90, "Invalid latitude"),
  longitude: z.number().min(-180, "Invalid longitude").max(180, "Invalid longitude"),
  description: z.string().max(255, "Description too long").optional()
});
```

### Geocoding Integration

#### Client-Side Geocoding
```typescript
// Browser geolocation API integration
const getCurrentLocation = (): Promise<{ latitude: number; longitude: number }> => {
  return new Promise((resolve, reject) => {
    if (!navigator.geolocation) {
      reject(new Error('Geolocation is not supported by this browser'));
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        resolve({
          latitude: position.coords.latitude,
          longitude: position.coords.longitude
        });
      },
      (error) => {
        reject(new Error(`Geolocation error: ${error.message}`));
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000 // 5 minutes
      }
    );
  });
};
```

#### Address Geocoding Validation
```typescript
const validateAddressGeocoding = async (addressData: AddressData) => {
  // Validate coordinates are within reasonable bounds for the country
  const countryBounds = getCountryBounds(addressData.country);

  if (!isWithinBounds(addressData.latitude, addressData.longitude, countryBounds)) {
    throw new HttpError(400, 'Coordinates do not match the specified country');
  }

  // Optional: Reverse geocoding validation
  // const reverseGeocodedAddress = await reverseGeocode(addressData.latitude, addressData.longitude);
  // if (!addressesMatch(addressData, reverseGeocodedAddress)) {
  //   throw new HttpError(400, 'Address and coordinates do not match');
  // }
};
```

### Geographic Search and Distance

#### Location-Based Search
```typescript
const findNearbyLocations = async (latitude: number, longitude: number, radiusKm: number = 10) => {
  // Using Haversine formula for distance calculation
  const locations = await prisma.sProvidingPlace.findMany({
    where: {
      detailedAddress: {
        isNot: null
      }
    },
    include: {
      detailedAddress: true,
      provider: {
        select: { title: true, phone: true }
      }
    }
  });

  return locations.filter(location => {
    if (!location.detailedAddress) return false;

    const distance = calculateDistance(
      latitude, longitude,
      location.detailedAddress.latitude,
      location.detailedAddress.longitude
    );

    return distance <= radiusKm;
  }).sort((a, b) => {
    const distanceA = calculateDistance(latitude, longitude, a.detailedAddress!.latitude, a.detailedAddress!.longitude);
    const distanceB = calculateDistance(latitude, longitude, b.detailedAddress!.latitude, b.detailedAddress!.longitude);
    return distanceA - distanceB;
  });
};

// Haversine formula for distance calculation
const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
  const R = 6371; // Earth's radius in kilometers
  const dLat = toRadians(lat2 - lat1);
  const dLon = toRadians(lon2 - lon1);

  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2);

  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
};

const toRadians = (degrees: number): number => degrees * (Math.PI / 180);
```

## Multi-Location Provider Support

### Location Hierarchy Management

#### Primary vs Secondary Locations
```typescript
interface LocationHierarchy {
  primary: {
    location: SProvidingPlace;
    isPrimary: true;
    canDelete: false; // Primary location cannot be deleted
  };
  secondary: {
    location: SProvidingPlace;
    isPrimary: false;
    canDelete: true;
  }[];
}
```

#### Location Switching Logic
```typescript
const switchPrimaryLocation = async (providerId: number, newPrimaryLocationId: number) => {
  return await prisma.$transaction(async (tx) => {
    // 1. Verify ownership of new primary location
    const newPrimaryLocation = await tx.sProvidingPlace.findFirst({
      where: {
        id: newPrimaryLocationId,
        sProviderId: providerId
      }
    });

    if (!newPrimaryLocation) {
      throw new HttpError(404, 'Location not found or not owned by provider');
    }

    // 2. Update provider's primary location reference
    await tx.sProvider.update({
      where: { id: providerId },
      data: { primaryLocationId: newPrimaryLocationId }
    });

    // 3. Update location metadata
    await tx.sProvidingPlace.updateMany({
      where: { sProviderId: providerId },
      data: { isPrimary: false }
    });

    await tx.sProvidingPlace.update({
      where: { id: newPrimaryLocationId },
      data: { isPrimary: true }
    });

    return newPrimaryLocation;
  });
};
```

### Cross-Location Service Management

#### Service Availability by Location
```typescript
interface LocationServiceConfig {
  locationId: number;
  serviceId: number;
  isAvailable: boolean;
  customPrice?: number; // Location-specific pricing
  customDuration?: number; // Location-specific duration
  specialInstructions?: string;
}
```

#### Location-Specific Queue Configuration
```typescript
const configureLocationQueues = async (locationId: number, queueConfigs: QueueConfig[]) => {
  for (const config of queueConfigs) {
    await prisma.queue.create({
      data: {
        title: config.title,
        sProvidingPlaceId: locationId,
        isActive: config.isActive,
        services: {
          connect: config.serviceIds.map(id => ({ id }))
        }
      }
    });

    // Create location-specific opening hours for the queue
    if (config.customHours) {
      await createQueueOpeningHours(queue.id, config.customHours);
    }
  }
};
```

### Location Performance Analytics

#### Location-Specific Metrics
```typescript
interface LocationMetrics {
  locationId: number;
  totalAppointments: number;
  completedAppointments: number;
  cancelledAppointments: number;
  averageWaitTime: number;
  customerSatisfactionScore: number;
  revenueGenerated: number;
  utilizationRate: number; // Percentage of available time slots used
}
```

#### Cross-Location Comparison
```typescript
const getLocationPerformanceComparison = async (providerId: number, dateRange: DateRange) => {
  const locations = await prisma.sProvidingPlace.findMany({
    where: { sProviderId: providerId },
    include: {
      appointments: {
        where: {
          createdAt: {
            gte: dateRange.startDate,
            lte: dateRange.endDate
          }
        }
      },
      queues: {
        include: {
          appointments: {
            where: {
              createdAt: {
                gte: dateRange.startDate,
                lte: dateRange.endDate
              }
            }
          }
        }
      }
    }
  });

  return locations.map(location => ({
    locationId: location.id,
    locationName: location.name,
    metrics: calculateLocationMetrics(location)
  }));
};

## File Upload Support

### Location-Related File Types

#### Supported File Categories
- **Location Photos**: Exterior and interior photos of the facility
- **Facility Documents**: Licenses, certifications, insurance documents
- **Floor Plans**: Layout diagrams and accessibility maps
- **Equipment Photos**: Medical equipment and facility amenities
- **Compliance Documents**: Health department certificates, safety inspections

#### File Upload API Integration
```typescript
// Location photo upload endpoint
POST /api/auth/providers/locations/:id/photos
Content-Type: application/json

{
  "fileType": "image/jpeg",
  "fileName": "clinic-exterior.jpg",
  "category": "exterior_photo",
  "description": "Main entrance view"
}
```

### Location Photo Management

#### Photo Categories
```typescript
enum LocationPhotoCategory {
  EXTERIOR = 'exterior_photo',
  INTERIOR = 'interior_photo',
  WAITING_AREA = 'waiting_area',
  TREATMENT_ROOM = 'treatment_room',
  EQUIPMENT = 'equipment_photo',
  ACCESSIBILITY = 'accessibility_feature',
  PARKING = 'parking_area'
}
```

#### Photo Upload Workflow
```typescript
const uploadLocationPhoto = async (locationId: number, photoData: LocationPhotoData, context: any) => {
  // 1. Verify location ownership
  await verifyLocationOwnership(context.provider.id, locationId, context.entities);

  // 2. Validate file type and size
  const allowedTypes = ['image/jpeg', 'image/png'];
  if (!allowedTypes.includes(photoData.fileType)) {
    throw new HttpError(400, 'Invalid file type. Only JPEG and PNG images are allowed.');
  }

  // 3. Generate S3 upload URL
  const uploadResult = await createFile({
    fileType: photoData.fileType,
    fileName: photoData.fileName
  }, context);

  // 4. Create location photo record
  const photoRecord = await context.entities.LocationPhoto.create({
    data: {
      locationId: locationId,
      fileId: uploadResult.fileId,
      category: photoData.category,
      description: photoData.description,
      isPublic: photoData.isPublic || false,
      sortOrder: photoData.sortOrder || 0
    }
  });

  return {
    uploadUrl: uploadResult.s3UploadUrl,
    uploadFields: uploadResult.s3UploadFields,
    photoId: photoRecord.id
  };
};
```

### Document Management

#### Location Document Types
```typescript
interface LocationDocument {
  id: string;
  locationId: number;
  fileId: string;
  documentType: 'license' | 'certificate' | 'insurance' | 'inspection' | 'floor_plan' | 'other';
  title: string;
  description?: string;
  expirationDate?: Date;
  isRequired: boolean;
  isPublic: boolean;
  uploadedAt: Date;
}
```

#### Document Validation and Compliance
```typescript
const validateLocationDocuments = async (locationId: number) => {
  const requiredDocuments = [
    'business_license',
    'health_department_certificate',
    'liability_insurance',
    'professional_license'
  ];

  const existingDocuments = await prisma.locationDocument.findMany({
    where: {
      locationId: locationId,
      expirationDate: { gte: new Date() } // Not expired
    }
  });

  const missingDocuments = requiredDocuments.filter(
    required => !existingDocuments.some(doc => doc.documentType === required)
  );

  return {
    isCompliant: missingDocuments.length === 0,
    missingDocuments,
    expiringDocuments: existingDocuments.filter(
      doc => doc.expirationDate &&
             doc.expirationDate <= new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
    )
  };
};
```

### File Access Control

#### Location File Permissions
```typescript
const getLocationFilePermissions = (file: LocationFile, user: User, location: SProvidingPlace) => {
  // Provider always has full access to their location files
  if (location.sProviderId === user.providerId) {
    return {
      canView: true,
      canEdit: true,
      canDelete: true,
      canDownload: true
    };
  }

  // Public files can be viewed by anyone
  if (file.isPublic) {
    return {
      canView: true,
      canEdit: false,
      canDelete: false,
      canDownload: false
    };
  }

  // Private files require specific permissions
  return {
    canView: false,
    canEdit: false,
    canDelete: false,
    canDownload: false
  };
};
```

## Error Handling

### HTTP Status Codes

#### Success Responses
- **200 OK**: Successful GET, PUT operations
- **201 Created**: Successful POST operations (location creation)
- **204 No Content**: Successful DELETE operations

#### Client Error Responses
- **400 Bad Request**: Invalid request data, validation errors
- **401 Unauthorized**: Authentication required
- **403 Forbidden**: Insufficient permissions, not location owner
- **404 Not Found**: Location not found
- **409 Conflict**: Business rule violations (active queues, future appointments)
- **422 Unprocessable Entity**: Address validation failures, geocoding errors

#### Server Error Responses
- **500 Internal Server Error**: Unexpected server errors
- **503 Service Unavailable**: Geocoding service unavailable

### Error Response Format
```typescript
interface LocationErrorResponse {
  success: false;
  message: string;
  code?: string;
  statusCode: number;
  details?: {
    field?: string;
    value?: any;
    constraint?: string;
  };
  timestamp: Date;
  path: string;
}
```

### Common Error Scenarios

#### Validation Errors
```typescript
// Address validation error
{
  "success": false,
  "message": "Address validation failed",
  "statusCode": 400,
  "code": "ADDRESS_VALIDATION_ERROR",
  "details": {
    "field": "latitude",
    "message": "Coordinates do not match the specified address",
    "value": { "latitude": 36.7538, "longitude": 3.0588 }
  }
}
```

#### Business Rule Violations
```typescript
// Cannot delete location with active queues
{
  "success": false,
  "message": "Cannot delete location: It has 3 active queue(s). Please deactivate or delete the queues first.",
  "statusCode": 409,
  "code": "LOCATION_HAS_ACTIVE_QUEUES",
  "details": {
    "activeQueues": 3,
    "queueIds": [1, 2, 3]
  }
}
```

#### Geocoding Errors
```typescript
// Geocoding service error
{
  "success": false,
  "message": "Unable to validate address coordinates",
  "statusCode": 422,
  "code": "GEOCODING_FAILED",
  "details": {
    "address": "123 Invalid Street, Unknown City",
    "reason": "Address not found"
  }
}
```

### Error Handling Patterns

#### Try-Catch with HttpError
```typescript
export const createProviderLocation = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Validate authentication
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Validate request data
    const locationData = validateAndExtract(createLocationSchema, req.body);

    // Validate address and geocoding
    if (locationData.latitude && locationData.longitude) {
      await validateAddressGeocoding({
        address: locationData.address,
        city: locationData.city,
        country: locationData.country,
        latitude: locationData.latitude,
        longitude: locationData.longitude
      });
    }

    // Call operation
    const newLocation = await addSProvidingPlace(locationData, context);

    return sendCreated(res, newLocation, 'Location created successfully');

  } catch (error: any) {
    console.error('[createProviderLocation] Error:', error);

    if (error instanceof HttpError) {
      return sendError(res, {
        statusCode: error.statusCode,
        message: error.message,
        code: error.code
      });
    }

    return sendError(res, error, 'Failed to create location');
  }
});
```

#### Validation Error Handling
```typescript
const validateLocationData = (data: any): LocationData => {
  const result = createLocationSchema.safeParse(data);

  if (!result.success) {
    const errors = result.error.errors.map(err => ({
      field: err.path.join('.'),
      message: err.message,
      value: err.input
    }));

    throw new HttpError(400, 'Location validation failed', 'VALIDATION_ERROR', { errors });
  }

  return result.data;
};
```

---

## API Reference Summary

### Complete Endpoint List

#### Provider Location Management
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/auth/providers/locations` | Get all provider locations | Yes |
| GET | `/api/auth/providers/locations/:id` | Get specific location | Yes |
| POST | `/api/auth/providers/locations` | Create new location | Yes |
| PUT | `/api/auth/providers/locations/:id` | Update location | Yes |
| DELETE | `/api/auth/providers/locations/:id` | Delete location | Yes |

#### Location Operating Hours
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/auth/providers/locations/:id/hours` | Get location operating hours | Yes |
| PUT | `/api/auth/providers/locations/:id/hours` | Update operating hours | Yes |

#### Location-Specific Resources
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/auth/providers/locations/:id/queues` | Get location queues | Yes |
| GET | `/api/auth/providers/locations/:id/services` | Get location services | Yes |
| POST | `/api/auth/providers/locations/:id/photos` | Upload location photo | Yes |
| GET | `/api/auth/providers/locations/:id/photos` | Get location photos | Yes |

#### File Management
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/api/auth/files/upload` | Upload location document | Yes |
| GET | `/api/auth/providers/locations/:id/documents` | Get location documents | Yes |

### Request/Response Examples

#### Create Location Request
```json
POST /api/auth/providers/locations
{
  "name": "Downtown Medical Clinic",
  "shortName": "Downtown",
  "address": "123 Main Street",
  "city": "Algiers",
  "postalCode": "16000",
  "country": "Algeria",
  "latitude": 36.7538,
  "longitude": 3.0588,
  "mobile": "+213555123456",
  "parking": true,
  "elevator": true,
  "handicapAccess": true,
  "timezone": "Africa/Algiers",
  "openingHours": [
    {
      "dayOfWeek": "Monday",
      "isActive": true,
      "hours": [
        {
          "timeFrom": "09:00",
          "timeTo": "17:00"
        }
      ]
    },
    {
      "dayOfWeek": "Tuesday",
      "isActive": true,
      "hours": [
        {
          "timeFrom": "09:00",
          "timeTo": "17:00"
        }
      ]
    }
  ]
}
```

#### Create Location Response
```json
{
  "success": true,
  "data": {
    "id": 15,
    "name": "Downtown Medical Clinic",
    "shortName": "Downtown",
    "address": "123 Main Street",
    "city": "Algiers",
    "mobile": "+213555123456",
    "isMobileHidden": false,
    "parking": true,
    "elevator": true,
    "handicapAccess": true,
    "timezone": "Africa/Algiers",
    "country": "Algeria",
    "postalCode": "16000",
    "latitude": 36.7538,
    "longitude": 3.0588,
    "queues": [],
    "openingHours": [
      {
        "dayOfWeek": "Monday",
        "isActive": true,
        "hours": [
          {
            "timeFrom": "09:00",
            "timeTo": "17:00"
          }
        ]
      },
      {
        "dayOfWeek": "Tuesday",
        "isActive": true,
        "hours": [
          {
            "timeFrom": "09:00",
            "timeTo": "17:00"
          }
        ]
      }
    ]
  },
  "message": "Location created successfully"
}
```

#### Update Location Request
```json
PUT /api/auth/providers/locations/15
{
  "name": "Downtown Medical Center",
  "parking": false,
  "openingHours": [
    {
      "dayOfWeek": "Monday",
      "isActive": true,
      "hours": [
        {
          "timeFrom": "08:00",
          "timeTo": "18:00"
        }
      ]
    }
  ]
}
```

#### Get Provider Locations Response
```json
{
  "success": true,
  "data": [
    {
      "id": 15,
      "name": "Downtown Medical Center",
      "shortName": "Downtown",
      "address": "123 Main Street",
      "city": "Algiers",
      "mobile": "+213555123456",
      "isMobileHidden": false,
      "parking": false,
      "elevator": true,
      "handicapAccess": true,
      "timezone": "Africa/Algiers",
      "country": "Algeria",
      "postalCode": "16000",
      "latitude": 36.7538,
      "longitude": 3.0588,
      "queues": [
        {
          "id": 8,
          "title": "General Queue",
          "isActive": true
        }
      ],
      "openingHours": [
        {
          "dayOfWeek": "Monday",
          "isActive": true,
          "hours": [
            {
              "timeFrom": "08:00",
              "timeTo": "18:00"
            }
          ]
        }
      ]
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 1,
    "totalPages": 1,
    "hasNext": false,
    "hasPrev": false
  }
}
```

---

## Implementation Notes

### Performance Considerations
- **Database Indexing**: Index on `sProviderId`, `latitude`, `longitude` for location queries
- **Geocoding Caching**: Cache geocoding results to reduce API calls
- **Address Validation**: Implement client-side validation before server submission
- **Image Optimization**: Compress location photos before upload

### Security Best Practices
- **Input Validation**: Comprehensive validation using Zod schemas
- **File Upload Security**: Validate file types and scan for malware
- **Geocoding Validation**: Verify coordinates match provided address
- **Access Control**: Strict ownership verification for all location operations

### Monitoring & Observability
- **Location Analytics**: Track location performance and utilization
- **Geocoding Metrics**: Monitor geocoding success rates and errors
- **File Upload Monitoring**: Track upload success rates and file sizes
- **Address Quality**: Monitor address validation accuracy

### Testing Strategy
- **Unit Tests**: Test location CRUD operations and validation logic
- **Integration Tests**: Test complete location management workflows
- **Geocoding Tests**: Test address validation and coordinate verification
- **File Upload Tests**: Test location photo and document upload flows

This documentation provides a comprehensive overview of the Provider Locations Management system, covering all aspects from database schema to API implementation, business rules, operating hours management, address validation, and technical considerations. The system supports multi-location providers with robust validation, geocoding integration, and comprehensive file management capabilities.
```
