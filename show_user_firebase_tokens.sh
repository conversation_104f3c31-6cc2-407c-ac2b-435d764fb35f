#!/bin/bash

# Script to show Firebase FCM tokens for a specific user
# Usage: ./show_user_firebase_tokens.sh [email_or_name]

SEARCH_TERM="$1"

if [ -z "$SEARCH_TERM" ]; then
    echo "🔥 Firebase Tokens by User"
    echo "=========================="
    echo "Usage: $0 <email_or_name>"
    echo ""
    echo "Examples:"
    echo "  $0 <EMAIL>"
    echo "  $0 dada"
    echo "  $0 nacer"
    echo ""
    echo "Available users:"
    
    # Find the Docker container
    CONTAINER_NAME=$(docker ps --format "table {{.Names}}" | grep wasp-dev-db)
    if [ -z "$CONTAINER_NAME" ]; then
        echo "❌ No Wasp PostgreSQL container found."
        exit 1
    fi
    
    DB_USER="postgresWaspDevUser"
    DB_NAME=$(echo $CONTAINER_NAME | sed 's/wasp-dev-db-//')
    
    docker exec $CONTAINER_NAME psql -U $DB_USER -d $DB_NAME -c "
    SELECT DISTINCT
        u.\"firstName\" || ' ' || COALESCE(u.\"lastName\", '') as full_name,
        u.\"email\",
        COUNT(udt.\"id\") as token_count
    FROM \"User\" u 
    JOIN \"UserDeviceToken\" udt ON u.\"id\" = udt.\"userId\"
    GROUP BY u.\"id\", u.\"firstName\", u.\"lastName\", u.\"email\"
    ORDER BY token_count DESC;" 2>/dev/null
    
    exit 0
fi

echo "🔥 Firebase Tokens for User: $SEARCH_TERM"
echo "========================================"

# Find the Docker container
CONTAINER_NAME=$(docker ps --format "table {{.Names}}" | grep wasp-dev-db)

if [ -z "$CONTAINER_NAME" ]; then
    echo "❌ No Wasp PostgreSQL container found. Make sure you run 'wasp start db' first."
    exit 1
fi

# Database credentials
DB_USER="postgresWaspDevUser"
DB_NAME=$(echo $CONTAINER_NAME | sed 's/wasp-dev-db-//')

# Function to run SQL queries
run_query() {
    docker exec $CONTAINER_NAME psql -U $DB_USER -d $DB_NAME -c "$1" 2>/dev/null
}

# Search for user and show their tokens
echo "🔍 Searching for user: $SEARCH_TERM"
echo ""

# Show user info and tokens
run_query "
SELECT 
    u.\"id\" as user_id,
    u.\"firstName\" || ' ' || COALESCE(u.\"lastName\", '') as full_name,
    u.\"email\",
    u.\"mobileNumber\",
    u.\"role\",
    u.\"createdAt\"::date as user_created
FROM \"User\" u 
WHERE 
    u.\"email\" ILIKE '%$SEARCH_TERM%' OR 
    u.\"firstName\" ILIKE '%$SEARCH_TERM%' OR 
    u.\"lastName\" ILIKE '%$SEARCH_TERM%'
LIMIT 5;"

echo ""
echo "📱 FCM Tokens for this user:"
echo "============================"

run_query "
SELECT 
    udt.\"id\" as token_id,
    udt.\"deviceType\",
    udt.\"token\" as full_fcm_token,
    udt.\"createdAt\",
    udt.\"updatedAt\"
FROM \"UserDeviceToken\" udt
JOIN \"User\" u ON udt.\"userId\" = u.\"id\"
WHERE 
    u.\"email\" ILIKE '%$SEARCH_TERM%' OR 
    u.\"firstName\" ILIKE '%$SEARCH_TERM%' OR 
    u.\"lastName\" ILIKE '%$SEARCH_TERM%'
ORDER BY udt.\"createdAt\" DESC;"

echo ""
echo "🔔 Recent Notifications for this user:"
echo "======================================"

run_query "
SELECT 
    n.\"title\",
    n.\"message\",
    n.\"type\",
    n.\"isRead\",
    n.\"createdAt\"
FROM \"Notification\" n
JOIN \"User\" u ON n.\"userId\" = u.\"id\"
WHERE 
    u.\"email\" ILIKE '%$SEARCH_TERM%' OR 
    u.\"firstName\" ILIKE '%$SEARCH_TERM%' OR 
    u.\"lastName\" ILIKE '%$SEARCH_TERM%'
ORDER BY n.\"createdAt\" DESC
LIMIT 10;"

echo ""
echo "📊 Token Summary for this user:"
echo "==============================="

run_query "
SELECT 
    udt.\"deviceType\",
    COUNT(*) as count,
    MIN(udt.\"createdAt\")::date as first_token,
    MAX(udt.\"createdAt\")::date as latest_token
FROM \"UserDeviceToken\" udt
JOIN \"User\" u ON udt.\"userId\" = u.\"id\"
WHERE 
    u.\"email\" ILIKE '%$SEARCH_TERM%' OR 
    u.\"firstName\" ILIKE '%$SEARCH_TERM%' OR 
    u.\"lastName\" ILIKE '%$SEARCH_TERM%'
GROUP BY udt.\"deviceType\"
ORDER BY count DESC;"

echo ""
echo "✅ User token analysis complete!"
