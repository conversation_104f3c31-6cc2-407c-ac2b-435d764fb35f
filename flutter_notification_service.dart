// notification_service.dart
import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'websocket_service.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final WebSocketService _webSocketService = WebSocketService();
  final FlutterLocalNotificationsPlugin _localNotifications = 
      FlutterLocalNotificationsPlugin();
  
  String? _baseUrl;
  String? _authToken;
  String? _fcmToken;

  // Notification streams
  final StreamController<List<AppNotification>> _notificationsController = 
      StreamController<List<AppNotification>>.broadcast();
  final StreamController<AppNotification> _newNotificationController = 
      StreamController<AppNotification>.broadcast();

  Stream<List<AppNotification>> get notificationsStream => _notificationsController.stream;
  Stream<AppNotification> get newNotificationStream => _newNotificationController.stream;

  List<AppNotification> _notificationCache = [];

  /// Initialize notification service
  Future<void> initialize({
    required String baseUrl,
    required String authToken,
  }) async {
    _baseUrl = baseUrl;
    _authToken = authToken;

    await _initializeLocalNotifications();
    await _initializeFirebaseMessaging();
    _setupWebSocketListeners();
  }

  /// Initialize local notifications
  Future<void> _initializeLocalNotifications() async {
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );
  }

  /// Initialize Firebase Cloud Messaging
  Future<void> _initializeFirebaseMessaging() async {
    // Request permission for notifications
    NotificationSettings settings = await FirebaseMessaging.instance.requestPermission(
      alert: true,
      badge: true,
      sound: true,
      provisional: false,
    );

    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      debugPrint('User granted notification permission');
      
      // Get FCM token
      _fcmToken = await FirebaseMessaging.instance.getToken();
      if (_fcmToken != null) {
        await _saveFcmTokenToServer(_fcmToken!);
      }

      // Listen for token refresh
      FirebaseMessaging.instance.onTokenRefresh.listen((newToken) {
        _fcmToken = newToken;
        _saveFcmTokenToServer(newToken);
      });

      // Handle foreground messages
      FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

      // Handle background messages
      FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);

      // Handle notification taps when app is in background
      FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);

      // Handle notification tap when app is terminated
      RemoteMessage? initialMessage = await FirebaseMessaging.instance.getInitialMessage();
      if (initialMessage != null) {
        _handleNotificationTap(initialMessage);
      }
    } else {
      debugPrint('User declined notification permission');
    }
  }

  /// Setup WebSocket listeners for real-time notifications
  void _setupWebSocketListeners() {
    _webSocketService.notificationStream.listen((data) {
      _handleRealtimeNotification(data);
    });
  }

  /// Save FCM token to server
  Future<void> _saveFcmTokenToServer(String token) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/api/auth/mobile/fcm-token'),
        headers: {
          'Authorization': 'Bearer $_authToken',
          'Content-Type': 'application/json',
        },
        body: json.encode({
          'token': token,
          'deviceType': 'android', // or 'ios' based on platform
        }),
      );

      if (response.statusCode == 200) {
        debugPrint('FCM token saved successfully');
      } else {
        debugPrint('Failed to save FCM token: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error saving FCM token: $e');
    }
  }

  /// Get all notifications
  Future<List<AppNotification>> getNotifications({
    int limit = 20,
    int offset = 0,
    bool unreadOnly = false,
  }) async {
    try {
      final queryParams = {
        'limit': limit.toString(),
        'offset': offset.toString(),
        'unreadOnly': unreadOnly.toString(),
      };

      final uri = Uri.parse('$_baseUrl/api/auth/mobile/notifications')
          .replace(queryParameters: queryParams);

      final response = await http.get(
        uri,
        headers: {
          'Authorization': 'Bearer $_authToken',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        _notificationCache = data.map((json) => AppNotification.fromJson(json)).toList();
        _notificationsController.add(_notificationCache);
        return _notificationCache;
      } else {
        throw Exception('Failed to load notifications: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching notifications: $e');
      rethrow;
    }
  }

  /// Mark notification as read
  Future<void> markNotificationAsRead(int notificationId) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/api/auth/mobile/notifications/$notificationId/read'),
        headers: {
          'Authorization': 'Bearer $_authToken',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        // Update local cache
        final index = _notificationCache.indexWhere((n) => n.id == notificationId);
        if (index != -1) {
          _notificationCache[index] = _notificationCache[index].copyWith(
            isRead: true,
            readAt: DateTime.now(),
          );
          _notificationsController.add(_notificationCache);
        }
      } else {
        throw Exception('Failed to mark notification as read: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error marking notification as read: $e');
      rethrow;
    }
  }

  /// Mark all notifications as read
  Future<void> markAllNotificationsAsRead() async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/api/auth/mobile/notifications/read-all'),
        headers: {
          'Authorization': 'Bearer $_authToken',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        // Update local cache
        _notificationCache = _notificationCache.map((n) => n.copyWith(
          isRead: true,
          readAt: DateTime.now(),
        )).toList();
        _notificationsController.add(_notificationCache);
      } else {
        throw Exception('Failed to mark all notifications as read: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error marking all notifications as read: $e');
      rethrow;
    }
  }

  /// Handle foreground FCM messages
  void _handleForegroundMessage(RemoteMessage message) {
    debugPrint('Received foreground message: ${message.messageId}');
    
    // Show local notification
    _showLocalNotification(
      title: message.notification?.title ?? 'New Notification',
      body: message.notification?.body ?? '',
      payload: json.encode(message.data),
    );

    // Add to notification cache if it's a notification
    if (message.data.containsKey('notificationId')) {
      _handleNewNotificationData(message.data);
    }
  }

  /// Handle background FCM messages
  static Future<void> _handleBackgroundMessage(RemoteMessage message) async {
    debugPrint('Received background message: ${message.messageId}');
    // Handle background message processing here
  }

  /// Handle notification tap
  void _handleNotificationTap(RemoteMessage message) {
    debugPrint('Notification tapped: ${message.messageId}');
    
    // Navigate to appropriate screen based on notification data
    if (message.data.containsKey('link')) {
      // Handle deep linking
      final link = message.data['link'];
      // Implement navigation logic here
    }
  }

  /// Handle real-time notifications from WebSocket
  void _handleRealtimeNotification(Map<String, dynamic> data) {
    try {
      final notification = AppNotification.fromJson(data);
      
      // Add to cache
      _notificationCache.insert(0, notification);
      _notificationsController.add(_notificationCache);
      _newNotificationController.add(notification);

      // Show local notification if app is in foreground
      _showLocalNotification(
        title: notification.title,
        body: notification.message,
        payload: json.encode({'notificationId': notification.id}),
      );
    } catch (e) {
      debugPrint('Error handling real-time notification: $e');
    }
  }

  /// Handle new notification data
  void _handleNewNotificationData(Map<String, dynamic> data) {
    // Refresh notifications to get the latest
    getNotifications();
  }

  /// Show local notification
  Future<void> _showLocalNotification({
    required String title,
    required String body,
    String? payload,
  }) async {
    const androidDetails = AndroidNotificationDetails(
      'default_channel',
      'Default Notifications',
      channelDescription: 'Default notification channel',
      importance: Importance.high,
      priority: Priority.high,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const notificationDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      DateTime.now().millisecondsSinceEpoch ~/ 1000,
      title,
      body,
      notificationDetails,
      payload: payload,
    );
  }

  /// Handle local notification tap
  void _onNotificationTapped(NotificationResponse response) {
    if (response.payload != null) {
      try {
        final data = json.decode(response.payload!);
        if (data.containsKey('notificationId')) {
          final notificationId = data['notificationId'] as int;
          markNotificationAsRead(notificationId);
        }
      } catch (e) {
        debugPrint('Error handling notification tap: $e');
      }
    }
  }

  /// Get unread notification count
  int get unreadCount {
    return _notificationCache.where((n) => !n.isRead).length;
  }

  void dispose() {
    _notificationsController.close();
    _newNotificationController.close();
  }
}

// Notification data model
class AppNotification {
  final int id;
  final String userId;
  final String type;
  final String title;
  final String message;
  final String? link;
  final bool isRead;
  final DateTime createdAt;
  final DateTime? readAt;

  AppNotification({
    required this.id,
    required this.userId,
    required this.type,
    required this.title,
    required this.message,
    this.link,
    required this.isRead,
    required this.createdAt,
    this.readAt,
  });

  factory AppNotification.fromJson(Map<String, dynamic> json) {
    return AppNotification(
      id: json['id'],
      userId: json['userId'],
      type: json['type'],
      title: json['title'],
      message: json['message'],
      link: json['link'],
      isRead: json['isRead'] ?? false,
      createdAt: DateTime.parse(json['createdAt']),
      readAt: json['readAt'] != null ? DateTime.parse(json['readAt']) : null,
    );
  }

  AppNotification copyWith({
    bool? isRead,
    DateTime? readAt,
  }) {
    return AppNotification(
      id: id,
      userId: userId,
      type: type,
      title: title,
      message: message,
      link: link,
      isRead: isRead ?? this.isRead,
      createdAt: createdAt,
      readAt: readAt ?? this.readAt,
    );
  }
}
