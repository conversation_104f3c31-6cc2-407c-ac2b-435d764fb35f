// main.dart - App Integration Example
import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'websocket_service.dart';
import 'message_service.dart';
import 'notification_service.dart';
import 'queue_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize Firebase
  await Firebase.initializeApp();
  
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'YachFin App',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: AppInitializer(),
    );
  }
}

class AppInitializer extends StatefulWidget {
  @override
  _AppInitializerState createState() => _AppInitializerState();
}

class _AppInitializerState extends State<AppInitializer> {
  final WebSocketService _webSocketService = WebSocketService();
  final MessageService _messageService = MessageService();
  final NotificationService _notificationService = NotificationService();
  final QueueService _queueService = QueueService();

  bool _isInitialized = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  Future<void> _initializeServices() async {
    try {
      // Replace with your actual server URL and auth token
      const serverUrl = 'http://your-server-url.com'; // e.g., 'http://localhost:3001'
      const authToken = 'your-jwt-token'; // Get from your auth system
      const userId = 'your-user-id'; // Get from your auth system

      // Initialize all services
      await _notificationService.initialize(
        baseUrl: serverUrl,
        authToken: authToken,
      );

      _messageService.initialize(
        baseUrl: serverUrl,
        authToken: authToken,
      );

      _queueService.initialize(
        baseUrl: serverUrl,
        authToken: authToken,
      );

      // Connect WebSocket (this should be done after user authentication)
      await _webSocketService.connect(
        serverUrl: serverUrl,
        authToken: authToken,
        userId: userId,
      );

      setState(() {
        _isInitialized = true;
      });

      // Load initial data
      await _loadInitialData();
    } catch (e) {
      setState(() {
        _error = e.toString();
      });
    }
  }

  Future<void> _loadInitialData() async {
    try {
      // Load conversations
      await _messageService.getConversations();
      
      // Load notifications
      await _notificationService.getNotifications();
      
      // Request queue status
      _queueService.requestQueueStatus();
    } catch (e) {
      print('Error loading initial data: $e');
    }
  }

  @override
  void dispose() {
    _webSocketService.dispose();
    _messageService.dispose();
    _notificationService.dispose();
    _queueService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_error != null) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error, size: 64, color: Colors.red),
              SizedBox(height: 16),
              Text('Initialization Error'),
              SizedBox(height: 8),
              Text(_error!, textAlign: TextAlign.center),
              SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _error = null;
                    _isInitialized = false;
                  });
                  _initializeServices();
                },
                child: Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    if (!_isInitialized) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Initializing app...'),
            ],
          ),
        ),
      );
    }

    return MainScreen();
  }
}

class MainScreen extends StatefulWidget {
  @override
  _MainScreenState createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _currentIndex = 0;

  final List<Widget> _screens = [
    QueueScreen(),
    MessagesScreen(),
    NotificationsScreen(),
    ProfileScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _screens[_currentIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,
        onTap: (index) => setState(() => _currentIndex = index),
        items: [
          BottomNavigationBarItem(
            icon: Icon(Icons.queue),
            label: 'Queue',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.message),
            label: 'Messages',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.notifications),
            label: 'Notifications',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: 'Profile',
          ),
        ],
      ),
    );
  }
}

// Example Queue Screen
class QueueScreen extends StatefulWidget {
  @override
  _QueueScreenState createState() => _QueueScreenState();
}

class _QueueScreenState extends State<QueueScreen> {
  final QueueService _queueService = QueueService();

  @override
  void initState() {
    super.initState();
    _setupQueueListener();
  }

  void _setupQueueListener() {
    _queueService.queueUpdateStream.listen((queueUpdate) {
      if (mounted) {
        setState(() {
          // Update UI with queue information
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final queueStatuses = _queueService.getAllQueueStatuses();

    return Scaffold(
      appBar: AppBar(
        title: Text('Queue Status'),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: () => _queueService.requestQueueStatus(),
          ),
        ],
      ),
      body: queueStatuses.isEmpty
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.queue, size: 64, color: Colors.grey),
                  SizedBox(height: 16),
                  Text('No active queues'),
                  SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: () => _queueService.requestQueueStatus(),
                    child: Text('Refresh'),
                  ),
                ],
              ),
            )
          : ListView.builder(
              itemCount: queueStatuses.length,
              itemBuilder: (context, index) {
                final queueStatus = queueStatuses.values.elementAt(index);
                return QueueStatusCard(queueStatus: queueStatus);
              },
            ),
    );
  }
}

class QueueStatusCard extends StatelessWidget {
  final QueueStatus queueStatus;

  const QueueStatusCard({Key? key, required this.queueStatus}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.all(8),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  queueStatus.serviceName ?? 'Service',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                Chip(
                  label: Text(queueStatus.status ?? 'Unknown'),
                  backgroundColor: _getStatusColor(queueStatus.status),
                ),
              ],
            ),
            SizedBox(height: 8),
            if (queueStatus.currentUserPosition != null)
              Text('Position: ${queueStatus.currentUserPosition}'),
            if (queueStatus.currentUserEstimatedWaitMinutes != null)
              Text('Estimated wait: ${queueStatus.currentUserEstimatedWaitMinutes} minutes'),
            if (queueStatus.totalActiveInQueue != null)
              Text('Total in queue: ${queueStatus.totalActiveInQueue}'),
            if (queueStatus.error != null)
              Padding(
                padding: EdgeInsets.only(top: 8),
                child: Text(
                  queueStatus.error!,
                  style: TextStyle(color: Colors.red),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(String? status) {
    switch (status?.toLowerCase()) {
      case 'pending':
        return Colors.orange;
      case 'confirmed':
        return Colors.blue;
      case 'inprogress':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }
}

// Placeholder screens
class MessagesScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Messages')),
      body: Center(child: Text('Messages Screen - Implement with MessageService')),
    );
  }
}

class NotificationsScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Notifications')),
      body: Center(child: Text('Notifications Screen - Implement with NotificationService')),
    );
  }
}

class ProfileScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Profile')),
      body: Center(child: Text('Profile Screen')),
    );
  }
}
