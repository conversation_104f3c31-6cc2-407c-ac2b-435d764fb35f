# Firebase Production Setup Guide

## Overview
This guide helps you complete the setup of your new Firebase production project "dalti-prod" for push notifications.

## ✅ Completed Steps

### 1. Backend Configuration Updated
- ✅ Updated `app/src/server/firebaseAdmin.ts` with production project details
- ✅ Changed project_id to "dalti-prod"
- ✅ Updated client_email to "<EMAIL>"
- ✅ Updated private_key_id and client_id with production values

### 2. Client Configuration Prepared
- ✅ Updated `app/src/client/firebase.ts` with production project structure
- ✅ Updated `app/public/firebase-messaging-sw.js` with production project structure

## 🔄 Remaining Steps

### Step 1: Get Firebase Client Configuration
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your "dalti-prod" project
3. Click Settings (⚙️) → Project settings
4. Scroll to "Your apps" section
5. If no web app exists, click "Add app" → Web (</>) and register your app
6. Copy the `firebaseConfig` object

### Step 2: Update Client Configuration Files
Replace the placeholder values in these files with your actual Firebase config:

**File: `app/src/client/firebase.ts`**
```javascript
const firebaseConfig = {
  apiKey: "YOUR_ACTUAL_API_KEY",           // Replace this
  authDomain: "dalti-prod.firebaseapp.com",
  projectId: "dalti-prod",
  storageBucket: "dalti-prod.appspot.com", // Verify this in Firebase Console
  messagingSenderId: "YOUR_ACTUAL_SENDER_ID", // Replace this
  appId: "YOUR_ACTUAL_APP_ID",             // Replace this
  measurementId: "YOUR_ACTUAL_MEASUREMENT_ID" // Optional, for Analytics
};
```

**File: `app/public/firebase-messaging-sw.js`**
```javascript
const firebaseConfig = {
  apiKey: "YOUR_ACTUAL_API_KEY",           // Same as above
  authDomain: "dalti-prod.firebaseapp.com",
  projectId: "dalti-prod",
  storageBucket: "dalti-prod.appspot.com", // Same as above
  messagingSenderId: "YOUR_ACTUAL_SENDER_ID", // Same as above
  appId: "YOUR_ACTUAL_APP_ID",             // Same as above
};
```

### Step 3: Generate VAPID Key
1. In Firebase Console → Cloud Messaging
2. Go to "Web configuration" tab
3. Generate a new key pair or use existing
4. Copy the VAPID key

**File: `app/src/client/notifications/useFcmTokenManager.ts`**
```javascript
const VAPID_KEY = "YOUR_NEW_PRODUCTION_VAPID_KEY"; // Replace this
```

### Step 4: Update Environment Variables

**For Development (.env.server):**
```bash
FIREBASE_ADMIN_SDK_CONFIG="***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
```

**For Production (Kubernetes Secret):**
Update `k8s-wasp/backend/02-secrets.yaml`:
```yaml
# Firebase Admin SDK - base64 encode the private key
FIREBASE_ADMIN_SDK_CONFIG: "LS0tLS1CRUdJTi..." # base64 encoded private key
```

### Step 5: Enable Firebase Cloud Messaging
1. In Firebase Console → Cloud Messaging
2. Make sure Cloud Messaging API is enabled
3. Configure any additional settings as needed

### Step 6: Test the Setup
1. Deploy your changes
2. Test notification sending from your backend
3. Verify notifications are received on web clients

## 🔐 Security Notes
- Never commit actual Firebase credentials to your repository
- Use environment variables for all sensitive data
- The client-side config is safe to be public (it's needed for client identification)
- Keep your private key secure and only in environment variables

## 📝 Files Modified
- ✅ `app/src/server/firebaseAdmin.ts` - Backend Firebase Admin SDK config
- 🔄 `app/src/client/firebase.ts` - Client Firebase config (needs your values)
- 🔄 `app/public/firebase-messaging-sw.js` - Service worker config (needs your values)
- 🔄 `app/src/client/notifications/useFcmTokenManager.ts` - VAPID key (needs your value)
- 🔄 Environment variables (needs your private key)

## Next Steps
1. Get your Firebase client configuration from Firebase Console
2. Replace placeholder values in the client files
3. Generate and update VAPID key
4. Update environment variables with your private key
5. Test the notification system
