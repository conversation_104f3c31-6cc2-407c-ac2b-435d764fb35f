# Authentication Refactoring Project Requirements Document (PRD)

## Project Overview
Refactor the Wasp project's authentication pages to use custom APIs instead of built-in Wasp authentication queries/actions. This will provide better control over the authentication flow, enable proper provider vs customer differentiation, and maintain consistency with the existing custom API infrastructure.

## Current State Analysis
- Login/signup pages are located in `app/src/auth/`
- LoginPage.tsx currently uses <PERSON><PERSON>'s built-in `login` function
- SignupPage.tsx currently uses <PERSON><PERSON>'s built-in `signup` function with custom provider logic
- CustomerSignupPage.tsx already uses custom `createCustomerUser` action (partially refactored)
- Custom API endpoints exist: `/api/auth/request-otp`, `/api/auth/verify-otp-register`, `/api/auth/login`, `/api/auth/provider/verify-otp-register`, `/api/auth/provider/login`

## Goals and Objectives
1. **Replace Built-in Auth**: Replace Wasp's built-in authentication with custom API endpoints
2. **Provider vs Customer Flow**: Ensure clear differentiation between provider and customer registration/login flows
3. **Session Management**: Implement proper session management and JWT token handling
4. **Error Handling**: Maintain consistent error handling and user feedback across all auth flows
5. **Backward Compatibility**: Ensure the refactoring doesn't break existing functionality
6. **Testing**: Comprehensive testing of all authentication flows

## Technical Requirements

### Authentication Flow Requirements
- **Login Flow**: Use `/api/auth/login` for general login and `/api/auth/provider/login` for provider-specific login
- **Customer Registration**: Use email-based OTP flow with `/api/auth/request-email-otp` and `/api/auth/verify-otp-register`
- **Provider Registration**: Use email-based OTP flow with provider category selection via `/api/auth/provider/verify-otp-register`
- **Session Management**: Handle JWT tokens and authentication state properly
- **Error Handling**: Consistent error messages and user feedback

### UI/UX Requirements
- Maintain existing UI design and user experience
- Clear differentiation between provider and customer flows
- Proper loading states and error messages
- Responsive design and accessibility

### Security Requirements
- Secure token storage and management
- Proper session handling and expiration
- Input validation and sanitization
- Protection against common auth vulnerabilities

## Implementation Phases

### Phase 1: Foundation & Utilities
- Create custom authentication utilities to replace Wasp's built-in functions
- Implement session management utilities for JWT tokens
- Create custom hooks for authentication state management

### Phase 2: Login Page Refactoring
- Update LoginPage.tsx to use custom login APIs
- Implement provider vs customer login differentiation
- Add proper error handling and user feedback

### Phase 3: Provider Signup Refactoring
- Update SignupPage.tsx to use custom provider registration APIs
- Implement email OTP flow for providers
- Handle provider category selection properly

### Phase 4: Customer Signup Verification
- Verify and improve CustomerSignupPage.tsx
- Ensure consistency with provider signup flow
- Test customer registration flow

### Phase 5: Session Management & Integration
- Implement proper authentication state management
- Handle token refresh and session persistence
- Update components that depend on authentication

### Phase 6: Testing & Cleanup
- Comprehensive testing of all auth flows
- Remove unused Wasp auth dependencies
- Update documentation and finalize implementation

## Success Criteria
- All authentication flows work with custom APIs
- No dependency on Wasp's built-in auth functions
- Provider and customer flows are clearly differentiated
- Proper session management and token handling
- Comprehensive error handling and user feedback
- All existing functionality is preserved
- Code is well-documented and maintainable

## Technical Constraints
- Must maintain compatibility with existing Wasp project structure
- Must work with existing custom API handlers
- Must preserve existing UI/UX design
- Must maintain security standards
- Must be thoroughly tested before deployment

## Dependencies
- Existing custom API handlers in `app/src/auth/apiHandlers.ts`
- Existing authentication middleware
- Current Wasp authentication configuration
- Existing UI components and styling

## Risks and Mitigation
- **Risk**: Breaking existing authentication flows
  **Mitigation**: Thorough testing and gradual rollout
- **Risk**: Session management issues
  **Mitigation**: Proper token handling and testing
- **Risk**: User experience degradation
  **Mitigation**: Maintain existing UI/UX patterns
- **Risk**: Security vulnerabilities
  **Mitigation**: Follow security best practices and code review

## User Stories

### As a Customer
- I want to register with my email so I can book appointments
- I want to log in securely so I can access my account
- I want to receive OTP verification so my account is secure
- I want clear error messages if something goes wrong

### As a Provider
- I want to register as a service provider with my category
- I want to log in to my provider account
- I want to complete my business setup after registration
- I want my login flow to be different from customers

### As a Developer
- I want consistent API patterns across all auth flows
- I want proper error handling and logging
- I want maintainable and testable code
- I want clear documentation for all auth endpoints

## Technical Specifications

### Frontend Components
- Custom authentication utilities replacing Wasp built-ins
- Session management hooks and context
- Updated LoginPage.tsx with custom API integration
- Updated SignupPage.tsx with provider flow
- Improved CustomerSignupPage.tsx consistency

### Backend APIs
- `/api/auth/login` - General login endpoint
- `/api/auth/provider/login` - Provider-specific login
- `/api/auth/request-email-otp` - Email OTP for registration
- `/api/auth/verify-otp-register` - Customer registration completion
- `/api/auth/provider/verify-otp-register` - Provider registration completion

### Security Features
- JWT token management
- Secure session handling
- Input validation with Zod schemas
- CSRF protection
- Rate limiting on auth endpoints
