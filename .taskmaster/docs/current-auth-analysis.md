# Current Authentication Implementation Analysis

## Overview
This document provides a comprehensive analysis of the current Wasp authentication implementation that needs to be refactored to use custom APIs.

## Current Wasp Auth Configuration

### Main Configuration (main.wasp lines 32-72)
```wasp
auth: {
  userEntity: User,
  methods: {
    email: {
      fromField: {
        name: "<PERSON><PERSON>",
        email: "<EMAIL>"
      },
      emailVerification: {
        clientRoute: EmailVerificationRoute,
        getEmailContentFn: import { getVerificationEmailContent } from "@src/auth/email-and-pass/emails",
      },
      passwordReset: {
        clientRoute: PasswordResetRoute,
        getEmailContentFn: import { getPasswordResetEmailContent } from "@src/auth/email-and-pass/emails",
      },
      userSignupFields: import { getEmailUserFields } from "@src/auth/userSignupFields",
    },
  },
  onAuthFailedRedirectTo: "/login",
  onAuthSucceededRedirectTo: "/admin",
}
```

### Key Features
- **Email-based authentication** (not username/password)
- **Email verification** enabled with custom email content
- **Password reset** functionality with custom email content
- **Custom user signup fields** via `getEmailUserFields`
- **Automatic redirects** on auth success/failure

## Current Auth Pages & Routes

### Auth Pages (main.wasp lines 178-209)
1. **LoginRoute** (`/login`) → `LoginPage`
2. **SignupRoute** (`/signup`) → `SignupPage` 
3. **CustomerSignupRoute** (`/signup-customer`) → `CustomerSignupPage`
4. **RequestPasswordResetRoute** (`/request-password-reset`) → `RequestPasswordResetPage`
5. **PasswordResetRoute** (`/password-reset`) → `PasswordResetPage`
6. **EmailVerificationRoute** (`/email-verification`) → `EmailVerificationPage`

## Files Using Wasp Auth Utilities

### 1. LoginPage.tsx
**Location**: `app/src/auth/LoginPage.tsx`
**Wasp Dependencies**:
- `import { login, useAuth } from 'wasp/client/auth'`

**Current Implementation**:
- Uses `useAuth()` hook to get current user data
- Uses `login({ email, password })` for authentication
- Redirects to AdminRoute after successful login
- Has error handling and loading states

**Key Code**:
```typescript
const { data: user } = useAuth();
await login({ email: values.email, password: values.password });
```

### 2. SignupPage.tsx
**Location**: `app/src/auth/SignupPage.tsx`
**Wasp Dependencies**:
- `import { signup, useAuth } from 'wasp/client/auth'`
- `import { useQuery, getProviderCategories, createSProviderForUser } from 'wasp/client/operations'`

**Current Implementation**:
- Uses `useAuth()` hook to get current user data
- Uses `signup({ email, password })` for registration
- After signup, calls `createSProviderForUser` action to create provider profile
- Has complex flow with potential race conditions (noted in comments)
- Redirects to login page after successful signup

**Key Code**:
```typescript
const { data: user } = useAuth();
await signup({ email, password });
await createSProviderForUser({ providerCategoryId: finalProviderCategoryId, email: email });
```

### 3. CustomerSignupPage.tsx ✅ (Partially Refactored)
**Location**: `app/src/auth/CustomerSignupPage.tsx`
**Wasp Dependencies**:
- `import { useAuth } from 'wasp/client/auth'` (only for user state)
- `import { createCustomerUser } from 'wasp/client/operations'` (custom action)

**Current Implementation**:
- Already uses custom `createCustomerUser` action instead of built-in signup
- Only uses `useAuth()` for checking current user state
- This is the most advanced in terms of custom API usage

### 4. App.tsx (Global Auth State)
**Location**: `app/src/client/App.tsx`
**Wasp Dependencies**:
- `import { useAuth } from 'wasp/client/auth'`

**Usage**: Global authentication state management for conditional rendering

### 5. AccountPage.tsx
**Location**: `app/src/user/AccountPage.tsx`
**Wasp Dependencies**:
- `import { logout } from 'wasp/client/auth'`

**Usage**: Logout functionality in user account page

### 6. UserMenuItems.tsx
**Location**: `app/src/user/UserMenuItems.tsx`
**Wasp Dependencies**:
- `import { logout } from 'wasp/client/auth'`

**Usage**: Logout functionality in user menu

### 7. NavBar.tsx
**Location**: `app/src/client/components/NavBar/NavBar.tsx`
**Wasp Dependencies**:
- `import { useAuth } from 'wasp/client/auth'`

**Usage**: Conditional rendering based on authentication state

### 8. Hero.tsx
**Location**: `app/src/landing-page/components/Hero.tsx`
**Wasp Dependencies**:
- `import { useAuth } from 'wasp/client/auth'`

**Usage**: Conditional rendering on landing page

### 9. useFcmTokenManager.ts
**Location**: `app/src/client/notifications/useFcmTokenManager.ts`
**Wasp Dependencies**:
- `import { useAuth } from 'wasp/client/auth'`

**Usage**: FCM token management for authenticated users

## Current Authentication Flows

### Provider Registration Flow
1. User visits `/signup` (SignupPage.tsx)
2. Fills form with email, password, and provider category
3. Calls `signup({ email, password })` (Wasp built-in)
4. If successful, calls `createSProviderForUser({ providerCategoryId, email })`
5. Redirects to login page with success message
6. **Issues**: Race conditions, complex error handling

### Customer Registration Flow ✅ (Already Custom)
1. User visits `/signup-customer` (CustomerSignupPage.tsx)
2. Fills form with email, password, firstName, lastName
3. Calls `createCustomerUser({ email, password, firstName, lastName })` (custom action)
4. Redirects to login page
5. **Status**: Already using custom API

### Login Flow
1. User visits `/login` (LoginPage.tsx)
2. Fills form with email and password
3. Calls `login({ email, password })` (Wasp built-in)
4. Redirects to `/admin` on success
5. **Issues**: Uses built-in Wasp authentication

### Session Management
- **Current**: Handled entirely by Wasp's built-in system
- **Token Storage**: Managed by Wasp internally
- **Auth State**: Provided by `useAuth()` hook
- **Logout**: Uses built-in `logout()` function

## Protected Routes

### Pages with `authRequired: true`
- AccountPage (`/account`)
- DemoAppPage (`/demo-app`)
- CheckoutPage (`/checkout`)
- FileUploadPage (`/file-upload`)
- All Admin pages (`/admin/*`)
- Customer pages (`/my-profile`, `/my-appointments`)

## Existing Custom API Endpoints

### Already Available
- `/api/auth/request-otp` - Phone OTP requests
- `/api/auth/request-email-otp` - Email OTP requests
- `/api/auth/verify-otp-register` - Customer registration completion
- `/api/auth/provider/verify-otp-register` - Provider registration completion
- `/api/auth/login` - General login endpoint
- `/api/auth/provider/login` - Provider-specific login
- `/api/auth/provider/complete-setup` - Provider onboarding completion

### API Namespace Configuration
```wasp
apiNamespace authApis {
  middlewareConfigFn: import { authApiNamespaceMiddleware } from "@src/auth/middleware",
  path: "/api/auth"
}
```

## Refactoring Requirements

### High Priority (Must Replace)
1. **LoginPage.tsx**: Replace `login()` with custom API call
2. **SignupPage.tsx**: Replace `signup()` with custom provider registration flow
3. **Global Auth State**: Replace `useAuth()` with custom auth context
4. **Logout Functionality**: Replace `logout()` with custom logout API

### Medium Priority (Enhance)
1. **CustomerSignupPage.tsx**: Ensure consistency with new auth system
2. **Session Management**: Implement JWT token handling
3. **Protected Routes**: Update route protection logic

### Low Priority (Maintain)
1. **Email Verification**: May keep existing Wasp functionality
2. **Password Reset**: May keep existing Wasp functionality

## Dependencies to Remove
1. `login` from 'wasp/client/auth'
2. `signup` from 'wasp/client/auth'
3. `logout` from 'wasp/client/auth'
4. `useAuth` from 'wasp/client/auth' (replace with custom)
5. Wasp auth configuration in main.wasp

## Next Steps
1. Create custom authentication utilities (JWT, session management)
2. Implement custom auth context and hooks
3. Refactor LoginPage and SignupPage
4. Update all components using Wasp auth utilities
5. Remove Wasp auth dependencies
6. Comprehensive testing of all auth flows
