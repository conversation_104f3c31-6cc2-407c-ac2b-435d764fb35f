# Task ID: 12
# Title: Implement Create Service API
# Status: done
# Dependencies: 2, 3, 4
# Priority: high
# Description: Create an API endpoint to allow providers to create new services.
# Details:
1. Create a new API handler for POST /api/providers/services
2. Use authentication middleware to verify provider
3. Implement Zod schema for service creation validation
4. Call existing Wasp operation to create service
5. Format response according to API standards
6. Add to main.wasp API definitions

```typescript
// In main.wasp
api {
  httpRoute: POST /api/providers/services,
  fn: import { createService } from "@server/api/providers/services",
  auth: true
}

// In @server/api/providers/services.ts
import { z } from 'zod';
import { createProviderService } from '../../../provider/operations';
import { validateRequest } from '../../common/validation';
import { handleApiError } from '../../common/errors';

const createServiceSchema = z.object({
  body: z.object({
    name: z.string().min(1),
    description: z.string().optional(),
    duration: z.number().int().positive(),
    price: z.number().nonnegative().optional(),
    currency: z.string().optional(),
    categoryId: z.string().uuid().optional(),
    isActive: z.boolean().optional().default(true),
    color: z.string().optional(),
    icon: z.string().optional()
  })
});

export const createService = [
  validateRequest(createServiceSchema),
  async (req, res) => {
    try {
      const providerId = req.providerId;
      const serviceData = req.body;
      
      const newService = await createProviderService({
        providerId,
        ...serviceData
      });
      
      return res.status(201).json({
        status: 'success',
        data: newService
      });
    } catch (error) {
      return handleApiError(res, error);
    }
  }
];
```

# Test Strategy:
1. Test with valid service data
2. Test with missing required fields
3. Test with invalid data types
4. Test with authenticated non-provider user
5. Test with unauthenticated request
6. Verify service is created in database with correct provider association
7. Verify response format matches API standards

# Subtasks:
## 1. Define the API endpoint in main.wasp [done]
### Dependencies: None
### Description: Add the API endpoint definition to main.wasp to register the new service creation endpoint with the Wasp framework.
### Details:
Add the following code to main.wasp in the api section:
```
api {
  httpRoute: POST /api/providers/services,
  fn: import { createService } from "@server/api/providers/services",
  auth: true
}
```
This registers the endpoint with the Wasp framework and specifies that authentication is required.

## 2. Create Zod validation schema [done]
### Dependencies: 12.1
### Description: Implement the Zod schema for validating service creation requests, ensuring all required fields are present and properly formatted.
### Details:
Create the file @server/api/providers/services.ts and implement the createServiceSchema using Zod. Include validation for name, description, duration, price, currency, categoryId, isActive, color, and icon fields with appropriate constraints for each field type. Import the necessary Zod package and set up the schema structure as shown in the example code.

## 3. Implement the service creation handler function [done]
### Dependencies: 12.2
### Description: Create the handler function that processes validated requests, extracts the provider ID from the authenticated request, and calls the service creation operation.
### Details:
In the same @server/api/providers/services.ts file, implement the createService handler function that:
1. Extracts providerId from the authenticated request
2. Gets service data from the request body
3. Calls the createProviderService operation with the combined data
4. Returns a properly formatted 201 response with the created service
5. Implements error handling using handleApiError

## 4. Implement the createProviderService operation [done]
### Dependencies: 12.3
### Description: Create the backend operation that handles the actual creation of a service in the database.
### Details:
Create or update the file @server/provider/operations.ts to implement the createProviderService function that:
1. Takes providerId and service data as parameters
2. Validates that the provider exists
3. Creates a new service record in the database using Prisma
4. Associates the service with the provider
5. Returns the created service object
6. Handles potential database errors or constraint violations

## 5. Implement error handling and request validation middleware [done]
### Dependencies: 12.4
### Description: Create or update the common utility functions for API request validation and error handling to ensure consistent behavior across endpoints.
### Details:
Create or update the files @server/common/validation.ts and @server/common/errors.ts to implement:
1. validateRequest middleware that uses Zod schemas to validate incoming requests
2. handleApiError function that formats error responses consistently
3. Custom error types for common scenarios (not found, unauthorized, validation error)
4. Proper error logging
Ensure these utilities are properly exported and imported in the services.ts file.

