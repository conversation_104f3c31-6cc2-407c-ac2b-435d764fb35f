# Task ID: 2
# Title: Implement Authentication Middleware for Provider APIs
# Status: done
# Dependencies: 1
# Priority: high
# Description: Create authentication middleware to ensure all Provider APIs require proper authentication and authorization.
# Details:
1. Review existing authentication system
2. Create middleware that verifies JWT tokens
3. Extract provider ID from authenticated user
4. Implement provider ownership verification function
5. Create utility to check if user has provider role

```typescript
// Example middleware implementation
export const requireProviderAuth = async (req, res, next) => {
  try {
    // Verify authentication token
    const user = req.user;
    if (!user) {
      return res.status(401).json({ message: 'Authentication required' });
    }
    
    // Verify user has provider role
    const isProvider = await checkProviderRole(user.id);
    if (!isProvider) {
      return res.status(403).json({ message: 'Provider access required' });
    }
    
    // Add provider info to request object
    req.providerId = await getProviderIdFromUserId(user.id);
    next();
  } catch (error) {
    return res.status(500).json({ message: 'Authentication error', error: error.message });
  }
};
```

# Test Strategy:
1. Test middleware with valid provider authentication
2. Test middleware with invalid authentication
3. Test middleware with non-provider user
4. Verify provider ID is correctly attached to request
5. Test error handling scenarios

# Subtasks:
## 1. Create JWT Token Verification Middleware [done]
### Dependencies: None
### Description: Implement middleware function to verify JWT tokens in request headers and extract user information.
### Details:
Create a new file `src/auth/middleware.ts` that exports a middleware function to verify JWT tokens. The function should:
1. Extract the JWT token from the Authorization header
2. Verify the token using the JWT library
3. Decode the token to get user information
4. Attach the user object to the request
5. Handle errors appropriately with proper status codes

## 2. Implement Provider Role Verification Function [done]
### Dependencies: None
### Description: Create a utility function that checks if a user has the provider role.
### Details:
Create a new file `src/auth/providerUtils.ts` that exports a function `checkProviderRole(userId: string): Promise<boolean>` which:
1. Queries the database to find the user by ID
2. Checks if the user has the provider role in their roles array
3. Returns true if the user is a provider, false otherwise
4. Handles potential database errors

## 3. Create Provider ID Retrieval Function [done]
### Dependencies: None
### Description: Implement a function to get the provider ID associated with a user ID.
### Details:
Add a function `getProviderIdFromUserId(userId: string): Promise<string | null>` to `src/auth/providerUtils.ts` that:
1. Queries the database to find the Provider entity associated with the user ID
2. Returns the provider ID if found, or null if not found
3. Handles potential database errors
4. Uses proper Prisma queries following Wasp patterns

## 4. Implement Complete Provider Authentication Middleware [done]
### Dependencies: None
### Description: Create the main provider authentication middleware that combines token verification, role checking, and provider ID retrieval.
### Details:
Create a new file `src/auth/providerMiddleware.ts` that exports the `requireProviderAuth` middleware function which:
1. Uses the JWT verification middleware
2. Checks if the authenticated user has the provider role
3. Retrieves and attaches the provider ID to the request object
4. Implements proper error handling with appropriate HTTP status codes
5. Follows the example implementation pattern provided in the task

## 5. Apply Provider Authentication to API Routes [done]
### Dependencies: None
### Description: Update the main.wasp file to apply the provider authentication middleware to all provider-specific API endpoints.
### Details:
1. Modify the main.wasp file to import and use the provider authentication middleware
2. Apply the middleware to all provider-specific API routes
3. Update any existing provider API handlers to use the provider ID from the request object
4. Create a middleware registration pattern that can be reused across multiple routes
5. Ensure proper TypeScript types are maintained throughout the request pipeline

