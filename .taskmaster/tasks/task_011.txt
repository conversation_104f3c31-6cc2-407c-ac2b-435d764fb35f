# Task ID: 11
# Title: Implement Delete Location API
# Status: done
# Dependencies: 2, 3, 4, 8
# Priority: medium
# Description: Create an API endpoint to delete a provider location.
# Details:
1. Create a new API handler for DELETE /api/providers/locations/:id
2. Use authentication middleware to verify provider
3. Verify provider owns the location
4. Check for dependencies (queues, appointments, etc.)
5. Call existing Wasp operation to delete location
6. Format response according to API standards
7. Add to main.wasp API definitions

```typescript
// In main.wasp
api {
  httpRoute: DELETE /api/providers/locations/:id,
  fn: import { deleteLocation } from "@server/api/providers/locations",
  auth: true
}

// In @server/api/providers/locations.ts
import { z } from 'zod';
import { deleteProviderLocation, getLocationById, checkLocationDependencies } from '../../../provider/operations';
import { validateRequest } from '../../common/validation';
import { handleApiError } from '../../common/errors';

const deleteLocationSchema = z.object({
  params: z.object({
    id: z.string().uuid()
  })
});

export const deleteLocation = [
  validateRequest(deleteLocationSchema),
  async (req, res) => {
    try {
      const providerId = req.providerId;
      const locationId = req.params.id;
      
      // Verify provider owns this location
      const existingLocation = await getLocationById(locationId);
      if (!existingLocation || existingLocation.providerId !== providerId) {
        return res.status(404).json({
          status: 'error',
          message: 'Location not found'
        });
      }
      
      // Check for dependencies
      const dependencies = await checkLocationDependencies(locationId);
      if (dependencies.hasActiveQueues || dependencies.hasFutureAppointments) {
        return res.status(409).json({
          status: 'error',
          message: 'Cannot delete location with active queues or future appointments',
          dependencies
        });
      }
      
      await deleteProviderLocation(locationId);
      
      return res.status(200).json({
        status: 'success',
        message: 'Location deleted successfully'
      });
    } catch (error) {
      return handleApiError(res, error);
    }
  }
];
```

# Test Strategy:
1. Test with valid location ID
2. Test with location that has dependencies
3. Test with location not owned by provider
4. Test with non-existent location ID
5. Test with authenticated non-provider user
6. Verify location is actually deleted from database
7. Verify proper error response when deletion not allowed

# Subtasks:
## 1. Create API definition in main.wasp [done]
### Dependencies: None
### Description: Add the DELETE /api/providers/locations/:id endpoint definition to the main.wasp file
### Details:
Add the API route definition to main.wasp using the Wasp API syntax. The route should be a DELETE method at /api/providers/locations/:id, pointing to the deleteLocation function that will be implemented in @server/api/providers/locations.ts. Make sure to set auth: true to require authentication.

## 2. Implement validation schema and request handler [done]
### Dependencies: 11.1
### Description: Create the deleteLocation handler with request validation using Zod
### Details:
Implement the deleteLocation function in @server/api/providers/locations.ts. Create a Zod schema (deleteLocationSchema) to validate the request parameters, ensuring the id is a valid UUID. Set up the handler as an array with the validation middleware followed by the actual request handler function.

## 3. Implement location ownership verification [done]
### Dependencies: 11.2
### Description: Add logic to verify that the authenticated provider owns the location being deleted
### Details:
Inside the request handler, extract the providerId from the authenticated request and the locationId from the request parameters. Use the getLocationById operation to fetch the location and verify that it exists and belongs to the authenticated provider. Return a 404 error if the location doesn't exist or doesn't belong to the provider.

## 4. Implement dependency checking logic [done]
### Dependencies: 11.3
### Description: Add logic to check if the location has active queues or future appointments
### Details:
Use the checkLocationDependencies operation to verify if the location can be safely deleted. If the location has active queues or future appointments, return a 409 Conflict response with details about the dependencies preventing deletion.

## 5. Implement location deletion and response handling [done]
### Dependencies: 11.4
### Description: Complete the handler with location deletion and proper response formatting
### Details:
If all checks pass, call the deleteProviderLocation operation to actually delete the location. Format the success response according to API standards with a 200 status code and a success message. Implement proper error handling using the handleApiError helper to catch and format any unexpected errors during the process.

