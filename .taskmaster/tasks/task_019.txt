# Task ID: 19
# Title: Implement Update Queue API
# Status: done
# Dependencies: 2, 3, 4, 17
# Priority: medium
# Description: Create an API endpoint to update an existing provider queue/resource.
# Details:
1. Create a new API handler for PATCH /api/providers/queues/:id
2. Use authentication middleware to verify provider
3. Implement Zod schema for queue update validation
4. Verify provider owns the queue
5. Call existing Wasp operation to update queue
6. Format response according to API standards
7. Add to main.wasp API definitions

```typescript
// In main.wasp
api {
  httpRoute: PATCH /api/providers/queues/:id,
  fn: import { updateQueue } from "@server/api/providers/queues",
  auth: true
}

// In @server/api/providers/queues.ts
import { z } from 'zod';
import { updateProviderQueue, getQueueById } from '../../../queue/operations';
import { getLocationById } from '../../../provider/operations';
import { validateRequest } from '../../common/validation';
import { handleApiError } from '../../common/errors';

const updateQueueSchema = z.object({
  params: z.object({
    id: z.string().uuid()
  }),
  body: z.object({
    name: z.string().min(1).optional(),
    description: z.string().optional(),
    locationId: z.string().uuid().optional(),
    capacity: z.number().int().positive().optional(),
    isActive: z.boolean().optional(),
    color: z.string().optional(),
    icon: z.string().optional()
  })
});

export const updateQueue = [
  validateRequest(updateQueueSchema),
  async (req, res) => {
    try {
      const providerId = req.providerId;
      const queueId = req.params.id;
      const updateData = req.body;
      
      // Verify provider owns this queue
      const existingQueue = await getQueueById(queueId);
      if (!existingQueue || existingQueue.providerId !== providerId) {
        return res.status(404).json({
          status: 'error',
          message: 'Queue not found'
        });
      }
      
      // If locationId is being updated, verify provider owns that location
      if (updateData.locationId) {
        const location = await getLocationById(updateData.locationId);
        if (!location || location.providerId !== providerId) {
          return res.status(404).json({
            status: 'error',
            message: 'Location not found'
          });
        }
      }
      
      const updatedQueue = await updateProviderQueue({
        id: queueId,
        ...updateData
      });
      
      return res.status(200).json({
        status: 'success',
        data: updatedQueue
      });
    } catch (error) {
      return handleApiError(res, error);
    }
  }
];
```

# Test Strategy:
1. Test with valid update data
2. Test with invalid data to verify validation
3. Test with queue not owned by provider
4. Test with non-existent queue ID
5. Test with location change to a location not owned by provider
6. Test with authenticated non-provider user
7. Verify queue is updated correctly in database
8. Test changing location of a queue

# Subtasks:
## 1. Create Update Queue API Definition in main.wasp [done]
### Dependencies: None
### Description: Add the PATCH API endpoint definition to main.wasp file to register the update queue endpoint with the Wasp framework.
### Details:
Open main.wasp and add the following API definition in the appropriate section:
```
api {
  httpRoute: PATCH /api/providers/queues/:id,
  fn: import { updateQueue } from "@server/api/providers/queues",
  auth: true
}
```
Ensure this is placed with other API definitions and follows the correct syntax for Wasp API declarations.

## 2. Implement Zod Validation Schema [done]
### Dependencies: 19.1
### Description: Create the Zod schema for validating the update queue request parameters and body.
### Details:
In the @server/api/providers/queues.ts file, implement the updateQueueSchema using Zod:
```typescript
const updateQueueSchema = z.object({
  params: z.object({
    id: z.string().uuid()
  }),
  body: z.object({
    name: z.string().min(1).optional(),
    description: z.string().optional(),
    locationId: z.string().uuid().optional(),
    capacity: z.number().int().positive().optional(),
    isActive: z.boolean().optional(),
    color: z.string().optional(),
    icon: z.string().optional()
  })
});
```
Ensure all imports are properly defined at the top of the file, including z from 'zod' and any validation utilities.

## 3. Implement Queue Ownership Verification [done]
### Dependencies: 19.2
### Description: Create the logic to verify that the provider making the request owns the queue they're trying to update.
### Details:
In the updateQueue handler function, implement the ownership verification logic:
```typescript
// Verify provider owns this queue
const existingQueue = await getQueueById(queueId);
if (!existingQueue || existingQueue.providerId !== providerId) {
  return res.status(404).json({
    status: 'error',
    message: 'Queue not found'
  });
}

// If locationId is being updated, verify provider owns that location
if (updateData.locationId) {
  const location = await getLocationById(updateData.locationId);
  if (!location || location.providerId !== providerId) {
    return res.status(404).json({
      status: 'error',
      message: 'Location not found'
    });
  }
}
```
Ensure the getQueueById and getLocationById operations are properly imported.

## 4. Implement Queue Update Logic [done]
### Dependencies: 19.3
### Description: Implement the core functionality to update the queue with the validated data.
### Details:
Complete the updateQueue handler by adding the logic to call the updateProviderQueue operation:
```typescript
const updatedQueue = await updateProviderQueue({
  id: queueId,
  ...updateData
});

return res.status(200).json({
  status: 'success',
  data: updatedQueue
});
```
Ensure the updateProviderQueue operation is properly imported and that the response follows the API standards with the correct status code and format.

## 5. Implement Error Handling and Complete API Handler [done]
### Dependencies: 19.4
### Description: Finalize the API handler with proper error handling and export the complete middleware array.
### Details:
Wrap the handler logic in a try-catch block and use the handleApiError utility for consistent error handling:
```typescript
export const updateQueue = [
  validateRequest(updateQueueSchema),
  async (req, res) => {
    try {
      const providerId = req.providerId;
      const queueId = req.params.id;
      const updateData = req.body;
      
      // Verification and update logic from previous subtasks
      
    } catch (error) {
      return handleApiError(res, error);
    }
  }
];
```
Ensure all necessary imports are at the top of the file, including handleApiError and validateRequest utilities.

