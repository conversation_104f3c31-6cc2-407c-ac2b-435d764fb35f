# Task ID: 15
# Title: Implement Delete Service API
# Status: done
# Dependencies: 2, 3, 4, 12
# Priority: medium
# Description: Create an API endpoint to delete a provider service.
# Details:
1. Create a new API handler for DELETE /api/providers/services/:id
2. Use authentication middleware to verify provider
3. Verify provider owns the service
4. Check for dependencies (queues, appointments, etc.)
5. Call existing Wasp operation to delete service
6. Format response according to API standards
7. Add to main.wasp API definitions

```typescript
// In main.wasp
api {
  httpRoute: DELETE /api/providers/services/:id,
  fn: import { deleteService } from "@server/api/providers/services",
  auth: true
}

// In @server/api/providers/services.ts
import { z } from 'zod';
import { deleteProviderService, getServiceById, checkServiceDependencies } from '../../../provider/operations';
import { validateRequest } from '../../common/validation';
import { handleApiError } from '../../common/errors';

const deleteServiceSchema = z.object({
  params: z.object({
    id: z.string().uuid()
  })
});

export const deleteService = [
  validateRequest(deleteServiceSchema),
  async (req, res) => {
    try {
      const providerId = req.providerId;
      const serviceId = req.params.id;
      
      // Verify provider owns this service
      const existingService = await getServiceById(serviceId);
      if (!existingService || existingService.providerId !== providerId) {
        return res.status(404).json({
          status: 'error',
          message: 'Service not found'
        });
      }
      
      // Check for dependencies
      const dependencies = await checkServiceDependencies(serviceId);
      if (dependencies.hasActiveQueues || dependencies.hasFutureAppointments) {
        return res.status(409).json({
          status: 'error',
          message: 'Cannot delete service with active queues or future appointments',
          dependencies
        });
      }
      
      await deleteProviderService(serviceId);
      
      return res.status(200).json({
        status: 'success',
        message: 'Service deleted successfully'
      });
    } catch (error) {
      return handleApiError(res, error);
    }
  }
];
```

# Test Strategy:
1. Test with valid service ID
2. Test with service that has dependencies
3. Test with service not owned by provider
4. Test with non-existent service ID
5. Test with authenticated non-provider user
6. Verify service is actually deleted from database
7. Verify proper error response when deletion not allowed

# Subtasks:
## 1. Create API route definition in main.wasp [done]
### Dependencies: None
### Description: Add the DELETE /api/providers/services/:id endpoint definition to the main.wasp file
### Details:
Add the API route definition to main.wasp using the proper Wasp syntax. The route should be a DELETE method at /api/providers/services/:id, pointing to the deleteService function that will be implemented in @server/api/providers/services.ts. Ensure the auth property is set to true to require authentication.

## 2. Implement validation schema and middleware [done]
### Dependencies: 15.1
### Description: Create the Zod validation schema and middleware for the delete service endpoint
### Details:
In @server/api/providers/services.ts, implement the deleteServiceSchema using Zod to validate the request parameters. The schema should validate that the id parameter is a valid UUID. Import the validateRequest middleware from the common validation utilities to use with this schema.

## 3. Implement service ownership verification [done]
### Dependencies: 15.2
### Description: Create the logic to verify that the authenticated provider owns the service being deleted
### Details:
Implement the getServiceById function in the provider operations module if it doesn't exist already. Use this function in the deleteService handler to fetch the service and verify that the providerId matches the authenticated provider's ID. Return a 404 error if the service doesn't exist or doesn't belong to the provider.

## 4. Implement dependency checking logic [done]
### Dependencies: 15.3
### Description: Create the checkServiceDependencies function to verify a service can be safely deleted
### Details:
Implement the checkServiceDependencies function in the provider operations module. This function should check if the service has any active queues or future appointments. Return an object with boolean flags indicating the presence of dependencies. In the handler, use this function to prevent deletion of services with dependencies by returning a 409 Conflict response.

## 5. Implement service deletion and response handling [done]
### Dependencies: 15.4
### Description: Complete the deleteProviderService operation and implement proper response formatting
### Details:
Implement the deleteProviderService function in the provider operations module to actually delete the service from the database. In the API handler, call this function when all checks pass and format the response according to API standards with a 200 status code and success message. Implement proper error handling using the handleApiError utility for any unexpected errors.

