# Task ID: 18
# Title: Implement Get Queues by Location API
# Status: done
# Dependencies: 2, 3, 4, 17
# Priority: high
# Description: Create an API endpoint to retrieve all queues/resources associated with a specific provider location.
# Details:
1. Create a new API handler for GET /api/providers/locations/:locationId/queues
2. Use authentication middleware to verify provider
3. Verify provider owns the location
4. Implement optional query parameters for filtering
5. Call existing Wasp operation to fetch queues by location
6. Format response according to API standards
7. Add to main.wasp API definitions

```typescript
// In main.wasp
api {
  httpRoute: GET /api/providers/locations/:locationId/queues,
  fn: import { getQueuesByLocation } from "@server/api/providers/queues",
  auth: true
}

// In @server/api/providers/queues.ts
import { z } from 'zod';
import { getQueuesByLocationId } from '../../../queue/operations';
import { getLocationById } from '../../../provider/operations';
import { validateRequest } from '../../common/validation';
import { handleApiError } from '../../common/errors';

const getQueuesByLocationSchema = z.object({
  params: z.object({
    locationId: z.string().uuid()
  }),
  query: z.object({
    isActive: z.enum(['true', 'false']).optional(),
    search: z.string().optional(),
    includeServices: z.enum(['true', 'false']).optional()
  })
});

export const getQueuesByLocation = [
  validateRequest(getQueuesByLocationSchema),
  async (req, res) => {
    try {
      const providerId = req.providerId;
      const locationId = req.params.locationId;
      const { isActive, search, includeServices } = req.query;
      
      // Verify provider owns the location
      const location = await getLocationById(locationId);
      if (!location || location.providerId !== providerId) {
        return res.status(404).json({
          status: 'error',
          message: 'Location not found'
        });
      }
      
      // Convert string query params to appropriate types
      const filters = {
        isActive: isActive ? isActive === 'true' : undefined,
        search: search || undefined,
        includeServices: includeServices === 'true'
      };
      
      const queues = await getQueuesByLocationId(locationId, filters);
      
      return res.status(200).json({
        status: 'success',
        data: queues
      });
    } catch (error) {
      return handleApiError(res, error);
    }
  }
];
```

# Test Strategy:
1. Test with location having multiple queues
2. Test with location having no queues
3. Test with various filter combinations
4. Test with location not owned by provider
5. Test with non-existent location ID
6. Test with authenticated non-provider user
7. Verify response format matches API standards
8. Verify correct queues are returned based on filters
9. Test includeServices parameter to check if services are included in response

# Subtasks:
## 1. Create queue operations function in operations.ts [done]
### Dependencies: None
### Description: Implement the getQueuesByLocationId operation function that will fetch queues by location with filtering capabilities
### Details:
Create or update the file '@server/queue/operations.ts' to implement the getQueuesByLocationId function. This function should accept a locationId parameter and an optional filters object with isActive, search, and includeServices properties. Use Prisma to query the database for queues associated with the given locationId. Apply filters based on the provided parameters. If includeServices is true, include related service entities in the query results.

## 2. Implement validation schema and middleware [done]
### Dependencies: None
### Description: Create the request validation schema and middleware for the API endpoint
### Details:
In '@server/api/common/validation.ts', ensure the validateRequest middleware is properly implemented. Then in '@server/api/providers/queues.ts', define the getQueuesByLocationSchema using zod to validate the request parameters and query strings. The schema should validate locationId as a UUID and optional query parameters (isActive, search, includeServices) with appropriate types.

## 3. Implement the API handler function [done]
### Dependencies: 18.1, 18.2
### Description: Create the getQueuesByLocation handler function that processes the request and returns queues data
### Details:
In '@server/api/providers/queues.ts', implement the getQueuesByLocation handler function as shown in the template. The function should: 1) Extract providerId from the request, 2) Verify the provider owns the location by calling getLocationById, 3) Convert query string parameters to appropriate types, 4) Call the getQueuesByLocationId operation with the locationId and filters, and 5) Return a formatted response with the queues data. Include proper error handling using the handleApiError utility.

## 4. Add API route definition to main.wasp [done]
### Dependencies: 18.3
### Description: Update the main.wasp file to include the new API endpoint definition
### Details:
Add the API route definition to main.wasp as shown in the template. The route should be defined as 'GET /api/providers/locations/:locationId/queues' and should import the getQueuesByLocation handler from '@server/api/providers/queues'. Set auth to true to ensure the endpoint requires authentication.

## 5. Implement error handling and testing [done]
### Dependencies: 18.3, 18.4
### Description: Add comprehensive error handling and create tests for the API endpoint
### Details:
Ensure the handleApiError utility in '@server/api/common/errors.ts' is properly implemented to handle various error types. Update the getQueuesByLocation handler to use try/catch blocks with this utility. Create test cases for the API endpoint covering: 1) Successful queue retrieval, 2) Location not found, 3) Unauthorized access (provider doesn't own location), 4) Invalid parameters, and 5) Server errors. Use Jest and Supertest for API testing.

