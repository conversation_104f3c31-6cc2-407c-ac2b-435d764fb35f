# Task ID: 16
# Title: Implement Service Category Management APIs
# Status: done
# Dependencies: 2, 3, 4
# Priority: medium
# Description: Create API endpoints to manage service categories for providers.
# Details:
1. Create API handlers for service category CRUD operations
2. Implement endpoints for:
   - GET /api/providers/service-categories
   - POST /api/providers/service-categories
   - PATCH /api/providers/service-categories/:id
   - DELETE /api/providers/service-categories/:id
3. Use authentication middleware to verify provider
4. Implement Zod schemas for validation
5. Call existing Wasp operations for category management
6. Format responses according to API standards
7. Add to main.wasp API definitions

```typescript
// In main.wasp
api {
  httpRoute: GET /api/providers/service-categories,
  fn: import { getServiceCategories } from "@server/api/providers/service-categories",
  auth: true
}

api {
  httpRoute: POST /api/providers/service-categories,
  fn: import { createServiceCategory } from "@server/api/providers/service-categories",
  auth: true
}

api {
  httpRoute: PATCH /api/providers/service-categories/:id,
  fn: import { updateServiceCategory } from "@server/api/providers/service-categories",
  auth: true
}

api {
  httpRoute: DELETE /api/providers/service-categories/:id,
  fn: import { deleteServiceCategory } from "@server/api/providers/service-categories",
  auth: true
}

// In @server/api/providers/service-categories.ts
import { z } from 'zod';
import { 
  getServiceCategoriesByProviderId,
  createProviderServiceCategory,
  updateProviderServiceCategory,
  deleteProviderServiceCategory,
  getServiceCategoryById
} from '../../../provider/operations';
import { validateRequest } from '../../common/validation';
import { handleApiError } from '../../common/errors';

// Get categories
export const getServiceCategories = async (req, res) => {
  try {
    const providerId = req.providerId;
    const categories = await getServiceCategoriesByProviderId(providerId);
    
    return res.status(200).json({
      status: 'success',
      data: categories
    });
  } catch (error) {
    return handleApiError(res, error);
  }
};

// Create category
const createCategorySchema = z.object({
  body: z.object({
    name: z.string().min(1),
    description: z.string().optional(),
    color: z.string().optional(),
    icon: z.string().optional()
  })
});

export const createServiceCategory = [
  validateRequest(createCategorySchema),
  async (req, res) => {
    try {
      const providerId = req.providerId;
      const categoryData = req.body;
      
      const newCategory = await createProviderServiceCategory({
        providerId,
        ...categoryData
      });
      
      return res.status(201).json({
        status: 'success',
        data: newCategory
      });
    } catch (error) {
      return handleApiError(res, error);
    }
  }
];

// Update category
const updateCategorySchema = z.object({
  params: z.object({
    id: z.string().uuid()
  }),
  body: z.object({
    name: z.string().min(1).optional(),
    description: z.string().optional(),
    color: z.string().optional(),
    icon: z.string().optional()
  })
});

export const updateServiceCategory = [
  validateRequest(updateCategorySchema),
  async (req, res) => {
    try {
      const providerId = req.providerId;
      const categoryId = req.params.id;
      const updateData = req.body;
      
      // Verify provider owns this category
      const existingCategory = await getServiceCategoryById(categoryId);
      if (!existingCategory || existingCategory.providerId !== providerId) {
        return res.status(404).json({
          status: 'error',
          message: 'Category not found'
        });
      }
      
      const updatedCategory = await updateProviderServiceCategory({
        id: categoryId,
        ...updateData
      });
      
      return res.status(200).json({
        status: 'success',
        data: updatedCategory
      });
    } catch (error) {
      return handleApiError(res, error);
    }
  }
];

// Delete category
const deleteCategorySchema = z.object({
  params: z.object({
    id: z.string().uuid()
  })
});

export const deleteServiceCategory = [
  validateRequest(deleteCategorySchema),
  async (req, res) => {
    try {
      const providerId = req.providerId;
      const categoryId = req.params.id;
      
      // Verify provider owns this category
      const existingCategory = await getServiceCategoryById(categoryId);
      if (!existingCategory || existingCategory.providerId !== providerId) {
        return res.status(404).json({
          status: 'error',
          message: 'Category not found'
        });
      }
      
      await deleteProviderServiceCategory(categoryId);
      
      return res.status(200).json({
        status: 'success',
        message: 'Category deleted successfully'
      });
    } catch (error) {
      return handleApiError(res, error);
    }
  }
];
```

# Test Strategy:
1. Test all CRUD operations with valid data
2. Test with invalid data to verify validation
3. Test with categories not owned by provider
4. Test with non-existent category IDs
5. Test with authenticated non-provider user
6. Verify categories are correctly created, updated, and deleted in database
7. Test deletion of category with associated services

# Subtasks:
## 1. Define API Routes in main.wasp [done]
### Dependencies: None
### Description: Add the four service category API endpoint definitions to main.wasp file
### Details:
Add the following API route definitions to main.wasp:
1. GET /api/providers/service-categories
2. POST /api/providers/service-categories
3. PATCH /api/providers/service-categories/:id
4. DELETE /api/providers/service-categories/:id

Ensure each route points to the correct handler function that will be implemented in the next steps and has auth: true to enforce authentication.

## 2. Implement Zod Validation Schemas [done]
### Dependencies: 16.1
### Description: Create validation schemas for request validation on all service category endpoints
### Details:
Create the following Zod schemas in the service-categories.ts file:
1. createCategorySchema - validating the request body for category creation
2. updateCategorySchema - validating both params (id) and body for category updates
3. deleteCategorySchema - validating params (id) for category deletion

Ensure proper validation rules for each field (name, description, color, icon) and use UUID validation for IDs.

## 3. Implement GET and POST Endpoints [done]
### Dependencies: 16.2
### Description: Create handler functions for retrieving and creating service categories
### Details:
In the service-categories.ts file:
1. Implement getServiceCategories function to retrieve categories for the authenticated provider
2. Implement createServiceCategory function with validation middleware
3. Use the existing operations (getServiceCategoriesByProviderId, createProviderServiceCategory)
4. Format responses according to API standards with status and data fields
5. Implement proper error handling using handleApiError utility

## 4. Implement PATCH and DELETE Endpoints [done]
### Dependencies: 16.3
### Description: Create handler functions for updating and deleting service categories
### Details:
In the service-categories.ts file:
1. Implement updateServiceCategory function with validation middleware
2. Implement deleteServiceCategory function with validation middleware
3. Add ownership verification to ensure providers can only modify their own categories
4. Use the existing operations (updateProviderServiceCategory, deleteProviderServiceCategory, getServiceCategoryById)
5. Format responses according to API standards
6. Implement proper error handling

## 5. Create Index File and Export Handlers [done]
### Dependencies: 16.4
### Description: Create an index.ts file to export all handlers and ensure proper imports
### Details:
1. Create an index.ts file in the @server/api/providers directory
2. Export all handler functions from service-categories.ts
3. Ensure all imports in service-categories.ts are correct
4. Verify that the path in main.wasp API definitions matches the export structure
5. Add any missing imports for utility functions (validateRequest, handleApiError)
6. Document the API endpoints with JSDoc comments

