# Task ID: 4
# Title: Define API Error Handling Utilities
# Status: done
# Dependencies: 1
# Priority: high
# Description: Create standardized error handling utilities to ensure consistent error responses across all Provider APIs.
# Details:
1. Create utility functions for common error responses
2. Implement Prisma error handling
3. Define error response format
4. Map common errors to appropriate HTTP status codes
5. Create error logging functionality

```typescript
// Example error handling utilities
export const handleApiError = (res, error) => {
  console.error('API Error:', error);
  
  // Handle Prisma errors
  if (error.code && error.code.startsWith('P')) {
    return handlePrismaError(res, error);
  }
  
  // Handle validation errors
  if (error.name === 'ValidationError') {
    return res.status(400).json({
      status: 'error',
      message: 'Validation failed',
      errors: error.errors
    });
  }
  
  // Handle not found errors
  if (error.name === 'NotFoundError') {
    return res.status(404).json({
      status: 'error',
      message: error.message || 'Resource not found'
    });
  }
  
  // Default server error
  return res.status(500).json({
    status: 'error',
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'production' ? undefined : error.message
  });
};

// Handle Prisma specific errors
const handlePrismaError = (res, error) => {
  switch (error.code) {
    case 'P2002': // Unique constraint violation
      return res.status(409).json({
        status: 'error',
        message: 'Resource already exists',
        fields: error.meta?.target
      });
    case 'P2025': // Record not found
      return res.status(404).json({
        status: 'error',
        message: 'Resource not found'
      });
    default:
      return res.status(500).json({
        status: 'error',
        message: 'Database error',
        error: process.env.NODE_ENV === 'production' ? undefined : error.message
      });
  }
};
```

# Test Strategy:
1. Test error handling with various error types
2. Verify Prisma errors are handled correctly
3. Test validation error formatting
4. Verify HTTP status codes are appropriate
5. Test error logging functionality
6. Ensure sensitive error details are not exposed in production

# Subtasks:
## 1. Create Base Error Types and Response Format [done]
### Dependencies: None
### Description: Define TypeScript interfaces for error responses and create base error classes that will be used throughout the application.
### Details:
Create a new file `src/shared/errors/types.ts` to define error interfaces and base classes. Include interfaces for `ApiErrorResponse`, `ValidationErrorDetails`, and custom error classes like `NotFoundError`, `ValidationError`, etc. Define the standard error response format with fields for status, message, errors (for validation), and optional stack trace for development.

## 2. Implement Prisma Error Handler [done]
### Dependencies: 4.1
### Description: Create a dedicated utility to handle Prisma-specific errors and map them to appropriate HTTP status codes and response formats.
### Details:
Create `src/shared/errors/prismaErrorHandler.ts` that maps Prisma error codes to HTTP status codes. Handle common cases like P2002 (unique constraint), P2025 (not found), P2003 (foreign key constraint), etc. Each error should be converted to the standard error response format defined in the previous subtask.

## 3. Develop General API Error Handler [done]
### Dependencies: 4.1, 4.2
### Description: Create the main error handling utility that will process all types of errors and delegate to specialized handlers when appropriate.
### Details:
Create `src/shared/errors/apiErrorHandler.ts` with the main `handleApiError` function that takes a response object and error. Implement logic to detect error types and delegate to appropriate handlers (like the Prisma handler). Include handling for validation errors, not found errors, authorization errors, and generic server errors. Ensure proper error logging is implemented.

## 4. Create Error Logging Functionality [done]
### Dependencies: 4.1
### Description: Implement error logging utilities that can be used across the application to consistently log errors with appropriate context.
### Details:
Create `src/shared/errors/errorLogger.ts` with functions to log errors at different severity levels. Include context information like request ID, user ID (if available), and timestamp. Configure the logger to respect the current environment (development/production) and implement appropriate redaction of sensitive information in production.

## 5. Integrate Error Handlers with Wasp Routes [done]
### Dependencies: 4.1, 4.2, 4.3, 4.4
### Description: Create middleware or utility functions to integrate the error handling system with Wasp route handlers.
### Details:
Create `src/server/middleware/errorHandling.ts` with middleware functions that can wrap route handlers to provide consistent error handling. Implement a `withErrorHandling` higher-order function that can wrap any route handler and apply the error handling utilities. Update example route handlers to use this middleware. Document usage patterns for the team.

