# Task ID: 23
# Title: Implement Customer Management APIs
# Status: done
# Dependencies: 2, 3, 4
# Priority: medium
# Description: Create API endpoints to manage provider's customers.
# Details:
1. Create API handlers for customer management operations
2. Implement endpoints for:
   - GET /api/providers/customers
   - GET /api/providers/customers/:id
   - POST /api/providers/customers
   - PATCH /api/providers/customers/:id
3. Use authentication middleware to verify provider
4. Implement Zod schemas for validation
5. Call existing Wasp operations for customer management
6. Format responses according to API standards
7. Add to main.wasp API definitions

```typescript
// In main.wasp
api {
  httpRoute: GET /api/providers/customers,
  fn: import { getProviderCustomers } from "@server/api/providers/customers",
  auth: true
}

api {
  httpRoute: GET /api/providers/customers/:id,
  fn: import { getProviderCustomer } from "@server/api/providers/customers",
  auth: true
}

api {
  httpRoute: POST /api/providers/customers,
  fn: import { createCustomer } from "@server/api/providers/customers",
  auth: true
}

api {
  httpRoute: PATCH /api/providers/customers/:id,
  fn: import { updateCustomer } from "@server/api/providers/customers",
  auth: true
}

// In @server/api/providers/customers.ts
import { z } from 'zod';
import { 
  getProviderCustomers as getCustomersOp,
  getCustomerById,
  createProviderCustomer,
  updateProviderCustomer
} from '../../../provider/operations';
import { validateRequest } from '../../common/validation';
import { handleApiError } from '../../common/errors';

// Get customers
const getCustomersSchema = z.object({
  query: z.object({
    search: z.string().optional(),
    page: z.string().transform(val => parseInt(val, 10)).optional(),
    limit: z.string().transform(val => parseInt(val, 10)).optional(),
    sortBy: z.enum(['name', 'email', 'createdAt']).optional(),
    sortOrder: z.enum(['asc', 'desc']).optional()
  })
});

export const getProviderCustomers = [
  validateRequest(getCustomersSchema),
  async (req, res) => {
    try {
      const providerId = req.providerId;
      const { search, page = 1, limit = 20, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
      
      const filters = {
        search: search || undefined,
        pagination: { page, limit },
        sort: { field: sortBy, order: sortOrder }
      };
      
      const result = await getCustomersOp(providerId, filters);
      
      return res.status(200).json({
        status: 'success',
        data: result.customers,
        pagination: {
          total: result.total,
          page,
          limit,
          pages: Math.ceil(result.total / limit)
        }
      });
    } catch (error) {
      return handleApiError(res, error);
    }
  }
];

// Get single customer
const getCustomerSchema = z.object({
  params: z.object({
    id: z.string().uuid()
  })
});

export const getProviderCustomer = [
  validateRequest(getCustomerSchema),
  async (req, res) => {
    try {
      const providerId = req.providerId;
      const customerId = req.params.id;
      
      const customer = await getCustomerById(customerId);
      
      // Verify provider has relationship with this customer
      if (!customer || customer.providerId !== providerId) {
        return res.status(404).json({
          status: 'error',
          message: 'Customer not found'
        });
      }
      
      return res.status(200).json({
        status: 'success',
        data: customer
      });
    } catch (error) {
      return handleApiError(res, error);
    }
  }
];

// Create customer
const createCustomerSchema = z.object({
  body: z.object({
    name: z.string().min(1),
    email: z.string().email().optional(),
    phone: z.string().optional(),
    notes: z.string().optional(),
    customFields: z.record(z.string()).optional()
  })
});

export const createCustomer = [
  validateRequest(createCustomerSchema),
  async (req, res) => {
    try {
      const providerId = req.providerId;
      const customerData = req.body;
      
      const newCustomer = await createProviderCustomer({
        providerId,
        ...customerData
      });
      
      return res.status(201).json({
        status: 'success',
        data: newCustomer
      });
    } catch (error) {
      return handleApiError(res, error);
    }
  }
];

// Update customer
const updateCustomerSchema = z.object({
  params: z.object({
    id: z.string().uuid()
  }),
  body: z.object({
    name: z.string().min(1).optional(),
    email: z.string().email().optional(),
    phone: z.string().optional(),
    notes: z.string().optional(),
    customFields: z.record(z.string()).optional()
  })
});

export const updateCustomer = [
  validateRequest(updateCustomerSchema),
  async (req, res) => {
    try {
      const providerId = req.providerId;
      const customerId = req.params.id;
      const updateData = req.body;
      
      // Verify provider has relationship with this customer
      const existingCustomer = await getCustomerById(customerId);
      if (!existingCustomer || existingCustomer.providerId !== providerId) {
        return res.status(404).json({
          status: 'error',
          message: 'Customer not found'
        });
      }
      
      const updatedCustomer = await updateProviderCustomer({
        id: customerId,
        ...updateData
      });
      
      return res.status(200).json({
        status: 'success',
        data: updatedCustomer
      });
    } catch (error) {
      return handleApiError(res, error);
    }
  }
];
```

# Test Strategy:
1. Test all operations with valid data
2. Test with invalid data to verify validation
3. Test with customer not associated with provider
4. Test with non-existent customer IDs
5. Test with authenticated non-provider user
6. Verify customers are correctly created and updated in database
7. Test search functionality
8. Test pagination and sorting
9. Test custom fields handling

# Subtasks:
## 1. Implement API route definitions in main.wasp [done]
### Dependencies: None
### Description: Add the customer management API route definitions to the main.wasp file to register the endpoints with the Wasp framework.
### Details:
Add the four API route definitions to main.wasp: GET /api/providers/customers, GET /api/providers/customers/:id, POST /api/providers/customers, and PATCH /api/providers/customers/:id. Each definition should specify the correct HTTP method, route path, handler function import path, and authentication requirement (auth: true).

## 2. Create validation schemas and common utilities [done]
### Dependencies: 23.1
### Description: Implement the Zod validation schemas for request validation and set up common error handling utilities.
### Details:
Create the validation schemas for each API endpoint using Zod. Implement the getCustomersSchema, getCustomerSchema, createCustomerSchema, and updateCustomerSchema. Ensure proper validation for query parameters, URL parameters, and request bodies. Set up or reuse the validateRequest middleware and handleApiError utility functions.

## 3. Implement GET endpoints for customer listing and retrieval [done]
### Dependencies: 23.2
### Description: Create the handler functions for retrieving all customers and getting a single customer by ID.
### Details:
Implement the getProviderCustomers and getProviderCustomer handler functions in @server/api/providers/customers.ts. Both should use the validation middleware, extract the providerId from the authenticated request, call the appropriate operations (getCustomersOp or getCustomerById), and format the responses according to API standards. Include pagination, sorting, and filtering for the list endpoint, and proper 404 handling for the single customer endpoint.

## 4. Implement POST endpoint for customer creation [done]
### Dependencies: 23.2
### Description: Create the handler function for creating new customers.
### Details:
Implement the createCustomer handler function in @server/api/providers/customers.ts. Use the validation middleware to validate the request body, extract the providerId from the authenticated request, call the createProviderCustomer operation with the combined data, and return a 201 response with the newly created customer data.

## 5. Implement PATCH endpoint for customer updates [done]
### Dependencies: 23.3, 23.4
### Description: Create the handler function for updating existing customers.
### Details:
Implement the updateCustomer handler function in @server/api/providers/customers.ts. Use the validation middleware to validate the request parameters and body, verify the customer exists and belongs to the authenticated provider, call the updateProviderCustomer operation with the update data, and return the updated customer. Include proper error handling for cases where the customer doesn't exist or doesn't belong to the provider.

