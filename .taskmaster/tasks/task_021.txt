# Task ID: 21
# Title: Implement Queue Service Assignment APIs
# Status: done
# Dependencies: 2, 3, 4, 12, 17
# Priority: medium
# Description: Create API endpoints to manage service assignments to queues/resources.
# Details:
1. Create API handlers for queue service assignment operations
2. Implement endpoints for:
   - GET /api/providers/queues/:id/services
   - POST /api/providers/queues/:id/services
   - DELETE /api/providers/queues/:id/services/:serviceId
3. Use authentication middleware to verify provider
4. Implement Zod schemas for validation
5. Call existing Wasp operations for queue service management
6. Format responses according to API standards
7. Add to main.wasp API definitions

```typescript
// In main.wasp
api {
  httpRoute: GET /api/providers/queues/:id/services,
  fn: import { getQueueServices } from "@server/api/providers/queue-services",
  auth: true
}

api {
  httpRoute: POST /api/providers/queues/:id/services,
  fn: import { assignServiceToQueue } from "@server/api/providers/queue-services",
  auth: true
}

api {
  httpRoute: DELETE /api/providers/queues/:id/services/:serviceId,
  fn: import { removeServiceFromQueue } from "@server/api/providers/queue-services",
  auth: true
}

// In @server/api/providers/queue-services.ts
import { z } from 'zod';
import { 
  getQueueById, 
  getQueueServices as getQueueServicesOp,
  assignServiceToQueue as assignServiceOp,
  removeServiceFromQueue as removeServiceOp
} from '../../../queue/operations';
import { getServiceById } from '../../../provider/operations';
import { validateRequest } from '../../common/validation';
import { handleApiError } from '../../common/errors';

// Get queue services
const getQueueServicesSchema = z.object({
  params: z.object({
    id: z.string().uuid()
  })
});

export const getQueueServices = [
  validateRequest(getQueueServicesSchema),
  async (req, res) => {
    try {
      const providerId = req.providerId;
      const queueId = req.params.id;
      
      // Verify provider owns this queue
      const existingQueue = await getQueueById(queueId);
      if (!existingQueue || existingQueue.providerId !== providerId) {
        return res.status(404).json({
          status: 'error',
          message: 'Queue not found'
        });
      }
      
      const services = await getQueueServicesOp(queueId);
      
      return res.status(200).json({
        status: 'success',
        data: services
      });
    } catch (error) {
      return handleApiError(res, error);
    }
  }
];

// Assign service to queue
const assignServiceSchema = z.object({
  params: z.object({
    id: z.string().uuid()
  }),
  body: z.object({
    serviceId: z.string().uuid()
  })
});

export const assignServiceToQueue = [
  validateRequest(assignServiceSchema),
  async (req, res) => {
    try {
      const providerId = req.providerId;
      const queueId = req.params.id;
      const { serviceId } = req.body;
      
      // Verify provider owns this queue
      const existingQueue = await getQueueById(queueId);
      if (!existingQueue || existingQueue.providerId !== providerId) {
        return res.status(404).json({
          status: 'error',
          message: 'Queue not found'
        });
      }
      
      // Verify provider owns this service
      const existingService = await getServiceById(serviceId);
      if (!existingService || existingService.providerId !== providerId) {
        return res.status(404).json({
          status: 'error',
          message: 'Service not found'
        });
      }
      
      await assignServiceOp(queueId, serviceId);
      
      return res.status(200).json({
        status: 'success',
        message: 'Service assigned to queue successfully'
      });
    } catch (error) {
      return handleApiError(res, error);
    }
  }
];

// Remove service from queue
const removeServiceSchema = z.object({
  params: z.object({
    id: z.string().uuid(),
    serviceId: z.string().uuid()
  })
});

export const removeServiceFromQueue = [
  validateRequest(removeServiceSchema),
  async (req, res) => {
    try {
      const providerId = req.providerId;
      const queueId = req.params.id;
      const serviceId = req.params.serviceId;
      
      // Verify provider owns this queue
      const existingQueue = await getQueueById(queueId);
      if (!existingQueue || existingQueue.providerId !== providerId) {
        return res.status(404).json({
          status: 'error',
          message: 'Queue not found'
        });
      }
      
      await removeServiceOp(queueId, serviceId);
      
      return res.status(200).json({
        status: 'success',
        message: 'Service removed from queue successfully'
      });
    } catch (error) {
      return handleApiError(res, error);
    }
  }
];
```

# Test Strategy:
1. Test all operations with valid data
2. Test with invalid data to verify validation
3. Test with queue not owned by provider
4. Test with service not owned by provider
5. Test with non-existent queue or service IDs
6. Test with authenticated non-provider user
7. Verify services are correctly assigned to and removed from queues in database
8. Test assigning a service that's already assigned
9. Test removing a service that's not assigned

# Subtasks:
## 1. Create API route definitions in main.wasp [done]
### Dependencies: None
### Description: Add the three API route definitions to main.wasp for queue service management: GET, POST, and DELETE endpoints.
### Details:
Add the following API route definitions to main.wasp:
1. GET /api/providers/queues/:id/services
2. POST /api/providers/queues/:id/services
3. DELETE /api/providers/queues/:id/services/:serviceId

Ensure each route has the correct HTTP method, path, handler function import path, and auth requirement set to true.

## 2. Implement Zod validation schemas [done]
### Dependencies: 21.1
### Description: Create the Zod validation schemas for request validation in the queue services API handlers.
### Details:
Define three Zod schemas in @server/api/providers/queue-services.ts:
1. getQueueServicesSchema - validate the queue ID in params
2. assignServiceSchema - validate queue ID in params and service ID in body
3. removeServiceSchema - validate queue ID and service ID in params

Ensure all IDs are validated as UUID strings.

## 3. Implement GET queue services endpoint handler [done]
### Dependencies: 21.2
### Description: Create the handler function for retrieving services assigned to a queue.
### Details:
Implement the getQueueServices handler in @server/api/providers/queue-services.ts that:
1. Uses the validation middleware with getQueueServicesSchema
2. Extracts providerId from the request and queueId from params
3. Verifies the provider owns the queue
4. Calls the getQueueServicesOp operation
5. Returns a formatted success response with the services data
6. Handles errors with the handleApiError utility

## 4. Implement POST service assignment endpoint handler [done]
### Dependencies: 21.2
### Description: Create the handler function for assigning a service to a queue.
### Details:
Implement the assignServiceToQueue handler in @server/api/providers/queue-services.ts that:
1. Uses the validation middleware with assignServiceSchema
2. Extracts providerId from the request, queueId from params, and serviceId from body
3. Verifies the provider owns both the queue and the service
4. Calls the assignServiceOp operation
5. Returns a formatted success response
6. Handles errors with the handleApiError utility

## 5. Implement DELETE service removal endpoint handler [done]
### Dependencies: 21.2
### Description: Create the handler function for removing a service from a queue.
### Details:
Implement the removeServiceFromQueue handler in @server/api/providers/queue-services.ts that:
1. Uses the validation middleware with removeServiceSchema
2. Extracts providerId from the request, queueId and serviceId from params
3. Verifies the provider owns the queue
4. Calls the removeServiceOp operation
5. Returns a formatted success response
6. Handles errors with the handleApiError utility

