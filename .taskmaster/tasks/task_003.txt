# Task ID: 3
# Title: Create Validation Utilities Using Zod Schemas
# Status: done
# Dependencies: 1
# Priority: high
# Description: Implement validation utilities using Zod schemas to validate API request inputs across all Provider endpoints.
# Details:
1. Create a validation middleware using Zod
2. Define common validation schemas for reuse
3. Implement error formatting for validation failures
4. Create utility functions for common validation patterns

```typescript
// Example validation middleware
import { z } from 'zod';

export const validateRequest = (schema) => (req, res, next) => {
  try {
    schema.parse({
      body: req.body,
      query: req.query,
      params: req.params
    });
    next();
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        status: 'error',
        message: 'Invalid request data',
        errors: error.errors.map(e => ({
          path: e.path.join('.'),
          message: e.message
        }))
      });
    }
    return res.status(500).json({ message: 'Internal validation error' });
  }
};

// Example common schemas
export const idSchema = z.object({
  params: z.object({
    id: z.string().uuid()
  })
});
```

# Test Strategy:
1. Test validation middleware with valid inputs
2. Test validation middleware with invalid inputs
3. Verify error responses match expected format
4. Test common validation schemas with edge cases
5. Verify validation works with different HTTP methods

# Subtasks:
## 1. Create Base Validation Middleware [done]
### Dependencies: None
### Description: Implement the core validation middleware function that will be used across all Provider endpoints to validate request data using Zod schemas.
### Details:
Create a new file at `src/utils/validation.ts` that exports a `validateRequest` middleware function. This function should accept a Zod schema and return an Express middleware that validates request data (body, query, params) against the schema. On validation failure, it should return a properly formatted 400 response with detailed error information. On success, it should call next() to continue request processing.

## 2. Define Common Validation Schemas [done]
### Dependencies: 3.1
### Description: Create a library of reusable Zod schemas for common validation patterns used across Provider endpoints.
### Details:
Create a new file at `src/utils/validationSchemas.ts` that exports common validation schemas including: idSchema (for UUID validation), paginationSchema (for limit/offset), dateRangeSchema, and other patterns specific to your Provider entities. Each schema should be well-documented with JSDoc comments explaining its purpose and usage. Import the Zod library and define type-safe schemas that can be composed together for different API endpoints.

## 3. Implement Error Formatting Utilities [done]
### Dependencies: 3.1
### Description: Create utility functions to standardize error formatting for validation failures across the application.
### Details:
Extend the validation.ts file to include dedicated error formatting functions. Create a `formatZodError` function that transforms Zod validation errors into a consistent, user-friendly format. Also implement a `createValidationErrorResponse` function that generates standardized API error responses. These utilities should ensure consistent error handling across all endpoints and make error messages clear and actionable for API consumers.

## 4. Create Specialized Validation Utilities [done]
### Dependencies: 3.2
### Description: Implement specialized validation utility functions for common validation patterns specific to the Provider domain.
### Details:
Create a new file at `src/utils/domainValidation.ts` that implements domain-specific validation utilities. Include functions like `validateProviderFields`, `validateServiceAvailability`, and other domain-specific validations. These utilities should leverage the base Zod schemas but add business logic validation specific to your application domain. Each utility should be strongly typed using TypeScript and follow the Wasp framework patterns.

## 5. Integrate Validation with API Routes [done]
### Dependencies: 3.1, 3.2, 3.3, 3.4
### Description: Apply the validation middleware to all Provider API endpoints in the application.
### Details:
Update all API route handlers to use the validation middleware. For each endpoint in your Provider API, identify the appropriate validation schema (using the common schemas where possible) and apply the validateRequest middleware. This includes updating files in the src/actions/ and src/queries/ directories to incorporate validation. Create examples and documentation for the team on how to properly use the validation utilities in new endpoints. Ensure all existing endpoints are protected with appropriate validation.

