# Task ID: 24
# Title: Implement Appointment Management APIs
# Status: done
# Dependencies: 2, 3, 4, 12, 17
# Priority: high
# Description: Create API endpoints to manage provider appointments.
# Details:
1. Create API handlers for appointment management operations
2. Implement endpoints for:
   - GET /api/providers/appointments
   - GET /api/providers/appointments/:id
   - POST /api/providers/appointments
   - PATCH /api/providers/appointments/:id
   - PATCH /api/providers/appointments/:id/status
3. Use authentication middleware to verify provider
4. Implement Zod schemas for validation
5. Call existing Wasp operations for appointment management
6. Format responses according to API standards
7. Add to main.wasp API definitions

```typescript
// In main.wasp
api {
  httpRoute: GET /api/providers/appointments,
  fn: import { getProviderAppointments } from "@server/api/providers/appointments",
  auth: true
}

api {
  httpRoute: GET /api/providers/appointments/:id,
  fn: import { getProviderAppointment } from "@server/api/providers/appointments",
  auth: true
}

api {
  httpRoute: POST /api/providers/appointments,
  fn: import { createAppointment } from "@server/api/providers/appointments",
  auth: true
}

api {
  httpRoute: PATCH /api/providers/appointments/:id,
  fn: import { updateAppointment } from "@server/api/providers/appointments",
  auth: true
}

api {
  httpRoute: PATCH /api/providers/appointments/:id/status,
  fn: import { updateAppointmentStatus } from "@server/api/providers/appointments",
  auth: true
}

// In @server/api/providers/appointments.ts
import { z } from 'zod';
import { 
  getProviderAppointments as getAppointmentsOp,
  getAppointmentById,
  createProviderAppointment,
  updateProviderAppointment,
  updateAppointmentStatus as updateStatusOp
} from '../../../provider/operations';
import { validateRequest } from '../../common/validation';
import { handleApiError } from '../../common/errors';

// Get appointments
const getAppointmentsSchema = z.object({
  query: z.object({
    from: z.string().optional(), // ISO date string
    to: z.string().optional(), // ISO date string
    status: z.string().optional(),
    queueId: z.string().uuid().optional(),
    locationId: z.string().uuid().optional(),
    serviceId: z.string().uuid().optional(),
    customerId: z.string().uuid().optional(),
    page: z.string().transform(val => parseInt(val, 10)).optional(),
    limit: z.string().transform(val => parseInt(val, 10)).optional()
  })
});

export const getProviderAppointments = [
  validateRequest(getAppointmentsSchema),
  async (req, res) => {
    try {
      const providerId = req.providerId;
      const { 
        from, to, status, queueId, locationId, serviceId, customerId,
        page = 1, limit = 20 
      } = req.query;
      
      const filters = {
        from: from ? new Date(from) : undefined,
        to: to ? new Date(to) : undefined,
        status: status || undefined,
        queueId: queueId || undefined,
        locationId: locationId || undefined,
        serviceId: serviceId || undefined,
        customerId: customerId || undefined,
        pagination: { page, limit }
      };
      
      const result = await getAppointmentsOp(providerId, filters);
      
      return res.status(200).json({
        status: 'success',
        data: result.appointments,
        pagination: {
          total: result.total,
          page,
          limit,
          pages: Math.ceil(result.total / limit)
        }
      });
    } catch (error) {
      return handleApiError(res, error);
    }
  }
];

// Get single appointment
const getAppointmentSchema = z.object({
  params: z.object({
    id: z.string().uuid()
  })
});

export const getProviderAppointment = [
  validateRequest(getAppointmentSchema),
  async (req, res) => {
    try {
      const providerId = req.providerId;
      const appointmentId = req.params.id;
      
      const appointment = await getAppointmentById(appointmentId);
      
      // Verify provider owns this appointment
      if (!appointment || appointment.providerId !== providerId) {
        return res.status(404).json({
          status: 'error',
          message: 'Appointment not found'
        });
      }
      
      return res.status(200).json({
        status: 'success',
        data: appointment
      });
    } catch (error) {
      return handleApiError(res, error);
    }
  }
];

// Create appointment
const createAppointmentSchema = z.object({
  body: z.object({
    customerId: z.string().uuid(),
    serviceId: z.string().uuid(),
    queueId: z.string().uuid(),
    startTime: z.string(), // ISO date string
    endTime: z.string().optional(), // ISO date string
    notes: z.string().optional(),
    status: z.string().optional().default('confirmed')
  })
});

export const createAppointment = [
  validateRequest(createAppointmentSchema),
  async (req, res) => {
    try {
      const providerId = req.providerId;
      const appointmentData = req.body;
      
      // Convert string dates to Date objects
      appointmentData.startTime = new Date(appointmentData.startTime);
      if (appointmentData.endTime) {
        appointmentData.endTime = new Date(appointmentData.endTime);
      }
      
      const newAppointment = await createProviderAppointment({
        providerId,
        ...appointmentData
      });
      
      return res.status(201).json({
        status: 'success',
        data: newAppointment
      });
    } catch (error) {
      return handleApiError(res, error);
    }
  }
];

// Update appointment
const updateAppointmentSchema = z.object({
  params: z.object({
    id: z.string().uuid()
  }),
  body: z.object({
    serviceId: z.string().uuid().optional(),
    queueId: z.string().uuid().optional(),
    startTime: z.string().optional(), // ISO date string
    endTime: z.string().optional(), // ISO date string
    notes: z.string().optional()
  })
});

export const updateAppointment = [
  validateRequest(updateAppointmentSchema),
  async (req, res) => {
    try {
      const providerId = req.providerId;
      const appointmentId = req.params.id;
      const updateData = req.body;
      
      // Verify provider owns this appointment
      const existingAppointment = await getAppointmentById(appointmentId);
      if (!existingAppointment || existingAppointment.providerId !== providerId) {
        return res.status(404).json({
          status: 'error',
          message: 'Appointment not found'
        });
      }
      
      // Convert string dates to Date objects if provided
      if (updateData.startTime) {
        updateData.startTime = new Date(updateData.startTime);
      }
      if (updateData.endTime) {
        updateData.endTime = new Date(updateData.endTime);
      }
      
      const updatedAppointment = await updateProviderAppointment({
        id: appointmentId,
        ...updateData
      });
      
      return res.status(200).json({
        status: 'success',
        data: updatedAppointment
      });
    } catch (error) {
      return handleApiError(res, error);
    }
  }
];

// Update appointment status
const updateStatusSchema = z.object({
  params: z.object({
    id: z.string().uuid()
  }),
  body: z.object({
    status: z.enum(['confirmed', 'completed', 'cancelled', 'no-show']),
    notes: z.string().optional()
  })
});

export const updateAppointmentStatus = [
  validateRequest(updateStatusSchema),
  async (req, res) => {
    try {
      const providerId = req.providerId;
      const appointmentId = req.params.id;
      const { status, notes } = req.body;
      
      // Verify provider owns this appointment
      const existingAppointment = await getAppointmentById(appointmentId);
      if (!existingAppointment || existingAppointment.providerId !== providerId) {
        return res.status(404).json({
          status: 'error',
          message: 'Appointment not found'
        });
      }
      
      const updatedAppointment = await updateStatusOp({
        id: appointmentId,
        status,
        notes
      });
      
      return res.status(200).json({
        status: 'success',
        data: updatedAppointment
      });
    } catch (error) {
      return handleApiError(res, error);
    }
  }
];
```

# Test Strategy:
1. Test all operations with valid data
2. Test with invalid data to verify validation
3. Test with appointment not owned by provider
4. Test with non-existent appointment IDs
5. Test with authenticated non-provider user
6. Verify appointments are correctly created, updated, and status changes in database
7. Test date handling and time zone considerations
8. Test filtering appointments by various criteria
9. Test pagination
10. Test status transitions (e.g., can't change from completed to confirmed)

# Subtasks:
## 1. Define API routes in main.wasp [done]
### Dependencies: None
### Description: Add the appointment management API endpoint definitions to main.wasp file to register the routes with the Wasp framework.
### Details:
Add the five API endpoint definitions to main.wasp: GET /api/providers/appointments, GET /api/providers/appointments/:id, POST /api/providers/appointments, PATCH /api/providers/appointments/:id, and PATCH /api/providers/appointments/:id/status. Each definition should specify the HTTP method, route path, handler function import path, and auth requirement (set to true for all endpoints).

## 2. Implement GET appointment endpoints [done]
### Dependencies: 24.1
### Description: Create the handler functions for retrieving appointments, including the list endpoint and single appointment endpoint.
### Details:
Create the @server/api/providers/appointments.ts file and implement the getProviderAppointments and getProviderAppointment handler functions. Include Zod schemas for request validation, proper error handling with handleApiError, and calls to the existing operations. For getProviderAppointments, implement filtering by date range, status, and other parameters, with pagination support. For getProviderAppointment, verify the provider has access to the requested appointment.

## 3. Implement POST appointment creation endpoint [done]
### Dependencies: 24.2
### Description: Create the handler function for creating new appointments.
### Details:
In the same file, implement the createAppointment handler function with Zod validation for the request body. The schema should validate customerId, serviceId, queueId, startTime, and optional fields like endTime, notes, and status. Convert string date representations to Date objects before calling the createProviderAppointment operation. Return a 201 status code with the newly created appointment data on success.

## 4. Implement PATCH appointment update endpoint [done]
### Dependencies: 24.3
### Description: Create the handler function for updating existing appointment details.
### Details:
Implement the updateAppointment handler function with Zod validation for both path parameters and request body. Verify the provider owns the appointment before allowing updates. The update schema should make all fields optional (serviceId, queueId, startTime, endTime, notes). Convert any provided date strings to Date objects before calling the updateProviderAppointment operation.

## 5. Implement PATCH appointment status endpoint [done]
### Dependencies: 24.4
### Description: Create the handler function for updating just the status of an appointment.
### Details:
Implement the updateAppointmentStatus handler function with Zod validation. The status schema should restrict values to the enum: 'confirmed', 'completed', 'cancelled', 'no-show'. Verify provider ownership before allowing status updates. Include optional notes field that can be updated along with the status. Call the updateStatusOp operation with the validated data.

