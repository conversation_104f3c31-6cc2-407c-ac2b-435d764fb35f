# Task ID: 25
# Title: Implement Reschedule Management APIs
# Status: done
# Dependencies: 2, 3, 4, 24
# Priority: medium
# Description: Create API endpoints to manage appointment reschedule requests.
# Details:
1. Create API handlers for reschedule management operations
2. Implement endpoints for:
   - GET /api/providers/reschedules
   - GET /api/providers/reschedules/:id
   - POST /api/providers/appointments/:id/reschedule
   - PATCH /api/providers/reschedules/:id/respond
3. Use authentication middleware to verify provider
4. Implement Zod schemas for validation
5. Call existing Wasp operations for reschedule management
6. Format responses according to API standards
7. Add to main.wasp API definitions

```typescript
// In main.wasp
api {
  httpRoute: GET /api/providers/reschedules,
  fn: import { getProviderReschedules } from "@server/api/providers/reschedules",
  auth: true
}

api {
  httpRoute: GET /api/providers/reschedules/:id,
  fn: import { getProviderReschedule } from "@server/api/providers/reschedules",
  auth: true
}

api {
  httpRoute: POST /api/providers/appointments/:id/reschedule,
  fn: import { createRescheduleRequest } from "@server/api/providers/reschedules",
  auth: true
}

api {
  httpRoute: PATCH /api/providers/reschedules/:id/respond,
  fn: import { respondToReschedule } from "@server/api/providers/reschedules",
  auth: true
}

// In @server/api/providers/reschedules.ts
import { z } from 'zod';
import { 
  getProviderRescheduleRequests,
  getRescheduleRequestById,
  createProviderRescheduleRequest,
  respondToRescheduleRequest,
  getAppointmentById
} from '../../../provider/operations';
import { validateRequest } from '../../common/validation';
import { handleApiError } from '../../common/errors';

// Get reschedule requests
const getReschedulesSchema = z.object({
  query: z.object({
    status: z.enum(['pending', 'approved', 'rejected', 'all']).optional().default('pending'),
    from: z.string().optional(), // ISO date string
    to: z.string().optional(), // ISO date string
    page: z.string().transform(val => parseInt(val, 10)).optional(),
    limit: z.string().transform(val => parseInt(val, 10)).optional()
  })
});

export const getProviderReschedules = [
  validateRequest(getReschedulesSchema),
  async (req, res) => {
    try {
      const providerId = req.providerId;
      const { status, from, to, page = 1, limit = 20 } = req.query;
      
      const filters = {
        status: status === 'all' ? undefined : status,
        from: from ? new Date(from) : undefined,
        to: to ? new Date(to) : undefined,
        pagination: { page, limit }
      };
      
      const result = await getProviderRescheduleRequests(providerId, filters);
      
      return res.status(200).json({
        status: 'success',
        data: result.reschedules,
        pagination: {
          total: result.total,
          page,
          limit,
          pages: Math.ceil(result.total / limit)
        }
      });
    } catch (error) {
      return handleApiError(res, error);
    }
  }
];

// Get single reschedule request
const getRescheduleSchema = z.object({
  params: z.object({
    id: z.string().uuid()
  })
});

export const getProviderReschedule = [
  validateRequest(getRescheduleSchema),
  async (req, res) => {
    try {
      const providerId = req.providerId;
      const rescheduleId = req.params.id;
      
      const reschedule = await getRescheduleRequestById(rescheduleId);
      
      // Verify provider owns this reschedule request
      if (!reschedule || reschedule.providerId !== providerId) {
        return res.status(404).json({
          status: 'error',
          message: 'Reschedule request not found'
        });
      }
      
      return res.status(200).json({
        status: 'success',
        data: reschedule
      });
    } catch (error) {
      return handleApiError(res, error);
    }
  }
];

// Create reschedule request
const createRescheduleSchema = z.object({
  params: z.object({
    id: z.string().uuid() // appointment ID
  }),
  body: z.object({
    newStartTime: z.string(), // ISO date string
    reason: z.string().optional(),
    notifyCustomer: z.boolean().optional().default(true)
  })
});

export const createRescheduleRequest = [
  validateRequest(createRescheduleSchema),
  async (req, res) => {
    try {
      const providerId = req.providerId;
      const appointmentId = req.params.id;
      const { newStartTime, reason, notifyCustomer } = req.body;
      
      // Verify provider owns this appointment
      const appointment = await getAppointmentById(appointmentId);
      if (!appointment || appointment.providerId !== providerId) {
        return res.status(404).json({
          status: 'error',
          message: 'Appointment not found'
        });
      }
      
      const rescheduleRequest = await createProviderRescheduleRequest({
        appointmentId,
        providerId,
        newStartTime: new Date(newStartTime),
        reason,
        notifyCustomer
      });
      
      return res.status(201).json({
        status: 'success',
        data: rescheduleRequest
      });
    } catch (error) {
      return handleApiError(res, error);
    }
  }
];

// Respond to reschedule request
const respondRescheduleSchema = z.object({
  params: z.object({
    id: z.string().uuid() // reschedule ID
  }),
  body: z.object({
    response: z.enum(['approve', 'reject']),
    notes: z.string().optional(),
    notifyCustomer: z.boolean().optional().default(true)
  })
});

export const respondToReschedule = [
  validateRequest(respondRescheduleSchema),
  async (req, res) => {
    try {
      const providerId = req.providerId;
      const rescheduleId = req.params.id;
      const { response, notes, notifyCustomer } = req.body;
      
      // Verify provider owns this reschedule request
      const reschedule = await getRescheduleRequestById(rescheduleId);
      if (!reschedule || reschedule.providerId !== providerId) {
        return res.status(404).json({
          status: 'error',
          message: 'Reschedule request not found'
        });
      }
      
      // Verify reschedule is still pending
      if (reschedule.status !== 'pending') {
        return res.status(400).json({
          status: 'error',
          message: 'Cannot respond to a reschedule request that is not pending'
        });
      }
      
      const updatedReschedule = await respondToRescheduleRequest({
        id: rescheduleId,
        response: response === 'approve' ? 'approved' : 'rejected',
        notes,
        notifyCustomer
      });
      
      return res.status(200).json({
        status: 'success',
        data: updatedReschedule
      });
    } catch (error) {
      return handleApiError(res, error);
    }
  }
];
```

# Test Strategy:
1. Test all operations with valid data
2. Test with invalid data to verify validation
3. Test with reschedule requests not owned by provider
4. Test with non-existent reschedule or appointment IDs
5. Test with authenticated non-provider user
6. Verify reschedule requests are correctly created and responded to in database
7. Test date handling and time zone considerations
8. Test filtering reschedule requests by status and date range
9. Test pagination
10. Test notification flag behavior
11. Test responding to already responded reschedule requests

# Subtasks:
## 1. Implement API Definitions in main.wasp [done]
### Dependencies: None
### Description: Add the API route definitions to main.wasp for all four reschedule management endpoints.
### Details:
Add the following API definitions to main.wasp:
1. GET /api/providers/reschedules
2. GET /api/providers/reschedules/:id
3. POST /api/providers/appointments/:id/reschedule
4. PATCH /api/providers/reschedules/:id/respond

Ensure each definition imports the correct handler function from @server/api/providers/reschedules.ts and has auth: true set to require authentication.

## 2. Create Zod Validation Schemas [done]
### Dependencies: 25.1
### Description: Implement the Zod validation schemas for all reschedule management API endpoints.
### Details:
Create the following validation schemas in @server/api/providers/reschedules.ts:
1. getReschedulesSchema - for filtering and pagination
2. getRescheduleSchema - for validating the reschedule ID parameter
3. createRescheduleSchema - for validating appointment ID and new schedule details
4. respondRescheduleSchema - for validating reschedule response

Ensure proper type transformations for numeric values and validation for date strings, UUIDs, and enum values.

## 3. Implement GET Reschedule Endpoints [done]
### Dependencies: 25.2
### Description: Implement the handler functions for retrieving reschedule requests.
### Details:
In @server/api/providers/reschedules.ts, implement:
1. getProviderReschedules - List reschedule requests with filtering and pagination
2. getProviderReschedule - Get a single reschedule request by ID

Both functions should:
- Extract providerId from the authenticated request
- Use the validation middleware with the appropriate schema
- Call the corresponding operations (getProviderRescheduleRequests, getRescheduleRequestById)
- Verify the provider has access to the requested data
- Format responses according to API standards with status and data fields
- Use handleApiError for error handling

## 4. Implement Create Reschedule Request Endpoint [done]
### Dependencies: 25.2
### Description: Implement the handler function for creating a new reschedule request.
### Details:
In @server/api/providers/reschedules.ts, implement createRescheduleRequest handler:
1. Extract providerId from the authenticated request
2. Validate the appointment ID parameter and request body
3. Verify the provider owns the appointment by calling getAppointmentById
4. Call createProviderRescheduleRequest operation with validated data
5. Return a 201 status with the created reschedule request
6. Handle errors with handleApiError

Ensure proper date handling by converting string dates to Date objects.

## 5. Implement Respond to Reschedule Endpoint [done]
### Dependencies: 25.2
### Description: Implement the handler function for responding to reschedule requests.
### Details:
In @server/api/providers/reschedules.ts, implement respondToReschedule handler:
1. Extract providerId from the authenticated request
2. Validate the reschedule ID parameter and response body
3. Verify the provider owns the reschedule request
4. Check that the reschedule request is still in 'pending' status
5. Call respondToRescheduleRequest operation with the validated data
6. Return a 200 status with the updated reschedule request
7. Handle errors with handleApiError

Ensure proper mapping of the 'approve'/'reject' input to 'approved'/'rejected' status.

