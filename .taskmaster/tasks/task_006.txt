# Task ID: 6
# Title: Implement Update Provider Profile API
# Status: done
# Dependencies: 2, 3, 4
# Priority: high
# Description: Create an API endpoint to update the authenticated provider's profile information.
# Details:
1. Create a new API handler for PATCH /api/providers/profile
2. Use authentication middleware to verify provider
3. Implement Zod schema for profile update validation
4. Call existing Wasp operation to update provider profile
5. Format response according to API standards
6. Add to main.wasp API definitions

```typescript
// In main.wasp
api {
  httpRoute: PATCH /api/providers/profile,
  fn: import { updateProviderProfile } from "@server/api/providers/profile",
  auth: true
}

// In @server/api/providers/profile.ts
import { z } from 'zod';
import { updateProvider } from '../../../provider/operations';
import { validateRequest } from '../../common/validation';
import { handleApiError } from '../../common/errors';

const updateProfileSchema = z.object({
  body: z.object({
    name: z.string().min(1).optional(),
    email: z.string().email().optional(),
    phone: z.string().optional(),
    bio: z.string().optional(),
    profileImage: z.string().url().optional(),
    // Add other profile fields as needed
  })
});

export const updateProviderProfile = [
  validateRequest(updateProfileSchema),
  async (req, res) => {
    try {
      const providerId = req.providerId;
      const profileData = req.body;
      
      const updatedProvider = await updateProvider({
        id: providerId,
        ...profileData
      });
      
      return res.status(200).json({
        status: 'success',
        data: updatedProvider
      });
    } catch (error) {
      return handleApiError(res, error);
    }
  }
];
```

# Test Strategy:
1. Test with valid profile update data
2. Test with invalid data to verify validation
3. Test with authenticated non-provider user
4. Test with unauthenticated request
5. Verify response format matches API standards
6. Verify profile is actually updated in database

# Subtasks:
## 1. Define API endpoint in main.wasp [done]
### Dependencies: None
### Description: Add the PATCH /api/providers/profile endpoint definition to main.wasp file with proper authentication requirement
### Details:
Open main.wasp and add the API definition for the provider profile update endpoint. Ensure it's properly configured with authentication requirement and points to the correct handler function that will be implemented in subsequent tasks. The route should be PATCH /api/providers/profile and should import the handler function from @server/api/providers/profile.

## 2. Create validation schema for profile updates [done]
### Dependencies: 6.1
### Description: Implement the Zod schema for validating provider profile update requests
### Details:
Create or update the @server/api/providers/profile.ts file to include the Zod validation schema for profile updates. The schema should validate the request body and include fields like name, email, phone, bio, profileImage, etc. Each field should have appropriate validation rules (e.g., email format, minimum length for name). Make sure to export the schema for use in the handler function.

## 3. Implement request validation middleware [done]
### Dependencies: 6.2
### Description: Create or update the validation middleware to use with the profile update endpoint
### Details:
Implement or update the validateRequest middleware in @server/common/validation.ts that will use the Zod schema to validate incoming requests. This middleware should extract the request body, validate it against the schema, and either pass the validated data to the next middleware or return appropriate validation error responses. Ensure it's properly typed with TypeScript.

## 4. Implement the profile update handler function [done]
### Dependencies: 6.2, 6.3
### Description: Create the main handler function that processes validated requests and updates provider profiles
### Details:
Implement the updateProviderProfile handler function in @server/api/providers/profile.ts. This function should extract the provider ID from the authenticated request, get the validated profile data, call the updateProvider operation with the appropriate parameters, and format the response according to API standards. Include proper error handling using the handleApiError utility.

## 5. Implement error handling and response formatting [done]
### Dependencies: 6.4
### Description: Create or update error handling utilities and ensure proper response formatting
### Details:
Implement or update the handleApiError utility in @server/common/errors.ts to properly handle different types of errors that might occur during profile updates (e.g., database errors, validation errors, not found errors). Ensure the API responses follow a consistent format with status, data, and error fields as appropriate. Update the handler function to use these utilities for all responses.

