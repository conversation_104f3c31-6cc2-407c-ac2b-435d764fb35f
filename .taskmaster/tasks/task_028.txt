# Task ID: 28
# Title: Fix Failed Integration Tests for API Endpoints
# Status: done
# Dependencies: 8, 11, 12, 15, 17, 20, 23, 24, 27
# Priority: medium
# Description: Resolve all 17 failed integration tests by addressing specific issues identified in server logs, including missing fields, incorrect response structures, and status code mismatches.
# Details:
This task involves systematically fixing all 17 failed integration tests across various API endpoints. The issues identified from server logs fall into several categories:

1. **Missing Fields in Location Creation**:
   - Fix the 'city' field validation in the Create Location API
   - Ensure all required location fields are properly validated and stored
   - Update response schema to include all expected fields

2. **Missing Response Fields in Services**:
   - Add missing 'description' and 'price' fields in service responses
   - Ensure service creation and retrieval endpoints return complete data
   - Validate response format against API specifications

3. **Status Code Mismatches**:
   - Correct HTTP status codes returned by API endpoints
   - Ensure error conditions return appropriate status codes (400, 401, 403, 404, etc.)
   - Verify success responses use correct status codes (200, 201, 204)

4. **Other Potential Issues**:
   - Check for authentication/authorization issues in protected endpoints
   - Verify proper error handling for invalid inputs
   - Ensure consistent response formats across all endpoints
   - Fix any data type mismatches between API contracts and implementations

For each failed test, create a subtask that:
1. Identifies the specific API endpoint being tested
2. Documents the exact failure and expected behavior
3. Provides a clear solution to fix the issue
4. Includes verification steps to confirm the fix works

The implementation should focus on maintaining API contract consistency while ensuring all tests pass reliably.

# Test Strategy:
For each of the 17 failed tests:

1. **Reproduce the Failure**:
   - Run the specific failed test in isolation to confirm the issue
   - Capture detailed logs and error messages
   - Document the exact request/response that's failing

2. **Implement the Fix**:
   - Make targeted changes to address the specific issue
   - Avoid introducing regressions in other tests
   - Follow the API contract specifications

3. **Verify the Fix**:
   - Run the previously failing test to confirm it now passes
   - Check that the response matches the expected format and values
   - Verify status codes are correct

4. **Regression Testing**:
   - Run the full test suite to ensure no new failures were introduced
   - Pay special attention to related endpoints that might be affected

5. **Documentation**:
   - Update API documentation if any contract changes were made
   - Document any edge cases or special handling implemented

6. **Final Verification**:
   - Run the complete integration test suite with all fixes applied
   - Confirm all 17 previously failing tests now pass consistently
   - Verify overall test coverage remains adequate

# Subtasks:
## 1. Fix Service Creation Test - Missing Description and Price Fields [done]
### Dependencies: None
### Description: Fix the 'should create service successfully' test by ensuring the API response includes the expected 'description' and 'price' fields in the service creation response.
### Details:
The test expects the service creation response to include 'description' and 'price' fields, but the current API response only returns: {"acceptNew": true, "acceptOnline": true, "color": "#FF5722", "duration": 30, "id": 6, "notificationOn": true, "pointsRequirements": 1, "title": "Test Service"}. Need to investigate the service creation endpoint and ensure these fields are included in the response.

## 2. Fix Service Validation Error Status Codes [done]
### Dependencies: None
### Description: Fix the service validation error tests that expect 400 status codes but receive 500. Update either the API to return 400 for validation errors or update the tests to accept 500.
### Details:
Two tests are failing: 'should fail with invalid service data' and 'should fail with missing required fields'. Both expect 400 status codes but the API returns 500. This affects tests in POST /api/auth/providers/services and PUT /api/auth/providers/services/:id endpoints.

## 3. Fix Service List Response - Missing Fields [done]
### Dependencies: None
### Description: Fix the GET /api/auth/providers/services endpoint to include missing fields: price, isActive, and categoryId in the response.
### Details:
The 'should get all services successfully', 'should filter services by active status', and 'should filter services by category' tests are failing because the API response is missing the 'price', 'isActive', and 'categoryId' fields that the tests expect.

## 4. Fix Service Update Response - Missing Fields [done]
### Dependencies: None
### Description: Fix the PUT /api/auth/providers/services/:id endpoint to include missing fields in the update response: description and price.
### Details:
The 'should update service successfully' and 'should update partial service data' tests are failing because the API response after updating a service is missing the 'description' and 'price' fields that the tests expect to verify the update was successful.

## 5. Fix Location Creation - Missing City Field Error [done]
### Dependencies: None
### Description: Fix the location creation issue where the API requires a 'city' field but the test data doesn't provide it, causing 'Argument city is missing' errors.
### Details:
From server logs: 'Argument city is missing' error occurs when creating locations. The API expects a city field in the detailedAddress.create object, but the test data structure doesn't include it properly. This affects 'should fail with invalid address data' and 'should delete location successfully' tests.

## 6. Fix Location List Response - Missing isActive Field [done]
### Dependencies: None
### Description: Fix the GET /api/auth/providers/locations endpoint to include the 'isActive' field in the response.
### Details:
The 'should get all locations successfully' and 'should filter locations by active status' tests are failing because the API response is missing the 'isActive' field. Current response shows: {"address": "123 Test Street...", "city": "Test City", "elevator": false, "handicapAccess": false, "id": 7, "isMobileHidden": false, "name": "Test Location", "parking": false} but tests expect an 'isActive' property.

## 7. Fix Location Update Response - Missing Description Field [done]
### Dependencies: None
### Description: Fix the PUT /api/auth/providers/locations/:id endpoint to include the 'description' field in the update response.
### Details:
The 'should update location successfully' and 'should update partial location data' tests are failing because the API response after updating a location is missing the 'description' field that the tests expect to verify the update was successful.

## 8. Fix Location Validation Error Status Code [done]
### Dependencies: None
### Description: Fix the location validation error test that expects 400 status code but receives 500 for invalid address data.
### Details:
The 'should fail with invalid address data' test expects a 400 status code for validation errors, but the API returns 500. This needs to be aligned - either update the API to return 400 for validation errors or update the test to accept 500.

## 9. Fix Authentication Error Status Code - Invalid Credentials [done]
### Dependencies: None
### Description: Fix the authentication test that expects 401 status code for invalid credentials but receives 400.
### Details:
The 'should reject invalid credentials' test in the health check suite expects a 401 (Unauthorized) status code when invalid credentials are provided, but the API returns 400 (Bad Request). This needs to be aligned for proper HTTP semantics.

## 10. Fix Health Check Error Format Status Code [done]
### Dependencies: None
### Description: Fix the health check test that expects 400 status code for invalid requests but receives 500.
### Details:
The 'should return proper error format for invalid requests' test expects a 400 status code for malformed requests, but the API returns 500. This affects the API response format validation in the health check suite.

