# Task ID: 22
# Title: Implement Schedule Management APIs
# Status: done
# Dependencies: 2, 3, 4
# Priority: high
# Description: Create API endpoints to manage provider opening hours and schedules.
# Details:
1. Create API handlers for schedule management operations
2. Implement endpoints for:
   - GET /api/providers/schedules
   - POST /api/providers/schedules
   - PATCH /api/providers/schedules/:id
   - DELETE /api/providers/schedules/:id
3. Use authentication middleware to verify provider
4. Implement Zod schemas for validation
5. Call existing Wasp operations for schedule management
6. Format responses according to API standards
7. Add to main.wasp API definitions

```typescript
// In main.wasp
api {
  httpRoute: GET /api/providers/schedules,
  fn: import { getProviderSchedules } from "@server/api/providers/schedules",
  auth: true
}

api {
  httpRoute: POST /api/providers/schedules,
  fn: import { createSchedule } from "@server/api/providers/schedules",
  auth: true
}

api {
  httpRoute: PATCH /api/providers/schedules/:id,
  fn: import { updateSchedule } from "@server/api/providers/schedules",
  auth: true
}

api {
  httpRoute: DELETE /api/providers/schedules/:id,
  fn: import { deleteSchedule } from "@server/api/providers/schedules",
  auth: true
}

// In @server/api/providers/schedules.ts
import { z } from 'zod';
import { 
  getProviderSchedules as getSchedulesOp,
  createSchedule as createScheduleOp,
  updateSchedule as updateScheduleOp,
  deleteSchedule as deleteScheduleOp,
  getScheduleById
} from '../../../provider/operations';
import { validateRequest } from '../../common/validation';
import { handleApiError } from '../../common/errors';

// Get schedules
const getSchedulesSchema = z.object({
  query: z.object({
    queueId: z.string().uuid().optional(),
    locationId: z.string().uuid().optional(),
    from: z.string().optional(), // ISO date string
    to: z.string().optional() // ISO date string
  })
});

export const getProviderSchedules = [
  validateRequest(getSchedulesSchema),
  async (req, res) => {
    try {
      const providerId = req.providerId;
      const { queueId, locationId, from, to } = req.query;
      
      const filters = {
        queueId: queueId || undefined,
        locationId: locationId || undefined,
        from: from ? new Date(from) : undefined,
        to: to ? new Date(to) : undefined
      };
      
      const schedules = await getSchedulesOp(providerId, filters);
      
      return res.status(200).json({
        status: 'success',
        data: schedules
      });
    } catch (error) {
      return handleApiError(res, error);
    }
  }
];

// Create schedule
const createScheduleSchema = z.object({
  body: z.object({
    queueId: z.string().uuid().optional(),
    locationId: z.string().uuid().optional(),
    dayOfWeek: z.number().int().min(0).max(6),
    startTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/), // HH:MM format
    endTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/), // HH:MM format
    isActive: z.boolean().optional().default(true),
    effectiveFrom: z.string().optional(), // ISO date string
    effectiveTo: z.string().optional() // ISO date string
  })
});

export const createSchedule = [
  validateRequest(createScheduleSchema),
  async (req, res) => {
    try {
      const providerId = req.providerId;
      const scheduleData = req.body;
      
      // Convert string dates to Date objects if provided
      if (scheduleData.effectiveFrom) {
        scheduleData.effectiveFrom = new Date(scheduleData.effectiveFrom);
      }
      if (scheduleData.effectiveTo) {
        scheduleData.effectiveTo = new Date(scheduleData.effectiveTo);
      }
      
      const newSchedule = await createScheduleOp({
        providerId,
        ...scheduleData
      });
      
      return res.status(201).json({
        status: 'success',
        data: newSchedule
      });
    } catch (error) {
      return handleApiError(res, error);
    }
  }
];

// Update schedule
const updateScheduleSchema = z.object({
  params: z.object({
    id: z.string().uuid()
  }),
  body: z.object({
    dayOfWeek: z.number().int().min(0).max(6).optional(),
    startTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(), // HH:MM format
    endTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(), // HH:MM format
    isActive: z.boolean().optional(),
    effectiveFrom: z.string().optional(), // ISO date string
    effectiveTo: z.string().optional() // ISO date string
  })
});

export const updateSchedule = [
  validateRequest(updateScheduleSchema),
  async (req, res) => {
    try {
      const providerId = req.providerId;
      const scheduleId = req.params.id;
      const updateData = req.body;
      
      // Verify provider owns this schedule
      const existingSchedule = await getScheduleById(scheduleId);
      if (!existingSchedule || existingSchedule.providerId !== providerId) {
        return res.status(404).json({
          status: 'error',
          message: 'Schedule not found'
        });
      }
      
      // Convert string dates to Date objects if provided
      if (updateData.effectiveFrom) {
        updateData.effectiveFrom = new Date(updateData.effectiveFrom);
      }
      if (updateData.effectiveTo) {
        updateData.effectiveTo = new Date(updateData.effectiveTo);
      }
      
      const updatedSchedule = await updateScheduleOp({
        id: scheduleId,
        ...updateData
      });
      
      return res.status(200).json({
        status: 'success',
        data: updatedSchedule
      });
    } catch (error) {
      return handleApiError(res, error);
    }
  }
];

// Delete schedule
const deleteScheduleSchema = z.object({
  params: z.object({
    id: z.string().uuid()
  })
});

export const deleteSchedule = [
  validateRequest(deleteScheduleSchema),
  async (req, res) => {
    try {
      const providerId = req.providerId;
      const scheduleId = req.params.id;
      
      // Verify provider owns this schedule
      const existingSchedule = await getScheduleById(scheduleId);
      if (!existingSchedule || existingSchedule.providerId !== providerId) {
        return res.status(404).json({
          status: 'error',
          message: 'Schedule not found'
        });
      }
      
      await deleteScheduleOp(scheduleId);
      
      return res.status(200).json({
        status: 'success',
        message: 'Schedule deleted successfully'
      });
    } catch (error) {
      return handleApiError(res, error);
    }
  }
];
```

# Test Strategy:
1. Test all CRUD operations with valid data
2. Test with invalid data to verify validation
3. Test with schedule not owned by provider
4. Test with non-existent schedule IDs
5. Test with authenticated non-provider user
6. Verify schedules are correctly created, updated, and deleted in database
7. Test time format validation
8. Test overlapping schedules
9. Test filtering schedules by queue, location, and date range

# Subtasks:
## 1. Implement GET /api/providers/schedules endpoint [done]
### Dependencies: None
### Description: Create the API handler for retrieving provider schedules with filtering options
### Details:
1. Complete the implementation of the getProviderSchedules function in @server/api/providers/schedules.ts
2. Ensure proper validation of query parameters using the existing Zod schema
3. Implement error handling for various scenarios (invalid parameters, database errors)
4. Format the response according to API standards
5. Verify the API route is correctly defined in main.wasp

## 2. Implement POST /api/providers/schedules endpoint [done]
### Dependencies: 22.1
### Description: Create the API handler for creating new provider schedules
### Details:
1. Complete the implementation of the createSchedule function in @server/api/providers/schedules.ts
2. Ensure proper validation of request body using the existing Zod schema
3. Implement date conversion from string to Date objects
4. Call the createScheduleOp operation with the validated data
5. Format the response with the newly created schedule
6. Handle potential errors like validation failures or database constraints

## 3. Implement PATCH /api/providers/schedules/:id endpoint [done]
### Dependencies: 22.2
### Description: Create the API handler for updating existing provider schedules
### Details:
1. Complete the implementation of the updateSchedule function in @server/api/providers/schedules.ts
2. Validate both URL parameters and request body using the existing Zod schema
3. Implement ownership verification to ensure providers can only update their own schedules
4. Handle date conversions from string to Date objects
5. Call the updateScheduleOp operation with the validated data
6. Format the response with the updated schedule data

## 4. Implement DELETE /api/providers/schedules/:id endpoint [done]
### Dependencies: 22.3
### Description: Create the API handler for deleting provider schedules
### Details:
1. Complete the implementation of the deleteSchedule function in @server/api/providers/schedules.ts
2. Validate URL parameters using the existing Zod schema
3. Implement ownership verification to ensure providers can only delete their own schedules
4. Call the deleteScheduleOp operation with the validated schedule ID
5. Format the success response according to API standards
6. Handle potential errors like schedule not found or database constraints

## 5. Finalize API integration and testing [done]
### Dependencies: 22.4
### Description: Complete the API implementation by finalizing main.wasp definitions and conducting comprehensive testing
### Details:
1. Verify all API routes are correctly defined in main.wasp
2. Ensure authentication middleware is properly applied to all routes
3. Implement any missing imports or dependencies
4. Create comprehensive documentation for the API endpoints
5. Verify error handling is consistent across all endpoints
6. Ensure all responses follow the established API response format
7. Check that all operations properly validate provider ownership of schedules

