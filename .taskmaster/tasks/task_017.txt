# Task ID: 17
# Title: Implement Create Queue/Resource API
# Status: done
# Dependencies: 2, 3, 4, 8
# Priority: high
# Description: Create an API endpoint to allow providers to create new queues/resources.
# Details:
1. Create a new API handler for POST /api/providers/queues
2. Use authentication middleware to verify provider
3. Implement Zod schema for queue creation validation
4. Verify provider owns the associated location
5. Call existing Wasp operation to create queue
6. Format response according to API standards
7. Add to main.wasp API definitions

```typescript
// In main.wasp
api {
  httpRoute: POST /api/providers/queues,
  fn: import { createQueue } from "@server/api/providers/queues",
  auth: true
}

// In @server/api/providers/queues.ts
import { z } from 'zod';
import { createProviderQueue } from '../../../queue/operations';
import { getLocationById } from '../../../provider/operations';
import { validateRequest } from '../../common/validation';
import { handleApiError } from '../../common/errors';

const createQueueSchema = z.object({
  body: z.object({
    name: z.string().min(1),
    description: z.string().optional(),
    locationId: z.string().uuid(),
    capacity: z.number().int().positive().optional(),
    isActive: z.boolean().optional().default(true),
    color: z.string().optional(),
    icon: z.string().optional(),
    allowedServices: z.array(z.string().uuid()).optional()
  })
});

export const createQueue = [
  validateRequest(createQueueSchema),
  async (req, res) => {
    try {
      const providerId = req.providerId;
      const queueData = req.body;
      
      // Verify provider owns the location
      const location = await getLocationById(queueData.locationId);
      if (!location || location.providerId !== providerId) {
        return res.status(404).json({
          status: 'error',
          message: 'Location not found'
        });
      }
      
      const newQueue = await createProviderQueue({
        providerId,
        ...queueData
      });
      
      return res.status(201).json({
        status: 'success',
        data: newQueue
      });
    } catch (error) {
      return handleApiError(res, error);
    }
  }
];
```

# Test Strategy:
1. Test with valid queue data
2. Test with missing required fields
3. Test with location not owned by provider
4. Test with non-existent location ID
5. Test with authenticated non-provider user
6. Verify queue is created in database with correct provider and location association
7. Verify response format matches API standards
8. Test with allowed services specified

# Subtasks:
## 1. Define Zod schema for queue creation validation [done]
### Dependencies: None
### Description: Create a robust validation schema using Zod to validate the request body for queue creation
### Details:
Implement the createQueueSchema in @server/api/providers/queues.ts that validates all required and optional fields for queue creation. Include validation for name (required, non-empty string), description (optional string), locationId (valid UUID), capacity (optional positive integer), isActive (optional boolean with default true), color (optional string), icon (optional string), and allowedServices (optional array of UUIDs).

## 2. Implement location ownership verification [done]
### Dependencies: 17.1
### Description: Create a function to verify that the provider making the request owns the location specified in the queue creation request
### Details:
Implement the location verification logic that calls getLocationById and checks if the location's providerId matches the authenticated provider's ID. This should be implemented within the request handler function in @server/api/providers/queues.ts. Return a 404 error with appropriate message if the location doesn't exist or doesn't belong to the provider.

## 3. Create queue operation function [done]
### Dependencies: 17.1, 17.2
### Description: Implement or update the createProviderQueue operation function that handles the database interaction for creating a new queue
### Details:
In the operations file (likely @server/queue/operations.ts), implement the createProviderQueue function that takes the validated queue data and creates a new queue in the database. This function should handle all database interactions, including creating relationships with allowed services if specified. Return the newly created queue object with all its properties.

## 4. Implement API request handler [done]
### Dependencies: 17.1, 17.2, 17.3
### Description: Create the full API request handler that processes the queue creation request
### Details:
Complete the implementation of the createQueue handler in @server/api/providers/queues.ts. This should use the validateRequest middleware with the createQueueSchema, extract the providerId from the request, verify location ownership, call the createProviderQueue operation, and format the response according to API standards. Include proper error handling using the handleApiError utility.

## 5. Add API endpoint to main.wasp [done]
### Dependencies: 17.4
### Description: Register the new API endpoint in the main.wasp file to make it available in the application
### Details:
Add the API definition to main.wasp using the correct Wasp syntax. The definition should specify the HTTP route as POST /api/providers/queues, import the createQueue handler function from @server/api/providers/queues, and set auth to true to ensure the endpoint requires authentication. Ensure the import path is correct and follows Wasp conventions.

