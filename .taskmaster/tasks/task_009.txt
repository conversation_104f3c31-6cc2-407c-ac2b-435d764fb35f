# Task ID: 9
# Title: Implement Get Provider Locations API
# Status: done
# Dependencies: 2, 3, 4
# Priority: high
# Description: Create an API endpoint to retrieve all locations associated with the authenticated provider.
# Details:
1. Create a new API handler for GET /api/providers/locations
2. Use authentication middleware to verify provider
3. Implement optional query parameters for filtering
4. Call existing Wasp operation to fetch provider locations
5. Format response according to API standards
6. Add to main.wasp API definitions

```typescript
// In main.wasp
api {
  httpRoute: GET /api/providers/locations,
  fn: import { getProviderLocations } from "@server/api/providers/locations",
  auth: true
}

// In @server/api/providers/locations.ts
import { z } from 'zod';
import { getLocationsByProviderId } from '../../../provider/operations';
import { validateRequest } from '../../common/validation';
import { handleApiError } from '../../common/errors';

const getLocationsSchema = z.object({
  query: z.object({
    isActive: z.enum(['true', 'false']).optional(),
    isDefault: z.enum(['true', 'false']).optional(),
    search: z.string().optional()
  })
});

export const getProviderLocations = [
  validateRequest(getLocationsSchema),
  async (req, res) => {
    try {
      const providerId = req.providerId;
      const { isActive, isDefault, search } = req.query;
      
      // Convert string query params to appropriate types
      const filters = {
        isActive: isActive ? isActive === 'true' : undefined,
        isDefault: isDefault ? isDefault === 'true' : undefined,
        search: search || undefined
      };
      
      const locations = await getLocationsByProviderId(providerId, filters);
      
      return res.status(200).json({
        status: 'success',
        data: locations
      });
    } catch (error) {
      return handleApiError(res, error);
    }
  }
];
```

# Test Strategy:
1. Test with provider having multiple locations
2. Test with provider having no locations
3. Test with various filter combinations
4. Test with authenticated non-provider user
5. Test with unauthenticated request
6. Verify response format matches API standards
7. Verify correct locations are returned based on filters

# Subtasks:
## 1. Create Provider Location Operation [done]
### Dependencies: None
### Description: Implement the getLocationsByProviderId operation that will fetch locations from the database based on provider ID and optional filters.
### Details:
Create a new file at @server/provider/operations.ts (or add to existing file) that exports the getLocationsByProviderId function. This function should accept a providerId and an optional filters object with isActive, isDefault, and search parameters. Use Prisma to query the database for locations that match the provider ID and any provided filters. For the search parameter, implement a filter that searches location name and address fields.

## 2. Implement API Validation and Error Handling [done]
### Dependencies: None
### Description: Create the validation schema and error handling utilities needed for the API endpoint.
### Details:
Create or update the following files:
1. @server/common/validation.ts - Implement the validateRequest middleware that uses Zod to validate incoming requests
2. @server/common/errors.ts - Implement the handleApiError function to standardize error responses
Ensure the validation middleware properly extracts and validates query parameters according to the schema defined in the task.

## 3. Implement API Handler Function [done]
### Dependencies: 9.1, 9.2
### Description: Create the API handler function that processes the request and returns location data.
### Details:
Create the file @server/api/providers/locations.ts with the getProviderLocations handler as shown in the task description. Ensure it properly:
1. Extracts the providerId from the authenticated request
2. Parses and converts query parameters to appropriate types
3. Calls the getLocationsByProviderId operation with the correct parameters
4. Returns a properly formatted response with status code 200 and the locations data
5. Uses the handleApiError function for error handling

## 4. Add API Route to main.wasp [done]
### Dependencies: 9.3
### Description: Define the API route in the main.wasp file to expose the endpoint.
### Details:
Add the API definition to main.wasp as shown in the task description. Ensure the route is defined as GET /api/providers/locations and properly imports the handler function from @server/api/providers/locations. Set auth: true to ensure the endpoint requires authentication.

## 5. Implement Authentication Middleware Integration [done]
### Dependencies: 9.3, 9.4
### Description: Ensure the API endpoint correctly extracts the provider ID from the authenticated user.
### Details:
Update the request handling to properly extract the provider ID from the authenticated user context. This may require:
1. Modifying the auth middleware to attach the provider ID to the request object
2. Ensuring the providerId is correctly passed from the auth context to the getLocationsByProviderId operation
3. Adding proper type definitions for the extended request object that includes providerId
4. Handling cases where the authenticated user is not associated with a provider

