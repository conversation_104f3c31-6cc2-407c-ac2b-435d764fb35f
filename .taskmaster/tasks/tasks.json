{"master": {"tasks": [{"id": 30, "title": "Create Subscription Mapping Service", "description": "Develop a service that maps between legacy PaymentPlanId enum and new Subscription UUIDs to maintain compatibility between systems.", "details": "Implement a SubscriptionMappingService class that provides bidirectional mapping between PaymentPlanId enum values and Subscription UUIDs. The service should include methods like `getSubscriptionIdFromPlanId(planId: PaymentPlanId): UUID` and `getPlanIdFromSubscriptionId(subscriptionId: UUID): PaymentPlanId`. Store mappings in a database table for persistence. Use a singleton pattern to ensure consistent access across the application.", "testStrategy": "Create unit tests for all mapping functions with test cases covering all possible PaymentPlanId values. Implement integration tests that verify database persistence of mappings. Test edge cases like non-existent mappings and error handling.", "priority": "high", "dependencies": [], "status": "done", "subtasks": []}, {"id": 31, "title": "Initialize Subscription Plan Mappings in Database", "description": "Create and populate the database with initial mappings between legacy payment plans and new subscription records.", "status": "in-progress", "dependencies": [30], "priority": "high", "details": "Update the subscription seed script to ensure it properly creates subscription plans with names that match the PaymentPlanId enum values. This will allow the existing SubscriptionMappingService to create mappings by matching subscription names. Steps include: 1) Review the PaymentPlanId enum values and ensure corresponding subscription plans are created with matching names. 2) Verify the subscription seed script creates all necessary subscription records. 3) Run the database migration and seeding to populate the subscription data. 4) Ensure the SubscriptionMappingService can correctly map between legacy payment plans and new subscription records.", "testStrategy": "Verify all subscription plans are correctly created with names matching PaymentPlanId enum values. Test that the SubscriptionMappingService correctly maps between legacy payment plans and new subscription records. Create a validation script that checks for mapping completeness by ensuring every PaymentPlanId enum value has a corresponding subscription record with a matching name. Test both forward and reverse mapping functionality.", "subtasks": [{"id": 1, "title": "Review PaymentPlanId enum values and corresponding subscription names", "description": "", "status": "todo", "dependencies": [], "details": "", "testStrategy": ""}, {"id": 2, "title": "Update subscription seed script to ensure correct name matching", "description": "", "status": "todo", "dependencies": [], "details": "", "testStrategy": ""}, {"id": 3, "title": "Run database migration and seeding to verify subscription creation", "description": "", "status": "todo", "dependencies": [], "details": "", "testStrategy": ""}, {"id": 4, "title": "Test SubscriptionMappingService with created subscription records", "description": "", "status": "todo", "dependencies": [], "details": "", "testStrategy": ""}, {"id": 5, "title": "Create validation script to verify mapping completeness", "description": "", "status": "todo", "dependencies": [], "details": "", "testStrategy": ""}]}, {"id": 32, "title": "Implement Subscription Synchronization Service", "description": "Create a service to maintain data consistency between legacy User model subscription fields and new UserSubscription records.", "details": "Develop a SubscriptionSyncService with methods to: 1) syncUserSubscriptionToLegacy(userId: UUID): void - Updates User model fields based on UserSubscription data. 2) syncLegacyToUserSubscription(userId: UUID): void - Creates/updates UserSubscription based on User model fields. 3) validateSubscriptionConsistency(userId: UUID): boolean - Checks if both systems have consistent data. Implement with transaction support to prevent partial updates.", "testStrategy": "Create unit tests for each synchronization method. Test scenarios including new subscriptions, upgrades, downgrades, and cancellations. Implement integration tests that verify database consistency after sync operations. Test error handling and transaction rollback.", "priority": "high", "dependencies": [30, 31], "status": "pending", "subtasks": []}, {"id": 33, "title": "Create Utility Functions for Status Mapping and Data Conversion", "description": "Develop utility functions to map subscription statuses and convert data formats between legacy and new subscription systems.", "details": "Implement a SubscriptionUtilities module with functions for: 1) mapLegacyStatusToNew(legacyStatus: string): SubscriptionStatus - Converts legacy status strings to new enum values. 2) mapNewStatusToLegacy(newStatus: SubscriptionStatus): string - Converts new status enum to legacy format. 3) convertLegacySubscriptionData(userData: User): UserSubscriptionData - Extracts subscription data from User model. 4) formatSubscriptionMetadata(subscription: Subscription): object - Creates metadata for payment processor.", "testStrategy": "Create comprehensive unit tests for all utility functions with test cases covering all possible status values and data formats. Test edge cases and error handling. Verify bidirectional conversion maintains data integrity.", "priority": "medium", "dependencies": [30], "status": "pending", "subtasks": []}, {"id": 34, "title": "Enhance CreateCheckoutSessionArgs Interface", "description": "Update the CreateCheckoutSessionArgs interface to include subscription metadata needed for the new subscription system.", "details": "Modify the CreateCheckoutSessionArgs interface to include optional subscription-related fields: 1) subscriptionId?: UUID - The ID of the subscription being purchased. 2) subscriptionMetadata?: object - Additional metadata for the subscription. 3) isLegacyMode?: boolean - Flag to indicate if using legacy or new subscription flow. Update all implementations and usages of this interface throughout the codebase. Ensure backward compatibility by making new fields optional.", "testStrategy": "Update existing unit tests for checkout session creation to include the new fields. Create new test cases specifically for subscription-related checkout flows. Verify backward compatibility with existing code that uses the interface.", "priority": "high", "dependencies": [30, 33], "status": "pending", "subtasks": []}, {"id": 35, "title": "Update LemonSqueezy Webhook Handler for UserSubscription Creation", "description": "Enhance the LemonSqueezy webhook handler to create and update UserSubscription records in addition to updating legacy User fields.", "details": "Modify the handleLemonSqueezyWebhook function to: 1) Continue updating User model fields for backward compatibility. 2) Create or update UserSubscription records based on webhook events. 3) Use the SubscriptionMappingService to determine the correct Subscription ID. 4) Implement transaction-based updates to ensure consistency between User and UserSubscription tables. 5) Handle all relevant event types: subscription_created, subscription_updated, subscription_cancelled, etc.", "testStrategy": "Create integration tests with mock LemonSqueezy webhook payloads for each event type. Verify both User model and UserSubscription records are correctly updated. Test error scenarios and transaction rollback. Validate data consistency between systems after webhook processing.", "priority": "high", "dependencies": [30, 31, 32, 33], "status": "pending", "subtasks": []}, {"id": 36, "title": "Update Stripe Webhook Handler for UserSubscription Creation", "description": "Enhance the Stripe webhook handler to create and update UserSubscription records in addition to updating legacy User fields.", "details": "Modify the handleStripeWebhook function to: 1) Continue updating User model fields for backward compatibility. 2) Create or update UserSubscription records based on webhook events. 3) Use the SubscriptionMappingService to determine the correct Subscription ID. 4) Implement transaction-based updates to ensure consistency between User and UserSubscription tables. 5) Handle all relevant event types: customer.subscription.created, customer.subscription.updated, customer.subscription.deleted, etc.", "testStrategy": "Create integration tests with mock Stripe webhook payloads for each event type. Verify both User model and UserSubscription records are correctly updated. Test error scenarios and transaction rollback. Validate data consistency between systems after webhook processing.", "priority": "high", "dependencies": [30, 31, 32, 33], "status": "pending", "subtasks": []}, {"id": 37, "title": "Modify generateCheckoutSession Operation", "description": "Update the generateCheckoutSession operation to support both legacy and new subscription flows while maintaining backward compatibility.", "details": "Enhance the generateCheckoutSession function to: 1) Accept the enhanced CreateCheckoutSessionArgs with subscription metadata. 2) Determine whether to use legacy or new subscription flow based on args or feature flags. 3) For new flow, include subscription metadata in the checkout session. 4) For legacy flow, maintain existing behavior. 5) Return consistent response format regardless of flow used. 6) Add proper error handling for subscription-specific issues.", "testStrategy": "Create unit tests for both legacy and new subscription flows. Test with various subscription types and payment processors. Verify checkout session contains correct metadata. Test error handling and edge cases. Perform end-to-end tests with actual payment processor APIs in test mode.", "priority": "high", "dependencies": [34, 35, 36], "status": "pending", "subtasks": []}, {"id": 38, "title": "Implement Transaction-based Updates for Data Consistency", "description": "Create a transaction management system to ensure data consistency when updating both legacy and new subscription systems.", "details": "Implement a TransactionManager class that: 1) Provides methods for executing operations within a database transaction. 2) Handles rollback on failure. 3) Logs transaction details for debugging. 4) Supports nested transactions if needed. Modify subscription-related operations to use transactions when updating multiple tables. Implement specific transaction handlers for webhook processing, subscription creation, and status changes.", "testStrategy": "Create unit tests for transaction management functions. Test successful transactions and rollbacks. Implement integration tests that simulate failures at different points in the transaction to verify proper rollback. Verify data consistency after failed transactions.", "priority": "high", "dependencies": [32, 35, 36], "status": "pending", "subtasks": []}, {"id": 39, "title": "Enhance Subscription API Endpoints", "description": "Update subscription API endpoints to integrate with payment processor checkout flow and support both legacy and new subscription systems.", "details": "Modify existing subscription API endpoints to: 1) Support operations on both User model and UserSubscription records. 2) Add new endpoints for UserSubscription-specific operations. 3) Implement feature flag checks to determine which system to use. 4) Ensure consistent response formats regardless of underlying system. 5) Add proper error handling and validation. Key endpoints to update: getSubscriptions, getUserSubscription, updateSubscription, cancelSubscription.", "testStrategy": "Create API tests for each endpoint covering both legacy and new subscription flows. Test with various subscription types and states. Verify correct handling of feature flags. Test error scenarios and edge cases. Implement integration tests that verify database state after API operations.", "priority": "medium", "dependencies": [32, 37, 38], "status": "pending", "subtasks": []}, {"id": 40, "title": "Modify handleSubscribeToSubscription Function", "description": "Update the handleSubscribeToSubscription function to redirect to payment processor when needed and support both subscription systems.", "details": "Enhance handleSubscribeToSubscription to: 1) Check if payment is required based on subscription type. 2) For paid subscriptions, generate checkout session using the updated generateCheckoutSession. 3) For free subscriptions, create UserSubscription record directly. 4) Update legacy User fields for backward compatibility. 5) Handle upgrades, downgrades, and reactivations appropriately. 6) Implement proper error handling and validation.", "testStrategy": "Create unit tests for different subscription scenarios (new, upgrade, downgrade, free, paid). Test integration with payment processors using test mode. Verify correct redirection for paid subscriptions. Test error handling and edge cases. Validate data consistency between systems after subscription changes.", "priority": "high", "dependencies": [37, 39], "status": "pending", "subtasks": []}, {"id": 41, "title": "Create Direct Subscription Creation for Admin/Special Cases", "description": "Implement functionality for direct creation of UserSubscription records for administrative purposes or special cases without payment processor involvement.", "details": "Create an AdminSubscriptionService with methods to: 1) createUserSubscription(userId: UUID, subscriptionId: UUID, options: object): UserSubscription - Creates subscription without payment. 2) updateUserSubscriptionStatus(userSubscriptionId: UUID, status: SubscriptionStatus): void - Directly updates status. 3) extendUserSubscription(userSubscriptionId: UUID, durationDays: number): void - Extends subscription period. Implement proper authorization checks and audit logging for all admin operations.", "testStrategy": "Create unit tests for all admin subscription functions. Test authorization checks and permissions. Implement integration tests that verify database state after admin operations. Test synchronization with legacy User model fields. Verify audit logging functionality.", "priority": "medium", "dependencies": [32, 38], "status": "pending", "subtasks": []}, {"id": 42, "title": "Implement Subscription Status Synchronization", "description": "Create a system to ensure subscription status is synchronized between legacy and new subscription systems.", "details": "Implement a SubscriptionStatusSyncJob that: 1) Runs periodically (e.g., hourly) to check for inconsistencies. 2) Identifies users with mismatched subscription status between systems. 3) Applies corrections using the SubscriptionSyncService. 4) Logs discrepancies for review. 5) Sends alerts for critical inconsistencies. Also implement event-based synchronization that triggers on status changes in either system.", "testStrategy": "Create unit tests for the synchronization job logic. Test with various discrepancy scenarios. Implement integration tests that verify correction of inconsistencies. Test performance with large datasets. Verify logging and alerting functionality.", "priority": "medium", "dependencies": [32, 38], "status": "pending", "subtasks": []}, {"id": 43, "title": "Create Migration Scripts for Existing User Subscription Data", "description": "Develop scripts to migrate existing user subscription data from the legacy User model to new UserSubscription records.", "details": "Create a migration system with: 1) Analysis script to identify users with active subscriptions. 2) Data migration function to create UserSubscription records based on User model data. 3) Validation function to verify successful migration. 4) Rollback capability to revert changes if needed. 5) Progress tracking and reporting. Implement batched processing to handle large user bases without performance impact.", "testStrategy": "Test migration scripts on a copy of production data. Verify all users with subscriptions are correctly migrated. Test rollback functionality. Measure performance and optimize for large datasets. Create validation scripts to verify data integrity post-migration.", "priority": "medium", "dependencies": [30, 31, 32, 33], "status": "pending", "subtasks": []}, {"id": 44, "title": "Implement UserSubscription Record Creation for Existing Subscriptions", "description": "Create a system to automatically generate UserSubscription records for users with existing subscriptions during the migration process.", "details": "Implement a UserSubscriptionMigrator class with methods to: 1) identifyUsersForMigration(): User[] - Finds users with active subscriptions but no UserSubscription records. 2) migrateUser(userId: UUID): UserSubscription - Creates UserSubscription based on User data. 3) validateMigration(userId: UUID): boolean - Verifies successful migration. 4) batchMigrateUsers(batchSize: number): MigrationResult - Processes users in batches. Include detailed logging and error handling for the migration process.", "testStrategy": "Create unit tests for each migration function. Test with various user subscription scenarios. Implement integration tests that verify correct UserSubscription creation. Test batch processing with different batch sizes. Verify error handling and logging functionality.", "priority": "medium", "dependencies": [43], "status": "pending", "subtasks": []}, {"id": 45, "title": "Validate Data Integrity Between Old and New Systems", "description": "Create validation tools to ensure data integrity and consistency between legacy and new subscription systems.", "details": "Develop a DataIntegrityValidator with functions to: 1) validateUserSubscription(userId: UUID): ValidationResult - Checks consistency for a single user. 2) validateAllSubscriptions(): ValidationSummary - Checks all users with subscriptions. 3) generateInconsistencyReport(): Report - Creates detailed report of discrepancies. 4) automaticRepair(options: RepairOptions): RepairResult - Attempts to fix inconsistencies automatically. Include severity classification for different types of inconsistencies.", "testStrategy": "Create unit tests for validation logic with various consistency scenarios. Test with intentionally inconsistent data to verify detection. Implement integration tests for the repair functionality. Verify report generation with different types of inconsistencies.", "priority": "medium", "dependencies": [43, 44], "status": "pending", "subtasks": []}, {"id": 46, "title": "Create Rollback Procedures for Migration Safety", "description": "Implement rollback procedures to safely revert migration changes if issues are detected during the transition period.", "details": "Create a MigrationRollbackService with methods to: 1) createRollbackSnapshot(): SnapshotId - Captures current state before migration. 2) rollbackToSnapshot(snapshotId: SnapshotId): RollbackResult - Reverts to previous state. 3) validateRollbackSuccess(): boolean - Verifies successful rollback. 4) cleanupSnapshots(olderThan: Date): void - Removes old snapshots. Implement transaction-based rollback operations to ensure atomicity.", "testStrategy": "Create unit tests for snapshot creation and rollback logic. Test rollback scenarios with various types of changes. Implement integration tests that verify database state after rollback. Test performance with large datasets. Verify cleanup functionality.", "priority": "medium", "dependencies": [43, 44, 45], "status": "pending", "subtasks": []}, {"id": 47, "title": "Update PricingPage Component for New Subscription APIs", "description": "Modify the PricingPage component to support both legacy and new subscription APIs through feature flags.", "details": "Update the PricingPage component to: 1) Check feature flags to determine which subscription system to use. 2) Fetch subscription data from appropriate API endpoints. 3) Display consistent UI regardless of underlying system. 4) Handle subscription selection and checkout flow for both systems. 5) Implement proper error handling and loading states. Use dependency injection or adapter pattern to abstract the subscription system differences.", "testStrategy": "Create unit tests for the updated component with both feature flag states. Test UI rendering with various subscription data. Implement integration tests for the checkout flow. Test error handling and edge cases. Perform end-to-end tests with both subscription systems.", "priority": "medium", "dependencies": [39, 40], "status": "pending", "subtasks": []}, {"id": 48, "title": "Implement Feature Flags for Gradual Rollout", "description": "Create a feature flag system to control the gradual rollout of the new subscription system to users.", "details": "Implement a FeatureFlagService with methods to: 1) isFeatureEnabled(featureName: string, userId?: UUID): boolean - Checks if feature is enabled globally or for specific user. 2) enableFeatureForUser(featureName: string, userId: UUID): void - Enables feature for specific user. 3) enableFeatureGlobally(featureName: string): void - Enables feature for all users. 4) getFeaturePercentage(featureName: string): number - Gets rollout percentage. Create specific flags for 'newSubscriptionSystem', 'newCheckoutFlow', etc.", "testStrategy": "Create unit tests for feature flag logic with various scenarios. Test user-specific and global flag settings. Implement integration tests that verify feature-dependent behavior. Test performance with large user bases. Verify persistence of flag settings.", "priority": "high", "dependencies": [], "status": "done", "subtasks": []}, {"id": 49, "title": "Enhance Checkout Flow for New Subscription System", "description": "Update the checkout flow to work seamlessly with the new subscription system while maintaining backward compatibility.", "details": "Modify the checkout flow to: 1) Use feature flags to determine which subscription system to use. 2) For new system, include subscription metadata in checkout. 3) Handle post-checkout actions appropriately for both systems. 4) Implement proper error handling and recovery. 5) Provide consistent user experience regardless of underlying system. Update UI components to support both flows without visible differences to users.", "testStrategy": "Create unit tests for checkout logic with both subscription systems. Test the complete checkout flow with mock payment processors. Implement integration tests for post-checkout actions. Test error handling and recovery. Perform end-to-end tests with actual payment processors in test mode.", "priority": "high", "dependencies": [37, 47, 48], "status": "pending", "subtasks": []}, {"id": 50, "title": "Add Subscription Management UI Components", "description": "Create or enhance UI components for managing subscriptions that work with both legacy and new subscription systems.", "details": "Develop UI components for: 1) Subscription details display. 2) Upgrade/downgrade flow. 3) Cancellation process. 4) Subscription history view. 5) Payment method management. Ensure components work with both subscription systems based on feature flags. Implement responsive design and accessibility features. Use adapter pattern to abstract data source differences.", "testStrategy": "Create unit tests for all UI components with both subscription data formats. Test user interactions and state changes. Implement integration tests for data fetching and updates. Test accessibility compliance. Perform usability testing with different user scenarios.", "priority": "medium", "dependencies": [39, 47, 48], "status": "pending", "subtasks": []}, {"id": 51, "title": "Create Integration Tests for Webhook Handlers", "description": "Develop comprehensive integration tests for payment processor webhook handlers to ensure proper handling of subscription events.", "details": "Create integration test suite that: 1) Mocks webhook payloads for all relevant event types from Stripe and LemonSqueezy. 2) Verifies correct handling of each event type. 3) Tests both legacy and new subscription updates. 4) Validates database state after webhook processing. 5) Tests error handling and recovery. Include tests for edge cases like duplicate events, out-of-order events, and malformed payloads.", "testStrategy": "Use a test database for integration tests. Create fixtures with sample webhook payloads. Verify database state before and after webhook processing. Test with feature flags in different states. Measure performance and optimize webhook handling if needed.", "priority": "high", "dependencies": [35, 36, 38], "status": "pending", "subtasks": []}, {"id": 52, "title": "Implement API Integration Tests for Subscription Flows", "description": "Create integration tests for subscription-related API endpoints to ensure proper functionality with both subscription systems.", "details": "Develop API test suite that: 1) Tests all subscription endpoints with both legacy and new systems. 2) Verifies correct handling of different subscription operations (create, update, cancel, etc.). 3) Tests feature flag behavior. 4) Validates response formats and error handling. 5) Verifies database state after API operations. Include tests for authorization and permission checks.", "testStrategy": "Use a test database for integration tests. Create test users with various subscription states. Test API endpoints with different request parameters. Verify response formats and status codes. Test with feature flags in different states.", "priority": "high", "dependencies": [39, 40, 41], "status": "pending", "subtasks": []}, {"id": 53, "title": "Create Data Migration Validation Scripts", "description": "Develop scripts to validate the success and data integrity of the subscription data migration process.", "details": "Create validation scripts that: 1) Compare User model subscription data with UserSubscription records. 2) Identify inconsistencies or missing data. 3) Generate detailed reports of migration status. 4) Provide statistics on migration success rate. 5) Identify users requiring manual intervention. Implement both automated validation and tools for manual verification by administrators.", "testStrategy": "Test validation scripts with intentionally inconsistent data to verify detection. Run validation on test migrations before production. Measure performance with large datasets and optimize if needed. Verify report accuracy and completeness.", "priority": "medium", "dependencies": [43, 44, 45], "status": "pending", "subtasks": []}, {"id": 54, "title": "Test Payment Processor Integration End-to-End", "description": "Perform comprehensive end-to-end testing of the payment processor integration with both subscription systems.", "details": "Create end-to-end test suite that: 1) Tests complete subscription flows from checkout to webhook processing. 2) Verifies correct handling of all subscription events. 3) Tests with both Stripe and LemonSqueezy in test mode. 4) Validates database state after each step. 5) Tests both legacy and new subscription systems. Include tests for edge cases like failed payments, refunds, and subscription changes.", "testStrategy": "Use payment processor test environments. Create test users and subscriptions. Verify database state after each step in the flow. Test with feature flags in different states. Measure performance and identify bottlenecks.", "priority": "high", "dependencies": [37, 49, 51, 52], "status": "pending", "subtasks": []}], "metadata": {"created": "2025-06-18T13:02:22.487Z", "updated": "2025-07-21T15:14:32.808Z", "description": "Tasks for master context"}}, "template-migration": {"tasks": [], "metadata": {"created": "2025-07-14T04:45:32.380Z", "updated": "2025-07-14T04:45:32.380Z", "description": "Tasks for migrating Wasp project to use the new admin dashboard template design"}}, "payment-integration": {"tasks": [{"id": 1, "title": "Update Subscription Model Schema", "description": "Enhance the Subscription model by adding paymentProcessorPlanId field and ensuring proper indexing for performance optimization.", "details": "Modify the Subscription model schema to include a new field 'paymentProcessorPlanId' that will store the corresponding plan ID from the payment processor (LemonSqueezy/Stripe). Ensure proper indexing on this field and any other fields that will be frequently queried. The schema update should maintain backward compatibility with existing User model fields.\n\nExample schema modification:\n```typescript\ninterface Subscription {\n  id: string;\n  name: string;\n  description: string;\n  price: number;\n  interval: 'monthly' | 'yearly' | 'one-time';\n  features: string[];\n  paymentProcessorPlanId: string; // New field\n  // other existing fields\n}\n\n// Add appropriate indexes\ndb.createIndex('Subscription', 'paymentProcessorPlanId');\n```", "testStrategy": "1. Unit test the updated Subscription model schema to ensure it accepts and validates the new paymentProcessorPlanId field.\n2. Test database queries using the new field to verify index performance.\n3. Verify backward compatibility by ensuring existing code that uses the Subscription model continues to function correctly.", "priority": "high", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 2, "title": "Create Payment Processor Plan Mapping Layer", "description": "Develop a mapping layer between the Subscription model and payment processor plans to facilitate seamless integration.", "details": "Implement a service that maps between internal Subscription IDs and payment processor plan IDs. This layer should handle the complexity of different payment processors (LemonSqueezy/Stripe) and provide a consistent interface for the rest of the application.\n\n```typescript\nclass PaymentProcessorMapper {\n  // Map internal subscription ID to payment processor plan ID\n  mapToProcessorPlanId(subscriptionId: string, processor: 'lemonSqueezy' | 'stripe'): string {\n    // Implementation logic\n  }\n  \n  // Map payment processor plan ID to internal subscription ID\n  mapToSubscriptionId(processorPlanId: string, processor: 'lemonSqueezy' | 'stripe'): string {\n    // Implementation logic\n  }\n  \n  // Get processor-specific metadata for a subscription\n  getProcessorMetadata(subscriptionId: string, processor: 'lemonSqueezy' | 'stripe'): Record<string, any> {\n    // Implementation logic\n  }\n}\n```\n\nEnsure the mapper handles both legacy PaymentPlanId enum values and new Subscription IDs for backward compatibility.", "testStrategy": "1. Unit test the mapping functions with various subscription IDs and payment processor types.\n2. Test edge cases such as invalid IDs or unsupported payment processors.\n3. Integration test with mock payment processor APIs to verify correct mapping behavior.", "priority": "high", "dependencies": [1], "status": "pending", "subtasks": []}, {"id": 3, "title": "Update Checkout Flow for Subscription Model", "description": "Enhance the checkout flow to support both legacy PaymentPlanId and new Subscription IDs while maintaining the same user experience.", "details": "Modify the existing generateCheckoutSession operation to work with both the legacy PaymentPlanId enum and the new Subscription model IDs. The updated flow should:\n\n1. Accept either a PaymentPlanId or a Subscription ID as input\n2. Use the mapping layer to determine the correct payment processor plan ID\n3. Generate a checkout session with the appropriate payment processor\n4. Return consistent response format regardless of input type\n\n```typescript\nasync function generateCheckoutSession(\n  input: { paymentPlanId?: PaymentPlanId, subscriptionId?: string },\n  userId: string,\n  returnUrl: string\n): Promise<CheckoutSession> {\n  // Determine which ID type was provided\n  let processorPlanId: string;\n  \n  if (input.subscriptionId) {\n    // Use new subscription ID\n    processorPlanId = paymentProcessorMapper.mapToProcessorPlanId(\n      input.subscriptionId,\n      currentPaymentProcessor\n    );\n  } else if (input.paymentPlanId) {\n    // Use legacy payment plan ID\n    processorPlanId = legacyPlanIdToProcessorId(input.paymentPlanId);\n  } else {\n    throw new Error('Either paymentPlanId or subscriptionId must be provided');\n  }\n  \n  // Generate checkout session with payment processor\n  // Return checkout session details\n}\n```", "testStrategy": "1. Unit test the function with both legacy PaymentPlanId and new Subscription ID inputs.\n2. Integration test with mock payment processor APIs to verify checkout session creation.\n3. End-to-end test of the complete checkout flow with both input types.\n4. Verify error handling for invalid inputs.", "priority": "high", "dependencies": [2], "status": "pending", "subtasks": []}, {"id": 4, "title": "Enhance Webhook Handlers for Dual-System Operation", "description": "Update webhook handlers to create UserSubscription records while maintaining User model updates for backward compatibility.", "details": "Modify the existing webhook handlers (e.g., updateUserLemonSqueezyPaymentDetails) to support dual-system operation during the transition period. The enhanced handlers should:\n\n1. Process incoming webhook events from payment processors\n2. Update the User model fields as before (subscriptionStatus, subscriptionPlan, credits, queues)\n3. Additionally create or update UserSubscription records with the new schema\n4. Handle all payment processor events (subscription created, updated, cancelled, payment failed, etc.)\n\n```typescript\nasync function handlePaymentWebhook(event: WebhookEvent): Promise<void> {\n  // Extract event data\n  const { type, data } = event;\n  \n  // Process event based on type\n  switch (type) {\n    case 'subscription_created':\n      // Update User model (legacy)\n      await updateUserSubscriptionDetails(data.userId, data.planId, 'active');\n      \n      // Create UserSubscription record (new system)\n      await createUserSubscription({\n        userId: data.userId,\n        subscriptionId: paymentProcessorMapper.mapToSubscriptionId(\n          data.processorPlanId,\n          data.processor\n        ),\n        status: 'active',\n        startDate: new Date(),\n        endDate: data.endDate,\n        processorSubscriptionId: data.subscriptionId\n      });\n      break;\n    \n    // Handle other event types\n    // ...\n  }\n}\n```", "testStrategy": "1. Unit test webhook handlers with various event types and data payloads.\n2. Verify both User model and UserSubscription records are correctly updated.\n3. Test error handling and recovery mechanisms.\n4. Integration test with mock webhook events from different payment processors.", "priority": "high", "dependencies": [2, 3], "status": "pending", "subtasks": []}, {"id": 5, "title": "Implement Subscription-Based Checkout API", "description": "Create API endpoints for generating subscription-based checkout sessions with return URL handling for external website integration.", "details": "Develop RESTful API endpoints that allow external websites to initiate checkout processes for subscriptions. The API should:\n\n1. Accept subscription ID and return URL parameters\n2. Generate a checkout session with the appropriate payment processor\n3. Return checkout URL and session information\n4. Support both JWT and API key authentication\n\n```typescript\n// API endpoint definition\napp.post('/api/checkout/subscription', authenticate, async (req, res) => {\n  try {\n    const { subscriptionId, returnUrl } = req.body;\n    const userId = req.user.id;\n    \n    // Validate inputs\n    if (!subscriptionId || !returnUrl) {\n      return res.status(400).json({ error: 'Missing required parameters' });\n    }\n    \n    // Generate checkout session\n    const session = await generateCheckoutSession(\n      { subscriptionId },\n      userId,\n      returnUrl\n    );\n    \n    return res.json({\n      checkoutUrl: session.url,\n      sessionId: session.id,\n      expiresAt: session.expiresAt\n    });\n  } catch (error) {\n    console.error('Checkout session creation failed:', error);\n    return res.status(500).json({ error: 'Failed to create checkout session' });\n  }\n});\n```", "testStrategy": "1. Unit test API endpoints with various input combinations.\n2. Test authentication mechanisms (JWT and API key).\n3. Integration test with mock payment processor to verify checkout session creation.\n4. Test error handling and response formats.", "priority": "high", "dependencies": [3], "status": "pending", "subtasks": []}, {"id": 6, "title": "Develop Subscription Management APIs", "description": "Create comprehensive CRUD operations for subscription lifecycle management via API endpoints.", "details": "Implement a set of RESTful API endpoints for managing subscriptions throughout their lifecycle. These endpoints should allow external websites to:\n\n1. Retrieve subscription details\n2. Update subscription parameters (where applicable)\n3. Cancel subscriptions\n4. Reactivate cancelled subscriptions\n5. Change subscription plans\n\n```typescript\n// Example API endpoints\n\n// Get subscription details\napp.get('/api/subscriptions/:id', authenticate, async (req, res) => {\n  // Implementation\n});\n\n// Cancel subscription\napp.post('/api/subscriptions/:id/cancel', authenticate, async (req, res) => {\n  try {\n    const { id } = req.params;\n    const userId = req.user.id;\n    \n    // Verify user owns this subscription\n    const subscription = await getUserSubscription(id, userId);\n    if (!subscription) {\n      return res.status(404).json({ error: 'Subscription not found' });\n    }\n    \n    // Cancel with payment processor\n    await cancelSubscriptionWithProcessor(subscription.processorSubscriptionId);\n    \n    // Update local records\n    await updateUserSubscription(id, { status: 'cancelled' });\n    \n    return res.json({ success: true });\n  } catch (error) {\n    console.error('Subscription cancellation failed:', error);\n    return res.status(500).json({ error: 'Failed to cancel subscription' });\n  }\n});\n\n// Additional endpoints for other operations\n```", "testStrategy": "1. Unit test each API endpoint with valid and invalid inputs.\n2. Test authorization logic to ensure users can only manage their own subscriptions.\n3. Integration test with mock payment processor for operations that require processor interaction.\n4. Test error handling and edge cases (e.g., cancelling already cancelled subscription).", "priority": "medium", "dependencies": [4], "status": "pending", "subtasks": []}, {"id": 7, "title": "Implement User Subscription Management APIs", "description": "Create API endpoints for managing user-specific subscription data including credit balance, subscription status, and history.", "details": "Develop RESTful API endpoints that provide access to user-specific subscription information. These endpoints should:\n\n1. Return current subscription status and details\n2. Provide credit balance information\n3. Show subscription history\n4. Allow credit usage tracking\n\n```typescript\n// Get user subscription details\napp.get('/api/user/subscription', authenticate, async (req, res) => {\n  try {\n    const userId = req.user.id;\n    \n    // Get active subscription\n    const subscription = await getActiveUserSubscription(userId);\n    \n    // Get credit balance\n    const credits = await getUserCredits(userId);\n    \n    return res.json({\n      subscription: subscription ? {\n        id: subscription.id,\n        plan: subscription.subscriptionId,\n        status: subscription.status,\n        startDate: subscription.startDate,\n        endDate: subscription.endDate,\n        autoRenew: subscription.autoRenew\n      } : null,\n      credits: credits,\n      queues: await getUserQueues(userId)\n    });\n  } catch (error) {\n    console.error('Failed to fetch user subscription:', error);\n    return res.status(500).json({ error: 'Failed to fetch subscription details' });\n  }\n});\n\n// Get subscription history\napp.get('/api/user/subscription/history', authenticate, async (req, res) => {\n  // Implementation\n});\n\n// Use credits\napp.post('/api/user/credits/use', authenticate, async (req, res) => {\n  // Implementation\n});\n```", "testStrategy": "1. Unit test each API endpoint with various user scenarios.\n2. Test authentication and authorization logic.\n3. Verify correct data is returned for different subscription states.\n4. Test credit usage logic and constraints.", "priority": "medium", "dependencies": [6], "status": "pending", "subtasks": []}, {"id": 8, "title": "En<PERSON>ce Webhook Endpoints for External Integration", "description": "Develop enhanced webhook endpoints for payment processor event handling that support external website integration.", "details": "Create or enhance webhook endpoints that can process payment processor events and notify external websites of subscription changes. The implementation should:\n\n1. Receive and validate webhook events from payment processors\n2. Process events to update internal subscription state\n3. Trigger notifications to external websites when relevant\n4. Support configurable callback URLs for external websites\n\n```typescript\n// Webhook endpoint for LemonSqueezy\napp.post('/webhooks/lemonSqueezy', async (req, res) => {\n  try {\n    // Verify webhook signature\n    const signature = req.headers['x-signature'];\n    if (!verifyLemonSqueezySignature(req.body, signature)) {\n      return res.status(401).json({ error: 'Invalid signature' });\n    }\n    \n    // Process webhook event\n    const event = req.body;\n    await handlePaymentWebhook({\n      type: event.meta.event_name,\n      processor: 'lemonSqueezy',\n      data: {\n        // Map LemonSqueezy event data to internal format\n        userId: await getUserIdFromCustomerId(event.data.attributes.customer_id),\n        processorPlanId: event.data.attributes.product_id,\n        subscriptionId: event.data.attributes.id,\n        // Other relevant data\n      }\n    });\n    \n    // Notify external websites if configured\n    await notifyExternalWebsites(event);\n    \n    return res.status(200).json({ received: true });\n  } catch (error) {\n    console.error('Webhook processing failed:', error);\n    // Still return 200 to acknowledge receipt\n    return res.status(200).json({ received: true, processed: false });\n  }\n});\n\n// Similar endpoint for Stripe\n// ...\n```", "testStrategy": "1. Unit test webhook endpoint with mock event payloads from different payment processors.\n2. Test signature verification logic.\n3. Verify correct processing of different event types.\n4. Test external notification mechanism with mock external websites.", "priority": "medium", "dependencies": [4], "status": "pending", "subtasks": []}, {"id": 9, "title": "Implement Authentication for External APIs", "description": "Develop authentication mechanisms that support both JWT tokens for user operations and API keys for server-to-server operations.", "details": "Create a flexible authentication system that can handle both user-specific operations via JWT tokens and server-to-server operations via API keys. The implementation should:\n\n1. Validate JWT tokens for user-specific endpoints\n2. Validate API keys for server-to-server endpoints\n3. Support scoped permissions for API keys\n4. Implement proper error handling and security measures\n\n```typescript\n// Authentication middleware factory\nfunction createAuthMiddleware(options: { allowApiKey?: boolean } = {}) {\n  return async (req, res, next) => {\n    try {\n      // Check for JWT token\n      const authHeader = req.headers.authorization;\n      if (authHeader?.startsWith('Bearer ')) {\n        const token = authHeader.substring(7);\n        const decoded = verifyJwtToken(token);\n        req.user = { id: decoded.userId, email: decoded.email };\n        return next();\n      }\n      \n      // Check for API key if allowed\n      if (options.allowApiKey) {\n        const apiKey = req.headers['x-api-key'];\n        if (apiKey) {\n          const apiKeyData = await validateApi<PERSON>ey(apiKey);\n          if (apiKeyData) {\n            req.apiKey = apiKeyData;\n            return next();\n          }\n        }\n      }\n      \n      // No valid authentication found\n      return res.status(401).json({ error: 'Unauthorized' });\n    } catch (error) {\n      console.error('Authentication failed:', error);\n      return res.status(401).json({ error: 'Unauthorized' });\n    }\n  };\n}\n\n// Usage examples\napp.get('/api/user/profile', createAuthMiddleware(), async (req, res) => {\n  // Only accessible with JWT token\n});\n\napp.get('/api/subscriptions', createAuthMiddleware({ allowApiKey: true }), async (req, res) => {\n  // Accessible with either JWT token or API key\n});\n```", "testStrategy": "1. Unit test authentication middleware with various token types and formats.\n2. Test JWT validation logic including expiration and signature verification.\n3. Test API key validation including scoped permissions.\n4. Security testing for common authentication vulnerabilities.", "priority": "high", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 10, "title": "Develop Data Migration Strategy", "description": "Create a comprehensive strategy for migrating existing user subscription data to the new UserSubscription model.", "details": "Implement a data migration process that transfers existing subscription data from User model fields to the new UserSubscription records. The migration should:\n\n1. Create Subscription records matching existing PaymentPlanId plans\n2. Generate UserSubscription records for all users with active subscriptions\n3. Maintain data integrity throughout the process\n4. Support rollback in case of failures\n\n```typescript\nasync function migrateSubscriptionData() {\n  // Create transaction for atomicity\n  const transaction = await db.beginTransaction();\n  \n  try {\n    // Step 1: Create Subscription records for legacy plans\n    const legacyPlans = [\n      { id: 'hobby', name: '<PERSON><PERSON>', paymentPlanId: PaymentPlanId.Hobby, /* other fields */ },\n      { id: 'pro', name: 'Pro', paymentPlanId: PaymentPlanId.Pro, /* other fields */ },\n      // Other plans\n    ];\n    \n    for (const plan of legacyPlans) {\n      await db.insert('Subscription', plan, { transaction });\n    }\n    \n    // Step 2: Find all users with active subscriptions\n    const users = await db.query(\n      'SELECT * FROM User WHERE subscriptionStatus = ?',\n      ['active'],\n      { transaction }\n    );\n    \n    // Step 3: Create UserSubscription records\n    for (const user of users) {\n      const subscriptionId = mapPaymentPlanIdToSubscriptionId(user.subscriptionPlan);\n      \n      await db.insert('UserSubscription', {\n        userId: user.id,\n        subscriptionId,\n        status: user.subscriptionStatus,\n        startDate: user.subscriptionStartDate || new Date(),\n        endDate: user.subscriptionEndDate,\n        // Other fields\n      }, { transaction });\n    }\n    \n    // Commit transaction\n    await transaction.commit();\n    \n    console.log(`Migration completed: ${users.length} subscriptions migrated`);\n  } catch (error) {\n    // Rollback on error\n    await transaction.rollback();\n    console.error('Migration failed:', error);\n    throw error;\n  }\n}\n```", "testStrategy": "1. Create a test environment with sample data mimicking production.\n2. Run migration in test environment and verify data integrity.\n3. Test rollback functionality by intentionally causing errors.\n4. Perform performance testing with dataset sizes similar to production.", "priority": "medium", "dependencies": [1, 2], "status": "pending", "subtasks": []}, {"id": 11, "title": "Implement Server-to-Server API Integration", "description": "Develop API key-based authentication and endpoints for server-to-server operations supporting external website integration.", "details": "Create a set of API endpoints specifically designed for server-to-server integration with external websites. These endpoints should:\n\n1. Use API key authentication\n2. Support bulk operations where appropriate\n3. Provide detailed error information\n4. Include rate limiting and security measures\n\n```typescript\n// API key management\napp.post('/api/apiKeys', adminAuthMiddleware, async (req, res) => {\n  // Create new API key for external website\n});\n\n// Bulk subscription status endpoint\napp.post('/api/server/subscriptions/status', apiKeyAuthMiddleware, async (req, res) => {\n  try {\n    const { userIds } = req.body;\n    \n    if (!Array.isArray(userIds)) {\n      return res.status(400).json({ error: 'userIds must be an array' });\n    }\n    \n    // Limit number of users that can be queried at once\n    if (userIds.length > 100) {\n      return res.status(400).json({ error: 'Maximum of 100 userIds allowed' });\n    }\n    \n    const subscriptions = await Promise.all(\n      userIds.map(async (userId) => {\n        const subscription = await getActiveUserSubscription(userId);\n        return {\n          userId,\n          hasActiveSubscription: !!subscription,\n          plan: subscription?.subscriptionId || null,\n          expiresAt: subscription?.endDate || null\n        };\n      })\n    );\n    \n    return res.json({ subscriptions });\n  } catch (error) {\n    console.error('Bulk subscription status check failed:', error);\n    return res.status(500).json({ error: 'Failed to retrieve subscription statuses' });\n  }\n});\n\n// Other server-to-server endpoints\n```", "testStrategy": "1. Unit test each API endpoint with various input scenarios.\n2. Test API key authentication and authorization.\n3. Performance test bulk operations with varying payload sizes.\n4. Test rate limiting and security measures.", "priority": "medium", "dependencies": [9], "status": "pending", "subtasks": []}, {"id": 12, "title": "Implement Customer Portal Integration", "description": "Develop APIs to support integration with subscription management interfaces on external websites.", "details": "Create endpoints that allow external websites to implement customer portal functionality for subscription management. These endpoints should:\n\n1. Generate customer portal sessions with payment processors\n2. Provide subscription management capabilities\n3. Support customization of portal appearance and behavior\n\n```typescript\n// Generate customer portal session\napp.post('/api/customer-portal/session', authenticate, async (req, res) => {\n  try {\n    const userId = req.user.id;\n    const { returnUrl } = req.body;\n    \n    // Get active subscription\n    const subscription = await getActiveUserSubscription(userId);\n    if (!subscription) {\n      return res.status(404).json({ error: 'No active subscription found' });\n    }\n    \n    // Get payment processor customer ID\n    const customer = await getCustomerForUser(userId);\n    if (!customer) {\n      return res.status(404).json({ error: 'Customer record not found' });\n    }\n    \n    // Generate portal session with payment processor\n    let portalUrl;\n    if (customer.processor === 'lemonSqueezy') {\n      portalUrl = await generateLemonSqueezyPortalUrl(customer.processorCustomerId, returnUrl);\n    } else if (customer.processor === 'stripe') {\n      portalUrl = await generateStripePortalUrl(customer.processorCustomerId, returnUrl);\n    } else {\n      return res.status(400).json({ error: 'Unsupported payment processor' });\n    }\n    \n    return res.json({ url: portalUrl });\n  } catch (error) {\n    console.error('Failed to generate customer portal session:', error);\n    return res.status(500).json({ error: 'Failed to generate portal session' });\n  }\n});\n```", "testStrategy": "1. Unit test portal session generation with mock payment processor APIs.\n2. Test with different payment processors to ensure consistent behavior.\n3. Test error handling for various scenarios (no subscription, no customer record, etc.).\n4. Integration test with actual payment processor sandbox environments.", "priority": "medium", "dependencies": [6, 7], "status": "pending", "subtasks": []}, {"id": 13, "title": "Implement Gradual System Transition", "description": "Develop a strategy for gradually transitioning from the legacy subscription system to the new system while maintaining service continuity.", "details": "Create a phased approach for transitioning between the old and new subscription systems. The implementation should:\n\n1. Support dual-write to both systems during transition\n2. Include feature flags to control system behavior\n3. Provide monitoring and comparison between systems\n4. Allow rollback to legacy system if needed\n\n```typescript\n// Feature flag service\nclass FeatureFlagService {\n  private flags: Record<string, boolean> = {\n    'use-new-subscription-system': false,\n    'dual-write-subscriptions': true,\n    'read-from-new-system': false\n  };\n  \n  isEnabled(flag: string): boolean {\n    return this.flags[flag] || false;\n  }\n  \n  enable(flag: string): void {\n    this.flags[flag] = true;\n  }\n  \n  disable(flag: string): void {\n    this.flags[flag] = false;\n  }\n}\n\n// Usage in subscription service\nasync function getUserSubscriptionInfo(userId: string) {\n  const featureFlags = new FeatureFlagService();\n  \n  if (featureFlags.isEnabled('read-from-new-system')) {\n    // Read from new system\n    return await getNewSystemSubscription(userId);\n  } else {\n    // Read from legacy system\n    return await getLegacySubscription(userId);\n  }\n}\n\n// Dual-write example\nasync function updateSubscription(userId: string, data: SubscriptionUpdateData) {\n  const featureFlags = new FeatureFlagService();\n  \n  // Always update legacy system\n  await updateLegacySubscription(userId, data);\n  \n  // Conditionally update new system\n  if (featureFlags.isEnabled('dual-write-subscriptions')) {\n    try {\n      await updateNewSystemSubscription(userId, data);\n    } catch (error) {\n      console.error('Failed to update new subscription system:', error);\n      // Continue without failing the operation\n    }\n  }\n}\n```", "testStrategy": "1. Test feature flag functionality with various configurations.\n2. Verify dual-write operations correctly update both systems.\n3. Test system behavior when transitioning between feature flag states.\n4. Test monitoring and comparison tools for detecting discrepancies between systems.", "priority": "medium", "dependencies": [4, 10], "status": "pending", "subtasks": []}, {"id": 14, "title": "Develop Comprehensive API Documentation", "description": "Create detailed API documentation for external website developers to integrate with the subscription system.", "details": "Develop comprehensive documentation that covers all aspects of the subscription system API. The documentation should include:\n\n1. Authentication methods (JWT and API key)\n2. Endpoint descriptions with request/response formats\n3. Example code snippets in common languages\n4. Error handling guidelines\n5. Best practices for integration\n\n```typescript\n// Example documentation generator using OpenAPI/Swagger\nimport { OpenAPIRegistry } from '@asteasolutions/zod-to-openapi';\nimport { z } from 'zod';\n\nconst registry = new OpenAPIRegistry();\n\n// Document authentication\nregistry.registerComponent('securitySchemes', 'bearerAuth', {\n  type: 'http',\n  scheme: 'bearer',\n  bearerFormat: 'JWT',\n  description: 'JWT token authentication for user-specific operations'\n});\n\nregistry.registerComponent('securitySchemes', 'apiKeyAuth', {\n  type: 'apiKey',\n  in: 'header',\n  name: 'x-api-key',\n  description: 'API key authentication for server-to-server operations'\n});\n\n// Document checkout endpoint\nregistry.registerPath({\n  method: 'post',\n  path: '/api/checkout/subscription',\n  summary: 'Create a checkout session for a subscription',\n  security: [{ bearerAuth: [] }],\n  request: {\n    body: {\n      content: {\n        'application/json': {\n          schema: z.object({\n            subscriptionId: z.string().describe('ID of the subscription plan'),\n            returnUrl: z.string().url().describe('URL to redirect after checkout')\n          })\n        }\n      }\n    }\n  },\n  responses: {\n    200: {\n      description: 'Checkout session created successfully',\n      content: {\n        'application/json': {\n          schema: z.object({\n            checkoutUrl: z.string().url(),\n            sessionId: z.string(),\n            expiresAt: z.string().datetime()\n          })\n        }\n      }\n    },\n    400: {\n      description: 'Invalid request parameters',\n      content: {\n        'application/json': {\n          schema: z.object({\n            error: z.string()\n          })\n        }\n      }\n    },\n    // Other response codes\n  }\n});\n\n// Generate OpenAPI document\nconst openApiDocument = registry.getDefinitions();\n// Write to file or serve via API\n```", "testStrategy": "1. Verify documentation accuracy by comparing with actual API implementation.\n2. Test example code snippets to ensure they work as documented.\n3. Have external developers review documentation for clarity and completeness.\n4. Test documentation rendering in various formats (HTML, PDF, etc.).", "priority": "low", "dependencies": [5, 6, 7, 8, 11, 12], "status": "pending", "subtasks": []}, {"id": 15, "title": "Implement System Monitoring and Analytics", "description": "Develop monitoring and analytics capabilities for the subscription system to track performance, usage, and business metrics.", "details": "Create a comprehensive monitoring and analytics system that provides visibility into the subscription system's operation. The implementation should:\n\n1. Track key performance metrics (API response times, error rates)\n2. Monitor business metrics (conversion rates, subscription counts by plan)\n3. Provide alerting for critical issues\n4. Support debugging and troubleshooting\n\n```typescript\n// Monitoring service\nclass SubscriptionMonitoring {\n  // Track API request\n  trackApiRequest(endpoint: string, statusCode: number, responseTime: number): void {\n    // Implementation using metrics library\n    metrics.increment('api.requests', { endpoint, statusCode });\n    metrics.recordTiming('api.response_time', responseTime, { endpoint });\n  }\n  \n  // Track subscription events\n  trackSubscriptionEvent(event: 'created' | 'cancelled' | 'renewed', planId: string): void {\n    metrics.increment('subscription.events', { event, planId });\n  }\n  \n  // Track checkout conversion\n  trackCheckoutConversion(planId: string, successful: boolean): void {\n    metrics.increment('checkout.attempts', { planId });\n    if (successful) {\n      metrics.increment('checkout.conversions', { planId });\n    }\n  }\n  \n  // Get subscription analytics\n  async getSubscriptionAnalytics(timeframe: 'day' | 'week' | 'month'): Promise<SubscriptionAnalytics> {\n    // Query database or analytics service\n    // Return formatted analytics data\n  }\n}\n\n// API endpoint for analytics\napp.get('/api/admin/analytics/subscriptions', adminAuthMiddleware, async (req, res) => {\n  try {\n    const { timeframe = 'week' } = req.query;\n    const monitoring = new SubscriptionMonitoring();\n    const analytics = await monitoring.getSubscriptionAnalytics(timeframe as any);\n    return res.json(analytics);\n  } catch (error) {\n    console.error('Failed to retrieve subscription analytics:', error);\n    return res.status(500).json({ error: 'Failed to retrieve analytics' });\n  }\n});\n\n// Middleware for API monitoring\nfunction monitoringMiddleware(req, res, next) {\n  const startTime = Date.now();\n  \n  // Capture response\n  const originalSend = res.send;\n  res.send = function(body) {\n    const responseTime = Date.now() - startTime;\n    const monitoring = new SubscriptionMonitoring();\n    monitoring.trackApiRequest(req.path, res.statusCode, responseTime);\n    return originalSend.call(this, body);\n  };\n  \n  next();\n}\n```", "testStrategy": "1. Unit test monitoring functions with mock metrics service.\n2. Verify metrics are correctly recorded for various API operations.\n3. Test analytics calculations with sample data.\n4. Integration test with actual metrics and monitoring systems.", "priority": "low", "dependencies": [5, 6, 7, 8, 13], "status": "pending", "subtasks": []}], "metadata": {"created": "2025-07-22T06:59:29.581Z", "updated": "2025-07-22T07:01:44.399Z", "description": "Tasks for payment-integration context"}}}