# Task ID: 13
# Title: Implement Get Provider Services API
# Status: done
# Dependencies: 2, 3, 4
# Priority: high
# Description: Create an API endpoint to retrieve all services associated with the authenticated provider.
# Details:
1. Create a new API handler for GET /api/providers/services
2. Use authentication middleware to verify provider
3. Implement optional query parameters for filtering
4. Call existing Wasp operation to fetch provider services
5. Format response according to API standards
6. Add to main.wasp API definitions

```typescript
// In main.wasp
api {
  httpRoute: GET /api/providers/services,
  fn: import { getProviderServices } from "@server/api/providers/services",
  auth: true
}

// In @server/api/providers/services.ts
import { z } from 'zod';
import { getServicesByProviderId } from '../../../provider/operations';
import { validateRequest } from '../../common/validation';
import { handleApiError } from '../../common/errors';

const getServicesSchema = z.object({
  query: z.object({
    isActive: z.enum(['true', 'false']).optional(),
    categoryId: z.string().uuid().optional(),
    search: z.string().optional(),
    minDuration: z.string().transform(val => parseInt(val, 10)).optional(),
    maxDuration: z.string().transform(val => parseInt(val, 10)).optional()
  })
});

export const getProviderServices = [
  validateRequest(getServicesSchema),
  async (req, res) => {
    try {
      const providerId = req.providerId;
      const { isActive, categoryId, search, minDuration, maxDuration } = req.query;
      
      // Convert string query params to appropriate types
      const filters = {
        isActive: isActive ? isActive === 'true' : undefined,
        categoryId: categoryId || undefined,
        search: search || undefined,
        minDuration: minDuration || undefined,
        maxDuration: maxDuration || undefined
      };
      
      const services = await getServicesByProviderId(providerId, filters);
      
      return res.status(200).json({
        status: 'success',
        data: services
      });
    } catch (error) {
      return handleApiError(res, error);
    }
  }
];
```

# Test Strategy:
1. Test with provider having multiple services
2. Test with provider having no services
3. Test with various filter combinations
4. Test with authenticated non-provider user
5. Test with unauthenticated request
6. Verify response format matches API standards
7. Verify correct services are returned based on filters

# Subtasks:
## 1. Define API endpoint in main.wasp [done]
### Dependencies: None
### Description: Add the GET /api/providers/services endpoint definition to main.wasp file with proper authentication requirements
### Details:
Open main.wasp and add the API definition for the provider services endpoint. Ensure it's properly configured with authentication and imports the correct handler function. The endpoint should be defined as GET /api/providers/services and should reference the handler function from @server/api/providers/services.ts. Make sure to set auth: true to enforce authentication.

## 2. Create validation schema for request parameters [done]
### Dependencies: 13.1
### Description: Implement the Zod validation schema for query parameters including isActive, categoryId, search, and duration filters
### Details:
Create the getServicesSchema using Zod to validate incoming request query parameters. Define validation rules for: isActive (enum of 'true'/'false'), categoryId (UUID string), search (string), minDuration and maxDuration (strings transformed to integers). All parameters should be optional. This schema will be used by the validateRequest middleware to ensure proper request format.

## 3. Implement the getServicesByProviderId operation [done]
### Dependencies: 13.2
### Description: Create the database operation function that retrieves services filtered by provider ID and optional query parameters
### Details:
Create or update the provider operations file to implement the getServicesByProviderId function. This function should accept a providerId and a filters object containing the optional filter parameters (isActive, categoryId, search, minDuration, maxDuration). Use Prisma to query the database for services that match the provider ID and apply any additional filters. Return the filtered services array.

## 4. Implement the API request handler [done]
### Dependencies: 13.3
### Description: Create the getProviderServices handler function that processes the request, calls the operation, and formats the response
### Details:
Implement the getProviderServices handler array in @server/api/providers/services.ts. The handler should: 1) Use the validateRequest middleware with the getServicesSchema, 2) Extract the providerId from the authenticated request, 3) Parse and transform query parameters, 4) Call the getServicesByProviderId operation with the providerId and filters, 5) Format the response according to API standards with status and data fields, and 6) Use handleApiError for error handling.

## 5. Create error handling and response formatting utilities [done]
### Dependencies: 13.4
### Description: Implement or update utility functions for API error handling and response formatting
### Details:
Create or update the common utilities for API error handling and response formatting. Implement the handleApiError function in @server/common/errors.ts to properly format error responses with appropriate HTTP status codes. Ensure it handles different error types (validation errors, not found errors, server errors) with appropriate status codes and messages. Also, implement any needed response formatting utilities to ensure consistent API response structure.

