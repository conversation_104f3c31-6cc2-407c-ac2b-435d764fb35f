# Task ID: 27
# Title: Fix Failed Integration Tests for Provider Mobile API
# Status: done
# Dependencies: 26, 8, 12, 17, 23, 24
# Priority: medium
# Description: Resolve all failed integration tests for the Provider Mobile API by addressing data format issues, timeout handling, error handling, test data structures, status codes, and test dependencies.
# Details:
This task involves fixing multiple issues identified in the integration test suite:

1. Location Data Format Issues:
   - Update test fixtures and assertions to handle address field as a string instead of an object
   - Modify location-related API tests in `/tests/integration/provider/locations.test.ts`
   - Ensure all location creation/update tests use the correct data format

2. Phone OTP Timeout Handling:
   - Implement proper timeout handling in authentication tests
   - Add retry logic for OTP verification tests
   - Update test timeouts to accommodate network latency
   - Modify `/tests/integration/provider/auth.test.ts` to handle timeout scenarios

3. Profile Management Error Handling:
   - Enhance error assertions in profile management tests
   - Add test cases for edge cases (invalid data, unauthorized access)
   - Update expected error messages and status codes
   - Fix tests in `/tests/integration/provider/profile.test.ts`

4. Onboarding Setup Test Data Structure:
   - Restructure test fixtures for onboarding flow
   - Update test data to match current API expectations
   - Fix data dependencies between onboarding steps
   - Update tests in `/tests/integration/provider/onboarding.test.ts`

5. Validation Error Status Code Corrections:
   - Update expected status codes for validation errors (should be 400 not 422)
   - Ensure consistent error response format across all tests
   - Fix assertions in all API tests that check validation errors

6. Queue Management Test Dependencies:
   - Refactor queue tests to properly clean up test data
   - Fix order dependencies between queue-related tests
   - Ensure proper isolation between test cases
   - Update tests in `/tests/integration/provider/queues.test.ts`

Implementation approach:
- Create a separate fix for each category of issues
- Update test fixtures to use correct data formats
- Refactor test setup/teardown to ensure proper isolation
- Add more robust error handling and assertions
- Document any API behavior changes discovered during fixes

# Test Strategy:
1. Run the full integration test suite to identify all failing tests:
   ```bash
   npm run test:integration
   ```

2. For each category of issues:
   - Run the specific test file in isolation to verify fixes
   - Example: `npm run test:integration -- --testPathPattern=provider/locations.test.ts`
   - Check that tests pass with the expected assertions

3. Verify location data format fixes:
   - Confirm location creation tests pass with string address format
   - Verify location update operations maintain correct format

4. Test OTP timeout handling:
   - Run authentication tests multiple times to ensure reliability
   - Verify tests pass consistently even with network delays

5. Validate profile management error handling:
   - Trigger each error condition manually to verify correct handling
   - Check that appropriate error messages are displayed

6. Verify onboarding test data structure:
   - Run the complete onboarding flow test to ensure all steps work
   - Confirm test data is properly structured for each step

7. Check validation error status codes:
   - Verify all validation errors return 400 status codes
   - Confirm error response format is consistent

8. Test queue management dependencies:
   - Run queue tests in isolation and as part of the full suite
   - Verify no interdependencies between test cases

9. Run the complete integration test suite again to ensure all tests pass:
   ```bash
   npm run test:integration
   ```

10. Document any API behavior changes or inconsistencies discovered during testing

# Subtasks:
## 1. Fix Location Address Format in Test Data [done]
### Dependencies: None
### Description: Update test fixtures and assertions to handle location address as a string instead of an object in the Provider Mobile API integration tests.
### Details:
1. Modify the test data generators in `/tests/fixtures/location.ts` to use string format for addresses
2. Update assertions in `/tests/integration/provider/locations.test.ts` to validate the correct string format
3. Fix all location creation and update tests to use the correct data format
4. Ensure any mock responses also return the correct address format

## 2. Implement Phone OTP Timeout Handling [done]
### Dependencies: None
### Description: Enhance authentication tests to properly handle OTP timeouts and network latency issues.
### Details:
1. Modify `/tests/integration/provider/auth.test.ts` to implement retry logic for OTP verification
2. Increase test timeouts to accommodate potential network latency
3. Add proper error handling for timeout scenarios
4. Implement test helpers for authentication that handle potential timeout issues

## 3. Fix Profile Management Error Handling [done]
### Dependencies: None
### Description: Enhance error assertions and add edge case tests for the profile management API tests.
### Details:
1. Update error assertions in `/tests/integration/provider/profile.test.ts`
2. Add test cases for invalid data submissions
3. Add test cases for unauthorized access scenarios
4. Update expected error messages and status codes to match current API behavior
5. Implement more robust error checking in profile-related tests

## 4. Restructure Onboarding Test Data [done]
### Dependencies: None
### Description: Update the test fixtures and data structure for the provider onboarding flow tests.
### Details:
1. Restructure test fixtures in `/tests/fixtures/onboarding.ts` to match current API expectations
2. Fix data dependencies between onboarding steps in `/tests/integration/provider/onboarding.test.ts`
3. Update test assertions to validate the correct response formats
4. Ensure test data is properly isolated between test runs

## 5. Update Validation Error Status Codes [done]
### Dependencies: 27.1, 27.3, 27.4
### Description: Correct expected status codes for validation errors across all integration tests.
### Details:
1. Update all test assertions expecting status code 422 to instead expect 400 for validation errors
2. Ensure consistent validation error response format is expected across all tests
3. Update any custom test helpers or utilities that check for validation errors
4. Document the status code change in test comments for future reference

## 6. Fix Queue Management Test Dependencies [done]
### Dependencies: None
### Description: Refactor queue-related tests to ensure proper isolation and cleanup of test data.
### Details:
1. Update tests in `/tests/integration/provider/queues.test.ts` to properly clean up test data after each test
2. Fix order dependencies between queue-related tests
3. Implement better test isolation to prevent tests from affecting each other
4. Add proper setup and teardown procedures for queue test data

## 7. Standardize Error Response Format Assertions [done]
### Dependencies: 27.3, 27.5
### Description: Ensure consistent error response format checking across all integration tests.
### Details:
1. Create a shared utility function for validating error response formats
2. Update all tests to use this shared utility for error validation
3. Ensure all tests expect the same error response structure
4. Document the expected error format in test utilities for future reference

## 8. Run Comprehensive Test Validation [done]
### Dependencies: 27.1, 27.2, 27.3, 27.4, 27.5, 27.6, 27.7
### Description: Perform a full validation of all fixed integration tests to ensure they pass consistently.
### Details:
1. Run the complete integration test suite multiple times to ensure consistent results
2. Document any remaining flaky tests or issues discovered
3. Verify that all test categories (locations, auth, profile, onboarding, queues) pass reliably
4. Create a summary report of fixed issues and any remaining concerns

