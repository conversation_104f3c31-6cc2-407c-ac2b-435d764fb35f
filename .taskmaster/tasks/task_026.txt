# Task ID: 26
# Title: Refactor Provider Mobile API Integration Tests for Authentication Flow
# Status: pending
# Dependencies: 2, 5, 6, 7, 9, 13, 23, 24, 25
# Priority: medium
# Description: Refactor the existing Provider Mobile API integration tests to properly handle authentication flow, user registration, login, and JWT token management to align with the implemented API endpoints.
# Details:
This task involves a comprehensive refactoring of the Provider Mobile API integration tests to ensure they properly test the authentication flow and align with existing API endpoints:

1. Analyze the current authentication flow in the implemented APIs:
   - Review the provider registration process
   - Examine the login flow and JWT token generation
   - Understand how token refresh works
   - Map out the complete provider setup workflow

2. Refactor the test suite structure:
   - Create a proper test setup module that handles authentication
   - Implement helper functions for registration, login, and token management
   - Organize tests in a logical sequence that follows the user journey

3. Implement proper test fixtures:
   - Create reusable fixtures for authenticated provider sessions
   - Implement test data generators for provider profiles, locations, and services
   - Set up cleanup routines to ensure test isolation

4. Update test cases for authentication-related endpoints:
   - Provider registration
   - Provider login
   - Password reset flow
   - Token refresh
   - Complete setup workflow (Task 7)

5. Ensure tests for authenticated endpoints properly handle JWT:
   - Get Provider Profile (Task 5)
   - Update Provider Profile (Task 6)
   - Get Provider Locations (Task 9)
   - Get Provider Services (Task 13)
   - Customer Management APIs (Task 23)
   - Appointment Management APIs (Task 24)
   - Reschedule Management APIs (Task 25)

6. Implement proper error case testing:
   - Test invalid credentials scenarios
   - Test expired token handling
   - Test unauthorized access attempts
   - Test malformed request handling

7. Update documentation:
   - Add comments explaining the test strategy
   - Document the authentication helper functions
   - Update the README with instructions for running the tests

Sample code for authentication helper:
```typescript
// test/helpers/auth.ts
export async function registerAndLoginProvider() {
  // Generate unique test provider
  const testProvider = generateTestProvider();
  
  // Register the provider
  const registerResponse = await request(app)
    .post('/api/auth/provider/register')
    .send(testProvider);
  
  // Login with the provider
  const loginResponse = await request(app)
    .post('/api/auth/provider/login')
    .send({
      email: testProvider.email,
      password: testProvider.password
    });
  
  // Extract and return the JWT token
  return {
    provider: testProvider,
    token: loginResponse.body.token
  };
}

// Example test using the helper
export async function setupAuthenticatedTest() {
  const { provider, token } = await registerAndLoginProvider();
  return {
    provider,
    authHeader: { Authorization: `Bearer ${token}` }
  };
}
```

# Test Strategy:
To verify the successful refactoring of the Provider Mobile API integration tests:

1. Run the complete test suite to ensure all tests pass:
   - Execute `npm run test:integration` and verify no tests fail
   - Check that tests properly authenticate before making API calls
   - Confirm tests handle token expiration and refresh correctly

2. Verify test coverage for authentication flows:
   - Run test coverage report and ensure authentication endpoints have >90% coverage
   - Confirm both success and failure paths are tested
   - Verify edge cases like token expiration are covered

3. Test the registration flow:
   - Verify tests for new user registration pass
   - Confirm validation error tests pass
   - Check duplicate email handling tests pass

4. Test the login flow:
   - Verify successful login tests pass and return valid JWT
   - Confirm invalid credential tests properly handle errors
   - Check that token refresh tests work correctly

5. Test authenticated API access:
   - Verify tests for all provider endpoints use proper authentication
   - Confirm unauthorized access tests fail appropriately
   - Check that expired token tests handle refresh correctly

6. Perform manual verification:
   - Review test logs to ensure proper authentication flow
   - Check database state after tests to verify proper cleanup
   - Confirm test isolation (tests don't interfere with each other)

7. Validate against API documentation:
   - Compare test coverage against API documentation
   - Ensure all documented endpoints have corresponding tests
   - Verify all authentication requirements are tested

8. Code review:
   - Have another developer review the test refactoring
   - Ensure best practices for testing are followed
   - Confirm helper functions are well-documented

# Subtasks:
## 1. Analyze Current Authentication API Implementation [done]
### Dependencies: None
### Description: Review and document the existing authentication flow in the implemented APIs to understand the current implementation before refactoring tests.
### Details:
Examine the codebase to identify all authentication-related endpoints and their functionality. Document the provider registration process, login flow, JWT token generation and validation, token refresh mechanism, and the complete provider setup workflow. Create a flow diagram showing the relationships between these endpoints and the expected request/response patterns.

## 2. Create Authentication Helper Module [done]
### Dependencies: 26.1
### Description: Implement a reusable authentication helper module that handles provider registration, login, and JWT token management for tests.
### Details:
Create a new file `test/helpers/auth.ts` with functions for: 1) Registering a new test provider, 2) Logging in with existing credentials, 3) Refreshing expired tokens, 4) Setting up an authenticated test environment, and 5) Cleaning up test data. Implement the `registerAndLoginProvider()` and `setupAuthenticatedTest()` functions as shown in the sample code.

## 3. Implement Test Data Generators [done]
### Dependencies: 26.1
### Description: Create utility functions to generate test data for provider profiles, locations, and services.
### Details:
Create a new file `test/helpers/testData.ts` with functions that generate random but valid test data for: 1) Provider registration information, 2) Provider profile details, 3) Provider locations, and 4) Provider services. Ensure generated data meets all validation requirements of the API. Include options to override specific fields for test customization.

## 4. Refactor Registration and Login Test Cases [done]
### Dependencies: 26.2, 26.3
### Description: Update test cases for provider registration, login, password reset, and token refresh endpoints.
### Details:
Refactor tests for `/api/auth/provider/register`, `/api/auth/provider/login`, password reset endpoints, and token refresh endpoints. Use the new helper functions and test data generators. Include positive test cases (successful registration/login) and negative test cases (duplicate email, invalid credentials, etc.). Ensure tests verify the correct response structure and status codes.

## 5. Refactor Provider Profile Test Cases [done]
### Dependencies: 26.2, 26.3, 26.4
### Description: Update test cases for authenticated provider profile endpoints using the new authentication helpers.
### Details:
Refactor tests for Get Provider Profile and Update Provider Profile endpoints. Use the authentication helpers to set up an authenticated test environment before making requests. Verify that profile data can be retrieved and updated correctly when authenticated, and that appropriate errors are returned for unauthenticated requests.

## 6. Refactor Provider Locations and Services Test Cases [pending]
### Dependencies: 26.2, 26.3, 26.5
### Description: Update test cases for provider locations and services endpoints to use the new authentication flow.
### Details:
Refactor tests for Get Provider Locations and Get Provider Services endpoints. Ensure tests first authenticate as a provider before attempting to access these endpoints. Verify that location and service data is correctly associated with the authenticated provider and that appropriate errors are returned for unauthenticated requests.

## 7. Implement Comprehensive Error Case Testing [pending]
### Dependencies: 26.4, 26.5, 26.6
### Description: Enhance test coverage by adding specific test cases for error scenarios in the authentication flow.
### Details:
Add test cases for: 1) Invalid credentials during login, 2) Expired JWT token handling, 3) Malformed JWT tokens, 4) Missing authentication headers, 5) Insufficient permissions, and 6) Rate limiting if implemented. Verify that appropriate error responses (status codes and messages) are returned in each case.

## 8. Update Test Documentation and README [pending]
### Dependencies: 26.2, 26.3, 26.4, 26.5, 26.6, 26.7
### Description: Document the refactored test suite structure, authentication helpers, and update the README with instructions for running tests.
### Details:
Add JSDoc comments to all helper functions explaining their purpose, parameters, and return values. Create a dedicated TESTING.md document explaining the test strategy, authentication flow, and how to run different test suites. Update the project README with a section on testing that references the TESTING.md file. Include examples of how to run specific test suites and how to troubleshoot common issues.

