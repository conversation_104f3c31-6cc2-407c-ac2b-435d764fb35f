# Task ID: 10
# Title: Implement Update Location API
# Status: done
# Dependencies: 2, 3, 4, 8
# Priority: medium
# Description: Create an API endpoint to update an existing provider location.
# Details:
1. Create a new API handler for PATCH /api/providers/locations/:id
2. Use authentication middleware to verify provider
3. Implement Zod schema for location update validation
4. Verify provider owns the location
5. Call existing Wasp operation to update location
6. Format response according to API standards
7. Add to main.wasp API definitions

```typescript
// In main.wasp
api {
  httpRoute: PATCH /api/providers/locations/:id,
  fn: import { updateLocation } from "@server/api/providers/locations",
  auth: true
}

// In @server/api/providers/locations.ts
import { z } from 'zod';
import { updateProviderLocation, getLocationById } from '../../../provider/operations';
import { validateRequest } from '../../common/validation';
import { handleApiError } from '../../common/errors';

const updateLocationSchema = z.object({
  params: z.object({
    id: z.string().uuid()
  }),
  body: z.object({
    name: z.string().min(1).optional(),
    address: z.string().min(1).optional(),
    city: z.string().min(1).optional(),
    state: z.string().optional(),
    zipCode: z.string().optional(),
    country: z.string().min(1).optional(),
    phone: z.string().optional(),
    email: z.string().email().optional(),
    isDefault: z.boolean().optional(),
    isActive: z.boolean().optional(),
    coordinates: z.object({
      latitude: z.number(),
      longitude: z.number()
    }).optional()
  })
});

export const updateLocation = [
  validateRequest(updateLocationSchema),
  async (req, res) => {
    try {
      const providerId = req.providerId;
      const locationId = req.params.id;
      const updateData = req.body;
      
      // Verify provider owns this location
      const existingLocation = await getLocationById(locationId);
      if (!existingLocation || existingLocation.providerId !== providerId) {
        return res.status(404).json({
          status: 'error',
          message: 'Location not found'
        });
      }
      
      const updatedLocation = await updateProviderLocation({
        id: locationId,
        ...updateData
      });
      
      return res.status(200).json({
        status: 'success',
        data: updatedLocation
      });
    } catch (error) {
      return handleApiError(res, error);
    }
  }
];
```

# Test Strategy:
1. Test with valid update data
2. Test with invalid data to verify validation
3. Test with location not owned by provider
4. Test with non-existent location ID
5. Test with authenticated non-provider user
6. Verify location is updated correctly in database
7. Test default location flag behavior (if one location is set as default, others should not be)

# Subtasks:
## 1. Define API route in main.wasp [done]
### Dependencies: None
### Description: Add the PATCH /api/providers/locations/:id endpoint definition to the main.wasp file
### Details:
Add the API route definition to main.wasp using the Wasp syntax for API routes. Specify the HTTP method as PATCH, the route path as /api/providers/locations/:id, the handler function import path, and set auth to true to ensure the endpoint is protected.

## 2. Create Zod validation schema [done]
### Dependencies: 10.1
### Description: Implement the Zod schema for validating location update requests
### Details:
Create the updateLocationSchema in the locations.ts file that validates both the request params (ensuring id is a valid UUID) and the request body (validating optional fields like name, address, coordinates, etc.). Ensure all fields have appropriate validation rules (min length, email format, etc.).

## 3. Implement location ownership verification [done]
### Dependencies: 10.2
### Description: Create the logic to verify that the provider owns the location they're trying to update
### Details:
Use the getLocationById operation to fetch the location by ID, then compare the location's providerId with the authenticated provider's ID from the request. Return a 404 error if the location doesn't exist or doesn't belong to the provider.

## 4. Implement update location handler [done]
### Dependencies: 10.3
### Description: Complete the updateLocation handler function to process valid requests
### Details:
After validation and ownership verification, call the updateProviderLocation operation with the location ID and update data. Format the response according to API standards with a success status and the updated location data. Implement proper error handling using the handleApiError utility.

## 5. Add integration tests for the API endpoint [done]
### Dependencies: 10.4
### Description: Create comprehensive tests for the update location API endpoint
### Details:
Create integration tests that cover the full functionality of the endpoint, including authentication, validation, authorization (ownership verification), successful updates, and error handling. Mock the necessary dependencies and test both successful and failure scenarios.

