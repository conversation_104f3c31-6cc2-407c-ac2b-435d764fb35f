# Task ID: 14
# Title: Implement Update Service API
# Status: done
# Dependencies: 2, 3, 4, 12
# Priority: medium
# Description: Create an API endpoint to update an existing provider service.
# Details:
1. Create a new API handler for PATCH /api/providers/services/:id
2. Use authentication middleware to verify provider
3. Implement Zod schema for service update validation
4. Verify provider owns the service
5. Call existing Wasp operation to update service
6. Format response according to API standards
7. Add to main.wasp API definitions

```typescript
// In main.wasp
api {
  httpRoute: PATCH /api/providers/services/:id,
  fn: import { updateService } from "@server/api/providers/services",
  auth: true
}

// In @server/api/providers/services.ts
import { z } from 'zod';
import { updateProviderService, getServiceById } from '../../../provider/operations';
import { validateRequest } from '../../common/validation';
import { handleApiError } from '../../common/errors';

const updateServiceSchema = z.object({
  params: z.object({
    id: z.string().uuid()
  }),
  body: z.object({
    name: z.string().min(1).optional(),
    description: z.string().optional(),
    duration: z.number().int().positive().optional(),
    price: z.number().nonnegative().optional(),
    currency: z.string().optional(),
    categoryId: z.string().uuid().optional().nullable(),
    isActive: z.boolean().optional(),
    color: z.string().optional(),
    icon: z.string().optional()
  })
});

export const updateService = [
  validateRequest(updateServiceSchema),
  async (req, res) => {
    try {
      const providerId = req.providerId;
      const serviceId = req.params.id;
      const updateData = req.body;
      
      // Verify provider owns this service
      const existingService = await getServiceById(serviceId);
      if (!existingService || existingService.providerId !== providerId) {
        return res.status(404).json({
          status: 'error',
          message: 'Service not found'
        });
      }
      
      const updatedService = await updateProviderService({
        id: serviceId,
        ...updateData
      });
      
      return res.status(200).json({
        status: 'success',
        data: updatedService
      });
    } catch (error) {
      return handleApiError(res, error);
    }
  }
];
```

# Test Strategy:
1. Test with valid update data
2. Test with invalid data to verify validation
3. Test with service not owned by provider
4. Test with non-existent service ID
5. Test with authenticated non-provider user
6. Verify service is updated correctly in database
7. Test category association changes

# Subtasks:
## 1. Define API endpoint in main.wasp [done]
### Dependencies: None
### Description: Add the PATCH /api/providers/services/:id endpoint definition to main.wasp file
### Details:
Add the API endpoint definition to main.wasp file using the Wasp API syntax. The endpoint should use PATCH method, point to the correct handler function that will be implemented in a separate file, and require authentication.

## 2. Create validation schema for service updates [done]
### Dependencies: 14.1
### Description: Implement the Zod validation schema for validating service update requests
### Details:
Create the updateServiceSchema using Zod to validate both the request params (service ID) and the request body (fields that can be updated). The schema should validate that the ID is a valid UUID and that all update fields meet the required constraints (e.g., name length, positive duration, non-negative price).

## 3. Implement service ownership verification [done]
### Dependencies: 14.2
### Description: Create a function to verify that the provider making the request owns the service being updated
### Details:
Implement the logic to fetch the existing service by ID using getServiceById and verify that the providerId of the service matches the authenticated provider's ID. This ensures providers can only update their own services.

## 4. Implement the service update handler function [done]
### Dependencies: 14.3
### Description: Create the main handler function that processes the update request and calls the updateProviderService operation
### Details:
Implement the updateService handler function that combines the validation middleware with the main request handler. The handler should extract the service ID from params, update data from body, verify ownership, call the updateProviderService operation, and format the response according to API standards.

## 5. Add error handling and response formatting [done]
### Dependencies: 14.4
### Description: Implement proper error handling and response formatting for the update service endpoint
### Details:
Enhance the handler with comprehensive error handling using the handleApiError utility. Ensure all responses follow the API standards with appropriate status codes (200 for success, 404 for not found, 400 for validation errors, etc.) and consistent response format with status and data/message fields.

