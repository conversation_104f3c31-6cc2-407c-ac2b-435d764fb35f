# Task ID: 8
# Title: Implement Create Location API
# Status: done
# Dependencies: 2, 3, 4
# Priority: high
# Description: Create an API endpoint to allow providers to create new providing places/locations.
# Details:
1. Create a new API handler for POST /api/providers/locations
2. Use authentication middleware to verify provider
3. Implement Zod schema for location creation validation
4. Call existing Wasp operation to create location
5. Format response according to API standards
6. Add to main.wasp API definitions

```typescript
// In main.wasp
api {
  httpRoute: POST /api/providers/locations,
  fn: import { createLocation } from "@server/api/providers/locations",
  auth: true
}

// In @server/api/providers/locations.ts
import { z } from 'zod';
import { createProviderLocation } from '../../../provider/operations';
import { validateRequest } from '../../common/validation';
import { handleApiError } from '../../common/errors';

const createLocationSchema = z.object({
  body: z.object({
    name: z.string().min(1),
    address: z.string().min(1),
    city: z.string().min(1),
    state: z.string().optional(),
    zipCode: z.string().optional(),
    country: z.string().min(1),
    phone: z.string().optional(),
    email: z.string().email().optional(),
    isDefault: z.boolean().optional(),
    coordinates: z.object({
      latitude: z.number(),
      longitude: z.number()
    }).optional()
  })
});

export const createLocation = [
  validateRequest(createLocationSchema),
  async (req, res) => {
    try {
      const providerId = req.providerId;
      const locationData = req.body;
      
      const newLocation = await createProviderLocation({
        providerId,
        ...locationData
      });
      
      return res.status(201).json({
        status: 'success',
        data: newLocation
      });
    } catch (error) {
      return handleApiError(res, error);
    }
  }
];
```

# Test Strategy:
1. Test with valid location data
2. Test with missing required fields
3. Test with authenticated non-provider user
4. Test with unauthenticated request
5. Verify location is created in database with correct provider association
6. Verify response format matches API standards
7. Test default location flag behavior

# Subtasks:
## 1. Define API route in main.wasp [done]
### Dependencies: None
### Description: Add the API endpoint definition to main.wasp to register the new location creation endpoint
### Details:
Add the API route definition to main.wasp file with the correct path, handler function import, and authentication requirement. The route should be POST /api/providers/locations and should import the createLocation handler from @server/api/providers/locations.

## 2. Create Zod validation schema [done]
### Dependencies: 8.1
### Description: Implement the Zod schema for validating location creation requests
### Details:
Create the createLocationSchema in @server/api/providers/locations.ts that validates the request body. The schema should validate required fields (name, address, city, country) and optional fields (state, zipCode, phone, email, isDefault, coordinates). Ensure proper validation rules are applied (e.g., email format, minimum string lengths).

## 3. Implement request validation middleware [done]
### Dependencies: 8.2
### Description: Create or reuse a validation middleware that uses the Zod schema to validate incoming requests
### Details:
Implement the validateRequest middleware in @server/common/validation.ts (if not already existing) that takes a Zod schema and returns an Express middleware function. The middleware should validate the request against the schema and pass control to the next middleware if validation succeeds, or return a 400 error with validation details if it fails.

## 4. Implement error handling utility [done]
### Dependencies: 8.3
### Description: Create or reuse an error handling utility to standardize API error responses
### Details:
Implement the handleApiError function in @server/common/errors.ts that takes a response object and an error, and returns an appropriate HTTP response. The function should handle different types of errors (validation errors, database errors, etc.) and format them according to the API standards with appropriate status codes.

## 5. Implement the createLocation handler [done]
### Dependencies: 8.1, 8.2, 8.3, 8.4
### Description: Implement the main handler function for the create location API endpoint
### Details:
Complete the implementation of the createLocation handler in @server/api/providers/locations.ts. The handler should extract the provider ID from the authenticated request, call the createProviderLocation operation with the validated request data, and return a 201 response with the newly created location. Ensure proper error handling using the handleApiError utility.

