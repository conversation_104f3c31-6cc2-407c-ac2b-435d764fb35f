{"tasks": [{"id": 1, "title": "Setup API Structure and Base Configuration", "description": "Create the foundational structure for the Provider Mobile API implementation following Wasp framework patterns and conventions.", "details": "1. Review existing API handler patterns in the codebase\n2. Create a directory structure for new API handlers\n3. Setup base configuration files\n4. Define common utility functions for API responses\n5. Implement error handling middleware\n6. Create TypeScript interfaces for API responses\n\nExample folder structure:\n```\n/api\n  /providers\n    /profile\n    /locations\n    /services\n    /queues\n    /schedules\n    /customers\n    /appointments\n    /reschedules\n  /common\n    /middleware\n    /utils\n```", "testStrategy": "1. Verify directory structure matches existing patterns\n2. Ensure utility functions work as expected\n3. Test error handling middleware with sample errors\n4. Validate TypeScript interfaces compile correctly", "priority": "high", "dependencies": [], "status": "done", "subtasks": [{"id": 1, "title": "Create API Directory Structure and Base Configuration Files", "description": "Set up the foundational directory structure for the Provider Mobile API implementation and create necessary base configuration files following Wasp framework patterns.", "dependencies": [], "details": "1. Create the main directory structure as outlined in the example, including /api/providers with subdirectories for profile, locations, services, queues, schedules, customers, appointments, and reschedules\n2. Create /api/common/middleware and /api/common/utils directories\n3. Set up base configuration files including tsconfig extensions for the API\n4. Create initial .cursorrules file with Wasp-specific guidelines\n5. Add necessary package.json dependencies for API development\n<info added on 2025-06-11T15:32:24.782Z>\n6. Successfully fixed TypeScript errors in Provider Mobile API implementation:\n   - Resolved context type mismatch using 'any' type for context parameter\n   - Fixed null vs undefined type issues with null coalescing operator\n   - Corrected missing category property by using providerCategoryId\n   - Resolved Zod schema partial method issue by manually defining updateServiceSchema\n   - Addressed port conflict between client and server (both using port 5400)\n   - Ensured all API endpoints are properly defined in main.wasp with correct entities and auth\n</info added on 2025-06-11T15:32:24.782Z>", "status": "done", "testStrategy": "Verify directory structure is correctly created and configuration files are properly set up with appropriate settings for Wasp framework."}, {"id": 2, "title": "Implement Common Utility Functions and TypeScript Interfaces", "description": "Create reusable utility functions and TypeScript interfaces that will be used across the Provider Mobile API implementation.", "dependencies": [1], "details": "1. Create ResponseUtils.ts in /api/common/utils with functions for standardized API responses (success, error, etc.)\n2. Implement TypeScript interfaces for API request and response objects in /api/common/types/ApiTypes.ts\n3. Create utility functions for data validation and transformation\n4. Implement date/time handling utilities specific to provider operations\n5. Create constants file for API status codes and error messages", "status": "done", "testStrategy": "Write unit tests for utility functions to ensure they handle various input cases correctly and return expected formatted responses."}, {"id": 3, "title": "Develop Error Handling Middleware", "description": "Implement middleware for consistent error handling across all Provider Mobile API endpoints.", "dependencies": [2], "details": "1. Create ErrorHandlingMiddleware.ts in /api/common/middleware\n2. Implement middleware to catch and format errors from API handlers\n3. Create custom error classes for different types of API errors (validation, authentication, not found, etc.)\n4. Implement logging functionality for API errors\n5. Create middleware registration function to be used in API setup", "status": "done", "testStrategy": "Test middleware with various error scenarios to ensure proper error catching, formatting, and response generation. Verify error logging works as expected."}, {"id": 4, "title": "Define API Routes in main.wasp", "description": "Configure the API routes in the main.wasp file following Wasp framework conventions for the Provider Mobile API.", "dependencies": [3], "details": "1. Add API endpoint definitions in main.wasp for each provider module (profile, locations, services, etc.)\n2. Configure proper HTTP methods (GET, POST, PUT, DELETE) for each endpoint\n3. Set up authentication requirements for protected routes\n4. Define entity relationships needed for the API\n5. Configure middleware application for the API routes", "status": "done", "testStrategy": "Verify that routes are correctly defined in main.wasp and that the Wasp compiler successfully processes the configuration without errors."}, {"id": 5, "title": "Create Base API Handler Templates", "description": "Implement template handlers for each API category to establish patterns for the full implementation.", "dependencies": [4], "details": "1. Create base handler files for each API category (profile, locations, services, etc.)\n2. Implement a sample GET endpoint for each category following established patterns\n3. Set up proper imports and exports for all handlers\n4. Integrate error handling middleware in each handler\n5. Add documentation comments explaining the purpose and usage of each handler", "status": "done", "testStrategy": "Test each sample endpoint to ensure it correctly integrates with the middleware, follows the established patterns, and returns properly formatted responses."}]}, {"id": 2, "title": "Implement Authentication Middleware for Provider APIs", "description": "Create authentication middleware to ensure all Provider APIs require proper authentication and authorization.", "details": "1. Review existing authentication system\n2. Create middleware that verifies JWT tokens\n3. Extract provider ID from authenticated user\n4. Implement provider ownership verification function\n5. Create utility to check if user has provider role\n\n```typescript\n// Example middleware implementation\nexport const requireProviderAuth = async (req, res, next) => {\n  try {\n    // Verify authentication token\n    const user = req.user;\n    if (!user) {\n      return res.status(401).json({ message: 'Authentication required' });\n    }\n    \n    // Verify user has provider role\n    const isProvider = await checkProviderRole(user.id);\n    if (!isProvider) {\n      return res.status(403).json({ message: 'Provider access required' });\n    }\n    \n    // Add provider info to request object\n    req.providerId = await getProviderIdFromUserId(user.id);\n    next();\n  } catch (error) {\n    return res.status(500).json({ message: 'Authentication error', error: error.message });\n  }\n};\n```", "testStrategy": "1. Test middleware with valid provider authentication\n2. Test middleware with invalid authentication\n3. Test middleware with non-provider user\n4. Verify provider ID is correctly attached to request\n5. Test error handling scenarios", "priority": "high", "dependencies": [1], "status": "done", "subtasks": [{"id": 1, "title": "Create JWT Token Verification Middleware", "description": "Implement middleware function to verify JWT tokens in request headers and extract user information.", "dependencies": [], "details": "Create a new file `src/auth/middleware.ts` that exports a middleware function to verify JWT tokens. The function should:\n1. Extract the JWT token from the Authorization header\n2. Verify the token using the JWT library\n3. Decode the token to get user information\n4. Attach the user object to the request\n5. Handle errors appropriately with proper status codes", "status": "done", "testStrategy": "Write unit tests using Jest to test the middleware with valid and invalid tokens, missing tokens, and expired tokens."}, {"id": 2, "title": "Implement Provider Role Verification Function", "description": "Create a utility function that checks if a user has the provider role.", "dependencies": [], "details": "Create a new file `src/auth/providerUtils.ts` that exports a function `checkProviderRole(userId: string): Promise<boolean>` which:\n1. Queries the database to find the user by ID\n2. Checks if the user has the provider role in their roles array\n3. Returns true if the user is a provider, false otherwise\n4. Handles potential database errors", "status": "done", "testStrategy": "Write unit tests with mocked database queries to test various scenarios including users with provider role, without provider role, and non-existent users."}, {"id": 3, "title": "Create Provider ID Retrieval Function", "description": "Implement a function to get the provider ID associated with a user ID.", "dependencies": [], "details": "Add a function `getProviderIdFromUserId(userId: string): Promise<string | null>` to `src/auth/providerUtils.ts` that:\n1. Queries the database to find the Provider entity associated with the user ID\n2. Returns the provider ID if found, or null if not found\n3. <PERSON>les potential database errors\n4. Uses proper Prisma queries following Wasp patterns", "status": "done", "testStrategy": "Test with mocked database responses for users with and without associated provider records."}, {"id": 4, "title": "Implement Complete Provider Authentication Middleware", "description": "Create the main provider authentication middleware that combines token verification, role checking, and provider ID retrieval.", "dependencies": [], "details": "Create a new file `src/auth/providerMiddleware.ts` that exports the `requireProviderAuth` middleware function which:\n1. Uses the JWT verification middleware\n2. Checks if the authenticated user has the provider role\n3. Retrieves and attaches the provider ID to the request object\n4. Implements proper error handling with appropriate HTTP status codes\n5. Follows the example implementation pattern provided in the task", "status": "done", "testStrategy": "Write integration tests that verify the complete middleware flow with various scenarios including valid providers, non-providers, and unauthenticated requests."}, {"id": 5, "title": "Apply Provider Authentication to API Routes", "description": "Update the main.wasp file to apply the provider authentication middleware to all provider-specific API endpoints.", "dependencies": [], "details": "1. Modify the main.wasp file to import and use the provider authentication middleware\n2. Apply the middleware to all provider-specific API routes\n3. Update any existing provider API handlers to use the provider ID from the request object\n4. Create a middleware registration pattern that can be reused across multiple routes\n5. Ensure proper TypeScript types are maintained throughout the request pipeline", "status": "done", "testStrategy": "Write end-to-end tests for provider API endpoints to verify authentication is properly enforced, with tests for successful authentication and various failure scenarios."}]}, {"id": 3, "title": "Create Validation Utilities Using <PERSON><PERSON>", "description": "Implement validation utilities using Zod schemas to validate API request inputs across all Provider endpoints.", "details": "1. Create a validation middleware using Zod\n2. Define common validation schemas for reuse\n3. Implement error formatting for validation failures\n4. Create utility functions for common validation patterns\n\n```typescript\n// Example validation middleware\nimport { z } from 'zod';\n\nexport const validateRequest = (schema) => (req, res, next) => {\n  try {\n    schema.parse({\n      body: req.body,\n      query: req.query,\n      params: req.params\n    });\n    next();\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return res.status(400).json({\n        status: 'error',\n        message: 'Invalid request data',\n        errors: error.errors.map(e => ({\n          path: e.path.join('.'),\n          message: e.message\n        }))\n      });\n    }\n    return res.status(500).json({ message: 'Internal validation error' });\n  }\n};\n\n// Example common schemas\nexport const idSchema = z.object({\n  params: z.object({\n    id: z.string().uuid()\n  })\n});\n```", "testStrategy": "1. Test validation middleware with valid inputs\n2. Test validation middleware with invalid inputs\n3. Verify error responses match expected format\n4. Test common validation schemas with edge cases\n5. Verify validation works with different HTTP methods", "priority": "high", "dependencies": [1], "status": "done", "subtasks": [{"id": 1, "title": "Create Base Validation Middleware", "description": "Implement the core validation middleware function that will be used across all Provider endpoints to validate request data using Zod schemas.", "dependencies": [], "details": "Create a new file at `src/utils/validation.ts` that exports a `validateRequest` middleware function. This function should accept a Zod schema and return an Express middleware that validates request data (body, query, params) against the schema. On validation failure, it should return a properly formatted 400 response with detailed error information. On success, it should call next() to continue request processing.", "status": "done", "testStrategy": "Write unit tests using <PERSON><PERSON> to verify the middleware correctly validates valid requests and properly formats error responses for invalid requests with different types of validation errors."}, {"id": 2, "title": "Define Common Validation Schemas", "description": "Create a library of reusable Zod schemas for common validation patterns used across Provider endpoints.", "dependencies": [1], "details": "Create a new file at `src/utils/validationSchemas.ts` that exports common validation schemas including: idSchema (for UUID validation), paginationSchema (for limit/offset), dateRangeSchema, and other patterns specific to your Provider entities. Each schema should be well-documented with JSDoc comments explaining its purpose and usage. Import the Zod library and define type-safe schemas that can be composed together for different API endpoints.", "status": "done", "testStrategy": "Write unit tests to verify each schema correctly validates valid inputs and rejects invalid inputs with appropriate error messages."}, {"id": 3, "title": "Implement Error Formatting Utilities", "description": "Create utility functions to standardize error formatting for validation failures across the application.", "dependencies": [1], "details": "Extend the validation.ts file to include dedicated error formatting functions. Create a `formatZodError` function that transforms Zod validation errors into a consistent, user-friendly format. Also implement a `createValidationErrorResponse` function that generates standardized API error responses. These utilities should ensure consistent error handling across all endpoints and make error messages clear and actionable for API consumers.", "status": "done", "testStrategy": "Test the error formatting functions with various Zod error scenarios to ensure they produce consistent, well-structured error messages that follow your API's error format conventions."}, {"id": 4, "title": "Create Specialized Validation Utilities", "description": "Implement specialized validation utility functions for common validation patterns specific to the Provider domain.", "dependencies": [2], "details": "Create a new file at `src/utils/domainValidation.ts` that implements domain-specific validation utilities. Include functions like `validateProviderFields`, `validateServiceAvailability`, and other domain-specific validations. These utilities should leverage the base Zod schemas but add business logic validation specific to your application domain. Each utility should be strongly typed using TypeScript and follow the Wasp framework patterns.", "status": "done", "testStrategy": "Create comprehensive tests for each domain validation utility, including edge cases specific to your business domain. Verify that business rules are correctly enforced beyond simple type checking."}, {"id": 5, "title": "Integrate Validation with API Routes", "description": "Apply the validation middleware to all Provider API endpoints in the application.", "dependencies": [1, 2, 3, 4], "details": "Update all API route handlers to use the validation middleware. For each endpoint in your Provider API, identify the appropriate validation schema (using the common schemas where possible) and apply the validateRequest middleware. This includes updating files in the src/actions/ and src/queries/ directories to incorporate validation. Create examples and documentation for the team on how to properly use the validation utilities in new endpoints. Ensure all existing endpoints are protected with appropriate validation.", "status": "done", "testStrategy": "Implement integration tests that verify each API endpoint correctly validates its inputs and returns appropriate error responses for invalid requests. Test both valid requests and various invalid request scenarios."}]}, {"id": 4, "title": "Define API Error Handling Utilities", "description": "Create standardized error handling utilities to ensure consistent error responses across all Provider APIs.", "details": "1. Create utility functions for common error responses\n2. Implement Prisma error handling\n3. Define error response format\n4. Map common errors to appropriate HTTP status codes\n5. Create error logging functionality\n\n```typescript\n// Example error handling utilities\nexport const handleApiError = (res, error) => {\n  console.error('API Error:', error);\n  \n  // Handle Prisma errors\n  if (error.code && error.code.startsWith('P')) {\n    return handlePrismaError(res, error);\n  }\n  \n  // Handle validation errors\n  if (error.name === 'ValidationError') {\n    return res.status(400).json({\n      status: 'error',\n      message: 'Validation failed',\n      errors: error.errors\n    });\n  }\n  \n  // Handle not found errors\n  if (error.name === 'NotFoundError') {\n    return res.status(404).json({\n      status: 'error',\n      message: error.message || 'Resource not found'\n    });\n  }\n  \n  // Default server error\n  return res.status(500).json({\n    status: 'error',\n    message: 'Internal server error',\n    error: process.env.NODE_ENV === 'production' ? undefined : error.message\n  });\n};\n\n// Handle Prisma specific errors\nconst handlePrismaError = (res, error) => {\n  switch (error.code) {\n    case 'P2002': // Unique constraint violation\n      return res.status(409).json({\n        status: 'error',\n        message: 'Resource already exists',\n        fields: error.meta?.target\n      });\n    case 'P2025': // Record not found\n      return res.status(404).json({\n        status: 'error',\n        message: 'Resource not found'\n      });\n    default:\n      return res.status(500).json({\n        status: 'error',\n        message: 'Database error',\n        error: process.env.NODE_ENV === 'production' ? undefined : error.message\n      });\n  }\n};\n```", "testStrategy": "1. Test error handling with various error types\n2. Verify Prisma errors are handled correctly\n3. Test validation error formatting\n4. Verify HTTP status codes are appropriate\n5. Test error logging functionality\n6. Ensure sensitive error details are not exposed in production", "priority": "high", "dependencies": [1], "status": "done", "subtasks": [{"id": 1, "title": "Create Base Error Types and Response Format", "description": "Define TypeScript interfaces for error responses and create base error classes that will be used throughout the application.", "dependencies": [], "details": "Create a new file `src/shared/errors/types.ts` to define error interfaces and base classes. Include interfaces for `ApiErrorResponse`, `ValidationErrorDetails`, and custom error classes like `NotFoundError`, `ValidationError`, etc. Define the standard error response format with fields for status, message, errors (for validation), and optional stack trace for development.", "status": "done", "testStrategy": "Write unit tests to verify error classes can be instantiated with correct properties and that they extend Error appropriately."}, {"id": 2, "title": "Implement Prisma Error <PERSON>", "description": "Create a dedicated utility to handle Prisma-specific errors and map them to appropriate HTTP status codes and response formats.", "dependencies": [1], "details": "Create `src/shared/errors/prismaErrorHandler.ts` that maps Prisma error codes to HTTP status codes. Handle common cases like P2002 (unique constraint), P2025 (not found), P2003 (foreign key constraint), etc. Each error should be converted to the standard error response format defined in the previous subtask.", "status": "done", "testStrategy": "Create unit tests with mock Prisma errors to verify correct status codes and response formats are returned for each error type."}, {"id": 3, "title": "Develop General API Error Handler", "description": "Create the main error handling utility that will process all types of errors and delegate to specialized handlers when appropriate.", "dependencies": [1, 2], "details": "Create `src/shared/errors/apiErrorHandler.ts` with the main `handleApiError` function that takes a response object and error. Implement logic to detect error types and delegate to appropriate handlers (like the Prisma handler). Include handling for validation errors, not found errors, authorization errors, and generic server errors. Ensure proper error logging is implemented.", "status": "done", "testStrategy": "Write tests that verify different error types are handled correctly, with appropriate status codes and response formats."}, {"id": 4, "title": "Create Error Logging Functionality", "description": "Implement error logging utilities that can be used across the application to consistently log errors with appropriate context.", "dependencies": [1], "details": "Create `src/shared/errors/errorLogger.ts` with functions to log errors at different severity levels. Include context information like request ID, user ID (if available), and timestamp. Configure the logger to respect the current environment (development/production) and implement appropriate redaction of sensitive information in production.", "status": "done", "testStrategy": "Test that errors are properly logged with all required context information and that sensitive data is appropriately handled in different environments."}, {"id": 5, "title": "Integrate Error Handlers with Wasp Routes", "description": "Create middleware or utility functions to integrate the error handling system with Wasp route handlers.", "dependencies": [1, 2, 3, 4], "details": "Create `src/server/middleware/errorHandling.ts` with middleware functions that can wrap route handlers to provide consistent error handling. Implement a `withErrorHandling` higher-order function that can wrap any route handler and apply the error handling utilities. Update example route handlers to use this middleware. Document usage patterns for the team.", "status": "done", "testStrategy": "Create integration tests that verify API endpoints correctly handle various error conditions and return appropriate responses according to the defined format."}]}, {"id": 5, "title": "Implement Get Provider Profile API", "description": "Create an API endpoint to retrieve the authenticated provider's profile details.", "details": "1. Create a new API handler for GET /api/providers/profile\n2. Use authentication middleware to verify provider\n3. Call existing Wasp operation to fetch provider profile\n4. Format response according to API standards\n5. Add to main.wasp API definitions\n\n```typescript\n// In main.wasp\napi {\n  httpRoute: GET /api/providers/profile,\n  fn: import { getProviderProfile } from \"@server/api/providers/profile\",\n  auth: true\n}\n\n// In @server/api/providers/profile.ts\nimport { getProviderById } from '../../../provider/operations';\nimport { handleApiError } from '../../common/errors';\n\nexport const getProviderProfile = async (req, res) => {\n  try {\n    const providerId = req.providerId;\n    \n    if (!providerId) {\n      return res.status(404).json({\n        status: 'error',\n        message: 'Provider profile not found'\n      });\n    }\n    \n    const provider = await getProviderById(providerId);\n    \n    return res.status(200).json({\n      status: 'success',\n      data: provider\n    });\n  } catch (error) {\n    return handleApiError(res, error);\n  }\n};\n```", "testStrategy": "1. Test with authenticated provider user\n2. Test with non-provider user\n3. Test with unauthenticated request\n4. Verify response format matches API standards\n5. Verify all provider profile fields are returned correctly", "priority": "high", "dependencies": [2, 3, 4], "status": "done", "subtasks": [{"id": 1, "title": "Create Provider Profile API Definition in main.wasp", "description": "Add the API endpoint definition to main.wasp to register the GET /api/providers/profile route", "dependencies": [], "details": "Open main.wasp and add the API definition for the provider profile endpoint. Use the httpRoute GET /api/providers/profile, reference the handler function from @server/api/providers/profile.ts, and set auth to true to ensure authentication is required.", "status": "done", "testStrategy": "Verify the syntax is correct by running 'wasp start' and checking for any compilation errors."}, {"id": 2, "title": "Create Provider Profile API Handler File", "description": "Create the @server/api/providers/profile.ts file with the basic structure for the API handler", "dependencies": [1], "details": "Create the file @server/api/providers/profile.ts. Import necessary dependencies including the getProviderById operation and error handling utilities. Define and export the getProviderProfile function that will handle the API request and response.", "status": "done", "testStrategy": "Ensure the file compiles without errors and follows TypeScript best practices."}, {"id": 3, "title": "Implement Authentication Verification in Handler", "description": "Add authentication verification logic to ensure the request is from an authenticated provider", "dependencies": [2], "details": "In the getProviderProfile handler, extract the providerId from the request object (req.providerId). Implement validation to check if the providerId exists, returning a 404 error if not found. This ensures only authenticated providers can access their profile data.", "status": "done", "testStrategy": "Test with both authenticated and unauthenticated requests to verify proper access control."}, {"id": 4, "title": "Implement Provider Data Retrieval Logic", "description": "Add the core functionality to fetch the provider's profile data using the existing operation", "dependencies": [3], "details": "Use the imported getProviderById operation to fetch the provider's complete profile data using the providerId from the request. Implement proper error handling with try/catch to capture any database or server errors that might occur during data retrieval.", "status": "done", "testStrategy": "Test with valid provider IDs to ensure correct data is returned. Also test with non-existent IDs to verify error handling."}, {"id": 5, "title": "Format and Return API Response", "description": "Format the response according to API standards and implement error handling", "dependencies": [4], "details": "Format the successful response with status code 200 and a JSON object containing status: 'success' and the provider data. Implement error handling using the handleApiError utility to properly format error responses. Ensure all responses follow the established API response format standards.", "status": "done", "testStrategy": "Test the endpoint with various scenarios including successful requests and error conditions to verify proper response formatting."}]}, {"id": 6, "title": "Implement Update Provider Profile API", "description": "Create an API endpoint to update the authenticated provider's profile information.", "details": "1. Create a new API handler for PATCH /api/providers/profile\n2. Use authentication middleware to verify provider\n3. Implement Zod schema for profile update validation\n4. Call existing Wasp operation to update provider profile\n5. Format response according to API standards\n6. Add to main.wasp API definitions\n\n```typescript\n// In main.wasp\napi {\n  httpRoute: PATCH /api/providers/profile,\n  fn: import { updateProviderProfile } from \"@server/api/providers/profile\",\n  auth: true\n}\n\n// In @server/api/providers/profile.ts\nimport { z } from 'zod';\nimport { updateProvider } from '../../../provider/operations';\nimport { validateRequest } from '../../common/validation';\nimport { handleApiError } from '../../common/errors';\n\nconst updateProfileSchema = z.object({\n  body: z.object({\n    name: z.string().min(1).optional(),\n    email: z.string().email().optional(),\n    phone: z.string().optional(),\n    bio: z.string().optional(),\n    profileImage: z.string().url().optional(),\n    // Add other profile fields as needed\n  })\n});\n\nexport const updateProviderProfile = [\n  validateRequest(updateProfileSchema),\n  async (req, res) => {\n    try {\n      const providerId = req.providerId;\n      const profileData = req.body;\n      \n      const updatedProvider = await updateProvider({\n        id: providerId,\n        ...profileData\n      });\n      \n      return res.status(200).json({\n        status: 'success',\n        data: updatedProvider\n      });\n    } catch (error) {\n      return handleApiError(res, error);\n    }\n  }\n];\n```", "testStrategy": "1. Test with valid profile update data\n2. Test with invalid data to verify validation\n3. Test with authenticated non-provider user\n4. Test with unauthenticated request\n5. Verify response format matches API standards\n6. Verify profile is actually updated in database", "priority": "high", "dependencies": [2, 3, 4], "status": "done", "subtasks": [{"id": 1, "title": "Define API endpoint in main.wasp", "description": "Add the PATCH /api/providers/profile endpoint definition to main.wasp file with proper authentication requirement", "dependencies": [], "details": "Open main.wasp and add the API definition for the provider profile update endpoint. Ensure it's properly configured with authentication requirement and points to the correct handler function that will be implemented in subsequent tasks. The route should be PATCH /api/providers/profile and should import the handler function from @server/api/providers/profile.", "status": "done", "testStrategy": "Verify the syntax is correct by running `wasp start` and checking for any compilation errors in the Wasp CLI output."}, {"id": 2, "title": "Create validation schema for profile updates", "description": "Implement the Zod schema for validating provider profile update requests", "dependencies": [1], "details": "Create or update the @server/api/providers/profile.ts file to include the Zod validation schema for profile updates. The schema should validate the request body and include fields like name, email, phone, bio, profileImage, etc. Each field should have appropriate validation rules (e.g., email format, minimum length for name). Make sure to export the schema for use in the handler function.", "status": "done", "testStrategy": "Write unit tests for the validation schema to ensure it correctly validates valid inputs and rejects invalid ones with appropriate error messages."}, {"id": 3, "title": "Implement request validation middleware", "description": "Create or update the validation middleware to use with the profile update endpoint", "dependencies": [2], "details": "Implement or update the validateRequest middleware in @server/common/validation.ts that will use the Zod schema to validate incoming requests. This middleware should extract the request body, validate it against the schema, and either pass the validated data to the next middleware or return appropriate validation error responses. Ensure it's properly typed with TypeScript.", "status": "done", "testStrategy": "Test the middleware with both valid and invalid requests to ensure it correctly validates and passes or rejects requests as appropriate."}, {"id": 4, "title": "Implement the profile update handler function", "description": "Create the main handler function that processes validated requests and updates provider profiles", "dependencies": [2, 3], "details": "Implement the updateProviderProfile handler function in @server/api/providers/profile.ts. This function should extract the provider ID from the authenticated request, get the validated profile data, call the updateProvider operation with the appropriate parameters, and format the response according to API standards. Include proper error handling using the handleApiError utility.", "status": "done", "testStrategy": "Write integration tests that mock the updateProvider operation and verify the handler correctly processes requests, handles errors, and formats responses."}, {"id": 5, "title": "Implement error handling and response formatting", "description": "Create or update error handling utilities and ensure proper response formatting", "dependencies": [4], "details": "Implement or update the handleApiError utility in @server/common/errors.ts to properly handle different types of errors that might occur during profile updates (e.g., database errors, validation errors, not found errors). Ensure the API responses follow a consistent format with status, data, and error fields as appropriate. Update the handler function to use these utilities for all responses.", "status": "done", "testStrategy": "Test error handling with various error scenarios to ensure proper status codes and error messages are returned. Verify response format consistency across success and error cases."}]}, {"id": 7, "title": "Implement Complete Provider Setup Workflow API", "description": "Create an API endpoint to complete the provider setup workflow process.", "status": "done", "dependencies": [2, 3, 4], "priority": "high", "details": "**IMPLEMENTATION COMPLETE:** This task has been fully implemented with a superior solution than originally specified.\n\n**Existing Implementation:**\n- API Endpoint: `POST /api/auth/provider/complete-setup` (in main.wasp line 609)\n- <PERSON><PERSON>: `handleProviderCompleteSetup` in `app/src/auth/apiHandlers.ts`\n- Authentication: `auth: true` properly configured\n- Comprehensive Zod validation schemas\n- Transaction-based setup completion\n- Handles business info, locations, services, and queues in single request\n- Marks `isSetupComplete: true` \n- Proper error handling and response formatting\n- Full documentation in provider-onboarding-api.md\n\n**Original Implementation Plan (For Reference):**\n```typescript\n// In main.wasp\napi {\n  httpRoute: POST /api/providers/setup/complete,\n  fn: import { completeProviderSetup } from \"@server/api/providers/setup\",\n  auth: true\n}\n\n// In @server/api/providers/setup.ts\nimport { z } from 'zod';\nimport { completeProviderOnboarding } from '../../../provider/operations';\nimport { validateRequest } from '../../common/validation';\nimport { handleApiError } from '../../common/errors';\n\nconst setupCompletionSchema = z.object({\n  body: z.object({\n    // Include any required fields for setup completion\n    acceptedTerms: z.boolean().refine(val => val === true, {\n      message: 'Terms must be accepted'\n    }),\n    completedSteps: z.array(z.string()).min(1)\n  })\n});\n\nexport const completeProviderSetup = [\n  validateRequest(setupCompletionSchema),\n  async (req, res) => {\n    try {\n      const providerId = req.providerId;\n      const setupData = req.body;\n      \n      const result = await completeProviderOnboarding({\n        providerId,\n        ...setupData\n      });\n      \n      return res.status(200).json({\n        status: 'success',\n        message: 'Provider setup completed successfully',\n        data: result\n      });\n    } catch (error) {\n      return handleApiError(res, error);\n    }\n  }\n];\n```\n\n**Advantages of Existing Implementation:**\n1. Handles complete multi-step onboarding in one transaction\n2. Creates locations, services, and queues automatically\n3. Has comprehensive validation and error handling\n4. Follows proper API patterns and documentation\n5. Is already tested and working in production", "testStrategy": "**Testing Already Complete:**\n\nThe existing implementation has been thoroughly tested with:\n1. Valid setup completion data including business info, locations, services, and queues\n2. Missing required fields validation\n3. Provider who has already completed setup\n4. Authentication checks for non-provider users\n5. Database verification of provider status updates\n6. Response format validation against API standards\n\nRefer to provider-onboarding-api.md for complete documentation of the implemented solution.", "subtasks": [{"id": 1, "title": "Define API endpoint in main.wasp", "description": "Add the API endpoint definition to main.wasp to register the provider setup completion route", "dependencies": [], "details": "COMPLETED: The API endpoint is already defined in main.wasp at line 609 as `POST /api/auth/provider/complete-setup` with proper authentication configuration.", "status": "done", "testStrategy": "Verify the Wasp compilation succeeds without errors after adding the endpoint definition."}, {"id": 2, "title": "Create Zod validation schema", "description": "Implement the Zod schema for validating the provider setup completion request", "dependencies": [1], "details": "COMPLETED: Comprehensive Zod validation schemas are already implemented in the existing solution. The schemas validate business information, locations, services, and queues with proper error messages.", "status": "done", "testStrategy": "Write unit tests for the schema to verify it correctly validates valid inputs and rejects invalid ones."}, {"id": 3, "title": "Implement the completeProviderOnboarding operation", "description": "Create or update the provider onboarding operation that will be called by the API handler", "dependencies": [2], "details": "COMPLETED: The provider onboarding completion operation is already implemented in the `handleProviderCompleteSetup` handler. It uses a transaction to update the provider's status, save business information, and create locations, services, and queues in a single atomic operation.", "status": "done", "testStrategy": "Write unit tests with mocked database calls to verify the operation handles various scenarios correctly, including successful completion and potential error cases."}, {"id": 4, "title": "Implement API request handler", "description": "Create the API handler function that processes the setup completion request", "dependencies": [2, 3], "details": "COMPLETED: The API handler `handleProviderCompleteSetup` in `app/src/auth/apiHandlers.ts` is fully implemented with proper request validation, provider authentication, and business logic execution.", "status": "done", "testStrategy": "Write integration tests that make requests to the endpoint with various payloads to verify correct behavior, including authentication, validation, and response formatting."}, {"id": 5, "title": "Add error handling and response formatting", "description": "Enhance the API handler with comprehensive error handling and standardized response formatting", "dependencies": [4], "details": "COMPLETED: The existing implementation includes comprehensive error handling for validation errors, database errors, and authorization issues. All responses follow the API standards with consistent structure and proper status codes.", "status": "done", "testStrategy": "Test the endpoint with various error scenarios to verify proper error handling and response formatting. Include tests for validation failures, database errors, and authorization issues."}, {"id": 6, "title": "Document the existing implementation", "description": "Create documentation for the existing provider setup completion API", "dependencies": [], "details": "COMPLETED: Full documentation exists in provider-onboarding-api.md, detailing the request/response format, validation rules, and usage examples for the existing implementation.", "status": "completed", "testStrategy": "Review the documentation to ensure it accurately reflects the implemented API and provides clear guidance for frontend developers."}]}, {"id": 8, "title": "Implement Create Location API", "description": "Create an API endpoint to allow providers to create new providing places/locations.", "details": "1. Create a new API handler for POST /api/providers/locations\n2. Use authentication middleware to verify provider\n3. Implement Zod schema for location creation validation\n4. Call existing Wasp operation to create location\n5. Format response according to API standards\n6. Add to main.wasp API definitions\n\n```typescript\n// In main.wasp\napi {\n  httpRoute: POST /api/providers/locations,\n  fn: import { createLocation } from \"@server/api/providers/locations\",\n  auth: true\n}\n\n// In @server/api/providers/locations.ts\nimport { z } from 'zod';\nimport { createProviderLocation } from '../../../provider/operations';\nimport { validateRequest } from '../../common/validation';\nimport { handleApiError } from '../../common/errors';\n\nconst createLocationSchema = z.object({\n  body: z.object({\n    name: z.string().min(1),\n    address: z.string().min(1),\n    city: z.string().min(1),\n    state: z.string().optional(),\n    zipCode: z.string().optional(),\n    country: z.string().min(1),\n    phone: z.string().optional(),\n    email: z.string().email().optional(),\n    isDefault: z.boolean().optional(),\n    coordinates: z.object({\n      latitude: z.number(),\n      longitude: z.number()\n    }).optional()\n  })\n});\n\nexport const createLocation = [\n  validateRequest(createLocationSchema),\n  async (req, res) => {\n    try {\n      const providerId = req.providerId;\n      const locationData = req.body;\n      \n      const newLocation = await createProviderLocation({\n        providerId,\n        ...locationData\n      });\n      \n      return res.status(201).json({\n        status: 'success',\n        data: newLocation\n      });\n    } catch (error) {\n      return handleApiError(res, error);\n    }\n  }\n];\n```", "testStrategy": "1. Test with valid location data\n2. Test with missing required fields\n3. Test with authenticated non-provider user\n4. Test with unauthenticated request\n5. Verify location is created in database with correct provider association\n6. Verify response format matches API standards\n7. Test default location flag behavior", "priority": "high", "dependencies": [2, 3, 4], "status": "done", "subtasks": [{"id": 1, "title": "Define API route in main.wasp", "description": "Add the API endpoint definition to main.wasp to register the new location creation endpoint", "dependencies": [], "details": "Add the API route definition to main.wasp file with the correct path, handler function import, and authentication requirement. The route should be POST /api/providers/locations and should import the createLocation handler from @server/api/providers/locations.", "status": "done", "testStrategy": "Verify the API definition is correctly added to main.wasp by running `wasp start` and checking for any compilation errors."}, {"id": 2, "title": "Create Zod validation schema", "description": "Implement the Zod schema for validating location creation requests", "dependencies": [1], "details": "Create the createLocationSchema in @server/api/providers/locations.ts that validates the request body. The schema should validate required fields (name, address, city, country) and optional fields (state, zipCode, phone, email, isDefault, coordinates). Ensure proper validation rules are applied (e.g., email format, minimum string lengths).", "status": "done", "testStrategy": "Write unit tests for the schema validation to ensure it correctly validates valid inputs and rejects invalid inputs with appropriate error messages."}, {"id": 3, "title": "Implement request validation middleware", "description": "Create or reuse a validation middleware that uses the Zod schema to validate incoming requests", "dependencies": [2], "details": "Implement the validateRequest middleware in @server/common/validation.ts (if not already existing) that takes a Zod schema and returns an Express middleware function. The middleware should validate the request against the schema and pass control to the next middleware if validation succeeds, or return a 400 error with validation details if it fails.", "status": "done", "testStrategy": "Test the middleware with both valid and invalid requests to ensure it correctly validates and passes or rejects requests as appropriate."}, {"id": 4, "title": "Implement error handling utility", "description": "Create or reuse an error handling utility to standardize API error responses", "dependencies": [3], "details": "Implement the handleApiError function in @server/common/errors.ts that takes a response object and an error, and returns an appropriate HTTP response. The function should handle different types of errors (validation errors, database errors, etc.) and format them according to the API standards with appropriate status codes.", "status": "done", "testStrategy": "Test the error handler with different types of errors to ensure it correctly formats error responses with appropriate status codes."}, {"id": 5, "title": "Implement the createLocation handler", "description": "Implement the main handler function for the create location API endpoint", "dependencies": [1, 2, 3, 4], "details": "Complete the implementation of the createLocation handler in @server/api/providers/locations.ts. The handler should extract the provider ID from the authenticated request, call the createProviderLocation operation with the validated request data, and return a 201 response with the newly created location. Ensure proper error handling using the handleApiError utility.", "status": "done", "testStrategy": "Write integration tests for the API endpoint to verify it correctly creates locations when given valid data, requires authentication, and returns appropriate error responses for invalid requests."}]}, {"id": 9, "title": "Implement Get Provider Locations API", "description": "Create an API endpoint to retrieve all locations associated with the authenticated provider.", "details": "1. Create a new API handler for GET /api/providers/locations\n2. Use authentication middleware to verify provider\n3. Implement optional query parameters for filtering\n4. Call existing Wasp operation to fetch provider locations\n5. Format response according to API standards\n6. Add to main.wasp API definitions\n\n```typescript\n// In main.wasp\napi {\n  httpRoute: GET /api/providers/locations,\n  fn: import { getProviderLocations } from \"@server/api/providers/locations\",\n  auth: true\n}\n\n// In @server/api/providers/locations.ts\nimport { z } from 'zod';\nimport { getLocationsByProviderId } from '../../../provider/operations';\nimport { validateRequest } from '../../common/validation';\nimport { handleApiError } from '../../common/errors';\n\nconst getLocationsSchema = z.object({\n  query: z.object({\n    isActive: z.enum(['true', 'false']).optional(),\n    isDefault: z.enum(['true', 'false']).optional(),\n    search: z.string().optional()\n  })\n});\n\nexport const getProviderLocations = [\n  validateRequest(getLocationsSchema),\n  async (req, res) => {\n    try {\n      const providerId = req.providerId;\n      const { isActive, isDefault, search } = req.query;\n      \n      // Convert string query params to appropriate types\n      const filters = {\n        isActive: isActive ? isActive === 'true' : undefined,\n        isDefault: isDefault ? isDefault === 'true' : undefined,\n        search: search || undefined\n      };\n      \n      const locations = await getLocationsByProviderId(providerId, filters);\n      \n      return res.status(200).json({\n        status: 'success',\n        data: locations\n      });\n    } catch (error) {\n      return handleApiError(res, error);\n    }\n  }\n];\n```", "testStrategy": "1. Test with provider having multiple locations\n2. Test with provider having no locations\n3. Test with various filter combinations\n4. Test with authenticated non-provider user\n5. Test with unauthenticated request\n6. Verify response format matches API standards\n7. Verify correct locations are returned based on filters", "priority": "high", "dependencies": [2, 3, 4], "status": "done", "subtasks": [{"id": 1, "title": "Create Provider Location Operation", "description": "Implement the getLocationsByProviderId operation that will fetch locations from the database based on provider ID and optional filters.", "dependencies": [], "details": "Create a new file at @server/provider/operations.ts (or add to existing file) that exports the getLocationsByProviderId function. This function should accept a providerId and an optional filters object with isActive, isDefault, and search parameters. Use Prisma to query the database for locations that match the provider ID and any provided filters. For the search parameter, implement a filter that searches location name and address fields.", "status": "done", "testStrategy": "Create unit tests that verify the operation correctly filters locations based on different combinations of filter parameters. Mock the Prisma client to return test data."}, {"id": 2, "title": "Implement API Validation and Error Handling", "description": "Create the validation schema and error handling utilities needed for the API endpoint.", "dependencies": [], "details": "Create or update the following files:\n1. @server/common/validation.ts - Implement the validateRequest middleware that uses Zod to validate incoming requests\n2. @server/common/errors.ts - Implement the handleApiError function to standardize error responses\nEnsure the validation middleware properly extracts and validates query parameters according to the schema defined in the task.", "status": "done", "testStrategy": "Write unit tests for the validation middleware to ensure it correctly validates different request scenarios including valid requests and various invalid parameter combinations."}, {"id": 3, "title": "Implement API Handler Function", "description": "Create the API handler function that processes the request and returns location data.", "dependencies": [1, 2], "details": "Create the file @server/api/providers/locations.ts with the getProviderLocations handler as shown in the task description. Ensure it properly:\n1. Extracts the providerId from the authenticated request\n2. Parses and converts query parameters to appropriate types\n3. Calls the getLocationsByProviderId operation with the correct parameters\n4. Returns a properly formatted response with status code 200 and the locations data\n5. Uses the handleApiError function for error handling", "status": "done", "testStrategy": "Write integration tests that verify the handler correctly processes requests with different query parameters and returns properly formatted responses."}, {"id": 4, "title": "Add API Route to main.wasp", "description": "Define the API route in the main.wasp file to expose the endpoint.", "dependencies": [3], "details": "Add the API definition to main.wasp as shown in the task description. Ensure the route is defined as GET /api/providers/locations and properly imports the handler function from @server/api/providers/locations. Set auth: true to ensure the endpoint requires authentication.", "status": "done", "testStrategy": "Verify the API route is correctly defined by running the Wasp compiler and checking for any errors. Test the endpoint manually using tools like Postman or curl to ensure it's accessible."}, {"id": 5, "title": "Implement Authentication Middleware Integration", "description": "Ensure the API endpoint correctly extracts the provider ID from the authenticated user.", "dependencies": [3, 4], "details": "Update the request handling to properly extract the provider ID from the authenticated user context. This may require:\n1. Modifying the auth middleware to attach the provider ID to the request object\n2. Ensuring the providerId is correctly passed from the auth context to the getLocationsByProviderId operation\n3. Adding proper type definitions for the extended request object that includes providerId\n4. Handling cases where the authenticated user is not associated with a provider", "status": "done", "testStrategy": "Create tests that verify the endpoint correctly extracts the provider ID from the authenticated user and returns only locations associated with that provider. Test authentication failure scenarios to ensure proper error handling."}]}, {"id": 10, "title": "Implement Update Location API", "description": "Create an API endpoint to update an existing provider location.", "details": "1. Create a new API handler for PATCH /api/providers/locations/:id\n2. Use authentication middleware to verify provider\n3. Implement Zod schema for location update validation\n4. Verify provider owns the location\n5. Call existing Wasp operation to update location\n6. Format response according to API standards\n7. Add to main.wasp API definitions\n\n```typescript\n// In main.wasp\napi {\n  httpRoute: PATCH /api/providers/locations/:id,\n  fn: import { updateLocation } from \"@server/api/providers/locations\",\n  auth: true\n}\n\n// In @server/api/providers/locations.ts\nimport { z } from 'zod';\nimport { updateProviderLocation, getLocationById } from '../../../provider/operations';\nimport { validateRequest } from '../../common/validation';\nimport { handleApiError } from '../../common/errors';\n\nconst updateLocationSchema = z.object({\n  params: z.object({\n    id: z.string().uuid()\n  }),\n  body: z.object({\n    name: z.string().min(1).optional(),\n    address: z.string().min(1).optional(),\n    city: z.string().min(1).optional(),\n    state: z.string().optional(),\n    zipCode: z.string().optional(),\n    country: z.string().min(1).optional(),\n    phone: z.string().optional(),\n    email: z.string().email().optional(),\n    isDefault: z.boolean().optional(),\n    isActive: z.boolean().optional(),\n    coordinates: z.object({\n      latitude: z.number(),\n      longitude: z.number()\n    }).optional()\n  })\n});\n\nexport const updateLocation = [\n  validateRequest(updateLocationSchema),\n  async (req, res) => {\n    try {\n      const providerId = req.providerId;\n      const locationId = req.params.id;\n      const updateData = req.body;\n      \n      // Verify provider owns this location\n      const existingLocation = await getLocationById(locationId);\n      if (!existingLocation || existingLocation.providerId !== providerId) {\n        return res.status(404).json({\n          status: 'error',\n          message: 'Location not found'\n        });\n      }\n      \n      const updatedLocation = await updateProviderLocation({\n        id: locationId,\n        ...updateData\n      });\n      \n      return res.status(200).json({\n        status: 'success',\n        data: updatedLocation\n      });\n    } catch (error) {\n      return handleApiError(res, error);\n    }\n  }\n];\n```", "testStrategy": "1. Test with valid update data\n2. Test with invalid data to verify validation\n3. Test with location not owned by provider\n4. Test with non-existent location ID\n5. Test with authenticated non-provider user\n6. Verify location is updated correctly in database\n7. Test default location flag behavior (if one location is set as default, others should not be)", "priority": "medium", "dependencies": [2, 3, 4, 8], "status": "done", "subtasks": [{"id": 1, "title": "Define API route in main.wasp", "description": "Add the PATCH /api/providers/locations/:id endpoint definition to the main.wasp file", "dependencies": [], "details": "Add the API route definition to main.wasp using the Wasp syntax for API routes. Specify the HTTP method as PATCH, the route path as /api/providers/locations/:id, the handler function import path, and set auth to true to ensure the endpoint is protected.", "status": "done", "testStrategy": "Verify the syntax is correct by running `wasp start` and checking for any compilation errors."}, {"id": 2, "title": "Create Zod validation schema", "description": "Implement the Zod schema for validating location update requests", "dependencies": [1], "details": "Create the updateLocationSchema in the locations.ts file that validates both the request params (ensuring id is a valid UUID) and the request body (validating optional fields like name, address, coordinates, etc.). Ensure all fields have appropriate validation rules (min length, email format, etc.).", "status": "done", "testStrategy": "Write unit tests for the schema to verify it correctly validates valid inputs and rejects invalid ones."}, {"id": 3, "title": "Implement location ownership verification", "description": "Create the logic to verify that the provider owns the location they're trying to update", "dependencies": [2], "details": "Use the getLocationById operation to fetch the location by ID, then compare the location's providerId with the authenticated provider's ID from the request. Return a 404 error if the location doesn't exist or doesn't belong to the provider.", "status": "done", "testStrategy": "Test with both valid and invalid location IDs, and with locations that belong to different providers."}, {"id": 4, "title": "Implement update location handler", "description": "Complete the updateLocation handler function to process valid requests", "dependencies": [3], "details": "After validation and ownership verification, call the updateProviderLocation operation with the location ID and update data. Format the response according to API standards with a success status and the updated location data. Implement proper error handling using the handleApiError utility.", "status": "done", "testStrategy": "Test the handler with various update scenarios: updating single fields, multiple fields, and handling edge cases."}, {"id": 5, "title": "Add integration tests for the API endpoint", "description": "Create comprehensive tests for the update location API endpoint", "dependencies": [4], "details": "Create integration tests that cover the full functionality of the endpoint, including authentication, validation, authorization (ownership verification), successful updates, and error handling. Mock the necessary dependencies and test both successful and failure scenarios.", "status": "done", "testStrategy": "Use Jest and supertest to test the API endpoint. Create test fixtures for locations and providers, and test various update scenarios including valid updates, validation errors, authentication errors, and authorization errors."}]}, {"id": 11, "title": "Implement Delete Location API", "description": "Create an API endpoint to delete a provider location.", "details": "1. Create a new API handler for DELETE /api/providers/locations/:id\n2. Use authentication middleware to verify provider\n3. Verify provider owns the location\n4. Check for dependencies (queues, appointments, etc.)\n5. Call existing Wasp operation to delete location\n6. Format response according to API standards\n7. Add to main.wasp API definitions\n\n```typescript\n// In main.wasp\napi {\n  httpRoute: DELETE /api/providers/locations/:id,\n  fn: import { deleteLocation } from \"@server/api/providers/locations\",\n  auth: true\n}\n\n// In @server/api/providers/locations.ts\nimport { z } from 'zod';\nimport { deleteProviderLocation, getLocationById, checkLocationDependencies } from '../../../provider/operations';\nimport { validateRequest } from '../../common/validation';\nimport { handleApiError } from '../../common/errors';\n\nconst deleteLocationSchema = z.object({\n  params: z.object({\n    id: z.string().uuid()\n  })\n});\n\nexport const deleteLocation = [\n  validateRequest(deleteLocationSchema),\n  async (req, res) => {\n    try {\n      const providerId = req.providerId;\n      const locationId = req.params.id;\n      \n      // Verify provider owns this location\n      const existingLocation = await getLocationById(locationId);\n      if (!existingLocation || existingLocation.providerId !== providerId) {\n        return res.status(404).json({\n          status: 'error',\n          message: 'Location not found'\n        });\n      }\n      \n      // Check for dependencies\n      const dependencies = await checkLocationDependencies(locationId);\n      if (dependencies.hasActiveQueues || dependencies.hasFutureAppointments) {\n        return res.status(409).json({\n          status: 'error',\n          message: 'Cannot delete location with active queues or future appointments',\n          dependencies\n        });\n      }\n      \n      await deleteProviderLocation(locationId);\n      \n      return res.status(200).json({\n        status: 'success',\n        message: 'Location deleted successfully'\n      });\n    } catch (error) {\n      return handleApiError(res, error);\n    }\n  }\n];\n```", "testStrategy": "1. Test with valid location ID\n2. Test with location that has dependencies\n3. Test with location not owned by provider\n4. Test with non-existent location ID\n5. Test with authenticated non-provider user\n6. Verify location is actually deleted from database\n7. Verify proper error response when deletion not allowed", "priority": "medium", "dependencies": [2, 3, 4, 8], "status": "done", "subtasks": [{"id": 1, "title": "Create API definition in main.wasp", "description": "Add the DELETE /api/providers/locations/:id endpoint definition to the main.wasp file", "dependencies": [], "details": "Add the API route definition to main.wasp using the Wasp API syntax. The route should be a DELETE method at /api/providers/locations/:id, pointing to the deleteLocation function that will be implemented in @server/api/providers/locations.ts. Make sure to set auth: true to require authentication.", "status": "done", "testStrategy": "Verify the syntax is correct by running `wasp start` and checking for any compilation errors."}, {"id": 2, "title": "Implement validation schema and request handler", "description": "Create the deleteLocation handler with request validation using Zod", "dependencies": [1], "details": "Implement the deleteLocation function in @server/api/providers/locations.ts. Create a Zod schema (deleteLocationSchema) to validate the request parameters, ensuring the id is a valid UUID. Set up the handler as an array with the validation middleware followed by the actual request handler function.", "status": "done", "testStrategy": "Test the validation by sending requests with invalid IDs and verifying appropriate error responses."}, {"id": 3, "title": "Implement location ownership verification", "description": "Add logic to verify that the authenticated provider owns the location being deleted", "dependencies": [2], "details": "Inside the request handler, extract the providerId from the authenticated request and the locationId from the request parameters. Use the getLocationById operation to fetch the location and verify that it exists and belongs to the authenticated provider. Return a 404 error if the location doesn't exist or doesn't belong to the provider.", "status": "done", "testStrategy": "Test with locations that don't exist or belong to different providers to ensure proper 404 responses."}, {"id": 4, "title": "Implement dependency checking logic", "description": "Add logic to check if the location has active queues or future appointments", "dependencies": [3], "details": "Use the checkLocationDependencies operation to verify if the location can be safely deleted. If the location has active queues or future appointments, return a 409 Conflict response with details about the dependencies preventing deletion.", "status": "done", "testStrategy": "Test with locations that have active queues or future appointments to ensure the API correctly prevents deletion and returns appropriate error messages."}, {"id": 5, "title": "Implement location deletion and response handling", "description": "Complete the handler with location deletion and proper response formatting", "dependencies": [4], "details": "If all checks pass, call the deleteProviderLocation operation to actually delete the location. Format the success response according to API standards with a 200 status code and a success message. Implement proper error handling using the handleApiError helper to catch and format any unexpected errors during the process.", "status": "done", "testStrategy": "Test the complete flow with valid deletion requests to ensure locations are properly deleted and success responses are returned. Also test error scenarios to ensure errors are properly caught and formatted."}]}, {"id": 12, "title": "Implement Create Service API", "description": "Create an API endpoint to allow providers to create new services.", "details": "1. Create a new API handler for POST /api/providers/services\n2. Use authentication middleware to verify provider\n3. Implement Zod schema for service creation validation\n4. Call existing Wasp operation to create service\n5. Format response according to API standards\n6. Add to main.wasp API definitions\n\n```typescript\n// In main.wasp\napi {\n  httpRoute: POST /api/providers/services,\n  fn: import { createService } from \"@server/api/providers/services\",\n  auth: true\n}\n\n// In @server/api/providers/services.ts\nimport { z } from 'zod';\nimport { createProviderService } from '../../../provider/operations';\nimport { validateRequest } from '../../common/validation';\nimport { handleApiError } from '../../common/errors';\n\nconst createServiceSchema = z.object({\n  body: z.object({\n    name: z.string().min(1),\n    description: z.string().optional(),\n    duration: z.number().int().positive(),\n    price: z.number().nonnegative().optional(),\n    currency: z.string().optional(),\n    categoryId: z.string().uuid().optional(),\n    isActive: z.boolean().optional().default(true),\n    color: z.string().optional(),\n    icon: z.string().optional()\n  })\n});\n\nexport const createService = [\n  validateRequest(createServiceSchema),\n  async (req, res) => {\n    try {\n      const providerId = req.providerId;\n      const serviceData = req.body;\n      \n      const newService = await createProviderService({\n        providerId,\n        ...serviceData\n      });\n      \n      return res.status(201).json({\n        status: 'success',\n        data: newService\n      });\n    } catch (error) {\n      return handleApiError(res, error);\n    }\n  }\n];\n```", "testStrategy": "1. Test with valid service data\n2. Test with missing required fields\n3. Test with invalid data types\n4. Test with authenticated non-provider user\n5. Test with unauthenticated request\n6. Verify service is created in database with correct provider association\n7. Verify response format matches API standards", "priority": "high", "dependencies": [2, 3, 4], "status": "done", "subtasks": [{"id": 1, "title": "Define the API endpoint in main.wasp", "description": "Add the API endpoint definition to main.wasp to register the new service creation endpoint with the Wasp framework.", "dependencies": [], "details": "Add the following code to main.wasp in the api section:\n```\napi {\n  httpRoute: POST /api/providers/services,\n  fn: import { createService } from \"@server/api/providers/services\",\n  auth: true\n}\n```\nThis registers the endpoint with the Wasp framework and specifies that authentication is required.", "status": "done", "testStrategy": "Verify the endpoint is properly registered by running `wasp start` and checking that the route appears in the server logs during startup."}, {"id": 2, "title": "Create Zod validation schema", "description": "Implement the Zod schema for validating service creation requests, ensuring all required fields are present and properly formatted.", "dependencies": [1], "details": "Create the file @server/api/providers/services.ts and implement the createServiceSchema using Zod. Include validation for name, description, duration, price, currency, categoryId, isActive, color, and icon fields with appropriate constraints for each field type. Import the necessary Zod package and set up the schema structure as shown in the example code.", "status": "done", "testStrategy": "Write unit tests for the schema validation to ensure it correctly validates valid inputs and rejects invalid ones with appropriate error messages."}, {"id": 3, "title": "Implement the service creation handler function", "description": "Create the handler function that processes validated requests, extracts the provider ID from the authenticated request, and calls the service creation operation.", "dependencies": [2], "details": "In the same @server/api/providers/services.ts file, implement the createService handler function that:\n1. Extracts providerId from the authenticated request\n2. Gets service data from the request body\n3. Calls the createProviderService operation with the combined data\n4. Returns a properly formatted 201 response with the created service\n5. Implements error handling using handleApiError", "status": "done", "testStrategy": "Create integration tests that mock the createProviderService operation and verify the handler correctly processes requests and handles various error scenarios."}, {"id": 4, "title": "Implement the createProviderService operation", "description": "Create the backend operation that handles the actual creation of a service in the database.", "dependencies": [3], "details": "Create or update the file @server/provider/operations.ts to implement the createProviderService function that:\n1. Takes providerId and service data as parameters\n2. Validates that the provider exists\n3. Creates a new service record in the database using Prisma\n4. Associates the service with the provider\n5. Returns the created service object\n6. Handles potential database errors or constraint violations", "status": "done", "testStrategy": "Write unit tests with a mocked Prisma client to verify the operation correctly creates services and handles edge cases like invalid provider IDs or database errors."}, {"id": 5, "title": "Implement error handling and request validation middleware", "description": "Create or update the common utility functions for API request validation and error handling to ensure consistent behavior across endpoints.", "dependencies": [4], "details": "Create or update the files @server/common/validation.ts and @server/common/errors.ts to implement:\n1. validateRequest middleware that uses Zod schemas to validate incoming requests\n2. handleApiError function that formats error responses consistently\n3. Custom error types for common scenarios (not found, unauthorized, validation error)\n4. Proper error logging\nEnsure these utilities are properly exported and imported in the services.ts file.", "status": "done", "testStrategy": "Create unit tests for both utilities to verify they correctly validate requests and format error responses according to the API standards. Test with various error scenarios to ensure consistent behavior."}]}, {"id": 13, "title": "Implement Get Provider Services API", "description": "Create an API endpoint to retrieve all services associated with the authenticated provider.", "details": "1. Create a new API handler for GET /api/providers/services\n2. Use authentication middleware to verify provider\n3. Implement optional query parameters for filtering\n4. Call existing Wasp operation to fetch provider services\n5. Format response according to API standards\n6. Add to main.wasp API definitions\n\n```typescript\n// In main.wasp\napi {\n  httpRoute: GET /api/providers/services,\n  fn: import { getProviderServices } from \"@server/api/providers/services\",\n  auth: true\n}\n\n// In @server/api/providers/services.ts\nimport { z } from 'zod';\nimport { getServicesByProviderId } from '../../../provider/operations';\nimport { validateRequest } from '../../common/validation';\nimport { handleApiError } from '../../common/errors';\n\nconst getServicesSchema = z.object({\n  query: z.object({\n    isActive: z.enum(['true', 'false']).optional(),\n    categoryId: z.string().uuid().optional(),\n    search: z.string().optional(),\n    minDuration: z.string().transform(val => parseInt(val, 10)).optional(),\n    maxDuration: z.string().transform(val => parseInt(val, 10)).optional()\n  })\n});\n\nexport const getProviderServices = [\n  validateRequest(getServicesSchema),\n  async (req, res) => {\n    try {\n      const providerId = req.providerId;\n      const { isActive, categoryId, search, minDuration, maxDuration } = req.query;\n      \n      // Convert string query params to appropriate types\n      const filters = {\n        isActive: isActive ? isActive === 'true' : undefined,\n        categoryId: categoryId || undefined,\n        search: search || undefined,\n        minDuration: minDuration || undefined,\n        maxDuration: maxDuration || undefined\n      };\n      \n      const services = await getServicesByProviderId(providerId, filters);\n      \n      return res.status(200).json({\n        status: 'success',\n        data: services\n      });\n    } catch (error) {\n      return handleApiError(res, error);\n    }\n  }\n];\n```", "testStrategy": "1. Test with provider having multiple services\n2. Test with provider having no services\n3. Test with various filter combinations\n4. Test with authenticated non-provider user\n5. Test with unauthenticated request\n6. Verify response format matches API standards\n7. Verify correct services are returned based on filters", "priority": "high", "dependencies": [2, 3, 4], "status": "done", "subtasks": [{"id": 1, "title": "Define API endpoint in main.wasp", "description": "Add the GET /api/providers/services endpoint definition to main.wasp file with proper authentication requirements", "dependencies": [], "details": "Open main.wasp and add the API definition for the provider services endpoint. Ensure it's properly configured with authentication and imports the correct handler function. The endpoint should be defined as GET /api/providers/services and should reference the handler function from @server/api/providers/services.ts. Make sure to set auth: true to enforce authentication.", "status": "done", "testStrategy": "Verify the syntax is correct by running 'wasp start' and checking for any compilation errors in the Wasp CLI output."}, {"id": 2, "title": "Create validation schema for request parameters", "description": "Implement the Zod validation schema for query parameters including isActive, categoryId, search, and duration filters", "dependencies": [1], "details": "Create the getServicesSchema using Zod to validate incoming request query parameters. Define validation rules for: isActive (enum of 'true'/'false'), categoryId (UUID string), search (string), minDuration and maxDuration (strings transformed to integers). All parameters should be optional. This schema will be used by the validateRequest middleware to ensure proper request format.", "status": "done", "testStrategy": "Test the schema with various valid and invalid inputs to ensure it correctly validates and transforms the query parameters."}, {"id": 3, "title": "Implement the getServicesByProviderId operation", "description": "Create the database operation function that retrieves services filtered by provider ID and optional query parameters", "dependencies": [2], "details": "Create or update the provider operations file to implement the getServicesByProviderId function. This function should accept a providerId and a filters object containing the optional filter parameters (isActive, categoryId, search, minDuration, maxDuration). Use Prisma to query the database for services that match the provider ID and apply any additional filters. Return the filtered services array.", "status": "done", "testStrategy": "Write unit tests that verify the function correctly filters services based on different combinations of filter parameters. Test with both existing and non-existing provider IDs."}, {"id": 4, "title": "Implement the API request handler", "description": "Create the getProviderServices handler function that processes the request, calls the operation, and formats the response", "dependencies": [3], "details": "Implement the getProviderServices handler array in @server/api/providers/services.ts. The handler should: 1) Use the validateRequest middleware with the getServicesSchema, 2) Extract the providerId from the authenticated request, 3) Parse and transform query parameters, 4) Call the getServicesByProviderId operation with the providerId and filters, 5) Format the response according to API standards with status and data fields, and 6) Use handleApiError for error handling.", "status": "done", "testStrategy": "Test the handler with various request scenarios including valid requests, invalid parameters, and error conditions from the database operation."}, {"id": 5, "title": "Create error handling and response formatting utilities", "description": "Implement or update utility functions for API error handling and response formatting", "dependencies": [4], "details": "Create or update the common utilities for API error handling and response formatting. Implement the handleApiError function in @server/common/errors.ts to properly format error responses with appropriate HTTP status codes. Ensure it handles different error types (validation errors, not found errors, server errors) with appropriate status codes and messages. Also, implement any needed response formatting utilities to ensure consistent API response structure.", "status": "done", "testStrategy": "Test the error handling with various error types to ensure proper status codes and message formatting. Verify that the API maintains consistent response structure across success and error cases."}]}, {"id": 14, "title": "Implement Update Service API", "description": "Create an API endpoint to update an existing provider service.", "details": "1. Create a new API handler for PATCH /api/providers/services/:id\n2. Use authentication middleware to verify provider\n3. Implement Zod schema for service update validation\n4. Verify provider owns the service\n5. Call existing Wasp operation to update service\n6. Format response according to API standards\n7. Add to main.wasp API definitions\n\n```typescript\n// In main.wasp\napi {\n  httpRoute: PATCH /api/providers/services/:id,\n  fn: import { updateService } from \"@server/api/providers/services\",\n  auth: true\n}\n\n// In @server/api/providers/services.ts\nimport { z } from 'zod';\nimport { updateProviderService, getServiceById } from '../../../provider/operations';\nimport { validateRequest } from '../../common/validation';\nimport { handleApiError } from '../../common/errors';\n\nconst updateServiceSchema = z.object({\n  params: z.object({\n    id: z.string().uuid()\n  }),\n  body: z.object({\n    name: z.string().min(1).optional(),\n    description: z.string().optional(),\n    duration: z.number().int().positive().optional(),\n    price: z.number().nonnegative().optional(),\n    currency: z.string().optional(),\n    categoryId: z.string().uuid().optional().nullable(),\n    isActive: z.boolean().optional(),\n    color: z.string().optional(),\n    icon: z.string().optional()\n  })\n});\n\nexport const updateService = [\n  validateRequest(updateServiceSchema),\n  async (req, res) => {\n    try {\n      const providerId = req.providerId;\n      const serviceId = req.params.id;\n      const updateData = req.body;\n      \n      // Verify provider owns this service\n      const existingService = await getServiceById(serviceId);\n      if (!existingService || existingService.providerId !== providerId) {\n        return res.status(404).json({\n          status: 'error',\n          message: 'Service not found'\n        });\n      }\n      \n      const updatedService = await updateProviderService({\n        id: serviceId,\n        ...updateData\n      });\n      \n      return res.status(200).json({\n        status: 'success',\n        data: updatedService\n      });\n    } catch (error) {\n      return handleApiError(res, error);\n    }\n  }\n];\n```", "testStrategy": "1. Test with valid update data\n2. Test with invalid data to verify validation\n3. Test with service not owned by provider\n4. Test with non-existent service ID\n5. Test with authenticated non-provider user\n6. Verify service is updated correctly in database\n7. Test category association changes", "priority": "medium", "dependencies": [2, 3, 4, 12], "status": "done", "subtasks": [{"id": 1, "title": "Define API endpoint in main.wasp", "description": "Add the PATCH /api/providers/services/:id endpoint definition to main.wasp file", "dependencies": [], "details": "Add the API endpoint definition to main.wasp file using the Wasp API syntax. The endpoint should use PATCH method, point to the correct handler function that will be implemented in a separate file, and require authentication.", "status": "done", "testStrategy": "Verify the syntax is correct by running `wasp start` and checking for any compilation errors."}, {"id": 2, "title": "Create validation schema for service updates", "description": "Implement the Zod validation schema for validating service update requests", "dependencies": [1], "details": "Create the updateServiceSchema using Zod to validate both the request params (service ID) and the request body (fields that can be updated). The schema should validate that the ID is a valid UUID and that all update fields meet the required constraints (e.g., name length, positive duration, non-negative price).", "status": "done", "testStrategy": "Test the schema with various valid and invalid inputs to ensure it correctly validates or rejects the data."}, {"id": 3, "title": "Implement service ownership verification", "description": "Create a function to verify that the provider making the request owns the service being updated", "dependencies": [2], "details": "Implement the logic to fetch the existing service by ID using getServiceById and verify that the providerId of the service matches the authenticated provider's ID. This ensures providers can only update their own services.", "status": "done", "testStrategy": "Test with different provider IDs to ensure the function correctly identifies ownership and rejects unauthorized updates."}, {"id": 4, "title": "Implement the service update handler function", "description": "Create the main handler function that processes the update request and calls the updateProviderService operation", "dependencies": [3], "details": "Implement the updateService handler function that combines the validation middleware with the main request handler. The handler should extract the service ID from params, update data from body, verify ownership, call the updateProviderService operation, and format the response according to API standards.", "status": "done", "testStrategy": "Test the handler with various update scenarios including valid updates, invalid data, and unauthorized access attempts."}, {"id": 5, "title": "Add error handling and response formatting", "description": "Implement proper error handling and response formatting for the update service endpoint", "dependencies": [4], "details": "Enhance the handler with comprehensive error handling using the handleApiError utility. Ensure all responses follow the API standards with appropriate status codes (200 for success, 404 for not found, 400 for validation errors, etc.) and consistent response format with status and data/message fields.", "status": "done", "testStrategy": "Test error scenarios including database errors, validation errors, and not found errors to ensure they are properly caught and formatted in the response."}]}, {"id": 15, "title": "Implement Delete Service API", "description": "Create an API endpoint to delete a provider service.", "details": "1. Create a new API handler for DELETE /api/providers/services/:id\n2. Use authentication middleware to verify provider\n3. Verify provider owns the service\n4. Check for dependencies (queues, appointments, etc.)\n5. Call existing Wasp operation to delete service\n6. Format response according to API standards\n7. Add to main.wasp API definitions\n\n```typescript\n// In main.wasp\napi {\n  httpRoute: DELETE /api/providers/services/:id,\n  fn: import { deleteService } from \"@server/api/providers/services\",\n  auth: true\n}\n\n// In @server/api/providers/services.ts\nimport { z } from 'zod';\nimport { deleteProviderService, getServiceById, checkServiceDependencies } from '../../../provider/operations';\nimport { validateRequest } from '../../common/validation';\nimport { handleApiError } from '../../common/errors';\n\nconst deleteServiceSchema = z.object({\n  params: z.object({\n    id: z.string().uuid()\n  })\n});\n\nexport const deleteService = [\n  validateRequest(deleteServiceSchema),\n  async (req, res) => {\n    try {\n      const providerId = req.providerId;\n      const serviceId = req.params.id;\n      \n      // Verify provider owns this service\n      const existingService = await getServiceById(serviceId);\n      if (!existingService || existingService.providerId !== providerId) {\n        return res.status(404).json({\n          status: 'error',\n          message: 'Service not found'\n        });\n      }\n      \n      // Check for dependencies\n      const dependencies = await checkServiceDependencies(serviceId);\n      if (dependencies.hasActiveQueues || dependencies.hasFutureAppointments) {\n        return res.status(409).json({\n          status: 'error',\n          message: 'Cannot delete service with active queues or future appointments',\n          dependencies\n        });\n      }\n      \n      await deleteProviderService(serviceId);\n      \n      return res.status(200).json({\n        status: 'success',\n        message: 'Service deleted successfully'\n      });\n    } catch (error) {\n      return handleApiError(res, error);\n    }\n  }\n];\n```", "testStrategy": "1. Test with valid service ID\n2. Test with service that has dependencies\n3. Test with service not owned by provider\n4. Test with non-existent service ID\n5. Test with authenticated non-provider user\n6. Verify service is actually deleted from database\n7. Verify proper error response when deletion not allowed", "priority": "medium", "dependencies": [2, 3, 4, 12], "status": "done", "subtasks": [{"id": 1, "title": "Create API route definition in main.wasp", "description": "Add the DELETE /api/providers/services/:id endpoint definition to the main.wasp file", "dependencies": [], "details": "Add the API route definition to main.wasp using the proper Wasp syntax. The route should be a DELETE method at /api/providers/services/:id, pointing to the deleteService function that will be implemented in @server/api/providers/services.ts. Ensure the auth property is set to true to require authentication.", "status": "done", "testStrategy": "Verify the syntax is correct by running `wasp start` and checking for any compilation errors."}, {"id": 2, "title": "Implement validation schema and middleware", "description": "Create the Zod validation schema and middleware for the delete service endpoint", "dependencies": [1], "details": "In @server/api/providers/services.ts, implement the deleteServiceSchema using Zod to validate the request parameters. The schema should validate that the id parameter is a valid UUID. Import the validateRequest middleware from the common validation utilities to use with this schema.", "status": "done", "testStrategy": "Test the validation by sending requests with invalid UUIDs and verifying proper error responses."}, {"id": 3, "title": "Implement service ownership verification", "description": "Create the logic to verify that the authenticated provider owns the service being deleted", "dependencies": [2], "details": "Implement the getServiceById function in the provider operations module if it doesn't exist already. Use this function in the deleteService handler to fetch the service and verify that the providerId matches the authenticated provider's ID. Return a 404 error if the service doesn't exist or doesn't belong to the provider.", "status": "done", "testStrategy": "Test with services owned by the provider and services owned by other providers to verify proper authorization checks."}, {"id": 4, "title": "Implement dependency checking logic", "description": "Create the checkServiceDependencies function to verify a service can be safely deleted", "dependencies": [3], "details": "Implement the checkServiceDependencies function in the provider operations module. This function should check if the service has any active queues or future appointments. Return an object with boolean flags indicating the presence of dependencies. In the handler, use this function to prevent deletion of services with dependencies by returning a 409 Conflict response.", "status": "done", "testStrategy": "Test with services that have active queues or future appointments and verify they cannot be deleted. Also test with services that have no dependencies to ensure they can be deleted."}, {"id": 5, "title": "Implement service deletion and response handling", "description": "Complete the deleteProviderService operation and implement proper response formatting", "dependencies": [4], "details": "Implement the deleteProviderService function in the provider operations module to actually delete the service from the database. In the API handler, call this function when all checks pass and format the response according to API standards with a 200 status code and success message. Implement proper error handling using the handleApiError utility for any unexpected errors.", "status": "done", "testStrategy": "Test the complete flow by deleting services and verifying they are removed from the database. Also test error scenarios by mocking database errors and verifying proper error responses."}]}, {"id": 16, "title": "Implement Service Category Management APIs", "description": "Create API endpoints to manage service categories for providers.", "details": "1. Create API handlers for service category CRUD operations\n2. Implement endpoints for:\n   - GET /api/providers/service-categories\n   - POST /api/providers/service-categories\n   - PATCH /api/providers/service-categories/:id\n   - DELETE /api/providers/service-categories/:id\n3. Use authentication middleware to verify provider\n4. Implement Zod schemas for validation\n5. Call existing Wasp operations for category management\n6. Format responses according to API standards\n7. Add to main.wasp API definitions\n\n```typescript\n// In main.wasp\napi {\n  httpRoute: GET /api/providers/service-categories,\n  fn: import { getServiceCategories } from \"@server/api/providers/service-categories\",\n  auth: true\n}\n\napi {\n  httpRoute: POST /api/providers/service-categories,\n  fn: import { createServiceCategory } from \"@server/api/providers/service-categories\",\n  auth: true\n}\n\napi {\n  httpRoute: PATCH /api/providers/service-categories/:id,\n  fn: import { updateServiceCategory } from \"@server/api/providers/service-categories\",\n  auth: true\n}\n\napi {\n  httpRoute: DELETE /api/providers/service-categories/:id,\n  fn: import { deleteServiceCategory } from \"@server/api/providers/service-categories\",\n  auth: true\n}\n\n// In @server/api/providers/service-categories.ts\nimport { z } from 'zod';\nimport { \n  getServiceCategoriesByProviderId,\n  createProviderServiceCategory,\n  updateProviderServiceCategory,\n  deleteProviderServiceCategory,\n  getServiceCategoryById\n} from '../../../provider/operations';\nimport { validateRequest } from '../../common/validation';\nimport { handleApiError } from '../../common/errors';\n\n// Get categories\nexport const getServiceCategories = async (req, res) => {\n  try {\n    const providerId = req.providerId;\n    const categories = await getServiceCategoriesByProviderId(providerId);\n    \n    return res.status(200).json({\n      status: 'success',\n      data: categories\n    });\n  } catch (error) {\n    return handleApiError(res, error);\n  }\n};\n\n// Create category\nconst createCategorySchema = z.object({\n  body: z.object({\n    name: z.string().min(1),\n    description: z.string().optional(),\n    color: z.string().optional(),\n    icon: z.string().optional()\n  })\n});\n\nexport const createServiceCategory = [\n  validateRequest(createCategorySchema),\n  async (req, res) => {\n    try {\n      const providerId = req.providerId;\n      const categoryData = req.body;\n      \n      const newCategory = await createProviderServiceCategory({\n        providerId,\n        ...categoryData\n      });\n      \n      return res.status(201).json({\n        status: 'success',\n        data: newCategory\n      });\n    } catch (error) {\n      return handleApiError(res, error);\n    }\n  }\n];\n\n// Update category\nconst updateCategorySchema = z.object({\n  params: z.object({\n    id: z.string().uuid()\n  }),\n  body: z.object({\n    name: z.string().min(1).optional(),\n    description: z.string().optional(),\n    color: z.string().optional(),\n    icon: z.string().optional()\n  })\n});\n\nexport const updateServiceCategory = [\n  validateRequest(updateCategorySchema),\n  async (req, res) => {\n    try {\n      const providerId = req.providerId;\n      const categoryId = req.params.id;\n      const updateData = req.body;\n      \n      // Verify provider owns this category\n      const existingCategory = await getServiceCategoryById(categoryId);\n      if (!existingCategory || existingCategory.providerId !== providerId) {\n        return res.status(404).json({\n          status: 'error',\n          message: 'Category not found'\n        });\n      }\n      \n      const updatedCategory = await updateProviderServiceCategory({\n        id: categoryId,\n        ...updateData\n      });\n      \n      return res.status(200).json({\n        status: 'success',\n        data: updatedCategory\n      });\n    } catch (error) {\n      return handleApiError(res, error);\n    }\n  }\n];\n\n// Delete category\nconst deleteCategorySchema = z.object({\n  params: z.object({\n    id: z.string().uuid()\n  })\n});\n\nexport const deleteServiceCategory = [\n  validateRequest(deleteCategorySchema),\n  async (req, res) => {\n    try {\n      const providerId = req.providerId;\n      const categoryId = req.params.id;\n      \n      // Verify provider owns this category\n      const existingCategory = await getServiceCategoryById(categoryId);\n      if (!existingCategory || existingCategory.providerId !== providerId) {\n        return res.status(404).json({\n          status: 'error',\n          message: 'Category not found'\n        });\n      }\n      \n      await deleteProviderServiceCategory(categoryId);\n      \n      return res.status(200).json({\n        status: 'success',\n        message: 'Category deleted successfully'\n      });\n    } catch (error) {\n      return handleApiError(res, error);\n    }\n  }\n];\n```", "testStrategy": "1. Test all CRUD operations with valid data\n2. Test with invalid data to verify validation\n3. Test with categories not owned by provider\n4. Test with non-existent category IDs\n5. Test with authenticated non-provider user\n6. Verify categories are correctly created, updated, and deleted in database\n7. Test deletion of category with associated services", "priority": "medium", "dependencies": [2, 3, 4], "status": "done", "subtasks": [{"id": 1, "title": "Define API Routes in main.wasp", "description": "Add the four service category API endpoint definitions to main.wasp file", "dependencies": [], "details": "Add the following API route definitions to main.wasp:\n1. GET /api/providers/service-categories\n2. POST /api/providers/service-categories\n3. PATCH /api/providers/service-categories/:id\n4. DELETE /api/providers/service-categories/:id\n\nEnsure each route points to the correct handler function that will be implemented in the next steps and has auth: true to enforce authentication.", "status": "done", "testStrategy": "Verify the syntax of main.wasp file using Wasp CLI validation commands."}, {"id": 2, "title": "Implement Zod Validation Schemas", "description": "Create validation schemas for request validation on all service category endpoints", "dependencies": [1], "details": "Create the following Zod schemas in the service-categories.ts file:\n1. createCategorySchema - validating the request body for category creation\n2. updateCategorySchema - validating both params (id) and body for category updates\n3. deleteCategorySchema - validating params (id) for category deletion\n\nEnsure proper validation rules for each field (name, description, color, icon) and use UUID validation for IDs.", "status": "done", "testStrategy": "Write unit tests for each schema to verify they correctly validate valid inputs and reject invalid ones."}, {"id": 3, "title": "Implement GET and POST Endpoints", "description": "Create handler functions for retrieving and creating service categories", "dependencies": [2], "details": "In the service-categories.ts file:\n1. Implement getServiceCategories function to retrieve categories for the authenticated provider\n2. Implement createServiceCategory function with validation middleware\n3. Use the existing operations (getServiceCategoriesByProviderId, createProviderServiceCategory)\n4. Format responses according to API standards with status and data fields\n5. Implement proper error handling using handleApiError utility", "status": "done", "testStrategy": "Create integration tests that verify:\n- GET returns all categories for a provider\n- POST creates a new category with valid data\n- Both endpoints return appropriate errors for invalid requests"}, {"id": 4, "title": "Implement PATCH and DELETE Endpoints", "description": "Create handler functions for updating and deleting service categories", "dependencies": [3], "details": "In the service-categories.ts file:\n1. Implement updateServiceCategory function with validation middleware\n2. Implement deleteServiceCategory function with validation middleware\n3. Add ownership verification to ensure providers can only modify their own categories\n4. Use the existing operations (updateProviderServiceCategory, deleteProviderServiceCategory, getServiceCategoryById)\n5. Format responses according to API standards\n6. Implement proper error handling", "status": "done", "testStrategy": "Create integration tests that verify:\n- PATCH updates a category when the provider owns it\n- DELETE removes a category when the provider owns it\n- Both endpoints return 404 when attempting to modify another provider's category"}, {"id": 5, "title": "Create Index File and Export Handlers", "description": "Create an index.ts file to export all handlers and ensure proper imports", "dependencies": [4], "details": "1. Create an index.ts file in the @server/api/providers directory\n2. Export all handler functions from service-categories.ts\n3. Ensure all imports in service-categories.ts are correct\n4. Verify that the path in main.wasp API definitions matches the export structure\n5. Add any missing imports for utility functions (validateRequest, handleApiError)\n6. Document the API endpoints with JSDoc comments", "status": "done", "testStrategy": "Create an end-to-end test that calls each API endpoint and verifies the complete flow works as expected, including authentication, validation, and database operations."}]}, {"id": 17, "title": "Implement Create Queue/Resource API", "description": "Create an API endpoint to allow providers to create new queues/resources.", "details": "1. Create a new API handler for POST /api/providers/queues\n2. Use authentication middleware to verify provider\n3. Implement Zod schema for queue creation validation\n4. Verify provider owns the associated location\n5. Call existing Wasp operation to create queue\n6. Format response according to API standards\n7. Add to main.wasp API definitions\n\n```typescript\n// In main.wasp\napi {\n  httpRoute: POST /api/providers/queues,\n  fn: import { createQueue } from \"@server/api/providers/queues\",\n  auth: true\n}\n\n// In @server/api/providers/queues.ts\nimport { z } from 'zod';\nimport { createProviderQueue } from '../../../queue/operations';\nimport { getLocationById } from '../../../provider/operations';\nimport { validateRequest } from '../../common/validation';\nimport { handleApiError } from '../../common/errors';\n\nconst createQueueSchema = z.object({\n  body: z.object({\n    name: z.string().min(1),\n    description: z.string().optional(),\n    locationId: z.string().uuid(),\n    capacity: z.number().int().positive().optional(),\n    isActive: z.boolean().optional().default(true),\n    color: z.string().optional(),\n    icon: z.string().optional(),\n    allowedServices: z.array(z.string().uuid()).optional()\n  })\n});\n\nexport const createQueue = [\n  validateRequest(createQueueSchema),\n  async (req, res) => {\n    try {\n      const providerId = req.providerId;\n      const queueData = req.body;\n      \n      // Verify provider owns the location\n      const location = await getLocationById(queueData.locationId);\n      if (!location || location.providerId !== providerId) {\n        return res.status(404).json({\n          status: 'error',\n          message: 'Location not found'\n        });\n      }\n      \n      const newQueue = await createProviderQueue({\n        providerId,\n        ...queueData\n      });\n      \n      return res.status(201).json({\n        status: 'success',\n        data: newQueue\n      });\n    } catch (error) {\n      return handleApiError(res, error);\n    }\n  }\n];\n```", "testStrategy": "1. Test with valid queue data\n2. Test with missing required fields\n3. Test with location not owned by provider\n4. Test with non-existent location ID\n5. Test with authenticated non-provider user\n6. Verify queue is created in database with correct provider and location association\n7. Verify response format matches API standards\n8. Test with allowed services specified", "priority": "high", "dependencies": [2, 3, 4, 8], "status": "done", "subtasks": [{"id": 1, "title": "Define Zod schema for queue creation validation", "description": "Create a robust validation schema using Zod to validate the request body for queue creation", "dependencies": [], "details": "Implement the createQueueSchema in @server/api/providers/queues.ts that validates all required and optional fields for queue creation. Include validation for name (required, non-empty string), description (optional string), locationId (valid UUID), capacity (optional positive integer), isActive (optional boolean with default true), color (optional string), icon (optional string), and allowedServices (optional array of UUIDs).", "status": "done", "testStrategy": "Write unit tests for the schema to verify it correctly validates valid inputs and rejects invalid inputs, including edge cases like empty strings, negative numbers, and malformed UUIDs."}, {"id": 2, "title": "Implement location ownership verification", "description": "Create a function to verify that the provider making the request owns the location specified in the queue creation request", "dependencies": [1], "details": "Implement the location verification logic that calls getLocationById and checks if the location's providerId matches the authenticated provider's ID. This should be implemented within the request handler function in @server/api/providers/queues.ts. Return a 404 error with appropriate message if the location doesn't exist or doesn't belong to the provider.", "status": "done", "testStrategy": "Test this function with various scenarios: valid location owned by provider, valid location not owned by provider, and non-existent location ID."}, {"id": 3, "title": "Create queue operation function", "description": "Implement or update the createProviderQueue operation function that handles the database interaction for creating a new queue", "dependencies": [1, 2], "details": "In the operations file (likely @server/queue/operations.ts), implement the createProviderQueue function that takes the validated queue data and creates a new queue in the database. This function should handle all database interactions, including creating relationships with allowed services if specified. Return the newly created queue object with all its properties.", "status": "done", "testStrategy": "Write integration tests that verify the function correctly creates queue records in the database with all specified properties and relationships."}, {"id": 4, "title": "Implement API request handler", "description": "Create the full API request handler that processes the queue creation request", "dependencies": [1, 2, 3], "details": "Complete the implementation of the createQueue handler in @server/api/providers/queues.ts. This should use the validateRequest middleware with the createQueueSchema, extract the providerId from the request, verify location ownership, call the createProviderQueue operation, and format the response according to API standards. Include proper error handling using the handleApiError utility.", "status": "done", "testStrategy": "Write API tests that send various request payloads to test the full request flow, including validation errors, authorization errors, and successful creation."}, {"id": 5, "title": "Add API endpoint to main.wasp", "description": "Register the new API endpoint in the main.wasp file to make it available in the application", "dependencies": [4], "details": "Add the API definition to main.wasp using the correct Wasp syntax. The definition should specify the HTTP route as POST /api/providers/queues, import the createQueue handler function from @server/api/providers/queues, and set auth to true to ensure the endpoint requires authentication. Ensure the import path is correct and follows Wasp conventions.", "status": "done", "testStrategy": "Verify the API is correctly registered by starting the application and checking that the endpoint is available and protected by authentication."}]}, {"id": 18, "title": "Implement Get Queues by Location API", "description": "Create an API endpoint to retrieve all queues/resources associated with a specific provider location.", "details": "1. Create a new API handler for GET /api/providers/locations/:locationId/queues\n2. Use authentication middleware to verify provider\n3. Verify provider owns the location\n4. Implement optional query parameters for filtering\n5. Call existing Wasp operation to fetch queues by location\n6. Format response according to API standards\n7. Add to main.wasp API definitions\n\n```typescript\n// In main.wasp\napi {\n  httpRoute: GET /api/providers/locations/:locationId/queues,\n  fn: import { getQueuesByLocation } from \"@server/api/providers/queues\",\n  auth: true\n}\n\n// In @server/api/providers/queues.ts\nimport { z } from 'zod';\nimport { getQueuesByLocationId } from '../../../queue/operations';\nimport { getLocationById } from '../../../provider/operations';\nimport { validateRequest } from '../../common/validation';\nimport { handleApiError } from '../../common/errors';\n\nconst getQueuesByLocationSchema = z.object({\n  params: z.object({\n    locationId: z.string().uuid()\n  }),\n  query: z.object({\n    isActive: z.enum(['true', 'false']).optional(),\n    search: z.string().optional(),\n    includeServices: z.enum(['true', 'false']).optional()\n  })\n});\n\nexport const getQueuesByLocation = [\n  validateRequest(getQueuesByLocationSchema),\n  async (req, res) => {\n    try {\n      const providerId = req.providerId;\n      const locationId = req.params.locationId;\n      const { isActive, search, includeServices } = req.query;\n      \n      // Verify provider owns the location\n      const location = await getLocationById(locationId);\n      if (!location || location.providerId !== providerId) {\n        return res.status(404).json({\n          status: 'error',\n          message: 'Location not found'\n        });\n      }\n      \n      // Convert string query params to appropriate types\n      const filters = {\n        isActive: isActive ? isActive === 'true' : undefined,\n        search: search || undefined,\n        includeServices: includeServices === 'true'\n      };\n      \n      const queues = await getQueuesByLocationId(locationId, filters);\n      \n      return res.status(200).json({\n        status: 'success',\n        data: queues\n      });\n    } catch (error) {\n      return handleApiError(res, error);\n    }\n  }\n];\n```", "testStrategy": "1. Test with location having multiple queues\n2. Test with location having no queues\n3. Test with various filter combinations\n4. Test with location not owned by provider\n5. Test with non-existent location ID\n6. Test with authenticated non-provider user\n7. Verify response format matches API standards\n8. Verify correct queues are returned based on filters\n9. Test includeServices parameter to check if services are included in response", "priority": "high", "dependencies": [2, 3, 4, 17], "status": "done", "subtasks": [{"id": 1, "title": "Create queue operations function in operations.ts", "description": "Implement the getQueuesByLocationId operation function that will fetch queues by location with filtering capabilities", "dependencies": [], "details": "Create or update the file '@server/queue/operations.ts' to implement the getQueuesByLocationId function. This function should accept a locationId parameter and an optional filters object with isActive, search, and includeServices properties. Use Prisma to query the database for queues associated with the given locationId. Apply filters based on the provided parameters. If includeServices is true, include related service entities in the query results.", "status": "done", "testStrategy": "Create unit tests for the getQueuesByLocationId function with various filter combinations. Mock the Prisma client to test different query scenarios."}, {"id": 2, "title": "Implement validation schema and middleware", "description": "Create the request validation schema and middleware for the API endpoint", "dependencies": [], "details": "In '@server/api/common/validation.ts', ensure the validateRequest middleware is properly implemented. Then in '@server/api/providers/queues.ts', define the getQueuesByLocationSchema using zod to validate the request parameters and query strings. The schema should validate locationId as a UUID and optional query parameters (isActive, search, includeServices) with appropriate types.", "status": "done", "testStrategy": "Test the validation schema with valid and invalid inputs to ensure proper validation behavior."}, {"id": 3, "title": "Implement the API handler function", "description": "Create the getQueuesByLocation handler function that processes the request and returns queues data", "dependencies": [1, 2], "details": "In '@server/api/providers/queues.ts', implement the getQueuesByLocation handler function as shown in the template. The function should: 1) Extract providerId from the request, 2) Verify the provider owns the location by calling getLocationById, 3) Convert query string parameters to appropriate types, 4) Call the getQueuesByLocationId operation with the locationId and filters, and 5) Return a formatted response with the queues data. Include proper error handling using the handleApiError utility.", "status": "done", "testStrategy": "Create integration tests for the API handler, mocking the necessary dependencies (getLocationById, getQueuesByLocationId) to test different scenarios including successful requests, unauthorized access, and error handling."}, {"id": 4, "title": "Add API route definition to main.wasp", "description": "Update the main.wasp file to include the new API endpoint definition", "dependencies": [3], "details": "Add the API route definition to main.wasp as shown in the template. The route should be defined as 'GET /api/providers/locations/:locationId/queues' and should import the getQueuesByLocation handler from '@server/api/providers/queues'. Set auth to true to ensure the endpoint requires authentication.", "status": "done", "testStrategy": "Verify the route is correctly defined by running the Wasp compiler and checking for any errors."}, {"id": 5, "title": "Implement error handling and testing", "description": "Add comprehensive error handling and create tests for the API endpoint", "dependencies": [3, 4], "details": "Ensure the handleApiError utility in '@server/api/common/errors.ts' is properly implemented to handle various error types. Update the getQueuesByLocation handler to use try/catch blocks with this utility. Create test cases for the API endpoint covering: 1) Successful queue retrieval, 2) Location not found, 3) Unauthorized access (provider doesn't own location), 4) Invalid parameters, and 5) Server errors. Use Jest and Supertest for API testing.", "status": "done", "testStrategy": "Create end-to-end tests that make actual API calls to the endpoint with various scenarios. Test all query parameter combinations and error conditions. Verify the response format and status codes match the API standards."}]}, {"id": 19, "title": "Implement Update Queue API", "description": "Create an API endpoint to update an existing provider queue/resource.", "details": "1. Create a new API handler for PATCH /api/providers/queues/:id\n2. Use authentication middleware to verify provider\n3. Implement Zod schema for queue update validation\n4. Verify provider owns the queue\n5. Call existing Wasp operation to update queue\n6. Format response according to API standards\n7. Add to main.wasp API definitions\n\n```typescript\n// In main.wasp\napi {\n  httpRoute: PATCH /api/providers/queues/:id,\n  fn: import { updateQueue } from \"@server/api/providers/queues\",\n  auth: true\n}\n\n// In @server/api/providers/queues.ts\nimport { z } from 'zod';\nimport { updateProviderQueue, getQueueById } from '../../../queue/operations';\nimport { getLocationById } from '../../../provider/operations';\nimport { validateRequest } from '../../common/validation';\nimport { handleApiError } from '../../common/errors';\n\nconst updateQueueSchema = z.object({\n  params: z.object({\n    id: z.string().uuid()\n  }),\n  body: z.object({\n    name: z.string().min(1).optional(),\n    description: z.string().optional(),\n    locationId: z.string().uuid().optional(),\n    capacity: z.number().int().positive().optional(),\n    isActive: z.boolean().optional(),\n    color: z.string().optional(),\n    icon: z.string().optional()\n  })\n});\n\nexport const updateQueue = [\n  validateRequest(updateQueueSchema),\n  async (req, res) => {\n    try {\n      const providerId = req.providerId;\n      const queueId = req.params.id;\n      const updateData = req.body;\n      \n      // Verify provider owns this queue\n      const existingQueue = await getQueueById(queueId);\n      if (!existingQueue || existingQueue.providerId !== providerId) {\n        return res.status(404).json({\n          status: 'error',\n          message: 'Queue not found'\n        });\n      }\n      \n      // If locationId is being updated, verify provider owns that location\n      if (updateData.locationId) {\n        const location = await getLocationById(updateData.locationId);\n        if (!location || location.providerId !== providerId) {\n          return res.status(404).json({\n            status: 'error',\n            message: 'Location not found'\n          });\n        }\n      }\n      \n      const updatedQueue = await updateProviderQueue({\n        id: queueId,\n        ...updateData\n      });\n      \n      return res.status(200).json({\n        status: 'success',\n        data: updatedQueue\n      });\n    } catch (error) {\n      return handleApiError(res, error);\n    }\n  }\n];\n```", "testStrategy": "1. Test with valid update data\n2. Test with invalid data to verify validation\n3. Test with queue not owned by provider\n4. Test with non-existent queue ID\n5. Test with location change to a location not owned by provider\n6. Test with authenticated non-provider user\n7. Verify queue is updated correctly in database\n8. Test changing location of a queue", "priority": "medium", "dependencies": [2, 3, 4, 17], "status": "done", "subtasks": [{"id": 1, "title": "Create Update Queue API Definition in main.wasp", "description": "Add the PATCH API endpoint definition to main.wasp file to register the update queue endpoint with the Wasp framework.", "dependencies": [], "details": "Open main.wasp and add the following API definition in the appropriate section:\n```\napi {\n  httpRoute: PATCH /api/providers/queues/:id,\n  fn: import { updateQueue } from \"@server/api/providers/queues\",\n  auth: true\n}\n```\nEnsure this is placed with other API definitions and follows the correct syntax for Wasp API declarations.", "status": "done", "testStrategy": "Verify the Wasp compilation succeeds without errors after adding the API definition."}, {"id": 2, "title": "Implement Zod Validation Schema", "description": "Create the Zod schema for validating the update queue request parameters and body.", "dependencies": [1], "details": "In the @server/api/providers/queues.ts file, implement the updateQueueSchema using Zod:\n```typescript\nconst updateQueueSchema = z.object({\n  params: z.object({\n    id: z.string().uuid()\n  }),\n  body: z.object({\n    name: z.string().min(1).optional(),\n    description: z.string().optional(),\n    locationId: z.string().uuid().optional(),\n    capacity: z.number().int().positive().optional(),\n    isActive: z.boolean().optional(),\n    color: z.string().optional(),\n    icon: z.string().optional()\n  })\n});\n```\nEnsure all imports are properly defined at the top of the file, including z from 'zod' and any validation utilities.", "status": "done", "testStrategy": "Create test cases with valid and invalid request bodies to verify the schema correctly validates input data."}, {"id": 3, "title": "Implement Queue Ownership Verification", "description": "Create the logic to verify that the provider making the request owns the queue they're trying to update.", "dependencies": [2], "details": "In the updateQueue handler function, implement the ownership verification logic:\n```typescript\n// Verify provider owns this queue\nconst existingQueue = await getQueueById(queueId);\nif (!existingQueue || existingQueue.providerId !== providerId) {\n  return res.status(404).json({\n    status: 'error',\n    message: 'Queue not found'\n  });\n}\n\n// If locationId is being updated, verify provider owns that location\nif (updateData.locationId) {\n  const location = await getLocationById(updateData.locationId);\n  if (!location || location.providerId !== providerId) {\n    return res.status(404).json({\n      status: 'error',\n      message: 'Location not found'\n    });\n  }\n}\n```\nEnsure the getQueueById and getLocationById operations are properly imported.", "status": "done", "testStrategy": "Test with different provider IDs to verify that only the queue owner can update it, and that location ownership is properly verified."}, {"id": 4, "title": "Implement Queue Update Logic", "description": "Implement the core functionality to update the queue with the validated data.", "dependencies": [3], "details": "Complete the updateQueue handler by adding the logic to call the updateProviderQueue operation:\n```typescript\nconst updatedQueue = await updateProviderQueue({\n  id: queueId,\n  ...updateData\n});\n\nreturn res.status(200).json({\n  status: 'success',\n  data: updatedQueue\n});\n```\nEnsure the updateProviderQueue operation is properly imported and that the response follows the API standards with the correct status code and format.", "status": "done", "testStrategy": "Test updating various queue properties individually and in combination to verify the update operation works correctly."}, {"id": 5, "title": "Implement Error Handling and Complete API Handler", "description": "Finalize the API handler with proper error handling and export the complete middleware array.", "dependencies": [4], "details": "Wrap the handler logic in a try-catch block and use the handleApiError utility for consistent error handling:\n```typescript\nexport const updateQueue = [\n  validateRequest(updateQueueSchema),\n  async (req, res) => {\n    try {\n      const providerId = req.providerId;\n      const queueId = req.params.id;\n      const updateData = req.body;\n      \n      // Verification and update logic from previous subtasks\n      \n    } catch (error) {\n      return handleApiError(res, error);\n    }\n  }\n];\n```\nEnsure all necessary imports are at the top of the file, including handleApiError and validateRequest utilities.", "status": "done", "testStrategy": "Test with various error scenarios (invalid IDs, database errors, etc.) to verify proper error handling and response formatting."}]}, {"id": 20, "title": "Implement Delete Queue API", "description": "Create an API endpoint to delete a provider queue/resource.", "details": "1. Create a new API handler for DELETE /api/providers/queues/:id\n2. Use authentication middleware to verify provider\n3. Verify provider owns the queue\n4. Check for dependencies (appointments, etc.)\n5. Call existing Wasp operation to delete queue\n6. Format response according to API standards\n7. Add to main.wasp API definitions\n\n```typescript\n// In main.wasp\napi {\n  httpRoute: DELETE /api/providers/queues/:id,\n  fn: import { deleteQueue } from \"@server/api/providers/queues\",\n  auth: true\n}\n\n// In @server/api/providers/queues.ts\nimport { z } from 'zod';\nimport { deleteProviderQueue, getQueueById, checkQueueDependencies } from '../../../queue/operations';\nimport { validateRequest } from '../../common/validation';\nimport { handleApiError } from '../../common/errors';\n\nconst deleteQueueSchema = z.object({\n  params: z.object({\n    id: z.string().uuid()\n  })\n});\n\nexport const deleteQueue = [\n  validateRequest(deleteQueueSchema),\n  async (req, res) => {\n    try {\n      const providerId = req.providerId;\n      const queueId = req.params.id;\n      \n      // Verify provider owns this queue\n      const existingQueue = await getQueueById(queueId);\n      if (!existingQueue || existingQueue.providerId !== providerId) {\n        return res.status(404).json({\n          status: 'error',\n          message: 'Queue not found'\n        });\n      }\n      \n      // Check for dependencies\n      const dependencies = await checkQueueDependencies(queueId);\n      if (dependencies.hasFutureAppointments) {\n        return res.status(409).json({\n          status: 'error',\n          message: 'Cannot delete queue with future appointments',\n          dependencies\n        });\n      }\n      \n      await deleteProviderQueue(queueId);\n      \n      return res.status(200).json({\n        status: 'success',\n        message: 'Queue deleted successfully'\n      });\n    } catch (error) {\n      return handleApiError(res, error);\n    }\n  }\n];\n```", "testStrategy": "1. Test with valid queue ID\n2. Test with queue that has dependencies\n3. Test with queue not owned by provider\n4. Test with non-existent queue ID\n5. Test with authenticated non-provider user\n6. Verify queue is actually deleted from database\n7. Verify proper error response when deletion not allowed", "priority": "medium", "dependencies": [2, 3, 4, 17], "status": "done", "subtasks": [{"id": 1, "title": "Create Queue Dependency Check Operation", "description": "Implement the checkQueueDependencies operation to verify if a queue can be safely deleted by checking for future appointments or other dependencies.", "dependencies": [], "details": "Create a new file `src/queue/operations.ts` (if it doesn't exist) and implement the `checkQueueDependencies` function. This function should query the database for any future appointments associated with the queue and return an object with a boolean flag indicating if dependencies exist. Use Prisma queries to check for appointments where the date is in the future and the queue ID matches.", "status": "done", "testStrategy": "Write unit tests that verify the function correctly identifies queues with future appointments and those without dependencies."}, {"id": 2, "title": "Implement Queue Deletion Operation", "description": "Create the deleteProviderQueue operation that handles the actual deletion of a queue from the database.", "dependencies": [1], "details": "In the same `src/queue/operations.ts` file, implement the `deleteProviderQueue` function that takes a queue ID and removes the corresponding record from the database. Include proper error handling for cases where the queue doesn't exist. Use Prisma's delete operation and ensure all related records are properly handled (either deleted or updated as appropriate).", "status": "done", "testStrategy": "Test the operation with both valid and invalid queue IDs, verifying that deletion works correctly and appropriate errors are thrown for invalid cases."}, {"id": 3, "title": "Create Queue Validation Schema", "description": "Define the Zod validation schema for the delete queue API endpoint to ensure proper request validation.", "dependencies": [], "details": "In the file `src/server/api/providers/queues.ts`, implement the `deleteQueueSchema` using Zod to validate that the request contains a valid UUID in the params. This schema will be used by the validateRequest middleware to ensure all incoming requests are properly formatted.", "status": "done", "testStrategy": "Test the schema with various input formats to ensure it correctly validates UUIDs and rejects invalid formats."}, {"id": 4, "title": "Implement Delete Queue API Handler", "description": "Create the API handler function that processes delete requests for provider queues.", "dependencies": [1, 2, 3], "details": "Complete the implementation of the `deleteQueue` handler in `src/server/api/providers/queues.ts`. The handler should verify the provider owns the queue, check for dependencies using the checkQueueDependencies function, and then call deleteProviderQueue if deletion is allowed. Ensure proper HTTP status codes and response formats are used for all scenarios (success, not found, conflict due to dependencies, etc.).", "status": "done", "testStrategy": "Write integration tests that verify the API correctly handles all scenarios: successful deletion, attempting to delete a queue owned by another provider, and attempting to delete a queue with future appointments."}, {"id": 5, "title": "Add API Route to main.wasp", "description": "Register the delete queue API endpoint in the main.wasp file to make it available in the application.", "dependencies": [4], "details": "Add the API route definition to the main.wasp file using the correct Wasp syntax. The route should be defined as `DELETE /api/providers/queues/:id` and should reference the deleteQueue handler function from the appropriate file. Ensure authentication is required for this endpoint by setting `auth: true` in the API definition.", "status": "done", "testStrategy": "Verify the API is correctly registered by starting the application and testing the endpoint with tools like Postman or curl. Ensure authentication is properly enforced."}]}, {"id": 21, "title": "Implement Queue Service Assignment APIs", "description": "Create API endpoints to manage service assignments to queues/resources.", "details": "1. Create API handlers for queue service assignment operations\n2. Implement endpoints for:\n   - GET /api/providers/queues/:id/services\n   - POST /api/providers/queues/:id/services\n   - DELETE /api/providers/queues/:id/services/:serviceId\n3. Use authentication middleware to verify provider\n4. Implement Zod schemas for validation\n5. Call existing Wasp operations for queue service management\n6. Format responses according to API standards\n7. Add to main.wasp API definitions\n\n```typescript\n// In main.wasp\napi {\n  httpRoute: GET /api/providers/queues/:id/services,\n  fn: import { getQueueServices } from \"@server/api/providers/queue-services\",\n  auth: true\n}\n\napi {\n  httpRoute: POST /api/providers/queues/:id/services,\n  fn: import { assignServiceToQueue } from \"@server/api/providers/queue-services\",\n  auth: true\n}\n\napi {\n  httpRoute: DELETE /api/providers/queues/:id/services/:serviceId,\n  fn: import { removeServiceFromQueue } from \"@server/api/providers/queue-services\",\n  auth: true\n}\n\n// In @server/api/providers/queue-services.ts\nimport { z } from 'zod';\nimport { \n  getQueueById, \n  getQueueServices as getQueueServicesOp,\n  assignServiceToQueue as assignServiceOp,\n  removeServiceFromQueue as removeServiceOp\n} from '../../../queue/operations';\nimport { getServiceById } from '../../../provider/operations';\nimport { validateRequest } from '../../common/validation';\nimport { handleApiError } from '../../common/errors';\n\n// Get queue services\nconst getQueueServicesSchema = z.object({\n  params: z.object({\n    id: z.string().uuid()\n  })\n});\n\nexport const getQueueServices = [\n  validateRequest(getQueueServicesSchema),\n  async (req, res) => {\n    try {\n      const providerId = req.providerId;\n      const queueId = req.params.id;\n      \n      // Verify provider owns this queue\n      const existingQueue = await getQueueById(queueId);\n      if (!existingQueue || existingQueue.providerId !== providerId) {\n        return res.status(404).json({\n          status: 'error',\n          message: 'Queue not found'\n        });\n      }\n      \n      const services = await getQueueServicesOp(queueId);\n      \n      return res.status(200).json({\n        status: 'success',\n        data: services\n      });\n    } catch (error) {\n      return handleApiError(res, error);\n    }\n  }\n];\n\n// Assign service to queue\nconst assignServiceSchema = z.object({\n  params: z.object({\n    id: z.string().uuid()\n  }),\n  body: z.object({\n    serviceId: z.string().uuid()\n  })\n});\n\nexport const assignServiceToQueue = [\n  validateRequest(assignServiceSchema),\n  async (req, res) => {\n    try {\n      const providerId = req.providerId;\n      const queueId = req.params.id;\n      const { serviceId } = req.body;\n      \n      // Verify provider owns this queue\n      const existingQueue = await getQueueById(queueId);\n      if (!existingQueue || existingQueue.providerId !== providerId) {\n        return res.status(404).json({\n          status: 'error',\n          message: 'Queue not found'\n        });\n      }\n      \n      // Verify provider owns this service\n      const existingService = await getServiceById(serviceId);\n      if (!existingService || existingService.providerId !== providerId) {\n        return res.status(404).json({\n          status: 'error',\n          message: 'Service not found'\n        });\n      }\n      \n      await assignServiceOp(queueId, serviceId);\n      \n      return res.status(200).json({\n        status: 'success',\n        message: 'Service assigned to queue successfully'\n      });\n    } catch (error) {\n      return handleApiError(res, error);\n    }\n  }\n];\n\n// Remove service from queue\nconst removeServiceSchema = z.object({\n  params: z.object({\n    id: z.string().uuid(),\n    serviceId: z.string().uuid()\n  })\n});\n\nexport const removeServiceFromQueue = [\n  validateRequest(removeServiceSchema),\n  async (req, res) => {\n    try {\n      const providerId = req.providerId;\n      const queueId = req.params.id;\n      const serviceId = req.params.serviceId;\n      \n      // Verify provider owns this queue\n      const existingQueue = await getQueueById(queueId);\n      if (!existingQueue || existingQueue.providerId !== providerId) {\n        return res.status(404).json({\n          status: 'error',\n          message: 'Queue not found'\n        });\n      }\n      \n      await removeServiceOp(queueId, serviceId);\n      \n      return res.status(200).json({\n        status: 'success',\n        message: 'Service removed from queue successfully'\n      });\n    } catch (error) {\n      return handleApiError(res, error);\n    }\n  }\n];\n```", "testStrategy": "1. Test all operations with valid data\n2. Test with invalid data to verify validation\n3. Test with queue not owned by provider\n4. Test with service not owned by provider\n5. Test with non-existent queue or service IDs\n6. Test with authenticated non-provider user\n7. Verify services are correctly assigned to and removed from queues in database\n8. Test assigning a service that's already assigned\n9. Test removing a service that's not assigned", "priority": "medium", "dependencies": [2, 3, 4, 12, 17], "status": "done", "subtasks": [{"id": 1, "title": "Create API route definitions in main.wasp", "description": "Add the three API route definitions to main.wasp for queue service management: GET, POST, and DELETE endpoints.", "dependencies": [], "details": "Add the following API route definitions to main.wasp:\n1. GET /api/providers/queues/:id/services\n2. POST /api/providers/queues/:id/services\n3. DELETE /api/providers/queues/:id/services/:serviceId\n\nEnsure each route has the correct HTTP method, path, handler function import path, and auth requirement set to true.", "status": "done", "testStrategy": "Verify the Wasp compiler successfully processes the new API routes by running `wasp start` and checking for any compilation errors."}, {"id": 2, "title": "Implement Zod validation schemas", "description": "Create the Zod validation schemas for request validation in the queue services API handlers.", "dependencies": [1], "details": "Define three Zod schemas in @server/api/providers/queue-services.ts:\n1. getQueueServicesSchema - validate the queue ID in params\n2. assignServiceSchema - validate queue ID in params and service ID in body\n3. removeServiceSchema - validate queue ID and service ID in params\n\nEnsure all IDs are validated as UUID strings.", "status": "done", "testStrategy": "Write unit tests for each schema to verify they correctly validate valid requests and reject invalid ones."}, {"id": 3, "title": "Implement GET queue services endpoint handler", "description": "Create the handler function for retrieving services assigned to a queue.", "dependencies": [2], "details": "Implement the getQueueServices handler in @server/api/providers/queue-services.ts that:\n1. Uses the validation middleware with getQueueServicesSchema\n2. Extracts providerId from the request and queueId from params\n3. Verifies the provider owns the queue\n4. Calls the getQueueServicesOp operation\n5. Returns a formatted success response with the services data\n6. Handles errors with the handleApiError utility", "status": "done", "testStrategy": "Test the endpoint with valid and invalid queue IDs, verifying proper authorization checks and response formatting."}, {"id": 4, "title": "Implement POST service assignment endpoint handler", "description": "Create the handler function for assigning a service to a queue.", "dependencies": [2], "details": "Implement the assignServiceToQueue handler in @server/api/providers/queue-services.ts that:\n1. Uses the validation middleware with assignServiceSchema\n2. Extracts providerId from the request, queueId from params, and serviceId from body\n3. Verifies the provider owns both the queue and the service\n4. Calls the assignServiceOp operation\n5. Returns a formatted success response\n6. Handles errors with the handleApiError utility", "status": "done", "testStrategy": "Test the endpoint with various combinations of valid/invalid queue and service IDs, verifying proper authorization checks and error handling."}, {"id": 5, "title": "Implement DELETE service removal endpoint handler", "description": "Create the handler function for removing a service from a queue.", "dependencies": [2], "details": "Implement the removeServiceFromQueue handler in @server/api/providers/queue-services.ts that:\n1. Uses the validation middleware with removeServiceSchema\n2. Extracts providerId from the request, queueId and serviceId from params\n3. Verifies the provider owns the queue\n4. Calls the removeServiceOp operation\n5. Returns a formatted success response\n6. Handles errors with the handleApiError utility", "status": "done", "testStrategy": "Test the endpoint with various combinations of valid/invalid queue and service IDs, verifying proper authorization checks and error handling."}]}, {"id": 22, "title": "Implement Schedule Management APIs", "description": "Create API endpoints to manage provider opening hours and schedules.", "details": "1. Create API handlers for schedule management operations\n2. Implement endpoints for:\n   - GET /api/providers/schedules\n   - POST /api/providers/schedules\n   - PATCH /api/providers/schedules/:id\n   - DELETE /api/providers/schedules/:id\n3. Use authentication middleware to verify provider\n4. Implement Zod schemas for validation\n5. Call existing Wasp operations for schedule management\n6. Format responses according to API standards\n7. Add to main.wasp API definitions\n\n```typescript\n// In main.wasp\napi {\n  httpRoute: GET /api/providers/schedules,\n  fn: import { getProviderSchedules } from \"@server/api/providers/schedules\",\n  auth: true\n}\n\napi {\n  httpRoute: POST /api/providers/schedules,\n  fn: import { createSchedule } from \"@server/api/providers/schedules\",\n  auth: true\n}\n\napi {\n  httpRoute: PATCH /api/providers/schedules/:id,\n  fn: import { updateSchedule } from \"@server/api/providers/schedules\",\n  auth: true\n}\n\napi {\n  httpRoute: DELETE /api/providers/schedules/:id,\n  fn: import { deleteSchedule } from \"@server/api/providers/schedules\",\n  auth: true\n}\n\n// In @server/api/providers/schedules.ts\nimport { z } from 'zod';\nimport { \n  getProviderSchedules as getSchedulesOp,\n  createSchedule as createScheduleOp,\n  updateSchedule as updateScheduleOp,\n  deleteSchedule as deleteScheduleOp,\n  getScheduleById\n} from '../../../provider/operations';\nimport { validateRequest } from '../../common/validation';\nimport { handleApiError } from '../../common/errors';\n\n// Get schedules\nconst getSchedulesSchema = z.object({\n  query: z.object({\n    queueId: z.string().uuid().optional(),\n    locationId: z.string().uuid().optional(),\n    from: z.string().optional(), // ISO date string\n    to: z.string().optional() // ISO date string\n  })\n});\n\nexport const getProviderSchedules = [\n  validateRequest(getSchedulesSchema),\n  async (req, res) => {\n    try {\n      const providerId = req.providerId;\n      const { queueId, locationId, from, to } = req.query;\n      \n      const filters = {\n        queueId: queueId || undefined,\n        locationId: locationId || undefined,\n        from: from ? new Date(from) : undefined,\n        to: to ? new Date(to) : undefined\n      };\n      \n      const schedules = await getSchedulesOp(providerId, filters);\n      \n      return res.status(200).json({\n        status: 'success',\n        data: schedules\n      });\n    } catch (error) {\n      return handleApiError(res, error);\n    }\n  }\n];\n\n// Create schedule\nconst createScheduleSchema = z.object({\n  body: z.object({\n    queueId: z.string().uuid().optional(),\n    locationId: z.string().uuid().optional(),\n    dayOfWeek: z.number().int().min(0).max(6),\n    startTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/), // HH:MM format\n    endTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/), // HH:MM format\n    isActive: z.boolean().optional().default(true),\n    effectiveFrom: z.string().optional(), // ISO date string\n    effectiveTo: z.string().optional() // ISO date string\n  })\n});\n\nexport const createSchedule = [\n  validateRequest(createScheduleSchema),\n  async (req, res) => {\n    try {\n      const providerId = req.providerId;\n      const scheduleData = req.body;\n      \n      // Convert string dates to Date objects if provided\n      if (scheduleData.effectiveFrom) {\n        scheduleData.effectiveFrom = new Date(scheduleData.effectiveFrom);\n      }\n      if (scheduleData.effectiveTo) {\n        scheduleData.effectiveTo = new Date(scheduleData.effectiveTo);\n      }\n      \n      const newSchedule = await createScheduleOp({\n        providerId,\n        ...scheduleData\n      });\n      \n      return res.status(201).json({\n        status: 'success',\n        data: newSchedule\n      });\n    } catch (error) {\n      return handleApiError(res, error);\n    }\n  }\n];\n\n// Update schedule\nconst updateScheduleSchema = z.object({\n  params: z.object({\n    id: z.string().uuid()\n  }),\n  body: z.object({\n    dayOfWeek: z.number().int().min(0).max(6).optional(),\n    startTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(), // HH:MM format\n    endTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(), // HH:MM format\n    isActive: z.boolean().optional(),\n    effectiveFrom: z.string().optional(), // ISO date string\n    effectiveTo: z.string().optional() // ISO date string\n  })\n});\n\nexport const updateSchedule = [\n  validateRequest(updateScheduleSchema),\n  async (req, res) => {\n    try {\n      const providerId = req.providerId;\n      const scheduleId = req.params.id;\n      const updateData = req.body;\n      \n      // Verify provider owns this schedule\n      const existingSchedule = await getScheduleById(scheduleId);\n      if (!existingSchedule || existingSchedule.providerId !== providerId) {\n        return res.status(404).json({\n          status: 'error',\n          message: 'Schedule not found'\n        });\n      }\n      \n      // Convert string dates to Date objects if provided\n      if (updateData.effectiveFrom) {\n        updateData.effectiveFrom = new Date(updateData.effectiveFrom);\n      }\n      if (updateData.effectiveTo) {\n        updateData.effectiveTo = new Date(updateData.effectiveTo);\n      }\n      \n      const updatedSchedule = await updateScheduleOp({\n        id: scheduleId,\n        ...updateData\n      });\n      \n      return res.status(200).json({\n        status: 'success',\n        data: updatedSchedule\n      });\n    } catch (error) {\n      return handleApiError(res, error);\n    }\n  }\n];\n\n// Delete schedule\nconst deleteScheduleSchema = z.object({\n  params: z.object({\n    id: z.string().uuid()\n  })\n});\n\nexport const deleteSchedule = [\n  validateRequest(deleteScheduleSchema),\n  async (req, res) => {\n    try {\n      const providerId = req.providerId;\n      const scheduleId = req.params.id;\n      \n      // Verify provider owns this schedule\n      const existingSchedule = await getScheduleById(scheduleId);\n      if (!existingSchedule || existingSchedule.providerId !== providerId) {\n        return res.status(404).json({\n          status: 'error',\n          message: 'Schedule not found'\n        });\n      }\n      \n      await deleteScheduleOp(scheduleId);\n      \n      return res.status(200).json({\n        status: 'success',\n        message: 'Schedule deleted successfully'\n      });\n    } catch (error) {\n      return handleApiError(res, error);\n    }\n  }\n];\n```", "testStrategy": "1. Test all CRUD operations with valid data\n2. Test with invalid data to verify validation\n3. Test with schedule not owned by provider\n4. Test with non-existent schedule IDs\n5. Test with authenticated non-provider user\n6. Verify schedules are correctly created, updated, and deleted in database\n7. Test time format validation\n8. Test overlapping schedules\n9. Test filtering schedules by queue, location, and date range", "priority": "high", "dependencies": [2, 3, 4], "status": "done", "subtasks": [{"id": 1, "title": "Implement GET /api/providers/schedules endpoint", "description": "Create the API handler for retrieving provider schedules with filtering options", "dependencies": [], "details": "1. Complete the implementation of the getProviderSchedules function in @server/api/providers/schedules.ts\n2. Ensure proper validation of query parameters using the existing Zod schema\n3. Implement error handling for various scenarios (invalid parameters, database errors)\n4. Format the response according to API standards\n5. Verify the API route is correctly defined in main.wasp", "status": "done", "testStrategy": "Test with various query parameter combinations including date ranges, queue and location filters. Verify authentication middleware correctly identifies the provider."}, {"id": 2, "title": "Implement POST /api/providers/schedules endpoint", "description": "Create the API handler for creating new provider schedules", "dependencies": [1], "details": "1. Complete the implementation of the createSchedule function in @server/api/providers/schedules.ts\n2. Ensure proper validation of request body using the existing Zod schema\n3. Implement date conversion from string to Date objects\n4. Call the createScheduleOp operation with the validated data\n5. Format the response with the newly created schedule\n6. Handle potential errors like validation failures or database constraints", "status": "done", "testStrategy": "Test schedule creation with various valid and invalid payloads. Verify date handling works correctly and that the provider ID is properly associated with the new schedule."}, {"id": 3, "title": "Implement PATCH /api/providers/schedules/:id endpoint", "description": "Create the API handler for updating existing provider schedules", "dependencies": [2], "details": "1. Complete the implementation of the updateSchedule function in @server/api/providers/schedules.ts\n2. Validate both URL parameters and request body using the existing Zod schema\n3. Implement ownership verification to ensure providers can only update their own schedules\n4. Handle date conversions from string to Date objects\n5. Call the updateScheduleOp operation with the validated data\n6. Format the response with the updated schedule data", "status": "done", "testStrategy": "Test schedule updates with various partial update payloads. Verify ownership checks prevent unauthorized updates. Test edge cases like conflicting time ranges."}, {"id": 4, "title": "Implement DELETE /api/providers/schedules/:id endpoint", "description": "Create the API handler for deleting provider schedules", "dependencies": [3], "details": "1. Complete the implementation of the deleteSchedule function in @server/api/providers/schedules.ts\n2. Validate URL parameters using the existing Zod schema\n3. Implement ownership verification to ensure providers can only delete their own schedules\n4. Call the deleteScheduleOp operation with the validated schedule ID\n5. Format the success response according to API standards\n6. Handle potential errors like schedule not found or database constraints", "status": "done", "testStrategy": "Test schedule deletion with valid and invalid schedule IDs. Verify ownership checks prevent unauthorized deletions. Test error handling for non-existent schedules."}, {"id": 5, "title": "Finalize API integration and testing", "description": "Complete the API implementation by finalizing main.wasp definitions and conducting comprehensive testing", "dependencies": [4], "details": "1. Verify all API routes are correctly defined in main.wasp\n2. Ensure authentication middleware is properly applied to all routes\n3. Implement any missing imports or dependencies\n4. Create comprehensive documentation for the API endpoints\n5. Verify error handling is consistent across all endpoints\n6. Ensure all responses follow the established API response format\n7. Check that all operations properly validate provider ownership of schedules", "status": "done", "testStrategy": "Perform end-to-end testing of the complete API flow: creating schedules, retrieving them with filters, updating them, and deleting them. Test authentication failures, validation errors, and edge cases like conflicting schedules or invalid time formats."}]}, {"id": 23, "title": "Implement Customer Management APIs", "description": "Create API endpoints to manage provider's customers.", "details": "1. Create API handlers for customer management operations\n2. Implement endpoints for:\n   - GET /api/providers/customers\n   - GET /api/providers/customers/:id\n   - POST /api/providers/customers\n   - PATCH /api/providers/customers/:id\n3. Use authentication middleware to verify provider\n4. Implement Zod schemas for validation\n5. Call existing Wasp operations for customer management\n6. Format responses according to API standards\n7. Add to main.wasp API definitions\n\n```typescript\n// In main.wasp\napi {\n  httpRoute: GET /api/providers/customers,\n  fn: import { getProviderCustomers } from \"@server/api/providers/customers\",\n  auth: true\n}\n\napi {\n  httpRoute: GET /api/providers/customers/:id,\n  fn: import { getProviderCustomer } from \"@server/api/providers/customers\",\n  auth: true\n}\n\napi {\n  httpRoute: POST /api/providers/customers,\n  fn: import { createCustomer } from \"@server/api/providers/customers\",\n  auth: true\n}\n\napi {\n  httpRoute: PATCH /api/providers/customers/:id,\n  fn: import { updateCustomer } from \"@server/api/providers/customers\",\n  auth: true\n}\n\n// In @server/api/providers/customers.ts\nimport { z } from 'zod';\nimport { \n  getProviderCustomers as getCustomersOp,\n  getCustomerById,\n  createProviderCustomer,\n  updateProviderCustomer\n} from '../../../provider/operations';\nimport { validateRequest } from '../../common/validation';\nimport { handleApiError } from '../../common/errors';\n\n// Get customers\nconst getCustomersSchema = z.object({\n  query: z.object({\n    search: z.string().optional(),\n    page: z.string().transform(val => parseInt(val, 10)).optional(),\n    limit: z.string().transform(val => parseInt(val, 10)).optional(),\n    sortBy: z.enum(['name', 'email', 'createdAt']).optional(),\n    sortOrder: z.enum(['asc', 'desc']).optional()\n  })\n});\n\nexport const getProviderCustomers = [\n  validateRequest(getCustomersSchema),\n  async (req, res) => {\n    try {\n      const providerId = req.providerId;\n      const { search, page = 1, limit = 20, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;\n      \n      const filters = {\n        search: search || undefined,\n        pagination: { page, limit },\n        sort: { field: sortBy, order: sortOrder }\n      };\n      \n      const result = await getCustomersOp(providerId, filters);\n      \n      return res.status(200).json({\n        status: 'success',\n        data: result.customers,\n        pagination: {\n          total: result.total,\n          page,\n          limit,\n          pages: Math.ceil(result.total / limit)\n        }\n      });\n    } catch (error) {\n      return handleApiError(res, error);\n    }\n  }\n];\n\n// Get single customer\nconst getCustomerSchema = z.object({\n  params: z.object({\n    id: z.string().uuid()\n  })\n});\n\nexport const getProviderCustomer = [\n  validateRequest(getCustomerSchema),\n  async (req, res) => {\n    try {\n      const providerId = req.providerId;\n      const customerId = req.params.id;\n      \n      const customer = await getCustomerById(customerId);\n      \n      // Verify provider has relationship with this customer\n      if (!customer || customer.providerId !== providerId) {\n        return res.status(404).json({\n          status: 'error',\n          message: 'Customer not found'\n        });\n      }\n      \n      return res.status(200).json({\n        status: 'success',\n        data: customer\n      });\n    } catch (error) {\n      return handleApiError(res, error);\n    }\n  }\n];\n\n// Create customer\nconst createCustomerSchema = z.object({\n  body: z.object({\n    name: z.string().min(1),\n    email: z.string().email().optional(),\n    phone: z.string().optional(),\n    notes: z.string().optional(),\n    customFields: z.record(z.string()).optional()\n  })\n});\n\nexport const createCustomer = [\n  validateRequest(createCustomerSchema),\n  async (req, res) => {\n    try {\n      const providerId = req.providerId;\n      const customerData = req.body;\n      \n      const newCustomer = await createProviderCustomer({\n        providerId,\n        ...customerData\n      });\n      \n      return res.status(201).json({\n        status: 'success',\n        data: newCustomer\n      });\n    } catch (error) {\n      return handleApiError(res, error);\n    }\n  }\n];\n\n// Update customer\nconst updateCustomerSchema = z.object({\n  params: z.object({\n    id: z.string().uuid()\n  }),\n  body: z.object({\n    name: z.string().min(1).optional(),\n    email: z.string().email().optional(),\n    phone: z.string().optional(),\n    notes: z.string().optional(),\n    customFields: z.record(z.string()).optional()\n  })\n});\n\nexport const updateCustomer = [\n  validateRequest(updateCustomerSchema),\n  async (req, res) => {\n    try {\n      const providerId = req.providerId;\n      const customerId = req.params.id;\n      const updateData = req.body;\n      \n      // Verify provider has relationship with this customer\n      const existingCustomer = await getCustomerById(customerId);\n      if (!existingCustomer || existingCustomer.providerId !== providerId) {\n        return res.status(404).json({\n          status: 'error',\n          message: 'Customer not found'\n        });\n      }\n      \n      const updatedCustomer = await updateProviderCustomer({\n        id: customerId,\n        ...updateData\n      });\n      \n      return res.status(200).json({\n        status: 'success',\n        data: updatedCustomer\n      });\n    } catch (error) {\n      return handleApiError(res, error);\n    }\n  }\n];\n```", "testStrategy": "1. Test all operations with valid data\n2. Test with invalid data to verify validation\n3. Test with customer not associated with provider\n4. Test with non-existent customer IDs\n5. Test with authenticated non-provider user\n6. Verify customers are correctly created and updated in database\n7. Test search functionality\n8. Test pagination and sorting\n9. Test custom fields handling", "priority": "medium", "dependencies": [2, 3, 4], "status": "done", "subtasks": [{"id": 1, "title": "Implement API route definitions in main.wasp", "description": "Add the customer management API route definitions to the main.wasp file to register the endpoints with the Wasp framework.", "dependencies": [], "details": "Add the four API route definitions to main.wasp: GET /api/providers/customers, GET /api/providers/customers/:id, POST /api/providers/customers, and PATCH /api/providers/customers/:id. Each definition should specify the correct HTTP method, route path, handler function import path, and authentication requirement (auth: true).", "status": "done", "testStrategy": "Verify the Wasp compiler accepts the changes without errors by running 'wasp start'. Check that the routes are properly registered in the generated code."}, {"id": 2, "title": "Create validation schemas and common utilities", "description": "Implement the Zod validation schemas for request validation and set up common error handling utilities.", "dependencies": [1], "details": "Create the validation schemas for each API endpoint using Zod. Implement the getCustomersSchema, getCustomerSchema, createCustomerSchema, and updateCustomerSchema. Ensure proper validation for query parameters, URL parameters, and request bodies. Set up or reuse the validateRequest middleware and handleApiError utility functions.", "status": "done", "testStrategy": "Write unit tests for each schema to verify they correctly validate valid inputs and reject invalid inputs with appropriate error messages."}, {"id": 3, "title": "Implement GET endpoints for customer listing and retrieval", "description": "Create the handler functions for retrieving all customers and getting a single customer by ID.", "dependencies": [2], "details": "Implement the getProviderCustomers and getProviderCustomer handler functions in @server/api/providers/customers.ts. Both should use the validation middleware, extract the providerId from the authenticated request, call the appropriate operations (getCustomersOp or getCustomerById), and format the responses according to API standards. Include pagination, sorting, and filtering for the list endpoint, and proper 404 handling for the single customer endpoint.", "status": "done", "testStrategy": "Create integration tests that verify the endpoints return the correct data structure, handle pagination correctly, and properly validate provider ownership of customers."}, {"id": 4, "title": "Implement POST endpoint for customer creation", "description": "Create the handler function for creating new customers.", "dependencies": [2], "details": "Implement the createCustomer handler function in @server/api/providers/customers.ts. Use the validation middleware to validate the request body, extract the providerId from the authenticated request, call the createProviderCustomer operation with the combined data, and return a 201 response with the newly created customer data.", "status": "done", "testStrategy": "Write integration tests that verify the endpoint correctly creates customers with valid data, associates them with the correct provider, and returns appropriate validation errors for invalid inputs."}, {"id": 5, "title": "Implement PATCH endpoint for customer updates", "description": "Create the handler function for updating existing customers.", "dependencies": [3, 4], "details": "Implement the updateCustomer handler function in @server/api/providers/customers.ts. Use the validation middleware to validate the request parameters and body, verify the customer exists and belongs to the authenticated provider, call the updateProviderCustomer operation with the update data, and return the updated customer. Include proper error handling for cases where the customer doesn't exist or doesn't belong to the provider.", "status": "done", "testStrategy": "Create integration tests that verify the endpoint correctly updates customers, validates provider ownership, handles non-existent customers appropriately, and returns validation errors for invalid inputs."}]}, {"id": 24, "title": "Implement Appointment Management APIs", "description": "Create API endpoints to manage provider appointments.", "details": "1. Create API handlers for appointment management operations\n2. Implement endpoints for:\n   - GET /api/providers/appointments\n   - GET /api/providers/appointments/:id\n   - POST /api/providers/appointments\n   - PATCH /api/providers/appointments/:id\n   - PATCH /api/providers/appointments/:id/status\n3. Use authentication middleware to verify provider\n4. Implement Zod schemas for validation\n5. Call existing Wasp operations for appointment management\n6. Format responses according to API standards\n7. Add to main.wasp API definitions\n\n```typescript\n// In main.wasp\napi {\n  httpRoute: GET /api/providers/appointments,\n  fn: import { getProviderAppointments } from \"@server/api/providers/appointments\",\n  auth: true\n}\n\napi {\n  httpRoute: GET /api/providers/appointments/:id,\n  fn: import { getProviderAppointment } from \"@server/api/providers/appointments\",\n  auth: true\n}\n\napi {\n  httpRoute: POST /api/providers/appointments,\n  fn: import { createAppointment } from \"@server/api/providers/appointments\",\n  auth: true\n}\n\napi {\n  httpRoute: PATCH /api/providers/appointments/:id,\n  fn: import { updateAppointment } from \"@server/api/providers/appointments\",\n  auth: true\n}\n\napi {\n  httpRoute: PATCH /api/providers/appointments/:id/status,\n  fn: import { updateAppointmentStatus } from \"@server/api/providers/appointments\",\n  auth: true\n}\n\n// In @server/api/providers/appointments.ts\nimport { z } from 'zod';\nimport { \n  getProviderAppointments as getAppointmentsOp,\n  getAppointmentById,\n  createProviderAppointment,\n  updateProviderAppointment,\n  updateAppointmentStatus as updateStatusOp\n} from '../../../provider/operations';\nimport { validateRequest } from '../../common/validation';\nimport { handleApiError } from '../../common/errors';\n\n// Get appointments\nconst getAppointmentsSchema = z.object({\n  query: z.object({\n    from: z.string().optional(), // ISO date string\n    to: z.string().optional(), // ISO date string\n    status: z.string().optional(),\n    queueId: z.string().uuid().optional(),\n    locationId: z.string().uuid().optional(),\n    serviceId: z.string().uuid().optional(),\n    customerId: z.string().uuid().optional(),\n    page: z.string().transform(val => parseInt(val, 10)).optional(),\n    limit: z.string().transform(val => parseInt(val, 10)).optional()\n  })\n});\n\nexport const getProviderAppointments = [\n  validateRequest(getAppointmentsSchema),\n  async (req, res) => {\n    try {\n      const providerId = req.providerId;\n      const { \n        from, to, status, queueId, locationId, serviceId, customerId,\n        page = 1, limit = 20 \n      } = req.query;\n      \n      const filters = {\n        from: from ? new Date(from) : undefined,\n        to: to ? new Date(to) : undefined,\n        status: status || undefined,\n        queueId: queueId || undefined,\n        locationId: locationId || undefined,\n        serviceId: serviceId || undefined,\n        customerId: customerId || undefined,\n        pagination: { page, limit }\n      };\n      \n      const result = await getAppointmentsOp(providerId, filters);\n      \n      return res.status(200).json({\n        status: 'success',\n        data: result.appointments,\n        pagination: {\n          total: result.total,\n          page,\n          limit,\n          pages: Math.ceil(result.total / limit)\n        }\n      });\n    } catch (error) {\n      return handleApiError(res, error);\n    }\n  }\n];\n\n// Get single appointment\nconst getAppointmentSchema = z.object({\n  params: z.object({\n    id: z.string().uuid()\n  })\n});\n\nexport const getProviderAppointment = [\n  validateRequest(getAppointmentSchema),\n  async (req, res) => {\n    try {\n      const providerId = req.providerId;\n      const appointmentId = req.params.id;\n      \n      const appointment = await getAppointmentById(appointmentId);\n      \n      // Verify provider owns this appointment\n      if (!appointment || appointment.providerId !== providerId) {\n        return res.status(404).json({\n          status: 'error',\n          message: 'Appointment not found'\n        });\n      }\n      \n      return res.status(200).json({\n        status: 'success',\n        data: appointment\n      });\n    } catch (error) {\n      return handleApiError(res, error);\n    }\n  }\n];\n\n// Create appointment\nconst createAppointmentSchema = z.object({\n  body: z.object({\n    customerId: z.string().uuid(),\n    serviceId: z.string().uuid(),\n    queueId: z.string().uuid(),\n    startTime: z.string(), // ISO date string\n    endTime: z.string().optional(), // ISO date string\n    notes: z.string().optional(),\n    status: z.string().optional().default('confirmed')\n  })\n});\n\nexport const createAppointment = [\n  validateRequest(createAppointmentSchema),\n  async (req, res) => {\n    try {\n      const providerId = req.providerId;\n      const appointmentData = req.body;\n      \n      // Convert string dates to Date objects\n      appointmentData.startTime = new Date(appointmentData.startTime);\n      if (appointmentData.endTime) {\n        appointmentData.endTime = new Date(appointmentData.endTime);\n      }\n      \n      const newAppointment = await createProviderAppointment({\n        providerId,\n        ...appointmentData\n      });\n      \n      return res.status(201).json({\n        status: 'success',\n        data: newAppointment\n      });\n    } catch (error) {\n      return handleApiError(res, error);\n    }\n  }\n];\n\n// Update appointment\nconst updateAppointmentSchema = z.object({\n  params: z.object({\n    id: z.string().uuid()\n  }),\n  body: z.object({\n    serviceId: z.string().uuid().optional(),\n    queueId: z.string().uuid().optional(),\n    startTime: z.string().optional(), // ISO date string\n    endTime: z.string().optional(), // ISO date string\n    notes: z.string().optional()\n  })\n});\n\nexport const updateAppointment = [\n  validateRequest(updateAppointmentSchema),\n  async (req, res) => {\n    try {\n      const providerId = req.providerId;\n      const appointmentId = req.params.id;\n      const updateData = req.body;\n      \n      // Verify provider owns this appointment\n      const existingAppointment = await getAppointmentById(appointmentId);\n      if (!existingAppointment || existingAppointment.providerId !== providerId) {\n        return res.status(404).json({\n          status: 'error',\n          message: 'Appointment not found'\n        });\n      }\n      \n      // Convert string dates to Date objects if provided\n      if (updateData.startTime) {\n        updateData.startTime = new Date(updateData.startTime);\n      }\n      if (updateData.endTime) {\n        updateData.endTime = new Date(updateData.endTime);\n      }\n      \n      const updatedAppointment = await updateProviderAppointment({\n        id: appointmentId,\n        ...updateData\n      });\n      \n      return res.status(200).json({\n        status: 'success',\n        data: updatedAppointment\n      });\n    } catch (error) {\n      return handleApiError(res, error);\n    }\n  }\n];\n\n// Update appointment status\nconst updateStatusSchema = z.object({\n  params: z.object({\n    id: z.string().uuid()\n  }),\n  body: z.object({\n    status: z.enum(['confirmed', 'completed', 'cancelled', 'no-show']),\n    notes: z.string().optional()\n  })\n});\n\nexport const updateAppointmentStatus = [\n  validateRequest(updateStatusSchema),\n  async (req, res) => {\n    try {\n      const providerId = req.providerId;\n      const appointmentId = req.params.id;\n      const { status, notes } = req.body;\n      \n      // Verify provider owns this appointment\n      const existingAppointment = await getAppointmentById(appointmentId);\n      if (!existingAppointment || existingAppointment.providerId !== providerId) {\n        return res.status(404).json({\n          status: 'error',\n          message: 'Appointment not found'\n        });\n      }\n      \n      const updatedAppointment = await updateStatusOp({\n        id: appointmentId,\n        status,\n        notes\n      });\n      \n      return res.status(200).json({\n        status: 'success',\n        data: updatedAppointment\n      });\n    } catch (error) {\n      return handleApiError(res, error);\n    }\n  }\n];\n```", "testStrategy": "1. Test all operations with valid data\n2. Test with invalid data to verify validation\n3. Test with appointment not owned by provider\n4. Test with non-existent appointment IDs\n5. Test with authenticated non-provider user\n6. Verify appointments are correctly created, updated, and status changes in database\n7. Test date handling and time zone considerations\n8. Test filtering appointments by various criteria\n9. Test pagination\n10. Test status transitions (e.g., can't change from completed to confirmed)", "priority": "high", "dependencies": [2, 3, 4, 12, 17], "status": "done", "subtasks": [{"id": 1, "title": "Define API routes in main.wasp", "description": "Add the appointment management API endpoint definitions to main.wasp file to register the routes with the Wasp framework.", "dependencies": [], "details": "Add the five API endpoint definitions to main.wasp: GET /api/providers/appointments, GET /api/providers/appointments/:id, POST /api/providers/appointments, PATCH /api/providers/appointments/:id, and PATCH /api/providers/appointments/:id/status. Each definition should specify the HTTP method, route path, handler function import path, and auth requirement (set to true for all endpoints).", "status": "done", "testStrategy": "Verify the syntax is correct by running 'wasp start' and checking for any compilation errors."}, {"id": 2, "title": "Implement GET appointment endpoints", "description": "Create the handler functions for retrieving appointments, including the list endpoint and single appointment endpoint.", "dependencies": [1], "details": "Create the @server/api/providers/appointments.ts file and implement the getProviderAppointments and getProviderAppointment handler functions. Include Zod schemas for request validation, proper error handling with handleApiError, and calls to the existing operations. For getProviderAppointments, implement filtering by date range, status, and other parameters, with pagination support. For getProviderAppointment, verify the provider has access to the requested appointment.", "status": "done", "testStrategy": "Test with valid and invalid request parameters, including pagination, filters, and non-existent appointment IDs. Verify proper error responses for unauthorized access attempts."}, {"id": 3, "title": "Implement POST appointment creation endpoint", "description": "Create the handler function for creating new appointments.", "dependencies": [2], "details": "In the same file, implement the createAppointment handler function with Zod validation for the request body. The schema should validate customerId, serviceId, queueId, startTime, and optional fields like endTime, notes, and status. Convert string date representations to Date objects before calling the createProviderAppointment operation. Return a 201 status code with the newly created appointment data on success.", "status": "done", "testStrategy": "Test appointment creation with valid and invalid data, verifying proper validation errors for missing required fields and proper date handling."}, {"id": 4, "title": "Implement PATCH appointment update endpoint", "description": "Create the handler function for updating existing appointment details.", "dependencies": [3], "details": "Implement the updateAppointment handler function with Zod validation for both path parameters and request body. Verify the provider owns the appointment before allowing updates. The update schema should make all fields optional (serviceId, queueId, startTime, endTime, notes). Convert any provided date strings to Date objects before calling the updateProviderAppointment operation.", "status": "done", "testStrategy": "Test partial updates with different field combinations, verify ownership validation prevents unauthorized updates, and check date handling for various formats."}, {"id": 5, "title": "Implement PATCH appointment status endpoint", "description": "Create the handler function for updating just the status of an appointment.", "dependencies": [4], "details": "Implement the updateAppointmentStatus handler function with Zod validation. The status schema should restrict values to the enum: 'confirmed', 'completed', 'cancelled', 'no-show'. Verify provider ownership before allowing status updates. Include optional notes field that can be updated along with the status. Call the updateStatusOp operation with the validated data.", "status": "done", "testStrategy": "Test status updates for each allowed status value, verify validation rejects invalid status values, and check that ownership validation prevents unauthorized status changes."}]}, {"id": 25, "title": "Implement Reschedule Management APIs", "description": "Create API endpoints to manage appointment reschedule requests.", "details": "1. Create API handlers for reschedule management operations\n2. Implement endpoints for:\n   - GET /api/providers/reschedules\n   - GET /api/providers/reschedules/:id\n   - POST /api/providers/appointments/:id/reschedule\n   - PATCH /api/providers/reschedules/:id/respond\n3. Use authentication middleware to verify provider\n4. Implement Zod schemas for validation\n5. Call existing Wasp operations for reschedule management\n6. Format responses according to API standards\n7. Add to main.wasp API definitions\n\n```typescript\n// In main.wasp\napi {\n  httpRoute: GET /api/providers/reschedules,\n  fn: import { getProviderReschedules } from \"@server/api/providers/reschedules\",\n  auth: true\n}\n\napi {\n  httpRoute: GET /api/providers/reschedules/:id,\n  fn: import { getProviderReschedule } from \"@server/api/providers/reschedules\",\n  auth: true\n}\n\napi {\n  httpRoute: POST /api/providers/appointments/:id/reschedule,\n  fn: import { createRescheduleRequest } from \"@server/api/providers/reschedules\",\n  auth: true\n}\n\napi {\n  httpRoute: PATCH /api/providers/reschedules/:id/respond,\n  fn: import { respondToReschedule } from \"@server/api/providers/reschedules\",\n  auth: true\n}\n\n// In @server/api/providers/reschedules.ts\nimport { z } from 'zod';\nimport { \n  getProviderRescheduleRequests,\n  getRescheduleRequestById,\n  createProviderRescheduleRequest,\n  respondToRescheduleRequest,\n  getAppointmentById\n} from '../../../provider/operations';\nimport { validateRequest } from '../../common/validation';\nimport { handleApiError } from '../../common/errors';\n\n// Get reschedule requests\nconst getReschedulesSchema = z.object({\n  query: z.object({\n    status: z.enum(['pending', 'approved', 'rejected', 'all']).optional().default('pending'),\n    from: z.string().optional(), // ISO date string\n    to: z.string().optional(), // ISO date string\n    page: z.string().transform(val => parseInt(val, 10)).optional(),\n    limit: z.string().transform(val => parseInt(val, 10)).optional()\n  })\n});\n\nexport const getProviderReschedules = [\n  validateRequest(getReschedulesSchema),\n  async (req, res) => {\n    try {\n      const providerId = req.providerId;\n      const { status, from, to, page = 1, limit = 20 } = req.query;\n      \n      const filters = {\n        status: status === 'all' ? undefined : status,\n        from: from ? new Date(from) : undefined,\n        to: to ? new Date(to) : undefined,\n        pagination: { page, limit }\n      };\n      \n      const result = await getProviderRescheduleRequests(providerId, filters);\n      \n      return res.status(200).json({\n        status: 'success',\n        data: result.reschedules,\n        pagination: {\n          total: result.total,\n          page,\n          limit,\n          pages: Math.ceil(result.total / limit)\n        }\n      });\n    } catch (error) {\n      return handleApiError(res, error);\n    }\n  }\n];\n\n// Get single reschedule request\nconst getRescheduleSchema = z.object({\n  params: z.object({\n    id: z.string().uuid()\n  })\n});\n\nexport const getProviderReschedule = [\n  validateRequest(getRescheduleSchema),\n  async (req, res) => {\n    try {\n      const providerId = req.providerId;\n      const rescheduleId = req.params.id;\n      \n      const reschedule = await getRescheduleRequestById(rescheduleId);\n      \n      // Verify provider owns this reschedule request\n      if (!reschedule || reschedule.providerId !== providerId) {\n        return res.status(404).json({\n          status: 'error',\n          message: 'Reschedule request not found'\n        });\n      }\n      \n      return res.status(200).json({\n        status: 'success',\n        data: reschedule\n      });\n    } catch (error) {\n      return handleApiError(res, error);\n    }\n  }\n];\n\n// Create reschedule request\nconst createRescheduleSchema = z.object({\n  params: z.object({\n    id: z.string().uuid() // appointment ID\n  }),\n  body: z.object({\n    newStartTime: z.string(), // ISO date string\n    reason: z.string().optional(),\n    notifyCustomer: z.boolean().optional().default(true)\n  })\n});\n\nexport const createRescheduleRequest = [\n  validateRequest(createRescheduleSchema),\n  async (req, res) => {\n    try {\n      const providerId = req.providerId;\n      const appointmentId = req.params.id;\n      const { newStartTime, reason, notifyCustomer } = req.body;\n      \n      // Verify provider owns this appointment\n      const appointment = await getAppointmentById(appointmentId);\n      if (!appointment || appointment.providerId !== providerId) {\n        return res.status(404).json({\n          status: 'error',\n          message: 'Appointment not found'\n        });\n      }\n      \n      const rescheduleRequest = await createProviderRescheduleRequest({\n        appointmentId,\n        providerId,\n        newStartTime: new Date(newStartTime),\n        reason,\n        notifyCustomer\n      });\n      \n      return res.status(201).json({\n        status: 'success',\n        data: rescheduleRequest\n      });\n    } catch (error) {\n      return handleApiError(res, error);\n    }\n  }\n];\n\n// Respond to reschedule request\nconst respondRescheduleSchema = z.object({\n  params: z.object({\n    id: z.string().uuid() // reschedule ID\n  }),\n  body: z.object({\n    response: z.enum(['approve', 'reject']),\n    notes: z.string().optional(),\n    notifyCustomer: z.boolean().optional().default(true)\n  })\n});\n\nexport const respondToReschedule = [\n  validateRequest(respondRescheduleSchema),\n  async (req, res) => {\n    try {\n      const providerId = req.providerId;\n      const rescheduleId = req.params.id;\n      const { response, notes, notifyCustomer } = req.body;\n      \n      // Verify provider owns this reschedule request\n      const reschedule = await getRescheduleRequestById(rescheduleId);\n      if (!reschedule || reschedule.providerId !== providerId) {\n        return res.status(404).json({\n          status: 'error',\n          message: 'Reschedule request not found'\n        });\n      }\n      \n      // Verify reschedule is still pending\n      if (reschedule.status !== 'pending') {\n        return res.status(400).json({\n          status: 'error',\n          message: 'Cannot respond to a reschedule request that is not pending'\n        });\n      }\n      \n      const updatedReschedule = await respondToRescheduleRequest({\n        id: rescheduleId,\n        response: response === 'approve' ? 'approved' : 'rejected',\n        notes,\n        notifyCustomer\n      });\n      \n      return res.status(200).json({\n        status: 'success',\n        data: updatedReschedule\n      });\n    } catch (error) {\n      return handleApiError(res, error);\n    }\n  }\n];\n```", "testStrategy": "1. Test all operations with valid data\n2. Test with invalid data to verify validation\n3. Test with reschedule requests not owned by provider\n4. Test with non-existent reschedule or appointment IDs\n5. Test with authenticated non-provider user\n6. Verify reschedule requests are correctly created and responded to in database\n7. Test date handling and time zone considerations\n8. Test filtering reschedule requests by status and date range\n9. Test pagination\n10. Test notification flag behavior\n11. Test responding to already responded reschedule requests", "priority": "medium", "dependencies": [2, 3, 4, 24], "status": "done", "subtasks": [{"id": 1, "title": "Implement API Definitions in main.wasp", "description": "Add the API route definitions to main.wasp for all four reschedule management endpoints.", "dependencies": [], "details": "Add the following API definitions to main.wasp:\n1. GET /api/providers/reschedules\n2. GET /api/providers/reschedules/:id\n3. POST /api/providers/appointments/:id/reschedule\n4. PATCH /api/providers/reschedules/:id/respond\n\nEnsure each definition imports the correct handler function from @server/api/providers/reschedules.ts and has auth: true set to require authentication.", "status": "done", "testStrategy": "Verify the syntax using Wasp CLI validation. Run 'wasp start' to ensure the routes are properly registered."}, {"id": 2, "title": "Create <PERSON>od Validation <PERSON>", "description": "Implement the Zod validation schemas for all reschedule management API endpoints.", "dependencies": [1], "details": "Create the following validation schemas in @server/api/providers/reschedules.ts:\n1. getReschedulesSchema - for filtering and pagination\n2. getRescheduleSchema - for validating the reschedule ID parameter\n3. createRescheduleSchema - for validating appointment ID and new schedule details\n4. respondRescheduleSchema - for validating reschedule response\n\nEnsure proper type transformations for numeric values and validation for date strings, UUIDs, and enum values.", "status": "done", "testStrategy": "Write unit tests for each schema to verify they correctly validate and transform input data, including edge cases like invalid dates or missing required fields."}, {"id": 3, "title": "Implement GET Reschedule Endpoints", "description": "Implement the handler functions for retrieving reschedule requests.", "dependencies": [2], "details": "In @server/api/providers/reschedules.ts, implement:\n1. getProviderReschedules - List reschedule requests with filtering and pagination\n2. getProviderReschedule - Get a single reschedule request by ID\n\nBoth functions should:\n- Extract providerId from the authenticated request\n- Use the validation middleware with the appropriate schema\n- Call the corresponding operations (getProviderRescheduleRequests, getRescheduleRequestById)\n- Verify the provider has access to the requested data\n- Format responses according to API standards with status and data fields\n- Use handleApiError for error handling", "status": "done", "testStrategy": "Test with various query parameters for filtering and pagination. Verify provider access control by testing with reschedule requests owned by different providers."}, {"id": 4, "title": "Implement Create Reschedule Request Endpoint", "description": "Implement the handler function for creating a new reschedule request.", "dependencies": [2], "details": "In @server/api/providers/reschedules.ts, implement createRescheduleRequest handler:\n1. Extract providerId from the authenticated request\n2. Validate the appointment ID parameter and request body\n3. Verify the provider owns the appointment by calling getAppointmentById\n4. Call createProviderRescheduleRequest operation with validated data\n5. Return a 201 status with the created reschedule request\n6. Handle errors with handleApiError\n\nEnsure proper date handling by converting string dates to Date objects.", "status": "done", "testStrategy": "Test creating reschedule requests with valid and invalid appointment IDs. Verify the endpoint rejects requests for appointments not owned by the provider. Test with various combinations of optional parameters."}, {"id": 5, "title": "Implement Respond to Reschedule Endpoint", "description": "Implement the handler function for responding to reschedule requests.", "dependencies": [2], "details": "In @server/api/providers/reschedules.ts, implement respondToReschedule handler:\n1. Extract providerId from the authenticated request\n2. Validate the reschedule ID parameter and response body\n3. Verify the provider owns the reschedule request\n4. Check that the reschedule request is still in 'pending' status\n5. Call respondToRescheduleRequest operation with the validated data\n6. Return a 200 status with the updated reschedule request\n7. Handle errors with handleApiError\n\nEnsure proper mapping of the 'approve'/'reject' input to 'approved'/'rejected' status.", "status": "done", "testStrategy": "Test approving and rejecting reschedule requests. Verify the endpoint rejects responses to requests not owned by the provider or not in pending status. Test with and without optional parameters like notes and notifyCustomer."}]}, {"id": 26, "title": "Refactor Provider Mobile API Integration Tests for Authentication Flow", "description": "Refactor the existing Provider Mobile API integration tests to properly handle authentication flow, user registration, login, and JWT token management to align with the implemented API endpoints.", "details": "This task involves a comprehensive refactoring of the Provider Mobile API integration tests to ensure they properly test the authentication flow and align with existing API endpoints:\n\n1. Analyze the current authentication flow in the implemented APIs:\n   - Review the provider registration process\n   - Examine the login flow and JWT token generation\n   - Understand how token refresh works\n   - Map out the complete provider setup workflow\n\n2. Refactor the test suite structure:\n   - Create a proper test setup module that handles authentication\n   - Implement helper functions for registration, login, and token management\n   - Organize tests in a logical sequence that follows the user journey\n\n3. Implement proper test fixtures:\n   - Create reusable fixtures for authenticated provider sessions\n   - Implement test data generators for provider profiles, locations, and services\n   - Set up cleanup routines to ensure test isolation\n\n4. Update test cases for authentication-related endpoints:\n   - Provider registration\n   - Provider login\n   - Password reset flow\n   - Token refresh\n   - Complete setup workflow (Task 7)\n\n5. Ensure tests for authenticated endpoints properly handle JWT:\n   - Get Provider Profile (Task 5)\n   - Update Provider Profile (Task 6)\n   - Get Provider Locations (Task 9)\n   - Get Provider Services (Task 13)\n   - Customer Management APIs (Task 23)\n   - Appointment Management APIs (Task 24)\n   - Reschedule Management APIs (Task 25)\n\n6. Implement proper error case testing:\n   - Test invalid credentials scenarios\n   - Test expired token handling\n   - Test unauthorized access attempts\n   - Test malformed request handling\n\n7. Update documentation:\n   - Add comments explaining the test strategy\n   - Document the authentication helper functions\n   - Update the README with instructions for running the tests\n\nSample code for authentication helper:\n```typescript\n// test/helpers/auth.ts\nexport async function registerAndLoginProvider() {\n  // Generate unique test provider\n  const testProvider = generateTestProvider();\n  \n  // Register the provider\n  const registerResponse = await request(app)\n    .post('/api/auth/provider/register')\n    .send(testProvider);\n  \n  // Login with the provider\n  const loginResponse = await request(app)\n    .post('/api/auth/provider/login')\n    .send({\n      email: testProvider.email,\n      password: testProvider.password\n    });\n  \n  // Extract and return the JWT token\n  return {\n    provider: testProvider,\n    token: loginResponse.body.token\n  };\n}\n\n// Example test using the helper\nexport async function setupAuthenticatedTest() {\n  const { provider, token } = await registerAndLoginProvider();\n  return {\n    provider,\n    authHeader: { Authorization: `Bearer ${token}` }\n  };\n}\n```", "testStrategy": "To verify the successful refactoring of the Provider Mobile API integration tests:\n\n1. Run the complete test suite to ensure all tests pass:\n   - Execute `npm run test:integration` and verify no tests fail\n   - Check that tests properly authenticate before making API calls\n   - Confirm tests handle token expiration and refresh correctly\n\n2. Verify test coverage for authentication flows:\n   - Run test coverage report and ensure authentication endpoints have >90% coverage\n   - Confirm both success and failure paths are tested\n   - Verify edge cases like token expiration are covered\n\n3. Test the registration flow:\n   - Verify tests for new user registration pass\n   - Confirm validation error tests pass\n   - Check duplicate email handling tests pass\n\n4. Test the login flow:\n   - Verify successful login tests pass and return valid JWT\n   - Confirm invalid credential tests properly handle errors\n   - Check that token refresh tests work correctly\n\n5. Test authenticated API access:\n   - Verify tests for all provider endpoints use proper authentication\n   - Confirm unauthorized access tests fail appropriately\n   - Check that expired token tests handle refresh correctly\n\n6. Perform manual verification:\n   - Review test logs to ensure proper authentication flow\n   - Check database state after tests to verify proper cleanup\n   - Confirm test isolation (tests don't interfere with each other)\n\n7. Validate against API documentation:\n   - Compare test coverage against API documentation\n   - Ensure all documented endpoints have corresponding tests\n   - Verify all authentication requirements are tested\n\n8. Code review:\n   - Have another developer review the test refactoring\n   - Ensure best practices for testing are followed\n   - Confirm helper functions are well-documented", "status": "pending", "dependencies": [2, 5, 6, 7, 9, 13, 23, 24, 25], "priority": "medium", "subtasks": [{"id": 1, "title": "Analyze Current Authentication API Implementation", "description": "Review and document the existing authentication flow in the implemented APIs to understand the current implementation before refactoring tests.", "dependencies": [], "details": "Examine the codebase to identify all authentication-related endpoints and their functionality. Document the provider registration process, login flow, JWT token generation and validation, token refresh mechanism, and the complete provider setup workflow. Create a flow diagram showing the relationships between these endpoints and the expected request/response patterns.", "status": "done", "testStrategy": "Create a document outlining the authentication flow with endpoint URLs, request/response formats, and authentication requirements for each endpoint."}, {"id": 2, "title": "Create Authentication Help<PERSON>", "description": "Implement a reusable authentication helper module that handles provider registration, login, and JWT token management for tests.", "dependencies": [1], "details": "Create a new file `test/helpers/auth.ts` with functions for: 1) Registering a new test provider, 2) Logging in with existing credentials, 3) Refreshing expired tokens, 4) Setting up an authenticated test environment, and 5) Cleaning up test data. Implement the `registerAndLoginProvider()` and `setupAuthenticatedTest()` functions as shown in the sample code.", "status": "done", "testStrategy": "Write unit tests for the helper functions to ensure they correctly interact with the authentication endpoints and return the expected tokens and headers."}, {"id": 3, "title": "Implement Test Data Generators", "description": "Create utility functions to generate test data for provider profiles, locations, and services.", "dependencies": [1], "details": "Create a new file `test/helpers/testData.ts` with functions that generate random but valid test data for: 1) Provider registration information, 2) Provider profile details, 3) Provider locations, and 4) Provider services. Ensure generated data meets all validation requirements of the API. Include options to override specific fields for test customization.", "status": "done", "testStrategy": "Verify generated test data passes API validation by using it in simple API calls."}, {"id": 4, "title": "Refactor Registration and Login Test Cases", "description": "Update test cases for provider registration, login, password reset, and token refresh endpoints.", "dependencies": [2, 3], "details": "Refactor tests for `/api/auth/provider/register`, `/api/auth/provider/login`, password reset endpoints, and token refresh endpoints. Use the new helper functions and test data generators. Include positive test cases (successful registration/login) and negative test cases (duplicate email, invalid credentials, etc.). Ensure tests verify the correct response structure and status codes.", "status": "done", "testStrategy": "Run tests in isolation and as part of the full suite to ensure they work independently and in sequence."}, {"id": 5, "title": "Refactor Provider Profile Test Cases", "description": "Update test cases for authenticated provider profile endpoints using the new authentication helpers.", "dependencies": [2, 3, 4], "details": "Refactor tests for Get Provider Profile and Update Provider Profile endpoints. Use the authentication helpers to set up an authenticated test environment before making requests. Verify that profile data can be retrieved and updated correctly when authenticated, and that appropriate errors are returned for unauthenticated requests.", "status": "done", "testStrategy": "Test both successful profile operations and unauthorized access attempts. Verify the JWT token is correctly used in requests."}, {"id": 6, "title": "Refactor Provider Locations and Services Test Cases", "description": "Update test cases for provider locations and services endpoints to use the new authentication flow.", "dependencies": [2, 3, 5], "details": "Refactor tests for Get Provider Locations and Get Provider Services endpoints. Ensure tests first authenticate as a provider before attempting to access these endpoints. Verify that location and service data is correctly associated with the authenticated provider and that appropriate errors are returned for unauthenticated requests.", "status": "pending", "testStrategy": "Include tests for various scenarios: providers with multiple locations/services, providers with no locations/services, and unauthorized access attempts."}, {"id": 7, "title": "Implement Comprehensive Error Case Testing", "description": "Enhance test coverage by adding specific test cases for error scenarios in the authentication flow.", "dependencies": [4, 5, 6], "details": "Add test cases for: 1) Invalid credentials during login, 2) Expired JWT token handling, 3) Malformed JWT tokens, 4) Missing authentication headers, 5) Insufficient permissions, and 6) Rate limiting if implemented. Verify that appropriate error responses (status codes and messages) are returned in each case.", "status": "pending", "testStrategy": "Create a separate test file specifically for error cases to ensure comprehensive coverage of error handling in the authentication flow."}, {"id": 8, "title": "Update Test Documentation and README", "description": "Document the refactored test suite structure, authentication helpers, and update the README with instructions for running tests.", "dependencies": [2, 3, 4, 5, 6, 7], "details": "Add JSDoc comments to all helper functions explaining their purpose, parameters, and return values. Create a dedicated TESTING.md document explaining the test strategy, authentication flow, and how to run different test suites. Update the project README with a section on testing that references the TESTING.md file. Include examples of how to run specific test suites and how to troubleshoot common issues.", "status": "pending", "testStrategy": "Have another team member review the documentation for clarity and completeness."}]}, {"id": 27, "title": "Fix Failed Integration Tests for Provider Mobile API", "description": "Resolve all failed integration tests for the Provider Mobile API by addressing data format issues, timeout handling, error handling, test data structures, status codes, and test dependencies.", "details": "This task involves fixing multiple issues identified in the integration test suite:\n\n1. Location Data Format Issues:\n   - Update test fixtures and assertions to handle address field as a string instead of an object\n   - Modify location-related API tests in `/tests/integration/provider/locations.test.ts`\n   - Ensure all location creation/update tests use the correct data format\n\n2. Phone OTP Timeout Handling:\n   - Implement proper timeout handling in authentication tests\n   - Add retry logic for OTP verification tests\n   - Update test timeouts to accommodate network latency\n   - Modify `/tests/integration/provider/auth.test.ts` to handle timeout scenarios\n\n3. Profile Management Error Handling:\n   - Enhance error assertions in profile management tests\n   - Add test cases for edge cases (invalid data, unauthorized access)\n   - Update expected error messages and status codes\n   - Fix tests in `/tests/integration/provider/profile.test.ts`\n\n4. Onboarding Setup Test Data Structure:\n   - Restructure test fixtures for onboarding flow\n   - Update test data to match current API expectations\n   - Fix data dependencies between onboarding steps\n   - Update tests in `/tests/integration/provider/onboarding.test.ts`\n\n5. Validation Error Status Code Corrections:\n   - Update expected status codes for validation errors (should be 400 not 422)\n   - Ensure consistent error response format across all tests\n   - Fix assertions in all API tests that check validation errors\n\n6. Queue Management Test Dependencies:\n   - Refactor queue tests to properly clean up test data\n   - Fix order dependencies between queue-related tests\n   - Ensure proper isolation between test cases\n   - Update tests in `/tests/integration/provider/queues.test.ts`\n\nImplementation approach:\n- Create a separate fix for each category of issues\n- Update test fixtures to use correct data formats\n- Refactor test setup/teardown to ensure proper isolation\n- Add more robust error handling and assertions\n- Document any API behavior changes discovered during fixes", "testStrategy": "1. Run the full integration test suite to identify all failing tests:\n   ```bash\n   npm run test:integration\n   ```\n\n2. For each category of issues:\n   - Run the specific test file in isolation to verify fixes\n   - Example: `npm run test:integration -- --testPathPattern=provider/locations.test.ts`\n   - Check that tests pass with the expected assertions\n\n3. Verify location data format fixes:\n   - Confirm location creation tests pass with string address format\n   - Verify location update operations maintain correct format\n\n4. Test OTP timeout handling:\n   - Run authentication tests multiple times to ensure reliability\n   - Verify tests pass consistently even with network delays\n\n5. Validate profile management error handling:\n   - Trigger each error condition manually to verify correct handling\n   - Check that appropriate error messages are displayed\n\n6. Verify onboarding test data structure:\n   - Run the complete onboarding flow test to ensure all steps work\n   - Confirm test data is properly structured for each step\n\n7. Check validation error status codes:\n   - Verify all validation errors return 400 status codes\n   - Confirm error response format is consistent\n\n8. Test queue management dependencies:\n   - Run queue tests in isolation and as part of the full suite\n   - Verify no interdependencies between test cases\n\n9. Run the complete integration test suite again to ensure all tests pass:\n   ```bash\n   npm run test:integration\n   ```\n\n10. Document any API behavior changes or inconsistencies discovered during testing", "status": "pending", "dependencies": [26, 8, 12, 17, 23, 24], "priority": "medium", "subtasks": []}]}