# Task ID: 20
# Title: Implement Delete Queue API
# Status: done
# Dependencies: 2, 3, 4, 17
# Priority: medium
# Description: Create an API endpoint to delete a provider queue/resource.
# Details:
1. Create a new API handler for DELETE /api/providers/queues/:id
2. Use authentication middleware to verify provider
3. Verify provider owns the queue
4. Check for dependencies (appointments, etc.)
5. Call existing Wasp operation to delete queue
6. Format response according to API standards
7. Add to main.wasp API definitions

```typescript
// In main.wasp
api {
  httpRoute: DELETE /api/providers/queues/:id,
  fn: import { deleteQueue } from "@server/api/providers/queues",
  auth: true
}

// In @server/api/providers/queues.ts
import { z } from 'zod';
import { deleteProviderQueue, getQueueById, checkQueueDependencies } from '../../../queue/operations';
import { validateRequest } from '../../common/validation';
import { handleApiError } from '../../common/errors';

const deleteQueueSchema = z.object({
  params: z.object({
    id: z.string().uuid()
  })
});

export const deleteQueue = [
  validateRequest(deleteQueueSchema),
  async (req, res) => {
    try {
      const providerId = req.providerId;
      const queueId = req.params.id;
      
      // Verify provider owns this queue
      const existingQueue = await getQueueById(queueId);
      if (!existingQueue || existingQueue.providerId !== providerId) {
        return res.status(404).json({
          status: 'error',
          message: 'Queue not found'
        });
      }
      
      // Check for dependencies
      const dependencies = await checkQueueDependencies(queueId);
      if (dependencies.hasFutureAppointments) {
        return res.status(409).json({
          status: 'error',
          message: 'Cannot delete queue with future appointments',
          dependencies
        });
      }
      
      await deleteProviderQueue(queueId);
      
      return res.status(200).json({
        status: 'success',
        message: 'Queue deleted successfully'
      });
    } catch (error) {
      return handleApiError(res, error);
    }
  }
];
```

# Test Strategy:
1. Test with valid queue ID
2. Test with queue that has dependencies
3. Test with queue not owned by provider
4. Test with non-existent queue ID
5. Test with authenticated non-provider user
6. Verify queue is actually deleted from database
7. Verify proper error response when deletion not allowed

# Subtasks:
## 1. Create Queue Dependency Check Operation [done]
### Dependencies: None
### Description: Implement the checkQueueDependencies operation to verify if a queue can be safely deleted by checking for future appointments or other dependencies.
### Details:
Create a new file `src/queue/operations.ts` (if it doesn't exist) and implement the `checkQueueDependencies` function. This function should query the database for any future appointments associated with the queue and return an object with a boolean flag indicating if dependencies exist. Use Prisma queries to check for appointments where the date is in the future and the queue ID matches.

## 2. Implement Queue Deletion Operation [done]
### Dependencies: 20.1
### Description: Create the deleteProviderQueue operation that handles the actual deletion of a queue from the database.
### Details:
In the same `src/queue/operations.ts` file, implement the `deleteProviderQueue` function that takes a queue ID and removes the corresponding record from the database. Include proper error handling for cases where the queue doesn't exist. Use Prisma's delete operation and ensure all related records are properly handled (either deleted or updated as appropriate).

## 3. Create Queue Validation Schema [done]
### Dependencies: None
### Description: Define the Zod validation schema for the delete queue API endpoint to ensure proper request validation.
### Details:
In the file `src/server/api/providers/queues.ts`, implement the `deleteQueueSchema` using Zod to validate that the request contains a valid UUID in the params. This schema will be used by the validateRequest middleware to ensure all incoming requests are properly formatted.

## 4. Implement Delete Queue API Handler [done]
### Dependencies: 20.1, 20.2, 20.3
### Description: Create the API handler function that processes delete requests for provider queues.
### Details:
Complete the implementation of the `deleteQueue` handler in `src/server/api/providers/queues.ts`. The handler should verify the provider owns the queue, check for dependencies using the checkQueueDependencies function, and then call deleteProviderQueue if deletion is allowed. Ensure proper HTTP status codes and response formats are used for all scenarios (success, not found, conflict due to dependencies, etc.).

## 5. Add API Route to main.wasp [done]
### Dependencies: 20.4
### Description: Register the delete queue API endpoint in the main.wasp file to make it available in the application.
### Details:
Add the API route definition to the main.wasp file using the correct Wasp syntax. The route should be defined as `DELETE /api/providers/queues/:id` and should reference the deleteQueue handler function from the appropriate file. Ensure authentication is required for this endpoint by setting `auth: true` in the API definition.

