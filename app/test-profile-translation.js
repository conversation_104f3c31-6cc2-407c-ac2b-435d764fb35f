/**
 * Test script for Provider Profile Translation APIs
 * Tests the translation functionality for profile retrieval and updates
 */

const axios = require('axios');

// Configuration
const BASE_URL = 'https://dalti-test.adscloud.org:8443';
const API_BASE = `${BASE_URL}/api/auth/providers`;

// Test data
const updateProfileData = {
  title: "عيادة الدكتور أحمد للأسنان", // Arabic: Dr. <PERSON>'s Dental Clinic
  presentation: "عيادة متخصصة في طب الأسنان مع أحدث التقنيات والمعدات الطبية المتطورة. نقدم خدمات شاملة لجميع أفراد العائلة", // Arabic: Specialized dental clinic with latest technologies and advanced medical equipment. We provide comprehensive services for all family members
  phone: "+213555123456"
};

const updateProfileDataFrench = {
  title: "Clinique Dentaire Dr. <PERSON>", // French: Dr. <PERSON>'s Dental Clinic
  presentation: "Clinique spécialisée en dentisterie avec les dernières technologies et équipements médicaux avancés. Nous offrons des services complets pour toute la famille", // French: Specialized dental clinic with latest technologies and advanced medical equipment. We offer comprehensive services for the whole family
};

// Helper function to make authenticated requests
async function makeAuthenticatedRequest(method, endpoint, data = null, token) {
  const config = {
    method,
    url: `${API_BASE}${endpoint}`,
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  };

  if (data) {
    config.data = data;
  }

  try {
    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`Error in ${method} ${endpoint}:`, error.response?.data || error.message);
    throw error;
  }
}

// Test functions
async function testProfileTranslation(authToken) {
  console.log('\n🧪 Testing Provider Profile Translation APIs...\n');

  try {
    // Test 1: Get current profile (baseline)
    console.log('📋 Test 1: Getting current provider profile...');
    const initialResponse = await makeAuthenticatedRequest('GET', '/profile', null, authToken);
    console.log('✅ Initial profile retrieved successfully');
    console.log('👤 Current profile:', {
      id: initialResponse.data.id,
      title: initialResponse.data.title,
      presentation: initialResponse.data.presentation,
      phone: initialResponse.data.phone,
      isVerified: initialResponse.data.isVerified,
      isSetupComplete: initialResponse.data.isSetupComplete
    });

    // Test 2: Update profile with Arabic text
    console.log('\n✏️ Test 2: Updating profile with Arabic text...');
    const updateResponse = await makeAuthenticatedRequest('PUT', '/profile', updateProfileData, authToken);
    console.log('✅ Profile updated successfully');
    console.log('👤 Updated profile:', {
      id: updateResponse.data.id,
      title: updateResponse.data.title,
      presentation: updateResponse.data.presentation,
      phone: updateResponse.data.phone
    });

    // Test 3: Get profile after Arabic update (should return translated content)
    console.log('\n🔍 Test 3: Getting profile after Arabic update...');
    const afterArabicResponse = await makeAuthenticatedRequest('GET', '/profile', null, authToken);
    console.log('✅ Profile retrieved after Arabic update');
    console.log('👤 Profile after Arabic update:', {
      id: afterArabicResponse.data.id,
      title: afterArabicResponse.data.title,
      presentation: afterArabicResponse.data.presentation,
      phone: afterArabicResponse.data.phone
    });

    // Test 4: Update profile with French text
    console.log('\n✏️ Test 4: Updating profile with French text...');
    const updateFrenchResponse = await makeAuthenticatedRequest('PUT', '/profile', updateProfileDataFrench, authToken);
    console.log('✅ Profile updated with French text');
    console.log('👤 Updated profile (French):', {
      id: updateFrenchResponse.data.id,
      title: updateFrenchResponse.data.title,
      presentation: updateFrenchResponse.data.presentation,
      phone: updateFrenchResponse.data.phone
    });

    // Test 5: Final verification - get profile again
    console.log('\n🔄 Test 5: Final profile verification...');
    const finalResponse = await makeAuthenticatedRequest('GET', '/profile', null, authToken);
    console.log('✅ Final profile verification successful');
    console.log('👤 Final profile state:', {
      id: finalResponse.data.id,
      title: finalResponse.data.title,
      presentation: finalResponse.data.presentation,
      phone: finalResponse.data.phone,
      isVerified: finalResponse.data.isVerified,
      isSetupComplete: finalResponse.data.isSetupComplete,
      category: finalResponse.data.category,
      averageRating: finalResponse.data.averageRating,
      totalReviews: finalResponse.data.totalReviews
    });

    console.log('\n🎉 All translation tests completed successfully!');
    console.log('\n📊 Test Summary:');
    console.log('- ✅ Initial profile retrieval');
    console.log('- ✅ Profile update with Arabic text');
    console.log('- ✅ Translation storage during Arabic update');
    console.log('- ✅ Translated content retrieval after Arabic update');
    console.log('- ✅ Profile update with French text');
    console.log('- ✅ Translation storage during French update');
    console.log('- ✅ Final profile verification');

    return finalResponse.data.id;

  } catch (error) {
    console.error('❌ Translation test failed:', error.message);
    throw error;
  }
}

// Main execution
async function main() {
  console.log('🚀 Starting Provider Profile Translation API Tests');
  console.log('🌐 Base URL:', BASE_URL);
  
  // Note: You'll need to provide a valid authentication token
  const authToken = process.env.PROVIDER_AUTH_TOKEN;
  
  if (!authToken) {
    console.error('❌ Error: PROVIDER_AUTH_TOKEN environment variable is required');
    console.log('💡 Usage: PROVIDER_AUTH_TOKEN=your_token_here node test-profile-translation.js');
    process.exit(1);
  }

  try {
    await testProfileTranslation(authToken);
    console.log('\n✅ All tests passed successfully!');
  } catch (error) {
    console.error('\n❌ Tests failed:', error.message);
    process.exit(1);
  }
}

// Run the tests
if (require.main === module) {
  main();
}

module.exports = { testProfileTranslation };
