-- CreateTable
CREATE TABLE "Advertisement" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "title" TEXT NOT NULL,
    "subtitle" TEXT,
    "description" TEXT,
    "callToActionText" TEXT NOT NULL,
    "callToActionLink" TEXT NOT NULL,
    "isExternal" BOOLEAN NOT NULL DEFAULT false,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "sortOrder" INTEGER NOT NULL DEFAULT 0,
    "backgroundImageId" TEXT,
    "pngImageId" TEXT,

    CONSTRAINT "Advertisement_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "Advertisement_isActive_idx" ON "Advertisement"("isActive");

-- CreateIndex
CREATE INDEX "Advertisement_sortOrder_idx" ON "Advertisement"("sortOrder");

-- CreateIndex
CREATE UNIQUE INDEX "Advertisement_backgroundImageId_key" ON "Advertisement"("backgroundImageId");

-- CreateIndex
CREATE UNIQUE INDEX "Advertisement_pngImageId_key" ON "Advertisement"("pngImageId");

-- AddForeignKey
ALTER TABLE "Advertisement" ADD CONSTRAINT "Advertisement_backgroundImageId_fkey" FOREIGN KEY ("backgroundImageId") REFERENCES "File"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Advertisement" ADD CONSTRAINT "Advertisement_pngImageId_fkey" FOREIGN KEY ("pngImageId") REFERENCES "File"("id") ON DELETE SET NULL ON UPDATE CASCADE;
