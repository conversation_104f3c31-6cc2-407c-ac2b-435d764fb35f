-- AlterTable
ALTER TABLE "Appointment" ADD COLUMN     "queueId" INTEGER;

-- CreateTable
CREATE TABLE "Queue" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "title" TEXT NOT NULL,
    "sProvidingPlaceId" INTEGER NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "sProviderId" INTEGER,

    CONSTRAINT "Queue_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "QueueOpening" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "queueId" INTEGER NOT NULL,
    "dayOfWeek" TEXT NOT NULL,
    "type" TEXT NOT NULL DEFAULT 'regular',
    "isActive" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "QueueOpening_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "QueueOpeningHours" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "queueOpeningId" INTEGER NOT NULL,
    "timeFrom" TEXT NOT NULL,
    "timeTo" TEXT NOT NULL,

    CONSTRAINT "QueueOpeningHours_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_ServiceQueues" (
    "A" INTEGER NOT NULL,
    "B" INTEGER NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "Queue_title_sProvidingPlaceId_key" ON "Queue"("title", "sProvidingPlaceId");

-- CreateIndex
CREATE UNIQUE INDEX "QueueOpening_queueId_dayOfWeek_type_key" ON "QueueOpening"("queueId", "dayOfWeek", "type");

-- CreateIndex
CREATE UNIQUE INDEX "_ServiceQueues_AB_unique" ON "_ServiceQueues"("A", "B");

-- CreateIndex
CREATE INDEX "_ServiceQueues_B_index" ON "_ServiceQueues"("B");

-- AddForeignKey
ALTER TABLE "Appointment" ADD CONSTRAINT "Appointment_queueId_fkey" FOREIGN KEY ("queueId") REFERENCES "Queue"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Queue" ADD CONSTRAINT "Queue_sProvidingPlaceId_fkey" FOREIGN KEY ("sProvidingPlaceId") REFERENCES "SProvidingPlace"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Queue" ADD CONSTRAINT "Queue_sProviderId_fkey" FOREIGN KEY ("sProviderId") REFERENCES "SProvider"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "QueueOpening" ADD CONSTRAINT "QueueOpening_queueId_fkey" FOREIGN KEY ("queueId") REFERENCES "Queue"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "QueueOpeningHours" ADD CONSTRAINT "QueueOpeningHours_queueOpeningId_fkey" FOREIGN KEY ("queueOpeningId") REFERENCES "QueueOpening"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_ServiceQueues" ADD CONSTRAINT "_ServiceQueues_A_fkey" FOREIGN KEY ("A") REFERENCES "Queue"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_ServiceQueues" ADD CONSTRAINT "_ServiceQueues_B_fkey" FOREIGN KEY ("B") REFERENCES "Service"("id") ON DELETE CASCADE ON UPDATE CASCADE;
