/*
  Warnings:

  - A unique constraint covering the columns `[chargilyCustomerId]` on the table `User` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "User" ADD COLUMN     "chargilyCustomerId" TEXT,
ADD COLUMN     "chargilyPaymentMethod" TEXT,
ADD COLUMN     "chargilyWebhookData" TEXT;

-- CreateTable
CREATE TABLE "ChargilyPayment" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" TEXT NOT NULL,
    "chargilyPaymentId" TEXT NOT NULL,
    "chargilyCustomerId" TEXT,
    "chargilyCheckoutId" TEXT,
    "chargilyPaymentLinkId" TEXT,
    "status" TEXT NOT NULL,
    "amount" DOUBLE PRECISION NOT NULL,
    "currency" TEXT NOT NULL DEFAULT 'dzd',
    "paymentMethod" TEXT,
    "planId" TEXT,
    "planType" TEXT,
    "creditsAllocated" INTEGER,
    "metadata" TEXT,
    "webhookData" TEXT,

    CONSTRAINT "ChargilyPayment_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "ChargilyPayment_chargilyPaymentId_key" ON "ChargilyPayment"("chargilyPaymentId");

-- CreateIndex
CREATE INDEX "ChargilyPayment_userId_status_idx" ON "ChargilyPayment"("userId", "status");

-- CreateIndex
CREATE INDEX "ChargilyPayment_chargilyPaymentId_idx" ON "ChargilyPayment"("chargilyPaymentId");

-- CreateIndex
CREATE INDEX "ChargilyPayment_status_createdAt_idx" ON "ChargilyPayment"("status", "createdAt");

-- CreateIndex
CREATE INDEX "ChargilyPayment_planId_status_idx" ON "ChargilyPayment"("planId", "status");

-- CreateIndex
CREATE UNIQUE INDEX "User_chargilyCustomerId_key" ON "User"("chargilyCustomerId");

-- AddForeignKey
ALTER TABLE "ChargilyPayment" ADD CONSTRAINT "ChargilyPayment_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
