-- Update existing users with default values for subscription fields
-- This migration fixes users who were created before the default values were set

-- Update subscriptionStatus for users who have NULL values
UPDATE "User" 
SET "subscriptionStatus" = 'active' 
WHERE "subscriptionStatus" IS NULL;

-- Update subscriptionPlan for users who have NULL values
UPDATE "User" 
SET "subscriptionPlan" = 'free' 
WHERE "subscriptionPlan" IS NULL;

-- Update datePaid for users who have NULL values
UPDATE "User" 
SET "datePaid" = CURRENT_TIMESTAMP 
WHERE "datePaid" IS NULL;

-- Update credits for users who have NULL values (should be rare since it's an Int with default)
UPDATE "User" 
SET "credits" = 30 
WHERE "credits" IS NULL;

-- Update queues for users who have NULL values (should be rare since it's an Int with default)
UPDATE "User" 
SET "queues" = 1 
WHERE "queues" IS NULL;
