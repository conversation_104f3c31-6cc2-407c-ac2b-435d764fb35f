-- AlterTable
ALTER TABLE "ProviderCategory" ADD COLUMN     "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "description" TEXT,
ADD COLUMN     "isActive" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "metadata" JSONB,
ADD COLUMN     "sortOrder" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP;

-- CreateIndex
CREATE INDEX "ProviderCategory_isActive_idx" ON "ProviderCategory"("isActive");

-- CreateIndex
CREATE INDEX "ProviderCategory_sortOrder_idx" ON "ProviderCategory"("sortOrder");

-- CreateIndex
CREATE INDEX "ProviderCategory_parentId_idx" ON "ProviderCategory"("parentId");
