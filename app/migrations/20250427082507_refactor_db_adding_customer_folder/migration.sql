-- CreateTable
CREATE TABLE "CustomerFolder" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "customerId" TEXT NOT NULL,
    "sProviderId" INTEGER NOT NULL,

    CONSTRAINT "CustomerFolder_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "CustomerFolder_customerId_sProviderId_key" ON "CustomerFolder"("customerId", "sProviderId");

-- AddForeignKey
ALTER TABLE "CustomerFolder" ADD CONSTRAINT "CustomerFolder_customerId_fkey" FOREIGN KEY ("customerId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- Add<PERSON><PERSON><PERSON><PERSON><PERSON>
ALTER TABLE "CustomerFolder" ADD CONSTRAINT "CustomerFolder_sProviderId_fkey" FOREIGN KEY ("sProviderId") REFERENCES "SProvider"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
