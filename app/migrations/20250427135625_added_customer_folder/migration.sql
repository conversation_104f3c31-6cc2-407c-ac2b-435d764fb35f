/*
  Warnings:

  - You are about to drop the column `customerId` on the `Appointment` table. All the data in the column will be lost.
  - Added the required column `customerFolderId` to the `Appointment` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "Appointment" DROP CONSTRAINT "Appointment_customerId_fkey";

-- AlterTable
ALTER TABLE "Appointment" DROP COLUMN "customerId",
ADD COLUMN     "customerFolderId" INTEGER NOT NULL;

-- CreateTable
CREATE TABLE "CustomerFolder" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "sProviderId" INTEGER NOT NULL,
    "userId" TEXT NOT NULL,
    "notes" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "CustomerFolder_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "CustomerFolder_sProviderId_userId_key" ON "CustomerFolder"("sProviderId", "userId");

-- AddForeignKey
ALTER TABLE "CustomerFolder" ADD CONSTRAINT "CustomerFolder_sProviderId_fkey" FOREIGN KEY ("sProviderId") REFERENCES "SProvider"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CustomerFolder" ADD CONSTRAINT "CustomerFolder_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Appointment" ADD CONSTRAINT "Appointment_customerFolderId_fkey" FOREIGN KEY ("customerFolderId") REFERENCES "CustomerFolder"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
