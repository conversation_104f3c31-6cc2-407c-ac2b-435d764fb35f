-- AlterTable
ALTER TABLE "Appointment" ADD COLUMN     "isOverflowed" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "overflowDetectedAt" TIMESTAMP(3),
ADD COLUMN     "overflowProcessedAt" TIMESTAMP(3),
ADD COLUMN     "overflowProcessingStatus" TEXT,
ADD COLUMN     "overflowReason" TEXT;

-- CreateTable
CREATE TABLE "RescheduleRequest" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "appointmentId" INTEGER NOT NULL,
    "requestedById" TEXT NOT NULL,
    "respondedById" TEXT,
    "suggestedStartTime" TIMESTAMP(3) NOT NULL,
    "suggestedEndTime" TIMESTAMP(3) NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "responseTime" TIMESTAMP(3),
    "reason" TEXT,
    "responseNote" TEXT,
    "expiresAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "RescheduleRequest_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "RescheduleRequest" ADD CONSTRAINT "RescheduleRequest_appointmentId_fkey" FOREIGN KEY ("appointmentId") REFERENCES "Appointment"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RescheduleRequest" ADD CONSTRAINT "RescheduleRequest_requestedById_fkey" FOREIGN KEY ("requestedById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RescheduleRequest" ADD CONSTRAINT "RescheduleRequest_respondedById_fkey" FOREIGN KEY ("respondedById") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
