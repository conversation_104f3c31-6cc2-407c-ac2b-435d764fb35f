/*
  Warnings:

  - You are about to drop the column `changeDate` on the `AppointmentHistory` table. All the data in the column will be lost.
  - Made the column `newStatus` on table `AppointmentHistory` required. This step will fail if there are existing NULL values in that column.
  - Made the column `previousStatus` on table `AppointmentHistory` required. This step will fail if there are existing NULL values in that column.
  - Made the column `newAgendaId` on table `AppointmentHistory` required. This step will fail if there are existing NULL values in that column.
  - Made the column `previousAgendaId` on table `AppointmentHistory` required. This step will fail if there are existing NULL values in that column.
  - Made the column `newMotifId` on table `AppointmentHistory` required. This step will fail if there are existing NULL values in that column.
  - Made the column `previousMotifId` on table `AppointmentHistory` required. This step will fail if there are existing NULL values in that column.
  - Made the column `changeReason` on table `AppointmentHistory` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE "Appointment" ALTER COLUMN "isOverflowed" DROP NOT NULL;

-- AlterTable
ALTER TABLE "AppointmentHistory" DROP COLUMN "changeDate",
ADD COLUMN     "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ALTER COLUMN "newStatus" SET NOT NULL,
ALTER COLUMN "previousStatus" SET NOT NULL,
ALTER COLUMN "newAgendaId" SET NOT NULL,
ALTER COLUMN "previousAgendaId" SET NOT NULL,
ALTER COLUMN "newMotifId" SET NOT NULL,
ALTER COLUMN "previousMotifId" SET NOT NULL,
ALTER COLUMN "changeReason" SET NOT NULL;

-- CreateTable
CREATE TABLE "QueueSwapRequest" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "appointment1Id" INTEGER NOT NULL,
    "appointment2Id" INTEGER NOT NULL,
    "requestedById" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'pending_customer2_approval',
    "expiresAt" TIMESTAMP(3),
    "notes" TEXT,

    CONSTRAINT "QueueSwapRequest_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "QueueSwapRequest_appointment1Id_idx" ON "QueueSwapRequest"("appointment1Id");

-- CreateIndex
CREATE INDEX "QueueSwapRequest_appointment2Id_idx" ON "QueueSwapRequest"("appointment2Id");

-- CreateIndex
CREATE INDEX "QueueSwapRequest_requestedById_idx" ON "QueueSwapRequest"("requestedById");

-- CreateIndex
CREATE INDEX "QueueSwapRequest_status_idx" ON "QueueSwapRequest"("status");

-- CreateIndex
CREATE INDEX "RescheduleRequest_appointmentId_idx" ON "RescheduleRequest"("appointmentId");

-- CreateIndex
CREATE INDEX "RescheduleRequest_requestedById_idx" ON "RescheduleRequest"("requestedById");

-- CreateIndex
CREATE INDEX "RescheduleRequest_respondedById_idx" ON "RescheduleRequest"("respondedById");

-- AddForeignKey
ALTER TABLE "QueueSwapRequest" ADD CONSTRAINT "QueueSwapRequest_appointment1Id_fkey" FOREIGN KEY ("appointment1Id") REFERENCES "Appointment"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "QueueSwapRequest" ADD CONSTRAINT "QueueSwapRequest_appointment2Id_fkey" FOREIGN KEY ("appointment2Id") REFERENCES "Appointment"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "QueueSwapRequest" ADD CONSTRAINT "QueueSwapRequest_requestedById_fkey" FOREIGN KEY ("requestedById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
