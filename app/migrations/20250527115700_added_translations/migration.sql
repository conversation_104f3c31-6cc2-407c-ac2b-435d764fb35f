-- CreateEnum
CREATE TYPE "LanguageCode" AS ENUM ('EN', 'AR', 'FR');

-- CreateTable
CREATE TABLE "Translation" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "refId" TEXT NOT NULL,
    "tableName" TEXT NOT NULL,
    "refField" TEXT NOT NULL,
    "languageCode" "LanguageCode" NOT NULL,
    "text" TEXT NOT NULL,

    CONSTRAINT "Translation_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "Translation_tableName_refId_refField_idx" ON "Translation"("tableName", "refId", "refField");

-- CreateIndex
CREATE INDEX "Translation_languageCode_idx" ON "Translation"("languageCode");

-- CreateIndex
CREATE UNIQUE INDEX "Translation_tableName_refId_refField_languageCode_key" ON "Translation"("tableName", "refId", "refField", "languageCode");
