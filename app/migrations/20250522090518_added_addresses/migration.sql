/*
  Warnings:

  - A unique constraint covering the columns `[detailedAddressId]` on the table `SProvidingPlace` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "SProvidingPlace" ADD COLUMN     "detailedAddressId" INTEGER;

-- CreateTable
CREATE TABLE "Address" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "street" TEXT NOT NULL,
    "city" TEXT NOT NULL,
    "state" TEXT,
    "postalCode" TEXT NOT NULL,
    "country" TEXT NOT NULL,
    "latitude" DOUBLE PRECISION NOT NULL,
    "longitude" DOUBLE PRECISION NOT NULL,
    "description" TEXT,
    "isPrimary" BOOLEAN NOT NULL DEFAULT false,
    "userId" TEXT,

    CONSTRAINT "Address_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "Address_userId_idx" ON "Address"("userId");

-- CreateIndex
CREATE INDEX "Address_latitude_longitude_idx" ON "Address"("latitude", "longitude");

-- CreateIndex
CREATE UNIQUE INDEX "SProvidingPlace_detailedAddressId_key" ON "SProvidingPlace"("detailedAddressId");

-- AddForeignKey
ALTER TABLE "Address" ADD CONSTRAINT "Address_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SProvidingPlace" ADD CONSTRAINT "SProvidingPlace_detailedAddressId_fkey" FOREIGN KEY ("detailedAddressId") REFERENCES "Address"("id") ON DELETE SET NULL ON UPDATE CASCADE;
