-- This migration seeds provider categories and their translations

-- Insert parent categories with proper timestamps
INSERT INTO "ProviderCategory" ("title", "description", "isActive", "sortOrder", "parentId", "createdAt", "updatedAt") VALUES
('Healthcare', 'Medical and health-related services', true, 1, NULL, NOW(), NOW()),
('Legal', 'Legal and law-related services', true, 2, NULL, NOW(), NOW()),
('Financial', 'Financial and accounting services', true, 3, NULL, NOW(), NOW()),
('Automotive', 'Car and vehicle-related services', true, 4, NULL, NOW(), NOW()),
('Home Services', 'Home maintenance and improvement services', true, 5, NULL, NOW(), NOW()),
('Education', 'Educational and training services', true, 6, NULL, NOW(), NOW()),
('Beauty & Wellness', 'Beauty, wellness and fitness services', true, 7, NULL, NOW(), NOW()),
('Professional Services', 'Business and professional services', true, 8, NULL, NOW(), NOW()),
('Food & Dining', 'Food and restaurant services', true, 9, NULL, NOW(), NOW()),
('Retail', 'Retail and shopping services', true, 10, NULL, NOW(), NOW()),
('Government & Public Services', 'Government and public services', true, 11, NULL, NOW(), NOW()),
('Arts & Entertainment', 'Arts, culture and entertainment services', true, 12, NULL, NOW(), NOW()),
('Sports & Recreation', 'Sports and recreational services', true, 13, NULL, NOW(), NOW()),
('Pet Services', 'Pet care and animal services', true, 14, NULL, NOW(), NOW()),
('Trades & Construction', 'Construction and trade services', true, 15, NULL, NOW(), NOW())
ON CONFLICT ("title") DO UPDATE SET
  "description" = EXCLUDED."description",
  "isActive" = EXCLUDED."isActive",
  "sortOrder" = EXCLUDED."sortOrder",
  "updatedAt" = NOW();

-- Insert child categories for Healthcare
INSERT INTO "ProviderCategory" ("title", "description", "isActive", "sortOrder", "parentId", "createdAt", "updatedAt")
SELECT 'Doctor', 'General medical practitioners', true, 1, p.id, NOW(), NOW()
FROM "ProviderCategory" p WHERE p.title = 'Healthcare'
UNION ALL
SELECT 'Dentist', 'Dental care professionals', true, 2, p.id, NOW(), NOW()
FROM "ProviderCategory" p WHERE p.title = 'Healthcare'
UNION ALL
SELECT 'Physiotherapist', 'Physical therapy specialists', true, 3, p.id, NOW(), NOW()
FROM "ProviderCategory" p WHERE p.title = 'Healthcare'
UNION ALL
SELECT 'Optometrist', 'Eye care specialists', true, 4, p.id, NOW(), NOW()
FROM "ProviderCategory" p WHERE p.title = 'Healthcare'
UNION ALL
SELECT 'Chiropractor', 'Chiropractic care specialists', true, 5, p.id, NOW(), NOW()
FROM "ProviderCategory" p WHERE p.title = 'Healthcare'
UNION ALL
SELECT 'Pharmacy', 'Pharmaceutical services', true, 6, p.id, NOW(), NOW()
FROM "ProviderCategory" p WHERE p.title = 'Healthcare'
ON CONFLICT ("title") DO UPDATE SET
  "description" = EXCLUDED."description",
  "parentId" = EXCLUDED."parentId",
  "sortOrder" = EXCLUDED."sortOrder",
  "updatedAt" = NOW();

-- Insert child categories for Legal
INSERT INTO "ProviderCategory" ("title", "description", "isActive", "sortOrder", "parentId") VALUES
('Lawyer', 'Legal representation services', true, 1, (SELECT id FROM "ProviderCategory" WHERE title = 'Legal')),
('Notary', 'Notarial services', true, 2, (SELECT id FROM "ProviderCategory" WHERE title = 'Legal')),
('Paralegal', 'Legal assistance services', true, 3, (SELECT id FROM "ProviderCategory" WHERE title = 'Legal')),
('Mediator', 'Mediation and conflict resolution', true, 4, (SELECT id FROM "ProviderCategory" WHERE title = 'Legal'))
ON CONFLICT ("title") DO NOTHING;

-- Insert child categories for Financial
INSERT INTO "ProviderCategory" ("title", "description", "isActive", "sortOrder", "parentId") VALUES
('Accountant', 'Accounting and bookkeeping services', true, 1, (SELECT id FROM "ProviderCategory" WHERE title = 'Financial')),
('Financial Advisor', 'Financial planning and advisory services', true, 2, (SELECT id FROM "ProviderCategory" WHERE title = 'Financial')),
('Insurance Agent', 'Insurance services and consultation', true, 3, (SELECT id FROM "ProviderCategory" WHERE title = 'Financial')),
('Bookkeeper', 'Bookkeeping and record management', true, 4, (SELECT id FROM "ProviderCategory" WHERE title = 'Financial'))
ON CONFLICT ("title") DO NOTHING;

-- Insert child categories for Automotive
INSERT INTO "ProviderCategory" ("title", "description", "isActive", "sortOrder", "parentId") VALUES
('Mechanic', 'Vehicle repair and maintenance', true, 1, (SELECT id FROM "ProviderCategory" WHERE title = 'Automotive')),
('Car Wash', 'Vehicle cleaning services', true, 2, (SELECT id FROM "ProviderCategory" WHERE title = 'Automotive')),
('Tire Shop', 'Tire sales and installation', true, 3, (SELECT id FROM "ProviderCategory" WHERE title = 'Automotive')),
('Body Shop', 'Vehicle body repair and painting', true, 4, (SELECT id FROM "ProviderCategory" WHERE title = 'Automotive'))
ON CONFLICT ("title") DO NOTHING;

-- Insert child categories for Home Services
INSERT INTO "ProviderCategory" ("title", "description", "isActive", "sortOrder", "parentId") VALUES
('Plumber', 'Plumbing services and repairs', true, 1, (SELECT id FROM "ProviderCategory" WHERE title = 'Home Services')),
('Electrician', 'Electrical services and repairs', true, 2, (SELECT id FROM "ProviderCategory" WHERE title = 'Home Services')),
('Landscaper', 'Landscaping and garden services', true, 3, (SELECT id FROM "ProviderCategory" WHERE title = 'Home Services')),
('HVAC Technician', 'Heating, ventilation, and air conditioning', true, 4, (SELECT id FROM "ProviderCategory" WHERE title = 'Home Services')),
('Painter', 'Painting and decorating services', true, 5, (SELECT id FROM "ProviderCategory" WHERE title = 'Home Services')),
('Cleaning Service', 'House and office cleaning', true, 6, (SELECT id FROM "ProviderCategory" WHERE title = 'Home Services'))
ON CONFLICT ("title") DO NOTHING;

-- Insert child categories for Education
INSERT INTO "ProviderCategory" ("title", "description", "isActive", "sortOrder", "parentId") VALUES
('Tutor', 'Private tutoring services', true, 1, (SELECT id FROM "ProviderCategory" WHERE title = 'Education')),
('Music Teacher', 'Music lessons and instruction', true, 2, (SELECT id FROM "ProviderCategory" WHERE title = 'Education')),
('Driving School', 'Driving lessons and instruction', true, 3, (SELECT id FROM "ProviderCategory" WHERE title = 'Education')),
('Language School', 'Language learning and instruction', true, 4, (SELECT id FROM "ProviderCategory" WHERE title = 'Education'))
ON CONFLICT ("title") DO NOTHING;

-- Insert child categories for Beauty & Wellness
INSERT INTO "ProviderCategory" ("title", "description", "isActive", "sortOrder", "parentId") VALUES
('Hair Salon', 'Hair styling and treatment', true, 1, (SELECT id FROM "ProviderCategory" WHERE title = 'Beauty & Wellness')),
('Barber Shop', 'Men''s grooming and haircuts', true, 2, (SELECT id FROM "ProviderCategory" WHERE title = 'Beauty & Wellness')),
('Nail Salon', 'Nail care and manicure services', true, 3, (SELECT id FROM "ProviderCategory" WHERE title = 'Beauty & Wellness')),
('Spa', 'Spa and relaxation services', true, 4, (SELECT id FROM "ProviderCategory" WHERE title = 'Beauty & Wellness')),
('Massage Therapist', 'Therapeutic massage services', true, 5, (SELECT id FROM "ProviderCategory" WHERE title = 'Beauty & Wellness')),
('Gym', 'Fitness and exercise facilities', true, 6, (SELECT id FROM "ProviderCategory" WHERE title = 'Beauty & Wellness'))
ON CONFLICT ("title") DO NOTHING;

-- Insert child categories for Professional Services
INSERT INTO "ProviderCategory" ("title", "description", "isActive", "sortOrder", "parentId") VALUES
('Consultant', 'Business consulting services', true, 1, (SELECT id FROM "ProviderCategory" WHERE title = 'Professional Services')),
('Marketing Agency', 'Marketing and advertising services', true, 2, (SELECT id FROM "ProviderCategory" WHERE title = 'Professional Services')),
('Web Developer', 'Website development and design', true, 3, (SELECT id FROM "ProviderCategory" WHERE title = 'Professional Services')),
('Graphic Designer', 'Graphic design and visual services', true, 4, (SELECT id FROM "ProviderCategory" WHERE title = 'Professional Services')),
('Photographer', 'Photography services', true, 5, (SELECT id FROM "ProviderCategory" WHERE title = 'Professional Services'))
ON CONFLICT ("title") DO NOTHING;

-- Insert child categories for Food & Dining
INSERT INTO "ProviderCategory" ("title", "description", "isActive", "sortOrder", "parentId") VALUES
('Restaurant', 'Dining and restaurant services', true, 1, (SELECT id FROM "ProviderCategory" WHERE title = 'Food & Dining')),
('Cafe', 'Coffee shops and cafes', true, 2, (SELECT id FROM "ProviderCategory" WHERE title = 'Food & Dining')),
('Bakery', 'Bakery and pastry services', true, 3, (SELECT id FROM "ProviderCategory" WHERE title = 'Food & Dining')),
('Catering', 'Catering and event food services', true, 4, (SELECT id FROM "ProviderCategory" WHERE title = 'Food & Dining'))
ON CONFLICT ("title") DO NOTHING;

-- Insert child categories for Retail
INSERT INTO "ProviderCategory" ("title", "description", "isActive", "sortOrder", "parentId") VALUES
('Boutique', 'Fashion and clothing retail', true, 1, (SELECT id FROM "ProviderCategory" WHERE title = 'Retail')),
('Bookstore', 'Books and literature retail', true, 2, (SELECT id FROM "ProviderCategory" WHERE title = 'Retail')),
('Electronics Store', 'Electronics and technology retail', true, 3, (SELECT id FROM "ProviderCategory" WHERE title = 'Retail')),
('Grocery Store', 'Food and grocery retail', true, 4, (SELECT id FROM "ProviderCategory" WHERE title = 'Retail'))
ON CONFLICT ("title") DO NOTHING;

-- Insert child categories for Government & Public Services
INSERT INTO "ProviderCategory" ("title", "description", "isActive", "sortOrder", "parentId") VALUES
('Post Office', 'Postal and mailing services', true, 1, (SELECT id FROM "ProviderCategory" WHERE title = 'Government & Public Services')),
('Library', 'Public library services', true, 2, (SELECT id FROM "ProviderCategory" WHERE title = 'Government & Public Services')),
('DMV/Registry', 'Vehicle registration and licensing', true, 3, (SELECT id FROM "ProviderCategory" WHERE title = 'Government & Public Services'))
ON CONFLICT ("title") DO NOTHING;

-- Insert child categories for Arts & Entertainment
INSERT INTO "ProviderCategory" ("title", "description", "isActive", "sortOrder", "parentId") VALUES
('Art Gallery', 'Art exhibitions and galleries', true, 1, (SELECT id FROM "ProviderCategory" WHERE title = 'Arts & Entertainment')),
('Museum', 'Museums and cultural institutions', true, 2, (SELECT id FROM "ProviderCategory" WHERE title = 'Arts & Entertainment')),
('Cinema', 'Movie theaters and cinemas', true, 3, (SELECT id FROM "ProviderCategory" WHERE title = 'Arts & Entertainment')),
('Theater', 'Live theater and performances', true, 4, (SELECT id FROM "ProviderCategory" WHERE title = 'Arts & Entertainment'))
ON CONFLICT ("title") DO NOTHING;

-- Insert child categories for Sports & Recreation
INSERT INTO "ProviderCategory" ("title", "description", "isActive", "sortOrder", "parentId") VALUES
('Sports Club', 'Sports clubs and facilities', true, 1, (SELECT id FROM "ProviderCategory" WHERE title = 'Sports & Recreation')),
('Yoga Studio', 'Yoga classes and instruction', true, 2, (SELECT id FROM "ProviderCategory" WHERE title = 'Sports & Recreation')),
('Golf Course', 'Golf courses and instruction', true, 3, (SELECT id FROM "ProviderCategory" WHERE title = 'Sports & Recreation')),
('Personal Trainer', 'Personal fitness training', true, 4, (SELECT id FROM "ProviderCategory" WHERE title = 'Sports & Recreation'))
ON CONFLICT ("title") DO NOTHING;

-- Insert child categories for Pet Services
INSERT INTO "ProviderCategory" ("title", "description", "isActive", "sortOrder", "parentId") VALUES
('Veterinarian', 'Veterinary care and services', true, 1, (SELECT id FROM "ProviderCategory" WHERE title = 'Pet Services')),
('Pet Groomer', 'Pet grooming and care', true, 2, (SELECT id FROM "ProviderCategory" WHERE title = 'Pet Services')),
('Dog Walker', 'Dog walking and exercise services', true, 3, (SELECT id FROM "ProviderCategory" WHERE title = 'Pet Services')),
('Pet Sitter', 'Pet sitting and care services', true, 4, (SELECT id FROM "ProviderCategory" WHERE title = 'Pet Services'))
ON CONFLICT ("title") DO NOTHING;

-- Insert child categories for Trades & Construction
INSERT INTO "ProviderCategory" ("title", "description", "isActive", "sortOrder", "parentId") VALUES
('Carpenter', 'Carpentry and woodworking services', true, 1, (SELECT id FROM "ProviderCategory" WHERE title = 'Trades & Construction')),
('Roofer', 'Roofing services and repairs', true, 2, (SELECT id FROM "ProviderCategory" WHERE title = 'Trades & Construction')),
('Welder', 'Welding and metalwork services', true, 3, (SELECT id FROM "ProviderCategory" WHERE title = 'Trades & Construction')),
('General Contractor', 'General construction services', true, 4, (SELECT id FROM "ProviderCategory" WHERE title = 'Trades & Construction'))
ON CONFLICT ("title") DO NOTHING;

-- Insert translations for parent categories
-- Healthcare translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Healthcare'), 'title', 'EN', 'Healthcare'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Healthcare'), 'title', 'FR', 'Soins de santé'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Healthcare'), 'title', 'AR', 'الرعاية الصحية')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Legal translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Legal'), 'title', 'EN', 'Legal'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Legal'), 'title', 'FR', 'Juridique'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Legal'), 'title', 'AR', 'قانوني')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Financial translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Financial'), 'title', 'EN', 'Financial'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Financial'), 'title', 'FR', 'Financier'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Financial'), 'title', 'AR', 'مالي')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Automotive translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Automotive'), 'title', 'EN', 'Automotive'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Automotive'), 'title', 'FR', 'Automobile'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Automotive'), 'title', 'AR', 'سيارات')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Home Services translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Home Services'), 'title', 'EN', 'Home Services'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Home Services'), 'title', 'FR', 'Services à domicile'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Home Services'), 'title', 'AR', 'خدمات منزلية')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Education translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Education'), 'title', 'EN', 'Education'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Education'), 'title', 'FR', 'Éducation'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Education'), 'title', 'AR', 'تعليم')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Beauty & Wellness translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Beauty & Wellness'), 'title', 'EN', 'Beauty & Wellness'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Beauty & Wellness'), 'title', 'FR', 'Beauté et Bien-être'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Beauty & Wellness'), 'title', 'AR', 'الجمال والعافية')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Professional Services translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Professional Services'), 'title', 'EN', 'Professional Services'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Professional Services'), 'title', 'FR', 'Services professionnels'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Professional Services'), 'title', 'AR', 'خدمات احترافية')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Food & Dining translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Food & Dining'), 'title', 'EN', 'Food & Dining'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Food & Dining'), 'title', 'FR', 'Restauration et Alimentation'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Food & Dining'), 'title', 'AR', 'الأطعمة والمطاعم')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Retail translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Retail'), 'title', 'EN', 'Retail'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Retail'), 'title', 'FR', 'Vente au détail'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Retail'), 'title', 'AR', 'تجارة التجزئة')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Government & Public Services translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Government & Public Services'), 'title', 'EN', 'Government & Public Services'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Government & Public Services'), 'title', 'FR', 'Services gouvernementaux et publics'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Government & Public Services'), 'title', 'AR', 'الخدمات الحكومية والعامة')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Arts & Entertainment translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Arts & Entertainment'), 'title', 'EN', 'Arts & Entertainment'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Arts & Entertainment'), 'title', 'FR', 'Arts et Divertissements'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Arts & Entertainment'), 'title', 'AR', 'الفنون والترفيه')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Sports & Recreation translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Sports & Recreation'), 'title', 'EN', 'Sports & Recreation'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Sports & Recreation'), 'title', 'FR', 'Sports et Loisirs'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Sports & Recreation'), 'title', 'AR', 'الرياضة والترفيه')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Pet Services translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Pet Services'), 'title', 'EN', 'Pet Services'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Pet Services'), 'title', 'FR', 'Services pour animaux de compagnie'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Pet Services'), 'title', 'AR', 'خدمات الحيوانات الأليفة')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Trades & Construction translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Trades & Construction'), 'title', 'EN', 'Trades & Construction'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Trades & Construction'), 'title', 'FR', 'Métiers et Construction'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Trades & Construction'), 'title', 'AR', 'الحرف والبناء')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Insert translations for child categories - Healthcare
-- Doctor translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Doctor'), 'title', 'EN', 'Doctor'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Doctor'), 'title', 'FR', 'Médecin'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Doctor'), 'title', 'AR', 'طبيب')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Dentist translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Dentist'), 'title', 'EN', 'Dentist'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Dentist'), 'title', 'FR', 'Dentiste'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Dentist'), 'title', 'AR', 'طبيب أسنان')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Physiotherapist translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Physiotherapist'), 'title', 'EN', 'Physiotherapist'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Physiotherapist'), 'title', 'FR', 'Kinésithérapeute'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Physiotherapist'), 'title', 'AR', 'أخصائي علاج طبيعي')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Optometrist translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Optometrist'), 'title', 'EN', 'Optometrist'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Optometrist'), 'title', 'FR', 'Optométriste'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Optometrist'), 'title', 'AR', 'أخصائي بصريات')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Chiropractor translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Chiropractor'), 'title', 'EN', 'Chiropractor'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Chiropractor'), 'title', 'FR', 'Chiropracteur'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Chiropractor'), 'title', 'AR', 'مقوم العظام')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Pharmacy translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Pharmacy'), 'title', 'EN', 'Pharmacy'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Pharmacy'), 'title', 'FR', 'Pharmacie'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Pharmacy'), 'title', 'AR', 'صيدلية')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Insert translations for child categories - Legal
-- Lawyer translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Lawyer'), 'title', 'EN', 'Lawyer'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Lawyer'), 'title', 'FR', 'Avocat'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Lawyer'), 'title', 'AR', 'محامي')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Notary translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Notary'), 'title', 'EN', 'Notary'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Notary'), 'title', 'FR', 'Notaire'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Notary'), 'title', 'AR', 'كاتب عدل')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Paralegal translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Paralegal'), 'title', 'EN', 'Paralegal'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Paralegal'), 'title', 'FR', 'Technicien juridique'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Paralegal'), 'title', 'AR', 'مساعد قانوني')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Mediator translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Mediator'), 'title', 'EN', 'Mediator'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Mediator'), 'title', 'FR', 'Médiateur'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Mediator'), 'title', 'AR', 'وسيط')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Insert translations for remaining categories (Financial, Automotive, Home Services, Education, Beauty & Wellness)
-- This includes all the translations from the child_translations.sql file
-- Financial categories
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Accountant'), 'title', 'EN', 'Accountant'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Accountant'), 'title', 'FR', 'Comptable'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Accountant'), 'title', 'AR', 'محاسب'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Financial Advisor'), 'title', 'EN', 'Financial Advisor'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Financial Advisor'), 'title', 'FR', 'Conseiller financier'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Financial Advisor'), 'title', 'AR', 'مستشار مالي'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Insurance Agent'), 'title', 'EN', 'Insurance Agent'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Insurance Agent'), 'title', 'FR', 'Agent d''assurance'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Insurance Agent'), 'title', 'AR', 'وكيل تأمين'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Bookkeeper'), 'title', 'EN', 'Bookkeeper'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Bookkeeper'), 'title', 'FR', 'Aide-comptable'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Bookkeeper'), 'title', 'AR', 'مسك الدفاتر')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Automotive categories
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Mechanic'), 'title', 'EN', 'Mechanic'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Mechanic'), 'title', 'FR', 'Mécanicien'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Mechanic'), 'title', 'AR', 'ميكانيكي'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Car Wash'), 'title', 'EN', 'Car Wash'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Car Wash'), 'title', 'FR', 'Lavage auto'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Car Wash'), 'title', 'AR', 'غسيل سيارات'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Tire Shop'), 'title', 'EN', 'Tire Shop'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Tire Shop'), 'title', 'FR', 'Magasin de pneus'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Tire Shop'), 'title', 'AR', 'متجر إطارات'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Body Shop'), 'title', 'EN', 'Body Shop'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Body Shop'), 'title', 'FR', 'Carrosserie'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Body Shop'), 'title', 'AR', 'ورشة هياكل سيارات')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;
-- Insert translations for child categories - Financial
-- Accountant translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Accountant'), 'title', 'EN', 'Accountant'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Accountant'), 'title', 'FR', 'Comptable'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Accountant'), 'title', 'AR', 'محاسب')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Financial Advisor translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Financial Advisor'), 'title', 'EN', 'Financial Advisor'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Financial Advisor'), 'title', 'FR', 'Conseiller financier'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Financial Advisor'), 'title', 'AR', 'مستشار مالي')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Insurance Agent translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Insurance Agent'), 'title', 'EN', 'Insurance Agent'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Insurance Agent'), 'title', 'FR', 'Agent d''assurance'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Insurance Agent'), 'title', 'AR', 'وكيل تأمين')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Bookkeeper translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Bookkeeper'), 'title', 'EN', 'Bookkeeper'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Bookkeeper'), 'title', 'FR', 'Aide-comptable'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Bookkeeper'), 'title', 'AR', 'مسك الدفاتر')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Insert translations for child categories - Automotive
-- Mechanic translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Mechanic'), 'title', 'EN', 'Mechanic'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Mechanic'), 'title', 'FR', 'Mécanicien'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Mechanic'), 'title', 'AR', 'ميكانيكي')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Car Wash translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Car Wash'), 'title', 'EN', 'Car Wash'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Car Wash'), 'title', 'FR', 'Lavage auto'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Car Wash'), 'title', 'AR', 'غسيل سيارات')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Tire Shop translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Tire Shop'), 'title', 'EN', 'Tire Shop'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Tire Shop'), 'title', 'FR', 'Magasin de pneus'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Tire Shop'), 'title', 'AR', 'متجر إطارات')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Body Shop translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Body Shop'), 'title', 'EN', 'Body Shop'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Body Shop'), 'title', 'FR', 'Carrosserie'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Body Shop'), 'title', 'AR', 'ورشة هياكل سيارات')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Insert translations for child categories - Home Services
-- Plumber translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Plumber'), 'title', 'EN', 'Plumber'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Plumber'), 'title', 'FR', 'Plombier'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Plumber'), 'title', 'AR', 'سباك')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Electrician translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Electrician'), 'title', 'EN', 'Electrician'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Electrician'), 'title', 'FR', 'Électricien'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Electrician'), 'title', 'AR', 'كهربائي')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Landscaper translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Landscaper'), 'title', 'EN', 'Landscaper'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Landscaper'), 'title', 'FR', 'Paysagiste'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Landscaper'), 'title', 'AR', 'منسق حدائق')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- HVAC Technician translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'HVAC Technician'), 'title', 'EN', 'HVAC Technician'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'HVAC Technician'), 'title', 'FR', 'Technicien CVC'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'HVAC Technician'), 'title', 'AR', 'فني تدفئة وتهوية وتكييف')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Painter translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Painter'), 'title', 'EN', 'Painter'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Painter'), 'title', 'FR', 'Peintre'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Painter'), 'title', 'AR', 'دهان')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Cleaning Service translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Cleaning Service'), 'title', 'EN', 'Cleaning Service'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Cleaning Service'), 'title', 'FR', 'Service de nettoyage'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Cleaning Service'), 'title', 'AR', 'خدمة تنظيف')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Insert translations for child categories - Education
-- Tutor translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Tutor'), 'title', 'EN', 'Tutor'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Tutor'), 'title', 'FR', 'Tuteur'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Tutor'), 'title', 'AR', 'مدرس خصوصي')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Music Teacher translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Music Teacher'), 'title', 'EN', 'Music Teacher'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Music Teacher'), 'title', 'FR', 'Professeur de musique'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Music Teacher'), 'title', 'AR', 'معلم موسيقى')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Driving School translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Driving School'), 'title', 'EN', 'Driving School'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Driving School'), 'title', 'FR', 'Auto-école'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Driving School'), 'title', 'AR', 'مدرسة تعليم قيادة')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Language School translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Language School'), 'title', 'EN', 'Language School'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Language School'), 'title', 'FR', 'École de langues'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Language School'), 'title', 'AR', 'مدرسة لغات')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Insert translations for child categories - Beauty & Wellness
-- Hair Salon translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Hair Salon'), 'title', 'EN', 'Hair Salon'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Hair Salon'), 'title', 'FR', 'Salon de coiffure'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Hair Salon'), 'title', 'AR', 'صالون تجميل')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Barber Shop translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Barber Shop'), 'title', 'EN', 'Barber Shop'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Barber Shop'), 'title', 'FR', 'Barbier'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Barber Shop'), 'title', 'AR', 'محل حلاقة')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Nail Salon translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Nail Salon'), 'title', 'EN', 'Nail Salon'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Nail Salon'), 'title', 'FR', 'Salon de manucure'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Nail Salon'), 'title', 'AR', 'صالون أظافر')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Spa translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Spa'), 'title', 'EN', 'Spa'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Spa'), 'title', 'FR', 'Spa'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Spa'), 'title', 'AR', 'منتجع صحي')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Massage Therapist translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Massage Therapist'), 'title', 'EN', 'Massage Therapist'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Massage Therapist'), 'title', 'FR', 'Massothérapeute'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Massage Therapist'), 'title', 'AR', 'معالج بالتدليك')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Gym translations
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Gym'), 'title', 'EN', 'Gym'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Gym'), 'title', 'FR', 'Salle de sport'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Gym'), 'title', 'AR', 'صالة رياضية')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Insert translations for remaining categories
-- Professional Services
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Consultant'), 'title', 'EN', 'Consultant'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Consultant'), 'title', 'FR', 'Consultant'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Consultant'), 'title', 'AR', 'مستشار'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Marketing Agency'), 'title', 'EN', 'Marketing Agency'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Marketing Agency'), 'title', 'FR', 'Agence de marketing'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Marketing Agency'), 'title', 'AR', 'وكالة تسويق'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Web Developer'), 'title', 'EN', 'Web Developer'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Web Developer'), 'title', 'FR', 'Développeur web'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Web Developer'), 'title', 'AR', 'مطور ويب'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Graphic Designer'), 'title', 'EN', 'Graphic Designer'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Graphic Designer'), 'title', 'FR', 'Graphiste'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Graphic Designer'), 'title', 'AR', 'مصمم جرافيك'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Photographer'), 'title', 'EN', 'Photographer'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Photographer'), 'title', 'FR', 'Photographe'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Photographer'), 'title', 'AR', 'مصور فوتوغرافي')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Food & Dining
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Restaurant'), 'title', 'EN', 'Restaurant'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Restaurant'), 'title', 'FR', 'Restaurant'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Restaurant'), 'title', 'AR', 'مطعم'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Cafe'), 'title', 'EN', 'Cafe'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Cafe'), 'title', 'FR', 'Café'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Cafe'), 'title', 'AR', 'مقهى'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Bakery'), 'title', 'EN', 'Bakery'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Bakery'), 'title', 'FR', 'Boulangerie'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Bakery'), 'title', 'AR', 'مخبز'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Catering'), 'title', 'EN', 'Catering'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Catering'), 'title', 'FR', 'Traiteur'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Catering'), 'title', 'AR', 'خدمات تموين')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Retail
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Boutique'), 'title', 'EN', 'Boutique'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Boutique'), 'title', 'FR', 'Boutique'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Boutique'), 'title', 'AR', 'بوتيك'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Bookstore'), 'title', 'EN', 'Bookstore'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Bookstore'), 'title', 'FR', 'Librairie'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Bookstore'), 'title', 'AR', 'مكتبة'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Electronics Store'), 'title', 'EN', 'Electronics Store'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Electronics Store'), 'title', 'FR', 'Magasin d''électronique'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Electronics Store'), 'title', 'AR', 'متجر إلكترونيات'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Grocery Store'), 'title', 'EN', 'Grocery Store'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Grocery Store'), 'title', 'FR', 'Épicerie'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Grocery Store'), 'title', 'AR', 'متجر بقالة')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Government & Public Services
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Post Office'), 'title', 'EN', 'Post Office'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Post Office'), 'title', 'FR', 'Bureau de poste'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Post Office'), 'title', 'AR', 'مكتب بريد'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Library'), 'title', 'EN', 'Library'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Library'), 'title', 'FR', 'Bibliothèque'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Library'), 'title', 'AR', 'مكتبة عامة'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'DMV/Registry'), 'title', 'EN', 'DMV/Registry'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'DMV/Registry'), 'title', 'FR', 'Service des immatriculations'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'DMV/Registry'), 'title', 'AR', 'إدارة المرور/السجل')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Arts & Entertainment
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Art Gallery'), 'title', 'EN', 'Art Gallery'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Art Gallery'), 'title', 'FR', 'Galerie d''art'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Art Gallery'), 'title', 'AR', 'معرض فني'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Museum'), 'title', 'EN', 'Museum'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Museum'), 'title', 'FR', 'Musée'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Museum'), 'title', 'AR', 'متحف'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Cinema'), 'title', 'EN', 'Cinema'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Cinema'), 'title', 'FR', 'Cinéma'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Cinema'), 'title', 'AR', 'سينما'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Theater'), 'title', 'EN', 'Theater'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Theater'), 'title', 'FR', 'Théâtre'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Theater'), 'title', 'AR', 'مسرح')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Sports & Recreation
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Sports Club'), 'title', 'EN', 'Sports Club'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Sports Club'), 'title', 'FR', 'Club de sport'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Sports Club'), 'title', 'AR', 'نادي رياضي'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Yoga Studio'), 'title', 'EN', 'Yoga Studio'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Yoga Studio'), 'title', 'FR', 'Studio de yoga'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Yoga Studio'), 'title', 'AR', 'استوديو يوجا'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Golf Course'), 'title', 'EN', 'Golf Course'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Golf Course'), 'title', 'FR', 'Terrain de golf'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Golf Course'), 'title', 'AR', 'ملعب جولف'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Personal Trainer'), 'title', 'EN', 'Personal Trainer'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Personal Trainer'), 'title', 'FR', 'Entraîneur personnel'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Personal Trainer'), 'title', 'AR', 'مدرب شخصي')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Pet Services
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Veterinarian'), 'title', 'EN', 'Veterinarian'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Veterinarian'), 'title', 'FR', 'Vétérinaire'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Veterinarian'), 'title', 'AR', 'طبيب بيطري'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Pet Groomer'), 'title', 'EN', 'Pet Groomer'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Pet Groomer'), 'title', 'FR', 'Toiletteur pour animaux'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Pet Groomer'), 'title', 'AR', 'مربي حيوانات أليفة'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Dog Walker'), 'title', 'EN', 'Dog Walker'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Dog Walker'), 'title', 'FR', 'Promeneur de chiens'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Dog Walker'), 'title', 'AR', 'ممشى كلاب'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Pet Sitter'), 'title', 'EN', 'Pet Sitter'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Pet Sitter'), 'title', 'FR', 'Gardien d''animaux'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Pet Sitter'), 'title', 'AR', 'جليس حيوانات أليفة')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;

-- Trades & Construction
INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text") VALUES
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Carpenter'), 'title', 'EN', 'Carpenter'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Carpenter'), 'title', 'FR', 'Charpentier'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Carpenter'), 'title', 'AR', 'نجار'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Roofer'), 'title', 'EN', 'Roofer'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Roofer'), 'title', 'FR', 'Couvreur'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Roofer'), 'title', 'AR', 'عامل أسقف'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Welder'), 'title', 'EN', 'Welder'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Welder'), 'title', 'FR', 'Soudeur'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'Welder'), 'title', 'AR', 'لحام'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'General Contractor'), 'title', 'EN', 'General Contractor'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'General Contractor'), 'title', 'FR', 'Entrepreneur général'),
('ProviderCategory', (SELECT id::text FROM "ProviderCategory" WHERE title = 'General Contractor'), 'title', 'AR', 'مقاول عام')
ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO NOTHING;
