-- This migration seeds provider categories and their translations
-- Fixed version with proper error handling and timestamps

-- Step 1: Insert parent categories with proper timestamps and conflict handling
INSERT INTO "ProviderCategory" ("title", "description", "isActive", "sortOrder", "parentId", "createdAt", "updatedAt") VALUES
('Healthcare', 'Medical and health-related services', true, 1, NULL, NOW(), NOW()),
('Legal', 'Legal and law-related services', true, 2, NULL, NOW(), NOW()),
('Financial', 'Financial and accounting services', true, 3, NULL, NOW(), NOW()),
('Automotive', 'Car and vehicle-related services', true, 4, NULL, NOW(), NOW()),
('Home Services', 'Home maintenance and improvement services', true, 5, NULL, NOW(), NOW()),
('Education', 'Educational and training services', true, 6, NULL, NOW(), NOW()),
('Beauty & Wellness', 'Beauty, wellness and fitness services', true, 7, NULL, NOW(), NOW()),
('Professional Services', 'Business and professional services', true, 8, NULL, NOW(), NOW()),
('Food & Dining', 'Food and restaurant services', true, 9, NULL, NOW(), NOW()),
('Retail', 'Retail and shopping services', true, 10, NULL, NOW(), NOW()),
('Government & Public Services', 'Government and public services', true, 11, NULL, NOW(), NOW()),
('Arts & Entertainment', 'Arts, culture and entertainment services', true, 12, NULL, NOW(), NOW()),
('Sports & Recreation', 'Sports and recreational services', true, 13, NULL, NOW(), NOW()),
('Pet Services', 'Pet care and animal services', true, 14, NULL, NOW(), NOW()),
('Trades & Construction', 'Construction and trade services', true, 15, NULL, NOW(), NOW())
ON CONFLICT ("title") DO UPDATE SET 
  "description" = EXCLUDED."description",
  "isActive" = EXCLUDED."isActive", 
  "sortOrder" = EXCLUDED."sortOrder",
  "updatedAt" = NOW();

-- Step 2: Insert child categories using safer approach with DO blocks
DO $$
DECLARE
    healthcare_id INTEGER;
    legal_id INTEGER;
    financial_id INTEGER;
    automotive_id INTEGER;
    home_services_id INTEGER;
    education_id INTEGER;
    beauty_wellness_id INTEGER;
    professional_services_id INTEGER;
    food_dining_id INTEGER;
    retail_id INTEGER;
    government_id INTEGER;
    arts_entertainment_id INTEGER;
    sports_recreation_id INTEGER;
    pet_services_id INTEGER;
    trades_construction_id INTEGER;
BEGIN
    -- Get parent category IDs
    SELECT id INTO healthcare_id FROM "ProviderCategory" WHERE title = 'Healthcare';
    SELECT id INTO legal_id FROM "ProviderCategory" WHERE title = 'Legal';
    SELECT id INTO financial_id FROM "ProviderCategory" WHERE title = 'Financial';
    SELECT id INTO automotive_id FROM "ProviderCategory" WHERE title = 'Automotive';
    SELECT id INTO home_services_id FROM "ProviderCategory" WHERE title = 'Home Services';
    SELECT id INTO education_id FROM "ProviderCategory" WHERE title = 'Education';
    SELECT id INTO beauty_wellness_id FROM "ProviderCategory" WHERE title = 'Beauty & Wellness';
    SELECT id INTO professional_services_id FROM "ProviderCategory" WHERE title = 'Professional Services';
    SELECT id INTO food_dining_id FROM "ProviderCategory" WHERE title = 'Food & Dining';
    SELECT id INTO retail_id FROM "ProviderCategory" WHERE title = 'Retail';
    SELECT id INTO government_id FROM "ProviderCategory" WHERE title = 'Government & Public Services';
    SELECT id INTO arts_entertainment_id FROM "ProviderCategory" WHERE title = 'Arts & Entertainment';
    SELECT id INTO sports_recreation_id FROM "ProviderCategory" WHERE title = 'Sports & Recreation';
    SELECT id INTO pet_services_id FROM "ProviderCategory" WHERE title = 'Pet Services';
    SELECT id INTO trades_construction_id FROM "ProviderCategory" WHERE title = 'Trades & Construction';

    -- Insert Healthcare child categories
    IF healthcare_id IS NOT NULL THEN
        INSERT INTO "ProviderCategory" ("title", "description", "isActive", "sortOrder", "parentId", "createdAt", "updatedAt") VALUES
        ('Doctor', 'General medical practitioners', true, 1, healthcare_id, NOW(), NOW()),
        ('Dentist', 'Dental care professionals', true, 2, healthcare_id, NOW(), NOW()),
        ('Physiotherapist', 'Physical therapy specialists', true, 3, healthcare_id, NOW(), NOW()),
        ('Optometrist', 'Eye care specialists', true, 4, healthcare_id, NOW(), NOW()),
        ('Chiropractor', 'Chiropractic care specialists', true, 5, healthcare_id, NOW(), NOW()),
        ('Pharmacy', 'Pharmaceutical services', true, 6, healthcare_id, NOW(), NOW())
        ON CONFLICT ("title") DO UPDATE SET 
          "description" = EXCLUDED."description",
          "parentId" = EXCLUDED."parentId",
          "sortOrder" = EXCLUDED."sortOrder",
          "updatedAt" = NOW();
    END IF;

    -- Insert Legal child categories
    IF legal_id IS NOT NULL THEN
        INSERT INTO "ProviderCategory" ("title", "description", "isActive", "sortOrder", "parentId", "createdAt", "updatedAt") VALUES
        ('Lawyer', 'Legal representation services', true, 1, legal_id, NOW(), NOW()),
        ('Notary', 'Notarial services', true, 2, legal_id, NOW(), NOW()),
        ('Paralegal', 'Legal assistance services', true, 3, legal_id, NOW(), NOW()),
        ('Mediator', 'Mediation and conflict resolution', true, 4, legal_id, NOW(), NOW())
        ON CONFLICT ("title") DO UPDATE SET 
          "description" = EXCLUDED."description",
          "parentId" = EXCLUDED."parentId",
          "sortOrder" = EXCLUDED."sortOrder",
          "updatedAt" = NOW();
    END IF;

    -- Insert Financial child categories
    IF financial_id IS NOT NULL THEN
        INSERT INTO "ProviderCategory" ("title", "description", "isActive", "sortOrder", "parentId", "createdAt", "updatedAt") VALUES
        ('Accountant', 'Accounting and bookkeeping services', true, 1, financial_id, NOW(), NOW()),
        ('Financial Advisor', 'Financial planning and advisory services', true, 2, financial_id, NOW(), NOW()),
        ('Insurance Agent', 'Insurance services and consultation', true, 3, financial_id, NOW(), NOW()),
        ('Bookkeeper', 'Bookkeeping and record management', true, 4, financial_id, NOW(), NOW())
        ON CONFLICT ("title") DO UPDATE SET 
          "description" = EXCLUDED."description",
          "parentId" = EXCLUDED."parentId",
          "sortOrder" = EXCLUDED."sortOrder",
          "updatedAt" = NOW();
    END IF;

    -- Insert Automotive child categories
    IF automotive_id IS NOT NULL THEN
        INSERT INTO "ProviderCategory" ("title", "description", "isActive", "sortOrder", "parentId", "createdAt", "updatedAt") VALUES
        ('Mechanic', 'Vehicle repair and maintenance', true, 1, automotive_id, NOW(), NOW()),
        ('Car Wash', 'Vehicle cleaning services', true, 2, automotive_id, NOW(), NOW()),
        ('Tire Shop', 'Tire sales and installation', true, 3, automotive_id, NOW(), NOW()),
        ('Body Shop', 'Vehicle body repair and painting', true, 4, automotive_id, NOW(), NOW())
        ON CONFLICT ("title") DO UPDATE SET 
          "description" = EXCLUDED."description",
          "parentId" = EXCLUDED."parentId",
          "sortOrder" = EXCLUDED."sortOrder",
          "updatedAt" = NOW();
    END IF;

    -- Insert Home Services child categories
    IF home_services_id IS NOT NULL THEN
        INSERT INTO "ProviderCategory" ("title", "description", "isActive", "sortOrder", "parentId", "createdAt", "updatedAt") VALUES
        ('Plumber', 'Plumbing services and repairs', true, 1, home_services_id, NOW(), NOW()),
        ('Electrician', 'Electrical services and repairs', true, 2, home_services_id, NOW(), NOW()),
        ('Landscaper', 'Landscaping and garden services', true, 3, home_services_id, NOW(), NOW()),
        ('HVAC Technician', 'Heating, ventilation, and air conditioning', true, 4, home_services_id, NOW(), NOW()),
        ('Painter', 'Painting and decorating services', true, 5, home_services_id, NOW(), NOW()),
        ('Cleaning Service', 'House and office cleaning', true, 6, home_services_id, NOW(), NOW())
        ON CONFLICT ("title") DO UPDATE SET 
          "description" = EXCLUDED."description",
          "parentId" = EXCLUDED."parentId",
          "sortOrder" = EXCLUDED."sortOrder",
          "updatedAt" = NOW();
    END IF;

    -- Insert Education child categories
    IF education_id IS NOT NULL THEN
        INSERT INTO "ProviderCategory" ("title", "description", "isActive", "sortOrder", "parentId", "createdAt", "updatedAt") VALUES
        ('Tutor', 'Private tutoring services', true, 1, education_id, NOW(), NOW()),
        ('Music Teacher', 'Music lessons and instruction', true, 2, education_id, NOW(), NOW()),
        ('Driving School', 'Driving lessons and instruction', true, 3, education_id, NOW(), NOW()),
        ('Language School', 'Language learning and instruction', true, 4, education_id, NOW(), NOW())
        ON CONFLICT ("title") DO UPDATE SET 
          "description" = EXCLUDED."description",
          "parentId" = EXCLUDED."parentId",
          "sortOrder" = EXCLUDED."sortOrder",
          "updatedAt" = NOW();
    END IF;

    -- Insert Beauty & Wellness child categories
    IF beauty_wellness_id IS NOT NULL THEN
        INSERT INTO "ProviderCategory" ("title", "description", "isActive", "sortOrder", "parentId", "createdAt", "updatedAt") VALUES
        ('Hair Salon', 'Hair styling and treatment', true, 1, beauty_wellness_id, NOW(), NOW()),
        ('Barber Shop', 'Men''s grooming and haircuts', true, 2, beauty_wellness_id, NOW(), NOW()),
        ('Nail Salon', 'Nail care and manicure services', true, 3, beauty_wellness_id, NOW(), NOW()),
        ('Spa', 'Spa and relaxation services', true, 4, beauty_wellness_id, NOW(), NOW()),
        ('Massage Therapist', 'Therapeutic massage services', true, 5, beauty_wellness_id, NOW(), NOW()),
        ('Gym', 'Fitness and exercise facilities', true, 6, beauty_wellness_id, NOW(), NOW())
        ON CONFLICT ("title") DO UPDATE SET
          "description" = EXCLUDED."description",
          "parentId" = EXCLUDED."parentId",
          "sortOrder" = EXCLUDED."sortOrder",
          "updatedAt" = NOW();
    END IF;

    -- Insert Professional Services child categories
    IF professional_services_id IS NOT NULL THEN
        INSERT INTO "ProviderCategory" ("title", "description", "isActive", "sortOrder", "parentId", "createdAt", "updatedAt") VALUES
        ('Consultant', 'Business consulting services', true, 1, professional_services_id, NOW(), NOW()),
        ('Marketing Agency', 'Marketing and advertising services', true, 2, professional_services_id, NOW(), NOW()),
        ('Web Developer', 'Website development and design', true, 3, professional_services_id, NOW(), NOW()),
        ('Graphic Designer', 'Graphic design and visual services', true, 4, professional_services_id, NOW(), NOW()),
        ('Photographer', 'Photography services', true, 5, professional_services_id, NOW(), NOW())
        ON CONFLICT ("title") DO UPDATE SET
          "description" = EXCLUDED."description",
          "parentId" = EXCLUDED."parentId",
          "sortOrder" = EXCLUDED."sortOrder",
          "updatedAt" = NOW();
    END IF;

    -- Insert Food & Dining child categories
    IF food_dining_id IS NOT NULL THEN
        INSERT INTO "ProviderCategory" ("title", "description", "isActive", "sortOrder", "parentId", "createdAt", "updatedAt") VALUES
        ('Restaurant', 'Dining and restaurant services', true, 1, food_dining_id, NOW(), NOW()),
        ('Cafe', 'Coffee shops and cafes', true, 2, food_dining_id, NOW(), NOW()),
        ('Bakery', 'Bakery and pastry services', true, 3, food_dining_id, NOW(), NOW()),
        ('Catering', 'Catering and event food services', true, 4, food_dining_id, NOW(), NOW())
        ON CONFLICT ("title") DO UPDATE SET
          "description" = EXCLUDED."description",
          "parentId" = EXCLUDED."parentId",
          "sortOrder" = EXCLUDED."sortOrder",
          "updatedAt" = NOW();
    END IF;

    -- Insert Retail child categories
    IF retail_id IS NOT NULL THEN
        INSERT INTO "ProviderCategory" ("title", "description", "isActive", "sortOrder", "parentId", "createdAt", "updatedAt") VALUES
        ('Boutique', 'Fashion and clothing retail', true, 1, retail_id, NOW(), NOW()),
        ('Bookstore', 'Books and literature retail', true, 2, retail_id, NOW(), NOW()),
        ('Electronics Store', 'Electronics and technology retail', true, 3, retail_id, NOW(), NOW()),
        ('Grocery Store', 'Food and grocery retail', true, 4, retail_id, NOW(), NOW())
        ON CONFLICT ("title") DO UPDATE SET
          "description" = EXCLUDED."description",
          "parentId" = EXCLUDED."parentId",
          "sortOrder" = EXCLUDED."sortOrder",
          "updatedAt" = NOW();
    END IF;

    -- Insert Government & Public Services child categories
    IF government_id IS NOT NULL THEN
        INSERT INTO "ProviderCategory" ("title", "description", "isActive", "sortOrder", "parentId", "createdAt", "updatedAt") VALUES
        ('Post Office', 'Postal and mailing services', true, 1, government_id, NOW(), NOW()),
        ('Library', 'Public library services', true, 2, government_id, NOW(), NOW()),
        ('DMV/Registry', 'Vehicle registration and licensing', true, 3, government_id, NOW(), NOW())
        ON CONFLICT ("title") DO UPDATE SET
          "description" = EXCLUDED."description",
          "parentId" = EXCLUDED."parentId",
          "sortOrder" = EXCLUDED."sortOrder",
          "updatedAt" = NOW();
    END IF;

    -- Insert Arts & Entertainment child categories
    IF arts_entertainment_id IS NOT NULL THEN
        INSERT INTO "ProviderCategory" ("title", "description", "isActive", "sortOrder", "parentId", "createdAt", "updatedAt") VALUES
        ('Art Gallery', 'Art exhibitions and galleries', true, 1, arts_entertainment_id, NOW(), NOW()),
        ('Museum', 'Museums and cultural institutions', true, 2, arts_entertainment_id, NOW(), NOW()),
        ('Cinema', 'Movie theaters and cinemas', true, 3, arts_entertainment_id, NOW(), NOW()),
        ('Theater', 'Live theater and performances', true, 4, arts_entertainment_id, NOW(), NOW())
        ON CONFLICT ("title") DO UPDATE SET
          "description" = EXCLUDED."description",
          "parentId" = EXCLUDED."parentId",
          "sortOrder" = EXCLUDED."sortOrder",
          "updatedAt" = NOW();
    END IF;

    -- Insert Sports & Recreation child categories
    IF sports_recreation_id IS NOT NULL THEN
        INSERT INTO "ProviderCategory" ("title", "description", "isActive", "sortOrder", "parentId", "createdAt", "updatedAt") VALUES
        ('Sports Club', 'Sports clubs and facilities', true, 1, sports_recreation_id, NOW(), NOW()),
        ('Yoga Studio', 'Yoga classes and instruction', true, 2, sports_recreation_id, NOW(), NOW()),
        ('Golf Course', 'Golf courses and instruction', true, 3, sports_recreation_id, NOW(), NOW()),
        ('Personal Trainer', 'Personal fitness training', true, 4, sports_recreation_id, NOW(), NOW())
        ON CONFLICT ("title") DO UPDATE SET
          "description" = EXCLUDED."description",
          "parentId" = EXCLUDED."parentId",
          "sortOrder" = EXCLUDED."sortOrder",
          "updatedAt" = NOW();
    END IF;

    -- Insert Pet Services child categories
    IF pet_services_id IS NOT NULL THEN
        INSERT INTO "ProviderCategory" ("title", "description", "isActive", "sortOrder", "parentId", "createdAt", "updatedAt") VALUES
        ('Veterinarian', 'Veterinary care and services', true, 1, pet_services_id, NOW(), NOW()),
        ('Pet Groomer', 'Pet grooming and care', true, 2, pet_services_id, NOW(), NOW()),
        ('Dog Walker', 'Dog walking and exercise services', true, 3, pet_services_id, NOW(), NOW()),
        ('Pet Sitter', 'Pet sitting and care services', true, 4, pet_services_id, NOW(), NOW())
        ON CONFLICT ("title") DO UPDATE SET
          "description" = EXCLUDED."description",
          "parentId" = EXCLUDED."parentId",
          "sortOrder" = EXCLUDED."sortOrder",
          "updatedAt" = NOW();
    END IF;

    -- Insert Trades & Construction child categories
    IF trades_construction_id IS NOT NULL THEN
        INSERT INTO "ProviderCategory" ("title", "description", "isActive", "sortOrder", "parentId", "createdAt", "updatedAt") VALUES
        ('Carpenter', 'Carpentry and woodworking services', true, 1, trades_construction_id, NOW(), NOW()),
        ('Roofer', 'Roofing services and repairs', true, 2, trades_construction_id, NOW(), NOW()),
        ('Welder', 'Welding and metalwork services', true, 3, trades_construction_id, NOW(), NOW()),
        ('General Contractor', 'General construction services', true, 4, trades_construction_id, NOW(), NOW())
        ON CONFLICT ("title") DO UPDATE SET
          "description" = EXCLUDED."description",
          "parentId" = EXCLUDED."parentId",
          "sortOrder" = EXCLUDED."sortOrder",
          "updatedAt" = NOW();
    END IF;

END $$;

-- Step 3: Insert translations for all categories
-- This uses a safer approach with proper error handling
DO $$
DECLARE
    category_record RECORD;
BEGIN
    -- Insert translations for all categories
    FOR category_record IN
        SELECT id, title FROM "ProviderCategory"
    LOOP
        -- Insert English translations (base language)
        INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text", "createdAt", "updatedAt")
        VALUES ('ProviderCategory', category_record.id::text, 'title', 'EN', category_record.title, NOW(), NOW())
        ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO UPDATE SET
          "text" = EXCLUDED."text",
          "updatedAt" = NOW();

        -- Insert French translations
        INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text", "createdAt", "updatedAt")
        VALUES ('ProviderCategory', category_record.id::text, 'title', 'FR',
            CASE category_record.title
                WHEN 'Healthcare' THEN 'Soins de santé'
                WHEN 'Legal' THEN 'Juridique'
                WHEN 'Financial' THEN 'Financier'
                WHEN 'Automotive' THEN 'Automobile'
                WHEN 'Home Services' THEN 'Services à domicile'
                WHEN 'Education' THEN 'Éducation'
                WHEN 'Beauty & Wellness' THEN 'Beauté et Bien-être'
                WHEN 'Professional Services' THEN 'Services professionnels'
                WHEN 'Food & Dining' THEN 'Restauration et Alimentation'
                WHEN 'Retail' THEN 'Vente au détail'
                WHEN 'Government & Public Services' THEN 'Services gouvernementaux et publics'
                WHEN 'Arts & Entertainment' THEN 'Arts et Divertissements'
                WHEN 'Sports & Recreation' THEN 'Sports et Loisirs'
                WHEN 'Pet Services' THEN 'Services pour animaux de compagnie'
                WHEN 'Trades & Construction' THEN 'Métiers et Construction'
                WHEN 'Doctor' THEN 'Médecin'
                WHEN 'Dentist' THEN 'Dentiste'
                WHEN 'Physiotherapist' THEN 'Kinésithérapeute'
                WHEN 'Optometrist' THEN 'Optométriste'
                WHEN 'Chiropractor' THEN 'Chiropracteur'
                WHEN 'Pharmacy' THEN 'Pharmacie'
                WHEN 'Lawyer' THEN 'Avocat'
                WHEN 'Notary' THEN 'Notaire'
                WHEN 'Paralegal' THEN 'Technicien juridique'
                WHEN 'Mediator' THEN 'Médiateur'
                WHEN 'Accountant' THEN 'Comptable'
                WHEN 'Financial Advisor' THEN 'Conseiller financier'
                WHEN 'Insurance Agent' THEN 'Agent d''assurance'
                WHEN 'Bookkeeper' THEN 'Aide-comptable'
                WHEN 'Mechanic' THEN 'Mécanicien'
                WHEN 'Car Wash' THEN 'Lavage auto'
                WHEN 'Tire Shop' THEN 'Magasin de pneus'
                WHEN 'Body Shop' THEN 'Carrosserie'
                WHEN 'Plumber' THEN 'Plombier'
                WHEN 'Electrician' THEN 'Électricien'
                WHEN 'Landscaper' THEN 'Paysagiste'
                WHEN 'HVAC Technician' THEN 'Technicien CVC'
                WHEN 'Painter' THEN 'Peintre'
                WHEN 'Cleaning Service' THEN 'Service de nettoyage'
                WHEN 'Tutor' THEN 'Tuteur'
                WHEN 'Music Teacher' THEN 'Professeur de musique'
                WHEN 'Driving School' THEN 'Auto-école'
                WHEN 'Language School' THEN 'École de langues'
                WHEN 'Hair Salon' THEN 'Salon de coiffure'
                WHEN 'Barber Shop' THEN 'Barbier'
                WHEN 'Nail Salon' THEN 'Salon de manucure'
                WHEN 'Spa' THEN 'Spa'
                WHEN 'Massage Therapist' THEN 'Massothérapeute'
                WHEN 'Gym' THEN 'Salle de sport'
                WHEN 'Consultant' THEN 'Consultant'
                WHEN 'Marketing Agency' THEN 'Agence de marketing'
                WHEN 'Web Developer' THEN 'Développeur web'
                WHEN 'Graphic Designer' THEN 'Graphiste'
                WHEN 'Photographer' THEN 'Photographe'
                WHEN 'Restaurant' THEN 'Restaurant'
                WHEN 'Cafe' THEN 'Café'
                WHEN 'Bakery' THEN 'Boulangerie'
                WHEN 'Catering' THEN 'Traiteur'
                WHEN 'Boutique' THEN 'Boutique'
                WHEN 'Bookstore' THEN 'Librairie'
                WHEN 'Electronics Store' THEN 'Magasin d''électronique'
                WHEN 'Grocery Store' THEN 'Épicerie'
                WHEN 'Post Office' THEN 'Bureau de poste'
                WHEN 'Library' THEN 'Bibliothèque'
                WHEN 'DMV/Registry' THEN 'Service des immatriculations'
                WHEN 'Art Gallery' THEN 'Galerie d''art'
                WHEN 'Museum' THEN 'Musée'
                WHEN 'Cinema' THEN 'Cinéma'
                WHEN 'Theater' THEN 'Théâtre'
                WHEN 'Sports Club' THEN 'Club de sport'
                WHEN 'Yoga Studio' THEN 'Studio de yoga'
                WHEN 'Golf Course' THEN 'Terrain de golf'
                WHEN 'Personal Trainer' THEN 'Entraîneur personnel'
                WHEN 'Veterinarian' THEN 'Vétérinaire'
                WHEN 'Pet Groomer' THEN 'Toiletteur pour animaux'
                WHEN 'Dog Walker' THEN 'Promeneur de chiens'
                WHEN 'Pet Sitter' THEN 'Gardien d''animaux'
                WHEN 'Carpenter' THEN 'Charpentier'
                WHEN 'Roofer' THEN 'Couvreur'
                WHEN 'Welder' THEN 'Soudeur'
                WHEN 'General Contractor' THEN 'Entrepreneur général'
                ELSE category_record.title || ' (FR)'
            END, NOW(), NOW())
        ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO UPDATE SET
          "text" = EXCLUDED."text",
          "updatedAt" = NOW();

        -- Insert Arabic translations
        INSERT INTO "Translation" ("tableName", "refId", "refField", "languageCode", "text", "createdAt", "updatedAt")
        VALUES ('ProviderCategory', category_record.id::text, 'title', 'AR',
            CASE category_record.title
                WHEN 'Healthcare' THEN 'الرعاية الصحية'
                WHEN 'Legal' THEN 'قانوني'
                WHEN 'Financial' THEN 'مالي'
                WHEN 'Automotive' THEN 'سيارات'
                WHEN 'Home Services' THEN 'خدمات منزلية'
                WHEN 'Education' THEN 'تعليم'
                WHEN 'Beauty & Wellness' THEN 'الجمال والعافية'
                WHEN 'Professional Services' THEN 'خدمات احترافية'
                WHEN 'Food & Dining' THEN 'الأطعمة والمطاعم'
                WHEN 'Retail' THEN 'تجارة التجزئة'
                WHEN 'Government & Public Services' THEN 'الخدمات الحكومية والعامة'
                WHEN 'Arts & Entertainment' THEN 'الفنون والترفيه'
                WHEN 'Sports & Recreation' THEN 'الرياضة والترفيه'
                WHEN 'Pet Services' THEN 'خدمات الحيوانات الأليفة'
                WHEN 'Trades & Construction' THEN 'الحرف والبناء'
                WHEN 'Doctor' THEN 'طبيب'
                WHEN 'Dentist' THEN 'طبيب أسنان'
                WHEN 'Physiotherapist' THEN 'أخصائي علاج طبيعي'
                WHEN 'Optometrist' THEN 'أخصائي بصريات'
                WHEN 'Chiropractor' THEN 'مقوم العظام'
                WHEN 'Pharmacy' THEN 'صيدلية'
                WHEN 'Lawyer' THEN 'محامي'
                WHEN 'Notary' THEN 'كاتب عدل'
                WHEN 'Paralegal' THEN 'مساعد قانوني'
                WHEN 'Mediator' THEN 'وسيط'
                WHEN 'Accountant' THEN 'محاسب'
                WHEN 'Financial Advisor' THEN 'مستشار مالي'
                WHEN 'Insurance Agent' THEN 'وكيل تأمين'
                WHEN 'Bookkeeper' THEN 'مسك الدفاتر'
                WHEN 'Mechanic' THEN 'ميكانيكي'
                WHEN 'Car Wash' THEN 'غسيل سيارات'
                WHEN 'Tire Shop' THEN 'متجر إطارات'
                WHEN 'Body Shop' THEN 'ورشة هياكل سيارات'
                WHEN 'Plumber' THEN 'سباك'
                WHEN 'Electrician' THEN 'كهربائي'
                WHEN 'Landscaper' THEN 'منسق حدائق'
                WHEN 'HVAC Technician' THEN 'فني تدفئة وتهوية وتكييف'
                WHEN 'Painter' THEN 'دهان'
                WHEN 'Cleaning Service' THEN 'خدمة تنظيف'
                WHEN 'Tutor' THEN 'مدرس خصوصي'
                WHEN 'Music Teacher' THEN 'معلم موسيقى'
                WHEN 'Driving School' THEN 'مدرسة تعليم قيادة'
                WHEN 'Language School' THEN 'مدرسة لغات'
                WHEN 'Hair Salon' THEN 'صالون تجميل'
                WHEN 'Barber Shop' THEN 'محل حلاقة'
                WHEN 'Nail Salon' THEN 'صالون أظافر'
                WHEN 'Spa' THEN 'منتجع صحي'
                WHEN 'Massage Therapist' THEN 'معالج بالتدليك'
                WHEN 'Gym' THEN 'صالة رياضية'
                WHEN 'Consultant' THEN 'مستشار'
                WHEN 'Marketing Agency' THEN 'وكالة تسويق'
                WHEN 'Web Developer' THEN 'مطور ويب'
                WHEN 'Graphic Designer' THEN 'مصمم جرافيك'
                WHEN 'Photographer' THEN 'مصور فوتوغرافي'
                WHEN 'Restaurant' THEN 'مطعم'
                WHEN 'Cafe' THEN 'مقهى'
                WHEN 'Bakery' THEN 'مخبز'
                WHEN 'Catering' THEN 'خدمات تموين'
                WHEN 'Boutique' THEN 'بوتيك'
                WHEN 'Bookstore' THEN 'مكتبة'
                WHEN 'Electronics Store' THEN 'متجر إلكترونيات'
                WHEN 'Grocery Store' THEN 'متجر بقالة'
                WHEN 'Post Office' THEN 'مكتب بريد'
                WHEN 'Library' THEN 'مكتبة عامة'
                WHEN 'DMV/Registry' THEN 'إدارة المرور/السجل'
                WHEN 'Art Gallery' THEN 'معرض فني'
                WHEN 'Museum' THEN 'متحف'
                WHEN 'Cinema' THEN 'سينما'
                WHEN 'Theater' THEN 'مسرح'
                WHEN 'Sports Club' THEN 'نادي رياضي'
                WHEN 'Yoga Studio' THEN 'استوديو يوجا'
                WHEN 'Golf Course' THEN 'ملعب جولف'
                WHEN 'Personal Trainer' THEN 'مدرب شخصي'
                WHEN 'Veterinarian' THEN 'طبيب بيطري'
                WHEN 'Pet Groomer' THEN 'مربي حيوانات أليفة'
                WHEN 'Dog Walker' THEN 'ممشى كلاب'
                WHEN 'Pet Sitter' THEN 'جليس حيوانات أليفة'
                WHEN 'Carpenter' THEN 'نجار'
                WHEN 'Roofer' THEN 'عامل أسقف'
                WHEN 'Welder' THEN 'لحام'
                WHEN 'General Contractor' THEN 'مقاول عام'
                ELSE category_record.title || ' (AR)'
            END, NOW(), NOW())
        ON CONFLICT ("tableName", "refId", "refField", "languageCode") DO UPDATE SET
          "text" = EXCLUDED."text",
          "updatedAt" = NOW();
    END LOOP;
END $$;
