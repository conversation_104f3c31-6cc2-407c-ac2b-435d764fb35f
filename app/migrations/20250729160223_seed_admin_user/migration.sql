-- Seed default admin user
-- This migration creates the default admin user for production deployment

DO $$
DECLARE
    admin_user_id TEXT;
    admin_auth_id TEXT;
    admin_email TEXT := '<EMAIL>';
    admin_password_hash TEXT := '$2b$10$K7L/8Y3NTCQ02.fmUK.OaO4kya2RkFLHPqBOcuQjk0Cnr.L4f/1u.'; -- bcrypt hash for 'Dadanasro07'
BEGIN
    -- Check if admin user already exists
    IF EXISTS (SELECT 1 FROM "User" WHERE email = admin_email) THEN
        RAISE NOTICE 'Admin user already exists, skipping creation';
        RETURN;
    END IF;

    -- Generate UUIDs for the admin user and auth record
    admin_user_id := gen_random_uuid()::TEXT;
    admin_auth_id := gen_random_uuid()::TEXT;

    -- Insert the admin user
    INSERT INTO "User" (
        "id",
        "createdAt",
        "email",
        "username",
        "firstName",
        "lastName",
        "isAdmin",
        "role",
        "subscriptionStatus",
        "subscriptionPlan",
        "datePaid",
        "credits",
        "queues",
        "preferedLanguage",
        "isEmailVerified"
    ) VALUES (
        admin_user_id,
        NOW(),
        admin_email,
        'admin',
        'Admin',
        'User',
        true,
        'ADMIN',
        'active',
        'free',
        NOW(),
        100,
        10,
        'EN',
        true
    );

    -- Insert the Auth record
    INSERT INTO "Auth" (
        "id",
        "userId"
    ) VALUES (
        admin_auth_id,
        admin_user_id
    );

    -- Insert the AuthIdentity record for email authentication
    INSERT INTO "AuthIdentity" (
        "providerName",
        "providerUserId",
        "providerData",
        "authId"
    ) VALUES (
        'email',
        admin_email,
        jsonb_build_object(
            'hashedPassword', admin_password_hash,
            'isEmailVerified', true,
            'emailVerificationSentAt', null,
            'passwordResetSentAt', null
        )::TEXT,
        admin_auth_id
    );

    RAISE NOTICE 'Admin user created successfully with email: %', admin_email;
    RAISE NOTICE 'Admin credentials: % / Dadanasro07', admin_email;

EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Error creating admin user: %', SQLERRM;
        -- Don't re-raise the error to prevent migration failure
END $$;
