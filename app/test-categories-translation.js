/**
 * Test script for Provider Categories Translation API
 * Tests the translation functionality for category retrieval with language parameters
 */

const axios = require('axios');

// Configuration
const BASE_URL = 'https://dalti-test.adscloud.org:8443';
const API_BASE = `${BASE_URL}/api`;

// Helper function to make public requests (no authentication needed)
async function makePublicRequest(method, endpoint, data = null) {
  const config = {
    method,
    url: `${API_BASE}${endpoint}`,
    headers: {
      'Content-Type': 'application/json'
    }
  };

  if (data) {
    config.data = data;
  }

  try {
    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`Error in ${method} ${endpoint}:`, error.response?.data || error.message);
    throw error;
  }
}

// Test functions
async function testCategoryTranslation() {
  console.log('\n🧪 Testing Provider Categories Translation API...\n');

  try {
    // Test 1: Get categories without language parameter (should default to English)
    console.log('📋 Test 1: Getting categories without language parameter (default)...');
    const defaultResponse = await makePublicRequest('GET', '/provider-categories');
    console.log('✅ Default categories retrieved successfully');
    console.log('🔄 Number of categories:', defaultResponse.length);
    
    if (defaultResponse.length > 0) {
      console.log('🔄 Sample category (default):', {
        id: defaultResponse[0].id,
        title: defaultResponse[0].title,
        description: defaultResponse[0].description,
        isActive: defaultResponse[0].isActive
      });
    }

    // Test 2: Get categories with English language parameter
    console.log('\n🇺🇸 Test 2: Getting categories with English language parameter...');
    const englishResponse = await makePublicRequest('GET', '/provider-categories?lang=en');
    console.log('✅ English categories retrieved successfully');
    console.log('🔄 Number of English categories:', englishResponse.length);
    
    if (englishResponse.length > 0) {
      console.log('🔄 Sample English category:', {
        id: englishResponse[0].id,
        title: englishResponse[0].title,
        description: englishResponse[0].description,
        isActive: englishResponse[0].isActive
      });
    }

    // Test 3: Get categories with Arabic language parameter
    console.log('\n🇸🇦 Test 3: Getting categories with Arabic language parameter...');
    const arabicResponse = await makePublicRequest('GET', '/provider-categories?lang=ar');
    console.log('✅ Arabic categories retrieved successfully');
    console.log('🔄 Number of Arabic categories:', arabicResponse.length);
    
    if (arabicResponse.length > 0) {
      console.log('🔄 Sample Arabic category:', {
        id: arabicResponse[0].id,
        title: arabicResponse[0].title,
        description: arabicResponse[0].description,
        isActive: arabicResponse[0].isActive
      });
    }

    // Test 4: Get categories with French language parameter
    console.log('\n🇫🇷 Test 4: Getting categories with French language parameter...');
    const frenchResponse = await makePublicRequest('GET', '/provider-categories?lang=fr');
    console.log('✅ French categories retrieved successfully');
    console.log('🔄 Number of French categories:', frenchResponse.length);
    
    if (frenchResponse.length > 0) {
      console.log('🔄 Sample French category:', {
        id: frenchResponse[0].id,
        title: frenchResponse[0].title,
        description: frenchResponse[0].description,
        isActive: frenchResponse[0].isActive
      });
    }

    // Test 5: Get categories with invalid language parameter (should fallback to English)
    console.log('\n❓ Test 5: Getting categories with invalid language parameter...');
    const invalidLangResponse = await makePublicRequest('GET', '/provider-categories?lang=invalid');
    console.log('✅ Categories with invalid language retrieved successfully (fallback to English)');
    console.log('🔄 Number of fallback categories:', invalidLangResponse.length);
    
    if (invalidLangResponse.length > 0) {
      console.log('🔄 Sample fallback category:', {
        id: invalidLangResponse[0].id,
        title: invalidLangResponse[0].title,
        description: invalidLangResponse[0].description,
        isActive: invalidLangResponse[0].isActive
      });
    }

    // Test 6: Compare responses to verify translation differences
    console.log('\n🔍 Test 6: Comparing translation differences...');
    
    if (defaultResponse.length > 0 && arabicResponse.length > 0) {
      const defaultCategory = defaultResponse[0];
      const arabicCategory = arabicResponse[0];
      
      console.log('🔄 Translation comparison for first category:');
      console.log(`   Default title: "${defaultCategory.title}"`);
      console.log(`   Arabic title: "${arabicCategory.title}"`);
      console.log(`   Default description: "${defaultCategory.description}"`);
      console.log(`   Arabic description: "${arabicCategory.description}"`);
      
      const titlesDifferent = defaultCategory.title !== arabicCategory.title;
      const descriptionsDifferent = defaultCategory.description !== arabicCategory.description;
      
      console.log(`   Titles different: ${titlesDifferent ? '✅' : '❌'}`);
      console.log(`   Descriptions different: ${descriptionsDifferent ? '✅' : '❌'}`);
    }

    // Test 7: Test case sensitivity of language parameter
    console.log('\n🔤 Test 7: Testing case sensitivity of language parameter...');
    const upperCaseResponse = await makePublicRequest('GET', '/provider-categories?lang=AR');
    console.log('✅ Uppercase language parameter handled successfully');
    console.log('🔄 Number of uppercase AR categories:', upperCaseResponse.length);

    const lowerCaseResponse = await makePublicRequest('GET', '/provider-categories?lang=ar');
    console.log('✅ Lowercase language parameter handled successfully');
    console.log('🔄 Number of lowercase ar categories:', lowerCaseResponse.length);

    console.log('\n🎉 All translation tests completed successfully!');
    console.log('\n📊 Test Summary:');
    console.log('- ✅ Default language retrieval (no parameter)');
    console.log('- ✅ English language retrieval (lang=en)');
    console.log('- ✅ Arabic language retrieval (lang=ar)');
    console.log('- ✅ French language retrieval (lang=fr)');
    console.log('- ✅ Invalid language fallback (lang=invalid)');
    console.log('- ✅ Translation difference verification');
    console.log('- ✅ Case sensitivity handling (AR vs ar)');

    return {
      defaultCount: defaultResponse.length,
      englishCount: englishResponse.length,
      arabicCount: arabicResponse.length,
      frenchCount: frenchResponse.length,
      invalidCount: invalidLangResponse.length,
      upperCaseCount: upperCaseResponse.length,
      lowerCaseCount: lowerCaseResponse.length
    };

  } catch (error) {
    console.error('❌ Translation test failed:', error.message);
    throw error;
  }
}

// Main execution
async function main() {
  console.log('🚀 Starting Provider Categories Translation API Tests');
  console.log('🌐 Base URL:', BASE_URL);
  console.log('📡 Testing public API endpoint (no authentication required)');
  
  try {
    const results = await testCategoryTranslation();
    console.log('\n✅ All tests passed successfully!');
    console.log('\n📈 Results Summary:', results);
  } catch (error) {
    console.error('\n❌ Tests failed:', error.message);
    process.exit(1);
  }
}

// Run the tests
if (require.main === module) {
  main();
}

module.exports = { testCategoryTranslation };
