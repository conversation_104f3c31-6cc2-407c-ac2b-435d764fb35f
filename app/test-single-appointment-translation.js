/**
 * Test script for Single Provider Appointment Translation API
 * Tests the translation functionality for single appointment retrieval
 */

const axios = require('axios');

// Configuration
const BASE_URL = 'https://dalti-test.adscloud.org:8443';
const API_BASE = `${BASE_URL}/api/auth/providers`;

// Helper function to make authenticated requests
async function makeAuthenticatedRequest(method, endpoint, data = null, token) {
  const config = {
    method,
    url: `${API_BASE}${endpoint}`,
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  };

  if (data) {
    config.data = data;
  }

  try {
    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`Error in ${method} ${endpoint}:`, error.response?.data || error.message);
    throw error;
  }
}

// Test functions
async function testSingleAppointmentTranslation(authToken) {
  console.log('\n🧪 Testing Single Provider Appointment Translation API...\n');

  try {
    // Test 1: Get all appointments to find an existing appointment ID
    console.log('📋 Test 1: Getting all appointments to find existing appointment...');
    const allAppointmentsResponse = await makeAuthenticatedRequest('GET', '/appointments', null, authToken);
    console.log('✅ All appointments retrieved successfully');
    console.log('🔄 Number of appointments:', allAppointmentsResponse.data.data.length);
    
    if (allAppointmentsResponse.data.data.length === 0) {
      console.log('⚠️ No appointments found. Please create an appointment first to test single appointment retrieval.');
      return null;
    }

    const firstAppointment = allAppointmentsResponse.data.data[0];
    const appointmentId = firstAppointment.id;
    
    console.log('🔄 Using appointment for testing:', {
      id: appointmentId,
      status: firstAppointment.status,
      notes: firstAppointment.notes,
      service: firstAppointment.service.title,
      customer: `${firstAppointment.customer.firstName} ${firstAppointment.customer.lastName}`
    });

    // Test 2: Get single appointment by ID
    console.log(`\n👤 Test 2: Getting single appointment by ID (${appointmentId})...`);
    const singleAppointmentResponse = await makeAuthenticatedRequest('GET', `/appointments/${appointmentId}`, null, authToken);
    console.log('✅ Single appointment retrieved successfully');
    console.log('🔄 Single appointment details:', {
      id: singleAppointmentResponse.data.id,
      status: singleAppointmentResponse.data.status,
      notes: singleAppointmentResponse.data.notes,
      expectedStartTime: singleAppointmentResponse.data.expectedAppointmentStartTime,
      service: {
        id: singleAppointmentResponse.data.service.id,
        title: singleAppointmentResponse.data.service.title,
        description: singleAppointmentResponse.data.service.description,
        duration: singleAppointmentResponse.data.service.duration
      },
      place: {
        id: singleAppointmentResponse.data.place.id,
        name: singleAppointmentResponse.data.place.name
      },
      queue: singleAppointmentResponse.data.queue ? {
        id: singleAppointmentResponse.data.queue.id,
        title: singleAppointmentResponse.data.queue.title
      } : null,
      customer: {
        id: singleAppointmentResponse.data.customer.id,
        firstName: singleAppointmentResponse.data.customer.firstName,
        lastName: singleAppointmentResponse.data.customer.lastName
      }
    });

    // Test 3: Compare single appointment with appointment from list
    console.log('\n🔍 Test 3: Comparing single appointment with appointment from list...');
    
    const listAppointment = firstAppointment;
    const singleAppointment = singleAppointmentResponse.data;
    
    console.log('🔄 Comparison results:');
    console.log(`   ID match: ${listAppointment.id === singleAppointment.id ? '✅' : '❌'}`);
    console.log(`   Status match: ${listAppointment.status === singleAppointment.status ? '✅' : '❌'}`);
    console.log(`   Notes match: ${listAppointment.notes === singleAppointment.notes ? '✅' : '❌'}`);
    console.log(`   Service title match: ${listAppointment.service.title === singleAppointment.service.title ? '✅' : '❌'}`);
    console.log(`   Place name match: ${listAppointment.place.name === singleAppointment.place.name ? '✅' : '❌'}`);
    
    if (listAppointment.queue && singleAppointment.queue) {
      console.log(`   Queue title match: ${listAppointment.queue.title === singleAppointment.queue.title ? '✅' : '❌'}`);
    }

    // Test 4: Test with invalid appointment ID
    console.log('\n❌ Test 4: Testing with invalid appointment ID...');
    try {
      await makeAuthenticatedRequest('GET', '/appointments/99999', null, authToken);
      console.log('❌ Expected error for invalid ID, but request succeeded');
    } catch (error) {
      console.log('✅ Correctly handled invalid appointment ID');
      console.log('🔄 Error response:', error.response?.data?.message || error.message);
    }

    // Test 5: Test with non-numeric appointment ID
    console.log('\n❌ Test 5: Testing with non-numeric appointment ID...');
    try {
      await makeAuthenticatedRequest('GET', '/appointments/invalid', null, authToken);
      console.log('❌ Expected error for non-numeric ID, but request succeeded');
    } catch (error) {
      console.log('✅ Correctly handled non-numeric appointment ID');
      console.log('🔄 Error response:', error.response?.data?.message || error.message);
    }

    // Test 6: Verify translation fields are present
    console.log('\n🌍 Test 6: Verifying translation fields are present...');
    const appointment = singleAppointmentResponse.data;
    
    console.log('🔄 Translation field verification:');
    console.log(`   Appointment notes: ${appointment.notes ? '✅ Present' : '❌ Missing'}`);
    console.log(`   Service title: ${appointment.service.title ? '✅ Present' : '❌ Missing'}`);
    console.log(`   Service description: ${appointment.service.description !== undefined ? '✅ Present' : '❌ Missing'}`);
    console.log(`   Place name: ${appointment.place.name ? '✅ Present' : '❌ Missing'}`);
    
    if (appointment.queue) {
      console.log(`   Queue title: ${appointment.queue.title ? '✅ Present' : '❌ Missing'}`);
    } else {
      console.log('   Queue: ❌ Not assigned to this appointment');
    }

    console.log('\n🎉 All single appointment translation tests completed successfully!');
    console.log('\n📊 Test Summary:');
    console.log('- ✅ All appointments retrieval for ID selection');
    console.log('- ✅ Single appointment retrieval with translations');
    console.log('- ✅ Data consistency between list and single endpoints');
    console.log('- ✅ Invalid appointment ID error handling');
    console.log('- ✅ Non-numeric appointment ID error handling');
    console.log('- ✅ Translation field verification');

    return appointmentId;

  } catch (error) {
    console.error('❌ Single appointment translation test failed:', error.message);
    throw error;
  }
}

// Main execution
async function main() {
  console.log('🚀 Starting Single Provider Appointment Translation API Tests');
  console.log('🌐 Base URL:', BASE_URL);
  
  // Note: You'll need to provide a valid authentication token
  const authToken = process.env.PROVIDER_AUTH_TOKEN;
  
  if (!authToken) {
    console.error('❌ Error: PROVIDER_AUTH_TOKEN environment variable is required');
    console.log('💡 Usage: PROVIDER_AUTH_TOKEN=your_token_here node test-single-appointment-translation.js');
    process.exit(1);
  }

  try {
    await testSingleAppointmentTranslation(authToken);
    console.log('\n✅ All tests passed successfully!');
  } catch (error) {
    console.error('\n❌ Tests failed:', error.message);
    process.exit(1);
  }
}

// Run the tests
if (require.main === module) {
  main();
}

module.exports = { testSingleAppointmentTranslation };
