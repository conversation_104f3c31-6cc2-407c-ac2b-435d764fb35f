{"name": "opensaas", "type": "module", "dependencies": {"@aws-sdk/client-s3": "^3.523.0", "@aws-sdk/s3-presigned-post": "^3.750.0", "@aws-sdk/s3-request-presigner": "^3.523.0", "@chargily/chargily-pay": "^2.1.0", "@faker-js/faker": "8.3.1", "@google-analytics/data": "4.1.0", "@headlessui/react": "1.7.13", "@lemonsqueezy/lemonsqueezy.js": "^3.2.0", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/typography": "^0.5.7", "apexcharts": "3.41.0", "axios": "^1.4.0", "clsx": "^2.1.0", "crypto": "^1.0.1", "dayjs": "^1.11.13", "deepl-node": "^1.18.0", "eld": "^1.0.0", "firebase": "^11.7.3", "firebase-admin": "^13.4.0", "flatpickr": "^4.6.13", "headlessui": "^0.0.0", "node-fetch": "3.3.0", "openai": "^4.55.3", "prettier": "3.1.1", "prettier-plugin-tailwindcss": "0.5.11", "react": "^18.2.0", "react-apexcharts": "1.4.1", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "react-icons": "^4.11.0", "react-router-dom": "^6.26.2", "stripe": "11.15.0", "tailwind-merge": "^2.2.1", "tailwindcss": "^3.2.7", "vanilla-cookieconsent": "^3.0.1", "wasp": "file:.wasp/out/sdk/wasp", "zod": "^3.23.8"}, "devDependencies": {"@types/express": "^4.17.13", "@types/react": "^18.0.37", "prisma": "5.19.1", "typescript": "^5.1.0", "vite": "^4.3.9"}}