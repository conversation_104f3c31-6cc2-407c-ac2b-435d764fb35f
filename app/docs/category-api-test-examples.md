# Provider Category API Test Examples

This document provides test examples for the expanded Provider Category API, demonstrating the flexible input validation.

## Update Category API Test

### ✅ Working Example (Fixed)

**Request**: `PUT /api/auth/admin/provider-categories/22`

**Payload** (with string inputs):
```json
{
    "title": "Lawyer",
    "description": "testdqs dqs qsqsd",
    "parentId": "3",
    "isActive": true,
    "sortOrder": 0,
    "metadata": {
        "icon": "",
        "color": "#EF4444",
        "keywords": [
            "dada"
        ],
        "seoTitle": "",
        "seoDescription": ""
    }
}
```

**Expected Response** (with admin auth):
```json
{
    "category": {
        "id": 22,
        "createdAt": "2025-01-08T02:30:00.000Z",
        "updatedAt": "2025-01-08T02:46:00.000Z",
        "title": "Lawyer",
        "description": "testdqs dqs qsqsd",
        "isActive": true,
        "sortOrder": 0,
        "parentId": 3,
        "metadata": {
            "icon": "",
            "color": "#EF4444",
            "keywords": ["dada"],
            "seoTitle": "",
            "seoDescription": ""
        },
        "imageId": null,
        "parent": {
            "id": 3,
            "title": "Professional Services"
        },
        "children": [],
        "image": null,
        "_count": {
            "providers": 0,
            "children": 0
        }
    },
    "message": "Provider category updated successfully"
}
```

## Input Validation Examples

### ✅ Valid Inputs

```json
// Numbers as numbers
{
    "parentId": 3,
    "sortOrder": 5
}

// Numbers as strings (auto-converted)
{
    "parentId": "3",
    "sortOrder": "5"
}

// Empty string for parentId (treated as null)
{
    "parentId": "",
    "sortOrder": 0
}

// Mixed types
{
    "parentId": 3,
    "sortOrder": "0"
}
```

### ❌ Invalid Inputs

```json
// Invalid number strings
{
    "parentId": "abc",  // Error: Invalid parentId
    "sortOrder": "xyz"  // Error: Invalid sortOrder
}

// Negative parentId
{
    "parentId": "-1"    // Error: Invalid parentId (must be positive)
}

// Non-integer sortOrder
{
    "parentId": "3.5"   // Error: Invalid parentId (must be integer)
}
```

## Create Category API Test

### ✅ Working Example

**Request**: `POST /api/auth/admin/provider-categories`

**Payload**:
```json
{
    "title": "Medical Specialists",
    "description": "Specialized medical professionals",
    "parentId": "1",
    "isActive": true,
    "sortOrder": "10",
    "metadata": {
        "icon": "fas fa-user-md",
        "color": "#10B981",
        "keywords": ["medical", "doctor", "specialist", "healthcare"],
        "seoTitle": "Find Medical Specialists",
        "seoDescription": "Connect with specialized medical professionals in your area"
    }
}
```

## Partial Update Examples

### Update Only Title
```json
{
    "title": "Legal Services"
}
```

### Update Only Metadata
```json
{
    "metadata": {
        "icon": "fas fa-balance-scale",
        "color": "#DC2626",
        "keywords": ["legal", "law", "attorney"],
        "seoTitle": "Professional Legal Services",
        "seoDescription": "Find qualified legal professionals"
    }
}
```

### Update Status and Order
```json
{
    "isActive": false,
    "sortOrder": "99"
}
```

### Clear Parent (Make Root Category)
```json
{
    "parentId": ""
}
```

## Error Response Examples

### Validation Error
```json
{
    "error": "Operation arguments validation failed"
}
```

### Authentication Error
```json
{
    "error": "Admin access required"
}
```

### Not Found Error
```json
{
    "error": "Provider category not found"
}
```

### Duplicate Title Error
```json
{
    "error": "Category with this title already exists"
}
```

## Testing Checklist

- [ ] Update with string `parentId` works
- [ ] Update with number `parentId` works
- [ ] Update with empty string `parentId` works (sets to null)
- [ ] Update with string `sortOrder` works
- [ ] Update with number `sortOrder` works
- [ ] Invalid string numbers return validation errors
- [ ] Partial updates work (only provided fields are updated)
- [ ] Metadata updates work correctly
- [ ] Authentication is required
- [ ] Category not found returns 404
- [ ] Duplicate titles are rejected

## cURL Examples

### Update Category (with admin token)
```bash
curl -X PUT "https://dapi-test.adscloud.org:8443/api/auth/admin/provider-categories/22" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -d '{
    "title": "Lawyer",
    "description": "Legal professionals and law firms",
    "parentId": "3",
    "isActive": true,
    "sortOrder": "0",
    "metadata": {
        "icon": "fas fa-gavel",
        "color": "#EF4444",
        "keywords": ["legal", "lawyer", "attorney"],
        "seoTitle": "Find Professional Lawyers",
        "seoDescription": "Connect with experienced legal professionals"
    }
}'
```

### Create Category (with admin token)
```bash
curl -X POST "https://dapi-test.adscloud.org:8443/api/auth/admin/provider-categories" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -d '{
    "title": "New Category",
    "description": "Category description",
    "parentId": "1",
    "isActive": true,
    "sortOrder": "5"
}'
```
