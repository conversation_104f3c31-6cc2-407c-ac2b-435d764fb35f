#!/usr/bin/env node

/**
 * Admin API Test Suite
 * 
 * This script tests all admin API endpoints to ensure they work correctly.
 * Run this script to verify the admin API is functioning properly.
 * 
 * Usage: node admin-api-test.js
 * 
 * Prerequisites:
 * - Admin user must exist in the database
 * - Server must be running
 * - Update the BASE_URL and admin credentials below
 */

const https = require('https');
const http = require('http');

// Configuration
const BASE_URL = 'http://localhost:3001'; // Update this to your server URL
const ADMIN_EMAIL = '<EMAIL>'; // Update with your admin email
const ADMIN_PASSWORD = 'adminpassword123'; // Update with your admin password

// Test state
let sessionId = null;
let testResults = [];
let createdCategoryId = null;
let testProviderId = null;

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

/**
 * HTTP Client helper
 */
class HttpClient {
  static async request(url, options = {}) {
    return new Promise((resolve, reject) => {
      const urlObj = new URL(url);
      const protocol = urlObj.protocol === 'https:' ? https : http;
      
      const requestOptions = {
        hostname: urlObj.hostname,
        port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
        path: urlObj.pathname + urlObj.search,
        method: options.method || 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        }
      };

      const req = protocol.request(requestOptions, (res) => {
        let data = '';
        res.on('data', (chunk) => {
          data += chunk;
        });
        res.on('end', () => {
          try {
            const response = {
              status: res.statusCode,
              headers: res.headers,
              data: data ? JSON.parse(data) : null
            };
            resolve(response);
          } catch (error) {
            resolve({
              status: res.statusCode,
              headers: res.headers,
              data: data,
              error: error.message
            });
          }
        });
      });

      req.on('error', (error) => {
        reject(error);
      });

      if (options.body) {
        req.write(JSON.stringify(options.body));
      }

      req.end();
    });
  }

  static async get(url, headers = {}) {
    return this.request(url, { method: 'GET', headers });
  }

  static async post(url, body, headers = {}) {
    return this.request(url, { method: 'POST', body, headers });
  }

  static async put(url, body, headers = {}) {
    return this.request(url, { method: 'PUT', body, headers });
  }

  static async delete(url, headers = {}) {
    return this.request(url, { method: 'DELETE', headers });
  }
}

/**
 * Test utilities
 */
function logTest(name, status, message = '') {
  const statusColor = status ? colors.green : colors.red;
  const statusText = status ? 'PASS' : 'FAIL';
  
  console.log(`${statusColor}[${statusText}]${colors.reset} ${name}`);
  if (message) {
    console.log(`  ${colors.yellow}${message}${colors.reset}`);
  }
  
  testResults.push({ name, status, message });
}

function logSection(name) {
  console.log(`\n${colors.blue}${colors.bright}=== ${name} ===${colors.reset}`);
}

function logInfo(message) {
  console.log(`${colors.cyan}ℹ ${message}${colors.reset}`);
}

function logError(message) {
  console.log(`${colors.red}✗ ${message}${colors.reset}`);
}

function logSuccess(message) {
  console.log(`${colors.green}✓ ${message}${colors.reset}`);
}

/**
 * Test functions
 */
async function testAdminLogin() {
  logSection('Admin Authentication');
  
  try {
    // Test successful login
    const response = await HttpClient.post(`${BASE_URL}/api/auth/admin/login`, {
      email: ADMIN_EMAIL,
      password: ADMIN_PASSWORD
    });

    if (response.status === 200 && response.data.sessionId) {
      sessionId = response.data.sessionId;
      logTest('Admin login successful', true, `Session ID: ${sessionId.substring(0, 10)}...`);
      logInfo(`Logged in as: ${response.data.user.firstName} ${response.data.user.lastName} (${response.data.user.email})`);
    } else {
      logTest('Admin login successful', false, `Status: ${response.status}, Data: ${JSON.stringify(response.data)}`);
      return false;
    }

    // Test invalid credentials
    const invalidResponse = await HttpClient.post(`${BASE_URL}/api/auth/admin/login`, {
      email: ADMIN_EMAIL,
      password: 'wrongpassword'
    });

    if (invalidResponse.status === 401 || invalidResponse.status === 403) {
      logTest('Invalid credentials rejected', true);
    } else {
      logTest('Invalid credentials rejected', false, `Expected 401/403, got ${invalidResponse.status}`);
    }

    return true;
  } catch (error) {
    logTest('Admin login', false, error.message);
    return false;
  }
}

async function testProviderManagement() {
  logSection('Provider Management');
  
  if (!sessionId) {
    logError('No session ID available. Skipping provider tests.');
    return;
  }

  const headers = {
    'Authorization': `Bearer ${sessionId}`
  };

  try {
    // Test get providers list
    const providersResponse = await HttpClient.get(`${BASE_URL}/api/admin/providers?page=1&limit=5`, headers);
    
    if (providersResponse.status === 200 && providersResponse.data.providers) {
      logTest('Get providers list', true, `Found ${providersResponse.data.providers.length} providers`);
      
      // Store a provider ID for status update test
      if (providersResponse.data.providers.length > 0) {
        testProviderId = providersResponse.data.providers[0].id;
        logInfo(`Using provider ID ${testProviderId} for status update test`);
      }
    } else {
      logTest('Get providers list', false, `Status: ${providersResponse.status}`);
    }

    // Test search functionality
    const searchResponse = await HttpClient.get(`${BASE_URL}/api/admin/providers?search=test&limit=5`, headers);
    
    if (searchResponse.status === 200) {
      logTest('Provider search functionality', true, `Search returned ${searchResponse.data.providers.length} results`);
    } else {
      logTest('Provider search functionality', false, `Status: ${searchResponse.status}`);
    }

    // Test filtering by verification status
    const verifiedResponse = await HttpClient.get(`${BASE_URL}/api/admin/providers?isVerified=true&limit=5`, headers);
    
    if (verifiedResponse.status === 200) {
      logTest('Provider verification filter', true, `Found ${verifiedResponse.data.providers.length} verified providers`);
    } else {
      logTest('Provider verification filter', false, `Status: ${verifiedResponse.status}`);
    }

    // Test provider status update (if we have a provider)
    if (testProviderId) {
      const statusUpdateResponse = await HttpClient.put(
        `${BASE_URL}/api/admin/providers/${testProviderId}/status`,
        {
          isVerified: true,
          reason: 'Test approval via API test suite'
        },
        headers
      );

      if (statusUpdateResponse.status === 200) {
        logTest('Update provider status', true, 'Provider status updated successfully');
      } else {
        logTest('Update provider status', false, `Status: ${statusUpdateResponse.status}`);
      }
    } else {
      logTest('Update provider status', false, 'No provider available for testing');
    }

    // Test unauthorized access (without proper headers)
    const unauthorizedResponse = await HttpClient.get(`${BASE_URL}/api/admin/providers`);
    
    if (unauthorizedResponse.status === 401 || unauthorizedResponse.status === 403) {
      logTest('Unauthorized access blocked', true);
    } else {
      logTest('Unauthorized access blocked', false, `Expected 401/403, got ${unauthorizedResponse.status}`);
    }

  } catch (error) {
    logTest('Provider management', false, error.message);
  }
}

async function testCustomerManagement() {
  logSection('Customer Management');
  
  if (!sessionId) {
    logError('No session ID available. Skipping customer tests.');
    return;
  }

  const headers = {
    'Authorization': `Bearer ${sessionId}`
  };

  try {
    // Test get customers list
    const customersResponse = await HttpClient.get(`${BASE_URL}/api/admin/customers?page=1&limit=5`, headers);
    
    if (customersResponse.status === 200 && customersResponse.data.customers) {
      logTest('Get customers list', true, `Found ${customersResponse.data.customers.length} customers`);
    } else {
      logTest('Get customers list', false, `Status: ${customersResponse.status}`);
    }

    // Test customer search
    const searchResponse = await HttpClient.get(`${BASE_URL}/api/admin/customers?search=test&limit=5`, headers);
    
    if (searchResponse.status === 200) {
      logTest('Customer search functionality', true, `Search returned ${searchResponse.data.customers.length} results`);
    } else {
      logTest('Customer search functionality', false, `Status: ${searchResponse.status}`);
    }

    // Test pagination
    const paginationResponse = await HttpClient.get(`${BASE_URL}/api/admin/customers?page=2&limit=3`, headers);
    
    if (paginationResponse.status === 200 && paginationResponse.data.pagination) {
      logTest('Customer pagination', true, 
        `Page ${paginationResponse.data.pagination.page} of ${paginationResponse.data.pagination.totalPages}`);
    } else {
      logTest('Customer pagination', false, `Status: ${paginationResponse.status}`);
    }

  } catch (error) {
    logTest('Customer management', false, error.message);
  }
}

async function testAdminUserManagement() {
  logSection('Admin User Management');
  
  if (!sessionId) {
    logError('No session ID available. Skipping admin user tests.');
    return;
  }

  const headers = {
    'Authorization': `Bearer ${sessionId}`
  };

  try {
    // Test creating a new admin user
    const testEmail = `testadmin${Date.now()}@example.com`;
    const createResponse = await HttpClient.post(
      `${BASE_URL}/api/admin/users`,
      {
        email: testEmail,
        firstName: 'Test',
        lastName: 'Admin',
        password: 'testpassword123',
        role: 'ADMIN'
      },
      headers
    );

    if (createResponse.status === 201) {
      logTest('Create admin user', true, `Created admin user: ${testEmail}`);
    } else {
      logTest('Create admin user', false, `Status: ${createResponse.status}, Response: ${JSON.stringify(createResponse.data)}`);
    }

    // Test creating duplicate admin user
    const duplicateResponse = await HttpClient.post(
      `${BASE_URL}/api/admin/users`,
      {
        email: testEmail,
        firstName: 'Duplicate',
        lastName: 'Admin',
        password: 'testpassword123',
        role: 'ADMIN'
      },
      headers
    );

    if (duplicateResponse.status === 400) {
      logTest('Duplicate admin user prevention', true, 'Duplicate email properly rejected');
    } else {
      logTest('Duplicate admin user prevention', false, `Expected 400, got ${duplicateResponse.status}`);
    }

    // Test creating admin user with invalid data
    const invalidDataResponse = await HttpClient.post(
      `${BASE_URL}/api/admin/users`,
      {
        email: 'invalid-email',
        firstName: '',
        lastName: 'Admin',
        password: '123', // Too short
        role: 'ADMIN'
      },
      headers
    );

    if (invalidDataResponse.status === 400) {
      logTest('Invalid admin user data validation', true, 'Invalid data properly rejected');
    } else {
      logTest('Invalid admin user data validation', false, `Expected 400, got ${invalidDataResponse.status}`);
    }

  } catch (error) {
    logTest('Admin user management', false, error.message);
  }
}

async function testProviderCategoryManagement() {
  logSection('Provider Category Management');
  
  if (!sessionId) {
    logError('No session ID available. Skipping category tests.');
    return;
  }

  const headers = {
    'Authorization': `Bearer ${sessionId}`
  };

  try {
    // Test get categories
    const categoriesResponse = await HttpClient.get(`${BASE_URL}/api/admin/provider-categories`, headers);
    
    if (categoriesResponse.status === 200 && categoriesResponse.data.categories) {
      logTest('Get provider categories', true, `Found ${categoriesResponse.data.categories.length} categories`);
    } else {
      logTest('Get provider categories', false, `Status: ${categoriesResponse.status}`);
    }

    // Test create category
    const testCategoryName = `Test Category ${Date.now()}`;
    const createResponse = await HttpClient.post(
      `${BASE_URL}/api/admin/provider-categories`,
      {
        title: testCategoryName,
        parentId: null
      },
      headers
    );

    if (createResponse.status === 201) {
      createdCategoryId = createResponse.data.category.id;
      logTest('Create provider category', true, `Created category: ${testCategoryName} (ID: ${createdCategoryId})`);
    } else {
      logTest('Create provider category', false, `Status: ${createResponse.status}`);
    }

    // Test create duplicate category
    const duplicateResponse = await HttpClient.post(
      `${BASE_URL}/api/admin/provider-categories`,
      {
        title: testCategoryName,
        parentId: null
      },
      headers
    );

    if (duplicateResponse.status === 400) {
      logTest('Duplicate category prevention', true, 'Duplicate category name properly rejected');
    } else {
      logTest('Duplicate category prevention', false, `Expected 400, got ${duplicateResponse.status}`);
    }

    // Test update category (if we created one)
    if (createdCategoryId) {
      const updatedName = `Updated ${testCategoryName}`;
      const updateResponse = await HttpClient.put(
        `${BASE_URL}/api/admin/provider-categories/${createdCategoryId}`,
        {
          title: updatedName,
          parentId: null
        },
        headers
      );

      if (updateResponse.status === 200) {
        logTest('Update provider category', true, `Updated category to: ${updatedName}`);
      } else {
        logTest('Update provider category', false, `Status: ${updateResponse.status}`);
      }
    }

    // Test create child category
    if (createdCategoryId) {
      const childCategoryName = `Child Category ${Date.now()}`;
      const childResponse = await HttpClient.post(
        `${BASE_URL}/api/admin/provider-categories`,
        {
          title: childCategoryName,
          parentId: createdCategoryId
        },
        headers
      );

      if (childResponse.status === 201) {
        logTest('Create child category', true, `Created child category: ${childCategoryName}`);
        
        // Try to delete parent (should fail)
        const deleteParentResponse = await HttpClient.delete(
          `${BASE_URL}/api/admin/provider-categories/${createdCategoryId}`,
          headers
        );

        if (deleteParentResponse.status === 400) {
          logTest('Prevent deletion of category with children', true, 'Parent category deletion properly prevented');
        } else {
          logTest('Prevent deletion of category with children', false, `Expected 400, got ${deleteParentResponse.status}`);
        }

        // Delete child first
        const deleteChildResponse = await HttpClient.delete(
          `${BASE_URL}/api/admin/provider-categories/${childResponse.data.category.id}`,
          headers
        );

        if (deleteChildResponse.status === 200) {
          logTest('Delete child category', true, 'Child category deleted successfully');
        } else {
          logTest('Delete child category', false, `Status: ${deleteChildResponse.status}`);
        }
      } else {
        logTest('Create child category', false, `Status: ${childResponse.status}`);
      }
    }

    // Test delete category (if we created one)
    if (createdCategoryId) {
      const deleteResponse = await HttpClient.delete(
        `${BASE_URL}/api/admin/provider-categories/${createdCategoryId}`,
        headers
      );

      if (deleteResponse.status === 200) {
        logTest('Delete provider category', true, 'Category deleted successfully');
      } else {
        logTest('Delete provider category', false, `Status: ${deleteResponse.status}`);
      }
    }

    // Test delete non-existent category
    const deleteNonExistentResponse = await HttpClient.delete(
      `${BASE_URL}/api/admin/provider-categories/99999`,
      headers
    );

    if (deleteNonExistentResponse.status === 404) {
      logTest('Delete non-existent category', true, 'Non-existent category properly handled');
    } else {
      logTest('Delete non-existent category', false, `Expected 404, got ${deleteNonExistentResponse.status}`);
    }

  } catch (error) {
    logTest('Provider category management', false, error.message);
  }
}

async function testErrorHandling() {
  logSection('Error Handling & Security');
  
  try {
    // Test accessing admin endpoints without authentication
    const noAuthResponse = await HttpClient.get(`${BASE_URL}/api/admin/providers`);
    
    if (noAuthResponse.status === 401 || noAuthResponse.status === 403) {
      logTest('Unauthenticated access blocked', true);
    } else {
      logTest('Unauthenticated access blocked', false, `Expected 401/403, got ${noAuthResponse.status}`);
    }

    // Test with invalid session token
    const invalidTokenResponse = await HttpClient.get(`${BASE_URL}/api/admin/providers`, {
      'Authorization': 'Bearer invalid-token-123'
    });
    
    if (invalidTokenResponse.status === 401 || invalidTokenResponse.status === 403) {
      logTest('Invalid token rejected', true);
    } else {
      logTest('Invalid token rejected', false, `Expected 401/403, got ${invalidTokenResponse.status}`);
    }

    // Test accessing non-existent endpoints
    const notFoundResponse = await HttpClient.get(`${BASE_URL}/api/admin/nonexistent`, {
      'Authorization': `Bearer ${sessionId}`
    });
    
    if (notFoundResponse.status === 404) {
      logTest('Non-existent endpoint handling', true);
    } else {
      logTest('Non-existent endpoint handling', false, `Expected 404, got ${notFoundResponse.status}`);
    }

  } catch (error) {
    logTest('Error handling', false, error.message);
  }
}

/**
 * Main test runner
 */
async function runTests() {
  console.log(`${colors.bright}${colors.magenta}Admin API Test Suite${colors.reset}`);
  console.log(`${colors.cyan}Testing against: ${BASE_URL}${colors.reset}`);
  console.log(`${colors.cyan}Admin email: ${ADMIN_EMAIL}${colors.reset}\n`);

  // Run all test suites
  const loginSuccessful = await testAdminLogin();
  
  if (loginSuccessful) {
    await testProviderManagement();
    await testCustomerManagement();
    await testAdminUserManagement();
    await testProviderCategoryManagement();
    await testErrorHandling();
  } else {
    logError('Cannot continue testing without successful login');
  }

  // Display results summary
  logSection('Test Results Summary');
  
  const passed = testResults.filter(r => r.status).length;
  const failed = testResults.filter(r => !r.status).length;
  const total = testResults.length;

  console.log(`${colors.bright}Total Tests: ${total}${colors.reset}`);
  console.log(`${colors.green}Passed: ${passed}${colors.reset}`);
  console.log(`${colors.red}Failed: ${failed}${colors.reset}`);

  if (failed > 0) {
    console.log(`\n${colors.red}${colors.bright}Failed Tests:${colors.reset}`);
    testResults.filter(r => !r.status).forEach(test => {
      console.log(`${colors.red}  ✗ ${test.name}${colors.reset}`);
      if (test.message) {
        console.log(`    ${colors.yellow}${test.message}${colors.reset}`);
      }
    });
  }

  const successRate = ((passed / total) * 100).toFixed(1);
  console.log(`\n${colors.bright}Success Rate: ${successRate}%${colors.reset}`);

  if (successRate === '100.0') {
    logSuccess('All tests passed! 🎉');
  } else if (successRate >= '80.0') {
    console.log(`${colors.yellow}Most tests passed, but some issues found. 🔧${colors.reset}`);
  } else {
    logError('Many tests failed. Please check the implementation. 🔥');
  }

  process.exit(failed > 0 ? 1 : 0);
}

/**
 * Handle script errors
 */
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

// Run the tests
runTests().catch(error => {
  console.error('Test runner failed:', error);
  process.exit(1);
}); 