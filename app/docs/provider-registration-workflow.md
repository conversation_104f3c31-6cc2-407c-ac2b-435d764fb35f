# Provider Registration Workflow - Multi-Step OTP Verification

This document describes the secure multi-step OTP verification workflow for provider registration that follows the same patterns as customer registration.

## Overview

The provider registration process follows a secure two-step OTP verification workflow:

1. **Step 1**: Request OTP (Phone or Email) - Creates user with CLIENT role
2. **Step 2**: Verify OTP and Complete Provider Registration - Creates provider profile

This ensures the same level of security and verification as customer registration while collecting provider-specific information and creating users with the correct CLIENT role for providers.

## Key Differences from Customer Registration

### User Role Assignment
- **Customer Registration**: Creates users with `role: "CUSTOMER"`
- **Provider Registration**: Creates users with `role: "CLIENT"`

### Dedicated Provider Actions
- **`requestProviderPhoneOtp`**: Provider-specific phone OTP action
- **`requestProviderEmailOtp`**: Provider-specific email OTP action
- **`handleVerifyOtpAndRegisterProvider`**: Provider-specific verification and registration

## Workflow Steps

### Step 1: Request OTP (Creates User with CLIENT Role)

Providers can register using either phone number or email address. The mobile app should request an OTP first. **Important**: Setting `isProviderRegistration: true` triggers the use of provider-specific OTP actions that create users with `role: "CLIENT"` instead of `role: "CUSTOMER"`.

#### **Phone-Based Registration**

**Endpoint:** `POST /api/auth/request-otp`

**Request Body:**
```json
{
  "phoneNumber": "+************",
  "firstName": "John",
  "lastName": "Doe",
  "isProviderRegistration": true,
  "providerCategoryId": 16,
  "businessName": "John's Medical Practice"
}
```

**Response:**
```json
{
  "message": "OTP sent successfully to your phone number. Please verify to complete registration.",
  "providerContext": {
    "isProviderRegistration": true,
    "providerCategoryId": 16,
    "businessName": "John's Medical Practice"
  }
}
```

**Internal Action Used:** `requestProviderPhoneOtp` (creates user with `role: "CLIENT"`)

#### **Email-Based Registration**

**Endpoint:** `POST /api/auth/request-email-otp`

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "password": "securepassword123",
  "isProviderRegistration": true,
  "providerCategoryId": 16,
  "businessName": "John's Medical Practice",
  "phone": "+************"
}
```

**Response:**
```json
{
  "message": "OTP sent successfully to your email address. Please verify to complete provider registration.",
  "providerContext": {
    "isProviderRegistration": true,
    "providerCategoryId": 16,
    "businessName": "John's Medical Practice",
    "phone": "+************"
  }
}
```

**Internal Action Used:** `requestProviderEmailOtp` (creates user with `role: "CLIENT"`)

### Step 2: Verify OTP and Complete Provider Registration

After receiving the OTP, complete the provider registration process. This step verifies the OTP and creates the provider profile linked to the user account.

**Endpoint:** `POST /api/auth/provider/verify-otp-register`

**Request Body:**
```json
{
  "otp": "123456",
  "identifier": "<EMAIL>",
  "password": "securepassword123",
  "firstName": "John",
  "lastName": "Doe",
  "providerCategoryId": 16,
  "businessName": "John's Medical Practice",
  "phone": "+************",
  "email": "<EMAIL>"
}
```

**Success Response (201):**
```json
{
  "message": "Provider registered successfully",
  "user": {
    "id": "user123",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "role": "CLIENT"
  },
  "provider": {
    "id": 45,
    "userId": "user123",
    "providerCategoryId": 16,
    "title": "John's Medical Practice",
    "phone": "+************",
    "isSetupComplete": false
  },
  "sessionId": "session_token_here"
}
```

**Key Changes:**
- **User Role**: Now correctly shows `"role": "CLIENT"` for provider users
- **Provider Profile**: Created in the same transaction as OTP verification
- **Session Token**: Automatic login after successful registration

## Field Descriptions

### Required Fields

- **`otp`**: 6-digit verification code received via SMS or email
- **`identifier`**: Email address or phone number (must match the one used for OTP request)
- **`password`**: User's chosen password (minimum 8 characters)
- **`firstName`**: Provider's first name
- **`lastName`**: Provider's last name
- **`providerCategoryId`**: Selected provider category ID (from categories API)

### Optional Fields

- **`businessName`**: Business or practice name (defaults to "FirstName LastName" if not provided). Can be `null` or omitted.
- **`phone`**: Phone number (if registering with email). Can be `null` or omitted.
- **`email`**: Email address (if registering with phone). Can be `null` or omitted.

**Note**: Optional fields can be sent as `null`, `undefined`, or omitted entirely. The API handles all these cases gracefully.

## Error Handling

### Common Error Responses

**400 - Invalid OTP:**
```json
{
  "message": "Invalid OTP."
}
```

**400 - Expired OTP:**
```json
{
  "message": "OTP has expired. Please request a new one."
}
```

**404 - User Not Found:**
```json
{
  "message": "User not found with this identifier. Please request an OTP first."
}
```

**400 - Provider Profile Exists:**
```json
{
  "message": "User already has a service provider profile."
}
```

**409 - Email Already in Use:**
```json
{
  "message": "The email address specified is already in use by another account."
}
```

## Mobile App Integration

### Flutter Implementation Example

```dart
class ProviderRegistrationService {
  // Step 1: Request Phone OTP (Creates user with CLIENT role)
  Future<Map<String, dynamic>> requestPhoneOtp({
    required String phoneNumber,
    required String firstName,
    required String lastName,
    required int providerCategoryId,
    String? businessName,
  }) async {
    final response = await http.post(
      Uri.parse('$baseUrl/api/auth/request-otp'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'phoneNumber': phoneNumber,
        'firstName': firstName,
        'lastName': lastName,
        'isProviderRegistration': true, // Triggers provider-specific OTP action
        'providerCategoryId': providerCategoryId,
        'businessName': businessName,
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception(jsonDecode(response.body)['message']);
    }
  }

  // Step 1 Alternative: Request Email OTP (Creates user with CLIENT role)
  Future<Map<String, dynamic>> requestEmailOtp({
    required String email,
    required String firstName,
    required String lastName,
    required String password,
    required int providerCategoryId,
    String? businessName,
    String? phone,
  }) async {
    final response = await http.post(
      Uri.parse('$baseUrl/api/auth/request-email-otp'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'email': email,
        'firstName': firstName,
        'lastName': lastName,
        'password': password,
        'isProviderRegistration': true, // Triggers provider-specific OTP action
        'providerCategoryId': providerCategoryId,
        'businessName': businessName,
        'phone': phone,
      }),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception(jsonDecode(response.body)['message']);
    }
  }

  // Step 2: Verify OTP and Complete Provider Registration
  Future<ProviderRegistrationResult> verifyOtpAndRegisterProvider({
    required String otp,
    required String identifier,
    required String password,
    required String firstName,
    required String lastName,
    required int providerCategoryId,
    String? businessName,
    String? phone,
    String? email,
  }) async {
    final response = await http.post(
      Uri.parse('$baseUrl/api/auth/provider/verify-otp-register'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'otp': otp,
        'identifier': identifier,
        'password': password,
        'firstName': firstName,
        'lastName': lastName,
        'providerCategoryId': providerCategoryId,
        'businessName': businessName,
        'phone': phone,
        'email': email,
      }),
    );

    if (response.statusCode == 201) {
      final data = jsonDecode(response.body);

      // Store session token for authenticated requests
      await _storeSessionToken(data['sessionId']);

      // Verify user has CLIENT role
      assert(data['user']['role'] == 'CLIENT', 'Provider user should have CLIENT role');

      return ProviderRegistrationResult.fromJson(data);
    } else {
      throw Exception(jsonDecode(response.body)['message']);
    }
  }

  Future<void> _storeSessionToken(String sessionId) async {
    // Store session token in secure storage
    await SecureStorage.write(key: 'session_token', value: sessionId);
  }
}

// Data Models
class ProviderRegistrationResult {
  final String message;
  final ProviderUser user;
  final ProviderProfile provider;
  final String sessionId;

  ProviderRegistrationResult({
    required this.message,
    required this.user,
    required this.provider,
    required this.sessionId,
  });

  factory ProviderRegistrationResult.fromJson(Map<String, dynamic> json) {
    return ProviderRegistrationResult(
      message: json['message'],
      user: ProviderUser.fromJson(json['user']),
      provider: ProviderProfile.fromJson(json['provider']),
      sessionId: json['sessionId'],
    );
  }
}

class ProviderUser {
  final String id;
  final String email;
  final String firstName;
  final String lastName;
  final String role; // Should be "CLIENT" for providers

  ProviderUser({
    required this.id,
    required this.email,
    required this.firstName,
    required this.lastName,
    required this.role,
  });

  factory ProviderUser.fromJson(Map<String, dynamic> json) {
    return ProviderUser(
      id: json['id'],
      email: json['email'],
      firstName: json['firstName'],
      lastName: json['lastName'],
      role: json['role'],
    );
  }
}

class ProviderProfile {
  final int id;
  final String userId;
  final int providerCategoryId;
  final String title;
  final String? phone;
  final bool isSetupComplete;

  ProviderProfile({
    required this.id,
    required this.userId,
    required this.providerCategoryId,
    required this.title,
    this.phone,
    required this.isSetupComplete,
  });

  factory ProviderProfile.fromJson(Map<String, dynamic> json) {
    return ProviderProfile(
      id: json['id'],
      userId: json['userId'],
      providerCategoryId: json['providerCategoryId'],
      title: json['title'],
      phone: json['phone'],
      isSetupComplete: json['isSetupComplete'],
    );
  }
}
```

## Security Features

### OTP Verification
- **6-digit OTP**: Secure random generation
- **10-minute expiry**: Time-limited validity
- **Cooldown period**: 60-second cooldown between OTP requests
- **Single use**: OTP becomes invalid after successful verification

### User Verification & Role Management
- **Email verification**: Email address is verified through OTP
- **Phone verification**: Phone number is verified through SMS OTP
- **Password security**: Minimum 8 characters required
- **Duplicate prevention**: Prevents duplicate provider profiles
- **Correct Role Assignment**: Provider users created with `role: "CLIENT"` from registration
- **Role-Based Access Control**: Proper authorization throughout the application

### Session Management
- **Automatic login**: User is logged in after successful registration
- **Session token**: Secure session token returned for authenticated requests
- **Setup tracking**: `isSetupComplete: false` indicates onboarding required
- **Role verification**: Client can verify user has correct CLIENT role

## Integration with Existing Systems

### Consistency with Customer Registration
- **Same OTP patterns**: Uses dedicated provider OTP actions with identical security features
- **Same verification**: Uses existing `verifyOtpAndRegister` action for OTP validation
- **Same patterns**: Follows identical error handling and response formats
- **Same security**: Maintains all security features from customer registration
- **Smart action selection**: Automatically chooses correct OTP action based on registration type

### Provider-Specific Enhancements
- **Dedicated OTP actions**: `requestProviderPhoneOtp` and `requestProviderEmailOtp`
- **Correct role assignment**: Creates users with `role: "CLIENT"` from the start
- **Category selection**: Validates provider category during registration
- **Business information**: Collects business name and contact details
- **Setup workflow**: Integrates with provider onboarding setup process
- **Provider profile**: Creates provider profile linked to user account
- **Role-based authorization**: Proper permissions throughout the application

### Backward Compatibility
- **Customer registration unchanged**: Existing customer flows continue to work
- **Same API endpoints**: Uses same endpoints with `isProviderRegistration` flag
- **No breaking changes**: All existing functionality preserved

## Next Steps After Registration

After successful provider registration:

1. **Store session token** for authenticated API requests
2. **Check setup status** using `isSetupComplete` field
3. **Redirect to onboarding** if `isSetupComplete: false`
4. **Complete provider setup** using `/api/auth/provider/complete-setup` endpoint
5. **Access provider dashboard** after setup completion

The multi-step OTP verification workflow ensures secure provider registration while maintaining consistency with the existing customer registration patterns.

## Testing the New Workflow

### Manual Testing Examples

#### Test Provider Email OTP Request
```bash
curl -X POST "http://localhost:5400/api/auth/request-email-otp" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "firstName": "Jane",
    "lastName": "Smith",
    "password": "securepassword123",
    "isProviderRegistration": true,
    "providerCategoryId": 16,
    "businessName": "Janes Medical Practice",
    "phone": "+213555123457"
  }'
```

**Expected Response:**
```json
{
  "message": "OTP sent successfully to your email address. Please verify to complete provider registration.",
  "providerContext": {
    "isProviderRegistration": true,
    "providerCategoryId": 16,
    "businessName": "Janes Medical Practice",
    "phone": "+213555123457"
  }
}
```

#### Test Provider OTP Verification
```bash
curl -X POST "http://localhost:5400/api/auth/provider/verify-otp-register" \
  -H "Content-Type: application/json" \
  -d '{
    "otp": "123456",
    "identifier": "<EMAIL>",
    "password": "securepassword123",
    "firstName": "Jane",
    "lastName": "Smith",
    "providerCategoryId": 16,
    "businessName": "Janes Medical Practice",
    "phone": "+213555123457",
    "email": "<EMAIL>"
  }'
```

**Expected Response (with valid OTP):**
```json
{
  "message": "Provider registered successfully",
  "user": {
    "id": "user123",
    "email": "<EMAIL>",
    "firstName": "Jane",
    "lastName": "Smith",
    "role": "CLIENT"
  },
  "provider": {
    "id": 45,
    "userId": "user123",
    "providerCategoryId": 16,
    "title": "Janes Medical Practice",
    "phone": "+213555123457",
    "isSetupComplete": false
  },
  "sessionId": "session_token_here"
}
```

### Key Verification Points

1. **User Role**: Verify that `user.role` is `"CLIENT"` in the response
2. **Provider Context**: Check that `providerContext` is returned in OTP request
3. **Provider Profile**: Confirm provider profile is created with correct category
4. **Session Token**: Ensure session token is returned for immediate authentication
5. **Setup Status**: Verify `isSetupComplete: false` for onboarding flow
6. **Null Handling**: Verify that `null` values in optional fields are handled correctly

### Payload Validation

The API correctly handles optional fields that can be `null`, `undefined`, or omitted:

**Valid Payloads:**
```json
// With null values
{
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "password": "securepassword123",
  "isProviderRegistration": true,
  "providerCategoryId": 16,
  "businessName": null,
  "phone": null
}

// With actual values
{
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "password": "securepassword123",
  "isProviderRegistration": true,
  "providerCategoryId": 16,
  "businessName": "Johns Medical Practice",
  "phone": "+************"
}

// With omitted optional fields
{
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "password": "securepassword123",
  "isProviderRegistration": true,
  "providerCategoryId": 16
}
```

**Response Handling:**
- Fields with `null` values are excluded from `providerContext`
- Fields with actual values are included in `providerContext`
- No validation errors for `null` optional fields

## Implementation Details

### New Wasp Actions Created

#### `requestProviderPhoneOtp`
```typescript
// Creates users with Role.CLIENT instead of Role.CUSTOMER
export const requestProviderPhoneOtp: RequestPhoneOtp<RequestProviderPhoneOtpData, { message: string }> = async (
  rawArgs,
  context
) => {
  // ... same logic as requestPhoneOtp but with:
  role: Role.CLIENT, // CLIENT role for provider users
  // ... rest of implementation
};
```

#### `requestProviderEmailOtp`
```typescript
// Creates users with Role.CLIENT instead of Role.CUSTOMER
export const requestProviderEmailOtp: RequestPhoneOtp<RequestProviderEmailOtpData, { message: string }> = async (
  rawArgs,
  context
) => {
  // ... same logic as requestEmailOtp but with:
  role: Role.CLIENT, // CLIENT role for provider users
  // ... rest of implementation
};
```

### Updated API Handlers

#### Smart OTP Action Selection
The API handlers now intelligently choose between customer and provider OTP actions based on the `isProviderRegistration` flag:

```typescript
// Phone OTP Handler
if (isProviderRegistration) {
  // Use provider-specific OTP action (creates users with CLIENT role)
  result = await requestProviderPhoneOtp({ phoneNumber, firstName, lastName });
} else {
  // Use customer OTP action (creates users with CUSTOMER role)
  result = await requestPhoneOtp({ phoneNumber, firstName, lastName });
}

// Email OTP Handler
if (isProviderRegistration) {
  // Use provider-specific OTP action (creates users with CLIENT role)
  result = await requestProviderEmailOtp({ email, firstName, lastName, password });
} else {
  // Use customer OTP action (creates users with CUSTOMER role)
  result = await requestEmailOtp({ email, firstName, lastName, password });
}
```

### Backward Compatibility
- **Customer registration continues to work unchanged**
- **Existing customer OTP endpoints remain functional**
- **Provider registration uses the same endpoints with `isProviderRegistration: true`**
