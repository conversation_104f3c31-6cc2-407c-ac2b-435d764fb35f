/**
 * TypeScript interfaces for the expanded ProviderCategory model
 */

export interface CategoryMetadata {
  icon?: string;
  color?: string;
  keywords?: string[];
  seoTitle?: string;
  seoDescription?: string;
}

export interface ProviderCategory {
  id: number;
  createdAt: string;
  updatedAt: string;
  title: string;
  description?: string | null;
  isActive: boolean;
  sortOrder: number;
  metadata?: CategoryMetadata | null;
  parentId?: number | null;
  imageId?: string | null;
}

export interface ProviderCategoryWithRelations extends ProviderCategory {
  parent?: ProviderCategory | null;
  children?: ProviderCategory[];
  image?: {
    id: string;
    name: string;
    type: string;
    key: string;
    uploadUrl: string;
    createdAt: string;
  } | null;
  providers?: {
    id: number;
    title: string;
    isVerified: boolean;
    user: {
      firstName: string;
      lastName: string;
      email: string;
    };
  }[];
  _count?: {
    providers: number;
    children: number;
  };
}

// API Request/Response types
export interface CreateCategoryRequest {
  title: string;
  description?: string;
  parentId?: number | string; // API accepts both number and string
  isActive?: boolean;
  sortOrder?: number | string; // API accepts both number and string
  metadata?: CategoryMetadata;
}

export interface UpdateCategoryRequest {
  title?: string;
  description?: string;
  parentId?: number | string; // API accepts both number and string
  isActive?: boolean;
  sortOrder?: number | string; // API accepts both number and string
  metadata?: CategoryMetadata;
}

export interface CategoryApiResponse {
  success: boolean;
  message: string;
  data: ProviderCategoryWithRelations;
}

export interface CategoriesListResponse {
  success: boolean;
  data: ProviderCategoryWithRelations[];
  message: string;
}

// Utility types for form handling
export interface CategoryFormData {
  title: string;
  description: string;
  parentId: string; // Form inputs are strings
  isActive: boolean;
  sortOrder: string; // Form inputs are strings
  metadata: {
    icon: string;
    color: string;
    keywords: string; // Comma-separated string for form input
    seoTitle: string;
    seoDescription: string;
  };
}

// Helper functions for type conversion
export const convertFormDataToRequest = (formData: CategoryFormData): CreateCategoryRequest => ({
  title: formData.title,
  description: formData.description || undefined,
  parentId: formData.parentId ? parseInt(formData.parentId) : undefined,
  isActive: formData.isActive,
  sortOrder: parseInt(formData.sortOrder) || 0,
  metadata: {
    icon: formData.metadata.icon,
    color: formData.metadata.color,
    keywords: formData.metadata.keywords 
      ? formData.metadata.keywords.split(',').map(k => k.trim()).filter(k => k.length > 0)
      : [],
    seoTitle: formData.metadata.seoTitle,
    seoDescription: formData.metadata.seoDescription,
  }
});

export const convertCategoryToFormData = (category: ProviderCategoryWithRelations): CategoryFormData => ({
  title: category.title,
  description: category.description || '',
  parentId: category.parentId?.toString() || '',
  isActive: category.isActive,
  sortOrder: category.sortOrder.toString(),
  metadata: {
    icon: category.metadata?.icon || '',
    color: category.metadata?.color || '#EF4444',
    keywords: category.metadata?.keywords?.join(', ') || '',
    seoTitle: category.metadata?.seoTitle || '',
    seoDescription: category.metadata?.seoDescription || '',
  }
});

// Default values
export const DEFAULT_CATEGORY_METADATA: CategoryMetadata = {
  icon: '',
  color: '#EF4444',
  keywords: [],
  seoTitle: '',
  seoDescription: ''
};

export const DEFAULT_CATEGORY_VALUES: Partial<CreateCategoryRequest> = {
  isActive: true,
  sortOrder: 0,
  metadata: DEFAULT_CATEGORY_METADATA
};

// Validation helpers
export const validateCategoryData = (data: CreateCategoryRequest): string[] => {
  const errors: string[] = [];
  
  if (!data.title || data.title.trim().length === 0) {
    errors.push('Title is required');
  }
  
  if (data.title && data.title.length > 255) {
    errors.push('Title must be less than 255 characters');
  }
  
  if (data.description && data.description.length > 1000) {
    errors.push('Description must be less than 1000 characters');
  }
  
  if (data.sortOrder !== undefined && (data.sortOrder < 0 || data.sortOrder > 9999)) {
    errors.push('Sort order must be between 0 and 9999');
  }
  
  if (data.metadata?.color && !/^#[0-9A-F]{6}$/i.test(data.metadata.color)) {
    errors.push('Color must be a valid hex color code (e.g., #FF0000)');
  }
  
  if (data.metadata?.keywords && data.metadata.keywords.length > 20) {
    errors.push('Maximum 20 keywords allowed');
  }
  
  if (data.metadata?.seoTitle && data.metadata.seoTitle.length > 60) {
    errors.push('SEO title should be less than 60 characters for optimal SEO');
  }
  
  if (data.metadata?.seoDescription && data.metadata.seoDescription.length > 160) {
    errors.push('SEO description should be less than 160 characters for optimal SEO');
  }
  
  return errors;
};

// Sorting and filtering utilities
export const sortCategoriesByOrder = (categories: ProviderCategory[]): ProviderCategory[] => {
  return [...categories].sort((a, b) => {
    if (a.sortOrder !== b.sortOrder) {
      return a.sortOrder - b.sortOrder;
    }
    return a.title.localeCompare(b.title);
  });
};

export const filterActiveCategories = (categories: ProviderCategory[]): ProviderCategory[] => {
  return categories.filter(cat => cat.isActive);
};

export const searchCategories = (categories: ProviderCategory[], searchTerm: string): ProviderCategory[] => {
  const term = searchTerm.toLowerCase();
  return categories.filter(cat => 
    cat.title.toLowerCase().includes(term) ||
    cat.description?.toLowerCase().includes(term) ||
    cat.metadata?.keywords?.some(keyword => keyword.toLowerCase().includes(term)) ||
    cat.metadata?.seoTitle?.toLowerCase().includes(term)
  );
};

export const buildCategoryHierarchy = (categories: ProviderCategory[]): ProviderCategory[] => {
  const categoryMap = new Map(categories.map(cat => [cat.id, { ...cat, children: [] }]));
  const rootCategories: ProviderCategory[] = [];
  
  categories.forEach(category => {
    const cat = categoryMap.get(category.id)!;
    if (category.parentId && categoryMap.has(category.parentId)) {
      const parent = categoryMap.get(category.parentId)!;
      if (!parent.children) parent.children = [];
      parent.children.push(cat);
    } else {
      rootCategories.push(cat);
    }
  });
  
  return rootCategories;
};
