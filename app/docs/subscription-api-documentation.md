# Subscription and Credits API System Documentation

## Overview

The Subscription and Credits API System provides a comprehensive solution for managing subscription plans, user subscriptions, and credit allocation within the Wasp.js application. This system allows users to subscribe to different service tiers, manage their credit balances, and track subscription history.

### Key Features

- **Flexible Subscription Plans**: Support for monthly, yearly, and one-time payment plans
- **Credit Management**: Automatic credit allocation based on subscription plans
- **Subscription History**: Complete audit trail of user subscription changes
- **Integration**: Seamless integration with existing payment processors and user management
- **RESTful API**: Clean, well-documented API endpoints for all subscription operations

## Database Schema

### Subscription Model

The `Subscription` model stores subscription plan details and configurations.

```prisma
model Subscription {
  id              String   @id @default(uuid())
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  
  name            String                    // Plan name (e.g., "Pro Plan")
  description     String   @db.Text        // Detailed plan description
  price           Float                     // Price in cents (e.g., 1000 = $10.00)
  duration        Int                       // Duration in days
  interval        String                    // "monthly", "yearly", "one-time"
  creditsIncluded Int                       // Number of credits included
  features        String   @db.Text        // JSON array of features
  isActive        Boolean  @default(true)  // Whether plan is available
  
  // Relations
  userSubscriptions UserSubscription[]
}
```

### UserSubscription Model

The `UserSubscription` model tracks individual user subscription instances and history.

```prisma
model UserSubscription {
  id                           String   @id @default(uuid())
  createdAt                    DateTime @default(now())
  updatedAt                    DateTime @updatedAt
  
  user                         User     @relation(fields: [userId], references: [id])
  userId                       String
  
  subscription                 Subscription @relation(fields: [subscriptionId], references: [id])
  subscriptionId               String
  
  status                       String   // "active", "expired", "cancelled", "pending"
  startDate                    DateTime // When subscription started
  endDate                      DateTime? // When subscription ends (null for one-time)
  creditsAllocated             Int      // Credits given for this subscription
  paymentProcessorSubscriptionId String? // External payment processor ID
  
  // Indexes for faster queries
  @@index([userId, status])
  @@index([startDate, endDate])
}
```

### User Model Updates

The existing `User` model has been extended with a subscription relation:

```prisma
model User {
  // ... existing fields
  
  // Subscription relations
  subscriptions UserSubscription[]
  
  // ... rest of model
}
```

## API Endpoints

### 1. Get All Subscription Plans

**Endpoint**: `GET /api/subscriptions`  
**Authentication**: Not required (Public endpoint)  
**Description**: Retrieves all available subscription plans

#### Query Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `isActive` | boolean | `true` | Filter by active status |
| `page` | number | `1` | Page number for pagination |
| `limit` | number | `10` | Number of items per page (max 100) |

#### Response Format

```json
{
  "success": true,
  "message": "Subscription plans retrieved successfully",
  "data": {
    "subscriptions": [
      {
        "id": "uuid-string",
        "name": "Pro Plan",
        "description": "For growing businesses...",
        "price": 1000,
        "duration": 30,
        "interval": "monthly",
        "creditsIncluded": 200,
        "features": [
          "10 queues",
          "Priority support",
          "Advanced features"
        ],
        "isActive": true,
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-01T00:00:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "totalCount": 6,
      "totalPages": 1
    }
  }
}
```

#### Example Request

```javascript
// Fetch all active subscription plans
const response = await fetch('/api/subscriptions?isActive=true&page=1&limit=10');
const data = await response.json();
```

### 2. Get User Subscription Status

**Endpoint**: `GET /api/auth/user/subscription`  
**Authentication**: Required  
**Description**: Retrieves current user's subscription details and credit balance

#### Response Format

```json
{
  "success": true,
  "message": "User subscription retrieved successfully",
  "data": {
    "subscription": {
      "id": "user-subscription-uuid",
      "status": "active",
      "startDate": "2024-01-01T00:00:00.000Z",
      "endDate": "2024-02-01T00:00:00.000Z",
      "creditsAllocated": 200,
      "subscription": {
        "id": "subscription-uuid",
        "name": "Pro Plan",
        "description": "For growing businesses...",
        "price": 1000,
        "duration": 30,
        "interval": "monthly",
        "creditsIncluded": 200,
        "features": [
          "10 queues",
          "Priority support",
          "Advanced features"
        ]
      }
    },
    "credits": 150,
    "legacySubscriptionStatus": "active",
    "legacySubscriptionPlan": "pro",
    "datePaid": "2024-01-01T00:00:00.000Z"
  }
}
```

#### Example Request

```javascript
// Get current user's subscription status
const response = await fetch('/api/auth/user/subscription', {
  headers: {
    'Authorization': 'Bearer your-jwt-token'
  }
});
const data = await response.json();
```

### 3. Subscribe to a Plan

**Endpoint**: `POST /api/auth/subscriptions/subscribe`  
**Authentication**: Required  
**Description**: Subscribe user to a subscription plan

#### Request Body

```json
{
  "subscriptionId": "uuid-string",
  "paymentMethodDetails": {
    "paymentMethodId": "optional-payment-method-id"
  }
}
```

#### Response Format

```json
{
  "success": true,
  "message": "Successfully subscribed to plan",
  "data": {
    "subscription": {
      "id": "new-user-subscription-uuid",
      "userId": "user-uuid",
      "subscriptionId": "subscription-uuid",
      "status": "active",
      "startDate": "2024-01-01T00:00:00.000Z",
      "endDate": "2024-02-01T00:00:00.000Z",
      "creditsAllocated": 200,
      "subscription": {
        "id": "subscription-uuid",
        "name": "Pro Plan",
        "features": [
          "10 queues",
          "Priority support"
        ]
      }
    },
    "updatedCredits": 350
  }
}
```

#### Example Request

```javascript
// Subscribe to a plan
const response = await fetch('/api/auth/subscriptions/subscribe', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your-jwt-token'
  },
  body: JSON.stringify({
    subscriptionId: 'subscription-uuid-here'
  })
});
const data = await response.json();
```

## Error Responses

All endpoints return consistent error responses with appropriate HTTP status codes:

### Common Error Format

```json
{
  "success": false,
  "message": "Error description"
}
```

### Validation Error Format

```json
{
  "success": false,
  "message": "Invalid request body",
  "errors": {
    "subscriptionId": {
      "_errors": ["Invalid uuid"]
    }
  }
}
```

### Common HTTP Status Codes

| Status Code | Description |
|-------------|-------------|
| `200` | Success |
| `201` | Created (successful subscription) |
| `400` | Bad Request (validation errors) |
| `401` | Unauthorized (authentication required) |
| `404` | Not Found (subscription plan not found) |
| `500` | Internal Server Error |

## Business Logic

### Subscription Management

1. **Plan Creation**: Subscription plans are created via database seeding or admin interface
2. **User Subscription**: Users can subscribe to one active plan at a time
3. **Credit Allocation**: Credits are automatically allocated when subscribing
4. **Status Tracking**: Subscription status is tracked throughout the lifecycle

### Credit System Integration

- **Automatic Allocation**: Credits are added to user balance upon subscription
- **Existing Integration**: Works with existing credit deduction logic in appointments
- **Balance Tracking**: User credit balance is maintained in the User model

### Subscription Lifecycle

1. **Active**: User has an active subscription with valid dates
2. **Expired**: Subscription has passed its end date
3. **Cancelled**: User or admin cancelled the subscription
4. **Pending**: Subscription created but payment not confirmed

### Business Rules

- Users can only have one active subscription at a time
- One-time purchases don't have end dates
- Credits are allocated immediately upon successful subscription
- Subscription history is maintained for audit purposes
- Legacy subscription fields are updated for backward compatibility

## Usage Examples

### Example 1: Fetching Available Plans

```typescript
// TypeScript example for fetching subscription plans
interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  duration: number;
  interval: string;
  creditsIncluded: number;
  features: string[];
  isActive: boolean;
}

async function getSubscriptionPlans(): Promise<SubscriptionPlan[]> {
  try {
    const response = await fetch('/api/subscriptions?isActive=true');
    const result = await response.json();

    if (result.success) {
      return result.data.subscriptions;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('Failed to fetch subscription plans:', error);
    throw error;
  }
}

// Usage
const plans = await getSubscriptionPlans();
console.log('Available plans:', plans);
```

### Example 2: Checking User Subscription Status

```typescript
interface UserSubscriptionStatus {
  subscription: {
    id: string;
    status: string;
    startDate: string;
    endDate?: string;
    subscription: SubscriptionPlan;
  } | null;
  credits: number;
}

async function getUserSubscriptionStatus(token: string): Promise<UserSubscriptionStatus> {
  try {
    const response = await fetch('/api/auth/user/subscription', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const result = await response.json();

    if (result.success) {
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('Failed to fetch user subscription:', error);
    throw error;
  }
}

// Usage
const userStatus = await getUserSubscriptionStatus('your-jwt-token');
if (userStatus.subscription) {
  console.log(`User has ${userStatus.subscription.subscription.name} plan`);
  console.log(`Credits remaining: ${userStatus.credits}`);
} else {
  console.log('User has no active subscription');
}
```

### Example 3: Subscribing to a Plan

```typescript
interface SubscribeRequest {
  subscriptionId: string;
  paymentMethodDetails?: {
    paymentMethodId?: string;
  };
}

async function subscribeToplan(
  subscriptionId: string,
  token: string
): Promise<any> {
  try {
    const requestBody: SubscribeRequest = {
      subscriptionId
    };

    const response = await fetch('/api/auth/subscriptions/subscribe', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(requestBody)
    });

    const result = await response.json();

    if (result.success) {
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('Failed to subscribe to plan:', error);
    throw error;
  }
}

// Usage
try {
  const subscription = await subscribeToplan('plan-uuid', 'your-jwt-token');
  console.log('Successfully subscribed!');
  console.log(`New credit balance: ${subscription.updatedCredits}`);
} catch (error) {
  console.error('Subscription failed:', error.message);
}
```

### Example 4: Error Handling

```typescript
async function handleSubscriptionErrors() {
  try {
    const response = await fetch('/api/auth/subscriptions/subscribe', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer invalid-token'
      },
      body: JSON.stringify({
        subscriptionId: 'invalid-uuid'
      })
    });

    const result = await response.json();

    if (!result.success) {
      switch (response.status) {
        case 400:
          console.error('Validation error:', result.errors);
          break;
        case 401:
          console.error('Authentication required');
          // Redirect to login
          break;
        case 404:
          console.error('Subscription plan not found');
          break;
        default:
          console.error('Unexpected error:', result.message);
      }
    }
  } catch (error) {
    console.error('Network error:', error);
  }
}
```

## Implementation Details

### File Structure

```
app/
├── src/
│   ├── subscriptions/
│   │   ├── apiHandlers.ts          # API endpoint handlers
│   │   └── validation.ts           # Zod validation schemas
│   └── server/
│       └── scripts/
│           ├── dbSeeds.ts          # Main seed exports
│           └── subscriptionSeed.ts # Subscription plan seeds
├── schema.prisma                   # Database schema
├── main.wasp                      # Wasp configuration
└── docs/
    └── subscription-api-documentation.md
```

### Validation Schemas

The system uses Zod for request validation:

```typescript
// From app/src/subscriptions/validation.ts
export const SubscribeToSubscriptionSchema = z.object({
  subscriptionId: z.string().uuid(),
  paymentMethodDetails: z.object({
    paymentMethodId: z.string().optional(),
  }).optional(),
});

export const GetSubscriptionsQuerySchema = z.object({
  isActive: z.boolean().optional().default(true),
  page: z.number().int().positive().optional().default(1),
  limit: z.number().int().positive().max(100).optional().default(10),
});
```

### Integration with Wasp.js

The subscription system integrates seamlessly with Wasp.js patterns:

1. **API Routes**: Defined in `main.wasp` with proper entity access
2. **Authentication**: Uses Wasp's built-in auth system
3. **Database**: Leverages Prisma ORM with Wasp's entity system
4. **Error Handling**: Follows Wasp's HttpError patterns
5. **Validation**: Uses Zod schemas consistent with existing codebase

### Database Transactions

Critical operations use database transactions to ensure data consistency:

```typescript
// Example from the subscribe handler
const result = await context.entities.$transaction(async (tx: any) => {
  // Create user subscription record
  const userSubscription = await tx.userSubscription.create({...});

  // Allocate credits to user
  const updatedUser = await tx.user.update({...});

  return { userSubscription, updatedUser };
});
```

## Setup Instructions

### 1. Database Migration

Apply the new database schema changes:

```bash
# Navigate to your Wasp project directory
cd your-wasp-project

# Create and apply the migration
wasp db migrate-dev --name add_subscription_models
```

### 2. Seed Initial Data

Populate the database with initial subscription plans:

```bash
# Run the database seeding
wasp db seed
```

This will create the following subscription plans:
- **Hobby Plan**: $2.00/month, 50 credits, 3 queues
- **Pro Plan**: $10.00/month, 200 credits, 10 queues
- **Enterprise Plan**: $50.00/month, 1000 credits, unlimited queues
- **Credits Pack - Small**: $1.00 one-time, 10 credits
- **Credits Pack - Medium**: $5.00 one-time, 60 credits
- **Credits Pack - Large**: $10.00 one-time, 150 credits

### 3. Start the Development Server

```bash
# Start the Wasp development server
wasp start
```

### 4. Test the API Endpoints

#### Test Public Endpoint (No Auth Required)

```bash
# Get all subscription plans
curl -X GET "http://localhost:3001/api/subscriptions"

# Get subscription plans with pagination
curl -X GET "http://localhost:3001/api/subscriptions?page=1&limit=5"
```

#### Test Authenticated Endpoints

First, obtain a JWT token by logging in through your app, then:

```bash
# Get user subscription status
curl -X GET "http://localhost:3001/api/auth/user/subscription" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Subscribe to a plan
curl -X POST "http://localhost:3001/api/auth/subscriptions/subscribe" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"subscriptionId": "SUBSCRIPTION_UUID_HERE"}'
```

### 5. Verify Database Changes

Check that the new tables were created:

```sql
-- Connect to your database and verify tables exist
\dt subscription*
\dt user_subscription*

-- Check sample data
SELECT * FROM "Subscription";
SELECT * FROM "UserSubscription";
```

## Advanced Configuration

### Custom Subscription Plans

To add custom subscription plans, you can either:

1. **Modify the seed script** (`app/src/server/scripts/subscriptionSeed.ts`)
2. **Add plans programmatically** using the Prisma client
3. **Create an admin interface** for plan management

Example of adding a custom plan:

```typescript
// Add to subscriptionSeed.ts
{
  name: 'Custom Enterprise',
  description: 'Custom plan for large organizations',
  price: 10000, // $100.00
  duration: 365, // 1 year
  interval: 'yearly',
  creditsIncluded: 5000,
  features: JSON.stringify([
    'Unlimited everything',
    'Dedicated support',
    'Custom integrations'
  ]),
  isActive: true
}
```

### Integration with Payment Processors

The system is designed to integrate with existing payment processors:

```typescript
// In your payment webhook handler
async function handlePaymentSuccess(paymentData: any) {
  // Create or update UserSubscription
  await prisma.userSubscription.create({
    data: {
      userId: paymentData.userId,
      subscriptionId: paymentData.subscriptionId,
      status: 'active',
      startDate: new Date(),
      endDate: calculateEndDate(paymentData.duration),
      creditsAllocated: paymentData.credits,
      paymentProcessorSubscriptionId: paymentData.externalId
    }
  });

  // Update user credits
  await prisma.user.update({
    where: { id: paymentData.userId },
    data: {
      credits: { increment: paymentData.credits }
    }
  });
}
```

### Monitoring and Analytics

Track subscription metrics:

```sql
-- Active subscriptions by plan
SELECT s.name, COUNT(us.id) as active_subscriptions
FROM "Subscription" s
LEFT JOIN "UserSubscription" us ON s.id = us."subscriptionId"
WHERE us.status = 'active'
GROUP BY s.name;

-- Revenue by month
SELECT
  DATE_TRUNC('month', us."createdAt") as month,
  SUM(s.price) as revenue
FROM "UserSubscription" us
JOIN "Subscription" s ON us."subscriptionId" = s.id
WHERE us.status = 'active'
GROUP BY month
ORDER BY month DESC;

-- Credit usage patterns
SELECT
  AVG(credits) as avg_credits,
  COUNT(*) as user_count
FROM "User"
WHERE role = 'CUSTOMER';
```

## Troubleshooting

### Common Issues

1. **Migration Fails**
   ```bash
   # Reset database if needed (development only)
   wasp db reset
   wasp db migrate-dev --name add_subscription_models
   ```

2. **Seed Data Not Created**
   ```bash
   # Check if seed function is properly exported
   # Verify main.wasp includes the seed import
   wasp db seed --verbose
   ```

3. **API Returns 404**
   - Verify API routes are added to `main.wasp`
   - Check that entities are included in API definitions
   - Restart the development server

4. **Authentication Errors**
   - Ensure JWT token is valid and not expired
   - Check that user exists and has proper permissions
   - Verify Authorization header format: `Bearer <token>`

### Debugging Tips

1. **Enable Detailed Logging**
   ```typescript
   // Add to your API handlers
   console.log('Request body:', req.body);
   console.log('User context:', context.user);
   ```

2. **Database Query Debugging**
   ```typescript
   // Enable Prisma query logging in development
   // Add to your Prisma client configuration
   log: ['query', 'info', 'warn', 'error']
   ```

3. **Test with Postman/Insomnia**
   - Import the API endpoints into your preferred API testing tool
   - Create environment variables for base URL and auth tokens
   - Test all endpoints systematically

## Security Considerations

1. **Input Validation**: All inputs are validated using Zod schemas
2. **Authentication**: Protected endpoints require valid JWT tokens
3. **Authorization**: Users can only access their own subscription data
4. **SQL Injection**: Prevented by using Prisma ORM
5. **Rate Limiting**: Consider implementing rate limiting for production

## Performance Optimization

1. **Database Indexes**: Added indexes on frequently queried fields
2. **Pagination**: Implemented pagination for subscription lists
3. **Caching**: Consider caching subscription plans (rarely change)
4. **Database Queries**: Optimized with proper includes and selects

---

## Support and Maintenance

For questions or issues with the subscription system:

1. Check this documentation first
2. Review the implementation files in `app/src/subscriptions/`
3. Test endpoints using the provided examples
4. Check database schema and relationships
5. Verify integration with existing payment systems

This subscription and credits API system provides a solid foundation for managing user subscriptions and can be extended based on specific business requirements.
