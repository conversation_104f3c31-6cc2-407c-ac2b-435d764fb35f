# Provider Onboarding Setup API

This document describes the comprehensive API endpoint for provider onboarding setup that handles all multi-step form data in a single request.

## Overview

The provider onboarding setup API allows mobile apps to submit all setup data from a multi-step form in one comprehensive request, minimizing API calls and ensuring data consistency through database transactions.

## Endpoint

### Provider Complete Setup

**Endpoint:** `POST /api/auth/provider/complete-setup`

**Description:** Processes all provider onboarding setup data from a multi-step form and updates the provider's profile with business information, locations, services, and queues.

**Authentication:** Required - Provider must be logged in

**Headers:**
```
Content-Type: application/json
Authorization: Bearer {session_token}
```

**Request Body Structure:**

```json
{
  "businessInfo": {
    "businessName": "John's Professional Services",
    "bio": "Professional healthcare services with 10+ years experience",
    "shortName": "JPS",
    "logoUrl": "https://example.com/logo.jpg",
    "phones": {
      "fixedLine": "+213123456789",
      "fax": "+213123456790",
      "mobile": "+213654321098"
    }
  },
  "locations": [
    {
      "name": "Main Clinic",
      "shortName": "Main",
      "country": "Algeria",
      "city": "Algiers",
      "timezone": "Africa/Algiers",
      "address": "123 Main Street, Algiers",
      "coordinates": {
        "latitude": 36.7538,
        "longitude": 3.0588
      },
      "openingHours": [
        {
          "dayOfWeek": "Monday",
          "isActive": true,
          "hours": [
            {
              "timeFrom": "09:00",
              "timeTo": "12:00"
            },
            {
              "timeFrom": "14:00",
              "timeTo": "18:00"
            }
          ]
        }
      ]
    }
  ],
  "services": [
    {
      "title": "General Consultation",
      "duration": 30,
      "price": 2500.0,
      "pointsRequirements": 1,
      "isPublic": true,
      "deliveryType": "at_location",
      "servedRegions": []
    },
    {
      "title": "Home Visit",
      "duration": 60,
      "price": 5000.0,
      "pointsRequirements": 2,
      "isPublic": true,
      "deliveryType": "at_customer",
      "servedRegions": ["16", "31", "42"]
    }
  ],
  "queues": [
    {
      "name": "General Queue",
      "locationIndex": 0,
      "serviceIndices": [0, 1],
      "customOpeningHours": [
        {
          "dayOfWeek": "Monday",
          "isActive": true,
          "hours": [
            {
              "timeFrom": "09:00",
              "timeTo": "17:00"
            }
          ]
        }
      ]
    }
  ]
}
```

## Validation Rules

### Business Information
- `businessName`: Required, non-empty string
- `bio`: Required, non-empty string (description)
- `shortName`: Optional string
- `logoUrl`: Optional, valid URL format
- `phones`: Object with at least one phone number required
  - `fixedLine`: Optional string
  - `fax`: Optional string  
  - `mobile`: Optional string

### Locations
- At least one location required
- `name`: Required, non-empty string
- `country`: Required, non-empty string
- `city`: Required, non-empty string
- `timezone`: Required, non-empty string
- `address`: Required, non-empty string
- `coordinates`: Optional object with latitude/longitude
- `openingHours`: Array of opening hours per day
  - `dayOfWeek`: Required string
  - `isActive`: Boolean (default: true)
  - `hours`: Array of time intervals
    - `timeFrom`/`timeTo`: Required, HH:mm format

### Services
- At least one service required
- `title`: Required, non-empty string
- `duration`: Required, positive integer (minutes)
- `price`: Optional, positive number
- `pointsRequirements`: Required, positive integer
- `isPublic`: Boolean (default: true)
- `deliveryType`: Required, enum: "at_location", "at_customer", "both"
- `servedRegions`: Optional array of region/wilaya IDs (for "at_customer")

### Queues
- At least one queue required
- `name`: Required, non-empty string
- `locationIndex`: Required, valid index in locations array
- `serviceIndices`: Required, array of valid indices in services array
- `customOpeningHours`: Optional, same format as location opening hours

## Response

### Success Response (200)

```json
{
  "message": "Provider setup completed successfully",
  "provider": {
    "id": 123,
    "title": "John's Professional Services",
    "presentation": "Professional healthcare services with 10+ years experience"
  },
  "summary": {
    "locationsCreated": 1,
    "servicesCreated": 2,
    "queuesCreated": 1
  }
}
```

### Error Responses

**400 - Validation Error:**
```json
{
  "message": "Invalid request body",
  "errors": {
    "businessInfo": {
      "businessName": {
        "_errors": ["Required"]
      }
    },
    "services": {
      "_errors": ["At least one service is required"]
    }
  }
}
```

**401 - Not Authenticated:**
```json
{
  "message": "User not authenticated."
}
```

**404 - Provider Profile Not Found:**
```json
{
  "message": "Provider profile not found for this user. Please register as a provider first."
}
```

**400 - Already Setup:**
```json
{
  "message": "Provider setup has already been completed"
}
```

## Implementation Details

### Database Operations

The endpoint performs the following operations in a single transaction:

1. **Update SProvider** with business information
2. **Create SProvidingPlace(s)** for each location
3. **Create Address records** for locations with coordinates
4. **Create Opening/OpeningHours** for location schedules
5. **Create Service records** for each service
6. **Create Queue records** for each queue
7. **Create QueueOpening/QueueOpeningHours** for custom queue schedules
8. **Associate services with queues** through many-to-many relations

### Transaction Safety

All operations are wrapped in a database transaction to ensure:
- Data consistency across all related tables
- Rollback on any failure
- Atomic completion of the entire setup process

### Error Handling

- Comprehensive input validation using Zod schemas
- Index validation for location and service references
- Database constraint validation
- Proper HTTP status codes and error messages

## Usage Examples

### cURL Example

```bash
curl -X POST http://localhost:5400/api/auth/provider/complete-setup \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {session_token}" \
  -d '{
    "businessInfo": {
      "businessName": "Test Clinic",
      "bio": "Professional medical services",
      "phones": {
        "mobile": "+213123456789"
      }
    },
    "locations": [{
      "name": "Main Location",
      "country": "Algeria",
      "city": "Algiers", 
      "timezone": "Africa/Algiers",
      "address": "123 Test Street",
      "openingHours": [{
        "dayOfWeek": "Monday",
        "isActive": true,
        "hours": [{"timeFrom": "09:00", "timeTo": "17:00"}]
      }]
    }],
    "services": [{
      "title": "Consultation",
      "duration": 30,
      "pointsRequirements": 1,
      "isPublic": true,
      "deliveryType": "at_location"
    }],
    "queues": [{
      "name": "Main Queue",
      "locationIndex": 0,
      "serviceIndices": [0]
    }]
  }'
```

### JavaScript/TypeScript Example

```typescript
const setupData = {
  businessInfo: {
    businessName: "Professional Services",
    bio: "Quality healthcare services",
    phones: { mobile: "+213123456789" }
  },
  locations: [/* location data */],
  services: [/* service data */],
  queues: [/* queue data */]
};

const response = await fetch('/api/auth/provider/complete-setup', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${sessionToken}`
  },
  body: JSON.stringify(setupData)
});

const result = await response.json();
```

## Integration Notes

- Designed for mobile apps with multi-step onboarding forms
- Minimizes API requests by accepting all data in one call
- Supports partial data (some fields are optional)
- Provides detailed validation feedback for form correction
- Returns summary of created entities for confirmation
- Handles complex relationships between locations, services, and queues

## Database Migration Complete ✅

The database migration has been successfully applied with the following new fields:
- `SProvider.isSetupComplete` (Boolean) - Tracks setup completion status
- `Service.price` (Float) - Optional pricing for services
- `Service.isPublic` (Boolean) - Visibility control for services
- `Service.deliveryType` (String) - Location preferences ("at_location", "at_customer", "both")
- `Service.servedRegions` (Text) - JSON array of served regions for customer location services
