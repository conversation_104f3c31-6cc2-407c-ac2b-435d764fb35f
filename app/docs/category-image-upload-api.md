# Provider Category Image Upload API

This document describes the API endpoints for managing images for provider categories, following the same pattern as the profile picture upload process.

## Overview

The category image upload process follows the same sequence as profile picture uploads:

1. **Request Upload URL**: Client requests a pre-signed upload URL
2. **Direct Upload**: Client uploads the image directly to cloud storage
3. **Database Update**: The category record is updated with the image reference

## API Endpoints

### 1. Upload Category Image

**Endpoint**: `POST /api/auth/admin/provider-categories/:categoryId/image`

**Authentication**: Admin access required

**Request Body**:
```json
{
  "fileName": "category-image.jpg",
  "fileType": "image/jpeg"
}
```

**Supported File Types**:
- `image/jpeg`
- `image/png`

**Response** (200 OK):
```json
{
  "success": true,
  "message": "Category image upload URL generated successfully",
  "data": {
    "uploadUrl": "https://s3.amazonaws.com/bucket/...",
    "uploadFields": {
      "key": "files/user-id/filename",
      "Content-Type": "image/jpeg",
      "policy": "...",
      "signature": "..."
    },
    "file": {
      "id": "file-uuid",
      "name": "category-image.jpg",
      "type": "image/jpeg",
      "key": "files/user-id/category-image.jpg"
    },
    "category": {
      "id": 1,
      "title": "Doctors",
      "imageId": "file-uuid",
      "parentId": null,
      "_count": {
        "providers": 5,
        "children": 2
      }
    }
  }
}
```

### 2. Get Category Image

**Endpoint**: `GET /api/auth/admin/provider-categories/:categoryId/image`

**Authentication**: Admin access required

**Response** (200 OK):
```json
{
  "success": true,
  "message": "Category image retrieved successfully",
  "data": {
    "category": {
      "id": 1,
      "title": "Doctors",
      "imageId": "file-uuid"
    },
    "image": {
      "id": "file-uuid",
      "name": "category-image.jpg",
      "type": "image/jpeg",
      "key": "files/user-id/category-image.jpg",
      "uploadUrl": "https://s3.amazonaws.com/bucket/...",
      "createdAt": "2025-01-07T18:00:00.000Z"
    }
  }
}
```

### 3. Remove Category Image

**Endpoint**: `DELETE /api/auth/admin/provider-categories/:categoryId/image`

**Authentication**: Admin access required

**Response** (200 OK):
```json
{
  "success": true,
  "message": "Category image removed successfully",
  "data": {
    "category": {
      "id": 1,
      "title": "Doctors",
      "imageId": null,
      "parentId": null,
      "_count": {
        "providers": 5,
        "children": 2
      }
    }
  }
}
```

### 4. Get Single Category (with detailed info)

**Endpoint**: `GET /api/auth/admin/provider-categories/:categoryId`

**Authentication**: Admin access required

**Response** (200 OK):
```json
{
  "success": true,
  "message": "Provider category retrieved successfully",
  "data": {
    "id": 1,
    "title": "Doctors",
    "parentId": null,
    "imageId": "file-uuid",
    "parent": null,
    "children": [
      {
        "id": 2,
        "title": "General Practitioners",
        "imageId": null,
        "_count": {
          "providers": 3,
          "children": 0
        }
      }
    ],
    "image": {
      "id": "file-uuid",
      "name": "doctors-category.jpg",
      "type": "image/jpeg",
      "key": "files/user-id/doctors-category.jpg",
      "uploadUrl": "https://s3.amazonaws.com/bucket/...",
      "createdAt": "2025-01-07T18:00:00.000Z"
    },
    "providers": [
      {
        "id": 1,
        "title": "Dr. Smith's Clinic",
        "isVerified": true,
        "user": {
          "firstName": "John",
          "lastName": "Smith",
          "email": "<EMAIL>"
        }
      }
    ],
    "_count": {
      "providers": 5,
      "children": 2
    }
  }
}
```

## Usage Example (JavaScript)

```javascript
// 1. Request upload URL
const uploadResponse = await fetch('/api/auth/admin/provider-categories/1/image', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your-admin-token'
  },
  body: JSON.stringify({
    fileName: 'doctors-category.jpg',
    fileType: 'image/jpeg'
  })
});

const { uploadUrl, uploadFields, file } = uploadResponse.data.data;

// 2. Upload file directly to S3
const formData = new FormData();
Object.entries(uploadFields).forEach(([key, value]) => {
  formData.append(key, value);
});
formData.append('file', selectedFile);

await fetch(uploadUrl, {
  method: 'POST',
  body: formData
});

console.log('Image uploaded successfully!', file);
```

## Error Responses

**401 Unauthorized**:
```json
{
  "success": false,
  "message": "Authentication required"
}
```

**403 Forbidden**:
```json
{
  "success": false,
  "message": "Admin access required"
}
```

**404 Not Found**:
```json
{
  "success": false,
  "message": "Provider category not found"
}
```

**400 Bad Request**:
```json
{
  "success": false,
  "message": "Invalid request data. Only image files are allowed for category images.",
  "errors": {
    "fileType": {
      "_errors": ["Invalid enum value..."]
    }
  }
}
```

## Database Schema Changes

The following changes were made to support category images:

```sql
-- Add image relation to ProviderCategory
ALTER TABLE "ProviderCategory" ADD COLUMN "imageId" TEXT;
ALTER TABLE "ProviderCategory" ADD CONSTRAINT "ProviderCategory_imageId_fkey" 
  FOREIGN KEY ("imageId") REFERENCES "File"("id") ON DELETE SET NULL;
ALTER TABLE "ProviderCategory" ADD CONSTRAINT "ProviderCategory_imageId_key" 
  UNIQUE ("imageId");

-- Update File model to include category image relation
-- (This is handled automatically by Prisma)
```

## Integration with Existing Category APIs

All existing category management APIs now include image information:

- `GET /api/auth/admin/provider-categories` - Returns all categories with image data
- `GET /api/auth/admin/provider-categories/:categoryId` - Returns single category with detailed info
- `POST /api/auth/admin/provider-categories` - Creates category (image can be added later)
- `PUT /api/auth/admin/provider-categories/:categoryId` - Updates category (preserves image)
- `DELETE /api/auth/admin/provider-categories/:categoryId` - Deletes category (image reference removed)

The image data is included in the response as:
```json
{
  "id": 1,
  "title": "Doctors",
  "parentId": null,
  "imageId": "file-uuid",
  "image": {
    "id": "file-uuid",
    "name": "doctors-category.jpg",
    "type": "image/jpeg",
    "key": "files/user-id/doctors-category.jpg"
  }
}
```
