# Provider Authentication API Endpoints

This document describes the new REST API endpoints for provider registration and login that wrap existing Wasp actions.

## Overview

Two new API endpoints have been added to handle provider-specific authentication:

1. **Provider Registration** - Creates a new user account and provider profile
2. **Provider Login** - Authenticates providers and returns provider-specific data

Both endpoints follow the established `/api/auth/` namespace pattern and use existing Wasp actions under the hood.

## Endpoints

### 1. Provider Registration

**Endpoint:** `POST /api/auth/provider/register`

**Description:** Creates a new user account and associated provider profile in a single request.

**Authentication:** None required (public endpoint)

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword123",
  "firstName": "John",
  "lastName": "Doe",
  "providerCategoryId": 1,
  "businessName": "John's Professional Services", // optional
  "phone": "+**********" // optional
}
```

**Validation Rules:**
- `email`: Valid email format, required
- `password`: Minimum 8 characters, required
- `firstName`: Non-empty string, required
- `lastName`: Non-empty string, required
- `providerCategoryId`: Positive integer, required
- `businessName`: Non-empty string, optional
- `phone`: String, optional

**Success Response (201):**
```json
{
  "message": "Provider account created successfully",
  "user": {
    "id": "user-uuid",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "role": "CUSTOMER"
  },
  "provider": {
    "id": 1,
    "userId": "user-uuid",
    "providerCategoryId": 1,
    "title": "John's Professional Services",
    "phone": "+**********"
  }
}
```

**Error Responses:**
- `400`: Invalid request body or validation errors
- `409`: User with email already exists
- `500`: Server error during account creation

### 2. Provider Login

**Endpoint:** `POST /api/auth/provider/login`

**Description:** Authenticates a provider using email/phone and password, returns session and provider data.

**Authentication:** None required (public endpoint)

**Request Body:**
```json
{
  "identifier": "<EMAIL>", // or phone number
  "password": "securepassword123"
}
```

**Validation Rules:**
- `identifier`: Non-empty string (email or phone), required
- `password`: Non-empty string, required

**Success Response (200):**
```json
{
  "sessionId": "session-uuid",
  "user": {
    "id": "user-uuid",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "role": "CUSTOMER"
  },
  "provider": {
    "id": 1,
    "userId": "user-uuid",
    "title": "John's Professional Services",
    "phone": "+**********",
    "providerCategoryId": 1,
    "category": {
      "id": 1,
      "title": "Category Name"
    }
  }
}
```

**Error Responses:**
- `400`: Invalid request body
- `401`: Invalid credentials
- `403`: Account not registered as provider or email/phone not verified
- `500`: Server error during authentication

## Implementation Details

### Actions Used

The API handlers wrap these existing Wasp actions:

1. **`createCustomerUser`** - Creates the user account with email/password authentication
2. **Direct Prisma operations** - Creates the provider profile linked to the user
3. **`loginWithPhoneOrEmail`** - Handles authentication and session creation

### Database Operations

**Registration Flow:**
1. Create user account using `createCustomerUser`
2. Create provider profile directly using Prisma
3. Update provider with additional details (business name, phone)
4. Send email verification (handled by `createCustomerUser`)

**Login Flow:**
1. Authenticate user using `loginWithPhoneOrEmail`
2. Verify user has associated provider profile
3. Check email/phone verification status
4. Return session ID and provider data

### Error Handling

All endpoints use consistent error handling:
- Input validation with Zod schemas
- HTTP status codes following REST conventions
- Structured error responses with descriptive messages
- Proper logging for debugging

### Security Features

- Password hashing (handled by Wasp auth system)
- Email verification requirement
- Session-based authentication
- Input sanitization and validation
- SQL injection protection (Prisma ORM)

## Usage Examples

### Registration with cURL
```bash
curl -X POST http://localhost:3001/api/auth/provider/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "securepassword123",
    "firstName": "John",
    "lastName": "Doe",
    "providerCategoryId": 1,
    "businessName": "John'\''s Services",
    "phone": "+**********"
  }'
```

### Login with cURL
```bash
curl -X POST http://localhost:3001/api/auth/provider/login \
  -H "Content-Type: application/json" \
  -d '{
    "identifier": "<EMAIL>",
    "password": "securepassword123"
  }'
```

### JavaScript/TypeScript Usage
```typescript
// Registration
const registerResponse = await fetch('/api/auth/provider/register', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'securepassword123',
    firstName: 'John',
    lastName: 'Doe',
    providerCategoryId: 1,
    businessName: 'John\'s Services'
  })
});

// Login
const loginResponse = await fetch('/api/auth/provider/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    identifier: '<EMAIL>',
    password: 'securepassword123'
  })
});

const { sessionId, user, provider } = await loginResponse.json();
```

## Integration Notes

- These endpoints are designed for mobile apps and external integrations
- Session ID can be used for subsequent authenticated requests
- Provider category IDs must exist in the database before registration
- Email verification is required before login (handled automatically)
- Phone verification is required if using phone number for login

## Testing

Use the cURL examples above or any HTTP client to test the endpoints. Make sure to:

1. Have a valid provider category ID in your database
2. Use a unique email address for registration
3. Verify the email before attempting login (check email for verification link)
4. Use the returned session ID for subsequent authenticated requests
