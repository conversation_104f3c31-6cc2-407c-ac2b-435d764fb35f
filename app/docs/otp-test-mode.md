# OTP Test Mode Documentation

## Overview

The OTP Test Mode is a development and testing feature that allows the Provider Mobile API to return the generated OTP code directly in the API response. This eliminates the need to intercept SMS messages or emails during testing, making the test suite more reliable and faster.

## Security

⚠️ **IMPORTANT SECURITY NOTICE** ⚠️

- **Test mode is ONLY available in development/test environments**
- **Test mode is automatically DISABLED in production** (`NODE_ENV=production`)
- **OTP codes are never returned in production for security reasons**

## Configuration

### Environment Variable

Add the following environment variable to enable test mode:

```bash
# In .env.server or environment configuration
OTP_TEST_MODE=true
```

### Requirements

Test mode will only work when:
1. `OTP_TEST_MODE=true` is set
2. `NODE_ENV` is NOT set to `production`

## API Behavior

### Normal Mode (Production/Test Mode Disabled)

**Request:**
```bash
POST /api/auth/request-email-otp
{
  "email": "<EMAIL>",
  "firstName": "<PERSON>",
  "lastName": "Doe",
  "password": "password123",
  "isProviderRegistration": true,
  "providerCategoryId": 1,
  "businessName": "Test Business"
}
```

**Response:**
```json
{
  "message": "OTP sent successfully to your email address. Please verify to complete provider registration.",
  "providerContext": {
    "isProviderRegistration": true,
    "providerCategoryId": 1,
    "businessName": "Test Business"
  }
}
```

### Test Mode Enabled

**Request:** (Same as above)

**Response:**
```json
{
  "message": "OTP sent successfully to your email address. Please verify to complete provider registration.",
  "otp": "123456",
  "providerContext": {
    "isProviderRegistration": true,
    "providerCategoryId": 1,
    "businessName": "Test Business"
  }
}
```

## Supported Endpoints

Test mode works with the following provider OTP endpoints:

### 1. Provider Phone OTP
- **Endpoint:** `POST /api/auth/request-otp`
- **Condition:** `isProviderRegistration: true`
- **Action:** `requestProviderPhoneOtp`

### 2. Provider Email OTP
- **Endpoint:** `POST /api/auth/request-email-otp`
- **Condition:** `isProviderRegistration: true`
- **Action:** `requestProviderEmailOtp`

## Usage in Tests

### Manual Testing

```bash
# Enable test mode
export OTP_TEST_MODE=true

# Start the development server
cd app && wasp start

# Make OTP request
curl -X POST "http://localhost:3001/api/auth/request-email-otp" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "firstName": "Test",
    "lastName": "User",
    "password": "password123",
    "isProviderRegistration": true,
    "providerCategoryId": 1,
    "businessName": "Test Business"
  }'

# Response will include: "otp": "123456"
```

### Integration Tests

```javascript
// In test files
describe('Provider Registration with Test Mode', () => {
  test('should return OTP in test mode', async () => {
    const response = await axios.post('/api/auth/request-email-otp', {
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      password: 'password123',
      isProviderRegistration: true,
      providerCategoryId: 1,
      businessName: 'Test Business'
    });

    // Check if test mode is enabled
    if (process.env.OTP_TEST_MODE === 'true' && process.env.NODE_ENV !== 'production') {
      expect(response.data).toHaveProperty('otp');
      expect(response.data.otp).toMatch(/^\d{6}$/);
      
      // Use the OTP for verification
      const otp = response.data.otp;
      // ... continue with verification
    }
  });
});
```

### Test Helper Integration

```javascript
// Updated test helper method
async function registerTestProvider(providerData) {
  // Request OTP
  const otpResponse = await requestProviderOtp(providerData);
  
  // Get OTP from response if test mode is enabled
  const otp = otpResponse.otp || '123456'; // Fallback to mock OTP
  
  // Complete registration
  const registrationResponse = await verifyOtpAndRegister({
    otp: otp,
    identifier: providerData.email,
    // ... other fields
  });
  
  return registrationResponse.data;
}
```

## Logging

When test mode is enabled, you'll see warning logs:

```
[OTP_TEST_MODE] Test mode is enabled - OTP codes will be returned in API responses
```

When test mode is attempted in production:

```
[OTP_TEST_MODE] Test mode is disabled in production for security reasons
```

## Best Practices

1. **Always check environment** before expecting OTP in response
2. **Provide fallback** to mock OTP when test mode is disabled
3. **Never enable in production** environments
4. **Use for integration tests** to avoid SMS/email dependencies
5. **Document test scenarios** that rely on test mode

## Troubleshooting

### OTP Not Returned

**Possible causes:**
- `OTP_TEST_MODE` not set to `'true'`
- `NODE_ENV` is set to `'production'`
- Using customer OTP endpoints instead of provider endpoints
- `isProviderRegistration` not set to `true`

**Solution:**
```bash
# Check environment variables
echo $OTP_TEST_MODE  # Should be 'true'
echo $NODE_ENV       # Should NOT be 'production'

# Verify request includes provider context
{
  "isProviderRegistration": true,
  // ... other fields
}
```

### Test Mode Not Working

1. Restart the development server after setting `OTP_TEST_MODE`
2. Check server logs for test mode warning messages
3. Verify the endpoint is a provider OTP endpoint
4. Ensure request includes `isProviderRegistration: true`

## Implementation Details

The test mode is implemented in:
- `app/src/auth/actions.ts` - OTP generation functions
- `app/src/auth/apiHandlers.ts` - API request handlers
- Environment variable: `OTP_TEST_MODE`

The feature adds an optional `otp` field to the response when test mode conditions are met, maintaining backward compatibility with existing clients.
