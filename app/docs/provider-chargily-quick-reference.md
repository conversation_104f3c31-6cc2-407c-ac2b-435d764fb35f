# Provider Dashboard - Chargily Pay Quick Reference

## 🚀 Quick Start

### 1. Check Available Payment Methods
```bash
GET /api/auth/providers/payment/methods
Authorization: Bearer {provider_token}
```

### 2. Create Subscription Checkout
```bash
POST /api/auth/providers/payment/checkout
Content-Type: application/json
Authorization: Bearer {provider_token}

{
  "planId": "provider_pro",
  "paymentProcessor": "chargily",
  "paymentMethod": "edahabia"
}
```

### 3. Check Payment Status
```bash
GET /api/auth/providers/payment/status
Authorization: Bearer {provider_token}
```

## 💳 Payment Methods

### EDAHABIA (Algerie Post)
- **Method ID**: `edahabia`
- **Currency**: DZD
- **Test Card**: `0000 0000 0000 0001`

### CIB (SATIM)
- **Method ID**: `cib`
- **Currency**: DZD
- **Test Card**: `4000 0000 0000 0002`

## 📋 Provider Plans

| Plan | Price | Appointments | Features |
|------|-------|--------------|----------|
| `provider_basic` | 2,000 DZD | 100/month | Basic analytics |
| `provider_pro` | 5,000 DZD | 1,000/month | Advanced analytics, Multi-location |
| `provider_enterprise` | 10,000 DZD | Unlimited | API access, White-label |

## 🔗 Key Endpoints

### Payment Management
- `GET /api/auth/providers/payment/methods` - Available payment methods
- `POST /api/auth/providers/payment/checkout` - Create checkout session
- `GET /api/auth/providers/payment/status` - Payment status & history

### Payment Links
- `POST /api/auth/providers/payment/payment-links` - Create payment link
- `GET /api/auth/providers/payment/payment-links` - List payment links

### Analytics
- `GET /api/auth/providers/payment/analytics` - Revenue analytics

### Customer Management
- `POST /api/auth/providers/payment/customers` - Create customer

## 📊 Response Format

All endpoints return:
```json
{
  "success": true|false,
  "message": "Description",
  "data": { ... }
}
```

## 🔔 Webhook Events

- `provider.subscription.created` - New provider subscription
- `provider.payment.succeeded` - Successful payment
- `provider.payment.failed` - Failed payment

## 🧪 Test Environment

**Base URL**: `https://dapi-test.adscloud.org:8443`

**Test Provider Account**:
- Email: `<EMAIL>`
- Password: `TestProvider123!`

**Test Cards**:
- EDAHABIA: `0000 0000 0000 0001`
- CIB: `4000 0000 0000 0002`
- PIN: `1234`

## 🔧 Error Codes

| Code | Description |
|------|-------------|
| 400 | Invalid request data |
| 401 | Authentication required |
| 403 | Insufficient permissions |
| 404 | Resource not found |
| 500 | Server error |

## 📱 Integration Example

```javascript
// Provider subscription checkout
async function createProviderCheckout(planId) {
  try {
    const response = await fetch('/api/auth/providers/payment/checkout', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${providerToken}`
      },
      body: JSON.stringify({
        planId: planId,
        paymentProcessor: 'chargily',
        paymentMethod: 'edahabia'
      })
    });
    
    const result = await response.json();
    
    if (result.success) {
      // Redirect to Chargily checkout
      window.location.href = result.data.checkoutUrl;
    } else {
      console.error('Checkout failed:', result.message);
    }
  } catch (error) {
    console.error('Error creating checkout:', error);
  }
}

// Check provider payment status
async function getProviderPaymentStatus() {
  try {
    const response = await fetch('/api/auth/providers/payment/status', {
      headers: {
        'Authorization': `Bearer ${providerToken}`
      }
    });
    
    const result = await response.json();
    
    if (result.success) {
      const provider = result.data.provider;
      console.log('Subscription:', provider.subscriptionPlan);
      console.log('Status:', provider.subscriptionStatus);
      console.log('Credits:', provider.credits);
    }
  } catch (error) {
    console.error('Error fetching status:', error);
  }
}
```
