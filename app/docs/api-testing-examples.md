# Provider Mobile API - Testing Examples

## 🧪 API Testing Guide

This document provides example HTTP requests for testing all Provider Mobile API endpoints.

**Base URL**: `https://dapi-test.adscloud.org:8443` (Development) | `https://dapi.adscloud.org` (Production)
**Authentication**: All endpoints require JWT token in Authorization header

## 🔐 Authentication Setup

First, obtain a JWT token through the authentication endpoints:

```bash
# Login to get JWT token
curl -X POST https://dapi-test.adscloud.org:8443/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

Use the returned token in all subsequent requests:
```bash
Authorization: Bearer <your_jwt_token>
```

## 📋 API Endpoint Examples

### **1. Provider Profile Management**

#### Get Provider Profile
```bash
curl -X GET https://dapi-test.adscloud.org:8443/api/auth/providers/profile \
  -H "Authorization: Bearer <token>"
```

#### Update Provider Profile
```bash
curl -X PUT https://dapi-test.adscloud.org:8443/api/auth/providers/profile \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "businessName": "Updated Business Name",
    "description": "Updated business description",
    "categoryId": 1
  }'
```

### **2. Location Management**

#### Get All Locations
```bash
curl -X GET https://dapi-test.adscloud.org:8443/api/auth/providers/locations \
  -H "Authorization: Bearer <token>"
```

#### Create New Location
```bash
curl -X POST https://dapi-test.adscloud.org:8443/api/auth/providers/locations \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Main Office",
    "description": "Primary business location",
    "address": {
      "street": "123 Main St",
      "city": "New York",
      "state": "NY",
      "postalCode": "10001",
      "country": "USA",
      "latitude": 40.7128,
      "longitude": -74.0060
    }
  }'
```

#### Update Location
```bash
curl -X PUT https://dapi-test.adscloud.org:8443/api/auth/providers/locations/1 \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Updated Location Name",
    "isActive": true
  }'
```

#### Delete Location
```bash
curl -X DELETE https://dapi-test.adscloud.org:8443/api/auth/providers/locations/1 \
  -H "Authorization: Bearer <token>"
```

### **3. Service Management**

#### Get All Services
```bash
curl -X GET https://dapi-test.adscloud.org:8443/api/auth/providers/services \
  -H "Authorization: Bearer <token>"
```

#### Create New Service
```bash
curl -X POST https://dapi-test.adscloud.org:8443/api/auth/providers/services \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Haircut",
    "description": "Professional haircut service",
    "duration": 30,
    "price": 25.00,
    "color": "#FF5722",
    "categoryId": 1
  }'
```

#### Update Service
```bash
curl -X PUT https://dapi-test.adscloud.org:8443/api/auth/providers/services/1 \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Premium Haircut",
    "price": 35.00,
    "isActive": true
  }'
```

### **4. Queue Management**

#### Get All Queues
```bash
curl -X GET https://dapi-test.adscloud.org:8443/api/auth/providers/queues \
  -H "Authorization: Bearer <token>"
```

#### Create New Queue
```bash
curl -X POST https://dapi-test.adscloud.org:8443/api/auth/providers/queues \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Main Queue",
    "locationId": 1,
    "serviceIds": [1, 2, 3]
  }'
```

#### Get Queues by Location
```bash
curl -X GET https://dapi-test.adscloud.org:8443/api/auth/providers/locations/1/queues \
  -H "Authorization: Bearer <token>"
```

#### Assign Service to Queue
```bash
curl -X POST https://dapi-test.adscloud.org:8443/api/auth/providers/queues/1/services \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "serviceId": 4
  }'
```

### **5. Schedule Management**

#### Get All Schedules
```bash
curl -X GET https://dapi-test.adscloud.org:8443/api/auth/providers/schedules \
  -H "Authorization: Bearer <token>"
```

#### Create New Schedule
```bash
curl -X POST https://dapi-test.adscloud.org:8443/api/auth/providers/schedules \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "dayOfWeek": 1,
    "startTime": "09:00",
    "endTime": "17:00",
    "locationId": 1
  }'
```

### **6. Customer Management**

#### Get All Customers
```bash
curl -X GET "https://dapi-test.adscloud.org:8443/api/auth/providers/customers?page=1&limit=20" \
  -H "Authorization: Bearer <token>"
```

#### Create New Customer
```bash
curl -X POST https://dapi-test.adscloud.org:8443/api/auth/providers/customers \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "phoneNumber": "+**********"
  }'
```

#### Get Customer Details
```bash
curl -X GET https://dapi-test.adscloud.org:8443/api/auth/providers/customers/customer_id \
  -H "Authorization: Bearer <token>"
```

### **7. Appointment Management**

#### Get All Appointments
```bash
curl -X GET "https://dapi-test.adscloud.org:8443/api/auth/providers/appointments?status=pending&page=1&limit=20" \
  -H "Authorization: Bearer <token>"
```

#### Create New Appointment
```bash
curl -X POST https://dapi-test.adscloud.org:8443/api/auth/providers/appointments \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "customerId": "9acde11b-5d76-4962-94d0-f1b69c85d99d",
    "serviceId": 1,
    "queueId": 1,
    "expectedStartTime": "2024-01-15T10:00:00Z",
    "notes": "Regular appointment"
  }'
```

#### Update Appointment Status
```bash
curl -X PUT https://dapi-test.adscloud.org:8443/api/auth/providers/appointments/1/status \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "confirmed",
    "notes": "Appointment confirmed"
  }'
```

### **8. Reschedule Management**

#### Get Reschedule Requests
```bash
curl -X GET "https://dapi-test.adscloud.org:8443/api/auth/providers/reschedules?status=pending" \
  -H "Authorization: Bearer <token>"
```

#### Create Reschedule Request
```bash
curl -X POST https://dapi-test.adscloud.org:8443/api/auth/providers/appointments/1/reschedule \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "newStartTime": "2024-01-16T14:00:00Z",
    "reason": "Schedule conflict",
    "notifyCustomer": true
  }'
```

#### Respond to Reschedule Request
```bash
curl -X PUT https://dapi-test.adscloud.org:8443/api/auth/providers/reschedules/1/respond \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "response": "approve",
    "notes": "Approved reschedule request",
    "notifyCustomer": true
  }'
```

## 🔍 Testing Scenarios

### **Complete Workflow Test**
1. **Login** → Get JWT token
2. **Get Profile** → Verify provider data
3. **Create Location** → Add business location
4. **Create Services** → Add business services
5. **Create Queue** → Set up service queue
6. **Create Schedule** → Set working hours
7. **Create Customer** → Add customer record
8. **Create Appointment** → Book appointment
9. **Update Status** → Change appointment status
10. **Create Reschedule** → Request reschedule
11. **Respond to Reschedule** → Approve/reject request

### **Error Testing**
- **401 Unauthorized** → Test without JWT token
- **400 Bad Request** → Test with invalid data
- **404 Not Found** → Test with non-existent IDs
- **409 Conflict** → Test dependency violations

### **Filtering & Pagination**
- **Location Filtering** → `?isActive=true`
- **Service Filtering** → `?categoryId=1&isActive=true`
- **Appointment Filtering** → `?status=pending&from=2024-01-01&to=2024-01-31`
- **Pagination** → `?page=1&limit=20`

## 📊 Expected Response Format

All endpoints return responses in this format:
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": {
    // Response data varies by endpoint
  }
}
```

Error responses:
```json
{
  "success": false,
  "message": "Error description",
  "error": {
    "code": "ERROR_CODE",
    "details": "Detailed error information"
  }
}
```

---

**All 30+ endpoints are fully functional and ready for testing!** 🚀

Use these examples to verify API functionality before Flutter integration.
