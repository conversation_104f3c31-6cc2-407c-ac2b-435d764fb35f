# Password Reset APIs

This document describes the password reset APIs that implement a secure multi-step OTP workflow for password recovery. The system follows the same OTP verification pattern used for user registration, ensuring consistency across authentication flows.

## API Endpoints

### 1. POST /api/auth/request-password-reset-otp

**Description**: Initiates the password reset process by sending a 6-digit OTP to the user's registered email address.

**Authentication**: Not required (publicly accessible)

**Request Body**:
```json
{
  "email": "<EMAIL>"
}
```

**Validation Schema**:
- `email`: Required, must be valid email format

**Response Format**:

**Success (200)**:
```json
{
  "message": "Password reset OTP sent successfully to your email address."
}
```

**Error Responses**:
- **400 Bad Request**: Invalid email format
```json
{
  "message": "Invalid request body",
  "errors": {
    "email": {
      "_errors": ["Invalid email format"]
    }
  }
}
```

- **404 Not Found**: Email not found
```json
{
  "message": "No account found with this email address."
}
```

- **429 Too Many Requests**: Cooldown period active
```json
{
  "message": "Please wait before requesting another OTP. Try again in X seconds."
}
```

**Implementation Details**:
- Validates user exists with the provided email address
- Enforces 60-second cooldown between OTP requests
- Generates 6-digit numeric OTP valid for 10 minutes
- Sends OTP via email using the application's email service
- Updates user record with OTP and expiration timestamp
- Does not reveal whether email exists for security (in production)

### 2. POST /api/auth/verify-password-reset-otp

**Description**: Verifies the OTP sent to the user's email and generates a secure reset token for password change.

**Authentication**: Not required (publicly accessible)

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "otp": "123456"
}
```

**Validation Schema**:
- `email`: Required, must be valid email format
- `otp`: Required, must be exactly 6 digits

**Response Format**:

**Success (200)**:
```json
{
  "resetToken": "abc123def456ghi789jkl012",
  "message": "OTP verified successfully. Use the reset token to set your new password."
}
```

**Error Responses**:
- **400 Bad Request**: Invalid OTP or expired
```json
{
  "message": "Invalid OTP."
}
```
```json
{
  "message": "OTP has expired. Please request a new password reset."
}
```

- **404 Not Found**: Email not found
```json
{
  "message": "No account found with this email address."
}
```

**Implementation Details**:
- Validates OTP matches the one stored for the user
- Checks OTP hasn't expired (10-minute validity)
- Generates secure reset token (30-minute validity)
- Clears OTP from user record after successful verification
- Reset token is cryptographically secure random string
- Token stored with expiration timestamp for security

### 3. POST /api/auth/reset-password

**Description**: Completes the password reset process using the reset token to set a new password.

**Authentication**: Not required (publicly accessible)

**Request Body**:
```json
{
  "resetToken": "abc123def456ghi789jkl012",
  "newPassword": "newSecurePassword123"
}
```

**Validation Schema**:
- `resetToken`: Required, non-empty string
- `newPassword`: Required, minimum 8 characters

**Response Format**:

**Success (200)**:
```json
{
  "message": "Password reset successfully. You can now log in with your new password."
}
```

**Error Responses**:
- **400 Bad Request**: Invalid or expired token
```json
{
  "message": "Invalid or expired reset token."
}
```
```json
{
  "message": "Reset token has expired. Please request a new password reset."
}
```

- **400 Bad Request**: Password validation failed
```json
{
  "message": "Invalid request body",
  "errors": {
    "newPassword": {
      "_errors": ["Password must be at least 8 characters long"]
    }
  }
}
```

**Implementation Details**:
- Validates reset token exists and hasn't expired
- Hashes new password using secure bcrypt algorithm
- Updates user's password in AuthIdentity table
- Clears reset token after successful password update
- Uses database transaction for atomic operation
- Password immediately usable for login after reset

## Security Considerations

### OTP Security
- **6-digit numeric OTP**: Balance between security and usability
- **10-minute expiration**: Prevents replay attacks
- **60-second cooldown**: Prevents brute force and spam
- **Single-use tokens**: OTP cleared after verification

### Reset Token Security
- **Cryptographically secure**: Generated using secure random methods
- **30-minute validity**: Limited time window for password reset
- **Single-use**: Token cleared after password reset
- **Database storage**: Tokens stored securely with expiration

### Rate Limiting
- **Email cooldown**: 60 seconds between OTP requests per email
- **Token expiration**: Automatic cleanup of expired tokens
- **Audit trail**: All password reset attempts logged

## Database Entities Used

The APIs interact with the following database entities:

### User Entity Fields
- `email`: User's email address (unique identifier)
- `emailOtp`: Temporary 6-digit OTP for verification
- `emailOtpExpiresAt`: OTP expiration timestamp
- `lastEmailOtpSentAt`: Last OTP request timestamp (for cooldown)
- `passwordResetToken`: Secure token for password reset
- `passwordResetTokenExpiresAt`: Reset token expiration timestamp

### AuthIdentity Entity
- `providerData`: Encrypted password storage
- `providerName`: Authentication provider ('email')
- `providerUserId`: User identifier for email provider

## Multi-Step OTP Workflow

The password reset follows a secure 3-step process:

1. **Request Phase**: User provides email → System sends OTP
2. **Verification Phase**: User provides OTP → System generates reset token
3. **Reset Phase**: User provides token + new password → System updates password

This pattern ensures:
- **Email ownership verification**: OTP confirms user controls the email
- **Time-limited access**: Each step has appropriate expiration times
- **Secure token exchange**: Reset token prevents direct password changes
- **Audit capability**: Each step can be logged and monitored

## Error Handling

All APIs follow consistent error handling patterns:

### HTTP Status Codes
- **200**: Success
- **400**: Bad Request (validation errors, invalid OTP/token)
- **404**: Not Found (email not registered)
- **429**: Too Many Requests (cooldown active)
- **500**: Internal Server Error

### Error Response Format
```json
{
  "message": "Human-readable error message",
  "errors": {
    "field": {
      "_errors": ["Specific validation error"]
    }
  }
}
```

## Usage Examples

### Complete Password Reset Flow

```javascript
// Step 1: Request password reset OTP
async function requestPasswordReset(email) {
  try {
    const response = await fetch('/api/auth/request-password-reset-otp', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ email })
    });

    const result = await response.json();

    if (response.ok) {
      console.log('OTP sent:', result.message);
      return { success: true, message: result.message };
    } else {
      console.error('Error:', result.message);
      return { success: false, error: result.message };
    }
  } catch (error) {
    console.error('Network error:', error);
    return { success: false, error: 'Network error occurred' };
  }
}

// Step 2: Verify OTP and get reset token
async function verifyResetOTP(email, otp) {
  try {
    const response = await fetch('/api/auth/verify-password-reset-otp', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ email, otp })
    });

    const result = await response.json();

    if (response.ok) {
      console.log('OTP verified:', result.message);
      return {
        success: true,
        resetToken: result.resetToken,
        message: result.message
      };
    } else {
      console.error('Error:', result.message);
      return { success: false, error: result.message };
    }
  } catch (error) {
    console.error('Network error:', error);
    return { success: false, error: 'Network error occurred' };
  }
}

// Step 3: Reset password with token
async function resetPassword(resetToken, newPassword) {
  try {
    const response = await fetch('/api/auth/reset-password', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ resetToken, newPassword })
    });

    const result = await response.json();

    if (response.ok) {
      console.log('Password reset:', result.message);
      return { success: true, message: result.message };
    } else {
      console.error('Error:', result.message);
      return { success: false, error: result.message };
    }
  } catch (error) {
    console.error('Network error:', error);
    return { success: false, error: 'Network error occurred' };
  }
}

// Complete workflow example
async function completePasswordReset(email, otp, newPassword) {
  // Step 1: Request OTP (usually done on a separate screen)
  const otpResult = await requestPasswordReset(email);
  if (!otpResult.success) {
    return otpResult;
  }

  // Step 2: Verify OTP and get reset token
  const verifyResult = await verifyResetOTP(email, otp);
  if (!verifyResult.success) {
    return verifyResult;
  }

  // Step 3: Reset password
  const resetResult = await resetPassword(verifyResult.resetToken, newPassword);
  return resetResult;
}
```

### React Hook Example

```javascript
import { useState } from 'react';

export function usePasswordReset() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const requestOTP = async (email) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/auth/request-password-reset-otp', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email })
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message);
      }

      return result;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const verifyOTP = async (email, otp) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/auth/verify-password-reset-otp', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, otp })
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message);
      }

      return result;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const resetPassword = async (resetToken, newPassword) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/auth/reset-password', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ resetToken, newPassword })
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message);
      }

      return result;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return {
    requestOTP,
    verifyOTP,
    resetPassword,
    loading,
    error
  };
}
```

## Integration with Existing OTP Workflow

The password reset APIs integrate seamlessly with the existing OTP infrastructure:

### Shared Components
- **OTP Generation**: Uses same 6-digit numeric format as registration
- **Email Service**: Leverages existing email sending infrastructure
- **Validation Patterns**: Consistent Zod schemas across all auth flows
- **Error Handling**: Uniform error response format
- **Database Fields**: Reuses existing OTP fields (`emailOtp`, `emailOtpExpiresAt`)

### Workflow Consistency
- **Multi-step Process**: Mirrors registration OTP verification flow
- **Time Limits**: Same 10-minute OTP expiration as registration
- **Cooldown Periods**: Consistent 60-second rate limiting
- **Security Patterns**: Same cryptographic standards

### Code Reusability
- **Validation Utilities**: Shared validation functions
- **Email Templates**: Consistent branding and formatting
- **Error Messages**: Standardized user-facing messages
- **Logging**: Unified audit trail format

## File Locations

- **API Handlers**: `app/src/auth/apiHandlers.ts` (lines 754-831)
- **Business Logic**: `app/src/auth/actions.ts` (lines 1040-1280)
- **Wasp Configuration**: `app/main.wasp` (lines 628-645)
- **Database Schema**: `app/schema.prisma` (User entity, lines 101-103)
- **Migration**: `app/migrations/20250707052147_add_password_reset_fields/migration.sql`
- **Documentation**: `app/docs/password-reset-apis.md`
- **Tests**: `app/tests/integration/suites/authentication.test.js` (lines 294-310)
