# Flutter Provider App - API Integration Specification

## Base URL Configuration
```dart
class ApiConfig {
  static const String baseUrl = 'http://localhost:5400'; // Development
  // static const String baseUrl = 'https://your-production-domain.com'; // Production
}
```

## Authentication Flow

### 1. Provider Registration
```dart
class AuthService {
  Future<ProviderRegistrationResponse> registerProvider({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    required int providerCategoryId,
    required String businessName,
    String? phone,
  }) async {
    final response = await http.post(
      Uri.parse('${ApiConfig.baseUrl}/api/auth/provider/register'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode({
        'email': email,
        'password': password,
        'firstName': firstName,
        'lastName': lastName,
        'providerCategoryId': providerCategoryId,
        'businessName': businessName,
        'phone': phone,
      }),
    );

    if (response.statusCode == 201) {
      return ProviderRegistrationResponse.fromJson(json.decode(response.body));
    } else {
      final error = json.decode(response.body);
      throw ApiException(error['message'] ?? 'Registration failed');
    }
  }
}

// Response Model
class ProviderRegistrationResponse {
  final String message;
  final ProviderUser user;
  final ProviderProfile provider;

  ProviderRegistrationResponse({
    required this.message,
    required this.user,
    required this.provider,
  });

  factory ProviderRegistrationResponse.fromJson(Map<String, dynamic> json) {
    return ProviderRegistrationResponse(
      message: json['message'],
      user: ProviderUser.fromJson(json['user']),
      provider: ProviderProfile.fromJson(json['provider']),
    );
  }
}
```

### 2. Provider Login
```dart
Future<LoginResponse> loginProvider({
  required String identifier, // email or phone
  required String password,
}) async {
  final response = await http.post(
    Uri.parse('${ApiConfig.baseUrl}/api/auth/provider/login'),
    headers: {'Content-Type': 'application/json'},
    body: json.encode({
      'identifier': identifier,
      'password': password,
    }),
  );

  if (response.statusCode == 200) {
    final data = json.decode(response.body);
    // Store session token securely
    await _secureStorage.write(key: 'session_token', value: data['sessionId']);
    return LoginResponse.fromJson(data);
  } else {
    final error = json.decode(response.body);
    throw ApiException(error['message'] ?? 'Login failed');
  }
}

class LoginResponse {
  final String sessionId;
  final ProviderUser user;
  final ProviderProfile provider;

  LoginResponse({
    required this.sessionId,
    required this.user,
    required this.provider,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    return LoginResponse(
      sessionId: json['sessionId'],
      user: ProviderUser.fromJson(json['user']),
      provider: ProviderProfile.fromJson(json['provider']),
    );
  }
}
```

### 3. Category Selection
```dart
class CategoryService {
  Future<List<ProviderCategory>> getCategories({String? language = 'AR'}) async {
    final uri = Uri.parse('${ApiConfig.baseUrl}/api/auth/provider/categories')
        .replace(queryParameters: language != null ? {'targetLanguage': language} : null);
    
    final response = await http.get(
      uri,
      headers: {'Content-Type': 'application/json'},
    );

    if (response.statusCode == 200) {
      final List<dynamic> data = json.decode(response.body);
      return data.map((json) => ProviderCategory.fromJson(json)).toList();
    } else {
      throw ApiException('Failed to load categories');
    }
  }

  // Build hierarchical structure for UI
  Map<String, List<ProviderCategory>> buildCategoryHierarchy(List<ProviderCategory> categories) {
    final parentCategories = categories.where((cat) => cat.parentId == null).toList();
    final childCategories = categories.where((cat) => cat.parentId != null).toList();
    
    Map<String, List<ProviderCategory>> hierarchy = {};
    
    for (final parent in parentCategories) {
      hierarchy[parent.title] = childCategories
          .where((child) => child.parentId == parent.id)
          .toList();
    }
    
    return hierarchy;
  }
}

class ProviderCategory {
  final int id;
  final String title;
  final int? parentId;

  ProviderCategory({
    required this.id,
    required this.title,
    this.parentId,
  });

  factory ProviderCategory.fromJson(Map<String, dynamic> json) {
    return ProviderCategory(
      id: json['id'],
      title: json['title'],
      parentId: json['parentId'],
    );
  }

  bool get isParent => parentId == null;
  bool get isChild => parentId != null;
}
```

### 4. Setup Wizard Data Models
```dart
class SetupData {
  final BusinessInfo businessInfo;
  final List<LocationData> locations;
  final List<ServiceData> services;
  final List<QueueData> queues;

  SetupData({
    required this.businessInfo,
    required this.locations,
    required this.services,
    required this.queues,
  });

  Map<String, dynamic> toJson() {
    return {
      'businessInfo': businessInfo.toJson(),
      'locations': locations.map((l) => l.toJson()).toList(),
      'services': services.map((s) => s.toJson()).toList(),
      'queues': queues.map((q) => q.toJson()).toList(),
    };
  }
}

class BusinessInfo {
  final String businessName;
  final String bio;
  final String? shortName;
  final String? logoUrl;
  final BusinessPhones phones;

  BusinessInfo({
    required this.businessName,
    required this.bio,
    this.shortName,
    this.logoUrl,
    required this.phones,
  });

  Map<String, dynamic> toJson() {
    return {
      'businessName': businessName,
      'bio': bio,
      'shortName': shortName,
      'logoUrl': logoUrl,
      'phones': phones.toJson(),
    };
  }
}

class BusinessPhones {
  final String? fixedLine;
  final String? fax;
  final String? mobile;

  BusinessPhones({this.fixedLine, this.fax, this.mobile});

  Map<String, dynamic> toJson() {
    return {
      'fixedLine': fixedLine,
      'fax': fax,
      'mobile': mobile,
    };
  }

  bool get hasAtLeastOne => fixedLine != null || fax != null || mobile != null;
}

class LocationData {
  final String name;
  final String? shortName;
  final String country;
  final String city;
  final String timezone;
  final String address;
  final Coordinates? coordinates;
  final List<OpeningHours> openingHours;

  LocationData({
    required this.name,
    this.shortName,
    required this.country,
    required this.city,
    required this.timezone,
    required this.address,
    this.coordinates,
    required this.openingHours,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'shortName': shortName,
      'country': country,
      'city': city,
      'timezone': timezone,
      'address': address,
      'coordinates': coordinates?.toJson(),
      'openingHours': openingHours.map((oh) => oh.toJson()).toList(),
    };
  }
}

class ServiceData {
  final String title;
  final int duration;
  final double? price;
  final int pointsRequirements;
  final bool isPublic;
  final String deliveryType; // "at_location", "at_customer", "both"
  final List<String>? servedRegions;

  ServiceData({
    required this.title,
    required this.duration,
    this.price,
    required this.pointsRequirements,
    required this.isPublic,
    required this.deliveryType,
    this.servedRegions,
  });

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'duration': duration,
      'price': price,
      'pointsRequirements': pointsRequirements,
      'isPublic': isPublic,
      'deliveryType': deliveryType,
      'servedRegions': servedRegions,
    };
  }
}
```

### 5. Complete Setup API Call
```dart
class SetupService {
  Future<SetupResponse> completeSetup(SetupData setupData) async {
    final token = await _secureStorage.read(key: 'session_token');
    
    final response = await http.post(
      Uri.parse('${ApiConfig.baseUrl}/api/auth/provider/complete-setup'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: json.encode(setupData.toJson()),
    );

    if (response.statusCode == 200) {
      return SetupResponse.fromJson(json.decode(response.body));
    } else {
      final error = json.decode(response.body);
      throw ApiException(error['message'] ?? 'Setup failed');
    }
  }
}

class SetupResponse {
  final String message;
  final ProviderProfile provider;
  final SetupSummary summary;

  SetupResponse({
    required this.message,
    required this.provider,
    required this.summary,
  });

  factory SetupResponse.fromJson(Map<String, dynamic> json) {
    return SetupResponse(
      message: json['message'],
      provider: ProviderProfile.fromJson(json['provider']),
      summary: SetupSummary.fromJson(json['summary']),
    );
  }
}

class SetupSummary {
  final int locationsCreated;
  final int servicesCreated;
  final int queuesCreated;

  SetupSummary({
    required this.locationsCreated,
    required this.servicesCreated,
    required this.queuesCreated,
  });

  factory SetupSummary.fromJson(Map<String, dynamic> json) {
    return SetupSummary(
      locationsCreated: json['locationsCreated'],
      servicesCreated: json['servicesCreated'],
      queuesCreated: json['queuesCreated'],
    );
  }
}
```

## Error Handling
```dart
class ApiException implements Exception {
  final String message;
  final int? statusCode;

  ApiException(this.message, [this.statusCode]);

  @override
  String toString() => 'ApiException: $message';
}

// Global error handler
void handleApiError(dynamic error) {
  if (error is ApiException) {
    // Show user-friendly error message
    showErrorSnackBar(error.message);
  } else {
    // Log unexpected errors
    print('Unexpected error: $error');
    showErrorSnackBar('An unexpected error occurred');
  }
}
```

## Secure Storage
```dart
class SecureStorageService {
  static const _storage = FlutterSecureStorage();

  static Future<void> storeToken(String token) async {
    await _storage.write(key: 'session_token', value: token);
  }

  static Future<String?> getToken() async {
    return await _storage.read(key: 'session_token');
  }

  static Future<void> clearToken() async {
    await _storage.delete(key: 'session_token');
  }
}
```

## Usage in UI
```dart
class RegistrationScreen extends StatefulWidget {
  @override
  _RegistrationScreenState createState() => _RegistrationScreenState();
}

class _RegistrationScreenState extends State<RegistrationScreen> {
  final _authService = AuthService();
  final _categoryService = CategoryService();
  
  List<ProviderCategory> _categories = [];
  ProviderCategory? _selectedCategory;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadCategories();
  }

  Future<void> _loadCategories() async {
    try {
      final categories = await _categoryService.getCategories(language: 'AR');
      setState(() {
        _categories = categories;
      });
    } catch (error) {
      handleApiError(error);
    }
  }

  Future<void> _register() async {
    if (_selectedCategory == null) {
      showErrorSnackBar('Please select a provider category');
      return;
    }

    setState(() => _isLoading = true);

    try {
      final response = await _authService.registerProvider(
        email: _emailController.text,
        password: _passwordController.text,
        firstName: _firstNameController.text,
        lastName: _lastNameController.text,
        providerCategoryId: _selectedCategory!.id,
        businessName: _businessNameController.text,
        phone: _phoneController.text,
      );

      // Navigate to login or setup wizard
      Navigator.pushReplacementNamed(context, '/setup-wizard');
    } catch (error) {
      handleApiError(error);
    } finally {
      setState(() => _isLoading = false);
    }
  }
}
```
