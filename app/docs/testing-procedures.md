# Payment Integration Testing Procedures

## 🎯 Overview

This document outlines comprehensive testing procedures for the multi-processor payment integration, including LemonSqueezy, Stripe, and Chargily Pay support.

## 🚀 Quick Start Testing

### 1. Start the Application
```bash
# Start the Wasp application
wasp start

# In another terminal, verify it's running
curl http://localhost:3001/health
```

### 2. Run Integration Tests
```bash
# Comprehensive payment integration test
node test-payment-integration.cjs

# Chargily-specific integration test
node test-chargily-integration.cjs

# Test with custom base URL
node test-payment-integration.cjs https://your-domain.com
```

### 3. Set Up Webhook Testing
```bash
# Automated ngrok setup and webhook testing
node setup-ngrok-testing.cjs 3001

# Manual webhook testing with existing ngrok URL
node test-chargily-webhooks.cjs https://abc123.ngrok.io
```

## 🧪 Test Categories

### 1. Environment and Configuration Tests

#### Test Environment Variables
```bash
node -e "
require('dotenv').config({ path: '.env.server' });
console.log('Chargily API Key:', process.env.CHARGILY_API_KEY ? 'Set' : 'Missing');
console.log('Chargily Mode:', process.env.CHARGILY_MODE);
console.log('LemonSqueezy API Key:', process.env.LEMONSQUEEZY_API_KEY ? 'Set' : 'Missing');
"
```

#### Test Database Schema
```bash
# Check if migrations are up to date
wasp db migrate-dev

# Verify Chargily fields exist
node test-chargily-integration.cjs | grep "Database schema"
```

### 2. API Connectivity Tests

#### Test Payment Processor APIs
```bash
# Test all processors
node test-payment-integration.cjs

# Test specific processor connectivity
node test-chargily-connection.cjs
```

#### Test Individual Endpoints
```bash
# Test subscription plans (public endpoint)
curl http://localhost:3001/api/auth/payment/plans

# Test payment processors (requires auth)
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:3001/api/auth/payment/processors

# Test Chargily status (requires auth)
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:3001/api/auth/payment/chargily/status
```

### 3. Webhook Integration Tests

#### Test Webhook Endpoints
```bash
# Test webhook accessibility
curl -X GET http://localhost:3001/api/payment/chargily/webhook
# Expected: 405 Method Not Allowed (webhooks only accept POST)

curl -X GET http://localhost:3001/api/payment/lemonsqueezy/webhook
# Expected: 405 Method Not Allowed

curl -X GET http://localhost:3001/api/payment/stripe/webhook
# Expected: 405 Method Not Allowed
```

#### Test Webhook Signature Validation
```bash
# Test Chargily webhook signature validation
node test-chargily-webhooks.cjs https://your-ngrok-url.ngrok.io
```

### 4. Multi-Processor Integration Tests

#### Test Payment Method Selection
```bash
# Test processor availability API
curl http://localhost:3001/api/auth/payment/processors

# Test checkout with different processors
curl -X POST http://localhost:3001/api/auth/payment/checkout \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -d '{"planId": "hobby", "paymentProcessor": "chargily"}'

curl -X POST http://localhost:3001/api/auth/payment/checkout \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -d '{"planId": "hobby", "paymentProcessor": "lemonsqueezy"}'
```

#### Test Backward Compatibility
```bash
# Test legacy checkout (should default to LemonSqueezy)
curl -X POST http://localhost:3001/api/auth/payment/checkout \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -d '{"planId": "hobby"}'
```

## 🔧 Manual Testing Procedures

### 1. UI Testing

#### Test Payment Method Selection
1. Navigate to `/pricing` page
2. Verify payment method selector appears (if multiple processors available)
3. Select different payment methods
4. Verify button text updates ("Buy with Chargily Pay" vs "Buy with Card")
5. Complete checkout flow for each processor

#### Test Account Page
1. Navigate to `/account` page
2. Verify payment processor information is displayed
3. Check payment method details (EDAHABIA/CIB for Chargily users)
4. Test "Manage Subscription" button with processor-specific text

### 2. Payment Flow Testing

#### Chargily Payment Flow
1. Select Chargily Pay as payment method
2. Choose EDAHABIA or CIB payment method
3. Use test card numbers:
   - EDAHABIA: `0000 0000 0000 0001`
   - CIB: `4000 0000 0000 0002`
4. Complete payment and verify webhook processing
5. Check user credits and subscription status

#### LemonSqueezy Payment Flow
1. Select Credit/Debit Card payment method
2. Complete payment with test card
3. Verify webhook processing
4. Check user credits and subscription status

### 3. Webhook Testing

#### Set Up ngrok
```bash
# Install ngrok (if not already installed)
# Download from https://ngrok.com/download

# Authenticate ngrok
ngrok authtoken YOUR_AUTH_TOKEN

# Start tunnel
ngrok http 3001
```

#### Configure Webhooks
1. **Chargily Dashboard**:
   - Go to https://pay.chargily.com/test/settings/webhooks
   - Add webhook URL: `https://your-ngrok-url.ngrok.io/api/payment/chargily/webhook`
   - Select events: `checkout.paid`, `checkout.failed`, `subscription.created`

2. **LemonSqueezy Dashboard**:
   - Go to https://app.lemonsqueezy.com/settings/webhooks
   - Add webhook URL: `https://your-ngrok-url.ngrok.io/api/payment/lemonsqueezy/webhook`
   - Select events: `order_created`, `subscription_created`

#### Test Webhook Delivery
```bash
# Send test webhooks
node test-chargily-webhooks.cjs https://your-ngrok-url.ngrok.io

# Monitor application logs for webhook processing
tail -f wasp-logs.txt
```

## 📊 Test Results Interpretation

### Expected Results

#### ✅ Successful Integration
- All API endpoints return 200 or appropriate status codes
- Payment processor selection works correctly
- Checkout sessions create successfully for all processors
- Webhooks process correctly and update user accounts
- UI displays processor-specific information

#### ⚠️ Partial Success
- Some processors work, others fail (check API keys)
- Webhooks accessible but signature validation fails (check secrets)
- UI works but some processor-specific features missing

#### ❌ Integration Issues
- API endpoints return 404 (check main.wasp configuration)
- Database errors (run migrations)
- Authentication failures (check JWT configuration)
- Connection refused (ensure application is running)

### Common Issues and Solutions

#### Database Issues
```bash
# Reset and migrate database
wasp db reset
wasp db migrate-dev
```

#### Environment Variable Issues
```bash
# Verify all required variables are set
node test-chargily-integration.cjs | grep "Environment"
```

#### API Handler Issues
```bash
# Check TypeScript compilation
wasp build
```

#### Webhook Issues
```bash
# Test webhook signature validation
node -e "
const crypto = require('crypto');
const secret = 'your-webhook-secret';
const payload = 'test-payload';
const signature = crypto.createHmac('sha256', secret).update(payload).digest('hex');
console.log('Test signature:', signature);
"
```

## 🚀 Production Deployment Testing

### Pre-Deployment Checklist
- [ ] All tests pass in staging environment
- [ ] Real payment credentials configured
- [ ] Webhook URLs updated to production domains
- [ ] SSL certificates valid for webhook endpoints
- [ ] Database migrations applied
- [ ] Environment variables set correctly

### Post-Deployment Verification
- [ ] Test payment flows with small amounts
- [ ] Verify webhook delivery in payment processor dashboards
- [ ] Monitor application logs for errors
- [ ] Test subscription management features
- [ ] Verify credit allocation and user account updates

## 📞 Support and Troubleshooting

### Log Monitoring
```bash
# Monitor application logs
tail -f wasp-logs.txt | grep -E "(payment|webhook|chargily)"

# Monitor webhook delivery
curl -s http://localhost:4040/api/tunnels | jq '.tunnels[0].public_url'
```

### Debug Mode
```bash
# Enable debug logging
export DEBUG=payment:*
wasp start
```

### Contact Information
- **Chargily Support**: <EMAIL>
- **LemonSqueezy Support**: https://lemonsqueezy.com/help
- **Integration Issues**: Check GitHub issues or project documentation
