import React, { useState } from 'react';
import axios from 'axios';

interface CategoryImageUploadProps {
  categoryId: number;
  currentImageId?: string;
  onUploadSuccess?: (fileId: string) => void;
  onRemoveSuccess?: () => void;
}

interface UploadResponse {
  success: boolean;
  message: string;
  data: {
    uploadUrl: string;
    uploadFields: Record<string, string>;
    file: {
      id: string;
      name: string;
      type: string;
      key: string;
    };
    category: {
      id: number;
      title: string;
      imageId: string;
      parentId: number | null;
      _count: {
        providers: number;
        children: number;
      };
    };
  };
}

const CategoryImageUpload: React.FC<CategoryImageUploadProps> = ({
  categoryId,
  currentImageId,
  onUploadSuccess,
  onRemoveSuccess
}) => {
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [showUploadModal, setShowUploadModal] = useState(false);

  // Get auth headers (implement based on your auth system)
  const getAuthHeaders = () => ({
    'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
    'Content-Type': 'application/json'
  });

  const BASE_URL = process.env.REACT_APP_API_URL || '';

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      if (!['image/jpeg', 'image/png'].includes(file.type)) {
        alert('Please select a JPEG or PNG image file.');
        return;
      }

      // Validate file size (5MB max)
      if (file.size > 5 * 1024 * 1024) {
        alert('File size must be less than 5MB.');
        return;
      }

      setSelectedFile(file);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) return;

    try {
      setUploading(true);
      setUploadProgress(0);

      // Call our category image upload API
      const response = await axios.post<UploadResponse>(
        `${BASE_URL}/api/auth/admin/provider-categories/${categoryId}/image`,
        {
          fileType: selectedFile.type,
          fileName: selectedFile.name,
        },
        {
          headers: getAuthHeaders()
        }
      );

      if (!response.data.success) {
        throw new Error(response.data.message || 'Upload failed');
      }

      const { uploadUrl, uploadFields, file } = response.data.data;

      // Upload file to S3
      const formData = new FormData();
      Object.entries(uploadFields || {}).forEach(([key, value]) => {
        formData.append(key, value as string);
      });
      formData.append('file', selectedFile);

      await axios.post(uploadUrl, formData, {
        onUploadProgress: (progressEvent) => {
          if (progressEvent.total) {
            const percentage = Math.round((progressEvent.loaded / progressEvent.total) * 100);
            setUploadProgress(percentage);
          }
        },
      });

      alert('Category image uploaded successfully!');
      onUploadSuccess?.(file.id);
      setShowUploadModal(false);
      setSelectedFile(null);

    } catch (error: any) {
      console.error('Upload failed:', error);
      alert(error.response?.data?.message || 'Upload failed. Please try again.');
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  const handleRemove = async () => {
    if (!currentImageId) return;

    if (!confirm('Are you sure you want to remove this category image?')) {
      return;
    }

    try {
      const response = await axios.delete(
        `${BASE_URL}/api/auth/admin/provider-categories/${categoryId}/image`,
        {
          headers: getAuthHeaders()
        }
      );

      if (response.data.success) {
        alert('Category image removed successfully!');
        onRemoveSuccess?.();
      }
    } catch (error: any) {
      console.error('Remove failed:', error);
      alert(error.response?.data?.message || 'Failed to remove image. Please try again.');
    }
  };

  return (
    <div className="category-image-upload">
      <div className="current-image">
        {currentImageId ? (
          <div>
            <p>Current image: {currentImageId}</p>
            <button 
              onClick={handleRemove}
              className="btn btn-danger"
              type="button"
            >
              Remove Image
            </button>
          </div>
        ) : (
          <p>No image uploaded</p>
        )}
      </div>

      <div className="upload-section">
        <button 
          onClick={() => setShowUploadModal(true)}
          className="btn btn-primary"
          type="button"
        >
          {currentImageId ? 'Change Image' : 'Upload Image'}
        </button>
      </div>

      {showUploadModal && (
        <div className="upload-modal">
          <div className="modal-content">
            <h3>Upload Category Image</h3>
            
            <div className="file-input">
              <input
                type="file"
                accept="image/jpeg,image/png"
                onChange={handleFileSelect}
                disabled={uploading}
              />
            </div>

            {selectedFile && (
              <div className="file-preview">
                <p>Selected: {selectedFile.name}</p>
                <p>Size: {(selectedFile.size / 1024 / 1024).toFixed(2)} MB</p>
              </div>
            )}

            {uploading && (
              <div className="upload-progress">
                <div className="progress-bar">
                  <div 
                    className="progress-fill" 
                    style={{ width: `${uploadProgress}%` }}
                  />
                </div>
                <p>{uploadProgress}% uploaded</p>
              </div>
            )}

            <div className="modal-actions">
              <button
                onClick={handleUpload}
                disabled={!selectedFile || uploading}
                className="btn btn-success"
                type="button"
              >
                {uploading ? 'Uploading...' : 'Upload'}
              </button>
              <button
                onClick={() => {
                  setShowUploadModal(false);
                  setSelectedFile(null);
                }}
                disabled={uploading}
                className="btn btn-secondary"
                type="button"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      <style jsx>{`
        .category-image-upload {
          padding: 1rem;
          border: 1px solid #ddd;
          border-radius: 8px;
          margin: 1rem 0;
        }

        .upload-modal {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1000;
        }

        .modal-content {
          background: white;
          padding: 2rem;
          border-radius: 8px;
          max-width: 500px;
          width: 90%;
        }

        .progress-bar {
          width: 100%;
          height: 20px;
          background: #f0f0f0;
          border-radius: 10px;
          overflow: hidden;
          margin: 1rem 0;
        }

        .progress-fill {
          height: 100%;
          background: #007bff;
          transition: width 0.3s ease;
        }

        .btn {
          padding: 0.5rem 1rem;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          margin: 0.25rem;
        }

        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn:disabled { opacity: 0.6; cursor: not-allowed; }

        .modal-actions {
          display: flex;
          justify-content: flex-end;
          margin-top: 1rem;
        }
      `}</style>
    </div>
  );
};

export default CategoryImageUpload;
