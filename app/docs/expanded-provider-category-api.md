# Expanded Provider Category API

This document describes the enhanced Provider Category model with additional attributes for better categorization, SEO, and management capabilities.

## Enhanced Model Structure

The ProviderCategory model now includes the following fields:

```typescript
interface ProviderCategory {
  id: number;
  createdAt: string;
  updatedAt: string;
  title: string;
  description?: string;
  isActive: boolean;
  sortOrder: number;
  metadata?: {
    icon: string;
    color: string;
    keywords: string[];
    seoTitle: string;
    seoDescription: string;
  };
  parentId?: number;
  imageId?: string;
  
  // Relations
  parent?: ProviderCategory;
  children?: ProviderCategory[];
  image?: File;
  providers?: Provider[];
  _count?: {
    providers: number;
    children: number;
  };
}
```

## API Endpoints

### 1. Create Provider Category

**Endpoint**: `POST /api/auth/admin/provider-categories`

**Request Body**:
```json
{
  "title": "Lawyer",
  "description": "Legal professionals and law firms",
  "parentId": 3,
  "isActive": true,
  "sortOrder": 0,
  "metadata": {
    "icon": "fas fa-gavel",
    "color": "#EF4444",
    "keywords": ["legal", "lawyer", "attorney", "law"],
    "seoTitle": "Find Professional Lawyers",
    "seoDescription": "Connect with experienced lawyers and legal professionals in your area"
  }
}
```

**Response** (201 Created):
```json
{
  "category": {
    "id": 17,
    "createdAt": "2025-01-08T02:30:00.000Z",
    "updatedAt": "2025-01-08T02:30:00.000Z",
    "title": "Lawyer",
    "description": "Legal professionals and law firms",
    "isActive": true,
    "sortOrder": 0,
    "metadata": {
      "icon": "fas fa-gavel",
      "color": "#EF4444",
      "keywords": ["legal", "lawyer", "attorney", "law"],
      "seoTitle": "Find Professional Lawyers",
      "seoDescription": "Connect with experienced lawyers and legal professionals in your area"
    },
    "parentId": 3,
    "imageId": null,
    "parent": {
      "id": 3,
      "title": "Professional Services"
    },
    "children": [],
    "image": null,
    "_count": {
      "providers": 0,
      "children": 0
    }
  },
  "message": "Provider category created successfully"
}
```

### 2. Update Provider Category

**Endpoint**: `PUT /api/auth/admin/provider-categories/:categoryId`

**Request Body** (all fields optional):
```json
{
  "title": "Legal Services",
  "description": "Updated description for legal services",
  "isActive": false,
  "sortOrder": 5,
  "metadata": {
    "icon": "fas fa-balance-scale",
    "color": "#DC2626",
    "keywords": ["legal", "lawyer", "attorney", "law", "legal-services"],
    "seoTitle": "Professional Legal Services",
    "seoDescription": "Find qualified legal professionals and law firms"
  }
}
```

### 3. Get Single Category

**Endpoint**: `GET /api/auth/admin/provider-categories/:categoryId`

**Response** includes all new fields:
```json
{
  "success": true,
  "message": "Provider category retrieved successfully",
  "data": {
    "id": 16,
    "createdAt": "2025-01-08T02:30:00.000Z",
    "updatedAt": "2025-01-08T02:35:00.000Z",
    "title": "Legal Services",
    "description": "Legal professionals and law firms",
    "isActive": true,
    "sortOrder": 0,
    "metadata": {
      "icon": "fas fa-gavel",
      "color": "#EF4444",
      "keywords": ["legal", "lawyer", "attorney"],
      "seoTitle": "Find Professional Lawyers",
      "seoDescription": "Connect with experienced legal professionals"
    },
    "parentId": 3,
    "imageId": "file-uuid",
    "parent": {
      "id": 3,
      "title": "Professional Services"
    },
    "children": [],
    "image": {
      "id": "file-uuid",
      "name": "legal-category.jpg",
      "type": "image/jpeg",
      "key": "files/user-id/legal-category.jpg"
    },
    "providers": [],
    "_count": {
      "providers": 5,
      "children": 0
    }
  }
}
```

### 4. Get All Categories

**Endpoint**: `GET /api/auth/admin/provider-categories`

Returns array of categories with all new fields included.

## Field Descriptions

| Field | Type | Required | Default | Description |
|-------|------|----------|---------|-------------|
| `title` | string | Yes | - | Category name (must be unique) |
| `description` | string | No | null | Detailed description of the category |
| `isActive` | boolean | No | true | Whether the category is active/visible |
| `sortOrder` | number/string | No | 0 | Display order (accepts both number and string) |
| `parentId` | number/string | No | null | ID of parent category (accepts both number and string) |
| `metadata` | object | No | null | Additional metadata for UI and SEO |

### Input Validation Notes

- **`parentId`**: Accepts both number (`3`) and string (`"3"`). Empty strings are treated as `null`.
- **`sortOrder`**: Accepts both number (`0`) and string (`"0"`). Must be a valid integer.
- **String to Number Conversion**: The API automatically converts valid string numbers to integers.
- **Validation Errors**: Invalid number strings (e.g., `"abc"`) will return validation errors.

### Metadata Object Structure

| Field | Type | Default | Description |
|-------|------|---------|-------------|
| `icon` | string | "" | CSS class or icon identifier |
| `color` | string | "#EF4444" | Hex color code for category theming |
| `keywords` | string[] | [] | Array of keywords for search/filtering |
| `seoTitle` | string | "" | SEO-optimized title for search engines |
| `seoDescription` | string | "" | SEO meta description |

## Database Schema Changes

```sql
-- Added fields to ProviderCategory table
ALTER TABLE "ProviderCategory" ADD COLUMN "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE "ProviderCategory" ADD COLUMN "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE "ProviderCategory" ADD COLUMN "description" TEXT;
ALTER TABLE "ProviderCategory" ADD COLUMN "isActive" BOOLEAN NOT NULL DEFAULT true;
ALTER TABLE "ProviderCategory" ADD COLUMN "sortOrder" INTEGER NOT NULL DEFAULT 0;
ALTER TABLE "ProviderCategory" ADD COLUMN "metadata" JSONB;

-- Added indexes for performance
CREATE INDEX "ProviderCategory_isActive_idx" ON "ProviderCategory"("isActive");
CREATE INDEX "ProviderCategory_sortOrder_idx" ON "ProviderCategory"("sortOrder");
CREATE INDEX "ProviderCategory_parentId_idx" ON "ProviderCategory"("parentId");
```

## Usage Examples

### Frontend Category Display
```javascript
// Sort categories by sortOrder for display
const sortedCategories = categories
  .filter(cat => cat.isActive)
  .sort((a, b) => a.sortOrder - b.sortOrder);

// Use metadata for styling
const categoryStyle = {
  color: category.metadata?.color || '#000',
  backgroundColor: `${category.metadata?.color}20` // 20% opacity
};
```

### SEO Implementation
```javascript
// Use SEO fields for meta tags
const metaTitle = category.metadata?.seoTitle || category.title;
const metaDescription = category.metadata?.seoDescription || category.description;
```

### Search/Filtering
```javascript
// Search using keywords
const searchResults = categories.filter(cat => 
  cat.metadata?.keywords?.some(keyword => 
    keyword.toLowerCase().includes(searchTerm.toLowerCase())
  )
);
```

## Migration Notes

- All new fields have appropriate defaults to ensure backward compatibility
- Existing categories will have `isActive: true` and `sortOrder: 0` by default
- The `metadata` field is optional and can be null
- Timestamps (`createdAt`, `updatedAt`) are automatically managed by Prisma
