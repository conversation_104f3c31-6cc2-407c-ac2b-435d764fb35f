# Upload APIs Documentation

## 🎯 Overview

The system implements a **two-phase upload workflow** using AWS S3 pre-signed URLs for secure, direct-to-cloud file uploads. This approach offloads upload traffic from the server and provides better performance.

## 📋 Available Upload APIs

### **User Profile Picture APIs**
- `POST /api/auth/user/profile-picture` - Generate upload URL
- `GET /api/auth/user/profile-picture` - Get profile picture
- `DELETE /api/auth/user/profile-picture` - Remove profile picture

### **Provider Logo APIs**
- `POST /api/auth/provider/logo` - Generate upload URL
- `DELETE /api/auth/provider/logo` - Remove logo
- `GET /api/auth/providers/mobile/logo` - Get logo (mobile)
- `POST /api/auth/providers/mobile/logo` - Upload logo (mobile)
- `DELETE /api/auth/providers/mobile/logo` - Remove logo (mobile)
- `POST /api/auth/providers/mobile/logo/confirm` - Confirm upload (mobile)

### **General File Upload APIs**
- `POST /api/auth/files/upload` - Generate upload URL for any file
- `GET /api/auth/files` - List user files
- `GET /api/auth/files/:fileId` - Get specific file

## 🔄 Upload Workflow

### **Phase 1: Generate Pre-signed URL**
```mermaid
sequenceDiagram
    participant C as Client
    participant S as Server
    participant DB as Database
    participant S3 as AWS S3

    C->>S: POST /api/auth/user/profile-picture<br/>{ fileName, fileType }
    S->>S: 1. Authenticate user
    S->>S: 2. Validate request (fileName, fileType)
    S->>S: 3. Call createFile operation
    S->>S3: 4. Generate pre-signed URL
    S3-->>S: Return upload URL + fields
    S->>DB: 5. Create File record
    DB-->>S: Return file ID
    S->>DB: 6. Update User.profilePictureId
    DB-->>S: Return updated user
    S-->>C: 200 OK<br/>{ uploadUrl, uploadFields, file, user }
```

### **Phase 2: Direct Upload to S3**
```mermaid
sequenceDiagram
    participant C as Client
    participant S3 as AWS S3

    C->>S3: POST to uploadUrl<br/>FormData with file + uploadFields
    S3-->>C: Upload successful (200 OK)
```

## 📝 API Request/Response Examples

### **1. Generate Upload URL**

#### **Profile Picture Upload**
```bash
POST /api/auth/user/profile-picture
Authorization: Bearer {token}
Content-Type: application/json

{
  "fileName": "profile.jpg",
  "fileType": "image/jpeg"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Profile picture upload URL generated successfully",
  "data": {
    "uploadUrl": "https://s3.amazonaws.com/bucket-name",
    "uploadFields": {
      "key": "user-id/uuid.jpg",
      "Content-Type": "image/jpeg",
      "policy": "...",
      "x-amz-signature": "..."
    },
    "file": {
      "id": "file-uuid",
      "name": "profile.jpg",
      "type": "image/jpeg",
      "key": "user-id/uuid.jpg"
    },
    "user": {
      "id": "user-uuid",
      "profilePictureId": "file-uuid"
    }
  }
}
```

#### **Provider Logo Upload**
```bash
POST /api/auth/provider/logo
Authorization: Bearer {token}
Content-Type: application/json

{
  "fileName": "logo.png",
  "fileType": "image/png"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Provider logo upload URL generated successfully",
  "data": {
    "uploadUrl": "https://s3.amazonaws.com/bucket-name",
    "uploadFields": {
      "key": "user-id/uuid.png",
      "Content-Type": "image/png",
      "policy": "...",
      "x-amz-signature": "..."
    },
    "file": {
      "id": "file-uuid",
      "name": "logo.png",
      "type": "image/png",
      "key": "user-id/uuid.png"
    },
    "provider": {
      "id": "provider-uuid",
      "logoId": "file-uuid"
    }
  }
}
```

### **2. Upload File to S3**
```javascript
// Client-side upload
const formData = new FormData();
Object.entries(uploadFields).forEach(([key, value]) => {
  formData.append(key, value);
});
formData.append('file', selectedFile);

const response = await fetch(uploadUrl, {
  method: 'POST',
  body: formData
});

if (response.ok) {
  console.log('Upload successful');
} else {
  console.error('Upload failed');
}
```

### **3. Get Profile Picture**
```bash
GET /api/auth/user/profile-picture
Authorization: Bearer {token}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "hasProfilePicture": true,
    "profilePicture": {
      "id": "file-uuid",
      "name": "profile.jpg",
      "type": "image/jpeg",
      "key": "user-id/uuid.jpg",
      "downloadUrl": "https://s3.amazonaws.com/signed-download-url",
      "createdAt": "2024-01-15T10:30:00.000Z"
    }
  }
}
```

### **4. Remove Profile Picture**
```bash
DELETE /api/auth/user/profile-picture
Authorization: Bearer {token}
```

**Response:**
```json
{
  "success": true,
  "message": "Profile picture removed successfully"
}
```

## 🔧 Technical Implementation

### **Core Components:**

#### **1. createFile Operation** (`src/file-upload/operations.ts`)
- Generates S3 pre-signed URLs using AWS SDK
- Creates File records in database
- Returns upload URL and fields for client

#### **2. S3Utils** (`src/file-upload/s3Utils.ts`)
- Handles AWS S3 interactions
- Generates unique file keys using UUID
- Creates pre-signed POST URLs with security conditions

#### **3. API Handlers**
- **Profile Pictures**: `src/user/profileApiHandlers.ts`
- **Provider Logos**: `src/provider/logoApiHandlers.ts`
- **General Files**: `src/file-upload/apiHandlers.ts`

#### **4. Client Upload Utilities** (`src/file-upload/fileUploading.ts`)
- Browser-side file upload with progress tracking
- File validation (type, size)
- FormData construction for S3 upload

### **File Validation:**
- **Supported Types**: `image/jpeg`, `image/png` (for profile pictures/logos)
- **Size Limit**: Configurable via `MAX_FILE_SIZE_BYTES`
- **Security**: Pre-signed URLs with expiration (1 hour)

### **Database Schema:**
```sql
-- File Entity
File {
  id: String (UUID)
  name: String
  type: String (MIME type)
  key: String (S3 key)
  uploadUrl: String
  userId: String
  createdAt: DateTime
}

-- User Entity (relevant fields)
User {
  profilePictureId: String? (references File.id)
}

-- Provider Entity (relevant fields)
SProvider {
  logoId: String? (references File.id)
}
```

## 🔒 Security Features

- **Authentication Required**: All upload APIs require valid JWT tokens
- **File Type Validation**: Only allowed MIME types accepted
- **Size Limits**: Enforced at S3 level via pre-signed URL conditions
- **Unique Keys**: UUID-based file keys prevent conflicts and collisions
- **Expiring URLs**: Pre-signed URLs expire after 1 hour
- **CORS Protection**: S3 bucket configured with appropriate CORS policies

## 📱 Client Integration Examples

### **React Component with Progress**
```javascript
import { uploadFileWithProgress } from '@src/file-upload/fileUploading';

function FileUploadComponent() {
  const [progress, setProgress] = useState(0);
  const [uploading, setUploading] = useState(false);

  const handleUpload = async (file) => {
    try {
      setUploading(true);
      await uploadFileWithProgress({
        file: file,
        setUploadProgressPercent: setProgress
      });
      console.log('Upload completed');
    } catch (error) {
      console.error('Upload failed:', error);
    } finally {
      setUploading(false);
    }
  };

  return (
    <div>
      <input type="file" onChange={(e) => handleUpload(e.target.files[0])} />
      {uploading && <div>Progress: {progress}%</div>}
    </div>
  );
}
```

### **Direct API Call**
```javascript
// Generate upload URL
const response = await fetch('/api/auth/user/profile-picture', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    fileName: file.name,
    fileType: file.type
  })
});

const { uploadUrl, uploadFields } = await response.json();

// Upload to S3
const formData = new FormData();
Object.entries(uploadFields).forEach(([key, value]) => {
  formData.append(key, value);
});
formData.append('file', file);

await fetch(uploadUrl, {
  method: 'POST',
  body: formData
});
```

## 🚀 Performance Benefits

1. **Reduced Server Load**: Files upload directly to S3, not through server
2. **Better Scalability**: No server bandwidth limitations for file uploads
3. **Faster Uploads**: Direct connection to S3 edge locations
4. **Progress Tracking**: Real-time upload progress without server polling
5. **Reliability**: AWS S3's built-in retry and error handling

## 🔧 Environment Configuration

Required environment variables:
```bash
# AWS S3 Configuration
AWS_S3_REGION=us-east-1
AWS_S3_FILES_BUCKET=your-bucket-name
AWS_S3_IAM_ACCESS_KEY=your-access-key
AWS_S3_IAM_SECRET_KEY=your-secret-key

# File Upload Limits
MAX_FILE_SIZE_BYTES=10485760  # 10MB
```

## 📊 Error Handling

### **Common Error Responses:**

#### **400 Bad Request**
```json
{
  "success": false,
  "message": "Invalid request data. Only image files are allowed for provider logos.",
  "errors": {
    "fileType": ["Invalid enum value. Expected 'image/jpeg' | 'image/png'"]
  }
}
```

#### **401 Unauthorized**
```json
{
  "success": false,
  "message": "Authentication required"
}
```

#### **404 Not Found**
```json
{
  "success": false,
  "message": "Provider profile not found"
}
```

#### **500 Internal Server Error**
```json
{
  "success": false,
  "message": "Failed to generate upload URL"
}
```

## 🧪 Testing

### **Test File Upload Flow**
```bash
# 1. Generate upload URL
curl -X POST https://your-api.com/api/auth/user/profile-picture \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"fileName": "test.jpg", "fileType": "image/jpeg"}'

# 2. Upload file to S3 (use returned uploadUrl and uploadFields)
curl -X POST "RETURNED_UPLOAD_URL" \
  -F "key=RETURNED_KEY" \
  -F "Content-Type=image/jpeg" \
  -F "policy=RETURNED_POLICY" \
  -F "x-amz-signature=RETURNED_SIGNATURE" \
  -F "file=@test.jpg"

# 3. Verify upload
curl -X GET https://your-api.com/api/auth/user/profile-picture \
  -H "Authorization: Bearer YOUR_TOKEN"
```

This architecture ensures **scalable, secure, and efficient** file uploads while maintaining a clean separation between the application server and file storage.
