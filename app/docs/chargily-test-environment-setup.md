# Chargily Pay Test Environment Setup Guide

## 🎯 Overview

This guide walks you through setting up a complete Chargily Pay test environment for the appointment booking system. Chargily Pay is an Algerian payment gateway that supports local payment methods like EDAHABIA (Algerie Post) and CIB (SATIM).

## 📋 Prerequisites

1. **Chargily Account**: Create a test account at [Chargily Pay Dashboard](https://pay.chargily.com)
2. **Node.js Environment**: Ensure you have Node.js 18+ installed
3. **Wasp Framework**: Project should be running with Wasp
4. **ngrok** (for webhook testing): Install from [ngrok.com](https://ngrok.com)

## 🔧 Step 1: Chargily Account Setup

### 1.1 Create Test Account
1. Visit [Chargily Pay Dashboard](https://pay.chargily.com)
2. Sign up for a new account
3. Complete account verification process
4. Switch to **Test Mode** in the dashboard

### 1.2 Get API Credentials
1. Navigate to **Settings > API Keys**
2. Copy your **Test Public Key** (starts with `test_pk_`)
3. Copy your **Test Secret Key** (starts with `test_sk_`)
4. Generate a **Webhook Secret** for secure webhook verification

### 1.3 Configure Webhook Endpoint
1. Go to **Settings > Webhooks**
2. Add new webhook endpoint: `https://your-ngrok-url.ngrok.io/api/payment/chargily/webhook`
3. Select events: `checkout.paid`, `checkout.failed`, `subscription.created`, `subscription.updated`
4. Set webhook secret (save this for environment variables)

## 🔧 Step 2: Environment Configuration

### 2.1 Update .env.server
Replace the placeholder values in `app/.env.server`:

```bash
# Chargily Pay configuration (Test Mode)
CHARGILY_API_KEY=test_pk_your_actual_test_public_key_here
CHARGILY_MODE=test
CHARGILY_WEBHOOK_SECRET=your_webhook_secret_here

# Chargily Test Plan IDs (create these in dashboard)
CHARGILY_HOBBY_PLAN_ID=price_test_hobby_plan_id
CHARGILY_PRO_PLAN_ID=price_test_pro_plan_id
CHARGILY_CREDITS_10_PLAN_ID=price_test_credits_plan_id
CHARGILY_FREE_PLAN_ID=price_test_free_plan_id
```

### 2.2 Environment Variables Explanation

| Variable | Description | Example |
|----------|-------------|---------|
| `CHARGILY_API_KEY` | Test public key from Chargily dashboard | `test_pk_abc123...` |
| `CHARGILY_MODE` | API mode (test/live) | `test` |
| `CHARGILY_WEBHOOK_SECRET` | Secret for webhook signature verification | `whsec_abc123...` |
| `CHARGILY_*_PLAN_ID` | Product/price IDs created in Chargily dashboard | `price_test_123...` |

## 🔧 Step 3: Create Test Products in Chargily Dashboard

### 3.1 Hobby Plan
1. Go to **Products > Create Product**
2. **Name**: "Hobby Plan"
3. **Description**: "Basic appointment booking plan"
4. **Price**: 2000 DZD (20.00 DZD)
5. **Currency**: DZD
6. **Type**: Recurring (Monthly)
7. **Metadata**: `{"planId": "hobby", "credits": 200, "queues": 3}`

### 3.2 Pro Plan
1. **Name**: "Pro Plan"
2. **Description**: "Professional appointment booking plan"
3. **Price**: 5000 DZD (50.00 DZD)
4. **Currency**: DZD
5. **Type**: Recurring (Monthly)
6. **Metadata**: `{"planId": "pro", "credits": 500, "queues": 10}`

### 3.3 Credits Pack
1. **Name**: "10 Credits Pack"
2. **Description**: "One-time credit purchase"
3. **Price**: 1000 DZD (10.00 DZD)
4. **Currency**: DZD
5. **Type**: One-time
6. **Metadata**: `{"planId": "credits10", "credits": 10}`

### 3.4 Free Plan
1. **Name**: "Free Plan"
2. **Description**: "Free trial plan"
3. **Price**: 0 DZD
4. **Currency**: DZD
5. **Type**: One-time
6. **Metadata**: `{"planId": "free", "credits": 50, "queues": 1}`

## 🔧 Step 4: Test Payment Methods

### 4.1 EDAHABIA Test Cards
Chargily provides test EDAHABIA card numbers for testing:

```
Card Number: 0000 0000 0000 0001
Expiry: Any future date
CVV: Any 3 digits
PIN: 1234
```

### 4.2 CIB Test Cards
Test CIB card numbers:

```
Card Number: 4000 0000 0000 0002
Expiry: Any future date
CVV: Any 3 digits
PIN: 1234
```

## 🔧 Step 5: Webhook Testing with ngrok

### 5.1 Install and Setup ngrok
```bash
# Install ngrok
npm install -g ngrok

# Authenticate (get auth token from ngrok.com)
ngrok authtoken YOUR_NGROK_AUTH_TOKEN

# Start tunnel to your local server
ngrok http 3001
```

### 5.2 Configure Webhook URL
1. Copy the HTTPS URL from ngrok (e.g., `https://abc123.ngrok.io`)
2. Update Chargily webhook endpoint to: `https://abc123.ngrok.io/api/payment/chargily/webhook`
3. Test webhook delivery in Chargily dashboard

## 🔧 Step 6: API Connectivity Test

### 6.1 Test Chargily Client Connection
Create a test script to verify API connectivity:

```javascript
// test-chargily-connection.js
const { ChargilyClient } = require('@chargily/chargily-pay');

const client = new ChargilyClient({
  api_key: process.env.CHARGILY_API_KEY,
  mode: process.env.CHARGILY_MODE || 'test'
});

async function testConnection() {
  try {
    // Test creating a customer
    const customer = await client.createCustomer({
      name: 'Test Customer',
      email: '<EMAIL>'
    });
    console.log('✅ Chargily connection successful:', customer.id);
    
    // Test creating a product
    const product = await client.createProduct({
      name: 'Test Product',
      description: 'Test product for API verification'
    });
    console.log('✅ Product creation successful:', product.id);
    
  } catch (error) {
    console.error('❌ Chargily connection failed:', error.message);
  }
}

testConnection();
```

### 6.2 Run Connection Test
```bash
cd app
node test-chargily-connection.js
```

## 🔧 Step 7: Integration Testing

### 7.1 Test Complete Payment Flow
1. **Start Application**: `wasp start`
2. **Start ngrok**: `ngrok http 3001`
3. **Navigate to Pricing Page**: `http://localhost:3000/pricing`
4. **Select Chargily Payment Method**
5. **Complete Test Payment** using test card numbers
6. **Verify Webhook Reception** in application logs
7. **Check User Credits** in account page

### 7.2 Test Scenarios
- [ ] Successful EDAHABIA payment
- [ ] Successful CIB payment
- [ ] Failed payment handling
- [ ] Webhook signature verification
- [ ] Subscription creation
- [ ] Credit allocation
- [ ] User account updates

## 📝 Development Team Documentation

### API Endpoints Available
- `POST /api/auth/payment/chargily/checkout` - Create checkout session
- `GET /api/auth/payment/chargily/status` - Get payment status
- `POST /api/payment/chargily/webhook` - Webhook handler (public)
- `POST /api/auth/payment/chargily/customer` - Customer management

### Test Credentials Summary
```bash
# Test Mode Configuration
CHARGILY_MODE=test
CHARGILY_API_KEY=test_pk_[your_key]
CHARGILY_WEBHOOK_SECRET=[your_secret]

# Test Cards
EDAHABIA: 0000 0000 0000 0001
CIB: 4000 0000 0000 0002
```

### Troubleshooting
1. **Webhook not receiving**: Check ngrok URL and Chargily webhook configuration
2. **API errors**: Verify API key and mode settings
3. **Payment failures**: Ensure test card numbers are correct
4. **Database issues**: Run `wasp db migrate-dev` to update schema

## 🚀 Production Deployment Notes

When moving to production:
1. Change `CHARGILY_MODE=live`
2. Use live API keys from Chargily dashboard
3. Update webhook URL to production domain
4. Create live products with real pricing
5. Test with real Algerian payment methods

## 🧪 Automated Testing

### Integration Test Suite
Run comprehensive integration tests:

```bash
# Test all payment processors and APIs
node test-payment-integration.cjs

# Test Chargily-specific integration
node test-chargily-integration.cjs

# Test with ngrok webhook setup
node setup-ngrok-testing.cjs 3001
```

### Test Scripts Available
- `test-chargily-connection.cjs` - API connectivity testing
- `test-chargily-webhooks.cjs` - Webhook testing with ngrok
- `test-chargily-integration.cjs` - Complete integration readiness
- `test-payment-integration.cjs` - Multi-processor payment testing
- `setup-ngrok-testing.cjs` - Automated ngrok setup and webhook testing

## 📞 Support

- **Chargily Documentation**: [docs.chargily.com](https://docs.chargily.com)
- **Chargily Support**: <EMAIL>
- **Integration Issues**: Check application logs and webhook delivery status
