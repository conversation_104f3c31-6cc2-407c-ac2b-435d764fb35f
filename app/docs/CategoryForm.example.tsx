import React, { useState, useEffect } from 'react';
import { 
  CreateCategoryRequest, 
  UpdateCategoryRequest,
  CategoryFormData,
  ProviderCategoryWithRelations,
  convertFormDataToRequest,
  convertCategoryToFormData,
  validateCategoryData,
  DEFAULT_CATEGORY_VALUES
} from './ProviderCategoryTypes';

interface CategoryFormProps {
  category?: ProviderCategoryWithRelations;
  parentCategories?: ProviderCategoryWithRelations[];
  onSubmit: (data: CreateCategoryRequest | UpdateCategoryRequest) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

const CategoryForm: React.FC<CategoryFormProps> = ({
  category,
  parentCategories = [],
  onSubmit,
  onCancel,
  isLoading = false
}) => {
  const [formData, setFormData] = useState<CategoryFormData>({
    title: '',
    description: '',
    parentId: '',
    isActive: true,
    sortOrder: '0',
    metadata: {
      icon: '',
      color: '#EF4444',
      keywords: '',
      seoTitle: '',
      seoDescription: ''
    }
  });

  const [errors, setErrors] = useState<string[]>([]);
  const [showAdvanced, setShowAdvanced] = useState(false);

  useEffect(() => {
    if (category) {
      setFormData(convertCategoryToFormData(category));
      setShowAdvanced(true);
    } else {
      setFormData(prev => ({
        ...prev,
        ...DEFAULT_CATEGORY_VALUES,
        metadata: {
          icon: '',
          color: '#EF4444',
          keywords: '',
          seoTitle: '',
          seoDescription: ''
        }
      }));
    }
  }, [category]);

  const handleInputChange = (field: keyof CategoryFormData, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    setErrors([]);
  };

  const handleMetadataChange = (field: keyof CategoryFormData['metadata'], value: string) => {
    setFormData(prev => ({
      ...prev,
      metadata: {
        ...prev.metadata,
        [field]: value
      }
    }));
    setErrors([]);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const requestData = convertFormDataToRequest(formData);
    const validationErrors = validateCategoryData(requestData);
    
    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      return;
    }

    try {
      await onSubmit(requestData);
    } catch (error: any) {
      setErrors([error.message || 'Failed to save category']);
    }
  };

  const availableParents = parentCategories.filter(p => p.id !== category?.id);

  return (
    <form onSubmit={handleSubmit} className="category-form">
      <div className="form-header">
        <h2>{category ? 'Edit Category' : 'Create New Category'}</h2>
      </div>

      {errors.length > 0 && (
        <div className="error-messages">
          {errors.map((error, index) => (
            <div key={index} className="error-message">{error}</div>
          ))}
        </div>
      )}

      {/* Basic Information */}
      <div className="form-section">
        <h3>Basic Information</h3>
        
        <div className="form-group">
          <label htmlFor="title">Title *</label>
          <input
            id="title"
            type="text"
            value={formData.title}
            onChange={(e) => handleInputChange('title', e.target.value)}
            placeholder="e.g., Lawyers, Doctors, Restaurants"
            required
            disabled={isLoading}
          />
        </div>

        <div className="form-group">
          <label htmlFor="description">Description</label>
          <textarea
            id="description"
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            placeholder="Detailed description of this category"
            rows={3}
            disabled={isLoading}
          />
        </div>

        <div className="form-row">
          <div className="form-group">
            <label htmlFor="parentId">Parent Category</label>
            <select
              id="parentId"
              value={formData.parentId}
              onChange={(e) => handleInputChange('parentId', e.target.value)}
              disabled={isLoading}
            >
              <option value="">No Parent (Root Category)</option>
              {availableParents.map(parent => (
                <option key={parent.id} value={parent.id}>
                  {parent.title}
                </option>
              ))}
            </select>
          </div>

          <div className="form-group">
            <label htmlFor="sortOrder">Sort Order</label>
            <input
              id="sortOrder"
              type="number"
              value={formData.sortOrder}
              onChange={(e) => handleInputChange('sortOrder', e.target.value)}
              min="0"
              max="9999"
              disabled={isLoading}
            />
          </div>
        </div>

        <div className="form-group">
          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={formData.isActive}
              onChange={(e) => handleInputChange('isActive', e.target.checked)}
              disabled={isLoading}
            />
            Active (visible to users)
          </label>
        </div>
      </div>

      {/* Advanced Settings */}
      <div className="form-section">
        <div className="section-header">
          <h3>Advanced Settings</h3>
          <button
            type="button"
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="toggle-button"
          >
            {showAdvanced ? 'Hide' : 'Show'} Advanced
          </button>
        </div>

        {showAdvanced && (
          <>
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="icon">Icon Class</label>
                <input
                  id="icon"
                  type="text"
                  value={formData.metadata.icon}
                  onChange={(e) => handleMetadataChange('icon', e.target.value)}
                  placeholder="e.g., fas fa-gavel, fas fa-stethoscope"
                  disabled={isLoading}
                />
              </div>

              <div className="form-group">
                <label htmlFor="color">Color</label>
                <div className="color-input-group">
                  <input
                    id="color"
                    type="color"
                    value={formData.metadata.color}
                    onChange={(e) => handleMetadataChange('color', e.target.value)}
                    disabled={isLoading}
                  />
                  <input
                    type="text"
                    value={formData.metadata.color}
                    onChange={(e) => handleMetadataChange('color', e.target.value)}
                    placeholder="#EF4444"
                    pattern="^#[0-9A-Fa-f]{6}$"
                    disabled={isLoading}
                  />
                </div>
              </div>
            </div>

            <div className="form-group">
              <label htmlFor="keywords">Keywords</label>
              <input
                id="keywords"
                type="text"
                value={formData.metadata.keywords}
                onChange={(e) => handleMetadataChange('keywords', e.target.value)}
                placeholder="legal, lawyer, attorney, law (comma-separated)"
                disabled={isLoading}
              />
              <small>Separate keywords with commas. Used for search and filtering.</small>
            </div>

            <div className="form-group">
              <label htmlFor="seoTitle">SEO Title</label>
              <input
                id="seoTitle"
                type="text"
                value={formData.metadata.seoTitle}
                onChange={(e) => handleMetadataChange('seoTitle', e.target.value)}
                placeholder="Find Professional Lawyers"
                maxLength={60}
                disabled={isLoading}
              />
              <small>Recommended: 50-60 characters</small>
            </div>

            <div className="form-group">
              <label htmlFor="seoDescription">SEO Description</label>
              <textarea
                id="seoDescription"
                value={formData.metadata.seoDescription}
                onChange={(e) => handleMetadataChange('seoDescription', e.target.value)}
                placeholder="Connect with experienced lawyers and legal professionals in your area"
                maxLength={160}
                rows={2}
                disabled={isLoading}
              />
              <small>Recommended: 150-160 characters</small>
            </div>
          </>
        )}
      </div>

      {/* Form Actions */}
      <div className="form-actions">
        <button
          type="button"
          onClick={onCancel}
          className="btn btn-secondary"
          disabled={isLoading}
        >
          Cancel
        </button>
        <button
          type="submit"
          className="btn btn-primary"
          disabled={isLoading}
        >
          {isLoading ? 'Saving...' : (category ? 'Update Category' : 'Create Category')}
        </button>
      </div>

      <style jsx>{`
        .category-form {
          max-width: 800px;
          margin: 0 auto;
          padding: 2rem;
          background: white;
          border-radius: 8px;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .form-header h2 {
          margin: 0 0 2rem 0;
          color: #333;
        }

        .error-messages {
          background: #fee;
          border: 1px solid #fcc;
          border-radius: 4px;
          padding: 1rem;
          margin-bottom: 2rem;
        }

        .error-message {
          color: #c33;
          margin: 0.25rem 0;
        }

        .form-section {
          margin-bottom: 2rem;
          padding-bottom: 2rem;
          border-bottom: 1px solid #eee;
        }

        .form-section:last-of-type {
          border-bottom: none;
        }

        .section-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1rem;
        }

        .section-header h3 {
          margin: 0;
          color: #555;
        }

        .toggle-button {
          background: none;
          border: 1px solid #ddd;
          padding: 0.5rem 1rem;
          border-radius: 4px;
          cursor: pointer;
          color: #666;
        }

        .form-group {
          margin-bottom: 1.5rem;
        }

        .form-row {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 1rem;
        }

        .form-group label {
          display: block;
          margin-bottom: 0.5rem;
          font-weight: 500;
          color: #333;
        }

        .checkbox-label {
          display: flex !important;
          align-items: center;
          gap: 0.5rem;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
          width: 100%;
          padding: 0.75rem;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 1rem;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
          outline: none;
          border-color: #007bff;
          box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        .color-input-group {
          display: flex;
          gap: 0.5rem;
        }

        .color-input-group input[type="color"] {
          width: 60px;
          height: 40px;
          padding: 0;
          border: 1px solid #ddd;
        }

        .form-group small {
          display: block;
          margin-top: 0.25rem;
          color: #666;
          font-size: 0.875rem;
        }

        .form-actions {
          display: flex;
          justify-content: flex-end;
          gap: 1rem;
          margin-top: 2rem;
          padding-top: 2rem;
          border-top: 1px solid #eee;
        }

        .btn {
          padding: 0.75rem 1.5rem;
          border: none;
          border-radius: 4px;
          font-size: 1rem;
          cursor: pointer;
          transition: background-color 0.2s;
        }

        .btn:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .btn-primary {
          background: #007bff;
          color: white;
        }

        .btn-primary:hover:not(:disabled) {
          background: #0056b3;
        }

        .btn-secondary {
          background: #6c757d;
          color: white;
        }

        .btn-secondary:hover:not(:disabled) {
          background: #545b62;
        }
      `}</style>
    </form>
  );
};

export default CategoryForm;
