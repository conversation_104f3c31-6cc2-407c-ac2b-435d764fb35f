# Chargily Pay API Integration Documentation

## 🎯 Overview

This document provides comprehensive API documentation for the Chargily Pay integration in the appointment booking system. Chargily Pay is an Algerian payment gateway that supports local payment methods including EDAHABIA (Algerie Post) and CIB (SATIM).

## 🔗 Base URLs

- **Development**: `https://your-ngrok-url.ngrok.io`
- **Test Environment**: `https://dapi-test.adscloud.org:8443`
- **Production**: `https://dapi.adscloud.org`

## 🔐 Authentication

All authenticated endpoints require a valid JWT token in the Authorization header:

```
Authorization: Bearer YOUR_JWT_TOKEN
```

## 📋 API Endpoints

### 1. Customer Management

#### Create Chargily Customer
**POST** `/api/auth/payment/chargily/customer`

Creates a new customer in Chargily Pay system.

**Request Body:**
```json
{
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "phone": "+213555123456",
  "address": {
    "country": "DZ",
    "state": "Algiers",
    "address": "123 Rue de la Liberté, Algiers"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Chargily customer created successfully",
  "data": {
    "customerId": "customer_abc123",
    "name": "Ahmed Ben Ali",
    "email": "<EMAIL>",
    "phone": "+213555123456"
  }
}
```

#### Get Customer Information
**GET** `/api/auth/payment/chargily/customer`

Retrieves current user's Chargily customer information.

**Response:**
```json
{
  "success": true,
  "message": "Customer information retrieved successfully",
  "data": {
    "customerId": "customer_abc123",
    "name": "Ahmed Ben Ali",
    "email": "<EMAIL>",
    "phone": "+213555123456",
    "paymentMethods": ["edahabia", "cib"]
  }
}
```

#### Update Customer Information
**PUT** `/api/auth/payment/chargily/customer`

Updates customer information in Chargily Pay.

**Request Body:**
```json
{
  "name": "Ahmed Ben Ali Updated",
  "phone": "+213555654321",
  "address": {
    "country": "DZ",
    "state": "Oran",
    "address": "456 Boulevard de la République, Oran"
  }
}
```

### 2. Checkout and Payments

#### Create Checkout Session
**POST** `/api/auth/payment/chargily/checkout`

Creates a new checkout session for payment processing.

**Request Body:**
```json
{
  "planId": "hobby",
  "paymentMethod": "edahabia",
  "successUrl": "https://yourapp.com/payment/success",
  "failureUrl": "https://yourapp.com/payment/failure"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Checkout session created successfully",
  "data": {
    "sessionId": "checkout_xyz789",
    "checkoutUrl": "https://pay.chargily.com/checkout/xyz789",
    "amount": 2000,
    "currency": "DZD",
    "paymentMethod": "edahabia",
    "expiresAt": "2024-01-15T11:30:00.000Z"
  }
}
```

#### Get Payment Status
**GET** `/api/auth/payment/chargily/status`

Retrieves current user's payment status and transaction history.

**Response:**
```json
{
  "success": true,
  "message": "Payment status retrieved successfully",
  "data": {
    "user": {
      "id": "user-uuid",
      "credits": 200,
      "subscriptionStatus": "active",
      "paymentProcessor": "chargily"
    },
    "recentTransactions": [
      {
        "id": "payment_123",
        "amount": 2000,
        "currency": "DZD",
        "status": "paid",
        "paymentMethod": "edahabia",
        "createdAt": "2024-01-15T10:30:00.000Z"
      }
    ],
    "subscription": {
      "planId": "hobby",
      "status": "active",
      "nextBillingDate": "2024-02-15T10:30:00.000Z"
    }
  }
}
```

### 3. Payment Links

#### Create Payment Link
**POST** `/api/auth/payment/chargily/payment-links`

Creates a reusable payment link for services.

**Request Body:**
```json
{
  "name": "Appointment Booking - Hobby Plan",
  "planId": "hobby",
  "description": "Monthly subscription for appointment booking",
  "metadata": {
    "userId": "user-uuid",
    "planType": "subscription"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Payment link created successfully",
  "data": {
    "paymentLinkId": "link_abc123",
    "paymentLinkUrl": "https://pay.chargily.com/links/abc123",
    "name": "Appointment Booking - Hobby Plan",
    "amount": 2000,
    "currency": "DZD"
  }
}
```

#### Get Payment Links
**GET** `/api/auth/payment/chargily/payment-links`

Retrieves user's payment links and their status.

**Response:**
```json
{
  "success": true,
  "message": "Payment links retrieved successfully",
  "data": {
    "paymentLinks": [
      {
        "id": "link_abc123",
        "name": "Appointment Booking - Hobby Plan",
        "url": "https://pay.chargily.com/links/abc123",
        "amount": 2000,
        "currency": "DZD",
        "status": "active",
        "createdAt": "2024-01-15T10:30:00.000Z"
      }
    ]
  }
}
```

### 4. Multi-Processor Support

#### Get Available Payment Processors
**GET** `/api/auth/payment/processors`

Returns available payment processors based on user's location.

**Response:**
```json
{
  "success": true,
  "message": "Available payment processors retrieved successfully",
  "data": {
    "processors": [
      {
        "id": "chargily",
        "name": "Chargily Pay",
        "description": "Algerian payment gateway (EDAHABIA/CIB)",
        "supportedMethods": ["edahabia", "cib"],
        "currency": "DZD",
        "isRecommended": true,
        "isAvailable": true
      },
      {
        "id": "lemonsqueezy",
        "name": "Lemon Squeezy",
        "description": "Global payment processing",
        "supportedMethods": ["card", "paypal"],
        "currency": "USD",
        "isRecommended": false,
        "isAvailable": true
      }
    ],
    "userCountryCode": "DZ",
    "defaultProcessor": "lemonsqueezy",
    "recommendedProcessor": "chargily"
  }
}
```

#### Create Multi-Processor Checkout
**POST** `/api/auth/payment/checkout`

Creates checkout session with processor selection support.

**Request Body:**
```json
{
  "planId": "hobby",
  "paymentProcessor": "chargily"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Checkout session created successfully",
  "data": {
    "sessionUrl": "https://pay.chargily.com/checkout/xyz789",
    "sessionId": "checkout_xyz789",
    "planId": "hobby",
    "planName": "Starter",
    "paymentProcessor": "chargily"
  }
}
```

## 🔔 Webhooks

### Webhook Endpoint
**POST** `/api/payment/chargily/webhook`

Receives webhook events from Chargily Pay (public endpoint, no authentication required).

### Supported Events

#### checkout.paid
Triggered when a payment is successfully completed.

```json
{
  "id": "evt_checkout_paid_123",
  "type": "checkout.paid",
  "data": {
    "id": "checkout_xyz789",
    "status": "paid",
    "amount": 2000,
    "currency": "dzd",
    "payment_method": "edahabia",
    "customer_id": "customer_abc123",
    "metadata": {
      "userId": "user-uuid",
      "planId": "hobby",
      "planType": "subscription"
    }
  }
}
```

#### checkout.failed
Triggered when a payment fails.

```json
{
  "id": "evt_checkout_failed_456",
  "type": "checkout.failed",
  "data": {
    "id": "checkout_xyz789",
    "status": "failed",
    "failure_reason": "insufficient_funds",
    "customer_id": "customer_abc123"
  }
}
```

#### subscription.created
Triggered when a subscription is created.

```json
{
  "id": "evt_subscription_created_789",
  "type": "subscription.created",
  "data": {
    "id": "subscription_def456",
    "status": "active",
    "customer_id": "customer_abc123",
    "plan_id": "price_hobby_monthly",
    "current_period_start": "2024-01-15T10:30:00.000Z",
    "current_period_end": "2024-02-15T10:30:00.000Z"
  }
}
```

### Webhook Signature Verification

All webhooks include a signature in the `Chargily-Signature` header for verification:

```javascript
const crypto = require('crypto');

function verifyWebhookSignature(payload, signature, secret) {
  const [timestamp, hash] = signature.split(',');
  const t = timestamp.split('=')[1];
  const v1 = hash.split('=')[1];
  
  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(`${t}.${payload}`)
    .digest('hex');
  
  return crypto.timingSafeEqual(
    Buffer.from(expectedSignature),
    Buffer.from(v1)
  );
}
```

## 💰 Supported Payment Methods

### EDAHABIA (Algerie Post)
- **Card Type**: Debit card issued by Algerie Post
- **Currency**: DZD (Algerian Dinar)
- **Test Card**: `0000 0000 0000 0001`
- **PIN**: `1234`

### CIB (SATIM)
- **Card Type**: Credit/Debit cards issued by Algerian banks
- **Currency**: DZD (Algerian Dinar)  
- **Test Card**: `4000 0000 0000 0002`
- **PIN**: `1234`

## 🔧 Error Handling

### Common Error Responses

#### 400 Bad Request
```json
{
  "success": false,
  "message": "Invalid request body: planId is required"
}
```

#### 401 Unauthorized
```json
{
  "success": false,
  "message": "Authentication required"
}
```

#### 404 Not Found
```json
{
  "success": false,
  "message": "Customer not found"
}
```

#### 500 Internal Server Error
```json
{
  "success": false,
  "message": "Failed to create checkout session: API key invalid"
}
```

## 🧪 Testing

### Test Environment
- Use `CHARGILY_MODE=test` in environment variables
- Test API keys start with `test_pk_` and `test_sk_`
- Use provided test card numbers for payment testing

### Integration Testing
```bash
# Run comprehensive integration tests
node test-payment-integration.cjs

# Test Chargily-specific functionality
node test-chargily-integration.cjs

# Set up webhook testing with ngrok
node setup-ngrok-testing.cjs 3001
```

## 📚 Additional Resources

- **Chargily Pay Documentation**: [docs.chargily.com](https://docs.chargily.com)
- **Test Environment Setup**: See `docs/chargily-test-environment-setup.md`
- **Integration Examples**: Check `test-*.cjs` files in project root
