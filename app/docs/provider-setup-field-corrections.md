# Provider Setup API - Field Name Corrections

## ⚠️ CRITICAL: Field Name Requirements

**The API validation is very strict about field names. Using incorrect field names will cause 400 Bad Request errors.**

## Required Field Name Changes

### 1. Time Fields in Opening Hours
**❌ WRONG:**
```json
{
  "hours": [
    {
      "start": "09:00",
      "end": "17:00"
    }
  ]
}
```

**✅ CORRECT:**
```json
{
  "hours": [
    {
      "timeFrom": "09:00",
      "timeTo": "17:00"
    }
  ]
}
```

### 2. Phone Fields in Business Info
**❌ WRONG:**
```json
{
  "phones": {
    "mobile": "**********",
    "landline": "**********"
  }
}
```

**✅ CORRECT:**
```json
{
  "phones": {
    "mobile": "**********",
    "fixedLine": "**********"
  }
}
```

### 3. Queue Opening Hours Field Name
**❌ WRONG:**
```json
{
  "queues": [
    {
      "name": "Main Queue",
      "locationIndex": 0,
      "serviceIndices": [0],
      "openingHours": [...]
    }
  ]
}
```

**✅ CORRECT:**
```json
{
  "queues": [
    {
      "name": "Main Queue",
      "locationIndex": 0,
      "serviceIndices": [0],
      "customOpeningHours": [...]
    }
  ]
}
```

## Complete Corrected Payload Template

```json
{
  "businessInfo": {
    "businessName": "Your Business Name",
    "bio": "Your business description",
    "shortName": null,
    "logoPath": null,
    "phones": {
      "mobile": "**********",
      "fixedLine": "**********"
    }
  },
  "locations": [
    {
      "name": "Location Name",
      "address": "Your Address",
      "city": "Your City",
      "country": "Your Country",
      "timezone": "Africa/Algiers",
      "parking": true,
      "elevator": true,
      "handicapAccess": true,
      "openingHours": [
        {
          "dayOfWeek": "Monday",
          "isActive": true,
          "hours": [
            {
              "timeFrom": "09:00",
              "timeTo": "17:00"
            }
          ]
        }
      ]
    }
  ],
  "services": [
    {
      "title": "Service Name",
      "duration": 30,
      "pointsRequirements": 1,
      "isPublic": true,
      "deliveryType": "at_location",
      "acceptOnline": true,
      "acceptNew": true,
      "notificationOn": true
    }
  ],
  "queues": [
    {
      "name": "Queue Name",
      "locationIndex": 0,
      "serviceIndices": [0],
      "customOpeningHours": [
        {
          "dayOfWeek": "Monday",
          "isActive": true,
          "hours": [
            {
              "timeFrom": "09:00",
              "timeTo": "17:00"
            }
          ]
        }
      ]
    }
  ]
}
```

## Flutter Implementation Guide

When building the payload in Flutter, ensure you use the correct field names:

```dart
// ❌ WRONG
final timeSlot = {
  "start": startTime,
  "end": endTime,
};

// ✅ CORRECT
final timeSlot = {
  "timeFrom": startTime,
  "timeTo": endTime,
};

// ❌ WRONG
final phones = {
  "mobile": mobileNumber,
  "landline": landlineNumber,
};

// ✅ CORRECT
final phones = {
  "mobile": mobileNumber,
  "fixedLine": landlineNumber,
};

// ❌ WRONG
final queue = {
  "name": queueName,
  "openingHours": hours,
};

// ✅ CORRECT
final queue = {
  "name": queueName,
  "customOpeningHours": hours,
};
```

## Common Validation Errors

### Error: "timeFrom Required"
**Cause:** Using `"start"` instead of `"timeFrom"`
**Solution:** Change all `"start"` fields to `"timeFrom"`

### Error: "timeTo Required"
**Cause:** Using `"end"` instead of `"timeTo"`
**Solution:** Change all `"end"` fields to `"timeTo"`

### Error: "Expected string, received null"
**Cause:** Sending `null` for optional fields that don't accept null
**Solution:** Either omit the field or ensure the schema accepts `.nullable().optional()`

## Testing Your Payload

Before sending to production, test your payload structure:

1. **Validate field names** against this document
2. **Check time format** (HH:MM format required)
3. **Verify phone field names** (`fixedLine` not `landline`)
4. **Confirm queue field name** (`customOpeningHours` not `openingHours`)

## Success Response

When the payload is correct, you'll receive:

```json
{
  "message": "Provider setup completed successfully",
  "provider": {
    "id": 2,
    "title": "Your Business Name",
    "presentation": "Your business description",
    "isSetupComplete": true
  },
  "summary": {
    "locationsCreated": 1,
    "servicesCreated": 1,
    "queuesCreated": 1
  }
}
```

**Remember: Field names are case-sensitive and must match exactly!**
