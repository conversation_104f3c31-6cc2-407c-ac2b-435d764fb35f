# Mobile Chat Messaging API Documentation

This document describes the mobile API endpoints available for the chat messaging system in the provider Flutter app.

## Authentication

All endpoints require authentication via J<PERSON><PERSON> token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

## Base URL

All endpoints are prefixed with `/api/auth/mobile/` for messaging operations and `/api/auth/providers/` for provider-specific operations.

## Endpoints

### 1. Get Conversations

**GET** `/api/auth/mobile/conversations`

Retrieves all conversations for the authenticated provider.

**Response:**
```json
[
  {
    "id": 1,
    "name": null,
    "isGroup": false,
    "displayName": "<PERSON>",
    "displayImage": null,
    "lastMessage": {
      "content": "Hello, when can I schedule my appointment?",
      "senderName": "<PERSON>",
      "createdAt": "2024-01-15T10:30:00.000Z"
    },
    "unreadCount": 2,
    "updatedAt": "2024-01-15T10:30:00.000Z"
  }
]
```

### 2. Get Messages in a Conversation

**GET** `/api/auth/mobile/messages/:conversationId`

Retrieves all messages in a specific conversation.

**Parameters:**
- `conversationId` (path parameter): The ID of the conversation

**Response:**
```json
[
  {
    "id": 1,
    "content": "Hello, when can I schedule my appointment?",
    "createdAt": "2024-01-15T10:30:00.000Z",
    "sender": {
      "id": "customer-uuid",
      "name": "<PERSON> <PERSON>e"
    },
    "status": "DELIVERED"
  },
  {
    "id": 2,
    "content": "I have availability tomorrow at 2 PM",
    "createdAt": "2024-01-15T10:35:00.000Z",
    "sender": {
      "id": "provider-uuid",
      "name": "Dr. Smith"
    },
    "status": "SENT"
  }
]
```

### 3. Send a Message

**POST** `/api/auth/mobile/messages`

Sends a message in an existing conversation.

**Request Body:**
```json
{
  "conversationId": 1,
  "content": "I have availability tomorrow at 2 PM"
}
```

**Response:**
```json
{
  "id": 2,
  "content": "I have availability tomorrow at 2 PM",
  "createdAt": "2024-01-15T10:35:00.000Z",
  "sender": {
    "id": "provider-uuid",
    "name": "Dr. Smith"
  },
  "status": "SENT"
}
```

### 4. Mark Message as Read

**POST** `/api/auth/mobile/messages/read`

Marks a message as read and updates unread counts.

**Request Body:**
```json
{
  "messageId": 1,
  "conversationId": 1
}
```

**Response:**
```json
{
  "success": true
}
```

### 5. Start New Conversation

**POST** `/api/auth/mobile/conversations/start`

Creates a new conversation with specified customers.

**Request Body:**

For 1-on-1 conversation:
```json
{
  "otherUserIds": ["customer-uuid-123"],
  "isGroup": false
}
```

For group conversation:
```json
{
  "otherUserIds": ["customer-uuid-123", "customer-uuid-456"],
  "name": "Appointment Discussion",
  "isGroup": true
}
```

**Response:**
```json
{
  "id": 3,
  "name": null,
  "isGroup": false,
  "displayName": "Direct Message",
  "createdAt": "2024-01-15T11:00:00.000Z",
  "updatedAt": "2024-01-15T11:00:00.000Z"
}
```

### 6. Get Provider Customers

**GET** `/api/auth/providers/customers`

Retrieves the list of customers associated with the provider (useful for starting new conversations).

**Response:**
```json
[
  {
    "id": "customer-folder-id",
    "customer": {
      "id": "customer-uuid-123",
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>",
      "mobileNumber": "+**********"
    }
  }
]
```

## Error Responses

All endpoints return standard HTTP status codes with error details:

```json
{
  "message": "Error description",
  "statusCode": 400
}
```

Common error codes:
- `400` - Bad Request (invalid parameters)
- `401` - Unauthorized (invalid or missing token)
- `403` - Forbidden (no permission for this conversation)
- `404` - Not Found (conversation or message not found)
- `409` - Conflict (conversation already exists)
- `500` - Internal Server Error

## Usage Flow for Flutter App

1. **Load Conversations**: Call `GET /conversations` on app start
2. **Display Conversation List**: Show conversations with unread counts and last messages
3. **Load Messages**: When user taps a conversation, call `GET /messages/:id`
4. **Send Messages**: Use `POST /messages` when user sends a message
5. **Start New Chat**: 
   - First, get customers list with `GET /providers/customers`
   - Let user select customer(s)
   - Call `POST /conversations/start` to create conversation
6. **Mark as Read**: Call `POST /messages/read` when user views messages

## WebSocket Support

The system also supports real-time messaging via WebSocket connections for live message updates. Messages are broadcast to conversation rooms when sent.

## Rate Limiting

API calls are subject to standard rate limiting. Implement appropriate retry logic with exponential backoff for production use. 