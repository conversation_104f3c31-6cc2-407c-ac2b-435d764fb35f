# Provider Dashboard - Chargily Pay API Documentation

## 🎯 Overview

This document provides comprehensive API documentation for integrating Chargily Pay (Algerian payment gateway) into the Provider Dashboard system. Chargily Pay supports local Algerian payment methods including EDAHABIA (Algerie Post) and CIB (SATIM).

## 🔗 Base URLs

- **Development**: `https://dapi-test.adscloud.org:8443`
- **Production**: `https://dapi.adscloud.org`

## 🔐 Authentication

All provider endpoints require authentication with a valid JWT token:

```
Authorization: Bearer YOUR_PROVIDER_JWT_TOKEN
```

## 📋 Provider Payment API Endpoints

### 1. Provider Subscription Management

#### Get Available Payment Methods
**GET** `/api/auth/providers/payment/methods`

Returns available payment methods for providers, including Chargily Pay for Algerian providers.

**Response:**
```json
{
  "success": true,
  "message": "Payment methods retrieved successfully",
  "data": {
    "methods": [
      {
        "id": "chargily",
        "name": "Chargily <PERSON>",
        "displayName": "Chargily <PERSON> (EDAHABIA/CIB)",
        "description": "Algerian payment gateway supporting local payment methods",
        "supportedMethods": ["edahabia", "cib"],
        "currency": "DZD",
        "isRecommended": true,
        "isAvailable": true,
        "region": ["DZ", "Algeria"],
        "features": [
          "EDAHABIA (Algerie Post)",
          "CIB (SATIM)",
          "DZD currency support",
          "Local Algerian support"
        ]
      },
      {
        "id": "lemonsqueezy",
        "name": "Lemon Squeezy",
        "displayName": "Credit/Debit Card",
        "description": "Global payment processing",
        "supportedMethods": ["card", "paypal"],
        "currency": "USD",
        "isRecommended": false,
        "isAvailable": true
      }
    ],
    "recommendedMethod": "chargily",
    "providerCountry": "DZ"
  }
}
```

#### Create Provider Subscription Checkout
**POST** `/api/auth/providers/payment/checkout`

Creates a checkout session for provider subscription plans using Chargily Pay.

**Request Body:**
```json
{
  "planId": "provider_pro",
  "paymentProcessor": "chargily",
  "paymentMethod": "edahabia",
  "billingCycle": "monthly",
  "metadata": {
    "providerId": "provider-uuid",
    "businessName": "Dr. Ahmed's Clinic",
    "businessType": "healthcare"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Provider checkout session created successfully",
  "data": {
    "sessionId": "checkout_provider_xyz789",
    "checkoutUrl": "https://pay.chargily.com/checkout/xyz789",
    "planId": "provider_pro",
    "planName": "Provider Pro Plan",
    "amount": 5000,
    "currency": "DZD",
    "paymentMethod": "edahabia",
    "paymentProcessor": "chargily",
    "expiresAt": "2024-01-15T11:30:00.000Z",
    "metadata": {
      "providerId": "provider-uuid",
      "subscriptionType": "provider",
      "features": ["unlimited_appointments", "analytics", "multi_location"]
    }
  }
}
```

#### Get Provider Subscription Status
**GET** `/api/auth/providers/payment/status`

Retrieves current provider's subscription status and payment history.

**Response:**
```json
{
  "success": true,
  "message": "Provider payment status retrieved successfully",
  "data": {
    "provider": {
      "id": "provider-uuid",
      "businessName": "Dr. Ahmed's Clinic",
      "subscriptionPlan": "provider_pro",
      "subscriptionStatus": "active",
      "paymentProcessor": "chargily",
      "paymentMethod": "edahabia",
      "credits": 1000,
      "nextBillingDate": "2024-02-15T10:30:00.000Z",
      "billingCycle": "monthly"
    },
    "currentPlan": {
      "id": "provider_pro",
      "name": "Provider Pro Plan",
      "price": 5000,
      "currency": "DZD",
      "features": [
        "Unlimited appointments",
        "Advanced analytics",
        "Multi-location support",
        "Priority support"
      ]
    },
    "paymentHistory": [
      {
        "id": "payment_123",
        "amount": 5000,
        "currency": "DZD",
        "status": "paid",
        "paymentMethod": "edahabia",
        "description": "Provider Pro Plan - Monthly",
        "paidAt": "2024-01-15T10:30:00.000Z",
        "invoiceUrl": "https://pay.chargily.com/invoices/inv_123"
      }
    ],
    "usage": {
      "appointmentsThisMonth": 45,
      "appointmentLimit": 1000,
      "creditsUsed": 45,
      "creditsRemaining": 955
    }
  }
}
```

### 2. Provider Payment Links

#### Create Provider Payment Link
**POST** `/api/auth/providers/payment/payment-links`

Creates reusable payment links for provider services or subscriptions.

**Request Body:**
```json
{
  "name": "Provider Pro Subscription",
  "description": "Monthly subscription for healthcare providers",
  "planId": "provider_pro",
  "customAmount": false,
  "metadata": {
    "providerId": "provider-uuid",
    "linkType": "subscription",
    "businessCategory": "healthcare"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Provider payment link created successfully",
  "data": {
    "paymentLinkId": "link_provider_abc123",
    "paymentLinkUrl": "https://pay.chargily.com/links/abc123",
    "name": "Provider Pro Subscription",
    "amount": 5000,
    "currency": "DZD",
    "status": "active",
    "expiresAt": null,
    "usageCount": 0,
    "maxUsage": null
  }
}
```

#### Get Provider Payment Links
**GET** `/api/auth/providers/payment/payment-links`

Retrieves all payment links created by the provider.

**Response:**
```json
{
  "success": true,
  "message": "Provider payment links retrieved successfully",
  "data": {
    "paymentLinks": [
      {
        "id": "link_provider_abc123",
        "name": "Provider Pro Subscription",
        "url": "https://pay.chargily.com/links/abc123",
        "amount": 5000,
        "currency": "DZD",
        "status": "active",
        "usageCount": 3,
        "totalRevenue": 15000,
        "createdAt": "2024-01-15T10:30:00.000Z"
      }
    ],
    "totalLinks": 1,
    "totalRevenue": 15000
  }
}
```

### 3. Provider Customer Management

#### Create Provider Customer
**POST** `/api/auth/providers/payment/customers`

Creates a customer record in Chargily for the provider's clients.

**Request Body:**
```json
{
  "name": "Ahmed Ben Ali",
  "email": "<EMAIL>",
  "phone": "+************",
  "address": {
    "country": "DZ",
    "state": "Algiers",
    "address": "123 Rue de la Liberté, Algiers"
  },
  "metadata": {
    "providerId": "provider-uuid",
    "customerType": "patient",
    "registrationSource": "provider_dashboard"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Provider customer created successfully",
  "data": {
    "customerId": "customer_provider_abc123",
    "name": "Ahmed Ben Ali",
    "email": "<EMAIL>",
    "phone": "+************",
    "paymentMethods": ["edahabia", "cib"],
    "createdAt": "2024-01-15T10:30:00.000Z"
  }
}
```

### 4. Provider Revenue & Analytics

#### Get Provider Revenue Analytics
**GET** `/api/auth/providers/payment/analytics`

Retrieves revenue analytics and payment statistics for the provider.

**Query Parameters:**
- `period` - `daily`, `weekly`, `monthly`, `yearly`
- `startDate` - Start date (ISO format)
- `endDate` - End date (ISO format)

**Response:**
```json
{
  "success": true,
  "message": "Provider payment analytics retrieved successfully",
  "data": {
    "summary": {
      "totalRevenue": 150000,
      "totalTransactions": 30,
      "averageTransactionValue": 5000,
      "currency": "DZD",
      "period": "monthly"
    },
    "revenueByMethod": {
      "edahabia": {
        "amount": 100000,
        "transactions": 20,
        "percentage": 66.7
      },
      "cib": {
        "amount": 50000,
        "transactions": 10,
        "percentage": 33.3
      }
    },
    "revenueChart": [
      {
        "date": "2024-01-01",
        "amount": 10000,
        "transactions": 2
      },
      {
        "date": "2024-01-02",
        "amount": 15000,
        "transactions": 3
      }
    ],
    "topPlans": [
      {
        "planId": "provider_pro",
        "planName": "Provider Pro Plan",
        "revenue": 100000,
        "subscriptions": 20
      }
    ]
  }
}
```

## 💰 Provider Subscription Plans

### Available Plans for Providers

| Plan ID | Name | Price (DZD) | Features |
|---------|------|-------------|----------|
| `provider_basic` | Basic Provider | 2,000 | 100 appointments/month, Basic analytics |
| `provider_pro` | Pro Provider | 5,000 | 1000 appointments/month, Advanced analytics, Multi-location |
| `provider_enterprise` | Enterprise | 10,000 | Unlimited appointments, White-label, API access |

### Plan Features Comparison

```json
{
  "provider_basic": {
    "appointmentLimit": 100,
    "analytics": "basic",
    "locations": 1,
    "support": "email",
    "features": ["appointment_booking", "basic_reports"]
  },
  "provider_pro": {
    "appointmentLimit": 1000,
    "analytics": "advanced",
    "locations": 5,
    "support": "priority",
    "features": ["appointment_booking", "advanced_reports", "multi_location", "custom_branding"]
  },
  "provider_enterprise": {
    "appointmentLimit": "unlimited",
    "analytics": "enterprise",
    "locations": "unlimited",
    "support": "dedicated",
    "features": ["all_features", "api_access", "white_label", "custom_integrations"]
  }
}
```

## 🔔 Webhook Events

### Provider Payment Webhooks
**POST** `/api/payment/chargily/webhook`

Webhook events specific to provider payments:

#### provider.subscription.created
```json
{
  "id": "evt_provider_subscription_created",
  "type": "provider.subscription.created",
  "data": {
    "providerId": "provider-uuid",
    "subscriptionId": "sub_123",
    "planId": "provider_pro",
    "status": "active",
    "paymentMethod": "edahabia",
    "amount": 5000,
    "currency": "DZD"
  }
}
```

#### provider.payment.succeeded
```json
{
  "id": "evt_provider_payment_succeeded",
  "type": "provider.payment.succeeded",
  "data": {
    "providerId": "provider-uuid",
    "paymentId": "payment_123",
    "amount": 5000,
    "currency": "DZD",
    "paymentMethod": "edahabia",
    "planId": "provider_pro"
  }
}
```

## 🧪 Testing

### Test Credentials for Providers
```bash
# Provider Test Account
Email: <EMAIL>
Password: TestProvider123!

# Chargily Test Cards
EDAHABIA: 0000 0000 0000 0001
CIB: ************** 0002
PIN: 1234
```

### Integration Testing
```javascript
// Example: Create provider checkout
const response = await fetch('/api/auth/providers/payment/checkout', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${providerToken}`
  },
  body: JSON.stringify({
    planId: 'provider_pro',
    paymentProcessor: 'chargily',
    paymentMethod: 'edahabia'
  })
});

const result = await response.json();
if (result.success) {
  window.location.href = result.data.checkoutUrl;
}
```

## 📞 Support

- **Provider Support**: <EMAIL>
- **Chargily Integration**: <EMAIL>
- **Technical Issues**: Check application logs and webhook delivery status
- **Documentation**: See `/docs/chargily-test-environment-setup.md` for setup details
