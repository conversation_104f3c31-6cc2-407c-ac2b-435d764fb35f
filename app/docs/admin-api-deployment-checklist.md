# Admin API Deployment Checklist

This checklist ensures your admin API system is properly deployed and secured.

## Pre-Deployment Setup

### 1. Database Configuration
- [ ] **First Admin User Creation**
  ```sql
  -- Create first admin user manually in database
  INSERT INTO "User" (id, email, "firstName", "lastName", role, "isAdmin", "createdAt")
  VALUES (
    'admin-001', 
    '<EMAIL>', 
    'Admin', 
    'User', 
    'ADMIN', 
    true, 
    NOW()
  );
  ```

- [ ] **Provider Categories Setup**
  ```sql
  -- Create basic provider categories
  INSERT INTO "ProviderCategory" (title) VALUES 
  ('Healthcare'),
  ('Legal Services'),
  ('Consulting'),
  ('Technology'),
  ('Education');
  ```

### 2. Environment Variables
- [ ] **Production Environment Variables**
  ```env
  # Database
  DATABASE_URL="your-production-database-url"
  
  # Session Secret
  SESSION_SECRET="your-super-secure-session-secret"
  
  # Admin Configuration
  ADMIN_EMAILS="<EMAIL>,<EMAIL>"
  
  # Optional: Rate limiting
  RATE_LIMIT_ENABLED=true
  RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
  RATE_LIMIT_MAX_REQUESTS=100
  ```

### 3. Security Configuration
- [ ] **CORS Settings**
  ```typescript
  // Configure CORS for admin endpoints
  const corsOptions = {
    origin: ['https://yourdomain.com', 'https://admin.yourdomain.com'],
    credentials: true,
    optionsSuccessStatus: 200
  };
  ```

- [ ] **Rate Limiting**
  ```typescript
  // Implement rate limiting for admin endpoints
  const adminRateLimit = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: 'Too many requests from this IP'
  });
  ```

## Deployment Steps

### 1. Code Deployment
- [ ] **Update main.wasp**
  - Verify all admin API endpoints are properly configured
  - Check entity relationships are correct
  - Ensure authentication is required for protected endpoints

- [ ] **Deploy Backend**
  ```bash
  # Build and deploy your Wasp application
  wasp build
  wasp deploy
  ```

### 2. Database Migration
- [ ] **Run Migrations**
  ```bash
  # Apply all pending migrations
  wasp db migrate-deploy
  ```

- [ ] **Seed Data**
  ```bash
  # Run seeding script if you have one
  wasp db seed
  ```

### 3. Testing
- [ ] **Run Test Suite**
  ```bash
  # Run the admin API test suite
  node app/docs/admin-api-test.js
  ```

- [ ] **Manual Testing Checklist**
  - [ ] Admin login works with correct credentials
  - [ ] Admin login fails with incorrect credentials
  - [ ] Non-admin users cannot access admin endpoints
  - [ ] Provider list loads with pagination
  - [ ] Provider approval/rejection works
  - [ ] Customer list loads properly
  - [ ] Category CRUD operations work
  - [ ] Error handling works correctly

## Post-Deployment Verification

### 1. Security Verification
- [ ] **Admin Access Control**
  - [ ] Only admin users can access admin endpoints
  - [ ] Regular users receive 403 errors for admin endpoints
  - [ ] Unauthenticated users receive 401 errors

- [ ] **Session Management**
  - [ ] Admin sessions expire appropriately
  - [ ] Session tokens are secure and not predictable
  - [ ] Logout functionality works

### 2. Performance Verification
- [ ] **Response Times**
  - [ ] Admin login: < 1 second
  - [ ] Provider list: < 2 seconds
  - [ ] Customer list: < 2 seconds
  - [ ] Category operations: < 1 second

- [ ] **Database Performance**
  - [ ] Queries are optimized with proper indexes
  - [ ] No N+1 query issues
  - [ ] Pagination works efficiently

### 3. Logging and Monitoring
- [ ] **Error Logging**
  - [ ] Admin actions are logged
  - [ ] Errors are properly logged with context
  - [ ] Security events are tracked

- [ ] **Health Checks**
  - [ ] Admin API endpoints respond to health checks
  - [ ] Database connectivity is monitored
  - [ ] Session store is healthy

## Security Best Practices

### 1. Authentication & Authorization
- [ ] **Strong Password Requirements**
  - Minimum 8 characters
  - Mix of uppercase, lowercase, numbers, and symbols
  - Password history to prevent reuse

- [ ] **Two-Factor Authentication** (Recommended)
  - SMS or authenticator app
  - Backup codes for recovery

- [ ] **Session Security**
  - Secure session cookies
  - Session timeout after inactivity
  - Concurrent session limits

### 2. Data Protection
- [ ] **Input Validation**
  - All inputs validated on server-side
  - SQL injection prevention
  - XSS protection

- [ ] **Data Encryption**
  - Sensitive data encrypted at rest
  - HTTPS for all communications
  - Database connection encryption

### 3. Audit & Compliance
- [ ] **Activity Logging**
  - All admin actions logged
  - IP address tracking
  - Timestamp all actions

- [ ] **Regular Security Reviews**
  - Monthly security audits
  - Dependency vulnerability checks
  - Access review for admin users

## Monitoring & Maintenance

### 1. Performance Monitoring
- [ ] **Key Metrics**
  - Response time percentiles
  - Error rate tracking
  - Database query performance
  - Session store performance

- [ ] **Alerting**
  - High error rates
  - Slow response times
  - Authentication failures
  - Database connection issues

### 2. Regular Maintenance
- [ ] **Weekly Tasks**
  - Review error logs
  - Check performance metrics
  - Verify backup integrity

- [ ] **Monthly Tasks**
  - Security patch updates
  - Performance optimization
  - Access review
  - Log cleanup

### 3. Backup & Recovery
- [ ] **Database Backups**
  - Daily automated backups
  - Weekly backup restoration tests
  - Backup retention policy

- [ ] **Disaster Recovery**
  - Recovery procedure documentation
  - Recovery time objectives defined
  - Regular disaster recovery drills

## Troubleshooting Common Issues

### 1. Authentication Problems
```bash
# Check admin user exists in database
SELECT * FROM "User" WHERE role = 'ADMIN' AND email = '<EMAIL>';

# Verify session configuration
# Check SESSION_SECRET is set
echo $SESSION_SECRET

# Check session store connectivity
```

### 2. Performance Issues
```bash
# Check database query performance
EXPLAIN ANALYZE SELECT * FROM "SProvider" WHERE "isVerified" = false;

# Monitor memory usage
top -p $(pgrep -f "node.*wasp")

# Check database connections
SELECT count(*) FROM pg_stat_activity WHERE datname = 'your_database';
```

### 3. Permission Issues
```bash
# Verify user roles
SELECT id, email, role, "isAdmin" FROM "User" WHERE role = 'ADMIN';

# Check API endpoint configuration in main.wasp
grep -n "admin" main.wasp
```

## Success Criteria

Your admin API deployment is successful when:

✅ **Functionality**
- All admin endpoints respond correctly
- Authentication and authorization work properly
- CRUD operations complete successfully
- Error handling provides meaningful messages

✅ **Security**
- Only authorized users can access admin features
- All inputs are properly validated
- Sessions are secure and expire appropriately
- Admin actions are logged for audit

✅ **Performance**
- Response times meet acceptable thresholds
- Database queries are optimized
- System handles expected load
- Memory usage is stable

✅ **Reliability**
- System recovers gracefully from errors
- Backups are functioning
- Monitoring alerts are configured
- Documentation is complete and accurate

## Emergency Contacts

In case of issues:
- **Technical Lead**: [Your Name] - [<EMAIL>]
- **DevOps Team**: [Team Email] - [<EMAIL>]
- **Security Team**: [Security Email] - [<EMAIL>]

---

Last Updated: [Current Date]
Review Date: [Review Date] 