# Advertisement Image Upload APIs

## Overview

This document describes the new image upload APIs for advertisements that follow the same pattern as provider logo and profile picture uploads. These APIs use AWS S3 pre-signed URLs for secure file uploads.

## API Endpoints

### 1. Upload Advertisement Background Image

**Endpoint:** `POST /api/auth/admin/advertisements/:id/background-image`

**Authentication:** Required (Admin only)

**Description:** Generates a pre-signed S3 upload URL for uploading a background image to an advertisement.

#### Request Parameters

**Path Parameters:**
- `id` (number): Advertisement ID

**Request Body:**
```json
{
  "fileType": "image/jpeg" | "image/png",
  "fileName": "string"
}
```

#### Response

**Success (200):**
```json
{
  "success": true,
  "message": "Advertisement background image upload URL generated successfully",
  "data": {
    "uploadUrl": "https://s3-presigned-url...",
    "uploadFields": {
      "key": "user-id/uuid.jpg",
      "Content-Type": "image/jpeg",
      // ... other S3 fields
    },
    "file": {
      "id": "file-uuid",
      "name": "filename.jpg",
      "type": "image/jpeg",
      "key": "user-id/uuid.jpg"
    },
    "advertisement": {
      "id": 1,
      "backgroundImageId": "file-uuid",
      "pngImageId": "existing-png-id-or-null"
    }
  }
}
```

**Error Responses:**
- `401`: Authentication required
- `403`: Admin access required
- `404`: Advertisement not found
- `400`: Invalid file type or missing parameters
- `500`: Internal server error

---

### 2. Upload Advertisement PNG Image

**Endpoint:** `POST /api/auth/admin/advertisements/:id/png-image`

**Authentication:** Required (Admin only)

**Description:** Generates a pre-signed S3 upload URL for uploading a PNG image to an advertisement.

#### Request Parameters

**Path Parameters:**
- `id` (number): Advertisement ID

**Request Body:**
```json
{
  "fileType": "image/jpeg" | "image/png",
  "fileName": "string"
}
```

#### Response

**Success (200):**
```json
{
  "success": true,
  "message": "Advertisement PNG image upload URL generated successfully",
  "data": {
    "uploadUrl": "https://s3-presigned-url...",
    "uploadFields": {
      "key": "user-id/uuid.png",
      "Content-Type": "image/png",
      // ... other S3 fields
    },
    "file": {
      "id": "file-uuid",
      "name": "filename.png",
      "type": "image/png",
      "key": "user-id/uuid.png"
    },
    "advertisement": {
      "id": 1,
      "backgroundImageId": "existing-bg-id-or-null",
      "pngImageId": "file-uuid"
    }
  }
}
```

**Error Responses:**
- `401`: Authentication required
- `403`: Admin access required
- `404`: Advertisement not found
- `400`: Invalid file type or missing parameters
- `500`: Internal server error

## Upload Flow

### Step 1: Request Upload URL
Make a POST request to the appropriate endpoint with file metadata:

```javascript
const response = await fetch('/api/auth/admin/advertisements/1/background-image', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your-jwt-token'
  },
  body: JSON.stringify({
    fileType: 'image/jpeg',
    fileName: 'background.jpg'
  })
});

const { data } = await response.json();
```

### Step 2: Upload File to S3
Use the returned pre-signed URL and fields to upload the file directly to S3:

```javascript
const formData = new FormData();

// Add all the upload fields first
Object.entries(data.uploadFields).forEach(([key, value]) => {
  formData.append(key, value);
});

// Add the file last
formData.append('file', selectedFile);

// Upload to S3
await fetch(data.uploadUrl, {
  method: 'POST',
  body: formData
});
```

### Step 3: File is Ready
The file is now uploaded and the advertisement record is updated with the new image ID. The image can be accessed via the File model's uploadUrl or through signed URLs.

## Technical Implementation

### File Storage
- Files are stored in AWS S3 using the existing file upload infrastructure
- File metadata is stored in the `File` table
- Advertisement records are updated with the file IDs

### Security
- Admin authentication required for all upload operations
- File type validation (only JPEG and PNG allowed)
- S3 pre-signed URLs expire after 1 hour
- File size limits enforced by S3 conditions

### Database Relations
- `Advertisement.backgroundImageId` → `File.id`
- `Advertisement.pngImageId` → `File.id`
- Each advertisement can have one background image and one PNG image
- Images are optional (nullable foreign keys)

## Error Handling

All endpoints follow consistent error handling patterns:

1. **Validation Errors (400)**: Invalid request data, unsupported file types
2. **Authentication Errors (401)**: Missing or invalid JWT token
3. **Authorization Errors (403)**: Non-admin users
4. **Not Found Errors (404)**: Advertisement doesn't exist
5. **Server Errors (500)**: Database or S3 operation failures

## Integration with Existing Systems

These APIs integrate seamlessly with:
- Existing S3 file upload infrastructure
- Admin authentication middleware
- File management system
- Advertisement CRUD operations

The implementation follows the same patterns used for:
- Profile picture uploads (`/api/auth/user/profile-picture`)
- Provider logo uploads (`/api/auth/provider/logo`)
- Category image uploads (`/api/auth/admin/provider-categories/:id/image`)
