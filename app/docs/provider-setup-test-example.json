{"businessInfo": {"businessName": "Dr. <PERSON>'s Medical Center", "bio": "Comprehensive healthcare services with over 15 years of experience. Specializing in general medicine, pediatrics, and preventive care. Committed to providing quality healthcare to our community.", "shortName": "DAMC", "logoUrl": "https://example.com/uploads/logo-damc.jpg", "phones": {"fixedLine": "+213021123456", "fax": "+213021123457", "mobile": "+213555123456"}}, "locations": [{"name": "Main Clinic - Algiers Center", "shortName": "Main", "country": "Algeria", "city": "Algiers", "timezone": "Africa/Algiers", "address": "15 Rue Didouche Mo<PERSON>d, Algiers 16000", "coordinates": {"latitude": 36.7538, "longitude": 3.0588}, "openingHours": [{"dayOfWeek": "Monday", "isActive": true, "hours": [{"timeFrom": "08:00", "timeTo": "12:00"}, {"timeFrom": "14:00", "timeTo": "18:00"}]}, {"dayOfWeek": "Tuesday", "isActive": true, "hours": [{"timeFrom": "08:00", "timeTo": "12:00"}, {"timeFrom": "14:00", "timeTo": "18:00"}]}, {"dayOfWeek": "Wednesday", "isActive": true, "hours": [{"timeFrom": "08:00", "timeTo": "12:00"}, {"timeFrom": "14:00", "timeTo": "18:00"}]}, {"dayOfWeek": "Thursday", "isActive": true, "hours": [{"timeFrom": "08:00", "timeTo": "12:00"}, {"timeFrom": "14:00", "timeTo": "18:00"}]}, {"dayOfWeek": "Friday", "isActive": true, "hours": [{"timeFrom": "08:00", "timeTo": "12:00"}]}, {"dayOfWeek": "Saturday", "isActive": true, "hours": [{"timeFrom": "09:00", "timeTo": "13:00"}]}, {"dayOfWeek": "Sunday", "isActive": false, "hours": []}]}, {"name": "Branch Clinic - <PERSON><PERSON>", "shortName": "Branch", "country": "Algeria", "city": "Algiers", "timezone": "Africa/Algiers", "address": "Cité 1200 Logements, Bab Ezzouar, Algiers", "coordinates": {"latitude": 36.7167, "longitude": 3.1833}, "openingHours": [{"dayOfWeek": "Monday", "isActive": true, "hours": [{"timeFrom": "09:00", "timeTo": "17:00"}]}, {"dayOfWeek": "Tuesday", "isActive": true, "hours": [{"timeFrom": "09:00", "timeTo": "17:00"}]}, {"dayOfWeek": "Wednesday", "isActive": true, "hours": [{"timeFrom": "09:00", "timeTo": "17:00"}]}, {"dayOfWeek": "Thursday", "isActive": true, "hours": [{"timeFrom": "09:00", "timeTo": "17:00"}]}, {"dayOfWeek": "Friday", "isActive": false, "hours": []}, {"dayOfWeek": "Saturday", "isActive": false, "hours": []}, {"dayOfWeek": "Sunday", "isActive": false, "hours": []}]}], "services": [{"title": "General Medical Consultation", "duration": 30, "price": 2500.0, "pointsRequirements": 1, "isPublic": true, "deliveryType": "at_location", "servedRegions": []}, {"title": "Pediatric Consultation", "duration": 45, "price": 3000.0, "pointsRequirements": 1, "isPublic": true, "deliveryType": "at_location", "servedRegions": []}, {"title": "Home Visit - General", "duration": 60, "price": 5000.0, "pointsRequirements": 2, "isPublic": true, "deliveryType": "at_customer", "servedRegions": ["16", "31", "42"]}, {"title": "Emergency Home Visit", "duration": 90, "price": 8000.0, "pointsRequirements": 3, "isPublic": false, "deliveryType": "at_customer", "servedRegions": ["16"]}, {"title": "Telemedicine Consultation", "duration": 20, "price": 1500.0, "pointsRequirements": 1, "isPublic": true, "deliveryType": "both", "servedRegions": []}], "queues": [{"name": "General Medicine Queue", "locationIndex": 0, "serviceIndices": [0, 4], "customOpeningHours": [{"dayOfWeek": "Monday", "isActive": true, "hours": [{"timeFrom": "08:30", "timeTo": "11:30"}, {"timeFrom": "14:30", "timeTo": "17:30"}]}, {"dayOfWeek": "Tuesday", "isActive": true, "hours": [{"timeFrom": "08:30", "timeTo": "11:30"}, {"timeFrom": "14:30", "timeTo": "17:30"}]}, {"dayOfWeek": "Wednesday", "isActive": true, "hours": [{"timeFrom": "08:30", "timeTo": "11:30"}, {"timeFrom": "14:30", "timeTo": "17:30"}]}, {"dayOfWeek": "Thursday", "isActive": true, "hours": [{"timeFrom": "08:30", "timeTo": "11:30"}, {"timeFrom": "14:30", "timeTo": "17:30"}]}, {"dayOfWeek": "Friday", "isActive": true, "hours": [{"timeFrom": "08:30", "timeTo": "11:30"}]}, {"dayOfWeek": "Saturday", "isActive": true, "hours": [{"timeFrom": "09:30", "timeTo": "12:30"}]}, {"dayOfWeek": "Sunday", "isActive": false, "hours": []}]}, {"name": "Pediatrics Queue", "locationIndex": 0, "serviceIndices": [1], "customOpeningHours": [{"dayOfWeek": "Monday", "isActive": true, "hours": [{"timeFrom": "14:00", "timeTo": "17:00"}]}, {"dayOfWeek": "Wednesday", "isActive": true, "hours": [{"timeFrom": "14:00", "timeTo": "17:00"}]}, {"dayOfWeek": "Saturday", "isActive": true, "hours": [{"timeFrom": "09:00", "timeTo": "12:00"}]}]}, {"name": "Branch General <PERSON>", "locationIndex": 1, "serviceIndices": [0, 4], "customOpeningHours": []}]}