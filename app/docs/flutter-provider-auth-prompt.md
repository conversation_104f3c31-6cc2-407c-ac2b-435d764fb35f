# Provider Mobile App Authentication Implementation Prompt

## Overview
Create a Flutter mobile app for service providers that replicates the authentication UI/UX from the existing 'Dalti' customer mobile app, with modifications to support provider-specific registration requirements.

## Base Reference
Copy the complete authentication flow from the 'Dalti' customer mobile app, including:
- UI design and styling
- Screen transitions and navigation
- Form validation patterns
- Error handling approaches
- Loading states and animations

## Required Modifications

### 1. Provider Registration Enhancement
**Modify the signup screen** to include an additional required field:

**New Field: Provider Category Selection**
- Add a dropdown/picker field for selecting provider category
- Position: After the name fields, before email/phone field
- Label: "Service Category" or "What services do you provide?"
- Required: Yes (validation required)
- Data Source: Fetch from API endpoint

### 2. API Integration Updates

Replace the Dalti API endpoints with the new provider authentication endpoints:

#### **Provider Registration Flow:**

**Step 1: Request OTP (Phone)**
```
Endpoint: POST /api/auth/request-otp
Body: {
  "phoneNumber": "[phone]",
  "firstName": "[firstName]",
  "lastName": "[lastName]",
  "isProviderRegistration": true,
  "providerCategoryId": [selectedCategoryId],
  "businessName": "[optional]"
}
```

**Step 1: Request OTP (Email)**
```
Endpoint: POST /api/auth/request-email-otp
Body: {
  "email": "[email]",
  "firstName": "[firstName]",
  "lastName": "[lastName]",
  "password": "[password]",
  "isProviderRegistration": true,
  "providerCategoryId": [selectedCategoryId],
  "businessName": "[optional]",
  "phone": "[optional]"
}
```

**Step 2: Verify OTP & Register Provider**
```
Endpoint: POST /api/auth/provider/verify-otp-register
Body: {
  "otp": "[6-digit-code]",
  "identifier": "[email_or_phone]",
  "password": "[password]",
  "firstName": "[firstName]",
  "lastName": "[lastName]",
  "providerCategoryId": [selectedCategoryId],
  "businessName": "[optional]",
  "phone": "[optional]",
  "email": "[optional]"
}
```

#### **Provider Login:**
```
Endpoint: POST /api/auth/provider/login
Body: {
  "identifier": "[email_or_phone]",
  "password": "[password]"
}
```

#### **Provider Categories:**
```
Endpoint: GET /api/auth/provider/categories?targetLanguage=EN
Response: [
  {"id": 1, "title": "Healthcare", "parentId": null},
  {"id": 16, "title": "Doctor", "parentId": 1},
  {"id": 30, "title": "Mechanic", "parentId": 4}
]
```

### 3. Category Selection Implementation

**UI Requirements:**
- Implement a searchable dropdown or modal picker
- Display categories in hierarchical structure (Parent > Child)
- Show category titles in user's preferred language
- Include search/filter functionality for easy selection
- Validate that a category is selected before proceeding

**Data Handling:**
- Fetch categories on app launch or when registration screen loads
- Cache categories locally for offline access
- Handle parent-child relationships in the UI
- Store selected category ID for registration API call

### 4. Post-Registration Flow

After successful provider registration and login, redirect to a **Provider Onboarding Setup** screen instead of the main home screen.

**Setup Flow Indicator:**
- Check if provider has completed setup using the `isSetupComplete` field from login response
- If `isSetupComplete: false`, redirect to multi-step onboarding
- If `isSetupComplete: true`, redirect to provider dashboard

**Important Role Verification:**
- Verify that the user has `role: "CLIENT"` in the registration response
- This ensures proper role-based access control throughout the app
- Provider users are automatically created with CLIENT role during OTP registration

### 5. Specific Implementation Requirements

#### **Category Dropdown Component:**
```dart
class ProviderCategoryPicker extends StatefulWidget {
  final Function(int categoryId) onCategorySelected;
  final String? selectedCategoryId;
  
  // Implementation should:
  // - Fetch categories from API
  // - Display in searchable format
  // - Handle parent-child relationships
  // - Support multiple languages
}
```

#### **Modified Registration Form:**
```dart
class ProviderSignupScreen extends StatefulWidget {
  // Add these fields to the existing Dalti signup form:
  // - Provider category selection (required)
  // - Business name (optional)
  // - Maintain all existing fields and validation
}
```

#### **API Service Updates:**
```dart
class ProviderAuthService {
  // Multi-step OTP verification workflow for provider registration
  // Maintain the same method signatures and error handling patterns as Dalti

  Future<List<ProviderCategory>> getProviderCategories({String? language});

  // Step 1: Request OTP (Phone or Email)
  Future<OtpResponse> requestPhoneOtp(ProviderOtpRequestData data);
  Future<OtpResponse> requestEmailOtp(ProviderEmailOtpRequestData data);

  // Step 2: Verify OTP and Complete Provider Registration
  Future<ProviderAuthResponse> verifyOtpAndRegisterProvider(ProviderVerificationData data);

  // Provider Login
  Future<ProviderAuthResponse> loginProvider(String identifier, String password);
}

// Data Models
class ProviderOtpRequestData {
  final String phoneNumber;
  final String firstName;
  final String lastName;
  final bool isProviderRegistration = true;
  final int providerCategoryId;
  final String? businessName;
}

class ProviderEmailOtpRequestData {
  final String email;
  final String firstName;
  final String lastName;
  final String password;
  final bool isProviderRegistration = true;
  final int providerCategoryId;
  final String? businessName;
  final String? phone;
}

class ProviderVerificationData {
  final String otp;
  final String identifier;
  final String password;
  final String firstName;
  final String lastName;
  final int providerCategoryId;
  final String? businessName;
  final String? phone;
  final String? email;
}
```

### 6. UI/UX Consistency Requirements

**Maintain from Dalti:**
- Color scheme and branding (adapt for provider app)
- Typography and spacing
- Button styles and interactions
- Form field designs and validation messages
- Loading indicators and animations
- Error handling and user feedback

**Provider-Specific Adaptations:**
- Update app name and branding for providers
- Modify welcome messages and copy for service providers
- Add provider-specific icons and imagery
- Update navigation labels for provider context

### 7. Technical Specifications

**State Management:** Use the same pattern as Dalti (likely Provider/Bloc)
**Navigation:** Replicate the route management system
**Local Storage:** Store provider session and preferences
**Error Handling:** Maintain consistent error messaging patterns
**Validation:** Apply same validation rules plus category selection validation

### 8. Testing Requirements

**Test Scenarios:**
- **Multi-step registration flow**: OTP request → OTP verification → Provider registration
- **Category selection**: Dropdown functionality and search
- **OTP validation**: Invalid OTP, expired OTP, missing OTP scenarios
- **Login flow**: Existing provider authentication
- **API error handling**: Network issues, invalid data, duplicate registration
- **Form validation**: All fields including category selection validation
- **Session management**: Token storage and persistence across app restarts
- **Provider context**: Proper handling of provider-specific data during registration

### 9. Deliverables

1. **Complete authentication flow** matching Dalti's UI/UX
2. **Enhanced registration** with provider category selection
3. **Category management** with hierarchical display and search
4. **API integration** with all provider authentication endpoints
5. **Session handling** with provider-specific data storage
6. **Error handling** consistent with Dalti patterns
7. **Navigation flow** leading to provider onboarding setup

### 10. Success Criteria

- [ ] UI matches Dalti's design language and user experience
- [ ] Provider category selection is intuitive and functional
- [ ] All API endpoints integrate correctly
- [ ] Multi-step OTP verification workflow functions properly
- [ ] Registration flow validates all required fields including category
- [ ] Provider users are created with CLIENT role (verify in response)
- [ ] Login flow authenticates providers successfully
- [ ] Session management works across app restarts
- [ ] Error states provide clear user feedback
- [ ] Navigation flows correctly to onboarding setup
- [ ] Role-based access control works throughout the app

This implementation will provide providers with a familiar, polished authentication experience while collecting the necessary provider-specific information during registration.
