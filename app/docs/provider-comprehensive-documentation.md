# Provider Comprehensive Documentation

## Table of Contents
1. [Provider Overview](#provider-overview)
2. [Provider Authentication & Registration](#provider-authentication--registration)
3. [Provider Profile Management](#provider-profile-management)
4. [Provider Services & Offerings](#provider-services--offerings)
5. [Provider Locations & Availability](#provider-locations--availability)
6. [Appointment Management](#appointment-management)
7. [Queue Management](#queue-management)
8. [Customer Interaction](#customer-interaction)
9. [API Endpoints Documentation](#api-endpoints-documentation)
10. [Database Schema](#database-schema)

## Provider Overview

### What is a Provider?
A provider in this system is a service professional who offers various services to customers through the platform. Providers can be healthcare professionals, beauty specialists, mechanics, or any service-based business.

### Provider Types/Categories
Providers are categorized using the `ProviderCategory` model:
- **Doctor** - Medical professionals
- **Barber** - Hair and grooming services
- **Mechanic** - Automotive services
- **And more** - Extensible category system

### Provider Lifecycle
1. **Registration** - Multi-step OTP verification process
2. **Profile Setup** - Business information and category selection
3. **Onboarding** - Location, services, and queue configuration
4. **Active** - Accepting appointments and managing customers
5. **Verified** - Platform-verified provider status

### Provider Status Management
- `isVerified`: Platform verification status
- `isSetupComplete`: Onboarding completion status
- `isActive`: Operational status

## Provider Authentication & Registration

### Registration Process
The provider registration follows a secure multi-step OTP verification workflow:

#### Step 1: Request OTP
**Endpoints:**
- `POST /api/auth/request-otp` (Phone OTP)
- `POST /api/auth/request-email-otp` (Email OTP)

**Request Body:**
```json
{
  "phoneNumber": "+**********", // For phone OTP
  "email": "<EMAIL>", // For email OTP
  "firstName": "John",
  "lastName": "Doe",
  "isProviderRegistration": true,
  "providerCategoryId": 1,
  "businessName": "John's Services",
  "phone": "+**********"
}
```

#### Step 2: Verify OTP and Complete Registration
**Endpoint:** `POST /api/auth/provider/verify-otp-register`

**Request Body:**
```json
{
  "identifier": "<EMAIL>", // Email or phone
  "otp": "123456",
  "password": "securepassword123",
  "firstName": "John",
  "lastName": "Doe",
  "providerCategoryId": 1,
  "businessName": "John's Services",
  "phone": "+**********"
}
```

**Response:**
```json
{
  "message": "Provider registered successfully",
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "role": "CLIENT"
  },
  "provider": {
    "id": 1,
    "userId": "uuid",
    "providerCategoryId": 1,
    "title": "John's Services",
    "phone": "+**********",
    "isSetupComplete": false
  },
  "sessionId": "session_token"
}
```

### Login Functionality
**Endpoint:** `POST /api/auth/provider/login`

**Request Body:**
```json
{
  "identifier": "<EMAIL>", // Email or phone
  "password": "securepassword123"
}
```

### Password Reset Workflow
1. **Request Reset OTP:** `POST /api/auth/password-reset-request`
2. **Verify OTP:** `POST /api/auth/password-reset-verify`
3. **Reset Password:** `POST /api/auth/password-reset`

### JWT Token Handling
- Session-based authentication with JWT tokens
- Tokens stored securely in mobile apps
- Automatic token refresh capabilities
- Role-based access control (CLIENT role for providers)

## Provider Profile Management

### Profile Creation and Updates
**Get Profile:** `GET /api/auth/providers/profile`
**Update Profile:** `PUT /api/auth/providers/profile`

**Profile Fields:**
```typescript
interface ProviderProfile {
  id: number;
  userId: string;
  title?: string;           // Business title
  phone?: string;           // Contact phone
  presentation?: string;    // Business description
  isVerified: boolean;      // Platform verification
  isSetupComplete: boolean; // Onboarding status
  category?: {
    id: number;
    title: string;
  };
  averageRating?: number;   // Customer ratings
  totalReviews: number;     // Review count
}
```

### Business Information Management
- Business name and description
- Contact information
- Category selection
- Verification status
- Setup completion tracking

### File Uploads (Profile Pictures, Documents)
**Logo Upload:** `POST /api/auth/providers/mobile/logo`

**Upload Process:**
1. Request pre-signed URL
2. Upload file to cloud storage
3. Store file metadata in database
4. Associate with provider profile

**Supported Formats:**
- Images: JPEG, PNG
- Maximum file size: 5MB
- Automatic image optimization

## Provider Services & Offerings

### Service Creation and Management
**Endpoints:**
- `GET /api/auth/providers/services` - List services
- `POST /api/auth/providers/services` - Create service
- `PUT /api/auth/providers/services/:id` - Update service
- `DELETE /api/auth/providers/services/:id` - Delete service

### Service Structure
```typescript
interface Service {
  id: number;
  title: string;
  duration: number;         // Minutes
  price: number;           // Service cost
  pointsRequirements: number; // Credits required
  isPublic: boolean;       // Visibility
  deliveryType: string;    // "at_location", "at_customer", "both"
  servedRegions: string[]; // Geographic coverage
  description?: string;    // Service details
  color: string;          // UI color code
  acceptOnline: boolean;   // Online booking
  acceptNew: boolean;      // New customer acceptance
  notificationOn: boolean; // Notification preferences
}
```

### Service Categories
**Endpoints:**
- `GET /api/auth/providers/service-categories`
- `POST /api/auth/providers/service-categories`
- `PUT /api/auth/providers/service-categories/:id`
- `DELETE /api/auth/providers/service-categories/:id`

### Pricing and Duration Settings
- Flexible pricing per service
- Duration ranges (min/max)
- Credit requirements
- Delivery type options

### Credit Requirements per Service
- Minimum credits required for booking
- Prevents no-shows and cancellations
- Configurable per service type
- Automatic credit deduction

## Provider Locations & Availability

### Multiple Location Management
**Endpoints:**
- `GET /api/auth/providers/locations` - List locations
- `POST /api/auth/providers/locations` - Create location
- `PUT /api/auth/providers/locations/:id` - Update location
- `DELETE /api/auth/providers/locations/:id` - Delete location

### Location Structure
```typescript
interface Location {
  id: number;
  name: string;
  shortName?: string;
  address?: string;
  city?: string;
  mobile?: string;
  isMobileHidden?: boolean;
  fax?: string;
  floor?: string;
  parking: boolean;
  elevator: boolean;
  handicapAccess: boolean;
  timezone?: string;
  // Address details
  country?: string;
  postalCode?: string;
  latitude?: number;
  longitude?: number;
  // Opening hours
  openingHours?: OpeningHoursDay[];
}
```

### Operating Hours Configuration
**Endpoints:**
- `GET /api/auth/providers/schedules` - Get schedules
- `POST /api/auth/providers/schedules` - Create schedule
- `PUT /api/auth/providers/schedules/:id` - Update schedule

**Opening Hours Structure:**
```typescript
interface OpeningHoursDay {
  dayOfWeek: string;        // "Monday", "Tuesday", etc.
  isActive: boolean;        // Day availability
  timeSlots: TimeSlot[];    // Multiple time ranges
}

interface TimeSlot {
  timeFrom: string;         // "09:00"
  timeTo: string;          // "17:00"
}
```

### Calendar and Availability Management
- Real-time availability calculation
- Appointment slot management
- Conflict detection
- Automatic scheduling optimization

### Time Slot Management
- Configurable slot durations
- Buffer time between appointments
- Break time management
- Overtime handling

## Appointment Management

### Appointment Creation, Updates, Cancellation
**Endpoints:**
- `GET /api/auth/providers/appointments` - List appointments
- `POST /api/auth/providers/appointments` - Create appointment
- `PUT /api/auth/providers/appointments/:id` - Update appointment
- `DELETE /api/auth/providers/appointments/:id` - Cancel appointment

### Appointment Structure
```typescript
interface Appointment {
  id: number;
  status: string;                    // "pending", "confirmed", "completed", etc.
  expectedAppointmentStartTime: Date;
  expectedAppointmentEndTime: Date;
  realAppointmentStartTime?: Date;   // Actual start
  realAppointmentEndTime?: Date;     // Actual end
  serviceDuration: number;
  notes?: string;
  realTimeStatus: string;            // "ontime", "delayed", "early"
  slots: number;                     // Resource slots used
  isOverflowed?: boolean;            // Beyond normal hours
  overflowReason?: string;           // Reason for overflow
}
```

### Appointment History and Audit Trail
Every appointment change creates an `AppointmentHistory` record:
```typescript
interface AppointmentHistory {
  id: number;
  appointmentId: number;
  changedByUserId: string;
  previousStartTime: Date;
  previousEndTime: Date;
  newStartTime: Date;
  newEndTime: Date;
  previousStatus: string;
  newStatus: string;
  changeReason: string;
  createdAt: Date;
}
```

### Customer Appointment Handling
- Customer booking requests
- Appointment confirmations
- Status updates and notifications
- Customer communication

### No-show and Cancellation Policies
- Automatic no-show detection
- Credit deduction for no-shows
- Cancellation time limits
- Penalty management

## Queue Management

### Queue Creation and Configuration
**Endpoints:**
- `GET /api/auth/providers/queues` - List queues
- `POST /api/auth/providers/queues` - Create queue
- `PUT /api/auth/providers/queues/:id` - Update queue
- `DELETE /api/auth/providers/queues/:id` - Delete queue

### Queue Structure
```typescript
interface Queue {
  id: number;
  title: string;                    // Queue name (e.g., "Chair 1", "Room A")
  sProvidingPlaceId: number;        // Location reference
  isActive: boolean;                // Operational status
  services: Service[];              // Available services
  openings: QueueOpening[];         // Operating schedule
}
```

### Queue Status Management
- Active/inactive status
- Service assignments
- Capacity management
- Real-time status updates

### Customer Queue Handling
- Queue position tracking
- Wait time estimation
- Queue notifications
- Customer queue management

### Queue Swap Functionality
**Endpoints:**
- `GET /api/auth/providers/queue-swaps` - List swap requests
- `POST /api/auth/providers/queue-swaps` - Create swap request
- `PUT /api/auth/providers/queue-swaps/:id` - Handle swap request

**Queue Swap Process:**
1. Customer requests position swap
2. Other customer approval required
3. Provider can approve/reject
4. Automatic expiration handling

## Customer Interaction

### Customer Management Features
**Endpoints:**
- `GET /api/auth/providers/customers` - List customers
- `GET /api/auth/providers/customers/:id` - Get customer details
- `PUT /api/auth/providers/customers/:id` - Update customer info

### Customer Folder System
Each provider-customer relationship creates a `CustomerFolder`:
```typescript
interface CustomerFolder {
  id: number;
  sProviderId: number;
  userId: string;               // Customer ID
  notes?: string;               // Provider notes
  isActive: boolean;            // Relationship status
  appointments: Appointment[];   // Appointment history
}
```

### Communication Capabilities
- In-app messaging system
- Appointment notifications
- Status update alerts
- Customer support features

### Rating and Review System
**Review Structure:**
```typescript
interface Review {
  id: number;
  rating: number;               // 1-5 stars
  comment?: string;             // Customer feedback
  status: string;               // "approved", "pending", "rejected"
  customerId: string;           // Reviewer
  providerId: number;           // Provider being reviewed
  appointmentId?: number;       // Related appointment
  response?: ReviewResponse;    // Provider response
}
```

### Customer History Tracking
- Complete appointment history
- Service preferences
- Payment history
- Communication logs
- Behavior patterns

## API Endpoints Documentation

### Base URLs
- **Development:** `https://dapi-test.adscloud.org:8443`
- **Production:** `https://dapi.adscloud.org`

### Authentication Requirements
All provider endpoints require:
- `Authorization: Bearer <jwt_token>`
- `Content-Type: application/json`
- Valid provider session

### Error Handling and Status Codes
**Standard Response Format:**
```json
{
  "success": boolean,
  "data": any,
  "message": string,
  "errors": any[]
}
```

**HTTP Status Codes:**
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `500` - Internal Server Error

### Complete API Endpoint List

#### Authentication (6 endpoints)
- `POST /api/auth/request-email-otp` - Request email OTP
- `POST /api/auth/request-otp` - Request phone OTP
- `POST /api/auth/provider/verify-otp-register` - Complete registration
- `POST /api/auth/provider/login` - Provider login
- `POST /api/auth/password-reset-request` - Request password reset
- `POST /api/auth/refresh-token` - Refresh JWT token

#### Profile Management (3 endpoints)
- `GET /api/auth/providers/profile` - Get profile
- `PUT /api/auth/providers/profile` - Update profile
- `GET /api/auth/provider/profile-completion` - Get completion status

#### Location Management (4 endpoints)
- `GET /api/auth/providers/locations` - List locations
- `POST /api/auth/providers/locations` - Create location
- `PUT /api/auth/providers/locations/:id` - Update location
- `DELETE /api/auth/providers/locations/:id` - Delete location

#### Service Management (8 endpoints)
- `GET /api/auth/providers/service-categories` - List categories
- `POST /api/auth/providers/service-categories` - Create category
- `PUT /api/auth/providers/service-categories/:id` - Update category
- `DELETE /api/auth/providers/service-categories/:id` - Delete category
- `GET /api/auth/providers/services` - List services
- `POST /api/auth/providers/services` - Create service
- `PUT /api/auth/providers/services/:id` - Update service
- `DELETE /api/auth/providers/services/:id` - Delete service

#### Schedule Management (3 endpoints)
- `GET /api/auth/providers/schedules` - Get schedules
- `POST /api/auth/providers/schedules` - Create schedule
- `PUT /api/auth/providers/schedules/:id` - Update schedule

#### Queue Management (6 endpoints)
- `GET /api/auth/providers/queues` - List queues
- `POST /api/auth/providers/queues` - Create queue
- `PUT /api/auth/providers/queues/:id` - Update queue
- `DELETE /api/auth/providers/queues/:id` - Delete queue
- `GET /api/auth/providers/queue-services` - List queue services
- `POST /api/auth/providers/queue-services` - Assign service to queue

#### Appointment Management (4 endpoints)
- `GET /api/auth/providers/appointments` - List appointments
- `POST /api/auth/providers/appointments` - Create appointment
- `PUT /api/auth/providers/appointments/:id` - Update appointment
- `PUT /api/auth/providers/time/appointments/extend` - Extend appointment

#### Customer Management (3 endpoints)
- `GET /api/auth/providers/customers` - List customers
- `GET /api/auth/providers/customers/:id` - Get customer details
- `PUT /api/auth/providers/customers/:id` - Update customer

#### File Upload (2 endpoints)
- `POST /api/auth/providers/mobile/logo` - Upload provider logo
- `DELETE /api/auth/providers/mobile/logo` - Remove provider logo

#### Reschedule Management (3 endpoints)
- `GET /api/auth/providers/reschedules` - List reschedule requests
- `PUT /api/auth/providers/reschedules/:id` - Handle reschedule request
- `POST /api/auth/providers/reschedules/:id/respond` - Respond to request

## Database Schema

### Core Provider Models

#### SProvider
```sql
model SProvider {
  id                  Int      @id @default(autoincrement())
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt
  
  category           ProviderCategory? @relation(fields: [providerCategoryId], references: [id])
  providerCategoryId Int?
  
  title              String?
  phone              String?
  presentation       String? @db.Text
  isVerified         Boolean @default(false)
  isSetupComplete    Boolean @default(false)
  
  logoId             String?
  logo               File?   @relation("ProviderLogo", fields: [logoId], references: [id])
  
  user               User    @relation(fields: [userId], references: [id])
  userId             String  @unique
  
  providingPlaces    SProvidingPlace[]
  services           Service[]
  serviceCategories  ServiceCategory[]
  customerFolders    CustomerFolder[]
  queues             Queue[]
  
  averageRating      Float?
  totalReviews       Int     @default(0)
  reviewsReceived    Review[] @relation("ProviderReviews")
}
```

#### ProviderCategory
```sql
model ProviderCategory {
  id          Int      @id @default(autoincrement())
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  title       String   @unique
  description String?  @db.Text
  isActive    Boolean  @default(true)
  sortOrder   Int      @default(0)
  metadata    Json?    // Icon, color, keywords
  
  parentId    Int?
  parent      ProviderCategory?  @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children    ProviderCategory[] @relation("CategoryHierarchy")
  
  imageId     String?
  image       File?    @relation("CategoryImage", fields: [imageId], references: [id])
  
  providers   SProvider[]
}
```

#### SProvidingPlace (Locations)
```sql
model SProvidingPlace {
  id             Int      @id @default(autoincrement())
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  
  provider       SProvider @relation(fields: [sProviderId], references: [id])
  sProviderId    Int
  
  name           String
  shortName      String?
  address        String?
  city           String?
  mobile         String?
  isMobileHidden Boolean @default(false)
  fax            String?
  floor          String?
  parking        Boolean @default(false)
  elevator       Boolean @default(false)
  handicapAccess Boolean @default(false)
  timezone       String?
  
  appointments   Appointment[]
  openings       Opening[]
  queues         Queue[]
  
  detailedAddress   Address? @relation(fields: [detailedAddressId], references: [id])
  detailedAddressId Int?     @unique
}
```

### Relationships Between Entities

#### Provider → User (1:1)
- Each provider has exactly one user account
- User role is "CLIENT" for providers
- Unique constraint on userId

#### Provider → Category (N:1)
- Each provider belongs to one category
- Categories can have multiple providers
- Optional relationship (can be null)

#### Provider → Locations (1:N)
- Provider can have multiple locations
- Each location belongs to one provider
- Cascade delete when provider is removed

#### Provider → Services (1:N)
- Provider can offer multiple services
- Each service belongs to one provider
- Services can be categorized

#### Provider → Queues (1:N)
- Provider can have multiple queues
- Queues are associated with locations
- Queue-specific schedules and services

#### Provider → Customers (N:N via CustomerFolder)
- Many-to-many relationship through CustomerFolder
- Tracks provider-customer interactions
- Stores relationship-specific data

### Key Fields and Constraints

#### Unique Constraints
- `SProvider.userId` - One provider per user
- `SProvider.logoId` - One logo per provider
- `ProviderCategory.title` - Unique category names
- `Queue.title + sProvidingPlaceId` - Unique queue names per location

#### Indexes
- `ProviderCategory.isActive` - Active category filtering
- `ProviderCategory.sortOrder` - Category ordering
- `Address.latitude + longitude` - Geolocation queries
- `Appointment.status` - Status-based filtering
- `Review.providerId` - Provider review lookup

#### Default Values
- `SProvider.isVerified = false` - Requires manual verification
- `SProvider.isSetupComplete = false` - Tracks onboarding
- `Service.acceptOnline = true` - Online booking enabled
- `Service.pointsRequirements = 1` - Minimum credit requirement
- `Queue.isActive = true` - Queues active by default

---

*This documentation covers the complete provider functionality in the Wasp.js appointment management system. For specific implementation details or API examples, refer to the individual endpoint documentation files.*
