# Provider First-Time Onboarding Setup API

## Overview
After successful provider registration, providers need to complete a multi-step onboarding setup to activate their account and start receiving bookings.

## API Endpoint
```
POST /api/auth/provider/complete-setup
```

## Purpose
Collects essential business information required for providers to:
- Accept bookings from customers
- Display their services and availability
- Set pricing and service details
- Configure their business profile

## Required Setup Data

### Business Information (`businessInfo`)
- **Business Name**: Official business name
- **Bio**: Detailed description of services offered (minimum 1 character)
- **Short Name**: Abbreviated business name (optional)
- **Logo URL**: URL to business logo image (optional)
- **Phone Numbers**: At least one phone number required
  - Mobile phone (recommended)
  - Fixed line phone (optional)
  - Fax number (optional)

### Locations (`locations`)
- **Location Details**: Physical business locations
- **Address Information**: Complete address with city, postal code, country
- **Timezone**: Location timezone (e.g., "Africa/Algiers")
- **Accessibility Features**: Parking, elevator, handicap access
- **Contact Information**: Location-specific phone/fax numbers
- **Coordinates**: Latitude/longitude for mapping (optional)
- **Opening Hours**: Daily schedule with time ranges

### Services (`services`)
- **Service Title**: Name of the service offered
- **Duration**: Service duration in minutes
- **Points Requirements**: Booking points/credits required
- **Delivery Type**: Where service is provided ("at_location", "at_customer", "remote")
- **Availability Settings**: Online booking, new customer acceptance
- **Public Visibility**: Whether service appears in public listings
- **Service Category**: Optional categorization
- **Color Coding**: Optional color for calendar display

### Queues (`queues`)
- **Queue Management**: Appointment scheduling queues
- **Location Association**: Which location the queue belongs to
- **Service Association**: Which services use this queue
- **Queue Hours**: Operating hours for the queue (can differ from location hours)

## Request Format
```json
{
  "businessInfo": {
    "businessName": "Professional Medical Practice",
    "bio": "Professional medical practice specializing in comprehensive healthcare services",
    "shortName": "MedPractice",
    "logoUrl": "https://example.com/logo.png",
    "phones": {
      "mobile": "+213555123456",
      "fixedLine": "+***********",
      "fax": "+***********"
    }
  },
  "locations": [
    {
      "name": "Main Clinic",
      "shortName": "Main",
      "address": "123 Medical Center Dr",
      "city": "Algiers",
      "postalCode": "16000",
      "country": "Algeria",
      "timezone": "Africa/Algiers",
      "mobile": "+213555123456",
      "fax": "+***********",
      "floor": "2nd Floor",
      "parking": true,
      "elevator": true,
      "handicapAccess": true,
      "latitude": 36.7538,
      "longitude": 3.0588,
      "openingHours": [
        {
          "dayOfWeek": "Monday",
          "isActive": true,
          "hours": [
            {"timeFrom": "09:00", "timeTo": "12:00"},
            {"timeFrom": "14:00", "timeTo": "17:00"}
          ]
        },
        {
          "dayOfWeek": "Tuesday",
          "isActive": true,
          "hours": [{"timeFrom": "09:00", "timeTo": "17:00"}]
        },
        {
          "dayOfWeek": "Wednesday",
          "isActive": true,
          "hours": [{"timeFrom": "09:00", "timeTo": "17:00"}]
        },
        {
          "dayOfWeek": "Thursday",
          "isActive": true,
          "hours": [{"timeFrom": "09:00", "timeTo": "17:00"}]
        },
        {
          "dayOfWeek": "Friday",
          "isActive": true,
          "hours": [{"timeFrom": "09:00", "timeTo": "17:00"}]
        },
        {
          "dayOfWeek": "Saturday",
          "isActive": false,
          "hours": []
        },
        {
          "dayOfWeek": "Sunday",
          "isActive": false,
          "hours": []
        }
      ]
    }
  ],
  "services": [
    {
      "title": "General Consultation",
      "color": "#4CAF50",
      "serviceCategoryId": 1,
      "duration": 30,
      "minDuration": 20,
      "maxDuration": 45,
      "pointsRequirements": 1,
      "acceptOnline": true,
      "acceptNew": true,
      "notificationOn": true,
      "isPublic": true,
      "deliveryType": "at_location"
    },
    {
      "title": "Follow-up Consultation",
      "color": "#2196F3",
      "serviceCategoryId": 1,
      "duration": 20,
      "pointsRequirements": 1,
      "acceptOnline": true,
      "acceptNew": true,
      "notificationOn": true,
      "isPublic": true,
      "deliveryType": "at_location"
    }
  ],
  "queues": [
    {
      "name": "Main Queue",
      "locationIndex": 0,
      "serviceIndices": [0, 1],
      "openingHours": [
        {
          "dayOfWeek": "Monday",
          "isActive": true,
          "hours": [{"timeFrom": "09:00", "timeTo": "17:00"}]
        },
        {
          "dayOfWeek": "Tuesday",
          "isActive": true,
          "hours": [{"timeFrom": "09:00", "timeTo": "17:00"}]
        },
        {
          "dayOfWeek": "Wednesday",
          "isActive": true,
          "hours": [{"timeFrom": "09:00", "timeTo": "17:00"}]
        },
        {
          "dayOfWeek": "Thursday",
          "isActive": true,
          "hours": [{"timeFrom": "09:00", "timeTo": "17:00"}]
        },
        {
          "dayOfWeek": "Friday",
          "isActive": true,
          "hours": [{"timeFrom": "09:00", "timeTo": "17:00"}]
        },
        {
          "dayOfWeek": "Saturday",
          "isActive": false,
          "hours": []
        },
        {
          "dayOfWeek": "Sunday",
          "isActive": false,
          "hours": []
        }
      ]
    }
  ]
}
```

## Response Format

### Success Response (200)
```json
{
  "message": "Provider setup completed successfully",
  "provider": {
    "id": 45,
    "userId": "user123",
    "isSetupComplete": true,
    "status": "active",
    "businessDescription": "Professional medical practice specializing in...",
    "businessAddress": {
      "street": "123 Medical Center Dr",
      "city": "Algiers",
      "state": "Algiers",
      "postalCode": "16000",
      "country": "Algeria"
    },
    "serviceAreas": ["Algiers", "Boumerdes"],
    "yearsExperience": 10
  },
  "services": [
    {
      "id": 1,
      "name": "General Consultation",
      "price": 5000,
      "duration": 30,
      "currency": "DZD"
    }
  ],
  "redirectTo": "provider_dashboard"
}
```

### Error Response (400)
```json
{
  "message": "Invalid setup data",
  "errors": {
    "businessDescription": ["Business description is required"],
    "businessAddress": ["Valid business address is required"],
    "services": ["At least one service must be provided"]
  }
}
```

## Key Features
- **Validates all required fields** before marking setup as complete
- **Updates provider status** from setup incomplete to active
- **Enables booking functionality** for the provider
- **Returns dashboard redirect** for immediate provider access
- **Creates service listings** associated with the provider
- **Stores business configuration** for customer discovery

## Authentication
- **Requires valid session token** from registration/login
- **Provider must be authenticated** and have `isSetupComplete: false`
- **One-time setup process** - cannot be repeated once completed
- **Role verification** - user must have CLIENT role

## Validation Rules

### Required Fields
- `businessDescription` (minimum 50 characters)
- `businessAddress` (complete address object)
- `serviceAreas` (at least one area)
- `businessHours` (at least one day with hours)
- `services` (at least one service)
- `yearsExperience` (positive number)
- `bookingPreferences` (complete preferences object)

### Business Logic Validation
- **Business hours**: Valid time format (HH:MM), open time before close time
- **Service pricing**: Positive numbers, valid currency codes
- **Service duration**: Between 15 minutes and 8 hours
- **Experience**: Between 0 and 50 years
- **Service areas**: Valid geographic locations
- **Advance booking**: Between 1 and 365 days

## Error Handling
- **Validation errors** for missing or invalid required fields
- **Business logic validation** (e.g., valid business hours, reasonable pricing)
- **Geographic validation** for service areas and addresses
- **Professional validation** for certifications and experience claims
- **Duplicate setup prevention** - returns error if already completed

## Mobile App Integration

### Flutter Implementation Example
```dart
class ProviderSetupService {
  Future<SetupResponse> completeProviderSetup({
    required String businessDescription,
    required BusinessAddress businessAddress,
    required List<String> serviceAreas,
    required Map<String, BusinessHours> businessHours,
    required List<ServiceData> services,
    required int yearsExperience,
    required BookingPreferences bookingPreferences,
    List<String>? certifications,
    List<String>? specializations,
  }) async {
    final response = await http.post(
      Uri.parse('$baseUrl/api/auth/provider/complete-setup'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $sessionToken',
      },
      body: jsonEncode({
        'businessDescription': businessDescription,
        'businessAddress': businessAddress.toJson(),
        'serviceAreas': serviceAreas,
        'businessHours': businessHours.map((k, v) => MapEntry(k, v.toJson())),
        'services': services.map((s) => s.toJson()).toList(),
        'yearsExperience': yearsExperience,
        'bookingPreferences': bookingPreferences.toJson(),
        if (certifications != null) 'certifications': certifications,
        if (specializations != null) 'specializations': specializations,
      }),
    );
    
    if (response.statusCode == 200) {
      return SetupResponse.fromJson(jsonDecode(response.body));
    } else {
      throw Exception(jsonDecode(response.body)['message']);
    }
  }
}
```

## Post-Setup Flow
After successful setup completion:

1. **Provider Status**: `isSetupComplete` becomes `true`
2. **Account Activation**: Provider account becomes active for bookings
3. **Dashboard Access**: Provider can access full dashboard functionality
4. **Customer Discovery**: Provider appears in customer search results
5. **Booking Availability**: Customers can book appointments with the provider

## Next Steps After Setup
- **Dashboard Navigation**: Redirect to provider dashboard
- **Profile Completion**: Optional additional profile enhancements
- **Service Management**: Add/edit services and pricing
- **Calendar Setup**: Configure availability and time slots
- **Notification Preferences**: Set up booking and payment notifications

This API transforms a registered provider into an active, bookable service provider ready to receive customer appointments and manage their business through the platform.

## ⚠️ CRITICAL: Field Name Requirements

**The API expects specific field names. Using incorrect field names will cause validation errors:**

### Time Fields (REQUIRED)
- ❌ `"start"` and `"end"`
- ✅ `"timeFrom"` and `"timeTo"`

### Phone Fields
- ❌ `"landline"`
- ✅ `"fixedLine"`

### Queue Opening Hours
- ❌ `"openingHours"` in queues
- ✅ `"customOpeningHours"` in queues

## Corrected Payload Example

**Your exact data with correct field names:**

```json
{
  "businessInfo": {
    "businessName": "Dalti Clinic",
    "bio": "this is a dalti clinic description for the display",
    "shortName": null,
    "logoPath": null,
    "phones": {
      "mobile": "**********",
      "fixedLine": "**********"
    }
  },
  "locations": [
    {
      "name": "Dalti Clinic Main",
      "address": "Rue 8 mars 55",
      "city": "Biskra",
      "country": "Algeria",
      "timezone": "Africa/Algiers",
      "parking": true,
      "elevator": true,
      "handicapAccess": true,
      "openingHours": [
        {
          "dayOfWeek": "Monday",
          "isActive": true,
          "hours": [
            {
              "timeFrom": "09:00",
              "timeTo": "17:00"
            }
          ]
        },
        {
          "dayOfWeek": "Tuesday",
          "isActive": true,
          "hours": [
            {
              "timeFrom": "09:00",
              "timeTo": "17:00"
            }
          ]
        },
        {
          "dayOfWeek": "Wednesday",
          "isActive": true,
          "hours": [
            {
              "timeFrom": "09:00",
              "timeTo": "17:00"
            }
          ]
        },
        {
          "dayOfWeek": "Thursday",
          "isActive": true,
          "hours": [
            {
              "timeFrom": "09:00",
              "timeTo": "17:00"
            }
          ]
        },
        {
          "dayOfWeek": "Friday",
          "isActive": false,
          "hours": []
        },
        {
          "dayOfWeek": "Saturday",
          "isActive": true,
          "hours": [
            {
              "timeFrom": "09:00",
              "timeTo": "17:00"
            }
          ]
        },
        {
          "dayOfWeek": "Sunday",
          "isActive": true,
          "hours": [
            {
              "timeFrom": "09:00",
              "timeTo": "17:00"
            }
          ]
        }
      ]
    }
  ],
  "services": [
    {
      "title": "test servuce",
      "duration": 5,
      "pointsRequirements": 1,
      "isPublic": true,
      "deliveryType": "at_location",
      "acceptOnline": true,
      "acceptNew": true,
      "notificationOn": true
    }
  ],
  "queues": [
    {
      "name": "Dada",
      "locationIndex": 0,
      "serviceIndices": [0],
      "customOpeningHours": [
        {
          "dayOfWeek": "Monday",
          "isActive": true,
          "hours": [
            {
              "timeFrom": "09:00",
              "timeTo": "17:00"
            }
          ]
        },
        {
          "dayOfWeek": "Tuesday",
          "isActive": true,
          "hours": [
            {
              "timeFrom": "09:00",
              "timeTo": "17:00"
            }
          ]
        },
        {
          "dayOfWeek": "Wednesday",
          "isActive": true,
          "hours": [
            {
              "timeFrom": "09:00",
              "timeTo": "17:00"
            }
          ]
        },
        {
          "dayOfWeek": "Thursday",
          "isActive": true,
          "hours": [
            {
              "timeFrom": "09:00",
              "timeTo": "17:00"
            }
          ]
        },
        {
          "dayOfWeek": "Friday",
          "isActive": false,
          "hours": []
        },
        {
          "dayOfWeek": "Saturday",
          "isActive": true,
          "hours": [
            {
              "timeFrom": "09:00",
              "timeTo": "17:00"
            }
          ]
        },
        {
          "dayOfWeek": "Sunday",
          "isActive": true,
          "hours": [
            {
              "timeFrom": "09:00",
              "timeTo": "17:00"
            }
          ]
        }
      ]
    }
  ]
}
```
