# Admin API Documentation

## Overview

The Admin API provides comprehensive management capabilities for the platform, allowing administrators to:
- Manage their own admin accounts
- Oversee provider approval and verification
- View and manage customer data
- Handle provider categories and hierarchies

## Authentication

### Admin Access Requirements

- Admin users cannot self-register
- First admin must be created manually in the database
- Subsequent admins can be created by existing admin users
- All admin operations require `role: 'ADMIN'` and valid session

### Admin Login

**Endpoint:** `POST /api/auth/admin/login`

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "adminpassword123"
}
```

**Response:**
```json
{
  "sessionId": "session_123abc",
  "user": {
    "id": "user_456def",
    "email": "<EMAIL>",
    "firstName": "Admin",
    "lastName": "User",
    "role": "ADMIN",
    "isAdmin": true
  }
}
```

**Error Responses:**
- `401`: Invalid credentials
- `403`: Admin access required
- `500`: Internal server error

---

## Provider Management

### Get Providers List

**Endpoint:** `GET /api/admin/providers`

**Query Parameters:**
- `page` (optional, default: 1): Page number for pagination
- `limit` (optional, default: 20, max: 100): Number of providers per page
- `isVerified` (optional): Filter by verification status (true/false)
- `search` (optional): Search in provider title, user name, or email

**Example Request:**
```
GET /api/admin/providers?page=1&limit=10&isVerified=false&search=dentist
```

**Response:**
```json
{
  "providers": [
    {
      "id": 1,
      "title": "Dr. Smith Dental Clinic",
      "isVerified": false,
      "isSetupComplete": true,
      "createdAt": "2024-01-01T10:00:00.000Z",
      "updatedAt": "2024-01-01T10:00:00.000Z",
      "user": {
        "id": "user_123",
        "firstName": "John",
        "lastName": "Smith",
        "email": "<EMAIL>",
        "mobileNumber": "+**********",
        "createdAt": "2024-01-01T10:00:00.000Z",
        "isPhoneVerified": true,
        "isEmailVerified": true
      },
      "category": {
        "id": 1,
        "title": "Healthcare"
      },
      "_count": {
        "services": 3,
        "customerFolders": 25,
        "reviewsReceived": 12
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "totalCount": 45,
    "totalPages": 5
  }
}
```

### Update Provider Status

**Endpoint:** `PUT /api/admin/providers/{providerId}/status`

**Request:**
```json
{
  "isVerified": true,
  "reason": "All documents verified successfully"
}
```

**Response:**
```json
{
  "provider": {
    "id": 1,
    "title": "Dr. Smith Dental Clinic",
    "isVerified": true,
    "updatedAt": "2024-01-01T11:00:00.000Z",
    "user": {
      "id": "user_123",
      "firstName": "John",
      "lastName": "Smith",
      "email": "<EMAIL>",
      "mobileNumber": "+**********"
    },
    "category": {
      "id": 1,
      "title": "Healthcare"
    }
  },
  "message": "Provider approved successfully"
}
```

**Error Responses:**
- `403`: Admin access required
- `404`: Provider not found
- `400`: Invalid request data
- `500`: Internal server error

---

## Customer Management

### Get Customers List

**Endpoint:** `GET /api/admin/customers`

**Query Parameters:**
- `page` (optional, default: 1): Page number for pagination
- `limit` (optional, default: 20, max: 100): Number of customers per page
- `search` (optional): Search in customer name, email, or mobile number

**Example Request:**
```
GET /api/admin/customers?page=1&limit=10&search=john
```

**Response:**
```json
{
  "customers": [
    {
      "id": "customer_123",
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>",
      "mobileNumber": "+**********",
      "createdAt": "2024-01-01T10:00:00.000Z",
      "isPhoneVerified": true,
      "isEmailVerified": true,
      "role": "CUSTOMER",
      "_count": {
        "customerFolders": 3
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "totalCount": 150,
    "totalPages": 15
  }
}
```

---

## Admin User Management

### Create Admin User

**Endpoint:** `POST /api/admin/users`

**Request:**
```json
{
  "email": "<EMAIL>",
  "firstName": "New",
  "lastName": "Admin",
  "password": "securepassword123",
  "role": "ADMIN"
}
```

**Response:**
```json
{
  "user": {
    "id": "user_789",
    "email": "<EMAIL>",
    "firstName": "New",
    "lastName": "Admin",
    "role": "ADMIN",
    "isAdmin": true,
    "createdAt": "2024-01-01T12:00:00.000Z"
  },
  "message": "Admin user created successfully"
}
```

**Error Responses:**
- `403`: Admin access required
- `400`: User with this email already exists
- `400`: Invalid request data
- `500`: Internal server error

---

## Provider Category Management

### Get Provider Categories

**Endpoint:** `GET /api/admin/provider-categories`

**Query Parameters:**
- `includeHierarchy` (optional, default: true): Include parent/child relationships

**Response:**
```json
{
  "categories": [
    {
      "id": 1,
      "title": "Healthcare",
      "parentId": null,
      "parent": null,
      "children": [
        {
          "id": 2,
          "title": "Dentistry",
          "parentId": 1
        },
        {
          "id": 3,
          "title": "General Practice",
          "parentId": 1
        }
      ],
      "_count": {
        "providers": 25,
        "children": 2
      }
    }
  ]
}
```

### Create Provider Category

**Endpoint:** `POST /api/admin/provider-categories`

**Request:**
```json
{
  "title": "Beauty & Wellness",
  "parentId": null
}
```

**Response:**
```json
{
  "category": {
    "id": 4,
    "title": "Beauty & Wellness",
    "parentId": null,
    "parent": null,
    "children": [],
    "_count": {
      "providers": 0,
      "children": 0
    }
  },
  "message": "Provider category created successfully"
}
```

### Update Provider Category

**Endpoint:** `PUT /api/admin/provider-categories/{categoryId}`

**Request:**
```json
{
  "title": "Health & Wellness",
  "parentId": null
}
```

**Response:**
```json
{
  "category": {
    "id": 4,
    "title": "Health & Wellness",
    "parentId": null,
    "parent": null,
    "children": [],
    "_count": {
      "providers": 0,
      "children": 0
    }
  },
  "message": "Provider category updated successfully"
}
```

### Delete Provider Category

**Endpoint:** `DELETE /api/admin/provider-categories/{categoryId}`

**Response:**
```json
{
  "message": "Provider category deleted successfully"
}
```

**Error Responses:**
- `400`: Cannot delete category with child categories
- `400`: Cannot delete category with assigned providers
- `404`: Category not found
- `403`: Admin access required

---

## Authentication Flow

### Using Session-Based Authentication

1. **Admin Login**: Call `/api/admin/login` with credentials
2. **Store Session**: Save the returned `sessionId`
3. **Include in Headers**: Add session ID to subsequent requests

**Example with JavaScript:**

```javascript
// Login
const loginResponse = await fetch('/api/admin/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'adminpassword123'
  })
});

const loginData = await loginResponse.json();
const sessionId = loginData.sessionId;

// Use session for subsequent requests
const providersResponse = await fetch('/api/admin/providers', {
  headers: {
    'Authorization': `Bearer ${sessionId}`,
    'Content-Type': 'application/json'
  }
});
```

---

## Error Handling

### Common Error Codes

- `400`: Bad Request - Invalid input data
- `401`: Unauthorized - Invalid or missing authentication
- `403`: Forbidden - Admin access required
- `404`: Not Found - Resource doesn't exist
- `500`: Internal Server Error - Server-side error

### Error Response Format

```json
{
  "error": "Error message describing what went wrong"
}
```

---

## Best Practices

### Security
- Always validate admin access before performing operations
- Use HTTPS in production
- Implement rate limiting for login attempts
- Log all admin actions for audit purposes

### Pagination
- Use reasonable page sizes (default: 20, max: 100)
- Always implement pagination for list endpoints
- Include total count information in responses

### Search and Filtering
- Use case-insensitive search
- Support multiple search criteria
- Implement proper indexing for performance

---

## Integration Examples

### Complete Admin Dashboard Integration

```javascript
class AdminAPI {
  constructor(baseUrl) {
    this.baseUrl = baseUrl;
    this.sessionId = null;
  }

  async login(email, password) {
    const response = await fetch(`${this.baseUrl}/api/admin/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, password })
    });

    if (!response.ok) {
      throw new Error('Login failed');
    }

    const data = await response.json();
    this.sessionId = data.sessionId;
    return data;
  }

  async getProviders(page = 1, limit = 20, isVerified = null, search = '') {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      ...(isVerified !== null && { isVerified: isVerified.toString() }),
      ...(search && { search })
    });

    const response = await fetch(`${this.baseUrl}/api/admin/providers?${params}`, {
      headers: {
        'Authorization': `Bearer ${this.sessionId}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error('Failed to fetch providers');
    }

    return await response.json();
  }

  async approveProvider(providerId, reason = '') {
    const response = await fetch(`${this.baseUrl}/api/admin/providers/${providerId}/status`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${this.sessionId}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        isVerified: true,
        reason
      })
    });

    if (!response.ok) {
      throw new Error('Failed to approve provider');
    }

    return await response.json();
  }

  async getCustomers(page = 1, limit = 20, search = '') {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      ...(search && { search })
    });

    const response = await fetch(`${this.baseUrl}/api/admin/customers?${params}`, {
      headers: {
        'Authorization': `Bearer ${this.sessionId}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error('Failed to fetch customers');
    }

    return await response.json();
  }

  async createAdminUser(userData) {
    const response = await fetch(`${this.baseUrl}/api/admin/users`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.sessionId}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(userData)
    });

    if (!response.ok) {
      throw new Error('Failed to create admin user');
    }

    return await response.json();
  }

  async getCategories(includeHierarchy = true) {
    const params = new URLSearchParams({
      includeHierarchy: includeHierarchy.toString()
    });

    const response = await fetch(`${this.baseUrl}/api/admin/provider-categories?${params}`, {
      headers: {
        'Authorization': `Bearer ${this.sessionId}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error('Failed to fetch categories');
    }

    return await response.json();
  }

  async createCategory(title, parentId = null) {
    const response = await fetch(`${this.baseUrl}/api/admin/provider-categories`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.sessionId}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        title,
        ...(parentId && { parentId })
      })
    });

    if (!response.ok) {
      throw new Error('Failed to create category');
    }

    return await response.json();
  }
}

// Usage
const adminAPI = new AdminAPI('https://your-api-domain.com');

// Login
await adminAPI.login('<EMAIL>', 'password123');

// Get unverified providers
const providers = await adminAPI.getProviders(1, 10, false);

// Approve a provider
await adminAPI.approveProvider(1, 'All documentation verified');

// Get customers
const customers = await adminAPI.getCustomers(1, 10, 'john');

// Create new admin
await adminAPI.createAdminUser({
  email: '<EMAIL>',
  firstName: 'New',
  lastName: 'Admin',
  password: 'securePassword123',
  role: 'ADMIN'
});
```

---

## Notes

- All timestamps are in ISO 8601 format
- IDs are either UUIDs (for Users) or integers (for Providers, Categories)
- All endpoints require admin authentication except login
- The API uses RESTful conventions
- Pagination is 1-based (first page is page 1)
- Search is case-insensitive and partial match
