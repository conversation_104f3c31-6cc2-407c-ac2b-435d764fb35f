# Provider Dashboard APIs

This document describes the new provider dashboard APIs that provide metrics, appointments, and revenue data for providers.

## API Endpoints

### 1. GET /api/auth/providers/dashboard/metrics

**Description**: Retrieves dashboard metrics for the authenticated provider including today's appointments breakdown, weekly stats, and monthly stats.

**Authentication**: Required (provider must be authenticated)

**Request**: No parameters required

**Response Format**:
```json
{
  "todayAppointments": {
    "total": 8,
    "completed": 3,
    "pending": 2,
    "confirmed": 3,
    "cancelled": 0
  },
  "weeklyStats": {
    "appointments": 45,
    "revenue": 2450.00,
    "newCustomers": 12
  },
  "monthlyStats": {
    "appointments": 180,
    "revenue": 9800.00,
    "newCustomers": 45
  }
}
```

**Implementation Details**:
- Calculates today's appointments by status (pending, confirmed, completed, cancelled)
- Weekly stats calculated from start of current week (Sunday) to today
- Monthly stats calculated from start of current month to today
- Revenue calculated from completed appointments only
- New customers counted as unique customer IDs in the period

### 2. GET /api/auth/providers/appointments/today

**Description**: Retrieves all appointments for the authenticated provider for today with customer and service details.

**Authentication**: Required (provider must be authenticated)

**Request**: No parameters required

**Response Format**:
```json
{
  "appointments": [
    {
      "id": 123,
      "customer": { 
        "firstName": "John", 
        "lastName": "Doe" 
      },
      "service": { 
        "title": "Haircut", 
        "duration": 30 
      },
      "expectedAppointmentStartTime": "2024-01-15T10:00:00Z",
      "status": "confirmed"
    }
  ]
}
```

**Implementation Details**:
- Filters appointments for today (00:00:00 to 23:59:59)
- Includes customer first name and last name
- Includes service title and duration
- Ordered by expected appointment start time (ascending)
- Returns all appointment statuses

### 3. GET /api/auth/providers/dashboard/revenue-chart

**Description**: Retrieves revenue chart data for the authenticated provider for a specified period.

**Authentication**: Required (provider must be authenticated)

**Query Parameters**:
- `period` (optional): "week" or "month" (default: "week")

**Response Format**:

For week period:
```json
{
  "labels": ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"],
  "data": [120, 150, 200, 180, 250, 300, 220]
}
```

For month period:
```json
{
  "labels": ["1", "2", "3", ..., "31"],
  "data": [120, 150, 200, ...]
}
```

**Implementation Details**:
- Week period: Shows revenue for each day of the current week (Sunday to Saturday)
- Month period: Shows revenue for each day of the current month
- Only includes completed appointments in revenue calculation
- Revenue calculated from service price field
- Data array corresponds to labels array (same length)

## Database Entities Used

The APIs use the following database entities:
- `SProvider`: Provider information
- `Appointment`: Appointment records
- `Service`: Service details including price
- `CustomerFolder`: Links customers to providers
- `User`: Customer information

## Error Handling

All APIs follow consistent error handling:
- 401: User not authenticated
- 404: Provider profile not found
- 400: Invalid query parameters (for revenue chart API)
- 500: Internal server error

## File Locations

- **API Handlers**: `app/src/provider/apiHandlers.ts`
- **Wasp Configuration**: `app/main.wasp` (lines 1605-1624)
- **Documentation**: `app/docs/provider-dashboard-apis.md`

## Usage Examples

### Fetch Dashboard Metrics
```javascript
const response = await fetch('/api/auth/providers/dashboard/metrics', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
const metrics = await response.json();
```

### Fetch Today's Appointments
```javascript
const response = await fetch('/api/auth/providers/appointments/today', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
const { appointments } = await response.json();
```

### Fetch Revenue Chart (Weekly)
```javascript
const response = await fetch('/api/auth/providers/dashboard/revenue-chart?period=week', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
const chartData = await response.json();
```

### Fetch Revenue Chart (Monthly)
```javascript
const response = await fetch('/api/auth/providers/dashboard/revenue-chart?period=month', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
const chartData = await response.json();
```
