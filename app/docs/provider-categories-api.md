# Provider Categories API

This document describes the API endpoint for fetching provider categories that can be used by mobile apps for category selection dropdowns during provider registration and browsing.

## Overview

The provider categories API provides access to the hierarchical list of provider categories with optional language translation support. This endpoint is designed for mobile apps to populate category selection dropdowns and filters.

## Endpoint

### Get Provider Categories

**Endpoint:** `GET /api/auth/provider/categories`

**Description:** Fetches the complete list of provider categories with optional language translation support.

**Authentication:** Public endpoint - No authentication required

**Query Parameters:**
- `targetLanguage` (optional): Language code for translations. Supported values: `EN`, `AR`, `FR`

## Request Examples

### Basic Request (Default English)

```bash
curl -X GET "http://localhost:5400/api/auth/provider/categories" \
  -H "Content-Type: application/json"
```

### Request with Arabic Translation

```bash
curl -X GET "http://localhost:5400/api/auth/provider/categories?targetLanguage=AR" \
  -H "Content-Type: application/json"
```

### Request with French Translation

```bash
curl -X GET "http://localhost:5400/api/auth/provider/categories?targetLanguage=FR" \
  -H "Content-Type: application/json"
```

## Response Format

### Success Response (200)

**English Response:**
```json
[
  {
    "id": 1,
    "title": "Healthcare",
    "parentId": null
  },
  {
    "id": 2,
    "title": "Legal",
    "parentId": null
  },
  {
    "id": 16,
    "title": "Doctor",
    "parentId": 1
  },
  {
    "id": 17,
    "title": "Dentist",
    "parentId": 1
  }
]
```

**Arabic Response (targetLanguage=AR):**
```json
[
  {
    "id": 1,
    "title": "الرعاية الصحية",
    "parentId": null
  },
  {
    "id": 2,
    "title": "قانوني",
    "parentId": null
  },
  {
    "id": 16,
    "title": "طبيب",
    "parentId": 1
  },
  {
    "id": 17,
    "title": "طبيب أسنان",
    "parentId": 1
  }
]
```

**French Response (targetLanguage=FR):**
```json
[
  {
    "id": 1,
    "title": "Soins de santé",
    "parentId": null
  },
  {
    "id": 2,
    "title": "Juridique",
    "parentId": null
  },
  {
    "id": 16,
    "title": "Médecin",
    "parentId": 1
  },
  {
    "id": 17,
    "title": "Dentiste",
    "parentId": 1
  }
]
```

### Error Responses

**400 - Invalid Language Parameter:**
```json
{
  "message": "Invalid targetLanguage parameter. Must be one of: EN, AR, FR"
}
```

**500 - Server Error:**
```json
{
  "message": "Failed to fetch provider categories."
}
```

## Response Structure

Each category object contains:

- **`id`** (number): Unique identifier for the category
- **`title`** (string): Category name (translated if targetLanguage is specified)
- **`parentId`** (number|null): ID of parent category (null for top-level categories)

## Category Hierarchy

The categories are organized in a two-level hierarchy:

### Parent Categories (parentId: null)
- Healthcare
- Legal
- Financial
- Automotive
- Home Services
- Education
- Beauty & Wellness
- Professional Services
- Food & Dining
- Retail
- Government & Public Services
- Arts & Entertainment
- Sports & Recreation
- Pet Services
- Trades & Construction

### Child Categories (parentId: parent_category_id)
Each parent category has multiple child categories. For example:
- **Healthcare**: Doctor, Dentist, Physiotherapist, Optometrist, etc.
- **Legal**: Lawyer, Notary, Paralegal, Mediator
- **Automotive**: Mechanic, Car Wash, Tire Shop, Body Shop

## Mobile App Integration

### Category Selection Dropdown

```javascript
// Fetch categories for dropdown
const fetchCategories = async (language = 'EN') => {
  try {
    const response = await fetch(
      `/api/auth/provider/categories?targetLanguage=${language}`
    );
    const categories = await response.json();
    
    // Separate parent and child categories
    const parentCategories = categories.filter(cat => cat.parentId === null);
    const childCategories = categories.filter(cat => cat.parentId !== null);
    
    return { parentCategories, childCategories };
  } catch (error) {
    console.error('Failed to fetch categories:', error);
    throw error;
  }
};

// Usage in React Native
const CategoryPicker = () => {
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState(null);
  
  useEffect(() => {
    fetchCategories('AR').then(({ parentCategories, childCategories }) => {
      setCategories({ parents: parentCategories, children: childCategories });
    });
  }, []);
  
  return (
    <Picker
      selectedValue={selectedCategory}
      onValueChange={setSelectedCategory}
    >
      {categories.parents?.map(category => (
        <Picker.Item 
          key={category.id} 
          label={category.title} 
          value={category.id} 
        />
      ))}
    </Picker>
  );
};
```

### Hierarchical Category Display

```javascript
// Build category tree for hierarchical display
const buildCategoryTree = (categories) => {
  const parentCategories = categories.filter(cat => cat.parentId === null);
  
  return parentCategories.map(parent => ({
    ...parent,
    children: categories.filter(cat => cat.parentId === parent.id)
  }));
};

// Usage
const categoryTree = buildCategoryTree(categories);
```

## Implementation Details

### Handler Function

The endpoint is implemented in `app/src/auth/apiHandlers.ts`:

```typescript
export const handleGetProviderCategories = async (req: Request, res: Response, context: any) => {
  try {
    // Parse and validate language parameter
    const targetLanguageParam = req.query.targetLanguage as string | undefined;
    let targetLanguage: LanguageCode | undefined = undefined;
    
    if (targetLanguageParam) {
      if (Object.values(LanguageCode).includes(targetLanguageParam as LanguageCode)) {
        targetLanguage = targetLanguageParam as LanguageCode;
      } else {
        return res.status(400).json({ 
          message: "Invalid targetLanguage parameter. Must be one of: EN, AR, FR" 
        });
      }
    }

    // Call existing operation
    const categories = await getProviderCategories({ targetLanguage }, context);
    return res.status(200).json(categories);
  } catch (error: any) {
    console.error("[API] Error fetching provider categories:", error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error.message || "Failed to fetch provider categories.";
    return res.status(statusCode).json({ message });
  }
};
```

### Wasp Configuration

```wasp
api authGetProviderCategoriesApi {
  fn: import { handleGetProviderCategories } from "@src/auth/apiHandlers",
  httpRoute: (GET, "/api/auth/provider/categories"),
  auth: false // Public endpoint
}
```

## Features

### ✅ Multi-language Support
- English (EN) - Default
- Arabic (AR) - Full RTL support
- French (FR) - Complete translations

### ✅ Public Access
- No authentication required
- Accessible to both authenticated and unauthenticated users
- Perfect for registration flows

### ✅ Hierarchical Structure
- Two-level category hierarchy
- Parent-child relationships
- Easy to build nested UI components

### ✅ Error Handling
- Input validation for language parameters
- Proper HTTP status codes
- Descriptive error messages

### ✅ Performance Optimized
- Leverages existing Wasp operations
- Database-level translations
- Efficient query structure

## Use Cases

1. **Provider Registration**: Category selection during signup
2. **Provider Browsing**: Filter providers by category
3. **Search Functionality**: Category-based search filters
4. **Admin Interfaces**: Category management dropdowns
5. **Mobile Apps**: Native category selection components

## Related Endpoints

- **Provider Registration**: `POST /api/auth/provider/register`
- **Provider Search**: `GET /api/search/providers`
- **Provider Details**: `GET /api/public/provider/:providerId`

The provider categories API provides a robust foundation for category-based functionality in mobile applications with comprehensive language support and hierarchical organization.
