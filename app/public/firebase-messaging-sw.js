// public/firebase-messaging-sw.js

// Scripts for firebase and firebase messaging
importScripts('https://www.gstatic.com/firebasejs/9.15.0/firebase-app-compat.js'); // Use a recent, compatible version
importScripts('https://www.gstatic.com/firebasejs/9.15.0/firebase-messaging-compat.js');

// Initialize the Firebase app in the service worker for dalti-prod
// IMPORTANT: This configuration MUST match the one in firebase.ts exactly
const firebaseConfig = {
  apiKey: "REPLACE_WITH_DALTI_PROD_API_KEY", // Must match firebase.ts
  authDomain: "dalti-prod.firebaseapp.com",
  projectId: "dalti-prod",
  storageBucket: "dalti-prod.appspot.com", // Must match firebase.ts
  messagingSenderId: "REPLACE_WITH_DALTI_PROD_SENDER_ID", // Must match firebase.ts
  appId: "REPLACE_WITH_DALTI_PROD_APP_ID", // Must match firebase.ts
  // measurementId is not typically needed in the service worker
};

firebase.initializeApp(firebaseConfig);

// Retrieve an instance of Firebase Messaging so that it can handle background messages.
const messaging = firebase.messaging();

messaging.onBackgroundMessage((payload) => {
  console.log('[firebase-messaging-sw.js] Received background message ', payload);
  // Customize notification here
  const notificationTitle = payload.notification.title || 'New Notification';
  const notificationOptions = {
    body: payload.notification.body || 'You have a new message.',
    icon: payload.notification.icon || '/logo.png', // Path to an icon in your public folder
    data: payload.data // Any custom data you send with the notification
  };

  self.registration.showNotification(notificationTitle, notificationOptions);
});