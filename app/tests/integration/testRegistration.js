#!/usr/bin/env node

/**
 * Test Provider Registration Flow
 * Verify the provider registration process works correctly
 */

require('dotenv').config();
const axios = require('axios');

// Configuration
const config = {
  apiUrl: process.env.TEST_API_URL || 'https://dapi-test.adscloud.org:8443',
  timeout: 10000
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

// Generate unique test data
function generateTestProvider() {
  const timestamp = Date.now();
  const uniqueId = Math.random().toString(36).substring(2, 8);
  
  return {
    email: `test.provider.${timestamp}.${uniqueId}@example.com`,
    phone: `+1${timestamp.toString().slice(-10)}`,
    password: 'TestPassword123!',
    firstName: 'Test',
    lastName: 'Provider',
    businessName: `Test Business ${uniqueId}`,
    providerCategoryId: 1 // Assuming category 1 exists
  };
}

async function testProviderRegistration() {
  console.log(colorize('🧪 Testing Provider Registration Flow', 'cyan'));
  console.log(colorize('=' .repeat(50), 'blue'));
  console.log('');
  console.log(`📡 API URL: ${colorize(config.apiUrl, 'yellow')}`);
  console.log('');

  const testProvider = generateTestProvider();
  console.log(colorize('📋 Generated test provider data:', 'yellow'));
  console.log(`   Email: ${testProvider.email}`);
  console.log(`   Phone: ${testProvider.phone}`);
  console.log(`   Business: ${testProvider.businessName}`);
  console.log('');

  try {
    // Test 1: Request OTP for provider registration (using email)
    console.log(colorize('📧 Step 1: Requesting provider email OTP...', 'yellow'));

    const otpRequestData = {
      email: testProvider.email,
      firstName: testProvider.firstName,
      lastName: testProvider.lastName,
      password: testProvider.password,
      isProviderRegistration: true,
      providerCategoryId: testProvider.providerCategoryId,
      businessName: testProvider.businessName,
      phone: testProvider.phone
    };

    const otpResponse = await axios.post(
      `${config.apiUrl}/api/auth/request-email-otp`,
      otpRequestData,
      {
        timeout: config.timeout,
        headers: { 'Content-Type': 'application/json' }
      }
    );

    if (otpResponse.data && otpResponse.data.message) {
      console.log(colorize('✅ OTP request successful!', 'green'));
      console.log(`   Message: ${otpResponse.data.message}`);
    } else {
      console.log(colorize('⚠️  OTP request returned unexpected format', 'yellow'));
    }

    // Test 2: Complete registration with mock OTP
    console.log('');
    console.log(colorize('🔐 Step 2: Completing provider registration with OTP...', 'yellow'));
    
    const registrationData = {
      otp: '123456', // Mock OTP for testing
      identifier: testProvider.email, // Use email as identifier for email OTP
      password: testProvider.password,
      firstName: testProvider.firstName,
      lastName: testProvider.lastName,
      providerCategoryId: testProvider.providerCategoryId,
      businessName: testProvider.businessName,
      phone: testProvider.phone,
      email: testProvider.email
    };

    const response = await axios.post(
      `${config.apiUrl}/api/auth/provider/verify-otp-register`,
      registrationData,
      { 
        timeout: config.timeout,
        headers: { 'Content-Type': 'application/json' }
      }
    );

    if (response.data && response.data.sessionId) {
      console.log(colorize('✅ Provider registration successful!', 'green'));
      console.log(`   Session ID: ${response.data.sessionId.substring(0, 20)}...`);
      console.log(`   User ID: ${response.data.user?.id}`);
      console.log(`   Provider ID: ${response.data.provider?.id}`);
      
      // Test 2: Use the session to access provider endpoints
      console.log('');
      console.log(colorize('🔍 Testing authenticated API access...', 'yellow'));
      
      const authHeaders = {
        'Authorization': `Bearer ${response.data.sessionId}`,
        'Content-Type': 'application/json'
      };

      // Test profile endpoint
      try {
        const profileResponse = await axios.get(
          `${config.apiUrl}/api/auth/providers/profile`,
          { headers: authHeaders, timeout: config.timeout }
        );

        if (profileResponse.data && profileResponse.data.success) {
          console.log(colorize('✅ Provider profile access successful!', 'green'));
          console.log(`   Business Name: ${profileResponse.data.data?.businessName || 'N/A'}`);
        } else {
          console.log(colorize('⚠️  Profile access returned unexpected format', 'yellow'));
        }
      } catch (profileError) {
        console.log(colorize(`❌ Profile access failed: ${profileError.response?.status || profileError.message}`, 'red'));
      }

      // Test locations endpoint
      try {
        const locationsResponse = await axios.get(
          `${config.apiUrl}/api/auth/providers/locations`,
          { headers: authHeaders, timeout: config.timeout }
        );

        if (locationsResponse.data && locationsResponse.data.success) {
          console.log(colorize('✅ Provider locations access successful!', 'green'));
          console.log(`   Locations count: ${locationsResponse.data.data?.length || 0}`);
        } else {
          console.log(colorize('⚠️  Locations access returned unexpected format', 'yellow'));
        }
      } catch (locationsError) {
        console.log(colorize(`❌ Locations access failed: ${locationsError.response?.status || locationsError.message}`, 'red'));
      }

      console.log('');
      console.log(colorize('🎉 Registration and authentication flow test completed!', 'green'));
      return true;

    } else {
      console.log(colorize('❌ Registration failed - no session ID received', 'red'));
      console.log('Response:', response.data);
      return false;
    }

  } catch (error) {
    console.log(colorize('❌ Registration test failed:', 'red'));
    
    if (error.response) {
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Message: ${error.response.data?.message || 'Unknown error'}`);
      
      if (error.response.data?.errors) {
        console.log('   Validation errors:');
        console.log(JSON.stringify(error.response.data.errors, null, 2));
      }
    } else {
      console.log(`   Error: ${error.message}`);
    }
    
    return false;
  }
}

async function testProviderLogin() {
  console.log('');
  console.log(colorize('🔐 Testing Provider Login with Non-existent User...', 'yellow'));
  
  try {
    const response = await axios.post(
      `${config.apiUrl}/api/auth/provider/login`,
      {
        identifier: '<EMAIL>',
        password: 'wrongpassword'
      },
      { 
        timeout: config.timeout,
        headers: { 'Content-Type': 'application/json' }
      }
    );

    console.log(colorize('⚠️  Login should have failed but didn\'t', 'yellow'));
    return false;
  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log(colorize('✅ Login correctly rejected invalid credentials', 'green'));
      console.log(`   Message: ${error.response.data?.message || 'Unknown error'}`);
      return true;
    } else {
      console.log(colorize(`❌ Unexpected login error: ${error.response?.status || error.message}`, 'red'));
      return false;
    }
  }
}

async function main() {
  const registrationSuccess = await testProviderRegistration();
  const loginTestSuccess = await testProviderLogin();
  
  console.log('');
  console.log(colorize('📊 Test Summary:', 'cyan'));
  console.log(colorize('=' .repeat(30), 'blue'));
  console.log(`Registration Flow: ${registrationSuccess ? colorize('✅ PASS', 'green') : colorize('❌ FAIL', 'red')}`);
  console.log(`Login Validation: ${loginTestSuccess ? colorize('✅ PASS', 'green') : colorize('❌ FAIL', 'red')}`);
  
  if (registrationSuccess && loginTestSuccess) {
    console.log('');
    console.log(colorize('🎉 All tests passed! Provider authentication flow is working correctly.', 'green'));
    process.exit(0);
  } else {
    console.log('');
    console.log(colorize('❌ Some tests failed. Check the implementation.', 'red'));
    process.exit(1);
  }
}

// Run the test
main().catch((error) => {
  console.error(colorize(`💥 Fatal error: ${error.message}`, 'red'));
  process.exit(1);
});
