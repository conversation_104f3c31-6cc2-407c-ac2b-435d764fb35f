/**
 * Test Data Generators
 * Utility functions to generate test data for Provider Mobile API integration tests
 */

const config = require('../config');

/**
 * Generate unique identifier for test data
 */
function generateUniqueId() {
  const timestamp = Date.now();
  const randomId = Math.random().toString(36).substring(2, 8);
  return { timestamp, randomId };
}

/**
 * Generate test provider registration data
 */
function generateProviderData(overrides = {}) {
  const { timestamp, randomId } = generateUniqueId();
  
  const defaultData = {
    email: `provider.test.${timestamp}.${randomId}@example.com`,
    phone: `+1${timestamp.toString().slice(-10)}`,
    password: 'TestPassword123!',
    firstName: 'Test',
    lastName: 'Provider',
    businessName: `Test Business ${randomId}`,
    providerCategoryId: config.categories.provider
  };

  return { ...defaultData, ...overrides };
}

/**
 * Generate test location data
 */
function generateLocationData(overrides = {}) {
  const { randomId } = generateUniqueId();
  
  const postalCode = Math.floor(Math.random() * 90000) + 10000;
  const streetNumber = Math.floor(Math.random() * 9999) + 1;

  const defaultData = {
    name: `Test Location ${randomId}`,
    description: `Integration test location ${randomId}`,
    address: `${streetNumber} Test Street, Test City, Test State ${postalCode}, Test Country`,
    city: 'Test City',
    country: 'Test Country',
    postalCode: postalCode.toString(),
    latitude: 40.7128 + (Math.random() - 0.5) * 0.1, // NYC area with small variation
    longitude: -74.0060 + (Math.random() - 0.5) * 0.1,
    isActive: true,
    timezone: 'America/New_York'
  };

  return { ...defaultData, ...overrides };
}

/**
 * Generate test service data
 */
function generateServiceData(overrides = {}) {
  const { randomId } = generateUniqueId();
  
  const serviceTypes = ['Consultation', 'Treatment', 'Checkup', 'Therapy', 'Examination'];
  const colors = ['#FF5722', '#2196F3', '#4CAF50', '#FF9800', '#9C27B0', '#F44336'];
  
  const defaultData = {
    title: `Test ${serviceTypes[Math.floor(Math.random() * serviceTypes.length)]} ${randomId}`,
    description: `Integration test service ${randomId}`,
    duration: [15, 30, 45, 60, 90][Math.floor(Math.random() * 5)], // Random duration
    price: Math.floor(Math.random() * 200) + 25, // $25-$225
    color: colors[Math.floor(Math.random() * colors.length)],
    categoryId: config.categories.service,
    isActive: true,
    isPublic: true
  };

  return { ...defaultData, ...overrides };
}

/**
 * Generate test queue data
 */
function generateQueueData(locationId, overrides = {}) {
  const { randomId } = generateUniqueId();
  
  const defaultData = {
    title: `Test Queue ${randomId}`,
    description: `Integration test queue ${randomId}`,
    locationId: locationId,
    isActive: true,
    maxCapacity: Math.floor(Math.random() * 20) + 5, // 5-25 capacity
    estimatedWaitTime: Math.floor(Math.random() * 60) + 15 // 15-75 minutes
  };

  return { ...defaultData, ...overrides };
}

/**
 * Generate test schedule data
 */
function generateScheduleData(overrides = {}) {
  const daysOfWeek = [0, 1, 2, 3, 4, 5, 6]; // Sunday = 0, Monday = 1, etc.
  const startHours = [8, 9, 10]; // 8am, 9am, 10am
  const endHours = [16, 17, 18]; // 4pm, 5pm, 6pm
  
  const defaultData = {
    dayOfWeek: daysOfWeek[Math.floor(Math.random() * 5)], // Weekdays only for testing
    startTime: `${startHours[Math.floor(Math.random() * startHours.length)].toString().padStart(2, '0')}:00`,
    endTime: `${endHours[Math.floor(Math.random() * endHours.length)].toString().padStart(2, '0')}:00`,
    isActive: true
  };

  return { ...defaultData, ...overrides };
}

/**
 * Generate test customer data
 */
function generateCustomerData(overrides = {}) {
  const { timestamp, randomId } = generateUniqueId();
  
  const firstNames = ['John', 'Jane', 'Mike', 'Sarah', 'David', 'Lisa', 'Chris', 'Emma'];
  const lastNames = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis'];
  
  const defaultData = {
    firstName: firstNames[Math.floor(Math.random() * firstNames.length)],
    lastName: lastNames[Math.floor(Math.random() * lastNames.length)],
    email: `customer.test.${timestamp}.${randomId}@example.com`,
    phoneNumber: `+1${timestamp.toString().slice(-10)}`,
    dateOfBirth: new Date(1980 + Math.floor(Math.random() * 30), Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1).toISOString().split('T')[0],
    notes: `Test customer ${randomId} - created for integration testing`
  };

  return { ...defaultData, ...overrides };
}

/**
 * Generate test appointment data
 */
function generateAppointmentData(customerId, serviceId, queueId, overrides = {}) {
  const { randomId } = generateUniqueId();
  
  // Generate future appointment time (1-7 days from now)
  const futureDate = new Date();
  futureDate.setDate(futureDate.getDate() + Math.floor(Math.random() * 7) + 1);
  futureDate.setHours(9 + Math.floor(Math.random() * 8), 0, 0, 0); // 9am-5pm
  
  const defaultData = {
    customerId: customerId,
    serviceId: serviceId,
    queueId: queueId,
    scheduledAt: futureDate.toISOString(),
    status: 'scheduled',
    notes: `Integration test appointment ${randomId}`,
    priority: ['normal', 'high', 'urgent'][Math.floor(Math.random() * 3)]
  };

  return { ...defaultData, ...overrides };
}

/**
 * Generate test reschedule request data
 */
function generateRescheduleData(appointmentId, overrides = {}) {
  const { randomId } = generateUniqueId();
  
  // Generate new future appointment time
  const newDate = new Date();
  newDate.setDate(newDate.getDate() + Math.floor(Math.random() * 14) + 1);
  newDate.setHours(9 + Math.floor(Math.random() * 8), 0, 0, 0);
  
  const reasons = [
    'Schedule conflict',
    'Emergency came up',
    'Feeling unwell',
    'Transportation issues',
    'Work commitment'
  ];
  
  const defaultData = {
    appointmentId: appointmentId,
    newScheduledAt: newDate.toISOString(),
    reason: reasons[Math.floor(Math.random() * reasons.length)] + ` - Test ${randomId}`,
    notifyCustomer: Math.random() > 0.5, // Random boolean
    requestedBy: 'provider' // or 'customer'
  };

  return { ...defaultData, ...overrides };
}

/**
 * Generate test service category data
 */
function generateServiceCategoryData(overrides = {}) {
  const { randomId } = generateUniqueId();
  
  const categories = ['Medical', 'Dental', 'Therapy', 'Consultation', 'Wellness', 'Beauty'];
  const colors = ['#FF5722', '#2196F3', '#4CAF50', '#FF9800', '#9C27B0'];
  
  const defaultData = {
    name: `Test ${categories[Math.floor(Math.random() * categories.length)]} ${randomId}`,
    description: `Integration test service category ${randomId}`,
    color: colors[Math.floor(Math.random() * colors.length)],
    isActive: true,
    sortOrder: Math.floor(Math.random() * 100)
  };

  return { ...defaultData, ...overrides };
}

/**
 * Generate complete test dataset for a provider
 */
function generateCompleteProviderDataset(overrides = {}) {
  const providerData = generateProviderData(overrides.provider);
  const locationData = generateLocationData(overrides.location);
  const serviceData = generateServiceData(overrides.service);
  const customerData = generateCustomerData(overrides.customer);
  
  return {
    provider: providerData,
    location: locationData,
    service: serviceData,
    customer: customerData,
    // Queue will need locationId after location is created
    getQueueData: (locationId) => generateQueueData(locationId, overrides.queue),
    // Schedule data
    schedule: generateScheduleData(overrides.schedule),
    // Appointment will need customerID, serviceId, queueId after they're created
    getAppointmentData: (customerId, serviceId, queueId) => 
      generateAppointmentData(customerId, serviceId, queueId, overrides.appointment),
    // Reschedule will need appointmentId after appointment is created
    getRescheduleData: (appointmentId) => 
      generateRescheduleData(appointmentId, overrides.reschedule)
  };
}

/**
 * Utility functions for common test data patterns
 */
const TestDataUtils = {
  /**
   * Generate unique email
   */
  generateEmail(prefix = 'test') {
    const { timestamp, randomId } = generateUniqueId();
    return `${prefix}.${timestamp}.${randomId}@example.com`;
  },

  /**
   * Generate unique phone number
   */
  generatePhone() {
    const timestamp = Date.now().toString().slice(-10);
    return `+1${timestamp}`;
  },

  /**
   * Generate future date
   */
  getFutureDate(daysFromNow = 1, hour = 10, minute = 0) {
    const date = new Date();
    date.setDate(date.getDate() + daysFromNow);
    date.setHours(hour, minute, 0, 0);
    return date.toISOString();
  },

  /**
   * Generate past date
   */
  getPastDate(daysAgo = 1, hour = 10, minute = 0) {
    const date = new Date();
    date.setDate(date.getDate() - daysAgo);
    date.setHours(hour, minute, 0, 0);
    return date.toISOString();
  },

  /**
   * Generate random string
   */
  generateRandomString(length = 8) {
    return Math.random().toString(36).substring(2, 2 + length);
  }
};

module.exports = {
  generateProviderData,
  generateLocationData,
  generateServiceData,
  generateQueueData,
  generateScheduleData,
  generateCustomerData,
  generateAppointmentData,
  generateRescheduleData,
  generateServiceCategoryData,
  generateCompleteProviderDataset,
  TestDataUtils
};
