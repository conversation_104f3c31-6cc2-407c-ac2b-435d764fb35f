/**
 * Integration Test Helper Functions
 * Utility functions for Provider Mobile API integration tests
 */

const axios = require('axios');
const config = require('../config');
const {
  generateProviderData,
  generateLocationData,
  generateServiceData,
  generateQueueData,
  generateScheduleData,
  generateCustomerData,
  generateAppointmentData,
  generateRescheduleData,
  generateCompleteProviderDataset,
  TestDataUtils
} = require('./testDataGenerators');

class TestHelpers {
  constructor() {
    this.authToken = null;
    this.currentProvider = null;
    this.testData = {
      createdIds: {
        locations: [],
        services: [],
        queues: [],
        schedules: [],
        customers: [],
        appointments: [],
        reschedules: []
      },
      createdProviders: []
    };

    // Configure axios defaults
    this.api = axios.create({
      baseURL: config.api.baseUrl,
      timeout: config.api.timeout,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    // Add request interceptor for auth
    this.api.interceptors.request.use((config) => {
      if (this.authToken) {
        config.headers.Authorization = `Bearer ${this.authToken}`;
      }
      return config;
    });

    // Add response interceptor for logging
    this.api.interceptors.response.use(
      (response) => {
        if (config.flags.verbose) {
          console.log(`✅ ${response.config.method.toUpperCase()} ${response.config.url} - ${response.status}`);
        }
        return response;
      },
      (error) => {
        if (config.flags.verbose) {
          console.log(`❌ ${error.config?.method?.toUpperCase()} ${error.config?.url} - ${error.response?.status || 'NETWORK_ERROR'}`);
          if (error.response?.data) {
            console.log('Error details:', error.response.data);
          }
        }
        return Promise.reject(error);
      }
    );
  }

  /**
   * Generate unique test provider data
   */
  generateTestProviderData(overrides = {}) {
    return generateProviderData(overrides);
  }

  /**
   * Generate test location data
   */
  generateTestLocationData(overrides = {}) {
    return generateLocationData(overrides);
  }

  /**
   * Generate test service data
   */
  generateTestServiceData(overrides = {}) {
    return generateServiceData(overrides);
  }

  /**
   * Generate test customer data
   */
  generateTestCustomerData(overrides = {}) {
    return generateCustomerData(overrides);
  }

  /**
   * Generate complete test dataset
   */
  generateCompleteTestDataset(overrides = {}) {
    return generateCompleteProviderDataset(overrides);
  }

  /**
   * Request OTP for provider registration
   */
  async requestProviderOtp(testProvider, useEmail = true) {
    try {
      if (useEmail) {
        const response = await this.api.post('/api/auth/request-email-otp', {
          email: testProvider.email,
          firstName: testProvider.firstName,
          lastName: testProvider.lastName,
          password: testProvider.password,
          isProviderRegistration: true,
          providerCategoryId: testProvider.providerCategoryId,
          businessName: testProvider.businessName,
          phone: testProvider.phone
        });
        return response.data;
      } else {
        const response = await this.api.post('/api/auth/request-otp', {
          phoneNumber: testProvider.phone,
          firstName: testProvider.firstName,
          lastName: testProvider.lastName,
          isProviderRegistration: true,
          providerCategoryId: testProvider.providerCategoryId,
          businessName: testProvider.businessName
        });
        return response.data;
      }
    } catch (error) {
      console.error('OTP request failed:', error.response?.data || error.message);
      throw error;
    }
  }

  /**
   * Register a new test provider (with OTP support)
   */
  async registerTestProvider(providerData = null) {
    const testProvider = providerData || this.generateTestProviderData();

    try {
      let otp = '123456'; // Default mock OTP

      // If test mode is enabled, request actual OTP
      if (process.env.OTP_TEST_MODE === 'true' && process.env.NODE_ENV !== 'production') {
        console.log('🧪 Test mode enabled - requesting real OTP...');
        const otpResponse = await this.requestProviderOtp(testProvider, true);
        if (otpResponse.otp) {
          otp = otpResponse.otp;
          console.log(`📧 Received OTP from test mode: ${otp}`);
        }
      }

      // Complete registration with OTP
      const response = await this.api.post('/api/auth/provider/verify-otp-register', {
        otp: otp,
        identifier: testProvider.email || testProvider.phone,
        password: testProvider.password,
        firstName: testProvider.firstName,
        lastName: testProvider.lastName,
        providerCategoryId: testProvider.providerCategoryId,
        businessName: testProvider.businessName,
        phone: testProvider.phone,
        email: testProvider.email
      });

      if (response.data && response.data.sessionId) {
        return {
          provider: testProvider,
          sessionId: response.data.sessionId,
          user: response.data.user,
          providerProfile: response.data.provider
        };
      } else {
        throw new Error('No session ID received from registration response');
      }
    } catch (error) {
      console.error('Provider registration failed:', error.response?.data || error.message);
      throw error;
    }
  }

  /**
   * Login with existing provider credentials
   */
  async loginProvider(identifier, password) {
    try {
      const response = await this.api.post('/api/auth/provider/login', {
        identifier,
        password
      });

      if (response.data && response.data.sessionId) {
        return {
          sessionId: response.data.sessionId,
          user: response.data.user,
          provider: response.data.provider
        };
      } else {
        throw new Error('No session ID received from login response');
      }
    } catch (error) {
      console.error('Provider login failed:', error.response?.data || error.message);
      throw error;
    }
  }

  /**
   * Authenticate and get session token (updated method)
   */
  async authenticate() {
    try {
      // Try to login with existing test user first
      try {
        const loginResult = await this.loginProvider(
          config.testUser.email,
          config.testUser.password
        );
        this.authToken = loginResult.sessionId;
        this.currentProvider = loginResult.provider;
        return this.authToken;
      } catch (loginError) {
        // If login fails, register a new test provider
        console.log('Login failed, registering new test provider...');
        const registrationResult = await this.registerTestProvider({
          email: config.testUser.email,
          phone: config.testUser.phoneNumber || this.generateTestPhone(),
          password: config.testUser.password,
          firstName: config.testUser.firstName,
          lastName: config.testUser.lastName,
          businessName: 'Test Provider Business',
          providerCategoryId: config.categories.provider
        });

        this.authToken = registrationResult.sessionId;
        this.currentProvider = registrationResult.providerProfile;
        this.testData.createdProviders = this.testData.createdProviders || [];
        this.testData.createdProviders.push(registrationResult.user.id);

        return this.authToken;
      }
    } catch (error) {
      console.error('Authentication failed:', error.response?.data || error.message);
      throw error;
    }
  }

  /**
   * Setup authenticated test environment
   */
  async setupAuthenticatedTest() {
    const sessionId = await this.authenticate();
    return {
      sessionId,
      authHeaders: { Authorization: `Bearer ${sessionId}` },
      provider: this.currentProvider
    };
  }

  /**
   * Create a fresh authenticated provider for testing
   */
  async createAuthenticatedProvider(providerOverrides = {}) {
    const registrationResult = await this.registerTestProvider(providerOverrides);

    // Set up authentication for this provider
    this.authToken = registrationResult.sessionId;
    this.currentProvider = registrationResult.providerProfile;

    // Track for cleanup
    this.testData.createdProviders = this.testData.createdProviders || [];
    this.testData.createdProviders.push(registrationResult.user.id);

    return {
      sessionId: registrationResult.sessionId,
      authHeaders: { Authorization: `Bearer ${registrationResult.sessionId}` },
      provider: registrationResult.providerProfile,
      user: registrationResult.user,
      credentials: {
        email: registrationResult.provider.email,
        password: registrationResult.provider.password
      }
    };
  }

  /**
   * Login with existing provider credentials
   */
  async loginWithProvider(email, password) {
    const loginResult = await this.loginProvider(email, password);

    // Update current authentication
    this.authToken = loginResult.sessionId;
    this.currentProvider = loginResult.provider;

    return {
      sessionId: loginResult.sessionId,
      authHeaders: { Authorization: `Bearer ${loginResult.sessionId}` },
      provider: loginResult.provider,
      user: loginResult.user
    };
  }

  /**
   * Wait for a specified amount of time
   */
  async wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Generate future date for appointments
   */
  getFutureDate(daysFromNow = 1, hour = 10, minute = 0) {
    return TestDataUtils.getFutureDate(daysFromNow, hour, minute);
  }

  /**
   * Generate past date
   */
  getPastDate(daysAgo = 1, hour = 10, minute = 0) {
    return TestDataUtils.getPastDate(daysAgo, hour, minute);
  }

  /**
   * Generate test email
   */
  generateTestEmail(prefix = 'test') {
    return TestDataUtils.generateEmail(prefix);
  }

  /**
   * Generate test phone number
   */
  generateTestPhone() {
    return TestDataUtils.generatePhone();
  }

  /**
   * Generate random string
   */
  generateRandomString(length = 8) {
    return TestDataUtils.generateRandomString(length);
  }

  /**
   * Assert response structure
   */
  assertResponse(response, expectedStatus = 200) {
    expect(response.status).toBe(expectedStatus);
    expect(response.data).toHaveProperty('success');
    expect(response.data).toHaveProperty('message');
    
    if (response.data.success) {
      expect(response.data).toHaveProperty('data');
    } else {
      // Error responses can have either 'error' field or just 'message' field
      const hasError = response.data.hasOwnProperty('error');
      const hasMessage = response.data.hasOwnProperty('message');
      expect(hasError || hasMessage).toBe(true);
    }
  }

  /**
   * Assert successful response
   */
  assertSuccess(response, expectedStatus = 200) {
    this.assertResponse(response, expectedStatus);
    expect(response.data.success).toBe(true);
    return response.data.data;
  }

  /**
   * Assert error response
   */
  assertError(response, expectedStatus = 400) {
    this.assertResponse(response, expectedStatus);
    expect(response.data.success).toBe(false);
    // Return error details - either from 'error' field or 'message' field
    return response.data.error || response.data.message;
  }

  /**
   * Track created resource for cleanup
   */
  trackCreated(type, id) {
    if (this.testData.createdIds[type]) {
      this.testData.createdIds[type].push(id);
    }
  }

  /**
   * Clean up created test data
   */
  async cleanup() {
    if (!config.flags.cleanup) {
      console.log('Cleanup skipped (TEST_CLEANUP=false)');
      return;
    }

    console.log('🧹 Cleaning up test data...');

    try {
      // Clean up in reverse order of dependencies
      await this.cleanupReschedules();
      await this.cleanupAppointments();
      await this.cleanupCustomers();
      await this.cleanupSchedules();
      await this.cleanupQueues();
      await this.cleanupServices();
      await this.cleanupLocations();
      await this.cleanupProviders();

      console.log('✅ Cleanup completed');
    } catch (error) {
      console.warn('⚠️ Cleanup failed:', error.message);
    }
  }

  async cleanupReschedules() {
    for (const id of this.testData.createdIds.reschedules) {
      try {
        await this.api.delete(`/api/auth/providers/reschedules/${id}`);
      } catch (error) {
        // Ignore cleanup errors
      }
    }
  }

  async cleanupAppointments() {
    for (const id of this.testData.createdIds.appointments) {
      try {
        await this.api.delete(`/api/auth/providers/appointments/${id}`);
      } catch (error) {
        // Ignore cleanup errors
      }
    }
  }

  async cleanupCustomers() {
    for (const id of this.testData.createdIds.customers) {
      try {
        await this.api.delete(`/api/auth/providers/customers/${id}`);
      } catch (error) {
        // Ignore cleanup errors
      }
    }
  }

  async cleanupSchedules() {
    for (const id of this.testData.createdIds.schedules) {
      try {
        await this.api.delete(`/api/auth/providers/schedules/${id}`);
      } catch (error) {
        // Ignore cleanup errors
      }
    }
  }

  async cleanupQueues() {
    for (const id of this.testData.createdIds.queues) {
      try {
        await this.api.delete(`/api/auth/providers/queues/${id}`);
      } catch (error) {
        // Ignore cleanup errors
      }
    }
  }

  async cleanupServices() {
    for (const id of this.testData.createdIds.services) {
      try {
        await this.api.delete(`/api/auth/providers/services/${id}`);
      } catch (error) {
        // Ignore cleanup errors
      }
    }
  }

  async cleanupLocations() {
    for (const id of this.testData.createdIds.locations) {
      try {
        await this.api.delete(`/api/auth/providers/locations/${id}`);
      } catch (error) {
        // Ignore cleanup errors
      }
    }
  }

  async cleanupProviders() {
    // Note: Provider cleanup might not be available via API
    // This is mainly for tracking purposes
    for (const userId of this.testData.createdProviders) {
      try {
        // If there's a provider deletion endpoint, use it here
        // await this.api.delete(`/api/auth/providers/${userId}`);
        console.log(`Provider ${userId} created during tests (manual cleanup may be required)`);
      } catch (error) {
        // Ignore cleanup errors
      }
    }
  }

  /**
   * Get test statistics
   */
  getTestStats() {
    const stats = {};
    for (const [type, ids] of Object.entries(this.testData.createdIds)) {
      stats[type] = ids.length;
    }
    return stats;
  }
}

module.exports = TestHelpers;
