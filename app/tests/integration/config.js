/**
 * Integration Test Configuration
 * Configuration for Provider Mobile API integration tests
 */

const config = {
  // API Configuration
  api: {
    baseUrl: process.env.TEST_API_URL || 'https://dapi-test.adscloud.org:8443',
    timeout: 30000,
    retries: 3
  },

  // Test User Credentials
  testUser: {
    email: process.env.TEST_USER_EMAIL || '<EMAIL>',
    password: process.env.TEST_USER_PASSWORD || 'TestPassword123!',
    firstName: 'Test',
    lastName: 'Provider',
    phoneNumber: '+**********'
  },

  // Test Data
  testData: {
    location: {
      name: 'Test Location',
      description: 'Integration test location',
      address: '123 Test Street, Test City, Test State 12345, Test Country',
      city: 'Test City',
      country: 'Test Country',
      postalCode: '12345',
      latitude: 40.7128,
      longitude: -74.0060
    },
    
    service: {
      title: 'Test Service',
      description: 'Integration test service',
      duration: 30,
      price: 25.00,
      color: '#FF5722',
      categoryId: 1
    },

    queue: {
      title: 'Test Queue',
      isActive: true
    },

    schedule: {
      dayOfWeek: 1, // Monday
      startTime: '09:00',
      endTime: '17:00'
    },

    customer: {
      firstName: 'Test',
      lastName: 'Customer',
      email: '<EMAIL>',
      phoneNumber: '+**********'
    },

    appointment: {
      notes: 'Integration test appointment'
    },

    reschedule: {
      reason: 'Integration test reschedule',
      notifyCustomer: true
    }
  },

  // Test Categories
  categories: {
    provider: 1, // Assuming category ID 1 exists
    service: 1   // Assuming service category ID 1 exists
  },

  // Test Timeouts
  timeouts: {
    short: 5000,
    medium: 10000,
    long: 30000
  },

  // Test Flags
  flags: {
    cleanup: process.env.TEST_CLEANUP !== 'false',
    verbose: process.env.TEST_VERBOSE === 'true',
    skipAuth: process.env.TEST_SKIP_AUTH === 'true'
  }
};

module.exports = config;
