#!/usr/bin/env node

/**
 * Quick Test Runner
 * Fast health check and basic API connectivity test
 */

const axios = require('axios');

// Load environment variables
require('dotenv').config();

// Configuration
const config = {
  apiUrl: process.env.TEST_API_URL || 'https://dapi-test.adscloud.org:8443',
  userEmail: process.env.TEST_USER_EMAIL || '<EMAIL>',
  userPassword: process.env.TEST_USER_PASSWORD || 'TestPassword123!'
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

async function testServerConnectivity() {
  console.log(colorize('🔍 Testing server connectivity...', 'yellow'));
  
  try {
    const response = await axios.get(config.apiUrl, { timeout: 5000 });
    console.log(colorize('✅ Server is responding', 'green'));
    return true;
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log(colorize('❌ Cannot connect to server', 'red'));
      console.log(colorize(`   Make sure the server is running at ${config.apiUrl}`, 'yellow'));
      return false;
    } else if (error.response) {
      console.log(colorize('✅ Server is responding (got HTTP response)', 'green'));
      return true;
    } else {
      console.log(colorize(`⚠️  Connection issue: ${error.message}`, 'yellow'));
      return false;
    }
  }
}

async function testAuthentication() {
  console.log(colorize('🔐 Testing authentication...', 'yellow'));

  try {
    // Try provider login first
    const response = await axios.post(`${config.apiUrl}/api/auth/provider/login`, {
      identifier: config.userEmail,
      password: config.userPassword
    }, { timeout: 10000 });

    if (response.data && response.data.sessionId) {
      console.log(colorize('✅ Provider authentication successful', 'green'));
      return response.data.sessionId;
    } else {
      console.log(colorize('❌ Authentication failed - no session ID received', 'red'));
      return null;
    }
  } catch (error) {
    if (error.response) {
      console.log(colorize(`❌ Provider authentication failed - ${error.response.status}`, 'red'));
      console.log(colorize(`   ${error.response.data?.message || 'Unknown error'}`, 'yellow'));

      // If provider login fails, try general login
      try {
        console.log(colorize('   Trying general login...', 'yellow'));
        const generalResponse = await axios.post(`${config.apiUrl}/api/auth/login`, {
          identifier: config.userEmail,
          password: config.userPassword
        }, { timeout: 10000 });

        if (generalResponse.data && generalResponse.data.sessionId) {
          console.log(colorize('✅ General authentication successful', 'green'));
          return generalResponse.data.sessionId;
        }
      } catch (generalError) {
        console.log(colorize('❌ General authentication also failed', 'red'));
      }
    } else {
      console.log(colorize(`❌ Authentication error: ${error.message}`, 'red'));
    }
    return null;
  }
}

async function testProviderEndpoints(token) {
  console.log(colorize('📱 Testing provider endpoints...', 'yellow'));
  
  const endpoints = [
    { name: 'Profile', url: '/api/auth/providers/profile' },
    { name: 'Locations', url: '/api/auth/providers/locations' },
    { name: 'Services', url: '/api/auth/providers/services' },
    { name: 'Queues', url: '/api/auth/providers/queues' }
  ];

  const results = [];

  for (const endpoint of endpoints) {
    try {
      const response = await axios.get(`${config.apiUrl}${endpoint.url}`, {
        headers: { Authorization: `Bearer ${token}` },
        timeout: 10000
      });

      if (response.data && response.data.success) {
        console.log(colorize(`  ✅ ${endpoint.name}: OK`, 'green'));
        results.push({ name: endpoint.name, status: 'OK' });
      } else {
        console.log(colorize(`  ⚠️  ${endpoint.name}: Unexpected response format`, 'yellow'));
        results.push({ name: endpoint.name, status: 'WARNING' });
      }
    } catch (error) {
      if (error.response) {
        console.log(colorize(`  ❌ ${endpoint.name}: ${error.response.status}`, 'red'));
        results.push({ name: endpoint.name, status: 'ERROR', code: error.response.status });
      } else {
        console.log(colorize(`  ❌ ${endpoint.name}: ${error.message}`, 'red'));
        results.push({ name: endpoint.name, status: 'ERROR', message: error.message });
      }
    }
  }

  return results;
}

async function testCreateAndDelete(token) {
  console.log(colorize('🧪 Testing create/delete operations...', 'yellow'));
  
  try {
    // Test creating a location
    const locationData = {
      name: 'Quick Test Location',
      description: 'Location created by quick test',
      address: {
        street: '123 Test Street',
        city: 'Test City',
        state: 'Test State',
        postalCode: '12345',
        country: 'Test Country',
        latitude: 40.7128,
        longitude: -74.0060
      }
    };

    const createResponse = await axios.post(`${config.apiUrl}/api/auth/providers/locations`, locationData, {
      headers: { Authorization: `Bearer ${token}` },
      timeout: 10000
    });

    if (createResponse.data && createResponse.data.success) {
      const locationId = createResponse.data.data.id;
      console.log(colorize('  ✅ Create operation: OK', 'green'));

      // Test deleting the location
      const deleteResponse = await axios.delete(`${config.apiUrl}/api/auth/providers/locations/${locationId}`, {
        headers: { Authorization: `Bearer ${token}` },
        timeout: 10000
      });

      if (deleteResponse.data && deleteResponse.data.success) {
        console.log(colorize('  ✅ Delete operation: OK', 'green'));
        return true;
      } else {
        console.log(colorize('  ⚠️  Delete operation: Unexpected response', 'yellow'));
        return false;
      }
    } else {
      console.log(colorize('  ❌ Create operation failed', 'red'));
      return false;
    }
  } catch (error) {
    if (error.response) {
      console.log(colorize(`  ❌ CRUD test failed: ${error.response.status}`, 'red'));
      console.log(colorize(`     ${error.response.data?.message || 'Unknown error'}`, 'yellow'));
    } else {
      console.log(colorize(`  ❌ CRUD test error: ${error.message}`, 'red'));
    }
    return false;
  }
}

function printSummary(results) {
  console.log('');
  console.log(colorize('📊 Test Summary:', 'cyan'));
  console.log(colorize('=' .repeat(40), 'blue'));
  
  const successful = results.filter(r => r.status === 'OK').length;
  const warnings = results.filter(r => r.status === 'WARNING').length;
  const errors = results.filter(r => r.status === 'ERROR').length;
  
  console.log(`✅ Successful: ${colorize(successful, 'green')}`);
  console.log(`⚠️  Warnings: ${colorize(warnings, 'yellow')}`);
  console.log(`❌ Errors: ${colorize(errors, 'red')}`);
  console.log('');
  
  if (errors === 0) {
    console.log(colorize('🎉 All tests passed! API is ready for integration.', 'green'));
  } else if (warnings > 0 && errors === 0) {
    console.log(colorize('⚠️  Tests passed with warnings. Check the issues above.', 'yellow'));
  } else {
    console.log(colorize('❌ Some tests failed. Please fix the issues before proceeding.', 'red'));
  }
}

async function main() {
  console.log('');
  console.log(colorize('🚀 Provider Mobile API Quick Test', 'cyan'));
  console.log(colorize('=' .repeat(40), 'blue'));
  console.log('');
  console.log(`📡 API URL: ${colorize(config.apiUrl, 'yellow')}`);
  console.log(`👤 Test User: ${colorize(config.userEmail, 'yellow')}`);
  console.log('');

  const results = [];

  // Test 1: Server Connectivity
  const serverOk = await testServerConnectivity();
  results.push({ name: 'Server Connectivity', status: serverOk ? 'OK' : 'ERROR' });
  
  if (!serverOk) {
    printSummary(results);
    process.exit(1);
  }

  console.log('');

  // Test 2: Authentication
  const token = await testAuthentication();
  results.push({ name: 'Authentication', status: token ? 'OK' : 'ERROR' });
  
  if (!token) {
    printSummary(results);
    process.exit(1);
  }

  console.log('');

  // Test 3: Provider Endpoints
  const endpointResults = await testProviderEndpoints(token);
  results.push(...endpointResults);

  console.log('');

  // Test 4: CRUD Operations
  const crudOk = await testCreateAndDelete(token);
  results.push({ name: 'CRUD Operations', status: crudOk ? 'OK' : 'ERROR' });

  // Print summary
  printSummary(results);

  // Exit with appropriate code
  const hasErrors = results.some(r => r.status === 'ERROR');
  process.exit(hasErrors ? 1 : 0);
}

// Run the quick test
main().catch((error) => {
  console.error(colorize(`💥 Fatal error: ${error.message}`, 'red'));
  process.exit(1);
});
