/**
 * Provider Authentication Integration Tests
 * Tests for provider registration, login, and authentication flow
 */

const TestHelpers = require('../utils/testHelpers');
const { generateProviderData } = require('../utils/testDataGenerators');
const config = require('../config');

describe('Provider Authentication', () => {
  let helpers;

  beforeAll(async () => {
    helpers = new TestHelpers();
  }, config.timeouts.long);

  afterAll(async () => {
    await helpers.cleanup();
  }, config.timeouts.long);

  describe('Provider Registration Flow', () => {
    describe('Email OTP Registration', () => {
      test('should request email OTP successfully', async () => {
        const providerData = generateProviderData();
        
        const otpRequestData = {
          email: providerData.email,
          firstName: providerData.firstName,
          lastName: providerData.lastName,
          password: providerData.password,
          isProviderRegistration: true,
          providerCategoryId: providerData.providerCategoryId,
          businessName: providerData.businessName,
          phone: providerData.phone
        };

        const response = await helpers.api.post('/api/auth/request-email-otp', otpRequestData);
        
        expect(response.status).toBe(200);
        expect(response.data).toHaveProperty('message');
        expect(response.data.message).toContain('OTP sent successfully');
        
        // Check provider context
        expect(response.data).toHaveProperty('providerContext');
        expect(response.data.providerContext.isProviderRegistration).toBe(true);
        expect(response.data.providerContext.providerCategoryId).toBe(providerData.providerCategoryId);
        
        // Store OTP if test mode is enabled
        if (response.data.otp) {
          providerData.receivedOtp = response.data.otp;
          console.log(`📧 Received OTP from test mode: ${response.data.otp}`);
        }
        
        // Store for next test
        this.testProviderData = providerData;
      });

      test('should complete registration with valid OTP', async () => {
        const providerData = this.testProviderData || generateProviderData();
        const otp = providerData.receivedOtp || '123456'; // Use received OTP or fallback
        
        const registrationData = {
          otp: otp,
          identifier: providerData.email,
          password: providerData.password,
          firstName: providerData.firstName,
          lastName: providerData.lastName,
          providerCategoryId: providerData.providerCategoryId,
          businessName: providerData.businessName,
          phone: providerData.phone,
          email: providerData.email
        };

        const response = await helpers.api.post('/api/auth/provider/verify-otp-register', registrationData);
        
        expect(response.status).toBe(201);
        expect(response.data).toHaveProperty('sessionId');
        expect(response.data).toHaveProperty('user');
        expect(response.data).toHaveProperty('provider');
        
        // Verify user data
        expect(response.data.user.email).toBe(providerData.email);
        expect(response.data.user.firstName).toBe(providerData.firstName);
        expect(response.data.user.lastName).toBe(providerData.lastName);
        expect(response.data.user.role).toBe('CUSTOMER'); // Note: Provider users have CUSTOMER role, not CLIENT
        
        // Verify provider data
        expect(response.data.provider.userId).toBe(response.data.user.id);
        expect(response.data.provider.providerCategoryId).toBe(providerData.providerCategoryId);
        expect(response.data.provider.title).toBe(providerData.businessName);
        expect(response.data.provider.isSetupComplete).toBe(false);
        
        // Track for cleanup
        helpers.testData.createdProviders.push(response.data.user.id);
        
        // Store for login tests
        this.registeredProvider = {
          ...providerData,
          userId: response.data.user.id,
          providerId: response.data.provider.id,
          sessionId: response.data.sessionId
        };
      });

      test('should reject registration with invalid OTP', async () => {
        const providerData = generateProviderData();
        
        // First request OTP
        await helpers.api.post('/api/auth/request-email-otp', {
          email: providerData.email,
          firstName: providerData.firstName,
          lastName: providerData.lastName,
          password: providerData.password,
          isProviderRegistration: true,
          providerCategoryId: providerData.providerCategoryId,
          businessName: providerData.businessName,
          phone: providerData.phone
        });
        
        // Try to register with invalid OTP
        const registrationData = {
          otp: '000000', // Invalid OTP
          identifier: providerData.email,
          password: providerData.password,
          firstName: providerData.firstName,
          lastName: providerData.lastName,
          providerCategoryId: providerData.providerCategoryId,
          businessName: providerData.businessName,
          phone: providerData.phone,
          email: providerData.email
        };

        try {
          await helpers.api.post('/api/auth/provider/verify-otp-register', registrationData);
          fail('Should have thrown an error for invalid OTP');
        } catch (error) {
          expect(error.response.status).toBe(400);
          expect(error.response.data.message).toContain('Invalid OTP');
        }
      });

      test('should reject duplicate email registration', async () => {
        const existingProvider = this.registeredProvider;
        if (!existingProvider) {
          console.log('Skipping duplicate email test - no existing provider');
          return;
        }
        
        const duplicateData = {
          email: existingProvider.email, // Same email
          firstName: 'Different',
          lastName: 'Name',
          password: 'DifferentPassword123!',
          isProviderRegistration: true,
          providerCategoryId: config.categories.provider,
          businessName: 'Different Business',
          phone: helpers.generateTestPhone()
        };

        try {
          await helpers.api.post('/api/auth/request-email-otp', duplicateData);
          fail('Should have thrown an error for duplicate email');
        } catch (error) {
          expect(error.response.status).toBeGreaterThanOrEqual(400);
          // The exact error message may vary based on implementation
        }
      });
    });

    describe('Phone OTP Registration', () => {
      test('should request phone OTP successfully', async () => {
        const providerData = generateProviderData();

        const otpRequestData = {
          phoneNumber: providerData.phone,
          firstName: providerData.firstName,
          lastName: providerData.lastName,
          isProviderRegistration: true,
          providerCategoryId: providerData.providerCategoryId,
          businessName: providerData.businessName
        };

        try {
          const response = await helpers.api.post('/api/auth/request-otp', otpRequestData);

          expect(response.status).toBe(200);
          expect(response.data).toHaveProperty('message');
          expect(response.data.message).toContain('OTP sent successfully');

          // Check provider context
          expect(response.data).toHaveProperty('providerContext');
          expect(response.data.providerContext.isProviderRegistration).toBe(true);

          // Store OTP if test mode is enabled
          if (response.data.otp) {
            console.log(`📱 Received phone OTP from test mode: ${response.data.otp}`);
          }
        } catch (error) {
          if (error.response?.status === 500 && error.response?.data?.message?.includes('Failed to send OTP')) {
            console.log('⏭️  Skipping phone OTP test - SMS service not configured');
            return; // Skip this test if SMS service is not available
          }
          throw error; // Re-throw if it's a different error
        }
      });
    });
  });

  describe('Provider Login Flow', () => {
    test('should login with valid credentials', async () => {
      const existingProvider = this.registeredProvider;
      if (!existingProvider) {
        console.log('Skipping login test - no registered provider available');
        return;
      }
      
      const loginData = {
        identifier: existingProvider.email,
        password: existingProvider.password
      };

      const response = await helpers.api.post('/api/auth/provider/login', loginData);
      
      expect(response.status).toBe(200);
      expect(response.data).toHaveProperty('sessionId');
      expect(response.data).toHaveProperty('user');
      expect(response.data).toHaveProperty('provider');
      
      // Verify session ID is different from registration session
      expect(response.data.sessionId).toBeDefined();
      expect(typeof response.data.sessionId).toBe('string');
      
      // Verify user data
      expect(response.data.user.id).toBe(existingProvider.userId);
      expect(response.data.user.email).toBe(existingProvider.email);
      
      // Verify provider data
      expect(response.data.provider.id).toBe(existingProvider.providerId);
      expect(response.data.provider.userId).toBe(existingProvider.userId);
    });

    test('should reject login with invalid email', async () => {
      const loginData = {
        identifier: '<EMAIL>',
        password: 'SomePassword123!'
      };

      try {
        await helpers.api.post('/api/auth/provider/login', loginData);
        fail('Should have thrown an error for invalid email');
      } catch (error) {
        expect(error.response.status).toBe(401);
        expect(error.response.data.message).toContain('Invalid credentials');
      }
    });

    test('should reject login with invalid password', async () => {
      const existingProvider = this.registeredProvider;
      if (!existingProvider) {
        console.log('Skipping invalid password test - no registered provider available');
        return;
      }
      
      const loginData = {
        identifier: existingProvider.email,
        password: 'WrongPassword123!'
      };

      try {
        await helpers.api.post('/api/auth/provider/login', loginData);
        fail('Should have thrown an error for invalid password');
      } catch (error) {
        expect(error.response.status).toBe(401);
        expect(error.response.data.message).toContain('Invalid credentials');
      }
    });

    test('should reject login for non-provider user', async () => {
      // This test would require creating a customer user first
      // For now, we'll skip it as it requires customer registration flow
      console.log('Skipping non-provider login test - requires customer registration implementation');
    });
  });

  describe('Password Reset Flow', () => {
    test('should handle password reset request', async () => {
      // Note: This test assumes password reset functionality exists
      // If not implemented, this test will be skipped
      const existingProvider = this.registeredProvider;
      if (!existingProvider) {
        console.log('Skipping password reset test - no registered provider available');
        return;
      }

      try {
        const response = await helpers.api.post('/api/auth/password-reset-request', {
          email: existingProvider.email
        });

        expect(response.status).toBe(200);
        expect(response.data).toHaveProperty('message');
        console.log('✅ Password reset functionality is available');
      } catch (error) {
        if (error.response?.status === 404) {
          console.log('⏭️  Password reset endpoint not implemented - skipping test');
        } else {
          throw error;
        }
      }
    });
  });

  describe('Token Management', () => {
    test('should handle token refresh', async () => {
      // Note: This test assumes token refresh functionality exists
      const existingProvider = this.registeredProvider;
      if (!existingProvider) {
        console.log('Skipping token refresh test - no registered provider available');
        return;
      }

      try {
        const response = await helpers.api.post('/api/auth/refresh-token', {}, {
          headers: { Authorization: `Bearer ${existingProvider.sessionId}` }
        });

        expect(response.status).toBe(200);
        expect(response.data).toHaveProperty('sessionId');
        console.log('✅ Token refresh functionality is available');
      } catch (error) {
        if (error.response?.status === 404) {
          console.log('⏭️  Token refresh endpoint not implemented - skipping test');
        } else {
          throw error;
        }
      }
    });

    test('should reject requests with invalid tokens', async () => {
      try {
        await helpers.api.get('/api/provider/profile', {
          headers: { Authorization: 'Bearer invalid-token' }
        });
        fail('Should have thrown an error for invalid token');
      } catch (error) {
        expect(error.response.status).toBeGreaterThanOrEqual(401);
      }
    });

    test('should reject requests without authentication', async () => {
      try {
        await helpers.api.get('/api/provider/profile');
        fail('Should have thrown an error for missing authentication');
      } catch (error) {
        expect(error.response.status).toBeGreaterThanOrEqual(401);
      }
    });
  });

  describe('Authentication Validation', () => {
    test('should validate request body for registration', async () => {
      const invalidData = {
        // Missing required fields
        email: 'invalid-email',
        password: '123' // Too short
      };

      try {
        await helpers.api.post('/api/auth/request-email-otp', invalidData);
        fail('Should have thrown validation error');
      } catch (error) {
        expect(error.response.status).toBe(400);
        expect(error.response.data.message).toContain('Invalid request body');
      }
    });

    test('should validate request body for login', async () => {
      const invalidData = {
        // Missing identifier and password
      };

      try {
        await helpers.api.post('/api/auth/provider/login', invalidData);
        fail('Should have thrown validation error');
      } catch (error) {
        expect(error.response.status).toBe(400);
        expect(error.response.data.message).toContain('Invalid request body');
      }
    });
  });
});
