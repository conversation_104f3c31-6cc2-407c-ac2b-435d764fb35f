# Provider Mobile API Authentication Flow Analysis

## 🔍 Current Authentication Implementation

Based on codebase analysis, here's the complete authentication flow for Provider Mobile APIs:

## 📋 Authentication Endpoints

### **1. Provider Registration Flow**
```
POST /api/auth/provider/verify-otp-register
```
- **Purpose**: Complete provider registration after OTP verification
- **Input**: `{ otp, identifier, password, firstName, lastName, providerCategoryId, businessName, phone, email }`
- **Process**: 
  1. Verifies O<PERSON> using existing `verifyOtpAndRegister` action
  2. Creates provider profile with `createSProviderForUser` action
  3. Automatically logs in user and returns session
- **Output**: `{ user, provider, sessionId }`

### **2. Provider Login Flow**
```
POST /api/auth/provider/login
```
- **Purpose**: Authenticate existing providers
- **Input**: `{ identifier, password }` (identifier can be email or phone)
- **Process**:
  1. Uses `loginWithPhoneOrEmail` action for authentication
  2. Verifies user has provider role (CLIENT)
  3. Returns provider-specific data
- **Output**: `{ user, provider, sessionId }`

### **3. General Login Flow**
```
POST /api/auth/login
```
- **Purpose**: General authentication for all users
- **Input**: `{ identifier, password }`
- **Process**: Uses `loginWithPhoneOrEmail` action
- **Output**: `{ sessionId }`

## 🔑 Session Management

### **Session Creation**
- Uses Wasp's `createSession(authId)` function
- Returns `sessionId` which serves as the JWT token
- Session is linked to user's auth record

### **Session Validation**
- **Mobile Auth Middleware**: Uses session ID as Bearer token
- **Provider Auth Middleware**: Validates user role and attaches provider info
- **Token Format**: `Authorization: Bearer <sessionId>`

### **Authentication Context**
```typescript
interface AuthContext {
  user: User;           // Authenticated user
  auth: Auth;          // Auth record
  provider?: Provider; // Provider info (if applicable)
}
```

## 🏗️ Authentication Architecture

### **User Roles**
- `CUSTOMER`: Regular customers
- `CLIENT`: Providers (confusing naming, but CLIENT = Provider)

### **Provider Verification**
- Providers must have `role: 'CLIENT'`
- Must have associated `SProvider` record
- Provider middleware attaches provider info to context

### **Middleware Chain**
1. **General Auth**: Validates session and attaches user
2. **Provider Auth**: Verifies provider role
3. **Provider Info**: Attaches provider data to context
4. **Resource Ownership**: Verifies provider owns resources

## 📊 API Response Formats

### **Successful Authentication**
```json
{
  "user": {
    "id": "string",
    "email": "string",
    "firstName": "string",
    "lastName": "string",
    "role": "CLIENT"
  },
  "provider": {
    "id": number,
    "userId": "string",
    "providerCategoryId": number,
    "title": "string",
    "phone": "string",
    "isSetupComplete": boolean
  },
  "sessionId": "string"
}
```

### **Error Responses**
```json
{
  "message": "Error description",
  "errors": { /* Validation errors */ }
}
```

## 🔧 Integration Test Requirements

### **1. Test User Creation**
- Need to create test providers through registration flow
- Cannot use direct signup - must use OTP verification flow
- Must include provider category and business information

### **2. Authentication Helper Functions**
```typescript
// Required helper functions:
async function createTestProvider(): Promise<TestProvider>
async function requestProviderOtp(phone: string): Promise<void>
async function registerProvider(otpData: OtpRegistrationData): Promise<AuthResult>
async function loginProvider(credentials: LoginData): Promise<AuthResult>
async function getAuthHeaders(sessionId: string): Promise<Headers>
```

### **3. Test Flow Sequence**
1. **Setup**: Create unique test provider data
2. **OTP Request**: Request OTP for provider registration
3. **Registration**: Complete registration with OTP
4. **Login**: Authenticate and get session
5. **API Testing**: Use session for authenticated requests
6. **Cleanup**: Remove test data

### **4. Session Management**
- Store `sessionId` from login response
- Use as Bearer token: `Authorization: Bearer <sessionId>`
- Handle session expiration and refresh

## ⚠️ Key Findings for Test Refactoring

### **Critical Issues to Fix**
1. **Wrong Login Format**: Tests use `email` field, should use `identifier`
2. **Missing Registration Flow**: Tests assume user exists, need registration
3. **Incorrect Token Handling**: Session ID is the token, not JWT
4. **Provider Role Requirement**: Must verify user has CLIENT role
5. **OTP Verification**: Registration requires OTP verification step

### **Authentication Flow for Tests**
```typescript
// Correct test authentication flow:
const testProvider = generateTestProviderData();
await requestProviderOtp(testProvider.phone);
const { sessionId } = await registerProvider({
  otp: "123456", // Mock OTP for tests
  identifier: testProvider.phone,
  password: testProvider.password,
  firstName: testProvider.firstName,
  lastName: testProvider.lastName,
  providerCategoryId: testProvider.categoryId,
  businessName: testProvider.businessName,
  phone: testProvider.phone,
  email: testProvider.email
});

// Use sessionId for subsequent requests
const authHeaders = { Authorization: `Bearer ${sessionId}` };
```

### **Provider Mobile API Endpoints**
All provider endpoints require:
- `auth: true` in Wasp configuration
- `Authorization: Bearer <sessionId>` header
- User must have `role: 'CLIENT'`
- Provider profile must exist

## 📝 Next Steps for Test Refactoring

1. **Update Authentication Helpers** (Task 26.2)
   - Fix login format to use `identifier`
   - Implement provider registration flow
   - Handle session ID as token

2. **Create Test Data Generators** (Task 26.3)
   - Generate valid provider registration data
   - Include required fields for provider creation

3. **Refactor Test Cases** (Task 26.4-26.6)
   - Update all tests to use correct authentication
   - Implement proper test setup and teardown
   - Add comprehensive error case testing

4. **Update Documentation** (Task 26.8)
   - Document correct authentication flow
   - Provide examples for test implementation

---

**This analysis provides the foundation for properly refactoring the integration tests to work with the actual authentication implementation.** 🔍✅
