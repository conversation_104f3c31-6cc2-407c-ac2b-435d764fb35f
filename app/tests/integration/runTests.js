#!/usr/bin/env node

/**
 * Integration Test Runner
 * Comprehensive test runner for Provider Mobile API integration tests
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Test configuration
const config = {
  apiUrl: process.env.TEST_API_URL || 'http://localhost:3001',
  verbose: process.env.TEST_VERBOSE === 'true',
  cleanup: process.env.TEST_CLEANUP !== 'false',
  coverage: process.env.TEST_COVERAGE === 'true',
  bail: process.env.TEST_BAIL === 'true'
};

// Test suites in dependency order
const testSuites = [
  {
    name: 'Profile Management',
    file: 'suites/profileManagement.test.js',
    description: 'Provider profile and setup tests'
  },
  {
    name: 'Location Management',
    file: 'suites/locationManagement.test.js',
    description: 'Location CRUD operations'
  },
  {
    name: 'Service Management',
    file: 'suites/serviceManagement.test.js',
    description: 'Service CRUD operations'
  },
  {
    name: 'Queue Management',
    file: 'suites/queueManagement.test.js',
    description: 'Queue and service assignment operations'
  },
  {
    name: 'Schedule Management',
    file: 'suites/scheduleManagement.test.js',
    description: 'Working hours and schedule management'
  },
  {
    name: 'Customer Management',
    file: 'suites/customerManagement.test.js',
    description: 'Customer relationship management'
  },
  {
    name: 'Appointment Management',
    file: 'suites/appointmentManagement.test.js',
    description: 'Appointment booking and management'
  },
  {
    name: 'Reschedule Management',
    file: 'suites/rescheduleManagement.test.js',
    description: 'Appointment reschedule requests'
  }
];

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

function printHeader() {
  console.log('');
  console.log(colorize('🧪 Provider Mobile API Integration Tests', 'cyan'));
  console.log(colorize('=' .repeat(50), 'blue'));
  console.log('');
  console.log(`📡 API URL: ${colorize(config.apiUrl, 'yellow')}`);
  console.log(`🔍 Verbose: ${colorize(config.verbose, 'yellow')}`);
  console.log(`🧹 Cleanup: ${colorize(config.cleanup, 'yellow')}`);
  console.log(`📊 Coverage: ${colorize(config.coverage, 'yellow')}`);
  console.log(`🛑 Bail on Failure: ${colorize(config.bail, 'yellow')}`);
  console.log('');
}

function printTestSuites() {
  console.log(colorize('📋 Test Suites:', 'cyan'));
  console.log('');
  
  testSuites.forEach((suite, index) => {
    console.log(`${colorize(`${index + 1}.`, 'blue')} ${colorize(suite.name, 'bright')}`);
    console.log(`   ${suite.description}`);
    console.log(`   ${colorize(suite.file, 'magenta')}`);
    console.log('');
  });
}

function runCommand(command, args, options = {}) {
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      ...options
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve(code);
      } else {
        reject(new Error(`Command failed with exit code ${code}`));
      }
    });

    child.on('error', (error) => {
      reject(error);
    });
  });
}

async function checkDependencies() {
  console.log(colorize('🔍 Checking dependencies...', 'yellow'));
  
  try {
    // Check if package.json exists
    if (!fs.existsSync('package.json')) {
      throw new Error('package.json not found. Run npm init first.');
    }

    // Check if node_modules exists
    if (!fs.existsSync('node_modules')) {
      console.log(colorize('📦 Installing dependencies...', 'yellow'));
      await runCommand('npm', ['install']);
    }

    console.log(colorize('✅ Dependencies OK', 'green'));
    console.log('');
  } catch (error) {
    console.error(colorize(`❌ Dependency check failed: ${error.message}`, 'red'));
    process.exit(1);
  }
}

async function runAllTests() {
  console.log(colorize('🚀 Running all integration tests...', 'cyan'));
  console.log('');

  const jestArgs = [
    '--runInBand',
    '--verbose'
  ];

  if (config.coverage) {
    jestArgs.push('--coverage');
  }

  if (config.bail) {
    jestArgs.push('--bail');
  }

  try {
    await runCommand('npx', ['jest', ...jestArgs]);
    console.log('');
    console.log(colorize('🎉 All tests completed successfully!', 'green'));
  } catch (error) {
    console.log('');
    console.error(colorize('❌ Tests failed!', 'red'));
    process.exit(1);
  }
}

async function runSingleSuite(suiteName) {
  const suite = testSuites.find(s => 
    s.name.toLowerCase().includes(suiteName.toLowerCase()) ||
    s.file.includes(suiteName.toLowerCase())
  );

  if (!suite) {
    console.error(colorize(`❌ Test suite '${suiteName}' not found!`, 'red'));
    console.log('');
    console.log(colorize('Available test suites:', 'yellow'));
    testSuites.forEach(s => {
      console.log(`  - ${s.name}`);
    });
    process.exit(1);
  }

  console.log(colorize(`🚀 Running ${suite.name} tests...`, 'cyan'));
  console.log('');

  try {
    await runCommand('npx', ['jest', '--runInBand', '--verbose', suite.file]);
    console.log('');
    console.log(colorize(`🎉 ${suite.name} tests completed successfully!`, 'green'));
  } catch (error) {
    console.log('');
    console.error(colorize(`❌ ${suite.name} tests failed!`, 'red'));
    process.exit(1);
  }
}

async function runSmokeTests() {
  console.log(colorize('🚀 Running smoke tests (success scenarios only)...', 'cyan'));
  console.log('');

  try {
    await runCommand('npx', [
      'jest',
      '--runInBand',
      '--verbose',
      '--testNamePattern=should.*successfully'
    ]);
    console.log('');
    console.log(colorize('🎉 Smoke tests completed successfully!', 'green'));
  } catch (error) {
    console.log('');
    console.error(colorize('❌ Smoke tests failed!', 'red'));
    process.exit(1);
  }
}

function printUsage() {
  console.log('');
  console.log(colorize('Usage:', 'cyan'));
  console.log('  node runTests.js [command] [options]');
  console.log('');
  console.log(colorize('Commands:', 'yellow'));
  console.log('  all          Run all test suites (default)');
  console.log('  smoke        Run smoke tests only (success scenarios)');
  console.log('  suite <name> Run specific test suite');
  console.log('  list         List available test suites');
  console.log('  help         Show this help message');
  console.log('');
  console.log(colorize('Environment Variables:', 'yellow'));
  console.log('  TEST_API_URL     API base URL (default: http://localhost:3001)');
  console.log('  TEST_VERBOSE     Enable verbose output (true/false)');
  console.log('  TEST_CLEANUP     Enable test cleanup (true/false)');
  console.log('  TEST_COVERAGE    Enable coverage report (true/false)');
  console.log('  TEST_BAIL        Bail on first failure (true/false)');
  console.log('');
}

async function main() {
  const command = process.argv[2] || 'all';
  const argument = process.argv[3];

  printHeader();

  switch (command) {
    case 'all':
      await checkDependencies();
      await runAllTests();
      break;

    case 'smoke':
      await checkDependencies();
      await runSmokeTests();
      break;

    case 'suite':
      if (!argument) {
        console.error(colorize('❌ Please specify a test suite name!', 'red'));
        printUsage();
        process.exit(1);
      }
      await checkDependencies();
      await runSingleSuite(argument);
      break;

    case 'list':
      printTestSuites();
      break;

    case 'help':
    case '--help':
    case '-h':
      printUsage();
      break;

    default:
      console.error(colorize(`❌ Unknown command: ${command}`, 'red'));
      printUsage();
      process.exit(1);
  }
}

// Run the main function
main().catch((error) => {
  console.error(colorize(`❌ Fatal error: ${error.message}`, 'red'));
  process.exit(1);
});
