/**
 * Jest Setup File
 * Global setup and configuration for integration tests
 */

require('dotenv').config();

// Global test timeout
jest.setTimeout(30000);

// Global test configuration
global.testConfig = {
  apiUrl: process.env.TEST_API_URL || 'http://localhost:3001',
  verbose: process.env.TEST_VERBOSE === 'true',
  cleanup: process.env.TEST_CLEANUP !== 'false'
};

// Global test utilities
global.expectValidResponse = (response, expectedStatus = 200) => {
  expect(response.status).toBe(expectedStatus);
  expect(response.data).toHaveProperty('success');
  expect(response.data).toHaveProperty('message');
  
  if (response.data.success) {
    expect(response.data).toHaveProperty('data');
  } else {
    expect(response.data).toHaveProperty('error');
  }
};

global.expectSuccessResponse = (response, expectedStatus = 200) => {
  expectValidResponse(response, expectedStatus);
  expect(response.data.success).toBe(true);
  return response.data.data;
};

global.expectErrorResponse = (response, expectedStatus = 400) => {
  expectValidResponse(response, expectedStatus);
  expect(response.data.success).toBe(false);
  return response.data.error;
};

// Console logging for test results
const originalConsoleLog = console.log;
console.log = (...args) => {
  if (global.testConfig.verbose) {
    originalConsoleLog(...args);
  }
};

// Global error handler for unhandled promises
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Global setup message
console.log('🧪 Integration Tests Setup Complete');
console.log(`📡 API URL: ${global.testConfig.apiUrl}`);
console.log(`🔍 Verbose: ${global.testConfig.verbose}`);
console.log(`🧹 Cleanup: ${global.testConfig.cleanup}`);
console.log('');
