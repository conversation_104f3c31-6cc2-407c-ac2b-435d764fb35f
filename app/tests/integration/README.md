# Provider Mobile API Integration Tests

## 🎯 Overview

Comprehensive integration test suite for the Provider Mobile API, covering all 30+ endpoints across 8 business domains.

## 📋 Test Coverage

### **Test Suites**
1. **Profile Management** (3 endpoints)
   - Get provider profile
   - Update provider profile
   - Complete provider setup

2. **Location Management** (4 endpoints)
   - Create, read, update, delete locations
   - Address management with coordinates

3. **Service Management** (4 endpoints)
   - Create, read, update, delete services
   - Service categories and pricing

4. **Queue Management** (7 endpoints)
   - Queue CRUD operations
   - Service assignments to queues
   - Location-based queue filtering

5. **Schedule Management** (4 endpoints)
   - Working hours management
   - Day-specific schedules
   - Location-based scheduling

6. **Customer Management** (4 endpoints)
   - Customer directory with search
   - Customer profiles and history

7. **Appointment Management** (4 endpoints)
   - Appointment CRUD operations
   - Status management
   - Calendar integration

8. **Reschedule Management** (4 endpoints)
   - Reschedule request handling
   - Approval/rejection workflows

## 🚀 Quick Start

### **Prerequisites**
- Node.js 16+ and npm 8+
- Running Provider Mobile API server
- Valid test user credentials

### **Installation**
```bash
cd app/tests/integration
npm install
```

### **Environment Setup**
Create a `.env` file or set environment variables:
```bash
# API Configuration
TEST_API_URL=http://localhost:3001
TEST_USER_EMAIL=<EMAIL>
TEST_USER_PASSWORD=TestPassword123!

# Test Configuration
TEST_VERBOSE=true
TEST_CLEANUP=true
TEST_COVERAGE=false
TEST_BAIL=false
```

### **Running Tests**

#### **All Tests**
```bash
npm test
# or
node runTests.js all
```

#### **Specific Test Suite**
```bash
npm run test:profile
npm run test:locations
npm run test:services
npm run test:queues
npm run test:schedules
npm run test:customers
npm run test:appointments
npm run test:reschedules

# or
node runTests.js suite profile
node runTests.js suite locations
```

#### **Smoke Tests (Success Scenarios Only)**
```bash
npm run test:smoke
# or
node runTests.js smoke
```

#### **Error Tests (Failure Scenarios Only)**
```bash
npm run test:errors
```

#### **With Coverage Report**
```bash
npm run test:coverage
# or
TEST_COVERAGE=true node runTests.js all
```

#### **Watch Mode**
```bash
npm run test:watch
```

## 🔧 Configuration

### **Environment Variables**

| Variable | Default | Description |
|----------|---------|-------------|
| `TEST_API_URL` | `http://localhost:3001` | API base URL |
| `TEST_USER_EMAIL` | `<EMAIL>` | Test user email |
| `TEST_USER_PASSWORD` | `TestPassword123!` | Test user password |
| `TEST_VERBOSE` | `false` | Enable verbose logging |
| `TEST_CLEANUP` | `true` | Clean up test data after tests |
| `TEST_COVERAGE` | `false` | Generate coverage report |
| `TEST_BAIL` | `false` | Stop on first test failure |

### **Test Configuration**
Edit `config.js` to modify:
- Test data templates
- API timeouts
- Category IDs
- Test flags

## 📊 Test Structure

### **Directory Layout**
```
tests/integration/
├── config.js              # Test configuration
├── setup.js               # Jest setup
├── package.json           # Dependencies and scripts
├── runTests.js            # Test runner
├── utils/
│   └── testHelpers.js     # Test utilities
└── suites/
    ├── profileManagement.test.js
    ├── locationManagement.test.js
    ├── serviceManagement.test.js
    ├── queueManagement.test.js
    ├── scheduleManagement.test.js
    ├── customerManagement.test.js
    ├── appointmentManagement.test.js
    └── rescheduleManagement.test.js
```

### **Test Helpers**
The `TestHelpers` class provides:
- **Authentication** - JWT token management
- **API Client** - Configured Axios instance
- **Assertions** - Response validation helpers
- **Data Management** - Test data creation and cleanup
- **Utilities** - Date generation, email/phone helpers

### **Test Data Management**
- **Automatic Cleanup** - Created test data is tracked and cleaned up
- **Dependency Order** - Tests run in dependency order
- **Isolation** - Each test suite is independent
- **Reusable Data** - Common test data templates

## 🧪 Test Scenarios

### **Success Scenarios**
- ✅ Valid data creation and updates
- ✅ Proper response structure validation
- ✅ Data persistence verification
- ✅ Relationship integrity checks

### **Error Scenarios**
- ❌ Invalid input data validation
- ❌ Missing required fields
- ❌ Non-existent resource access
- ❌ Unauthorized access attempts
- ❌ Dependency constraint violations

### **Edge Cases**
- 🔍 Empty data sets
- 🔍 Boundary value testing
- 🔍 Concurrent operation handling
- 🔍 Large data set performance

## 📈 Test Reports

### **Console Output**
- Real-time test progress
- Detailed error messages
- Performance timing
- Cleanup status

### **Coverage Report**
```bash
npm run test:coverage
```
Generates HTML coverage report in `coverage/` directory.

### **Test Results**
- **Pass/Fail Status** for each endpoint
- **Response Time** measurements
- **Data Validation** results
- **Cleanup Status** confirmation

## 🔍 Debugging

### **Verbose Mode**
```bash
TEST_VERBOSE=true npm test
```
Shows detailed HTTP request/response logs.

### **Single Test**
```bash
npx jest --runInBand --verbose profileManagement.test.js
```

### **Test Pattern**
```bash
npx jest --runInBand --testNamePattern="should create.*successfully"
```

### **Debug Mode**
```bash
node --inspect-brk node_modules/.bin/jest --runInBand
```

## 🛠️ Troubleshooting

### **Common Issues**

#### **Authentication Failures**
- Verify test user credentials
- Check API server is running
- Confirm user has provider role

#### **Test Data Conflicts**
- Run cleanup: `TEST_CLEANUP=true npm test`
- Check for existing test data
- Verify database state

#### **Network Issues**
- Confirm API URL is correct
- Check firewall/proxy settings
- Verify SSL certificates

#### **Timeout Issues**
- Increase timeout in `config.js`
- Check API server performance
- Verify database connectivity

### **Reset Test Environment**
```bash
# Clean up all test data
node scripts/cleanup.js

# Reset test database (if applicable)
npm run db:reset:test
```

## 📝 Writing New Tests

### **Test Template**
```javascript
describe('New Feature', () => {
  let helpers;

  beforeAll(async () => {
    helpers = new TestHelpers();
    await helpers.authenticate();
  });

  afterAll(async () => {
    await helpers.cleanup();
  });

  test('should perform action successfully', async () => {
    const response = await helpers.api.post('/api/endpoint', data);
    const result = helpers.assertSuccess(response, 201);
    
    expect(result).toHaveProperty('id');
    helpers.trackCreated('type', result.id);
  });
});
```

### **Best Practices**
- Use descriptive test names
- Test both success and failure scenarios
- Clean up created test data
- Use helper functions for common operations
- Validate response structure and data
- Test edge cases and boundary conditions

## 🎯 CI/CD Integration

### **GitHub Actions**
```yaml
- name: Run Integration Tests
  run: |
    cd app/tests/integration
    npm install
    npm test
  env:
    TEST_API_URL: ${{ secrets.TEST_API_URL }}
    TEST_USER_EMAIL: ${{ secrets.TEST_USER_EMAIL }}
    TEST_USER_PASSWORD: ${{ secrets.TEST_USER_PASSWORD }}
```

### **Docker**
```dockerfile
FROM node:16
WORKDIR /tests
COPY package*.json ./
RUN npm install
COPY . .
CMD ["npm", "test"]
```

---

**The integration test suite provides comprehensive coverage of all Provider Mobile API endpoints with automated cleanup and detailed reporting!** 🧪✅
