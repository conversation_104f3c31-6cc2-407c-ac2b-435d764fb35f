#!/usr/bin/env node

/**
 * Chargily Pay API Connection Test Script
 * 
 * This script tests the connection to Chargily Pay API and verifies
 * that all required environment variables are properly configured.
 * 
 * Usage: node test-chargily-connection.js
 */

require('dotenv').config({ path: '.env.server' });

// Check if required packages are installed
let ChargilyClient;
try {
  const chargilyPay = require('@chargily/chargily-pay');
  ChargilyClient = chargilyPay.ChargilyClient;
} catch (error) {
  console.error('❌ @chargily/chargily-pay package not found. Please install it first:');
  console.error('   npm install @chargily/chargily-pay');
  process.exit(1);
}

// Test configuration
const TEST_CONFIG = {
  customer: {
    name: 'Test Customer',
    email: '<EMAIL>',
    phone: '+213555123456',
    address: {
      country: 'DZ',
      state: 'Algiers',
      address: '123 Test Street, Algiers'
    }
  },
  product: {
    name: 'Test Product - API Verification',
    description: 'This is a test product created to verify API connectivity'
  },
  price: {
    amount: 1000, // 10.00 DZD
    currency: 'dzd'
  }
};

/**
 * Validate environment variables
 */
function validateEnvironment() {
  console.log('🔍 Validating environment configuration...\n');
  
  const requiredVars = [
    'CHARGILY_API_KEY',
    'CHARGILY_MODE',
    'CHARGILY_WEBHOOK_SECRET'
  ];
  
  const missingVars = [];
  const configStatus = {};
  
  requiredVars.forEach(varName => {
    const value = process.env[varName];
    if (!value || value.includes('placeholder')) {
      missingVars.push(varName);
      configStatus[varName] = '❌ Missing or placeholder';
    } else {
      configStatus[varName] = '✅ Configured';
    }
  });
  
  // Display configuration status
  Object.entries(configStatus).forEach(([key, status]) => {
    console.log(`${key}: ${status}`);
  });
  
  // Check optional plan IDs
  const planIds = [
    'CHARGILY_HOBBY_PLAN_ID',
    'CHARGILY_PRO_PLAN_ID',
    'CHARGILY_CREDITS_10_PLAN_ID',
    'CHARGILY_FREE_PLAN_ID'
  ];
  
  console.log('\n📋 Plan ID Configuration:');
  planIds.forEach(planId => {
    const value = process.env[planId];
    const status = (!value || value.includes('placeholder')) ? '⚠️  Placeholder' : '✅ Configured';
    console.log(`${planId}: ${status}`);
  });
  
  if (missingVars.length > 0) {
    console.error('\n❌ Missing required environment variables:');
    missingVars.forEach(varName => {
      console.error(`   - ${varName}`);
    });
    console.error('\nPlease update your .env.server file with actual Chargily credentials.');
    console.error('See docs/chargily-test-environment-setup.md for setup instructions.');
    return false;
  }
  
  console.log('\n✅ Environment validation passed!\n');
  return true;
}

/**
 * Initialize Chargily client
 */
function initializeClient() {
  try {
    const client = new ChargilyClient({
      api_key: process.env.CHARGILY_API_KEY,
      mode: process.env.CHARGILY_MODE || 'test'
    });
    
    console.log(`🔧 Chargily client initialized in ${process.env.CHARGILY_MODE || 'test'} mode`);
    return client;
  } catch (error) {
    console.error('❌ Failed to initialize Chargily client:', error.message);
    return null;
  }
}

/**
 * Test customer creation
 */
async function testCustomerCreation(client) {
  console.log('👤 Testing customer creation...');
  
  try {
    const customer = await client.createCustomer(TEST_CONFIG.customer);
    console.log(`✅ Customer created successfully: ${customer.id}`);
    return customer;
  } catch (error) {
    console.error('❌ Customer creation failed:', error.message);
    if (error.response) {
      console.error('   Response:', error.response.data);
    }
    return null;
  }
}

/**
 * Test product creation
 */
async function testProductCreation(client) {
  console.log('📦 Testing product creation...');
  
  try {
    const product = await client.createProduct(TEST_CONFIG.product);
    console.log(`✅ Product created successfully: ${product.id}`);
    return product;
  } catch (error) {
    console.error('❌ Product creation failed:', error.message);
    if (error.response) {
      console.error('   Response:', error.response.data);
    }
    return null;
  }
}

/**
 * Test price creation
 */
async function testPriceCreation(client, productId) {
  console.log('💰 Testing price creation...');
  
  try {
    const price = await client.createPrice({
      ...TEST_CONFIG.price,
      product_id: productId
    });
    console.log(`✅ Price created successfully: ${price.id}`);
    return price;
  } catch (error) {
    console.error('❌ Price creation failed:', error.message);
    if (error.response) {
      console.error('   Response:', error.response.data);
    }
    return null;
  }
}

/**
 * Test checkout session creation
 */
async function testCheckoutSession(client, customerId, priceId) {
  console.log('🛒 Testing checkout session creation...');
  
  try {
    const checkout = await client.createCheckout({
      items: [
        {
          price: priceId,
          quantity: 1
        }
      ],
      success_url: 'https://example.com/success',
      failure_url: 'https://example.com/failure',
      payment_method: 'edahabia', // Test with EDAHABIA
      customer_id: customerId,
      metadata: {
        test: 'true',
        environment: 'test'
      }
    });
    
    console.log(`✅ Checkout session created successfully: ${checkout.id}`);
    console.log(`   Checkout URL: ${checkout.checkout_url}`);
    return checkout;
  } catch (error) {
    console.error('❌ Checkout session creation failed:', error.message);
    if (error.response) {
      console.error('   Response:', error.response.data);
    }
    return null;
  }
}

/**
 * Cleanup test resources
 */
async function cleanup(client, resources) {
  console.log('\n🧹 Cleaning up test resources...');
  
  const { customer, product } = resources;
  
  try {
    if (customer && client.deleteCustomer) {
      await client.deleteCustomer(customer.id);
      console.log('✅ Test customer deleted');
    }
    
    if (product && client.deleteProduct) {
      await client.deleteProduct(product.id);
      console.log('✅ Test product deleted');
    }
  } catch (error) {
    console.warn('⚠️  Cleanup warning:', error.message);
    console.warn('   You may need to manually delete test resources from Chargily dashboard');
  }
}

/**
 * Main test function
 */
async function runTests() {
  console.log('🚀 Starting Chargily Pay API Connection Test\n');
  console.log('=' .repeat(60));
  
  // Validate environment
  if (!validateEnvironment()) {
    process.exit(1);
  }
  
  // Initialize client
  const client = initializeClient();
  if (!client) {
    process.exit(1);
  }
  
  const resources = {};
  let allTestsPassed = true;
  
  try {
    // Test customer creation
    const customer = await testCustomerCreation(client);
    if (customer) {
      resources.customer = customer;
    } else {
      allTestsPassed = false;
    }
    
    // Test product creation
    const product = await testProductCreation(client);
    if (product) {
      resources.product = product;
    } else {
      allTestsPassed = false;
    }
    
    // Test price creation (only if product was created)
    let price = null;
    if (product) {
      price = await testPriceCreation(client, product.id);
      if (!price) {
        allTestsPassed = false;
      }
    }
    
    // Test checkout session (only if customer and price were created)
    if (customer && price) {
      const checkout = await testCheckoutSession(client, customer.id, price.id);
      if (!checkout) {
        allTestsPassed = false;
      }
    }
    
  } catch (error) {
    console.error('❌ Unexpected error during testing:', error.message);
    allTestsPassed = false;
  } finally {
    // Cleanup
    await cleanup(client, resources);
  }
  
  // Final results
  console.log('\n' + '=' .repeat(60));
  if (allTestsPassed) {
    console.log('🎉 All tests passed! Chargily Pay integration is ready.');
    console.log('\nNext steps:');
    console.log('1. Create actual products in Chargily dashboard');
    console.log('2. Update plan IDs in .env.server');
    console.log('3. Set up webhook endpoint with ngrok');
    console.log('4. Test complete payment flow in application');
  } else {
    console.log('❌ Some tests failed. Please check the errors above.');
    console.log('\nTroubleshooting:');
    console.log('1. Verify API keys are correct and not placeholders');
    console.log('2. Check Chargily account is in test mode');
    console.log('3. Ensure account has necessary permissions');
    console.log('4. Review Chargily dashboard for any account issues');
  }
  
  process.exit(allTestsPassed ? 0 : 1);
}

// Run tests if script is executed directly
if (require.main === module) {
  runTests().catch(error => {
    console.error('💥 Fatal error:', error.message);
    process.exit(1);
  });
}

module.exports = { runTests, validateEnvironment };
