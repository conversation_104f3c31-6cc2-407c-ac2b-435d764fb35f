#!/usr/bin/env node

/**
 * ngrok Setup and Webhook Testing Script
 * 
 * This script helps set up ngrok for webhook testing and provides
 * comprehensive webhook testing for all payment processors.
 * 
 * Features:
 * 1. Automatic ngrok setup and configuration
 * 2. Webhook URL generation and validation
 * 3. Test webhook delivery for all processors
 * 4. Integration with payment processor dashboards
 * 
 * Usage: node setup-ngrok-testing.cjs [port]
 */

require('dotenv').config({ path: '.env.server' });
const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

const DEFAULT_PORT = 3001;
const NGROK_CONFIG_FILE = '.ngrok.yml';

/**
 * Check if ngrok is installed
 */
function checkNgrokInstallation() {
  console.log('🔍 Checking ngrok installation...\n');
  
  try {
    const version = execSync('ngrok version', { encoding: 'utf8' });
    console.log('✅ ngrok is installed:', version.trim());
    return true;
  } catch (error) {
    console.error('❌ ngrok is not installed or not in PATH');
    console.error('\nInstall ngrok:');
    console.error('1. Download from https://ngrok.com/download');
    console.error('2. Or install via package manager:');
    console.error('   - macOS: brew install ngrok');
    console.error('   - Windows: choco install ngrok');
    console.error('   - Linux: snap install ngrok');
    return false;
  }
}

/**
 * Check ngrok authentication
 */
function checkNgrokAuth() {
  console.log('🔐 Checking ngrok authentication...\n');
  
  try {
    // Try to get account info
    execSync('ngrok config check', { encoding: 'utf8', stdio: 'pipe' });
    console.log('✅ ngrok is authenticated');
    return true;
  } catch (error) {
    console.error('❌ ngrok is not authenticated');
    console.error('\nTo authenticate ngrok:');
    console.error('1. Sign up at https://ngrok.com');
    console.error('2. Get your auth token from https://dashboard.ngrok.com/get-started/your-authtoken');
    console.error('3. Run: ngrok authtoken YOUR_AUTH_TOKEN');
    return false;
  }
}

/**
 * Create ngrok configuration file
 */
function createNgrokConfig(port) {
  console.log('📝 Creating ngrok configuration...\n');
  
  const config = `
version: "2"
authtoken_from_env: true
tunnels:
  payment-webhook:
    proto: http
    addr: ${port}
    subdomain_from_env: true
    inspect: true
    bind_tls: true
  web:
    proto: http
    addr: ${port}
    inspect: false
`;

  fs.writeFileSync(NGROK_CONFIG_FILE, config.trim());
  console.log(`✅ ngrok configuration created: ${NGROK_CONFIG_FILE}`);
}

/**
 * Start ngrok tunnel
 */
async function startNgrokTunnel(port) {
  console.log(`🚀 Starting ngrok tunnel for port ${port}...\n`);
  
  return new Promise((resolve, reject) => {
    const ngrokProcess = spawn('ngrok', ['http', port.toString(), '--log=stdout'], {
      stdio: ['pipe', 'pipe', 'pipe']
    });
    
    let output = '';
    let tunnelUrl = null;
    
    ngrokProcess.stdout.on('data', (data) => {
      output += data.toString();
      
      // Look for tunnel URL in output
      const urlMatch = output.match(/https:\/\/[a-z0-9-]+\.ngrok\.io/);
      if (urlMatch && !tunnelUrl) {
        tunnelUrl = urlMatch[0];
        console.log(`✅ ngrok tunnel started: ${tunnelUrl}`);
        resolve({ process: ngrokProcess, url: tunnelUrl });
      }
    });
    
    ngrokProcess.stderr.on('data', (data) => {
      console.error('ngrok error:', data.toString());
    });
    
    ngrokProcess.on('close', (code) => {
      if (code !== 0 && !tunnelUrl) {
        reject(new Error(`ngrok exited with code ${code}`));
      }
    });
    
    // Timeout after 30 seconds
    setTimeout(() => {
      if (!tunnelUrl) {
        ngrokProcess.kill();
        reject(new Error('ngrok tunnel startup timeout'));
      }
    }, 30000);
  });
}

/**
 * Test webhook endpoints with ngrok URL
 */
async function testWebhookEndpoints(ngrokUrl) {
  console.log('🔗 Testing webhook endpoints...\n');
  
  const webhookEndpoints = [
    { path: '/api/payment/lemonsqueezy/webhook', name: 'LemonSqueezy' },
    { path: '/api/payment/stripe/webhook', name: 'Stripe' },
    { path: '/api/payment/chargily/webhook', name: 'Chargily' }
  ];
  
  const results = {};
  
  for (const endpoint of webhookEndpoints) {
    const fullUrl = `${ngrokUrl}${endpoint.path}`;
    console.log(`Testing ${endpoint.name}: ${fullUrl}`);
    
    try {
      const fetch = (await import('node-fetch')).default;
      
      const response = await fetch(fullUrl, {
        method: 'GET',
        timeout: 5000
      });
      
      if (response.status === 405) {
        console.log(`✅ ${endpoint.name} webhook endpoint accessible`);
        results[endpoint.name] = { success: true, url: fullUrl };
      } else if (response.status === 404) {
        console.error(`❌ ${endpoint.name} webhook endpoint not found`);
        results[endpoint.name] = { success: false, error: 'Not found' };
      } else {
        console.log(`⚠️  ${endpoint.name} unexpected response: ${response.status}`);
        results[endpoint.name] = { success: true, url: fullUrl, warning: `Status ${response.status}` };
      }
      
    } catch (error) {
      console.error(`❌ ${endpoint.name} test failed:`, error.message);
      results[endpoint.name] = { success: false, error: error.message };
    }
  }
  
  return results;
}

/**
 * Generate webhook configuration instructions
 */
function generateWebhookInstructions(ngrokUrl, webhookResults) {
  console.log('\n📋 Webhook Configuration Instructions\n');
  console.log('=' .repeat(70));
  
  console.log('\n🍋 LemonSqueezy Webhook Setup:');
  if (webhookResults.LemonSqueezy?.success) {
    console.log(`1. Go to https://app.lemonsqueezy.com/settings/webhooks`);
    console.log(`2. Create new webhook with URL: ${webhookResults.LemonSqueezy.url}`);
    console.log(`3. Select events: order_created, subscription_created, subscription_updated`);
    console.log(`4. Set signing secret in .env.server: LEMONSQUEEZY_WEBHOOK_SECRET`);
  } else {
    console.log('❌ LemonSqueezy webhook endpoint not accessible');
  }
  
  console.log('\n💳 Stripe Webhook Setup:');
  if (webhookResults.Stripe?.success) {
    console.log(`1. Go to https://dashboard.stripe.com/test/webhooks`);
    console.log(`2. Create new webhook with URL: ${webhookResults.Stripe.url}`);
    console.log(`3. Select events: checkout.session.completed, invoice.payment_succeeded`);
    console.log(`4. Set signing secret in .env.server: STRIPE_WEBHOOK_SECRET`);
  } else {
    console.log('❌ Stripe webhook endpoint not accessible');
  }
  
  console.log('\n🇩🇿 Chargily Webhook Setup:');
  if (webhookResults.Chargily?.success) {
    console.log(`1. Go to https://pay.chargily.com/test/settings/webhooks`);
    console.log(`2. Create new webhook with URL: ${webhookResults.Chargily.url}`);
    console.log(`3. Select events: checkout.paid, checkout.failed, subscription.created`);
    console.log(`4. Set signing secret in .env.server: CHARGILY_WEBHOOK_SECRET`);
  } else {
    console.log('❌ Chargily webhook endpoint not accessible');
  }
  
  console.log('\n⚠️  Important Notes:');
  console.log('- Keep ngrok running while testing webhooks');
  console.log('- ngrok URLs change when restarted (unless using paid plan)');
  console.log('- Test webhook delivery in each payment processor dashboard');
  console.log('- Monitor application logs for webhook processing');
}

/**
 * Run webhook tests
 */
async function runWebhookTests(ngrokUrl) {
  console.log('🧪 Running webhook tests...\n');
  
  try {
    // Import and run existing webhook tests
    const { sendTestWebhook } = require('./test-chargily-webhooks.cjs');
    
    const testEvents = ['checkout.paid', 'checkout.failed', 'subscription.created'];
    
    for (const eventType of testEvents) {
      console.log(`Testing ${eventType} webhook...`);
      await sendTestWebhook(ngrokUrl, eventType);
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    console.log('✅ Webhook tests completed');
    
  } catch (error) {
    console.error('❌ Webhook tests failed:', error.message);
    console.error('Make sure test-chargily-webhooks.cjs exists and is working');
  }
}

/**
 * Monitor ngrok tunnel
 */
function monitorNgrokTunnel(ngrokProcess, ngrokUrl) {
  console.log('\n🔍 Monitoring ngrok tunnel...\n');
  console.log('Press Ctrl+C to stop ngrok and exit');
  console.log(`Tunnel URL: ${ngrokUrl}`);
  console.log(`Inspect URL: http://localhost:4040`);
  
  // Handle process termination
  process.on('SIGINT', () => {
    console.log('\n🛑 Stopping ngrok tunnel...');
    ngrokProcess.kill();
    process.exit(0);
  });
  
  ngrokProcess.on('close', (code) => {
    console.log(`\n🔚 ngrok tunnel closed with code ${code}`);
    process.exit(code);
  });
}

/**
 * Main setup function
 */
async function setupNgrokTesting() {
  const port = parseInt(process.argv[2]) || DEFAULT_PORT;
  
  console.log('🚀 ngrok Webhook Testing Setup\n');
  console.log(`Setting up ngrok tunnel for port ${port}...\n`);
  console.log('=' .repeat(70));
  
  // Check prerequisites
  if (!checkNgrokInstallation()) {
    process.exit(1);
  }
  
  if (!checkNgrokAuth()) {
    process.exit(1);
  }
  
  try {
    // Create ngrok config
    createNgrokConfig(port);
    
    // Start ngrok tunnel
    const { process: ngrokProcess, url: ngrokUrl } = await startNgrokTunnel(port);
    
    // Test webhook endpoints
    const webhookResults = await testWebhookEndpoints(ngrokUrl);
    
    // Generate configuration instructions
    generateWebhookInstructions(ngrokUrl, webhookResults);
    
    // Run webhook tests
    await runWebhookTests(ngrokUrl);
    
    // Monitor tunnel
    monitorNgrokTunnel(ngrokProcess, ngrokUrl);
    
  } catch (error) {
    console.error('💥 Setup failed:', error.message);
    process.exit(1);
  }
}

// Install node-fetch if needed
async function ensureFetch() {
  try {
    await import('node-fetch');
  } catch (error) {
    console.log('Installing node-fetch...');
    const { execSync } = require('child_process');
    execSync('npm install node-fetch@2', { stdio: 'inherit' });
  }
}

// Run setup if script is executed directly
if (require.main === module) {
  ensureFetch().then(() => {
    setupNgrokTesting().catch(error => {
      console.error('💥 Fatal error:', error.message);
      process.exit(1);
    });
  });
}

module.exports = { setupNgrokTesting, testWebhookEndpoints };
