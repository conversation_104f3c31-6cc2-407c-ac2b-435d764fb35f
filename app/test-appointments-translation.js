/**
 * Test script for Provider Appointments Translation APIs
 * Tests the translation functionality for appointment creation, updates, and retrieval
 */

const axios = require('axios');

// Configuration
const BASE_URL = 'https://dalti-test.adscloud.org:8443';
const API_BASE = `${BASE_URL}/api/auth/providers`;

// Test data
const testAppointmentData = {
  customerId: "customer123", // You'll need to use an existing customer ID
  serviceId: 1, // You'll need to use an existing service ID
  queueId: 1, // You'll need to use an existing queue ID
  expectedStartTime: "2024-12-15T10:00:00Z", // Future date
  notes: "موعد مهم للفحص الدوري والتنظيف العام للأسنان" // Arabic: Important appointment for routine checkup and general teeth cleaning
};

const updateAppointmentData = {
  notes: "Rendez-vous important pour un contrôle de routine et un nettoyage général des dents" // French: Important appointment for routine checkup and general teeth cleaning
};

// Helper function to make authenticated requests
async function makeAuthenticatedRequest(method, endpoint, data = null, token) {
  const config = {
    method,
    url: `${API_BASE}${endpoint}`,
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  };

  if (data) {
    config.data = data;
  }

  try {
    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`Error in ${method} ${endpoint}:`, error.response?.data || error.message);
    throw error;
  }
}

// Test functions
async function testAppointmentTranslation(authToken) {
  console.log('\n🧪 Testing Provider Appointments Translation APIs...\n');

  try {
    // Test 1: Get all appointments (baseline)
    console.log('📋 Test 1: Getting all provider appointments...');
    const initialResponse = await makeAuthenticatedRequest('GET', '/appointments', null, authToken);
    console.log('✅ Initial appointments retrieved successfully');
    console.log('🔄 Number of existing appointments:', initialResponse.data.data.length);
    
    if (initialResponse.data.data.length > 0) {
      console.log('🔄 Sample existing appointment:', {
        id: initialResponse.data.data[0].id,
        status: initialResponse.data.data[0].status,
        notes: initialResponse.data.data[0].notes,
        service: initialResponse.data.data[0].service.title,
        customer: `${initialResponse.data.data[0].customer.firstName} ${initialResponse.data.data[0].customer.lastName}`
      });
    }

    // Test 2: Create appointment with Arabic notes
    console.log('\n📝 Test 2: Creating appointment with Arabic notes...');
    const createResponse = await makeAuthenticatedRequest('POST', '/appointments', testAppointmentData, authToken);
    console.log('✅ Appointment created successfully');
    console.log('🔄 Created appointment:', {
      id: createResponse.data.id,
      status: createResponse.data.status,
      notes: createResponse.data.notes,
      expectedStartTime: createResponse.data.expectedAppointmentStartTime,
      service: createResponse.data.service.title,
      customer: `${createResponse.data.customer.firstName} ${createResponse.data.customer.lastName}`
    });

    const appointmentId = createResponse.data.id;

    // Test 3: Get all appointments after creation (should include translated content)
    console.log('\n📋 Test 3: Getting all appointments after creation...');
    const afterCreateResponse = await makeAuthenticatedRequest('GET', '/appointments', null, authToken);
    console.log('✅ Appointments retrieved after creation');
    console.log('🔄 Number of appointments:', afterCreateResponse.data.data.length);
    
    const createdAppointment = afterCreateResponse.data.data.find(apt => apt.id === appointmentId);
    if (createdAppointment) {
      console.log('🔄 Found created appointment in list:', {
        id: createdAppointment.id,
        status: createdAppointment.status,
        notes: createdAppointment.notes,
        service: createdAppointment.service.title
      });
    }

    // Test 4: Update appointment with French notes
    console.log('\n✏️ Test 4: Updating appointment with French notes...');
    const updateResponse = await makeAuthenticatedRequest('PUT', `/appointments/${appointmentId}`, updateAppointmentData, authToken);
    console.log('✅ Appointment updated successfully');
    console.log('🔄 Updated appointment:', {
      id: updateResponse.data.id,
      status: updateResponse.data.status,
      notes: updateResponse.data.notes,
      service: updateResponse.data.service.title,
      customer: `${updateResponse.data.customer.firstName} ${updateResponse.data.customer.lastName}`
    });

    // Test 5: Get appointments with filtering (test translation with filters)
    console.log('\n🔍 Test 5: Getting appointments with status filter...');
    const filteredResponse = await makeAuthenticatedRequest('GET', '/appointments?status=pending', null, authToken);
    console.log('✅ Filtered appointments retrieved successfully');
    console.log('🔄 Number of pending appointments:', filteredResponse.data.data.length);
    
    const filteredAppointment = filteredResponse.data.data.find(apt => apt.id === appointmentId);
    if (filteredAppointment) {
      console.log('🔄 Found appointment in filtered results:', {
        id: filteredAppointment.id,
        status: filteredAppointment.status,
        notes: filteredAppointment.notes
      });
    }

    // Test 6: Get appointments with date filtering
    console.log('\n📅 Test 6: Getting appointments with date filter...');
    const today = new Date().toISOString().split('T')[0];
    const tomorrow = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0];
    const dateFilteredResponse = await makeAuthenticatedRequest('GET', `/appointments?startDate=${today}&endDate=${tomorrow}`, null, authToken);
    console.log('✅ Date-filtered appointments retrieved successfully');
    console.log('🔄 Number of appointments in date range:', dateFilteredResponse.data.data.length);

    // Test 7: Final verification - get all appointments again
    console.log('\n🔄 Test 7: Final appointment verification...');
    const finalResponse = await makeAuthenticatedRequest('GET', '/appointments', null, authToken);
    console.log('✅ Final appointment verification successful');
    console.log('🔄 Final appointment state:', {
      totalAppointments: finalResponse.data.data.length,
      updatedAppointment: finalResponse.data.data.find(apt => apt.id === appointmentId)
    });

    console.log('\n🎉 All translation tests completed successfully!');
    console.log('\n📊 Test Summary:');
    console.log('- ✅ Initial appointment retrieval');
    console.log('- ✅ Appointment creation with Arabic notes');
    console.log('- ✅ Translation storage during creation');
    console.log('- ✅ Translated content retrieval (all appointments)');
    console.log('- ✅ Appointment update with French notes');
    console.log('- ✅ Translation storage during update');
    console.log('- ✅ Status-based filtering with translations');
    console.log('- ✅ Date-based filtering with translations');
    console.log('- ✅ Final appointment verification');

    return appointmentId;

  } catch (error) {
    console.error('❌ Translation test failed:', error.message);
    throw error;
  }
}

// Main execution
async function main() {
  console.log('🚀 Starting Provider Appointments Translation API Tests');
  console.log('🌐 Base URL:', BASE_URL);
  
  // Note: You'll need to provide a valid authentication token
  const authToken = process.env.PROVIDER_AUTH_TOKEN;
  
  if (!authToken) {
    console.error('❌ Error: PROVIDER_AUTH_TOKEN environment variable is required');
    console.log('💡 Usage: PROVIDER_AUTH_TOKEN=your_token_here node test-appointments-translation.js');
    process.exit(1);
  }

  try {
    await testAppointmentTranslation(authToken);
    console.log('\n✅ All tests passed successfully!');
  } catch (error) {
    console.error('\n❌ Tests failed:', error.message);
    process.exit(1);
  }
}

// Run the tests
if (require.main === module) {
  main();
}

module.exports = { testAppointmentTranslation };
