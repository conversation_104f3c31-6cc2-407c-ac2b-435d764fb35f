/**
 * Test script for Provider Customers Translation APIs
 * Tests the translation functionality for customer creation, updates, and retrieval
 */

const axios = require('axios');

// Configuration
const BASE_URL = 'https://dalti-test.adscloud.org:8443';
const API_BASE = `${BASE_URL}/api/auth/providers`;

// Test data
const testCustomerData = {
  firstName: "أحمد", // Arabic: Ahmed
  lastName: "بن علي", // Arabic: Ben Ali
  mobileNumber: "+213555123456",
  email: "<EMAIL>",
  nationalId: "1234567890123",
  notes: "عميل مهم يحتاج إلى رعاية خاصة ومتابعة دورية للحالة الصحية" // Arabic: Important client who needs special care and regular health monitoring
};

const updateCustomerData = {
  firstName: "Ahmed", // French: Ahmed
  lastName: "<PERSON>", // French: <PERSON>
  notes: "Client important qui a besoin de soins spéciaux et d'un suivi régulier de l'état de santé" // French: Important client who needs special care and regular health monitoring
};

// Helper function to make authenticated requests
async function makeAuthenticatedRequest(method, endpoint, data = null, token) {
  const config = {
    method,
    url: `${API_BASE}${endpoint}`,
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  };

  if (data) {
    config.data = data;
  }

  try {
    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`Error in ${method} ${endpoint}:`, error.response?.data || error.message);
    throw error;
  }
}

// Test functions
async function testCustomerTranslation(authToken) {
  console.log('\n🧪 Testing Provider Customers Translation APIs...\n');

  try {
    // Test 1: Get all customers (baseline)
    console.log('📋 Test 1: Getting all provider customers...');
    const initialResponse = await makeAuthenticatedRequest('GET', '/customers', null, authToken);
    console.log('✅ Initial customers retrieved successfully');
    console.log('🔄 Number of existing customers:', initialResponse.data.length);
    
    if (initialResponse.data.length > 0) {
      console.log('🔄 Sample existing customer:', {
        id: initialResponse.data[0].id,
        firstName: initialResponse.data[0].firstName,
        lastName: initialResponse.data[0].lastName,
        notes: initialResponse.data[0].notes,
        appointmentCount: initialResponse.data[0].appointmentCount
      });
    }

    // Test 2: Create customer with Arabic notes
    console.log('\n📝 Test 2: Creating customer with Arabic notes...');
    const createResponse = await makeAuthenticatedRequest('POST', '/customers', testCustomerData, authToken);
    console.log('✅ Customer created successfully');
    console.log('🔄 Created customer:', {
      id: createResponse.data.id,
      firstName: createResponse.data.firstName,
      lastName: createResponse.data.lastName,
      mobileNumber: createResponse.data.mobileNumber,
      email: createResponse.data.email,
      notes: createResponse.data.notes,
      appointmentCount: createResponse.data.appointmentCount
    });

    const customerId = createResponse.data.id;

    // Test 3: Get single customer by ID
    console.log('\n👤 Test 3: Getting single customer by ID...');
    const singleCustomerResponse = await makeAuthenticatedRequest('GET', `/customers/${customerId}`, null, authToken);
    console.log('✅ Single customer retrieved successfully');
    console.log('🔄 Single customer details:', {
      id: singleCustomerResponse.data.id,
      firstName: singleCustomerResponse.data.firstName,
      lastName: singleCustomerResponse.data.lastName,
      notes: singleCustomerResponse.data.notes,
      appointmentCount: singleCustomerResponse.data.appointmentCount
    });

    // Test 4: Get all customers after creation (should include translated content)
    console.log('\n📋 Test 4: Getting all customers after creation...');
    const afterCreateResponse = await makeAuthenticatedRequest('GET', '/customers', null, authToken);
    console.log('✅ Customers retrieved after creation');
    console.log('🔄 Number of customers:', afterCreateResponse.data.length);
    
    const createdCustomer = afterCreateResponse.data.find(customer => customer.id === customerId);
    if (createdCustomer) {
      console.log('🔄 Found created customer in list:', {
        id: createdCustomer.id,
        firstName: createdCustomer.firstName,
        lastName: createdCustomer.lastName,
        notes: createdCustomer.notes
      });
    }

    // Test 5: Update customer with French notes
    console.log('\n✏️ Test 5: Updating customer with French notes...');
    const updateResponse = await makeAuthenticatedRequest('PUT', `/customers/${customerId}`, updateCustomerData, authToken);
    console.log('✅ Customer updated successfully');
    console.log('🔄 Updated customer:', {
      id: updateResponse.data.id,
      firstName: updateResponse.data.firstName,
      lastName: updateResponse.data.lastName,
      notes: updateResponse.data.notes,
      appointmentCount: updateResponse.data.appointmentCount
    });

    // Test 6: Get single customer after update
    console.log('\n👤 Test 6: Getting single customer after update...');
    const updatedSingleCustomerResponse = await makeAuthenticatedRequest('GET', `/customers/${customerId}`, null, authToken);
    console.log('✅ Updated single customer retrieved successfully');
    console.log('🔄 Updated single customer details:', {
      id: updatedSingleCustomerResponse.data.id,
      firstName: updatedSingleCustomerResponse.data.firstName,
      lastName: updatedSingleCustomerResponse.data.lastName,
      notes: updatedSingleCustomerResponse.data.notes
    });

    // Test 7: Final verification - get all customers again
    console.log('\n🔄 Test 7: Final customer verification...');
    const finalResponse = await makeAuthenticatedRequest('GET', '/customers', null, authToken);
    console.log('✅ Final customer verification successful');
    console.log('🔄 Final customer state:', {
      totalCustomers: finalResponse.data.length,
      updatedCustomer: finalResponse.data.find(c => c.id === customerId)
    });

    console.log('\n🎉 All translation tests completed successfully!');
    console.log('\n📊 Test Summary:');
    console.log('- ✅ Initial customer retrieval');
    console.log('- ✅ Customer creation with Arabic notes');
    console.log('- ✅ Translation storage during creation');
    console.log('- ✅ Single customer retrieval with translations');
    console.log('- ✅ Translated content retrieval (all customers)');
    console.log('- ✅ Customer update with French notes');
    console.log('- ✅ Translation storage during update');
    console.log('- ✅ Single customer retrieval after update');
    console.log('- ✅ Final customer verification');

    return customerId;

  } catch (error) {
    console.error('❌ Translation test failed:', error.message);
    throw error;
  }
}

// Main execution
async function main() {
  console.log('🚀 Starting Provider Customers Translation API Tests');
  console.log('🌐 Base URL:', BASE_URL);
  
  // Note: You'll need to provide a valid authentication token
  const authToken = process.env.PROVIDER_AUTH_TOKEN;
  
  if (!authToken) {
    console.error('❌ Error: PROVIDER_AUTH_TOKEN environment variable is required');
    console.log('💡 Usage: PROVIDER_AUTH_TOKEN=your_token_here node test-customers-translation.js');
    process.exit(1);
  }

  try {
    await testCustomerTranslation(authToken);
    console.log('\n✅ All tests passed successfully!');
  } catch (error) {
    console.error('\n❌ Tests failed:', error.message);
    process.exit(1);
  }
}

// Run the tests
if (require.main === module) {
  main();
}

module.exports = { testCustomerTranslation };
