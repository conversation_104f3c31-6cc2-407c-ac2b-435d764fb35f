/**
 * Test script for Advertisement API endpoints
 * This script tests the basic functionality of the Advertisement feature
 */

const BASE_URL = 'http://localhost:3001'; // Adjust if your server runs on a different port

// Test data
const testAdvertisement = {
  title: 'Test Wall Painting Service',
  subtitle: 'Make your wall stylish',
  description: 'Professional wall painting service with high-quality materials and expert craftsmanship.',
  callToActionText: 'Book Now',
  callToActionLink: 'https://example.com/book',
  isExternal: true,
  isActive: true,
  sortOrder: 1
};

// Helper function to make HTTP requests
async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });
    
    const data = await response.json();
    return { status: response.status, data };
  } catch (error) {
    console.error('Request failed:', error);
    return { status: 500, error: error.message };
  }
}

// Test functions
async function testPublicAdvertisementsAPI() {
  console.log('\n🧪 Testing Public Advertisements API...');
  
  const result = await makeRequest(`${BASE_URL}/api/public/advertisements`);
  
  if (result.status === 200) {
    console.log('✅ Public API works!');
    console.log(`📊 Found ${result.data.data.advertisements.length} active advertisements`);
    
    if (result.data.data.advertisements.length > 0) {
      const ad = result.data.data.advertisements[0];
      console.log(`📝 Sample advertisement: "${ad.title}"`);
      console.log(`🔗 Call to action: "${ad.callToActionText}" -> ${ad.callToActionLink}`);
    }
    
    return true;
  } else {
    console.log('❌ Public API failed:', result.data || result.error);
    return false;
  }
}

async function testAdminLogin() {
  console.log('\n🔐 Testing Admin Login...');
  
  // This would need actual admin credentials
  // For now, we'll just test if the endpoint exists
  const result = await makeRequest(`${BASE_URL}/api/auth/admin/login`, {
    method: 'POST',
    body: JSON.stringify({
      email: '<EMAIL>',
      password: 'testpassword'
    })
  });
  
  if (result.status === 401 || result.status === 400) {
    console.log('✅ Admin login endpoint exists (returned expected auth error)');
    return true;
  } else if (result.status === 200) {
    console.log('✅ Admin login successful!');
    return result.data.sessionId;
  } else {
    console.log('❌ Admin login endpoint issue:', result.data || result.error);
    return false;
  }
}

async function testAdminAdvertisementsAPI(sessionId = null) {
  console.log('\n🛠️ Testing Admin Advertisements API...');
  
  const headers = sessionId ? { 'Authorization': `Bearer ${sessionId}` } : {};
  
  const result = await makeRequest(`${BASE_URL}/api/auth/admin/advertisements`, {
    headers
  });
  
  if (result.status === 401) {
    console.log('✅ Admin API properly requires authentication');
    return true;
  } else if (result.status === 200) {
    console.log('✅ Admin API works!');
    console.log(`📊 Found ${result.data.data.advertisements.length} total advertisements`);
    return true;
  } else {
    console.log('❌ Admin API failed:', result.data || result.error);
    return false;
  }
}

async function testDatabaseSchema() {
  console.log('\n🗄️ Testing Database Schema...');
  
  // Test if we can query the database structure
  // This is a basic test to see if our schema is properly set up
  try {
    // We'll test this by trying to access the public API
    // If the schema is wrong, the API would fail
    const result = await testPublicAdvertisementsAPI();
    if (result) {
      console.log('✅ Database schema appears to be working correctly');
      return true;
    } else {
      console.log('❌ Database schema might have issues');
      return false;
    }
  } catch (error) {
    console.log('❌ Database schema test failed:', error.message);
    return false;
  }
}

// Main test runner
async function runTests() {
  console.log('🚀 Starting Advertisement Feature Tests...');
  console.log('=' .repeat(50));
  
  const results = {
    publicAPI: false,
    adminLogin: false,
    adminAPI: false,
    database: false
  };
  
  // Test database schema
  results.database = await testDatabaseSchema();
  
  // Test public API
  results.publicAPI = await testPublicAdvertisementsAPI();
  
  // Test admin login
  const sessionId = await testAdminLogin();
  results.adminLogin = !!sessionId;
  
  // Test admin API
  results.adminAPI = await testAdminAdvertisementsAPI(sessionId);
  
  // Summary
  console.log('\n📋 Test Results Summary:');
  console.log('=' .repeat(50));
  console.log(`Database Schema: ${results.database ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Public API: ${results.publicAPI ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Admin Login: ${results.adminLogin ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Admin API: ${results.adminAPI ? '✅ PASS' : '❌ FAIL'}`);
  
  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  console.log(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! Advertisement feature is ready to use.');
  } else {
    console.log('⚠️ Some tests failed. Please check the implementation.');
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  runTests,
  testPublicAdvertisementsAPI,
  testAdminLogin,
  testAdminAdvertisementsAPI,
  testDatabaseSchema
};
