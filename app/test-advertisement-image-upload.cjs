#!/usr/bin/env node

/**
 * Test script for Advertisement Image Upload APIs
 * 
 * This script tests the new image upload endpoints:
 * - POST /api/auth/admin/advertisements/:id/background-image
 * - POST /api/auth/admin/advertisements/:id/png-image
 * 
 * Usage: node test-advertisement-image-upload.js
 */

const BASE_URL = process.env.API_BASE_URL || 'http://localhost:3001';

async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });
    
    const data = await response.json().catch(() => null);
    
    return {
      status: response.status,
      data,
      ok: response.ok
    };
  } catch (error) {
    return {
      status: 0,
      error: error.message,
      ok: false
    };
  }
}

async function testServerConnectivity() {
  console.log('🔗 Testing server connectivity...');
  
  const result = await makeRequest(BASE_URL);
  
  if (result.status === 0) {
    console.log('❌ Cannot connect to server. Make sure the server is running.');
    return false;
  }
  
  console.log(`✅ Server is responding (status: ${result.status})`);
  return true;
}

async function testAdminLogin() {
  console.log('\n🔐 Testing Admin Login...');
  
  // Try with the known admin credentials
  const result = await makeRequest(`${BASE_URL}/api/auth/admin/login`, {
    method: 'POST',
    body: JSON.stringify({
      email: '<EMAIL>',
      password: 'password123' // This should be the actual admin password
    })
  });
  
  if (result.status === 200 && result.data.success) {
    console.log('✅ Admin login successful!');
    return result.data.sessionId || result.data.token;
  } else if (result.status === 401 || result.status === 400) {
    console.log('⚠️ Admin login failed (check credentials)');
    console.log('Response:', result.data);
    return null;
  } else {
    console.log('❌ Admin login endpoint issue:', result.data || result.error);
    return null;
  }
}

async function createTestAdvertisement(authToken) {
  console.log('\n📝 Creating test advertisement...');
  
  const headers = authToken ? { 'Authorization': `Bearer ${authToken}` } : {};
  
  const testAd = {
    title: 'Test Advertisement for Image Upload',
    subtitle: 'Testing image upload functionality',
    description: 'This is a test advertisement created for testing image upload APIs',
    callToActionText: 'Test Now',
    callToActionLink: 'https://example.com/test',
    isActive: true,
    sortOrder: 999
  };
  
  const result = await makeRequest(`${BASE_URL}/api/auth/admin/advertisements`, {
    method: 'POST',
    headers,
    body: JSON.stringify(testAd)
  });
  
  if (result.status === 201 && result.data.success) {
    console.log(`✅ Test advertisement created with ID: ${result.data.data.id}`);
    return result.data.data.id;
  } else {
    console.log('❌ Failed to create test advertisement:', result.data || result.error);
    return null;
  }
}

async function testBackgroundImageUpload(authToken, advertisementId) {
  console.log('\n🖼️ Testing background image upload...');
  
  const headers = authToken ? { 'Authorization': `Bearer ${authToken}` } : {};
  
  const uploadData = {
    fileType: 'image/jpeg',
    fileName: 'test-background.jpg'
  };
  
  const result = await makeRequest(`${BASE_URL}/api/auth/admin/advertisements/${advertisementId}/background-image`, {
    method: 'POST',
    headers,
    body: JSON.stringify(uploadData)
  });
  
  if (result.status === 200 && result.data.success) {
    console.log('✅ Background image upload URL generated successfully');
    console.log(`📁 File ID: ${result.data.data.file.id}`);
    console.log(`🔗 Upload URL: ${result.data.data.uploadUrl.substring(0, 50)}...`);
    return true;
  } else {
    console.log('❌ Background image upload failed:', result.data || result.error);
    return false;
  }
}

async function testPngImageUpload(authToken, advertisementId) {
  console.log('\n🖼️ Testing PNG image upload...');
  
  const headers = authToken ? { 'Authorization': `Bearer ${authToken}` } : {};
  
  const uploadData = {
    fileType: 'image/png',
    fileName: 'test-logo.png'
  };
  
  const result = await makeRequest(`${BASE_URL}/api/auth/admin/advertisements/${advertisementId}/png-image`, {
    method: 'POST',
    headers,
    body: JSON.stringify(uploadData)
  });
  
  if (result.status === 200 && result.data.success) {
    console.log('✅ PNG image upload URL generated successfully');
    console.log(`📁 File ID: ${result.data.data.file.id}`);
    console.log(`🔗 Upload URL: ${result.data.data.uploadUrl.substring(0, 50)}...`);
    return true;
  } else {
    console.log('❌ PNG image upload failed:', result.data || result.error);
    return false;
  }
}

async function testInvalidAdvertisementId(authToken) {
  console.log('\n🚫 Testing invalid advertisement ID...');
  
  const headers = authToken ? { 'Authorization': `Bearer ${authToken}` } : {};
  
  const uploadData = {
    fileType: 'image/jpeg',
    fileName: 'test.jpg'
  };
  
  const result = await makeRequest(`${BASE_URL}/api/auth/admin/advertisements/99999/background-image`, {
    method: 'POST',
    headers,
    body: JSON.stringify(uploadData)
  });
  
  if (result.status === 404) {
    console.log('✅ Correctly returns 404 for non-existent advertisement');
    return true;
  } else {
    console.log('❌ Should return 404 for non-existent advertisement:', result.data || result.error);
    return false;
  }
}

async function testInvalidFileType(authToken, advertisementId) {
  console.log('\n🚫 Testing invalid file type...');
  
  const headers = authToken ? { 'Authorization': `Bearer ${authToken}` } : {};
  
  const uploadData = {
    fileType: 'application/pdf', // Invalid file type
    fileName: 'test.pdf'
  };
  
  const result = await makeRequest(`${BASE_URL}/api/auth/admin/advertisements/${advertisementId}/background-image`, {
    method: 'POST',
    headers,
    body: JSON.stringify(uploadData)
  });
  
  if (result.status === 400) {
    console.log('✅ Correctly rejects invalid file type');
    return true;
  } else {
    console.log('❌ Should reject invalid file type:', result.data || result.error);
    return false;
  }
}

async function cleanupTestAdvertisement(authToken, advertisementId) {
  console.log('\n🧹 Cleaning up test advertisement...');
  
  const headers = authToken ? { 'Authorization': `Bearer ${authToken}` } : {};
  
  const result = await makeRequest(`${BASE_URL}/api/auth/admin/advertisements/${advertisementId}`, {
    method: 'DELETE',
    headers
  });
  
  if (result.status === 200) {
    console.log('✅ Test advertisement cleaned up successfully');
    return true;
  } else {
    console.log('⚠️ Failed to cleanup test advertisement (this is okay)');
    return false;
  }
}

async function runTests() {
  console.log('🧪 Advertisement Image Upload API Tests');
  console.log('=' .repeat(50));
  
  const results = {};
  
  // Test server connectivity
  results.connectivity = await testServerConnectivity();
  if (!results.connectivity) {
    console.log('\n❌ Cannot proceed without server connectivity');
    return;
  }
  
  // Test admin login
  const authToken = await testAdminLogin();
  results.adminLogin = !!authToken;
  
  if (!authToken) {
    console.log('\n⚠️ Cannot test upload APIs without admin authentication');
    console.log('Please ensure admin credentials are correct');
    return;
  }
  
  // Create test advertisement
  const advertisementId = await createTestAdvertisement(authToken);
  results.createAd = !!advertisementId;
  
  if (!advertisementId) {
    console.log('\n❌ Cannot test upload APIs without test advertisement');
    return;
  }
  
  // Test upload APIs
  results.backgroundUpload = await testBackgroundImageUpload(authToken, advertisementId);
  results.pngUpload = await testPngImageUpload(authToken, advertisementId);
  
  // Test error cases
  results.invalidId = await testInvalidAdvertisementId(authToken);
  results.invalidFileType = await testInvalidFileType(authToken, advertisementId);
  
  // Cleanup
  results.cleanup = await cleanupTestAdvertisement(authToken, advertisementId);
  
  // Summary
  console.log('\n📋 Test Results Summary:');
  console.log('=' .repeat(50));
  console.log(`Server Connectivity: ${results.connectivity ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Admin Login: ${results.adminLogin ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Create Advertisement: ${results.createAd ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Background Image Upload: ${results.backgroundUpload ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`PNG Image Upload: ${results.pngUpload ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Invalid ID Handling: ${results.invalidId ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Invalid File Type: ${results.invalidFileType ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Cleanup: ${results.cleanup ? '✅ PASS' : '⚠️ SKIP'}`);
  
  const passCount = Object.values(results).filter(Boolean).length;
  const totalCount = Object.keys(results).length;
  
  console.log('\n🎯 Overall Result:');
  console.log(`${passCount}/${totalCount} tests passed`);
  
  if (passCount === totalCount) {
    console.log('🎉 All tests passed! Image upload APIs are working correctly.');
  } else {
    console.log('⚠️ Some tests failed. Please check the implementation.');
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { runTests };
