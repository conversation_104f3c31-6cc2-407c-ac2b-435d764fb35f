/**
 * Test script to verify the appointment status API handles case-insensitive status values
 */

const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:3001'; // Wasp dev server URL
const API_ENDPOINT = '/api/auth/providers/appointments';

// Test cases for status parameter
const statusTestCases = [
  'pending',
  'PENDING', 
  'Pending',
  'confirmed',
  'CONFIRMED',
  'inprogress',  // This was causing the original error
  'inProgress',
  'INPROGRESS',
  'InProgress',
  'completed',
  'COMPLETED',
  'canceled',
  'cancelled',
  'CANCELLED',
  'noshow',
  'NOSHOW'
];

async function testStatusParameter() {
  console.log('Testing appointment status API with case-insensitive status values...\n');
  
  let passed = 0;
  let failed = 0;

  for (const status of statusTestCases) {
    try {
      console.log(`Testing status: "${status}"`);
      
      const response = await axios.get(`${BASE_URL}${API_ENDPOINT}`, {
        params: {
          page: 1,
          pageSize: 20,
          status: status
        },
        headers: {
          'Authorization': 'Bearer test-token', // You'll need a valid token
          'Content-Type': 'application/json'
        },
        timeout: 5000
      });

      if (response.status === 200) {
        console.log(`✅ Status "${status}" accepted (HTTP ${response.status})`);
        passed++;
      } else {
        console.log(`⚠️  Status "${status}" returned HTTP ${response.status}`);
        failed++;
      }
    } catch (error) {
      if (error.response) {
        if (error.response.status === 401) {
          console.log(`🔒 Status "${status}" - Authentication required (expected for this test)`);
          passed++; // This is expected since we don't have a real token
        } else if (error.response.status === 400 && error.response.data?.message?.includes('Validation failed')) {
          console.log(`❌ Status "${status}" - Validation error: ${error.response.data.message}`);
          failed++;
        } else {
          console.log(`⚠️  Status "${status}" - HTTP ${error.response.status}: ${error.response.data?.message || 'Unknown error'}`);
          failed++;
        }
      } else {
        console.log(`❌ Status "${status}" - Network error: ${error.message}`);
        failed++;
      }
    }
    
    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  console.log(`\nResults: ${passed} passed, ${failed} failed`);
  
  if (failed === 0) {
    console.log('🎉 All status values handled correctly!');
  } else {
    console.log('💥 Some status values failed validation!');
  }
}

// Run the test
testStatusParameter().catch(console.error);
