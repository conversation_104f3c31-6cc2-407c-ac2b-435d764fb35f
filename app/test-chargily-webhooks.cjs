#!/usr/bin/env node

/**
 * Chargily Webhook Testing Script
 * 
 * This script helps test Chargily webhook integration by:
 * 1. Verifying webhook signature validation
 * 2. Testing webhook endpoint accessibility
 * 3. Simulating webhook events
 * 
 * Usage: node test-chargily-webhooks.js [ngrok-url]
 */

require('dotenv').config({ path: '.env.server' });
const crypto = require('crypto');

// Test webhook payloads
const TEST_WEBHOOKS = {
  'checkout.paid': {
    id: 'evt_test_checkout_paid',
    type: 'checkout.paid',
    data: {
      id: 'checkout_test_123',
      status: 'paid',
      amount: 2000,
      currency: 'dzd',
      payment_method: 'edahabia',
      customer_id: 'customer_test_123',
      metadata: {
        userId: 'user_test_123',
        planId: 'hobby',
        planType: 'subscription',
        creditsAmount: '200'
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  },
  'checkout.failed': {
    id: 'evt_test_checkout_failed',
    type: 'checkout.failed',
    data: {
      id: 'checkout_test_456',
      status: 'failed',
      amount: 2000,
      currency: 'dzd',
      payment_method: 'cib',
      customer_id: 'customer_test_123',
      failure_reason: 'insufficient_funds',
      metadata: {
        userId: 'user_test_123',
        planId: 'hobby'
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  },
  'subscription.created': {
    id: 'evt_test_subscription_created',
    type: 'subscription.created',
    data: {
      id: 'subscription_test_789',
      status: 'active',
      customer_id: 'customer_test_123',
      plan_id: 'price_test_hobby',
      current_period_start: new Date().toISOString(),
      current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      metadata: {
        userId: 'user_test_123',
        planId: 'hobby',
        queues: '3'
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  }
};

/**
 * Generate webhook signature
 */
function generateWebhookSignature(payload, secret) {
  const timestamp = Math.floor(Date.now() / 1000);
  const signedPayload = `${timestamp}.${payload}`;
  const signature = crypto
    .createHmac('sha256', secret)
    .update(signedPayload, 'utf8')
    .digest('hex');
  
  return {
    timestamp,
    signature: `t=${timestamp},v1=${signature}`
  };
}

/**
 * Test webhook signature validation
 */
function testSignatureValidation() {
  console.log('🔐 Testing webhook signature validation...\n');
  
  const secret = process.env.CHARGILY_WEBHOOK_SECRET;
  if (!secret || secret.includes('placeholder')) {
    console.error('❌ CHARGILY_WEBHOOK_SECRET not configured');
    return false;
  }
  
  const testPayload = JSON.stringify(TEST_WEBHOOKS['checkout.paid']);
  const { timestamp, signature } = generateWebhookSignature(testPayload, secret);
  
  console.log('Test payload:', testPayload.substring(0, 100) + '...');
  console.log('Generated signature:', signature);
  console.log('Timestamp:', timestamp);
  
  // Verify signature
  const [t, v1] = signature.split(',');
  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(`${timestamp}.${testPayload}`, 'utf8')
    .digest('hex');
  
  const providedSignature = v1.split('=')[1];
  const isValid = crypto.timingSafeEqual(
    Buffer.from(expectedSignature, 'hex'),
    Buffer.from(providedSignature, 'hex')
  );
  
  if (isValid) {
    console.log('✅ Signature validation working correctly\n');
    return true;
  } else {
    console.error('❌ Signature validation failed\n');
    return false;
  }
}

/**
 * Test webhook endpoint accessibility
 */
async function testWebhookEndpoint(baseUrl) {
  console.log('🌐 Testing webhook endpoint accessibility...\n');
  
  const webhookUrl = `${baseUrl}/api/payment/chargily/webhook`;
  console.log('Testing URL:', webhookUrl);
  
  try {
    const fetch = (await import('node-fetch')).default;
    
    // Test with a simple GET request first
    const getResponse = await fetch(webhookUrl, {
      method: 'GET',
      timeout: 5000
    });
    
    console.log('GET response status:', getResponse.status);
    
    if (getResponse.status === 405) {
      console.log('✅ Endpoint exists (Method Not Allowed is expected for GET)');
    } else if (getResponse.status === 404) {
      console.error('❌ Webhook endpoint not found (404)');
      return false;
    } else {
      console.log('⚠️  Unexpected response, but endpoint seems accessible');
    }
    
    return true;
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.error('❌ Connection refused - is your application running?');
    } else if (error.code === 'ENOTFOUND') {
      console.error('❌ Host not found - check your ngrok URL');
    } else {
      console.error('❌ Network error:', error.message);
    }
    return false;
  }
}

/**
 * Send test webhook
 */
async function sendTestWebhook(baseUrl, eventType) {
  console.log(`📤 Sending test webhook: ${eventType}...\n`);
  
  const webhookUrl = `${baseUrl}/api/payment/chargily/webhook`;
  const payload = JSON.stringify(TEST_WEBHOOKS[eventType]);
  const secret = process.env.CHARGILY_WEBHOOK_SECRET;
  
  const { signature } = generateWebhookSignature(payload, secret);
  
  try {
    const fetch = (await import('node-fetch')).default;
    
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Chargily-Signature': signature,
        'User-Agent': 'Chargily-Webhook-Test/1.0'
      },
      body: payload,
      timeout: 10000
    });
    
    const responseText = await response.text();
    
    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    console.log('Response body:', responseText.substring(0, 500));
    
    if (response.status === 200) {
      console.log(`✅ ${eventType} webhook processed successfully\n`);
      return true;
    } else {
      console.error(`❌ ${eventType} webhook failed with status ${response.status}\n`);
      return false;
    }
    
  } catch (error) {
    console.error(`❌ Error sending ${eventType} webhook:`, error.message);
    return false;
  }
}

/**
 * Validate ngrok URL
 */
function validateNgrokUrl(url) {
  if (!url) {
    console.error('❌ No ngrok URL provided');
    console.error('Usage: node test-chargily-webhooks.js https://abc123.ngrok.io');
    return false;
  }
  
  if (!url.startsWith('https://') || !url.includes('ngrok.io')) {
    console.error('❌ Invalid ngrok URL format');
    console.error('Expected format: https://abc123.ngrok.io');
    return false;
  }
  
  return true;
}

/**
 * Check if application is running
 */
async function checkApplicationStatus() {
  console.log('🔍 Checking if application is running...\n');
  
  try {
    const fetch = (await import('node-fetch')).default;
    const response = await fetch('http://localhost:3001/health', {
      timeout: 3000
    });
    
    if (response.ok) {
      console.log('✅ Application is running on port 3001\n');
      return true;
    }
  } catch (error) {
    // Try alternative health check
    try {
      const response = await fetch('http://localhost:3001', {
        timeout: 3000
      });
      console.log('✅ Application is running on port 3001\n');
      return true;
    } catch (error2) {
      console.error('❌ Application not running on port 3001');
      console.error('Please start your application with: wasp start\n');
      return false;
    }
  }
}

/**
 * Main test function
 */
async function runWebhookTests() {
  const ngrokUrl = process.argv[2];
  
  console.log('🚀 Starting Chargily Webhook Testing\n');
  console.log('=' .repeat(60));
  
  // Validate inputs
  if (!validateNgrokUrl(ngrokUrl)) {
    process.exit(1);
  }
  
  // Check application status
  if (!(await checkApplicationStatus())) {
    process.exit(1);
  }
  
  let allTestsPassed = true;
  
  // Test signature validation
  if (!testSignatureValidation()) {
    allTestsPassed = false;
  }
  
  // Test endpoint accessibility
  if (!(await testWebhookEndpoint(ngrokUrl))) {
    allTestsPassed = false;
  }
  
  // Test webhook events
  for (const eventType of Object.keys(TEST_WEBHOOKS)) {
    if (!(await sendTestWebhook(ngrokUrl, eventType))) {
      allTestsPassed = false;
    }
    
    // Wait between requests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // Final results
  console.log('=' .repeat(60));
  if (allTestsPassed) {
    console.log('🎉 All webhook tests passed!');
    console.log('\nWebhook integration is working correctly.');
    console.log('\nNext steps:');
    console.log('1. Update Chargily dashboard webhook URL to:', `${ngrokUrl}/api/payment/chargily/webhook`);
    console.log('2. Test real payments in your application');
    console.log('3. Monitor webhook delivery in Chargily dashboard');
  } else {
    console.log('❌ Some webhook tests failed.');
    console.log('\nTroubleshooting:');
    console.log('1. Ensure your application is running (wasp start)');
    console.log('2. Check ngrok is forwarding to correct port (3001)');
    console.log('3. Verify webhook secret in .env.server');
    console.log('4. Check application logs for errors');
  }
  
  process.exit(allTestsPassed ? 0 : 1);
}

// Install node-fetch if not available
async function ensureFetch() {
  try {
    await import('node-fetch');
  } catch (error) {
    console.error('❌ node-fetch not found. Installing...');
    const { execSync } = require('child_process');
    try {
      execSync('npm install node-fetch@2', { stdio: 'inherit' });
      console.log('✅ node-fetch installed successfully');
    } catch (installError) {
      console.error('❌ Failed to install node-fetch:', installError.message);
      console.error('Please install manually: npm install node-fetch@2');
      process.exit(1);
    }
  }
}

// Run tests if script is executed directly
if (require.main === module) {
  ensureFetch().then(() => {
    runWebhookTests().catch(error => {
      console.error('💥 Fatal error:', error.message);
      process.exit(1);
    });
  });
}

module.exports = { runWebhookTests, testSignatureValidation, sendTestWebhook };
