/**
 * Test script for Provider Services Translation APIs
 * Tests the translation functionality for service creation, updates, and retrieval
 */

const axios = require('axios');

// Configuration
const BASE_URL = 'https://dalti-test.adscloud.org:8443';
const API_BASE = `${BASE_URL}/api/auth/providers`;

// Test data
const testServiceData = {
  title: "خدمة طبية متخصصة", // Arabic: Specialized Medical Service
  description: "خدمة طبية عالية الجودة مع أحدث التقنيات", // Arabic: High quality medical service with latest technologies
  duration: 45,
  pointsRequirements: 2,
  color: "#FF5733",
  acceptOnline: true,
  acceptNew: true,
  notificationOn: true
};

const updateServiceData = {
  title: "Service Médical Spécialisé", // French: Specialized Medical Service
  description: "Service médical de haute qualité avec les dernières technologies", // French: High quality medical service with latest technologies
  duration: 60,
  pointsRequirements: 3
};

// Helper function to make authenticated requests
async function makeAuthenticatedRequest(method, endpoint, data = null, token) {
  const config = {
    method,
    url: `${API_BASE}${endpoint}`,
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  };

  if (data) {
    config.data = data;
  }

  try {
    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`Error in ${method} ${endpoint}:`, error.response?.data || error.message);
    throw error;
  }
}

// Test functions
async function testServiceTranslation(authToken) {
  console.log('\n🧪 Testing Provider Services Translation APIs...\n');

  try {
    // Test 1: Create service with Arabic text
    console.log('📝 Test 1: Creating service with Arabic text...');
    const createResponse = await makeAuthenticatedRequest('POST', '/services', testServiceData, authToken);
    console.log('✅ Service created successfully');
    console.log('🔧 Created service:', {
      id: createResponse.data.id,
      title: createResponse.data.title,
      description: createResponse.data.description,
      duration: createResponse.data.duration,
      pointsRequirements: createResponse.data.pointsRequirements
    });

    const serviceId = createResponse.data.id;

    // Test 2: Get all services (should return translated content based on user's preferred language)
    console.log('\n📋 Test 2: Getting all services...');
    const getAllResponse = await makeAuthenticatedRequest('GET', '/services', null, authToken);
    console.log('✅ Services retrieved successfully');
    console.log('🔧 Number of services:', getAllResponse.data.length);
    
    const createdService = getAllResponse.data.find(service => service.id === serviceId);
    if (createdService) {
      console.log('🔧 Found created service in list:', {
        id: createdService.id,
        title: createdService.title,
        description: createdService.description,
        duration: createdService.duration
      });
    }

    // Test 3: Get single service by ID
    console.log('\n🔍 Test 3: Getting single service by ID...');
    const getSingleResponse = await makeAuthenticatedRequest('GET', `/services/${serviceId}`, null, authToken);
    console.log('✅ Single service retrieved successfully');
    console.log('🔧 Service details:', {
      id: getSingleResponse.data.id,
      title: getSingleResponse.data.title,
      description: getSingleResponse.data.description,
      duration: getSingleResponse.data.duration,
      pointsRequirements: getSingleResponse.data.pointsRequirements
    });

    // Test 4: Update service with French text
    console.log('\n✏️ Test 4: Updating service with French text...');
    const updateResponse = await makeAuthenticatedRequest('PUT', `/services/${serviceId}`, updateServiceData, authToken);
    console.log('✅ Service updated successfully');
    console.log('🔧 Updated service:', {
      id: updateResponse.data.id,
      title: updateResponse.data.title,
      description: updateResponse.data.description,
      duration: updateResponse.data.duration,
      pointsRequirements: updateResponse.data.pointsRequirements
    });

    // Test 5: Verify translations were saved by getting service again
    console.log('\n🔄 Test 5: Verifying translations after update...');
    const verifyResponse = await makeAuthenticatedRequest('GET', `/services/${serviceId}`, null, authToken);
    console.log('✅ Service verification successful');
    console.log('🔧 Final service state:', {
      id: verifyResponse.data.id,
      title: verifyResponse.data.title,
      description: verifyResponse.data.description,
      duration: verifyResponse.data.duration,
      pointsRequirements: verifyResponse.data.pointsRequirements
    });

    console.log('\n🎉 All translation tests completed successfully!');
    console.log('\n📊 Test Summary:');
    console.log('- ✅ Service creation with Arabic text');
    console.log('- ✅ Translation storage during creation');
    console.log('- ✅ Translated content retrieval (all services)');
    console.log('- ✅ Translated content retrieval (single service)');
    console.log('- ✅ Service update with French text');
    console.log('- ✅ Translation storage during update');
    console.log('- ✅ Translation verification after update');

    return serviceId;

  } catch (error) {
    console.error('❌ Translation test failed:', error.message);
    throw error;
  }
}

// Main execution
async function main() {
  console.log('🚀 Starting Provider Services Translation API Tests');
  console.log('🌐 Base URL:', BASE_URL);
  
  // Note: You'll need to provide a valid authentication token
  const authToken = process.env.PROVIDER_AUTH_TOKEN;
  
  if (!authToken) {
    console.error('❌ Error: PROVIDER_AUTH_TOKEN environment variable is required');
    console.log('💡 Usage: PROVIDER_AUTH_TOKEN=your_token_here node test-services-translation.js');
    process.exit(1);
  }

  try {
    await testServiceTranslation(authToken);
    console.log('\n✅ All tests passed successfully!');
  } catch (error) {
    console.error('\n❌ Tests failed:', error.message);
    process.exit(1);
  }
}

// Run the tests
if (require.main === module) {
  main();
}

module.exports = { testServiceTranslation };
