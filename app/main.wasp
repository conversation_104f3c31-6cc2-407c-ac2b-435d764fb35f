app OpenSaaS {
  wasp: {
    version: "^0.16.0"
  },

  title: "<PERSON><PERSON>",

  head: [
    "<meta charset='utf-8' />",
    "<meta name='description' content='Your apps main description and features.' />",
    "<meta name='author' content='Your (App) Name' />",
    "<meta name='keywords' content='saas, solution, product, app, service' />",
    
    "<meta property='og:type' content='website' />",
    "<meta property='og:title' content='Your Open SaaS App' />",
    "<meta property='og:site_name' content='Your Open SaaS App' />",
    "<meta property='og:url' content='https://your-saas-app.com' />",
    "<meta property='og:description' content='Your apps main description and features.' />",
    "<meta property='og:image' content='https://your-saas-app.com/public-banner.webp' />",
    "<meta name='twitter:image' content='https://your-saas-app.com/public-banner.webp' />",
    "<meta name='twitter:image:width' content='800' />",
    "<meta name='twitter:image:height' content='400' />",
    "<meta name='twitter:card' content='summary_large_image' />",
    // TODO: You can put your Plausible analytics scripts below (https://docs.opensaas.sh/guides/analytics/):
    // NOTE: Plausible does not use Cookies, so you can simply add the scripts here.
    // Google, on the other hand, does, so you must instead add the script dynamically
    // via the Cookie Consent component after the user clicks the "Accept" cookies button.
    "<script defer data-domain='<your-site-id>' src='https://plausible.io/js/script.js'></script>",  // for production
    "<script defer data-domain='<your-site-id>' src='https://plausible.io/js/script.local.js'></script>",  // for development
  ],

  // 🔐 Auth out of the box! https://wasp.sh/docs/auth/overview
  auth: {
    userEntity: User,
    methods: {
      // NOTE: If you decide to not use email auth, make sure to also delete the related routes and pages below.
      //   (RequestPasswordReset(Route|Page), PasswordReset(Route|Page), EmailVerification(Route|Page))
      //usernameAndPassword: {},
      email: {
        fromField: {
          name: "Dalti",
          email: "<EMAIL>"
        },
        emailVerification: {
          clientRoute: EmailVerificationRoute,
          getEmailContentFn: import { getVerificationEmailContent } from "@src/auth/email-and-pass/emails",
        },
        passwordReset: {
          clientRoute: PasswordResetRoute,
          getEmailContentFn: import { getPasswordResetEmailContent } from "@src/auth/email-and-pass/emails",
        },
        userSignupFields: import { getEmailUserFields } from "@src/auth/userSignupFields",
      },
      // Uncomment to enable Google Auth (check https://wasp.sh/docs/auth/social-auth/google for setup instructions):
      // google: { // Guide for setting up Auth via Google
      //   userSignupFields: import { getGoogleUserFields } from "@src/auth/userSignupFields",
      //   configFn: import { getGoogleAuthConfig } from "@src/auth/userSignupFields",
      // },
      // Uncomment to enable GitHub Auth (check https://wasp.sh/docs/auth/social-auth/github for setup instructions):
      // gitHub: {
      //   userSignupFields: import { getGitHubUserFields } from "@src/auth/userSignupFields",
      //   configFn: import { getGitHubAuthConfig } from "@src/auth/userSignupFields",
      // },
      // Uncomment to enable Discord Auth (check https://wasp.sh/docs/auth/social-auth/discord for setup instructions):
      // discord: {
      //   userSignupFields: import { getDiscordUserFields } from "@src/auth/userSignupFields",
      //   configFn: import { getDiscordAuthConfig } from "@src/auth/userSignupFields"
      // }
    },
    onAuthFailedRedirectTo: "/login",
    onAuthSucceededRedirectTo: "/admin",
  },

  db: {
    // Run `wasp db seed` to seed the database with the seed functions below:
    seeds: [
      // Populates the database with a bunch of fake users to work with during development.
      import { seedMockUsers } from "@src/server/scripts/dbSeeds",
      import { clearAppointments } from "@src/server/scripts/dbSeeds",
      import { seedProviderCategories } from "@src/server/scripts/dbSeeds",
      import { seedAdditionalAppointmentsForCustomers } from "@src/server/scripts/dbSeeds", // Added new seed function
      import { seedAdminUser } from "@src/server/scripts/dbSeeds",
      import { seedSubscriptionPlans } from "@src/server/scripts/dbSeeds", // Added subscription plans seed
    ]
  },

  // in main.wasp file 
  client: {
    rootComponent: import App from "@src/client/Root",
  },

  emailSender: {
    provider: SMTP,
    defaultFrom: {
      name: "Dalti",
      email: "<EMAIL>"
    },
  },

  // ADDED: WebSocket configuration
  webSocket: {
    fn: import { webSocketFn } from "@src/server/webSocket",
    autoConnect: true, // Default is true, explicit for clarity
  },
}

// Action to create SProvider profile for the logged-in user
action createSProviderForUser {
  fn: import { createSProviderForUser } from "@src/auth/actions",
  entities: [SProvider, ProviderCategory, User], // User entity is implicitly available via context
  auth: true // Action requires user to be logged in
}

// --- Create Customer User Action Definition ---
action createCustomerUser {
  fn: import { createCustomerUser } from "@src/auth/actions",
  entities: [User], // Need User entity access
  auth: false // Publicly accessible for signup
}

action requestPhoneOtp {
  fn: import { requestPhoneOtp } from "@src/auth/actions",
  entities: [User],
  auth: false // Publicly accessible for OTP request
}

query checkUserExistence {
  fn: import { checkUserExistence } from "@src/auth/queries",
  entities: [User],
  auth: false // Publicly accessible
}

action verifyOtpAndRegister {
  fn: import { verifyOtpAndRegister } from "@src/auth/actions",
  entities: [User],
  auth: false // Publicly accessible for registration completion
}

action loginWithPhoneOrEmail {
  fn: import { loginWithPhoneOrEmail } from "@src/auth/actions",
  entities: [User], // Access Auth/AuthIdentity via User relationship
  auth: false // Publicly accessible for login attempt
}

// Action for requesting email OTP
action requestEmailOtp {
  fn: import { requestEmailOtp } from "@src/auth/actions",
  entities: [User],
  auth: false // Publicly accessible for OTP request
}

// Password reset actions
action requestPasswordResetOtp {
  fn: import { requestPasswordResetOtp } from "@src/auth/actions",
  entities: [User],
  auth: false // Publicly accessible for password reset request
}

action verifyPasswordResetOtp {
  fn: import { verifyPasswordResetOtp } from "@src/auth/actions",
  entities: [User],
  auth: false // Publicly accessible for OTP verification
}

action resetPassword {
  fn: import { resetPassword } from "@src/auth/actions",
  entities: [User],
  auth: false // Publicly accessible for password reset
}

// Action for requesting provider phone OTP
action requestProviderPhoneOtp {
  fn: import { requestProviderPhoneOtp } from "@src/auth/actions",
  entities: [User],
  auth: false // Publicly accessible for provider OTP request
}

// Action for requesting provider email OTP
action requestProviderEmailOtp {
  fn: import { requestProviderEmailOtp } from "@src/auth/actions",
  entities: [User],
  auth: false // Publicly accessible for provider OTP request
}

// --- Remove Customer Signup Action Definition ---
/*
action customerSignup {
  fn: import { customerSignup } from "@src/auth/actions",
  entities: [User], // User entity is needed for checks and creation
  auth: false // Signup should be publicly accessible
}
*/

route LandingPageRoute { path: "/", to: LandingPage }
page LandingPage {
  component: import LandingPage from "@src/landing-page/LandingPage"
}

//#region Auth Pages
route LoginRoute { path: "/login", to: LoginPage }
page LoginPage {
  component: import Login from "@src/auth/LoginPage"
}

route SignupRoute { path: "/signup", to: SignupPage }
page SignupPage {
  component: import { Signup } from "@src/auth/SignupPage"
}

route CustomerSignupRoute { path: "/signup-customer", to: CustomerSignupPage }
page CustomerSignupPage {
  component: import { CustomerSignupPage } from "@src/auth/CustomerSignupPage" // We will create this component next
}

route RequestPasswordResetRoute { path: "/request-password-reset", to: RequestPasswordResetPage }
page RequestPasswordResetPage {
  component: import { RequestPasswordResetPage } from "@src/auth/email-and-pass/RequestPasswordResetPage",
}

route PasswordResetRoute { path: "/password-reset", to: PasswordResetPage }
page PasswordResetPage {
  component: import { PasswordResetPage } from "@src/auth/email-and-pass/PasswordResetPage",
}

route EmailVerificationRoute { path: "/email-verification", to: EmailVerificationPage }
page EmailVerificationPage {
  component: import { EmailVerificationPage } from "@src/auth/email-and-pass/EmailVerificationPage",
}

//#endregion

//#region User
route AccountRoute { path: "/account", to: AccountPage }
page AccountPage {
  authRequired: true,
  component: import Account from "@src/user/AccountPage"
}

query getPaginatedUsers {
  fn: import { getPaginatedUsers } from "@src/user/operations",
  entities: [User]
}

action updateIsUserAdminById {
  fn: import { updateIsUserAdminById } from "@src/user/operations",
  entities: [User]
}

action giveFreePlanUser {
  fn: import { giveFreePlanUser } from "@src/user/operations",
  entities: [User]
}

action removeFreePlanUser {
  fn: import { removeFreePlanUser } from "@src/user/operations",
  entities: [User]
}
//#endregion

//#region Demo AI App
route DemoAppRoute { path: "/demo-app", to: DemoAppPage }
page DemoAppPage {
  authRequired: true,
  component: import DemoAppPage from "@src/demo-ai-app/DemoAppPage"
}

action generateGptResponse {
  fn: import { generateGptResponse } from "@src/demo-ai-app/operations",
  entities: [User, Task, GptResponse]
}

action createTask {
  fn: import { createTask } from "@src/demo-ai-app/operations",
  entities: [Task]
}

action deleteTask {
  fn: import { deleteTask } from "@src/demo-ai-app/operations",
  entities: [Task]
}

action updateTask {
  fn: import { updateTask } from "@src/demo-ai-app/operations",
  entities: [Task]
}

query getGptResponses {
  fn: import { getGptResponses } from "@src/demo-ai-app/operations",
  entities: [User, GptResponse]
}

query getAllTasksByUser {
  fn: import { getAllTasksByUser } from "@src/demo-ai-app/operations",
  entities: [Task]
}
//#endregion

//#region Payment
route PricingPageRoute { path: "/pricing", to: PricingPage }
page PricingPage {
  component: import PricingPage from "@src/payment/PricingPage"
}

route CheckoutRoute { path: "/checkout", to: CheckoutPage }
page CheckoutPage {
  authRequired: true,
  component: import Checkout from "@src/payment/CheckoutPage"
}

query getCustomerPortalUrl {
  fn: import { getCustomerPortalUrl } from  "@src/payment/operations",
  entities: [User]
}

action generateCheckoutSession {
  fn: import { generateCheckoutSession } from "@src/payment/operations",
  entities: [User]
}

api paymentsWebhook {
  fn: import { paymentsWebhook } from "@src/payment/webhook",
  entities: [User],
  middlewareConfigFn: import { paymentsMiddlewareConfigFn } from "@src/payment/webhook",
  httpRoute: (POST, "/payments-webhook")
}
//#endregion

//#region File Upload
route FileUploadRoute { path: "/file-upload", to: FileUploadPage }
page FileUploadPage {
  authRequired: true,
  component: import FileUpload from "@src/file-upload/FileUploadPage"
}

action createFile {
  fn: import { createFile } from "@src/file-upload/operations",
  entities: [User, File]
}

query getAllFilesByUser {
  fn: import { getAllFilesByUser } from "@src/file-upload/operations",
  entities: [User, File]
}

query getDownloadFileSignedURL {
  fn: import { getDownloadFileSignedURL } from "@src/file-upload/operations",
  entities: [User, File]
}
//#endregion

//#region Analytics
query getDailyStats {
  fn: import { getDailyStats } from "@src/analytics/operations",
  entities: [User, DailyStats]
}

job dailyStatsJob {
  executor: PgBoss,
  perform: {
    fn: import { calculateDailyStats } from "@src/analytics/stats"
  },
  schedule: {
    // cron: "0 * * * *" // every hour. useful in production
    cron: "* * * * *" // every minute. useful for debugging
  },
  entities: [User, DailyStats, Logs, PageViewSource]
}

// New Job: Auto-cancel past pending/confirmed appointments daily
job autoCancelPastAppointmentsJob {
  executor: PgBoss,
  perform: {
    fn: import { autoCancelPastAppointments } from "@src/server/jobs"
  },
  schedule: {
    // Runs once daily at midnight server time
    cron: "0 0 * * *"
    // cron: "* * * * *" // every minute. useful for debugging
  },
  // Only needs Appointment entity access for finding and updating
  entities: [Appointment] 
}
//#endregion

//#region Admin Dashboard
route AdminRoute { path: "/admin", to: AnalyticsDashboardPage }
page AnalyticsDashboardPage {
  authRequired: true,
  component: import AnalyticsDashboardPage from "@src/admin/dashboards/analytics/AnalyticsDashboardPage"
}

route AdminAppointmentsRoute { path: "/admin/appointments", to: AdminAppointmentsPage } 
page AdminAppointmentsPage {
  authRequired: true,
  component: import AdminAppointmentsPage from "@src/admin/dashboards/appointments/AppointmentsPage"
}

route AdminUsersRoute { path: "/admin/clients", to: AdminUsersPage }
page AdminUsersPage {
  authRequired: true,
  component: import AdminUsers from "@src/admin/dashboards/users/UsersDashboardPage"
}

route AdminCustomerProfileRoute { path: "/admin/customers/:customerId", to: AdminCustomerProfilePage }
page AdminCustomerProfilePage {
  authRequired: true,
  component: import AdminCustomerProfilePage from "@src/admin/dashboards/customers/CustomerProfilePage"
}

route AdminSettingsRoute { path: "/admin/settings", to: AdminSettingsPage }
page AdminSettingsPage {
  authRequired: true,
  component: import AdminSettings from "@src/admin/elements/settings/SettingsPage"
}

route AdminChartsRoute { path: "/admin/chart", to: AdminChartsPage }
page AdminChartsPage {
  authRequired: true,
  component: import AdminCharts from "@src/admin/elements/charts/ChartsPage"
}

route AdminFormElementsRoute { path: "/admin/forms/form-elements", to: AdminFormElementsPage }
page AdminFormElementsPage {
  authRequired: true,
  component: import AdminForms from "@src/admin/elements/forms/FormElementsPage"
}

route AdminFormLayoutsRoute { path: "/admin/forms/form-layouts", to: AdminFormLayoutsPage }
page AdminFormLayoutsPage {
  authRequired: true,
  component: import AdminForms from "@src/admin/elements/forms/FormLayoutsPage"
}

route AdminCalendarRoute { path: "/admin/calendar", to: AdminCalendarPage }
page AdminCalendarPage {
  authRequired: true,
  component: import AdminCalendar from "@src/admin/elements/calendar/CalendarPage"
}

route AdminUIAlertsRoute { path: "/admin/ui/alerts", to: AdminUIAlertsPage }
page AdminUIAlertsPage {
  authRequired: true,
  component: import AdminUI from "@src/admin/elements/ui-elements/AlertsPage"
}

route AdminUIButtonsRoute { path: "/admin/ui/buttons", to: AdminUIButtonsPage }
page AdminUIButtonsPage {
  authRequired: true,
  component: import AdminUI from "@src/admin/elements/ui-elements/ButtonsPage"
}

route AdminUserAccountRoute { path: "/admin/account", to: AdminUserAccountPage }
page AdminUserAccountPage {
  authRequired: true,
  component: import AdminUserAccountPage from "@src/admin/dashboards/Account/UserAccountPage"
}

// Route and Page for Categories Management
route AdminCategoriesRoute { path: "/admin/categories", to: AdminCategoriesPage }
page AdminCategoriesPage {
  authRequired: true,
  component: import AdminCategoriesPage from "@src/admin/elements/categories/CategoriesPage" // We will create this component
}

// Route and Page for Advertisements Management
route AdminAdvertisementsRoute { path: "/admin/advertisements", to: AdminAdvertisementsPage }
page AdminAdvertisementsPage {
  authRequired: true,
  component: import AdminAdvertisementsPage from "@src/admin/elements/advertisements/AdvertisementsPage"
}

// Route and Page for Services Management
route AdminServicesRoute { path: "/admin/services", to: AdminServicesPage }
page AdminServicesPage {
  authRequired: true,
  component: import AdminServicesPage from "@src/admin/elements/services/ServicesPage"
}

// ADDED: Route and Page for Queues Management
route AdminQueuesRoute { path: "/admin/queues", to: AdminQueuesPage }
page AdminQueuesPage {
  authRequired: true,
  component: import AdminQueuesPage from "@src/admin/elements/queues/QueuesPage"
}

route NotFoundRoute { path: "*", to: NotFoundPage }
page NotFoundPage {
  component: import { NotFoundPage } from "@src/client/components/NotFoundPage"
}
//#endregion

//#region Contact Form Messages
// TODO: 
// add functionality to allow users to send messages to admin
// and make them accessible via the admin dashboard
route AdminMessagesRoute { path: "/admin/messages", to: AdminMessagesPage }
page AdminMessagesPage {
  authRequired: true,
    component: import AdminMessagesPage from "@src/admin/elements/messages/MessagesPage"
}
//#endregion

//#region Chat System
// Queries
query getConversations {
  fn: import { getConversations } from "@src/chat/queries",
  entities: [User, Conversation, Participant, Message],
  auth: true
}

query getMessages {
  fn: import { getMessages } from "@src/chat/queries",
  entities: [User, Message, Conversation, MessageStatus, Participant],
  auth: true
}

// Actions
action sendMessage {
  fn: import { sendMessage } from "@src/chat/actions",
  entities: [User, Conversation, Message, Participant, MessageStatus, Notification],
  auth: true
}

action startConversation {
  fn: import { startConversation } from "@src/chat/actions",
  entities: [User, Conversation, Participant],
  auth: true
}

action markMessageAsRead {
  fn: import { markMessageAsRead } from "@src/chat/actions",
  entities: [User, Message, MessageStatus, Participant, Conversation],
  auth: true
}


//#endregion

// --- Search Results Page ---
route SearchPageRoute { path: "/search", to: SearchPage }
page SearchPage {
  component: import SearchPage from "@src/search/SearchPage" // We will create this component
}

// Add this API definition for the health check endpoint
api healthCheckApi {
  fn: import { healthCheckApi } from "@src/healthApi.js",
  auth: false, // Health check should not require authentication
  httpRoute: (GET, "/health")
}

// Define a namespace for auth APIs to apply common middleware
apiNamespace authApis {
  middlewareConfigFn: import { authApiNamespaceMiddleware } from "@src/auth/middleware",
  path: "/api/auth" // This will apply to /api/auth/request-otp, /api/auth/user-exists, etc.
}

// Mobile API endpoints for chat - will use the authApis namespace and its modified middleware
api mobileGetConversations {
  fn: import { mobileGetConversations } from "@src/chat/mobileApi.ts",
  entities: [User, Conversation, Participant, Message],
  auth: true, // Middleware will handle auth
  httpRoute: (GET, "/api/auth/mobile/conversations") // Path under /api/auth
}

api mobileGetMessages {
  fn: import { mobileGetMessages } from "@src/chat/mobileApi.ts",
  entities: [User, Message, Conversation, MessageStatus, Participant],
  auth: true, // Middleware will handle auth
  httpRoute: (GET, "/api/auth/mobile/messages/:conversationId") // Path under /api/auth
}

api mobileSendMessage {
  fn: import { mobileSendMessage } from "@src/chat/mobileApi.ts",
  entities: [User, Conversation, Message, Participant, MessageStatus, Notification],
  auth: true, // Middleware will handle auth
  httpRoute: (POST, "/api/auth/mobile/messages") // Path under /api/auth
}

api mobileMarkMessageAsRead {
  fn: import { mobileMarkMessageAsRead } from "@src/chat/mobileApi.ts",
  entities: [User, Message, MessageStatus, Participant, Conversation],
  auth: true, // Middleware will handle auth
  httpRoute: (POST, "/api/auth/mobile/messages/read") // Path under /api/auth
}

api mobileStartConversation {
  fn: import { mobileStartConversation } from "@src/chat/mobileApi.ts",
  entities: [User, Conversation, Participant],
  auth: true, // Middleware will handle auth
  httpRoute: (POST, "/api/auth/mobile/conversations/start") // Path under /api/auth
}

// API Endpoint for Requesting Phone OTP
api requestOtpApi {
  fn: import { handleRequestOtp } from "@src/auth/apiHandlers", // We will create this handler next
  httpRoute: (POST, "/api/auth/request-otp"),
  auth: false // Publicly accessible
}

// API Endpoint for Checking User Existence
api checkUserExistenceApi {
  fn: import { handleCheckUserExistence } from "@src/auth/apiHandlers",
  httpRoute: (GET, "/api/auth/user-exists"), // e.g., /api/auth/user-exists?email=<EMAIL>&mobileNumber=123
  auth: false // Publicly accessible
}

// API Endpoint for Verifying OTP and Registering User
api verifyOtpAndRegisterApi {
  fn: import { handleVerifyOtpAndRegister } from "@src/auth/apiHandlers",
  httpRoute: (POST, "/api/auth/verify-otp-register"),
  auth: false // Publicly accessible for registration step
}

// API Endpoint for Login with Phone/Email and Password
api loginApi {
  fn: import { handleLoginWithPhoneOrEmail } from "@src/auth/apiHandlers",
  httpRoute: (POST, "/api/auth/login"),
  auth: false // Publicly accessible
}

// API Endpoint for Requesting Email OTP
api requestEmailOtpApi {
  fn: import { handleRequestEmailOtp } from "@src/auth/apiHandlers", // We will create this handler
  httpRoute: (POST, "/api/auth/request-email-otp"),
  auth: false // Publicly accessible
}

// Password Reset API Endpoints
api requestPasswordResetOtpApi {
  fn: import { handleRequestPasswordResetOtp } from "@src/auth/apiHandlers",
  httpRoute: (POST, "/api/auth/request-password-reset-otp"),
  auth: false // Publicly accessible
}

api verifyPasswordResetOtpApi {
  fn: import { handleVerifyPasswordResetOtp } from "@src/auth/apiHandlers",
  httpRoute: (POST, "/api/auth/verify-password-reset-otp"),
  auth: false // Publicly accessible
}

api resetPasswordApi {
  fn: import { handleResetPassword } from "@src/auth/apiHandlers",
  httpRoute: (POST, "/api/auth/reset-password"),
  auth: false // Publicly accessible
}

// API Endpoint for Provider OTP Verification and Registration
api providerVerifyOtpAndRegisterApi {
  fn: import { handleVerifyOtpAndRegisterProvider } from "@src/auth/apiHandlers",
  httpRoute: (POST, "/api/auth/provider/verify-otp-register"),
  auth: false // Publicly accessible for registration step
}

// API Endpoint for Provider Login
api providerLoginApi {
  fn: import { handleProviderLogin } from "@src/auth/apiHandlers",
  httpRoute: (POST, "/api/auth/provider/login"),
  auth: false // Publicly accessible for login
}

// API Endpoint for Provider Complete Setup
api providerCompleteSetupApi {
  fn: import { handleProviderCompleteSetup } from "@src/auth/apiHandlers",
  httpRoute: (POST, "/api/auth/provider/complete-setup"),
  auth: true // Requires authentication - provider must be logged in
}

// API Endpoint for Provider Onboarding Status
api providerOnboardingStatusApi {
  fn: import { handleGetProviderOnboardingStatus } from "@src/auth/apiHandlers",
  httpRoute: (GET, "/api/auth/providers/onboarding/status"),
  entities: [User, SProvider],
  auth: true // Requires authentication - provider must be logged in
}

// API Endpoint for Get Provider Categories (Auth namespace)
api authGetProviderCategoriesApi {
  fn: import { handleGetProviderCategories } from "@src/auth/apiHandlers",
  httpRoute: (GET, "/api/auth/provider/categories"),
  entities: [ProviderCategory, Translation], // Add entities for the handler
  auth: true 
}

// --- Profile Picture Management APIs (Auth namespace) ---
api uploadProfilePictureApi {
  fn: import { handleUploadProfilePicture } from "@src/user/profileApiHandlers",
  httpRoute: (POST, "/api/auth/user/profile-picture"),
  entities: [User, File],
  auth: true
}

api getProfilePictureApi {
  fn: import { handleGetProfilePicture } from "@src/user/profileApiHandlers",
  httpRoute: (GET, "/api/auth/user/profile-picture"),
  entities: [User, File],
  auth: true
}

api removeProfilePictureApi {
  fn: import { handleRemoveProfilePicture } from "@src/user/profileApiHandlers",
  httpRoute: (DELETE, "/api/auth/user/profile-picture"),
  entities: [User, File],
  auth: true
}

api getUserFileByIdApi {
  fn: import { handleGetUserFileById } from "@src/file-upload/apiHandlers",
  httpRoute: (GET, "/api/auth/files/:fileId"),
  entities: [User, File],
  auth: true
}

api getProviderLogoApi {
  fn: import { handleGetProviderLogo } from "@src/provider/logoApiHandlers",
  httpRoute: (GET, "/api/auth/provider/logo"),
  entities: [User, File, SProvider],
  auth: true
}

api uploadProviderLogoApi {
  fn: import { handleUploadProviderLogo } from "@src/provider/logoApiHandlers",
  httpRoute: (POST, "/api/auth/provider/logo"),
  entities: [User, File, SProvider],
  auth: true
}

api removeProviderLogoApi {
  fn: import { handleRemoveProviderLogo } from "@src/provider/logoApiHandlers",
  httpRoute: (DELETE, "/api/auth/provider/logo"),
  entities: [User, File, SProvider],
  auth: true
}

//#region Provider & Services
query getProviderCategories {
  fn: import { getProviderCategories } from "@src/provider/operations",
  entities: [ProviderCategory,Translation] // Add ProviderCategory entity
}

// ADDED: API endpoint for getProviderCategories
api getProviderCategoriesApi {
  fn: import { handleGetProviderCategories } from "@src/provider/apiHandlers",
  httpRoute: (GET, "/api/provider-categories"),
  entities: [ProviderCategory, Translation], // Add Translation entity for API handler
  auth: false // Assuming categories are public
}

query getUserServiceProvider {
  fn: import { getUserServiceProvider } from "@src/provider/operations",
  entities: [SProvider,Translation]
}

// Action to update SProvider details for the logged-in user
action updateSProvider {
  fn: import { updateSProvider } from "@src/provider/operations",
  entities: [SProvider, Translation], // User is implicitly available via context
  auth: true // Requires user to be logged in
}

// Action to add a new SProvidingPlace for the user's SProvider
action addSProvidingPlace {
  fn: import { addSProvidingPlace } from "@src/provider/operations",
  entities: [SProvidingPlace, SProvider, Address], // Need access to both entities
  auth: true // Requires user to be logged in
}

// Action to update an existing SProvidingPlace
action updateSProvidingPlace {
  fn: import { updateSProvidingPlace } from "@src/provider/operations",
  entities: [SProvidingPlace, SProvider, Address, Translation], // Need access to both
  auth: true // Requires user to be logged in
}

// Query to get SProvidingPlaces for the logged-in user's SProvider
query getSProvidingPlaces {
  fn: import { getSProvidingPlaces } from "@src/provider/operations",
  entities: [SProvidingPlace, SProvider, Translation],
  auth: true // Requires user to be logged in
}

// Query to get Opening hours for the user's SProvider
query getSProviderOpenings {
  fn: import { getSProviderOpenings } from "@src/provider/operations",
  entities: [Opening, OpeningHours, SProvidingPlace, SProvider],
  auth: true
}

// Action to update Opening hours for a specific SProvidingPlace
action updateSProviderOpenings {
  fn: import { updateSProviderOpenings } from "@src/provider/operations",
  entities: [Opening, OpeningHours, SProvidingPlace, SProvider], // Need access to these
  auth: true
}

// Action to create a new ServiceCategory for the user's SProvider
action createServiceCategory {
  fn: import { createServiceCategory } from "@src/provider/operations",
  entities: [ServiceCategory, SProvider],
  auth: true // Requires user to be logged in
}

// Query to get ServiceCategories for the logged-in user's SProvider
query getServiceCategories {
  fn: import { getServiceCategories } from "@src/provider/operations",
  entities: [ServiceCategory, SProvider],
  auth: true // Requires user to be logged in
}

// Action to delete a ServiceCategory
action deleteServiceCategory {
  fn: import { deleteServiceCategory } from "@src/provider/operations",
  entities: [ServiceCategory, SProvider],
  auth: true // Requires user to be logged in
}

// --- Service CRUD ---

// Action to create a new Service for the user's SProvider
action createService {
  fn: import { createService } from "@src/provider/operations",
  entities: [Service, SProvider, ServiceCategory, Queue, Translation],
  auth: true
}

// Query to get Services for the logged-in user's SProvider
query getServices {
  fn: import { getServices } from "@src/provider/operations",
  entities: [Service, SProvider, ServiceCategory, Queue, Translation],
  auth: true
}

// Action to update an existing Service
action updateService {
  fn: import { updateService } from "@src/provider/operations",
  entities: [Service, SProvider, ServiceCategory, Queue, Translation],
  auth: true
}

// Action to delete a Service
action deleteService {
  fn: import { deleteService } from "@src/provider/operations",
  entities: [Service, SProvider, Appointment, Queue, Translation],
  auth: true
}

// Query to get Customers linked to the current Provider
query getProviderCustomers {
  fn: import { getProviderCustomers } from "@src/provider/operations",
  entities: [User, SProvider, CustomerFolder , Translation],
  auth: true
}

// Action to create a new Customer linked to the current Provider
action createProviderCustomer {
  fn: import { createProviderCustomer } from "@src/provider/operations",
  entities: [User, SProvider, CustomerFolder, Translation],
  auth: true
}

// --- Appointment CRUD --- 

action createAppointment {
  fn: import { createAppointment } from "@src/provider/operations",
  entities: [Appointment, CustomerFolder, Service, SProvidingPlace, User, SProvider, Queue],
  auth: true
}

query getAppointments {
  fn: import { getAppointments } from "@src/provider/operations",
  entities: [Appointment, CustomerFolder, Service, SProvidingPlace, User, SProvider, Queue , Translation],
  auth: true
}

action updateAppointment {
  fn: import { updateAppointment } from "@src/provider/operations",
  entities: [Appointment, AppointmentHistory, CustomerFolder, Service, SProvidingPlace, User, SProvider, Queue],
  auth: true
}

// --- Search Providers Query Definition ---
query searchProviders {
  fn: import { searchProviders } from "@src/provider/operations",
  entities: [SProvider, User, ProviderCategory, Service, SProvidingPlace, Queue, Opening, OpeningHours, Appointment, QueueOpening, QueueOpeningHours, Translation]
  // No auth needed for public search
}

// ADDED: API endpoint for searchProviders
api searchProvidersApi {
  fn: import { handleSearchProviders } from "@src/provider/apiHandlers",
  httpRoute: (GET, "/api/search/providers"),
  auth: false
}

// --- Get Provider Availability Query Definition ---
query getProviderAvailability {
  fn: import { getProviderAvailability } from "@src/provider/operations",
  entities: [Service, SProvidingPlace, Opening, OpeningHours, Appointment, Queue, QueueOpening, QueueOpeningHours, Translation]
  // No auth needed if availability is public
}

// ADDED: API endpoint for getProviderAvailability
api getProviderAvailabilityApi {
  fn: import { handleGetProviderAvailability } from "@src/provider/apiHandlers",
  httpRoute: (GET, "/api/provider-availability"),
  auth: false // Availability can be public
}

// ADDED: API endpoint for createCustomerAppointment
api createCustomerAppointmentApi {
  fn: import { handleCreateCustomerAppointment } from "@src/provider/apiHandlers", // Handler to be created
  httpRoute: (POST, "/api/auth/appointments/customer-booking"),
  auth: true // This endpoint requires authentication
}

// ADDED: API endpoint for getProviderProfileCompletion
api getProviderProfileCompletionApi {
  fn: import { handleGetProviderProfileCompletion } from "@src/provider/apiHandlers",
  httpRoute: (GET, "/api/auth/provider/profile-completion"),
  entities: [User, SProvider, ProviderCategory, SProvidingPlace, Service, Queue, Address],
  auth: true // This endpoint requires authentication - provider must be logged in
}

//#endregion

//#region Queue Management (NEW)

// Action to create a new Queue for a Providing Place
action createQueue {
  fn: import { createQueue } from "@src/queue/operations",
  entities: [Queue, SProvidingPlace, SProvider, Service, Translation], // Added Service
  auth: true
}

// Query to get Queues for a specific Providing Place
query getQueuesByPlace {
  fn: import { getQueuesByPlace } from "@src/queue/operations",
  entities: [Queue, SProvidingPlace, SProvider, Service, Translation], // Ensure Service is here for the include
  auth: true
}

// Action to update Queue details (title, isActive)
action updateQueue {
  fn: import { updateQueue } from "@src/queue/operations",
  entities: [Queue, SProvidingPlace, SProvider, Service, Translation], // Added Service
  auth: true
}

// Action to delete a Queue
action deleteQueue {
  fn: import { deleteQueue } from "@src/queue/operations",
  entities: [Queue, Appointment, SProvidingPlace, SProvider, Translation], // Need Appt for checks, SProvider for auth
  auth: true
}

// Action to assign Services to a Queue (M2M)
action assignServicesToQueue {
  fn: import { assignServicesToQueue } from "@src/queue/operations",
  entities: [Queue, Service, SProvidingPlace, SProvider], // Need Service for linking, SProvider for auth
  auth: true
}

// Query to get QueueOpenings for a specific Queue
query getQueueOpenings {
  fn: import { getQueueOpenings } from "@src/queue/operations",
  entities: [QueueOpening, QueueOpeningHours, Queue, SProvidingPlace, SProvider], // Need SProvider for auth
  auth: true
}

// Action to update QueueOpenings for a specific Queue
action updateQueueOpenings {
  fn: import { updateQueueOpenings } from "@src/queue/operations",
  entities: [QueueOpening, QueueOpeningHours, Queue, SProvidingPlace, SProvider], // Need SProvider for auth
  auth: true
}

// Query to get availability based on Queues
query getQueueAvailability {
  fn: import { getQueueAvailability } from "@src/queue/operations",
  // Entities needed for fetching queues, services, queue openings, appointments
  entities: [Queue, Service, QueueOpening, QueueOpeningHours, Appointment, SProvidingPlace]
  // Publicly accessible for booking
}

//#endregion

//#region New Action Definitions
action createCustomerAppointment {
  fn: import { createCustomerAppointment } from "@src/provider/operations",
  entities: [Appointment, CustomerFolder, Service, SProvidingPlace, User, SProvider, Queue],
  auth: true
}

// Action for a customer to cancel their own appointment
action cancelAppointment {
  fn: import { cancelAppointment } from "@src/customer/operations",
  entities: [Appointment, User, CustomerFolder, Queue, Address],
  auth: true
}

// Action for a provider to mark an appointment as completed and refund credits
action completeAppointment {
  fn: import { completeAppointment } from "@src/provider/operations",
  entities: [Appointment, User, CustomerFolder, Service, SProvider, Queue],
  auth: true
}
//#endregion

// =========================================================================
// Customer Specific Routes & Operations
// =========================================================================

route MyProfileRoute { path: "/my-profile", to: MyProfilePage }
page MyProfilePage {
  component: import MyProfilePage from "@src/pages/customer/MyProfilePage",
  authRequired: true
  // TODO: Consider adding role check here if needed
}

route MyAppointmentsRoute { path: "/my-appointments", to: MyAppointmentsPage }
page MyAppointmentsPage {
  component: import MyAppointmentsPage from "@src/pages/customer/MyAppointmentsPage",
  authRequired: true
  // TODO: Consider adding role check here if needed
}

query getCustomerAppointments {
  fn: import { getCustomerAppointments } from "@src/customer/operations",
  entities: [Appointment, CustomerFolder, Service, SProvidingPlace, User, SProvider, Queue, Address],
  auth: true
}

query getAdminCustomerDetails {
  fn: import { getAdminCustomerDetails } from "@src/admin/operations",
  entities: [User, CustomerFolder, Appointment, Service, SProvidingPlace, SProvider, Queue, Address],
  auth: true
}

action noShowAppointment {
  fn: import { noShowAppointment } from "@src/provider/operations",
  entities: [Appointment, AppointmentHistory, User, CustomerFolder, SProvider, SProvidingPlace, Queue],
  auth: true
}

// =========================================================================

// Query to get overflowed appointments for a provider
query getOverflowedAppointments {
  fn: import { getOverflowedAppointments } from "@src/provider/operations",
  entities: [Appointment, CustomerFolder, Service, SProvidingPlace, User, SProvider, Queue],
  auth: true
}

// Action to handle an overflowed appointment (cancel/reschedule/confirm overtime)
action handleOverflowedAppointment {
  fn: import { handleOverflowedAppointment } from "@src/provider/operations",
  entities: [Appointment, AppointmentHistory, CustomerFolder, Service, SProvidingPlace, User, SProvider, Queue, RescheduleRequest],
  auth: true
}

// Query to get rescheduling requests for a user (both sent and received)
query getRescheduleRequests {
  fn: import { getRescheduleRequests } from "@src/provider/operations",
  entities: [RescheduleRequest, Appointment, User, CustomerFolder, Service, SProvidingPlace, Queue],
  auth: true
}

// Action to create a rescheduling request
action createRescheduleRequest {
  fn: import { createRescheduleRequest } from "@src/provider/operations",
  entities: [RescheduleRequest, Appointment, User, CustomerFolder, Service, SProvidingPlace, Queue],
  auth: true
}

// Action to respond to a rescheduling request
action respondToRescheduleRequest {
  fn: import { respondToRescheduleRequest } from "@src/provider/operations",
  entities: [RescheduleRequest, Appointment, AppointmentHistory, User, CustomerFolder, Service, SProvidingPlace, Queue],
  auth: true
}

// PROVIDER ACTIONS & QUERIES
// ... existing code ...

// ... existing code ...

action requestQueueSwap {
  fn: import { requestQueueSwap } from "@src/shared/operations",
  entities: [User, Appointment, QueueSwapRequest]
}

action respondToQueueSwapRequest {
  fn: import { respondToQueueSwapRequest } from "@src/shared/operations",
  entities: [User, Appointment, QueueSwapRequest, AppointmentHistory]
}

action finalizeQueueSwap {
  fn: import { finalizeQueueSwap } from "@src/shared/operations",
  entities: [User, Appointment, QueueSwapRequest, AppointmentHistory]
}

query getQueueSwapRequests {
  fn: import { getQueueSwapRequests } from "@src/shared/operations",
  entities: [User, Appointment, QueueSwapRequest]
}

// --- Early Closure Action ---
action closeEarlyForToday {
  fn: import { closeEarlyForToday } from "@src/provider/operations",
  entities: [Appointment, User, SProvider],
  auth: true
}

// ADDED: API endpoint for getCustomerAppointments
api getCustomerAppointmentsApi {
  fn: import { handleGetCustomerAppointments } from "@src/customer/apiHandlers", // Handler to be created in a new file
  httpRoute: (GET, "/api/auth/customer/appointments"),
  auth: true // Requires authentication
}

// --- Notification Operations ---
query getUserNotifications {
  fn: import { getUserNotifications } from "@src/notifications/operations",
  entities: [User, Notification],
  auth: true
}

action markNotificationAsRead {
  fn: import { markNotificationAsRead } from "@src/notifications/operations",
  entities: [User,Notification],
  auth: true
}

action markAllNotificationsAsRead {
  fn: import { markAllNotificationsAsRead } from "@src/notifications/operations",
  entities: [User, Notification],
  auth: true
}

action saveFcmToken {
  fn: import { saveFcmToken } from "@src/notifications/fcmActions",
  entities: [User, UserDeviceToken],
  auth: true
}

// ADDED: API endpoint for mobile FCM token registration
api saveMobileFcmTokenApi {
  fn: import { handleSaveMobileFcmToken } from "@src/notifications/apiHandlers",
  httpRoute: (POST, "/api/auth/notifications/mobile/save-fcm-token"),
  entities: [User, UserDeviceToken],
  auth: true // Assumes mobile app authenticates in a way Wasp APIs can understand
}

// API for mobile to get user notifications
api getMobileUserNotificationsApi {
  fn: import { handleGetMobileUserNotifications } from "@src/notifications/apiHandlers",
  httpRoute: (GET, "/api/auth/notifications/mobile/list"),
  entities: [User, Notification],
  auth: true
}

// API for mobile to mark a notification as read
api markMobileNotificationAsReadApi {
  fn: import { handleMarkMobileNotificationAsRead } from "@src/notifications/apiHandlers",
  httpRoute: (POST, "/api/auth/notifications/mobile/mark-as-read"), // Using POST to send notificationId in body
  entities: [User, Notification],
  auth: true
}

// API for mobile to mark all notifications as read
api markAllMobileNotificationsAsReadApi {
  fn: import { handleMarkAllMobileNotificationsAsRead } from "@src/notifications/apiHandlers",
  httpRoute: (POST, "/api/auth/notifications/mobile/mark-all-as-read"),
  entities: [User, Notification],
  auth: true
}

// --- Firebase Testing APIs ---

// API for testing Firebase setup (admin/development use)
api testFirebaseSetupApi {
  fn: import { handleTestFirebaseSetup } from "@src/notifications/apiHandlers",
  httpRoute: (GET, "/api/auth/notifications/test/firebase-setup"),
  entities: [User, UserDeviceToken],
  auth: true
}

// API for testing notification send to current user
api testSendNotificationApi {
  fn: import { handleTestSendNotification } from "@src/notifications/apiHandlers",
  httpRoute: (POST, "/api/auth/notifications/test/send"),
  entities: [User, UserDeviceToken],
  auth: true
}

// --- Queue Swap APIs for Mobile/External Clients ---
api requestQueueSwapApi {
  fn: import { handleRequestQueueSwap } from "@src/shared/apiHandlers",
  httpRoute: (POST, "/api/auth/queue/request-swap"),
  // Entities involved: User (auth), Appointment (read), Queue (read), CustomerFolder (read), SProvider (read for auth check in func), QueueSwapRequest (create), Notification (create)
  entities: [User, Appointment, Queue, CustomerFolder, SProvider, QueueSwapRequest, Notification],
  auth: true
}

api respondToQueueSwapApi {
  fn: import { handleRespondToQueueSwap } from "@src/shared/apiHandlers",
  httpRoute: (POST, "/api/auth/queue/respond-swap"),
  // Entities involved: User (auth), QueueSwapRequest (read/update), Appointment (read/update), AppointmentHistory (create), Notification (create)
  entities: [User, QueueSwapRequest, Appointment, AppointmentHistory, Notification],
  auth: true
}

// --- Customer Address APIs ---
api getCustomerAddressesApi {
  fn: import { handleGetCustomerAddresses } from "@src/customer/apiHandlers",
  httpRoute: (GET, "/api/auth/customer/addresses"),
  entities: [User, Address],
  auth: true
}

api createCustomerAddressApi {
  fn: import { handleCreateCustomerAddress } from "@src/customer/apiHandlers",
  httpRoute: (POST, "/api/auth/customer/addresses"),
  entities: [User, Address],
  auth: true
}

api updateCustomerAddressApi {
  fn: import { handleUpdateCustomerAddress } from "@src/customer/apiHandlers",
  httpRoute: (PUT, "/api/auth/customer/addresses/:addressId"), // addressId as path parameter
  entities: [User, Address],
  auth: true
}

api deleteCustomerAddressApi {
  fn: import { handleDeleteCustomerAddress } from "@src/customer/apiHandlers",
  httpRoute: (DELETE, "/api/auth/customer/addresses/:addressId"), // addressId as path parameter
  entities: [User, Address],
  auth: true
}


action updateUserPreferedLanguage {
  fn: import { updateUserPreferedLanguage } from "@src/user/operations",
  entities: [User],
  auth: true
}

api updateUserPreferedLanguageApi {
  fn: import { handleUpdateUserPreferedLanguage } from "@src/user/apiHandlers",
  httpRoute: (POST, "/api/auth/user/update-prefered-language"), // Will be prefixed by /api/auth by the namespace
  entities: [User], // For context.user access in handler if directly using prisma
  auth: true
}

// Query to get public provider information by ID (no auth required)
query getPublicProviderById {
  fn: import { getPublicProviderById } from "@src/customer/operations",
  entities: [SProvider, User, SProvidingPlace, Queue, Service, ProviderCategory, Address],
  auth: false
}

// API route for public provider information
api GetPublicProviderApi {
  fn: import { handleGetPublicProviderById } from "@src/customer/apiHandlers",
  entities: [SProvider, User, SProvidingPlace, Queue, Service, ProviderCategory, Address],
  auth: false,
  httpRoute: (GET, "/api/public/provider/:providerId")
}

// --- Review Management Actions ---
action createReview {
  fn: import { createReview } from "@src/reviews/operations",
  entities: [Review, User, SProvider, Appointment, Notification],
  auth: true
}

action updateReview {
  fn: import { updateReview } from "@src/reviews/operations",
  entities: [Review, User, SProvider, Appointment, Notification],
  auth: true
}

action deleteReview {
  fn: import { deleteReview } from "@src/reviews/operations",
  entities: [Review, User, SProvider, Appointment, Notification],
  auth: true
}

// Query to get reviews for a provider
query getReviewsForProvider {
  fn: import { getReviewsForProvider } from "@src/reviews/operations",
  entities: [Review, User, SProvider, Appointment, Notification]
}

// Query to get reviews by customer
query getReviewsByCustomer {
  fn: import { getReviewsByCustomer } from "@src/reviews/operations",
  entities: [Review, User, SProvider, Appointment, Notification],
  auth: true
}

// API endpoints for mobile/external clients
api createReviewApi {
  fn: import { handleCreateReview } from "@src/reviews/apiHandlers",
  httpRoute: (POST, "/api/auth/reviews"),
  entities: [Review, User, SProvider, Appointment, Notification],
  auth: true
}

api getProviderReviewsApi {
  fn: import { handleGetProviderReviews } from "@src/reviews/apiHandlers",
  httpRoute: (GET, "/api/public/providers/:providerId/reviews"),
  entities: [Review, User, SProvider]
}

api getPublicAdvertisementsApi {
  fn: import { handleGetPublicAdvertisements } from "@src/advertisements/apiHandlers",
  httpRoute: (GET, "/api/auth/public/advertisements"),
  entities: [Advertisement, File],
  auth: false
}

// --- Provider Mobile API Endpoints ---

// Provider Profile Management APIs
api getProviderProfileApi {
  fn: import { getProviderProfile } from "@src/provider/mobile/handlers/profile",
  httpRoute: (GET, "/api/auth/providers/profile"),
  entities: [User, SProvider, ProviderCategory],
  auth: true
}

api updateProviderProfileApi {
  fn: import { updateProviderProfile } from "@src/provider/mobile/handlers/profile",
  httpRoute: (PUT, "/api/auth/providers/profile"),
  entities: [User, SProvider, ProviderCategory],
  auth: true
}

// Provider Location Management APIs
api getProviderLocationsApi {
  fn: import { getProviderLocations } from "@src/provider/mobile/handlers/locations",
  httpRoute: (GET, "/api/auth/providers/locations"),
  entities: [User, SProvider, SProvidingPlace, Address],
  auth: true
}

api createProviderLocationApi {
  fn: import { createProviderLocation } from "@src/provider/mobile/handlers/locations",
  httpRoute: (POST, "/api/auth/providers/locations"),
  entities: [User, SProvider, SProvidingPlace, Address, Opening, OpeningHours],
  auth: true
}

api updateProviderLocationApi {
  fn: import { updateProviderLocation } from "@src/provider/mobile/handlers/locations",
  httpRoute: (PUT, "/api/auth/providers/locations/:id"),
  entities: [User, SProvider, SProvidingPlace, Address, Opening, OpeningHours],
  auth: true
}

api getProviderLocationApi {
  fn: import { getProviderLocation } from "@src/provider/mobile/handlers/locations",
  httpRoute: (GET, "/api/auth/providers/locations/:id"),
  entities: [User, SProvider, SProvidingPlace, Address, Opening, OpeningHours],
  auth: true
}

api deleteProviderLocationApi {
  fn: import { deleteProviderLocation } from "@src/provider/mobile/handlers/locations",
  httpRoute: (DELETE, "/api/auth/providers/locations/:id"),
  entities: [User, SProvider, SProvidingPlace, Queue, Appointment, Opening, OpeningHours , Translation],
  auth: true
}

// Provider Service Category Management APIs
api getProviderServiceCategoriesApi {
  fn: import { getProviderServiceCategories } from "@src/provider/mobile/handlers/serviceCategories",
  httpRoute: (GET, "/api/auth/providers/service-categories"),
  entities: [User, SProvider, ServiceCategory],
  auth: true
}

api createProviderServiceCategoryApi {
  fn: import { createProviderServiceCategory } from "@src/provider/mobile/handlers/serviceCategories",
  httpRoute: (POST, "/api/auth/providers/service-categories"),
  entities: [User, SProvider, ServiceCategory, Translation],
  auth: true
}

api updateProviderServiceCategoryApi {
  fn: import { updateProviderServiceCategory } from "@src/provider/mobile/handlers/serviceCategories",
  httpRoute: (PUT, "/api/auth/providers/service-categories/:id"),
  entities: [User, SProvider, ServiceCategory, Translation],
  auth: true
}

api deleteProviderServiceCategoryApi {
  fn: import { deleteProviderServiceCategory } from "@src/provider/mobile/handlers/serviceCategories",
  httpRoute: (DELETE, "/api/auth/providers/service-categories/:id"),
  entities: [User, SProvider, ServiceCategory, Service, Translation],
  auth: true
}

// Provider Service Management APIs
api getProviderServicesApi {
  fn: import { getProviderServices } from "@src/provider/mobile/handlers/services",
  httpRoute: (GET, "/api/auth/providers/services"),
  entities: [User, SProvider, Service, ServiceCategory],
  auth: true
}

api getProviderServiceApi {
  fn: import { getProviderService } from "@src/provider/mobile/handlers/services",
  httpRoute: (GET, "/api/auth/providers/services/:id"),
  entities: [User, SProvider, Service, ServiceCategory],
  auth: true
}

api createProviderServiceApi {
  fn: import { createProviderService } from "@src/provider/mobile/handlers/services",
  httpRoute: (POST, "/api/auth/providers/services"),
  entities: [User, SProvider, Service, ServiceCategory, Translation],
  auth: true
}

api updateProviderServiceApi {
  fn: import { updateProviderService } from "@src/provider/mobile/handlers/services",
  httpRoute: (PUT, "/api/auth/providers/services/:id"),
  entities: [User, SProvider, Service, ServiceCategory, Translation],
  auth: true
}

api deleteProviderServiceApi {
  fn: import { deleteProviderService } from "@src/provider/mobile/handlers/services",
  httpRoute: (DELETE, "/api/auth/providers/services/:id"),
  entities: [User, SProvider, Service, Queue, Appointment, Translation],
  auth: true
}

// Provider Schedule Management APIs
api getProviderSchedulesApi {
  fn: import { getProviderSchedules } from "@src/provider/mobile/handlers/schedules",
  httpRoute: (GET, "/api/auth/providers/schedules"),
  entities: [User, SProvider, SProvidingPlace, Opening, OpeningHours],
  auth: true
}

api createProviderScheduleApi {
  fn: import { createProviderSchedule } from "@src/provider/mobile/handlers/schedules",
  httpRoute: (POST, "/api/auth/providers/schedules"),
  entities: [User, SProvider, SProvidingPlace, Opening, OpeningHours],
  auth: true
}

api updateProviderScheduleApi {
  fn: import { updateProviderSchedule } from "@src/provider/mobile/handlers/schedules",
  httpRoute: (PUT, "/api/auth/providers/schedules/:id"),
  entities: [User, SProvider, SProvidingPlace, Opening, OpeningHours],
  auth: true
}

api deleteProviderScheduleApi {
  fn: import { deleteProviderSchedule } from "@src/provider/mobile/handlers/schedules",
  httpRoute: (DELETE, "/api/auth/providers/schedules/:id"),
  entities: [User, SProvider, SProvidingPlace, Opening, OpeningHours],
  auth: true
}

// Provider Queue Management APIs
api getProviderQueuesApi {
  fn: import { getProviderQueues } from "@src/provider/mobile/handlers/queues",
  httpRoute: (GET, "/api/auth/providers/queues"),
  entities: [User, SProvider, SProvidingPlace, Queue, Service, Translation, QueueOpening, QueueOpeningHours],
  auth: true
}

api createProviderQueueApi {
  fn: import { createProviderQueue } from "@src/provider/mobile/handlers/queues",
  httpRoute: (POST, "/api/auth/providers/queues"),
  entities: [User, SProvider, SProvidingPlace, Queue, Service, Translation , QueueOpening, QueueOpeningHours],
  auth: true
}

api updateProviderQueueApi {
  fn: import { updateProviderQueue } from "@src/provider/mobile/handlers/queues",
  httpRoute: (PUT, "/api/auth/providers/queues/:id"),
  entities: [User, SProvider, SProvidingPlace, Queue, Service, Translation, QueueOpening, QueueOpeningHours],
  auth: true
}

api deleteProviderQueueApi {
  fn: import { deleteProviderQueue } from "@src/provider/mobile/handlers/queues",
  httpRoute: (DELETE, "/api/auth/providers/queues/:id"),
  entities: [User, SProvider, SProvidingPlace, Queue, Appointment, QueueOpening, QueueOpeningHours, Translation],
  auth: true
}

api getQueuesByLocationApi {
  fn: import { getQueuesByLocation } from "@src/provider/mobile/handlers/queues",
  httpRoute: (GET, "/api/auth/providers/locations/:locationId/queues"),
  entities: [User, SProvider, SProvidingPlace, Queue, Service],
  auth: true
}

// Provider Queue Limit APIs
api getQueueLimitsApi {
  fn: import { getQueueLimits } from "@src/provider/mobile/handlers/queueLimits",
  httpRoute: (GET, "/api/auth/providers/queues/limits"),
  entities: [User, SProvider, Queue, SProvidingPlace],
  auth: true
}

api canCreateQueueApi {
  fn: import { canCreateQueue } from "@src/provider/mobile/handlers/queueLimits",
  httpRoute: (GET, "/api/auth/providers/queues/can-create"),
  entities: [User, SProvider, Queue, SProvidingPlace],
  auth: true
}

// Provider Queue Service Assignment APIs
api getQueueServicesApi {
  fn: import { getQueueServices } from "@src/provider/mobile/handlers/queueServices",
  httpRoute: (GET, "/api/auth/providers/queues/:queueId/services"),
  entities: [User, SProvider, Queue, Service],
  auth: true
}

api assignServiceToQueueApi {
  fn: import { assignServiceToQueue } from "@src/provider/mobile/handlers/queueServices",
  httpRoute: (POST, "/api/auth/providers/queues/:queueId/services"),
  entities: [User, SProvider, Queue, Service],
  auth: true
}

api removeServiceFromQueueApi {
  fn: import { removeServiceFromQueue } from "@src/provider/mobile/handlers/queueServices",
  httpRoute: (DELETE, "/api/auth/providers/queues/:queueId/services/:serviceId"),
  entities: [User, SProvider, Queue, Service],
  auth: true
}

// Provider Reschedule Management APIs
api getProviderReschedulesApi {
  fn: import { getProviderReschedules } from "@src/provider/mobile/handlers/reschedules",
  httpRoute: (GET, "/api/auth/providers/reschedules"),
  entities: [User, SProvider, RescheduleRequest, Appointment, CustomerFolder, Service],
  auth: true
}

api getProviderRescheduleApi {
  fn: import { getProviderReschedule } from "@src/provider/mobile/handlers/reschedules",
  httpRoute: (GET, "/api/auth/providers/reschedules/:id"),
  entities: [User, SProvider, RescheduleRequest, Appointment, CustomerFolder, Service],
  auth: true
}

api createRescheduleRequestApi {
  fn: import { createRescheduleRequest } from "@src/provider/mobile/handlers/reschedules",
  httpRoute: (POST, "/api/auth/providers/appointments/:id/reschedule"),
  entities: [User, SProvider, RescheduleRequest, Appointment, CustomerFolder, Service],
  auth: true
}

api respondToRescheduleApi {
  fn: import { respondToReschedule } from "@src/provider/mobile/handlers/reschedules",
  httpRoute: (PUT, "/api/auth/providers/reschedules/:id/respond"),
  entities: [User, SProvider, RescheduleRequest, Appointment, CustomerFolder, Service],
  auth: true
}

// Provider Customer Management APIs
api getProviderCustomersApi {
  fn: import { getProviderCustomers } from "@src/provider/mobile/handlers/customers",
  httpRoute: (GET, "/api/auth/providers/customers"),
  entities: [User, SProvider, CustomerFolder, Appointment],
  auth: true
}

api getProviderCustomerApi {
  fn: import { getProviderCustomer } from "@src/provider/mobile/handlers/customers",
  httpRoute: (GET, "/api/auth/providers/customers/:id"),
  entities: [User, SProvider, CustomerFolder, Appointment],
  auth: true
}

api createProviderCustomerApi {
  fn: import { createProviderCustomer } from "@src/provider/mobile/handlers/customers",
  httpRoute: (POST, "/api/auth/providers/customers"),
  entities: [User, SProvider, CustomerFolder],
  auth: true
}

api updateProviderCustomerApi {
  fn: import { updateProviderCustomer } from "@src/provider/mobile/handlers/customers",
  httpRoute: (PUT, "/api/auth/providers/customers/:id"),
  entities: [User, SProvider, CustomerFolder],
  auth: true
}

api softDeleteProviderCustomerApi {
  fn: import { softDeleteProviderCustomer } from "@src/provider/mobile/handlers/customers",
  httpRoute: (DELETE, "/api/auth/providers/customers/:id"),
  entities: [User, SProvider, CustomerFolder],
  auth: true
}

api restoreProviderCustomerApi {
  fn: import { restoreProviderCustomer } from "@src/provider/mobile/handlers/customers",
  httpRoute: (POST, "/api/auth/providers/customers/restore"),
  entities: [User, SProvider, CustomerFolder],
  auth: true
}

// Provider Appointment Management APIs
api getProviderAppointmentsApi {
  fn: import { getProviderAppointments } from "@src/provider/mobile/handlers/appointments",
  httpRoute: (GET, "/api/auth/providers/appointments"),
  entities: [User, SProvider, Appointment, Service, SProvidingPlace, Queue, CustomerFolder],
  auth: true
}

// Provider Dashboard APIs - Must come before parameterized routes
api getProviderDashboardMetricsApi {
  fn: import { handleGetProviderDashboardMetrics } from "@src/provider/apiHandlers",
  httpRoute: (GET, "/api/auth/providers/dashboard/metrics"),
  entities: [User, SProvider, Appointment, Service, CustomerFolder],
  auth: true
}

api getProviderTodayAppointmentsApi {
  fn: import { handleGetProviderTodayAppointments } from "@src/provider/apiHandlers",
  httpRoute: (GET, "/api/auth/providers/appointments/today"),
  entities: [User, SProvider, Appointment, Service, CustomerFolder],
  auth: true
}

api getProviderAppointmentApi {
  fn: import { getProviderAppointment } from "@src/provider/mobile/handlers/appointments",
  httpRoute: (GET, "/api/auth/providers/appointments/:id"),
  entities: [User, SProvider, Appointment, Service, SProvidingPlace, Queue, CustomerFolder],
  auth: true
}

api createProviderAppointmentApi {
  fn: import { createProviderAppointment } from "@src/provider/mobile/handlers/appointments",
  httpRoute: (POST, "/api/auth/providers/appointments"),
  entities: [User, SProvider, Appointment, Service, SProvidingPlace, Queue, CustomerFolder, AppointmentHistory],
  auth: true
}

api extendAppointmentApi {
  fn: import { handleExtendAppointment } from "@src/provider/apiHandlers",
  httpRoute: (PUT, "/api/auth/providers/time/appointments/extend"),
  entities: [Appointment, AppointmentHistory, User, CustomerFolder, Service, SProvider, Queue],
  auth: true
}

api getProviderRevenueChartApi {
  fn: import { handleGetProviderRevenueChart } from "@src/provider/apiHandlers",
  httpRoute: (GET, "/api/auth/providers/dashboard/revenue-chart"),
  entities: [User, SProvider, Appointment, Service, CustomerFolder],
  auth: true
}


api updateProviderAppointmentApi {
  fn: import { updateProviderAppointment } from "@src/provider/mobile/handlers/appointments",
  httpRoute: (PUT, "/api/auth/providers/appointments/:id"),
  entities: [User, SProvider, Appointment, Service, SProvidingPlace, Queue, CustomerFolder, AppointmentHistory],
  auth: true
}



api updateAppointmentStatusApi {
  fn: import { updateAppointmentStatus } from "@src/provider/mobile/handlers/appointments",
  httpRoute: (PUT, "/api/auth/providers/appointments/:id/status"),
  entities: [User, SProvider, Appointment, Service, CustomerFolder, AppointmentHistory],
  auth: true
}



// Provider Logo Management APIs for Mobile
api getProviderLogoMobileApi {
  fn: import { getProviderLogo } from "@src/provider/mobile/handlers/logo",
  httpRoute: (GET, "/api/auth/providers/mobile/logo"),
  entities: [User, SProvider, File],
  auth: true
}

api uploadProviderLogoMobileApi {
  fn: import { uploadProviderLogo } from "@src/provider/mobile/handlers/logo",
  httpRoute: (POST, "/api/auth/providers/mobile/logo"),
  entities: [User, SProvider, File],
  auth: true
}

api removeProviderLogoMobileApi {
  fn: import { removeProviderLogo } from "@src/provider/mobile/handlers/logo",
  httpRoute: (DELETE, "/api/auth/providers/mobile/logo"),
  entities: [User, SProvider, File],
  auth: true
}

api confirmLogoUploadMobileApi {
  fn: import { confirmLogoUpload } from "@src/provider/mobile/handlers/logo",
  httpRoute: (POST, "/api/auth/providers/mobile/logo/confirm"),
  entities: [User, SProvider, File],
  auth: true
}

// --- ADMIN API ENDPOINTS ---

// Admin Authentication
api adminLoginApi {
  fn: import { handleAdminLogin } from "@src/admin/apiHandlers",
  httpRoute: (POST, "/api/auth/admin/login"),
  entities: [User],
  auth: false // Public endpoint for admin login
}

// Admin User Management
action createAdminUser {
  fn: import { createAdminUser } from "@src/admin/operations",
  entities: [User],
  auth: true
}

api createAdminUserApi {
  fn: import { handleCreateAdminUser } from "@src/admin/apiHandlers",
  httpRoute: (POST, "/api/auth/admin/users"),
  entities: [User],
  auth: true // Requires admin authentication
}

// Admin Provider Management
query getAdminProviders {
  fn: import { getAdminProviders } from "@src/admin/operations",
  entities: [User, SProvider, ProviderCategory],
  auth: true
}

api getAdminProvidersApi {
  fn: import { handleGetAdminProviders } from "@src/admin/apiHandlers",
  httpRoute: (GET, "/api/auth/admin/providers"),
  entities: [User, SProvider, ProviderCategory],
  auth: true
}

api getAdminProviderApi {
  fn: import { handleGetAdminProvider } from "@src/admin/apiHandlers",
  httpRoute: (GET, "/api/auth/admin/providers/:providerId"),
  entities: [User, SProvider, ProviderCategory, Service, Queue],
  auth: true
}

action updateProviderStatus {
  fn: import { updateProviderStatus } from "@src/admin/operations",
  entities: [User, SProvider],
  auth: true
}

api updateProviderStatusApi {
  fn: import { handleUpdateProviderStatus } from "@src/admin/apiHandlers",
  httpRoute: (PUT, "/api/auth/admin/providers/:providerId/status"),
  entities: [User, SProvider],
  auth: true
}

api getAdminProviderLocationsApi {
  fn: import { handleGetAdminProviderLocations } from "@src/admin/apiHandlers",
  httpRoute: (GET, "/api/auth/admin/providers/:providerId/locations"),
  entities: [SProvider, SProvidingPlace, Address, Opening, OpeningHours, Queue],
  auth: true
}

api getAdminProviderServicesApi {
  fn: import { handleGetAdminProviderServices } from "@src/admin/apiHandlers",
  httpRoute: (GET, "/api/auth/admin/providers/:providerId/services"),
  entities: [SProvider, Service, ServiceCategory, Queue, SProvidingPlace],
  auth: true
}

api getAdminProviderStatsApi {
  fn: import { handleGetAdminProviderStats } from "@src/admin/apiHandlers",
  httpRoute: (GET, "/api/auth/admin/providers/:providerId/stats"),
  entities: [SProvider, Appointment, CustomerFolder, User, Service, SProvidingPlace, Queue],
  auth: true
}

// Admin Customer Management
query getAdminCustomers {
  fn: import { getAdminCustomers } from "@src/admin/operations",
  entities: [User, CustomerFolder],
  auth: true
}

api getAdminCustomersApi {
  fn: import { handleGetAdminCustomers } from "@src/admin/apiHandlers",
  httpRoute: (GET, "/api/auth/admin/customers"),
  entities: [User, CustomerFolder],
  auth: true
}

// Admin Provider Category Management
query getAdminProviderCategories {
  fn: import { getAdminProviderCategories } from "@src/admin/operations",
  entities: [ProviderCategory, Translation], // Add Translation entity
  auth: true
}

api getAdminProviderCategoriesApi {
  fn: import { handleGetProviderCategories } from "@src/admin/apiHandlers",
  httpRoute: (GET, "/api/auth/admin/provider-categories"),
  entities: [ProviderCategory, Translation], // Add Translation entity
  auth: true
}

api getAdminProviderCategoryApi {
  fn: import { handleGetAdminProviderCategory } from "@src/admin/apiHandlers",
  httpRoute: (GET, "/api/auth/admin/provider-categories/:categoryId"),
  entities: [ProviderCategory, SProvider, User, File],
  auth: true
}

action createProviderCategory {
  fn: import { createProviderCategory } from "@src/admin/operations",
  entities: [ProviderCategory],
  auth: true
}

api createProviderCategoryApi {
  fn: import { handleCreateProviderCategory } from "@src/admin/apiHandlers",
  httpRoute: (POST, "/api/auth/admin/provider-categories"),
  entities: [ProviderCategory],
  auth: true
}

action updateProviderCategory {
  fn: import { updateProviderCategory } from "@src/admin/operations",
  entities: [ProviderCategory],
  auth: true
}

api updateProviderCategoryApi {
  fn: import { handleUpdateProviderCategory } from "@src/admin/apiHandlers",
  httpRoute: (PUT, "/api/auth/admin/provider-categories/:categoryId"),
  entities: [ProviderCategory],
  auth: true
}

action deleteProviderCategory {
  fn: import { deleteProviderCategory } from "@src/admin/operations",
  entities: [ProviderCategory],
  auth: true
}

api deleteProviderCategoryApi {
  fn: import { handleDeleteProviderCategory } from "@src/admin/apiHandlers",
  httpRoute: (DELETE, "/api/auth/admin/provider-categories/:categoryId"),
  entities: [ProviderCategory],
  auth: true
}

// Admin Provider Category Image Management
api uploadCategoryImageApi {
  fn: import { handleUploadCategoryImage } from "@src/admin/categoryApiHandlers",
  httpRoute: (POST, "/api/auth/admin/provider-categories/:categoryId/image"),
  entities: [ProviderCategory, File],
  auth: true
}

api getCategoryImageApi {
  fn: import { handleGetCategoryImage } from "@src/admin/categoryApiHandlers",
  httpRoute: (GET, "/api/auth/admin/provider-categories/:categoryId/image"),
  entities: [ProviderCategory, File],
  auth: true
}

api removeCategoryImageApi {
  fn: import { handleRemoveCategoryImage } from "@src/admin/categoryApiHandlers",
  httpRoute: (DELETE, "/api/auth/admin/provider-categories/:categoryId/image"),
  entities: [ProviderCategory, File],
  auth: true
}

// Admin Advertisement Management
query getAdvertisements {
  fn: import { getAdvertisements } from "@src/admin/operations",
  entities: [Advertisement, File],
  auth: true
}

api getAdvertisementsApi {
  fn: import { handleGetAdvertisements } from "@src/admin/apiHandlers",
  httpRoute: (GET, "/api/auth/admin/advertisements"),
  entities: [Advertisement, File],
  auth: true
}

action createAdvertisement {
  fn: import { createAdvertisement } from "@src/admin/operations",
  entities: [Advertisement, File],
  auth: true
}

api createAdvertisementApi {
  fn: import { handleCreateAdvertisement } from "@src/admin/apiHandlers",
  httpRoute: (POST, "/api/auth/admin/advertisements"),
  entities: [Advertisement, File],
  auth: true
}

action updateAdvertisement {
  fn: import { updateAdvertisement } from "@src/admin/operations",
  entities: [Advertisement, File],
  auth: true
}

api updateAdvertisementApi {
  fn: import { handleUpdateAdvertisement } from "@src/admin/apiHandlers",
  httpRoute: (PUT, "/api/auth/admin/advertisements/:advertisementId"),
  entities: [Advertisement, File],
  auth: true
}

action deleteAdvertisement {
  fn: import { deleteAdvertisement } from "@src/admin/operations",
  entities: [Advertisement],
  auth: true
}

api deleteAdvertisementApi {
  fn: import { handleDeleteAdvertisement } from "@src/admin/apiHandlers",
  httpRoute: (DELETE, "/api/auth/admin/advertisements/:advertisementId"),
  entities: [Advertisement],
  auth: true
}

// Admin Advertisement Image Upload APIs
api uploadAdvertisementBackgroundImageApi {
  fn: import { handleUploadAdvertisementBackgroundImage } from "@src/admin/apiHandlers",
  httpRoute: (POST, "/api/auth/admin/advertisements/:advertisementId/background-image"),
  entities: [Advertisement, File],
  auth: true
}

api uploadAdvertisementPngImageApi {
  fn: import { handleUploadAdvertisementPngImage } from "@src/admin/apiHandlers",
  httpRoute: (POST, "/api/auth/admin/advertisements/:advertisementId/png-image"),
  entities: [Advertisement, File],
  auth: true
}

action extendAppointment {
  fn: import { extendAppointment } from "@src/provider/operations",
  entities: [Appointment, AppointmentHistory, User, CustomerFolder, Service, SProvider, Queue],
  auth: true
}

// --- Subscription Management APIs ---

// Public API to fetch all available subscription plans
api getSubscriptionsApi {
  fn: import { handleGetSubscriptions } from "@src/subscriptions/apiHandlers",
  httpRoute: (GET, "/api/auth/subscriptions"),
  entities: [Subscription],
  auth: false
}

// Authenticated API to fetch current user's subscription and credit balance
api getUserSubscriptionApi {
  fn: import { handleGetUserSubscription } from "@src/subscriptions/apiHandlers",
  httpRoute: (GET, "/api/auth/user/subscription"),
  entities: [User, UserSubscription, Subscription],
  auth: true
}

// Authenticated API to subscribe user to a subscription plan
api subscribeToSubscriptionApi {
  fn: import { handleSubscribeToSubscription } from "@src/subscriptions/apiHandlers",
  httpRoute: (POST, "/api/auth/subscriptions/subscribe"),
  entities: [User, UserSubscription, Subscription],
  auth: true
}

// --- Payment Subscription APIs (LemonSqueezy Integration) ---

// Public API to fetch all available payment plans
api getPaymentPlansApi {
  fn: import { handleGetSubscriptionPlans } from "@src/payment/subscriptionApiHandlers",
  httpRoute: (GET, "/api/auth/payment/plans"),
  auth: false
}

// Authenticated API to get user's current subscription status and credits
api getUserPaymentStatusApi {
  fn: import { handleGetUserSubscriptionStatus } from "@src/payment/subscriptionApiHandlers",
  httpRoute: (GET, "/api/auth/payment/status"),
  entities: [User],
  auth: true
}

// Authenticated API to create checkout session for a payment plan
api createPaymentCheckoutApi {
  fn: import { handleCreateCheckoutSession } from "@src/payment/subscriptionApiHandlers",
  httpRoute: (POST, "/api/auth/payment/checkout"),
  entities: [User],
  auth: true
}

// Authenticated API to get customer portal URL
api getPaymentCustomerPortalApi {
  fn: import { handleGetCustomerPortalUrl } from "@src/payment/subscriptionApiHandlers",
  httpRoute: (GET, "/api/auth/payment/customer-portal"),
  entities: [User],
  auth: true
}

// Authenticated API to get user's usage statistics
api getPaymentUsageStatsApi {
  fn: import { handleGetUsageStats } from "@src/payment/subscriptionApiHandlers",
  httpRoute: (GET, "/api/auth/payment/usage"),
  entities: [User, Appointment, CustomerFolder,Queue],
  auth: true
}

// Authenticated API to get user's used credits for current month
api getUsedCreditsApi {
  fn: import { handleGetUsedCredits } from "@src/payment/subscriptionApiHandlers",
  httpRoute: (GET, "/api/auth/payment/credits/used"),
  entities: [User, Appointment, CustomerFolder],
  auth: true
}

// Authenticated API to get available payment processors for user
api getAvailablePaymentProcessorsApi {
  fn: import { handleGetAvailablePaymentProcessors } from "@src/payment/subscriptionApiHandlers",
  httpRoute: (GET, "/api/auth/payment/processors"),
  entities: [User],
  auth: true
}

// --- Chargily Pay APIs (Algerian Payment Gateway) ---

// Authenticated API to create Chargily customer
api createChargilyCustomerApi {
  fn: import { handleCreateChargilyCustomer } from "@src/payment/chargily/apiHandlers",
  httpRoute: (POST, "/api/auth/payment/chargily/customer"),
  entities: [User],
  auth: true
}

// Authenticated API to get Chargily customer
api getChargilyCustomerApi {
  fn: import { handleGetChargilyCustomer } from "@src/payment/chargily/apiHandlers",
  httpRoute: (GET, "/api/auth/payment/chargily/customer"),
  entities: [User],
  auth: true
}

// Authenticated API to update Chargily customer
api updateChargilyCustomerApi {
  fn: import { handleUpdateChargilyCustomer } from "@src/payment/chargily/apiHandlers",
  httpRoute: (PUT, "/api/auth/payment/chargily/customer"),
  entities: [User],
  auth: true
}

// Authenticated API to delete Chargily customer
api deleteChargilyCustomerApi {
  fn: import { handleDeleteChargilyCustomer } from "@src/payment/chargily/apiHandlers",
  httpRoute: (DELETE, "/api/auth/payment/chargily/customer"),
  entities: [User],
  auth: true
}

// Authenticated API to get or create Chargily customer
api getOrCreateChargilyCustomerApi {
  fn: import { handleGetOrCreateChargilyCustomer } from "@src/payment/chargily/apiHandlers",
  httpRoute: (POST, "/api/auth/payment/chargily/customer/get-or-create"),
  entities: [User],
  auth: true
}

// Authenticated API to create Chargily checkout session
api createChargilyCheckoutApi {
  fn: import { handleCreateChargilyCheckout } from "@src/payment/chargily/apiHandlers",
  httpRoute: (POST, "/api/auth/payment/chargily/checkout"),
  entities: [User, ChargilyPayment],
  auth: true
}

// Authenticated API to create Chargily payment link
api createChargilyPaymentLinkApi {
  fn: import { handleCreateChargilyPaymentLink } from "@src/payment/chargily/apiHandlers",
  httpRoute: (POST, "/api/auth/payment/chargily/payment-links"),
  entities: [User, ChargilyPayment],
  auth: true
}

// Authenticated API to get Chargily payment links and history
api getChargilyPaymentLinksApi {
  fn: import { handleGetChargilyPaymentLinks } from "@src/payment/chargily/apiHandlers",
  httpRoute: (GET, "/api/auth/payment/chargily/payment-links"),
  entities: [User, ChargilyPayment],
  auth: true
}

// Authenticated API to get Chargily payment status
api getChargilyPaymentStatusApi {
  fn: import { handleGetChargilyPaymentStatus } from "@src/payment/chargily/apiHandlers",
  httpRoute: (GET, "/api/auth/payment/chargily/status"),
  entities: [User, ChargilyPayment],
  auth: true
}

// Public API for Chargily webhook (no authentication required)
api chargilyWebhookApi {
  fn: import { chargilyWebhook } from "@src/payment/chargily/webhook",
  entities: [User, ChargilyPayment],
  middlewareConfigFn: import { chargilyMiddlewareConfigFn } from "@src/payment/chargily/webhook",
  httpRoute: (POST, "/api/payment/chargily/webhook"),
  auth: false
}