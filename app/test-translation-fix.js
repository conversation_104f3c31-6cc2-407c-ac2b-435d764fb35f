/**
 * Quick test to verify the translation fix works
 * Tests that the API endpoints don't crash with Prisma client errors
 */

const axios = require('axios');

// Configuration
const BASE_URL = 'https://dalti-test.adscloud.org:8443';
const API_BASE = `${BASE_URL}/api/auth/providers`;

// Helper function to make authenticated requests
async function makeAuthenticatedRequest(method, endpoint, data = null, token) {
  const config = {
    method,
    url: `${API_BASE}${endpoint}`,
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  };

  if (data) {
    config.data = data;
  }

  try {
    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`Error in ${method} ${endpoint}:`, error.response?.data || error.message);
    throw error;
  }
}

// Test function
async function testTranslationFix(authToken) {
  console.log('\n🔧 Testing Translation Fix...\n');

  try {
    // Test 1: Get all locations (should not crash with Prisma errors)
    console.log('📋 Test 1: Getting all locations (checking for Prisma errors)...');
    const getAllResponse = await makeAuthenticatedRequest('GET', '/locations', null, authToken);
    console.log('✅ GET /locations successful - No Prisma client errors');
    console.log('📍 Number of locations:', getAllResponse.data?.length || 0);

    // If there are locations, test getting a single one
    if (getAllResponse.data && getAllResponse.data.length > 0) {
      const firstLocationId = getAllResponse.data[0].id;
      
      console.log('\n🔍 Test 2: Getting single location by ID...');
      const getSingleResponse = await makeAuthenticatedRequest('GET', `/locations/${firstLocationId}`, null, authToken);
      console.log('✅ GET /locations/:id successful - No Prisma client errors');
      console.log('📍 Location details:', {
        id: getSingleResponse.data.id,
        name: getSingleResponse.data.name,
        shortName: getSingleResponse.data.shortName
      });
    } else {
      console.log('ℹ️ No existing locations found, skipping single location test');
    }

    console.log('\n🎉 Translation fix verification completed successfully!');
    console.log('\n📊 Test Summary:');
    console.log('- ✅ No Prisma client access errors');
    console.log('- ✅ Translation functions working correctly');
    console.log('- ✅ API endpoints responding normally');

  } catch (error) {
    console.error('❌ Translation fix test failed:', error.message);
    
    // Check if it's the specific Prisma error we were fixing
    if (error.message.includes("Cannot read properties of undefined (reading 'findUnique')")) {
      console.error('🚨 CRITICAL: The Prisma client access issue is still present!');
      console.error('💡 The fix may not have been applied correctly.');
    }
    
    throw error;
  }
}

// Main execution
async function main() {
  console.log('🚀 Starting Translation Fix Verification Test');
  console.log('🌐 Base URL:', BASE_URL);
  
  // Note: You'll need to provide a valid authentication token
  const authToken = process.env.PROVIDER_AUTH_TOKEN;
  
  if (!authToken) {
    console.error('❌ Error: PROVIDER_AUTH_TOKEN environment variable is required');
    console.log('💡 Usage: PROVIDER_AUTH_TOKEN=your_token_here node test-translation-fix.js');
    process.exit(1);
  }

  try {
    await testTranslationFix(authToken);
    console.log('\n✅ All tests passed - Translation fix is working correctly!');
  } catch (error) {
    console.error('\n❌ Tests failed:', error.message);
    process.exit(1);
  }
}

// Run the tests
if (require.main === module) {
  main();
}

module.exports = { testTranslationFix };
