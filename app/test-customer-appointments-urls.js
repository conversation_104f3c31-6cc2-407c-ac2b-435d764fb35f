#!/usr/bin/env node

/**
 * Test script for Customer Appointment API with proper image URLs
 * 
 * This script tests the enhanced customer appointment API:
 * - GET /api/auth/customer/appointments
 * 
 * Verifies that profile pictures and logos have proper downloadUrl fields
 * 
 * Usage: node test-customer-appointments-urls.js
 */

const BASE_URL = process.env.API_BASE_URL || 'http://localhost:3001';

async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });
    
    const data = await response.json().catch(() => null);
    
    return {
      status: response.status,
      data,
      ok: response.ok
    };
  } catch (error) {
    return {
      status: 0,
      error: error.message,
      ok: false
    };
  }
}

async function testServerConnectivity() {
  console.log('🔗 Testing server connectivity...');
  
  const result = await makeRequest(BASE_URL);
  
  if (result.status === 0) {
    console.log('❌ Cannot connect to server. Make sure the server is running.');
    return false;
  }
  
  console.log(`✅ Server is responding (status: ${result.status})`);
  return true;
}

async function testCustomerLogin() {
  console.log('\n🔐 Testing Customer Login...');
  
  // Try with a known customer account (you may need to adjust credentials)
  const result = await makeRequest(`${BASE_URL}/api/auth/login`, {
    method: 'POST',
    body: JSON.stringify({
      email: '<EMAIL>', // Adjust as needed
      password: 'password123' // This should be the actual customer password
    })
  });
  
  if (result.status === 200 && result.data.sessionId) {
    console.log('✅ Customer login successful!');
    return result.data.sessionId;
  } else if (result.status === 401 || result.status === 400) {
    console.log('⚠️ Customer login failed (check credentials)');
    console.log('Response:', result.data);
    return null;
  } else {
    console.log('❌ Customer login endpoint issue:', result.data || result.error);
    return null;
  }
}

async function testCustomerAppointments(authToken) {
  console.log('\n📅 Testing Customer Appointments API...');
  
  const headers = authToken ? { 'Authorization': `Bearer ${authToken}` } : {};
  
  const result = await makeRequest(`${BASE_URL}/api/auth/customer/appointments`, {
    method: 'GET',
    headers
  });
  
  if (result.status === 200) {
    console.log('✅ Customer appointments fetched successfully');
    
    const appointments = result.data;
    console.log(`📊 Found ${appointments.length} appointments`);
    
    // Analyze the response structure
    let hasProviderInfo = false;
    let hasProfilePictures = false;
    let hasLogos = false;
    let hasDownloadUrls = false;
    
    appointments.forEach((appointment, index) => {
      console.log(`\n📋 Appointment ${index + 1}:`);
      console.log(`   Service: ${appointment.service?.title || 'N/A'}`);
      
      // Check service provider
      const serviceProvider = appointment.service?.provider;
      if (serviceProvider) {
        hasProviderInfo = true;
        console.log(`   Provider: ${serviceProvider.title || 'N/A'}`);
        console.log(`   Provider User: ${serviceProvider.user?.firstName} ${serviceProvider.user?.lastName}`);
        
        // Check profile picture
        if (serviceProvider.user?.profilePicture) {
          hasProfilePictures = true;
          const profilePic = serviceProvider.user.profilePicture;
          console.log(`   Profile Picture: ${profilePic.name}`);
          console.log(`   Profile Picture Key: ${profilePic.key || 'N/A'}`);
          console.log(`   Profile Picture Upload URL: ${profilePic.uploadUrl ? 'Present' : 'Missing'}`);
          console.log(`   Profile Picture Download URL: ${profilePic.downloadUrl ? 'Present' : 'Missing'}`);
          
          if (profilePic.downloadUrl) {
            hasDownloadUrls = true;
            console.log(`   ✅ Download URL generated: ${profilePic.downloadUrl.substring(0, 50)}...`);
          } else {
            console.log(`   ⚠️ No download URL generated`);
          }
        }
        
        // Check logo
        if (serviceProvider.logo) {
          hasLogos = true;
          const logo = serviceProvider.logo;
          console.log(`   Logo: ${logo.name}`);
          console.log(`   Logo Key: ${logo.key || 'N/A'}`);
          console.log(`   Logo Upload URL: ${logo.uploadUrl ? 'Present' : 'Missing'}`);
          console.log(`   Logo Download URL: ${logo.downloadUrl ? 'Present' : 'Missing'}`);
          
          if (logo.downloadUrl) {
            hasDownloadUrls = true;
            console.log(`   ✅ Logo download URL generated: ${logo.downloadUrl.substring(0, 50)}...`);
          } else {
            console.log(`   ⚠️ No logo download URL generated`);
          }
        }
      }
      
      // Check customer folder provider (backup source)
      const folderProvider = appointment.customerFolder?.provider;
      if (folderProvider && !serviceProvider) {
        hasProviderInfo = true;
        console.log(`   Folder Provider: ${folderProvider.title || 'N/A'}`);
      }
    });
    
    // Summary
    console.log('\n📋 API Response Analysis:');
    console.log(`Provider Information: ${hasProviderInfo ? '✅ Present' : '❌ Missing'}`);
    console.log(`Profile Pictures: ${hasProfilePictures ? '✅ Present' : '❌ Missing'}`);
    console.log(`Business Logos: ${hasLogos ? '✅ Present' : '❌ Missing'}`);
    console.log(`Download URLs: ${hasDownloadUrls ? '✅ Generated' : '❌ Missing'}`);
    
    return {
      success: true,
      appointmentCount: appointments.length,
      hasProviderInfo,
      hasProfilePictures,
      hasLogos,
      hasDownloadUrls
    };
    
  } else {
    console.log('❌ Failed to fetch customer appointments:', result.data || result.error);
    return { success: false };
  }
}

async function runTests() {
  console.log('🧪 Customer Appointment API URL Fix Tests');
  console.log('=' .repeat(50));
  
  const results = {};
  
  // Test server connectivity
  results.connectivity = await testServerConnectivity();
  if (!results.connectivity) {
    console.log('\n❌ Cannot proceed without server connectivity');
    return;
  }
  
  // Test customer login
  const authToken = await testCustomerLogin();
  results.customerLogin = !!authToken;
  
  if (!authToken) {
    console.log('\n⚠️ Cannot test appointment API without customer authentication');
    console.log('Please ensure customer credentials are correct');
    return;
  }
  
  // Test customer appointments
  const appointmentResult = await testCustomerAppointments(authToken);
  results.appointmentApi = appointmentResult.success;
  results.appointmentData = appointmentResult;
  
  // Summary
  console.log('\n📋 Test Results Summary:');
  console.log('=' .repeat(50));
  console.log(`Server Connectivity: ${results.connectivity ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Customer Login: ${results.customerLogin ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Appointment API: ${results.appointmentApi ? '✅ PASS' : '❌ FAIL'}`);
  
  if (results.appointmentData) {
    console.log(`Appointments Found: ${results.appointmentData.appointmentCount}`);
    console.log(`Provider Info: ${results.appointmentData.hasProviderInfo ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Profile Pictures: ${results.appointmentData.hasProfilePictures ? '✅ PASS' : '⚠️ NONE'}`);
    console.log(`Business Logos: ${results.appointmentData.hasLogos ? '✅ PASS' : '⚠️ NONE'}`);
    console.log(`Download URLs: ${results.appointmentData.hasDownloadUrls ? '✅ PASS' : '❌ FAIL'}`);
  }
  
  const passCount = Object.values(results).filter(Boolean).length;
  const totalCount = Object.keys(results).length;
  
  console.log('\n🎯 Overall Result:');
  console.log(`${passCount}/${totalCount} core tests passed`);
  
  if (results.appointmentData?.hasDownloadUrls) {
    console.log('🎉 SUCCESS: Download URLs are being generated correctly!');
    console.log('📱 Mobile apps can now display provider images properly.');
  } else if (results.appointmentApi) {
    console.log('⚠️ API works but no download URLs found. This may be normal if no providers have images uploaded.');
  } else {
    console.log('❌ API tests failed. Please check the implementation.');
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { runTests };
