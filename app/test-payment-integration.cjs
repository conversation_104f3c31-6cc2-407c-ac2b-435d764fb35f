#!/usr/bin/env node

/**
 * Complete Payment Integration Test Suite
 * 
 * Tests all payment processors (LemonSqueezy, Stripe, Chargily) to ensure:
 * 1. No regression in existing functionality
 * 2. Proper multi-processor support
 * 3. Complete payment flows work end-to-end
 * 4. Webhook processing works correctly
 * 
 * Usage: node test-payment-integration.cjs [ngrok-url]
 */

require('dotenv').config({ path: '.env.server' });
const crypto = require('crypto');

// Test configuration
const TEST_CONFIG = {
  baseUrl: process.argv[2] || 'http://localhost:3001',
  testUser: {
    email: '<EMAIL>',
    password: 'TestPassword123!',
    name: 'Test Payment User'
  },
  testPlans: ['hobby', 'pro', 'credits10'],
  processors: ['lemonsqueezy', 'chargily'] // Skip stripe for now
};

/**
 * Test user authentication
 */
async function testUserAuth() {
  console.log('👤 Testing user authentication...\n');
  
  try {
    const fetch = (await import('node-fetch')).default;
    
    // Try to login with test user
    const loginResponse = await fetch(`${TEST_CONFIG.baseUrl}/api/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: TEST_CONFIG.testUser.email,
        password: TEST_CONFIG.testUser.password
      })
    });
    
    if (loginResponse.ok) {
      const loginData = await loginResponse.json();
      console.log('✅ User authentication successful');
      return loginData.token || loginData.sessionToken;
    } else {
      console.log('⚠️  Login failed, user might not exist (this is expected for first run)');
      return null;
    }
    
  } catch (error) {
    console.error('❌ Authentication test failed:', error.message);
    return null;
  }
}

/**
 * Test payment processor availability
 */
async function testProcessorAvailability(authToken) {
  console.log('🔍 Testing payment processor availability...\n');
  
  try {
    const fetch = (await import('node-fetch')).default;
    
    const headers = { 'Content-Type': 'application/json' };
    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken}`;
    }
    
    const response = await fetch(`${TEST_CONFIG.baseUrl}/api/auth/payment/processors`, {
      headers
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Payment processors API working');
      console.log('Available processors:', data.data?.processors?.map(p => p.id) || 'Unknown');
      return data.data?.processors || [];
    } else {
      console.error('❌ Payment processors API failed:', response.status);
      return [];
    }
    
  } catch (error) {
    console.error('❌ Processor availability test failed:', error.message);
    return [];
  }
}

/**
 * Test subscription plans API
 */
async function testSubscriptionPlans() {
  console.log('📋 Testing subscription plans API...\n');
  
  try {
    const fetch = (await import('node-fetch')).default;
    
    const response = await fetch(`${TEST_CONFIG.baseUrl}/api/auth/payment/plans`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Subscription plans API working');
      console.log('Available plans:', data.data?.plans?.map(p => p.id) || 'Unknown');
      return data.data?.plans || [];
    } else {
      console.error('❌ Subscription plans API failed:', response.status);
      return [];
    }
    
  } catch (error) {
    console.error('❌ Subscription plans test failed:', error.message);
    return [];
  }
}

/**
 * Test checkout session creation for each processor
 */
async function testCheckoutSessions(authToken, processors) {
  console.log('🛒 Testing checkout session creation...\n');
  
  const results = {};
  
  for (const processor of processors) {
    console.log(`Testing ${processor.id} checkout...`);
    
    try {
      const fetch = (await import('node-fetch')).default;
      
      const headers = { 'Content-Type': 'application/json' };
      if (authToken) {
        headers['Authorization'] = `Bearer ${authToken}`;
      }
      
      const response = await fetch(`${TEST_CONFIG.baseUrl}/api/auth/payment/checkout`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          planId: 'hobby',
          paymentProcessor: processor.id
        })
      });
      
      if (response.ok) {
        const data = await response.json();
        console.log(`✅ ${processor.id} checkout session created`);
        results[processor.id] = { success: true, data };
      } else {
        const errorData = await response.text();
        console.error(`❌ ${processor.id} checkout failed:`, response.status, errorData);
        results[processor.id] = { success: false, error: errorData };
      }
      
    } catch (error) {
      console.error(`❌ ${processor.id} checkout error:`, error.message);
      results[processor.id] = { success: false, error: error.message };
    }
    
    // Wait between requests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  return results;
}

/**
 * Test webhook endpoints
 */
async function testWebhookEndpoints() {
  console.log('🔗 Testing webhook endpoints...\n');
  
  const webhookEndpoints = [
    '/api/payment/lemonsqueezy/webhook',
    '/api/payment/stripe/webhook', 
    '/api/payment/chargily/webhook'
  ];
  
  const results = {};
  
  for (const endpoint of webhookEndpoints) {
    try {
      const fetch = (await import('node-fetch')).default;
      
      const response = await fetch(`${TEST_CONFIG.baseUrl}${endpoint}`, {
        method: 'GET' // Just test if endpoint exists
      });
      
      // 405 Method Not Allowed is expected for webhooks (they only accept POST)
      if (response.status === 405) {
        console.log(`✅ ${endpoint} endpoint exists`);
        results[endpoint] = true;
      } else if (response.status === 404) {
        console.error(`❌ ${endpoint} not found`);
        results[endpoint] = false;
      } else {
        console.log(`⚠️  ${endpoint} unexpected response: ${response.status}`);
        results[endpoint] = true; // Assume it exists
      }
      
    } catch (error) {
      console.error(`❌ ${endpoint} test failed:`, error.message);
      results[endpoint] = false;
    }
  }
  
  return results;
}

/**
 * Test Chargily-specific endpoints
 */
async function testChargilyEndpoints(authToken) {
  console.log('🇩🇿 Testing Chargily-specific endpoints...\n');
  
  const chargilyEndpoints = [
    { method: 'GET', path: '/api/auth/payment/chargily/status', name: 'Payment Status' },
    { method: 'GET', path: '/api/auth/payment/chargily/payment-links', name: 'Payment Links' },
    { method: 'GET', path: '/api/auth/payment/chargily/customer', name: 'Customer Info' }
  ];
  
  const results = {};
  
  for (const endpoint of chargilyEndpoints) {
    try {
      const fetch = (await import('node-fetch')).default;
      
      const headers = { 'Content-Type': 'application/json' };
      if (authToken) {
        headers['Authorization'] = `Bearer ${authToken}`;
      }
      
      const response = await fetch(`${TEST_CONFIG.baseUrl}${endpoint.path}`, {
        method: endpoint.method,
        headers
      });
      
      if (response.ok) {
        console.log(`✅ ${endpoint.name} endpoint working`);
        results[endpoint.path] = true;
      } else if (response.status === 401) {
        console.log(`⚠️  ${endpoint.name} requires authentication (expected)`);
        results[endpoint.path] = true;
      } else {
        console.error(`❌ ${endpoint.name} failed: ${response.status}`);
        results[endpoint.path] = false;
      }
      
    } catch (error) {
      console.error(`❌ ${endpoint.name} test failed:`, error.message);
      results[endpoint.path] = false;
    }
  }
  
  return results;
}

/**
 * Test backward compatibility with existing payment system
 */
async function testBackwardCompatibility(authToken) {
  console.log('🔄 Testing backward compatibility...\n');
  
  const legacyEndpoints = [
    '/api/auth/payment/status',
    '/api/auth/payment/customer-portal'
  ];
  
  const results = {};
  
  for (const endpoint of legacyEndpoints) {
    try {
      const fetch = (await import('node-fetch')).default;
      
      const headers = { 'Content-Type': 'application/json' };
      if (authToken) {
        headers['Authorization'] = `Bearer ${authToken}`;
      }
      
      const response = await fetch(`${TEST_CONFIG.baseUrl}${endpoint}`, {
        headers
      });
      
      if (response.ok || response.status === 401) {
        console.log(`✅ Legacy endpoint ${endpoint} still working`);
        results[endpoint] = true;
      } else {
        console.error(`❌ Legacy endpoint ${endpoint} broken: ${response.status}`);
        results[endpoint] = false;
      }
      
    } catch (error) {
      console.error(`❌ Legacy endpoint ${endpoint} test failed:`, error.message);
      results[endpoint] = false;
    }
  }
  
  return results;
}

/**
 * Generate comprehensive test report
 */
function generateTestReport(results) {
  console.log('\n📊 Payment Integration Test Report\n');
  console.log('=' .repeat(70));
  
  const categories = [
    { name: 'User Authentication', result: results.auth !== null },
    { name: 'Processor Availability', result: results.processors.length > 0 },
    { name: 'Subscription Plans', result: results.plans.length > 0 },
    { name: 'Checkout Sessions', result: Object.values(results.checkouts).some(r => r.success) },
    { name: 'Webhook Endpoints', result: Object.values(results.webhooks).every(r => r) },
    { name: 'Chargily Endpoints', result: Object.values(results.chargily).every(r => r) },
    { name: 'Backward Compatibility', result: Object.values(results.legacy).every(r => r) }
  ];
  
  categories.forEach(category => {
    const status = category.result ? '✅ PASS' : '❌ FAIL';
    console.log(`${category.name.padEnd(25)} ${status}`);
  });
  
  const passedTests = categories.filter(c => c.result).length;
  const totalTests = categories.length;
  const percentage = Math.round((passedTests / totalTests) * 100);
  
  console.log('\n' + '=' .repeat(70));
  console.log(`Overall Status: ${passedTests}/${totalTests} categories passed (${percentage}%)`);
  
  // Detailed results
  console.log('\n📋 Detailed Results:');
  console.log(`Available Processors: ${results.processors.map(p => p.id).join(', ') || 'None'}`);
  console.log(`Available Plans: ${results.plans.map(p => p.id).join(', ') || 'None'}`);
  
  console.log('\nCheckout Results:');
  Object.entries(results.checkouts).forEach(([processor, result]) => {
    const status = result.success ? '✅' : '❌';
    console.log(`  ${status} ${processor}: ${result.success ? 'Success' : result.error}`);
  });
  
  if (percentage >= 80) {
    console.log('\n🎉 Payment integration is working well!');
    console.log('\nRecommendations:');
    console.log('1. Test with real payment credentials');
    console.log('2. Set up webhook testing with ngrok');
    console.log('3. Test complete payment flows in UI');
  } else {
    console.log('\n⚠️  Payment integration needs attention.');
    console.log('\nTroubleshooting:');
    console.log('1. Ensure application is running (wasp start)');
    console.log('2. Check database migrations are up to date');
    console.log('3. Verify environment variables are set');
    console.log('4. Check API handler implementations');
  }
  
  return percentage >= 80;
}

/**
 * Main test runner
 */
async function runPaymentIntegrationTests() {
  console.log('🚀 Payment Integration Test Suite\n');
  console.log('Testing multi-processor payment integration...\n');
  console.log('=' .repeat(70));
  
  const results = {};
  
  try {
    // Test authentication
    results.auth = await testUserAuth();
    
    // Test processor availability
    results.processors = await testProcessorAvailability(results.auth);
    
    // Test subscription plans
    results.plans = await testSubscriptionPlans();
    
    // Test checkout sessions
    results.checkouts = await testCheckoutSessions(results.auth, results.processors);
    
    // Test webhook endpoints
    results.webhooks = await testWebhookEndpoints();
    
    // Test Chargily endpoints
    results.chargily = await testChargilyEndpoints(results.auth);
    
    // Test backward compatibility
    results.legacy = await testBackwardCompatibility(results.auth);
    
  } catch (error) {
    console.error('💥 Error during testing:', error.message);
    results.error = error.message;
  }
  
  // Generate report
  const success = generateTestReport(results);
  
  process.exit(success ? 0 : 1);
}

// Install node-fetch if needed
async function ensureFetch() {
  try {
    await import('node-fetch');
  } catch (error) {
    console.log('Installing node-fetch...');
    const { execSync } = require('child_process');
    execSync('npm install node-fetch@2', { stdio: 'inherit' });
  }
}

// Run tests if script is executed directly
if (require.main === module) {
  ensureFetch().then(() => {
    runPaymentIntegrationTests().catch(error => {
      console.error('💥 Fatal error:', error.message);
      process.exit(1);
    });
  });
}

module.exports = { runPaymentIntegrationTests };
