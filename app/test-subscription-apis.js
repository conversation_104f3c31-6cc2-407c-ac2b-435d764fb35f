#!/usr/bin/env node

/**
 * Test script for Payment Subscription APIs
 * 
 * This script tests all the new subscription API endpoints to ensure they're working correctly.
 * Run this script after starting the Wasp development server.
 * 
 * Usage: node test-subscription-apis.js
 */

const BASE_URL = 'http://localhost:3001'; // Wasp dev server default port

async function testAPI(endpoint, options = {}) {
  try {
    console.log(`\n🧪 Testing: ${options.method || 'GET'} ${endpoint}`);
    
    const response = await fetch(`${BASE_URL}${endpoint}`, {
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      body: options.body ? JSON.stringify(options.body) : undefined
    });

    const data = await response.json();
    
    if (response.ok) {
      console.log(`✅ Success (${response.status}):`, JSON.stringify(data, null, 2));
    } else {
      console.log(`❌ Error (${response.status}):`, JSON.stringify(data, null, 2));
    }
    
    return { success: response.ok, data, status: response.status };
  } catch (error) {
    console.log(`💥 Request failed:`, error.message);
    return { success: false, error: error.message };
  }
}

async function runTests() {
  console.log('🚀 Starting Payment Subscription API Tests...\n');
  console.log('📍 Base URL:', BASE_URL);
  
  // Test 1: Get Payment Plans (Public endpoint)
  await testAPI('/api/auth/payment/plans');
  
  // Test 2: Get User Payment Status (Requires auth - will fail without token)
  await testAPI('/api/auth/payment/status');
  
  // Test 3: Get Used Credits (Requires auth - will fail without token)
  await testAPI('/api/auth/payment/credits/used');

  // Test 4: Get Usage Stats (Requires auth - will fail without token)
  await testAPI('/api/auth/payment/usage');

  // Test 5: Get Customer Portal (Requires auth - will fail without token)
  await testAPI('/api/auth/payment/customer-portal');
  
  // Test 6: Create Checkout Session (Requires auth - will fail without token)
  await testAPI('/api/auth/payment/checkout', {
    method: 'POST',
    body: { planId: 'hobby' }
  });
  
  console.log('\n🏁 Tests completed!');
  console.log('\n📝 Notes:');
  console.log('- The /api/auth/payment/plans endpoint should work (public)');
  console.log('- Other endpoints will return 401 Unauthorized without a valid JWT token');
  console.log('- To test authenticated endpoints, you need to:');
  console.log('  1. Register/login a user through the app');
  console.log('  2. Get the JWT token from localStorage or cookies');
  console.log('  3. Add Authorization: Bearer <token> header to requests');
}

// Run the tests
runTests().catch(console.error);
