/**
 * Manual test script for OTP Test Mode functionality
 * Run this script to test that OTP codes are returned when test mode is enabled
 */

const axios = require('axios');

const API_URL = 'http://localhost:5400'; // Local development server

async function testOtpMode() {
  console.log('🧪 Testing OTP Test Mode Functionality');
  console.log('=====================================');
  console.log('');

  // Generate unique test data
  const timestamp = Date.now();
  const uniqueId = Math.random().toString(36).substring(2, 8);
  
  const testData = {
    email: `otp.test.${timestamp}.${uniqueId}@example.com`,
    phone: `+1${timestamp.toString().slice(-10)}`,
    password: 'TestPassword123!',
    firstName: 'OTP',
    lastName: 'Test',
    businessName: `OTP Test Business ${uniqueId}`,
    providerCategoryId: 1 // Assuming category 1 exists
  };

  console.log('📋 Test Data:');
  console.log(`   Email: ${testData.email}`);
  console.log(`   Phone: ${testData.phone}`);
  console.log(`   Business: ${testData.businessName}`);
  console.log('');

  try {
    // Test 1: Provider Email OTP
    console.log('📧 Test 1: Provider Email OTP Request');
    console.log('------------------------------------');
    
    const emailOtpRequest = {
      email: testData.email,
      firstName: testData.firstName,
      lastName: testData.lastName,
      password: testData.password,
      isProviderRegistration: true,
      providerCategoryId: testData.providerCategoryId,
      businessName: testData.businessName,
      phone: testData.phone
    };

    const emailResponse = await axios.post(
      `${API_URL}/api/auth/request-email-otp`,
      emailOtpRequest,
      {
        timeout: 10000,
        headers: { 'Content-Type': 'application/json' }
      }
    );

    console.log(`✅ Status: ${emailResponse.status}`);
    console.log(`📝 Message: ${emailResponse.data.message}`);
    
    if (emailResponse.data.otp) {
      console.log(`🔐 OTP Code: ${emailResponse.data.otp}`);
      console.log('✅ Test Mode Working: OTP code returned in response');
    } else {
      console.log('❌ Test Mode Not Working: No OTP code in response');
    }

    if (emailResponse.data.providerContext) {
      console.log('✅ Provider Context: Included in response');
    }

    console.log('');

    // Test 2: Provider Phone OTP
    console.log('📱 Test 2: Provider Phone OTP Request');
    console.log('------------------------------------');
    
    const phoneOtpRequest = {
      phoneNumber: testData.phone,
      firstName: testData.firstName,
      lastName: testData.lastName,
      isProviderRegistration: true,
      providerCategoryId: testData.providerCategoryId,
      businessName: testData.businessName
    };

    const phoneResponse = await axios.post(
      `${API_URL}/api/auth/request-otp`,
      phoneOtpRequest,
      {
        timeout: 10000,
        headers: { 'Content-Type': 'application/json' }
      }
    );

    console.log(`✅ Status: ${phoneResponse.status}`);
    console.log(`📝 Message: ${phoneResponse.data.message}`);
    
    if (phoneResponse.data.otp) {
      console.log(`🔐 OTP Code: ${phoneResponse.data.otp}`);
      console.log('✅ Test Mode Working: OTP code returned in response');
    } else {
      console.log('❌ Test Mode Not Working: No OTP code in response');
    }

    if (phoneResponse.data.providerContext) {
      console.log('✅ Provider Context: Included in response');
    }

    console.log('');

    // Test 3: Complete Registration Flow (if email OTP was received)
    if (emailResponse.data.otp) {
      console.log('🔄 Test 3: Complete Registration Flow');
      console.log('------------------------------------');
      
      const registrationData = {
        otp: emailResponse.data.otp,
        identifier: testData.email,
        password: testData.password,
        firstName: testData.firstName,
        lastName: testData.lastName,
        providerCategoryId: testData.providerCategoryId,
        businessName: testData.businessName,
        phone: testData.phone,
        email: testData.email
      };

      try {
        const registrationResponse = await axios.post(
          `${API_URL}/api/auth/provider/verify-otp-register`,
          registrationData,
          {
            timeout: 10000,
            headers: { 'Content-Type': 'application/json' }
          }
        );

        console.log(`✅ Registration Status: ${registrationResponse.status}`);
        console.log(`🎉 Registration Successful!`);
        console.log(`   Session ID: ${registrationResponse.data.sessionId?.substring(0, 20)}...`);
        console.log(`   User ID: ${registrationResponse.data.user?.id}`);
        console.log(`   Provider ID: ${registrationResponse.data.provider?.id}`);
        
      } catch (regError) {
        console.log(`❌ Registration Failed: ${regError.response?.status || regError.message}`);
        if (regError.response?.data) {
          console.log(`   Error: ${regError.response.data.message || JSON.stringify(regError.response.data)}`);
        }
      }
    }

    console.log('');
    console.log('🎯 Test Summary');
    console.log('===============');
    console.log('✅ OTP Test Mode implementation is working correctly!');
    console.log('✅ OTP codes are being returned in test mode responses');
    console.log('✅ Provider context is included in responses');
    console.log('✅ Complete registration flow works with test mode OTP');

  } catch (error) {
    console.error('❌ Test Failed:', error.response?.data || error.message);
    if (error.response?.status) {
      console.error(`   Status: ${error.response.status}`);
    }
    if (error.response?.data) {
      console.error(`   Response: ${JSON.stringify(error.response.data, null, 2)}`);
    }
  }
}

// Check environment variables
console.log('🔧 Environment Check');
console.log('====================');
console.log(`OTP_TEST_MODE: ${process.env.OTP_TEST_MODE || 'not set'}`);
console.log(`NODE_ENV: ${process.env.NODE_ENV || 'not set'}`);
console.log('');

// Run the test
testOtpMode().catch(console.error);
