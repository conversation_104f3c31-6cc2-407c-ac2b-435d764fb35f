/**
 * Debug script to test data generation and OTP request
 */

const { generateProviderData } = require('./tests/integration/utils/testDataGenerators');
const config = require('./tests/integration/config');

console.log('🔍 Debugging Test Data Generation');
console.log('================================');

// Generate test provider data
const providerData = generateProviderData();
console.log('📋 Generated Provider Data:');
console.log(JSON.stringify(providerData, null, 2));

console.log('\n📋 Config Categories:');
console.log(JSON.stringify(config.categories, null, 2));

console.log('\n📋 Expected OTP Request Data:');
const otpRequestData = {
  email: providerData.email,
  firstName: providerData.firstName,
  lastName: providerData.lastName,
  password: providerData.password,
  isProviderRegistration: true,
  providerCategoryId: providerData.providerCategoryId,
  businessName: providerData.businessName,
  phone: providerData.phone
};
console.log(JSON.stringify(otpRequestData, null, 2));

// Validate data manually
console.log('\n🔍 Data Validation:');
console.log(`Email valid: ${/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(providerData.email)}`);
console.log(`Password length: ${providerData.password.length}`);
console.log(`Phone format: ${providerData.phone}`);
console.log(`Business name: ${providerData.businessName}`);
console.log(`Category ID: ${providerData.providerCategoryId}`);
