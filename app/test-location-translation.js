/**
 * Test script for Provider Location Translation APIs
 * Tests the translation functionality for location creation and updates
 */

const axios = require('axios');

// Configuration
const BASE_URL = 'https://dalti-test.adscloud.org:8443';
const API_BASE = `${BASE_URL}/api/auth/providers`;

// Test data
const testLocationData = {
  name: "عيادة الأسنان الحديثة", // Arabic: Modern Dental Clinic
  shortName: "عيادة الأسنان", // Arabic: Dental Clinic
  address: "شارع الاستقلال", // Arabic: Independence Street
  city: "الجزائر", // Arabic: Algiers
  mobile: "+213555123456",
  parking: true,
  elevator: false,
  handicapAccess: true,
  timezone: "Africa/Algiers"
};

const updateLocationData = {
  name: "Clinique Dentaire Moderne", // French: Modern Dental Clinic
  shortName: "Clinique Dentaire", // French: Dental Clinic
  address: "Rue de l'Indépendance", // French: Independence Street
  city: "Alger" // French: Algiers
};

// Helper function to make authenticated requests
async function makeAuthenticatedRequest(method, endpoint, data = null, token) {
  const config = {
    method,
    url: `${API_BASE}${endpoint}`,
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  };

  if (data) {
    config.data = data;
  }

  try {
    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`Error in ${method} ${endpoint}:`, error.response?.data || error.message);
    throw error;
  }
}

// Test functions
async function testLocationTranslation(authToken) {
  console.log('\n🧪 Testing Provider Location Translation APIs...\n');

  try {
    // Test 1: Create location with Arabic text
    console.log('📝 Test 1: Creating location with Arabic text...');
    const createResponse = await makeAuthenticatedRequest('POST', '/locations', testLocationData, authToken);
    console.log('✅ Location created successfully');
    console.log('📍 Created location:', {
      id: createResponse.data.id,
      name: createResponse.data.name,
      shortName: createResponse.data.shortName,
      address: createResponse.data.address,
      city: createResponse.data.city
    });

    const locationId = createResponse.data.id;

    // Test 2: Get all locations (should return translated content based on user's preferred language)
    console.log('\n📋 Test 2: Getting all locations...');
    const getAllResponse = await makeAuthenticatedRequest('GET', '/locations', null, authToken);
    console.log('✅ Locations retrieved successfully');
    console.log('📍 Number of locations:', getAllResponse.data.length);
    
    const createdLocation = getAllResponse.data.find(loc => loc.id === locationId);
    if (createdLocation) {
      console.log('📍 Found created location in list:', {
        id: createdLocation.id,
        name: createdLocation.name,
        shortName: createdLocation.shortName,
        address: createdLocation.address,
        city: createdLocation.city
      });
    }

    // Test 3: Get single location by ID
    console.log('\n🔍 Test 3: Getting single location by ID...');
    const getSingleResponse = await makeAuthenticatedRequest('GET', `/locations/${locationId}`, null, authToken);
    console.log('✅ Single location retrieved successfully');
    console.log('📍 Location details:', {
      id: getSingleResponse.data.id,
      name: getSingleResponse.data.name,
      shortName: getSingleResponse.data.shortName,
      address: getSingleResponse.data.address,
      city: getSingleResponse.data.city
    });

    // Test 4: Update location with French text
    console.log('\n✏️ Test 4: Updating location with French text...');
    const updateResponse = await makeAuthenticatedRequest('PUT', `/locations/${locationId}`, updateLocationData, authToken);
    console.log('✅ Location updated successfully');
    console.log('📍 Updated location:', {
      id: updateResponse.data.id,
      name: updateResponse.data.name,
      shortName: updateResponse.data.shortName,
      address: updateResponse.data.address,
      city: updateResponse.data.city
    });

    // Test 5: Verify translations were saved by getting location again
    console.log('\n🔄 Test 5: Verifying translations after update...');
    const verifyResponse = await makeAuthenticatedRequest('GET', `/locations/${locationId}`, null, authToken);
    console.log('✅ Location verification successful');
    console.log('📍 Final location state:', {
      id: verifyResponse.data.id,
      name: verifyResponse.data.name,
      shortName: verifyResponse.data.shortName,
      address: verifyResponse.data.address,
      city: verifyResponse.data.city
    });

    console.log('\n🎉 All translation tests completed successfully!');
    console.log('\n📊 Test Summary:');
    console.log('- ✅ Location creation with Arabic text');
    console.log('- ✅ Translation storage during creation');
    console.log('- ✅ Translated content retrieval (all locations)');
    console.log('- ✅ Translated content retrieval (single location)');
    console.log('- ✅ Location update with French text');
    console.log('- ✅ Translation storage during update');
    console.log('- ✅ Translation verification after update');

    return locationId;

  } catch (error) {
    console.error('❌ Translation test failed:', error.message);
    throw error;
  }
}

// Main execution
async function main() {
  console.log('🚀 Starting Provider Location Translation API Tests');
  console.log('🌐 Base URL:', BASE_URL);
  
  // Note: You'll need to provide a valid authentication token
  const authToken = process.env.PROVIDER_AUTH_TOKEN;
  
  if (!authToken) {
    console.error('❌ Error: PROVIDER_AUTH_TOKEN environment variable is required');
    console.log('💡 Usage: PROVIDER_AUTH_TOKEN=your_token_here node test-location-translation.js');
    process.exit(1);
  }

  try {
    await testLocationTranslation(authToken);
    console.log('\n✅ All tests passed successfully!');
  } catch (error) {
    console.error('\n❌ Tests failed:', error.message);
    process.exit(1);
  }
}

// Run the tests
if (require.main === module) {
  main();
}

module.exports = { testLocationTranslation };
