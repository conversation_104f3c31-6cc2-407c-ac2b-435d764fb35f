/**
 * Test script for Provider Queues Translation APIs
 * Tests the translation functionality for queue creation, updates, and retrieval
 */

const axios = require('axios');

// Configuration
const BASE_URL = 'https://dalti-test.adscloud.org:8443';
const API_BASE = `${BASE_URL}/api/auth/providers`;

// Test data
const testQueueData = {
  title: "طابور الأسنان العام", // Arabic: General Dental Queue
  sProvidingPlaceId: 1, // You'll need to use an existing location ID
  isActive: true,
  serviceIds: [1], // You'll need to use existing service IDs
  openingHours: [
    {
      dayOfWeek: "Monday",
      isActive: true,
      hours: [
        {
          timeFrom: "09:00",
          timeTo: "17:00"
        }
      ]
    }
  ]
};

const updateQueueData = {
  title: "File d'attente dentaire générale", // French: General Dental Queue
  isActive: true
};

// Helper function to make authenticated requests
async function makeAuthenticatedRequest(method, endpoint, data = null, token) {
  const config = {
    method,
    url: `${API_BASE}${endpoint}`,
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  };

  if (data) {
    config.data = data;
  }

  try {
    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`Error in ${method} ${endpoint}:`, error.response?.data || error.message);
    throw error;
  }
}

// Test functions
async function testQueueTranslation(authToken) {
  console.log('\n🧪 Testing Provider Queues Translation APIs...\n');

  try {
    // Test 1: Get all queues (baseline)
    console.log('📋 Test 1: Getting all provider queues...');
    const initialResponse = await makeAuthenticatedRequest('GET', '/queues', null, authToken);
    console.log('✅ Initial queues retrieved successfully');
    console.log('🔄 Number of existing queues:', initialResponse.data.length);
    
    if (initialResponse.data.length > 0) {
      console.log('🔄 Sample existing queue:', {
        id: initialResponse.data[0].id,
        title: initialResponse.data[0].title,
        isActive: initialResponse.data[0].isActive,
        sProvidingPlaceId: initialResponse.data[0].sProvidingPlaceId
      });
    }

    // Test 2: Create queue with Arabic text
    console.log('\n📝 Test 2: Creating queue with Arabic text...');
    const createResponse = await makeAuthenticatedRequest('POST', '/queues', testQueueData, authToken);
    console.log('✅ Queue created successfully');
    console.log('🔄 Created queue:', {
      id: createResponse.data.id,
      title: createResponse.data.title,
      isActive: createResponse.data.isActive,
      sProvidingPlaceId: createResponse.data.sProvidingPlaceId,
      services: createResponse.data.services
    });

    const queueId = createResponse.data.id;

    // Test 3: Get all queues after creation (should include translated content)
    console.log('\n📋 Test 3: Getting all queues after creation...');
    const afterCreateResponse = await makeAuthenticatedRequest('GET', '/queues', null, authToken);
    console.log('✅ Queues retrieved after creation');
    console.log('🔄 Number of queues:', afterCreateResponse.data.length);
    
    const createdQueue = afterCreateResponse.data.find(queue => queue.id === queueId);
    if (createdQueue) {
      console.log('🔄 Found created queue in list:', {
        id: createdQueue.id,
        title: createdQueue.title,
        isActive: createdQueue.isActive,
        sProvidingPlaceId: createdQueue.sProvidingPlaceId
      });
    }

    // Test 4: Update queue with French text
    console.log('\n✏️ Test 4: Updating queue with French text...');
    const updateResponse = await makeAuthenticatedRequest('PUT', `/queues/${queueId}`, updateQueueData, authToken);
    console.log('✅ Queue updated successfully');
    console.log('🔄 Updated queue:', {
      id: updateResponse.data.id,
      title: updateResponse.data.title,
      isActive: updateResponse.data.isActive,
      sProvidingPlaceId: updateResponse.data.sProvidingPlaceId,
      services: updateResponse.data.services
    });

    // Test 5: Get queues by location (if location ID is available)
    if (testQueueData.sProvidingPlaceId) {
      console.log('\n🏢 Test 5: Getting queues by location...');
      const locationQueuesResponse = await makeAuthenticatedRequest('GET', `/locations/${testQueueData.sProvidingPlaceId}/queues`, null, authToken);
      console.log('✅ Location queues retrieved successfully');
      console.log('🔄 Number of queues at location:', locationQueuesResponse.data.length);
      
      const locationQueue = locationQueuesResponse.data.find(queue => queue.id === queueId);
      if (locationQueue) {
        console.log('🔄 Found queue in location:', {
          id: locationQueue.id,
          title: locationQueue.title,
          isActive: locationQueue.isActive
        });
      }
    }

    // Test 6: Final verification - get all queues again
    console.log('\n🔄 Test 6: Final queue verification...');
    const finalResponse = await makeAuthenticatedRequest('GET', '/queues', null, authToken);
    console.log('✅ Final queue verification successful');
    console.log('🔄 Final queue state:', {
      totalQueues: finalResponse.data.length,
      updatedQueue: finalResponse.data.find(q => q.id === queueId)
    });

    console.log('\n🎉 All translation tests completed successfully!');
    console.log('\n📊 Test Summary:');
    console.log('- ✅ Initial queue retrieval');
    console.log('- ✅ Queue creation with Arabic text');
    console.log('- ✅ Translation storage during creation');
    console.log('- ✅ Translated content retrieval (all queues)');
    console.log('- ✅ Queue update with French text');
    console.log('- ✅ Translation storage during update');
    if (testQueueData.sProvidingPlaceId) {
      console.log('- ✅ Location-based queue retrieval with translations');
    }
    console.log('- ✅ Final queue verification');

    return queueId;

  } catch (error) {
    console.error('❌ Translation test failed:', error.message);
    throw error;
  }
}

// Main execution
async function main() {
  console.log('🚀 Starting Provider Queues Translation API Tests');
  console.log('🌐 Base URL:', BASE_URL);
  
  // Note: You'll need to provide a valid authentication token
  const authToken = process.env.PROVIDER_AUTH_TOKEN;
  
  if (!authToken) {
    console.error('❌ Error: PROVIDER_AUTH_TOKEN environment variable is required');
    console.log('💡 Usage: PROVIDER_AUTH_TOKEN=your_token_here node test-queues-translation.js');
    process.exit(1);
  }

  try {
    await testQueueTranslation(authToken);
    console.log('\n✅ All tests passed successfully!');
  } catch (error) {
    console.error('\n❌ Tests failed:', error.message);
    process.exit(1);
  }
}

// Run the tests
if (require.main === module) {
  main();
}

module.exports = { testQueueTranslation };
