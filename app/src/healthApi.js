// src/healthApi.js
// Basic health check API endpoint implementation for Kubernetes probes

export const healthCheckApi = async (req, res, context) => {
  // This endpoint currently performs no checks and just returns success.
  // Optional: You could add checks here for database connectivity
  // or other critical dependencies if needed.
  // If checks fail, return res.status(503).json({ status: 'error' });

  console.log("Health check API endpoint called successfully.");
  res.status(200).json({ status: "ok" });
}; 