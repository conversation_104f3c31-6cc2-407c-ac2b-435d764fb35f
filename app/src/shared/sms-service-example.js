const path = require('path');
const { SMS } = require('../config/database'); 
const produceMessage = require('../config/Kafka/kafkaProducer');
const pug = require('pug');
const axios = require('axios');
const { logger, operationDurationHistogram, smsSent, DefaultInc } = require('../config/init');
const config = require('../config/config');
class SmsService {
    static async send (language, template, context, isTemplate, content , destinationNumber) {
        let htm;
        let operationStatus = false;
        if(isTemplate == true) {
            const templatePath = path.resolve(`./templates/${template}/${language}/html.pug`);
            const compiledTemplate = pug.compileFile(templatePath);
            htm = compiledTemplate(context);
        } else {
            htm = content;
        }
        const startTime = process.hrtime();
        const url = 'http://yachfin2.ddns.net:8080/message';
        const authUsername = 'sms';
        const authPassword = '5NZ8VdhE';

        const headers = {
            'Content-Type': 'application/json',
        };

        const data = {
            message: htm,
            phoneNumbers: [destinationNumber],
        };



        try {
            const res = await axios.post(url, data, {
                headers,
                auth: { 
                  username: authUsername,
                  password: authPassword,
                },
                timeout: 10000 // Set timeout to 5000 milliseconds (5 seconds)
              });
              
            if(res.status == 202) {
                operationStatus = true;
            }
        } catch (error) {
            console.log(error);
        } 

        const result = await SMS.create({
            destination:destinationNumber,
            content
        });

        const [seconds, nanoseconds] = process.hrtime(startTime);
        const duration = seconds + nanoseconds / 1e9;
        operationDurationHistogram.observe({ namespace:config.serviceNameSpace, operation: 'send_smss_service' }, duration);

        return result && operationStatus;
    }

    static async getSMS( destination ) {
        const result = await SMS.findAll({where:{
            destination
        }})

        return result;
    }

} 


module.exports = SmsService;