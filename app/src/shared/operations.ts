// import { HttpError } from 'wasp/server'
import { type User, type Appointment, type QueueSwapRequest, type AppointmentHistory } from 'wasp/entities'
import { type PrismaClient, type Prisma, Role } from '@prisma/client'
// import prisma from '@src/dbClient'
import { HttpError, prisma } from 'wasp/server';
import dayjs from 'dayjs'
import { z } from 'zod'
import { ensureArgsSchemaOrThrowHttpError } from '../server/validation';
import { createNotificationEntry } from '../notifications/operations';
import { broadcastQueueStateUpdate, getIoInstance } from '../server/webSocket';

const requestQueueSwapInputSchema = z.object({
  appointment1Id: z.number().int().positive() || z.string(),  
  appointment2Id: z.number().int().positive() || z.string(),
  notes: z.string().max(500).optional(),
});

export const requestQueueSwap = async (
  rawArgs: z.infer<typeof requestQueueSwapInputSchema>,
  context: any
): Promise<QueueSwapRequest> => {
  if (!context.user?.id) {
    throw new HttpError(401, "User not authenticated");
  }
  const userId = context.user.id;

  const args = ensureArgsSchemaOrThrowHttpError(requestQueueSwapInputSchema, rawArgs);

  const tt = await prisma.$transaction(async (tx: Prisma.TransactionClient) => {
    // 1. Fetch appointment1 and its relevant details
    const appointment1 = await tx.appointment.findUnique({
      where: { id: args.appointment1Id },
      include: {
        customerFolder: {
          select: { userId: true, sProviderId: true , customer: { select: { id: true , firstName: true, lastName: true } } }
        },
        queue: {
          select: { id: true, sProviderId: true, isActive: true }
        },
      },
    });

    if (!appointment1) {
      throw new HttpError(404, "Original appointment not found.");
    }

    if (appointment1.status === 'canceled' || appointment1.status === 'completed' || appointment1.status === 'noshow') {
      throw new HttpError(400, "Cannot request swap for an appointment that is canceled, completed, or a no-show.");
    }

    if (!appointment1.queue || !appointment1.queue.isActive) {
      throw new HttpError(400, "Appointment is not in an active queue.");
    }

    // 2. Verify authorization
    const isCustomerOfAppt1 = appointment1.customerFolder?.userId === userId;
    // Check if the user is a provider and if that provider owns the queue
    let isProviderOfQueue = false;
    const providerProfile = await tx.sProvider.findUnique({ 
        where: { userId }, 
        select: { id: true } 
    });
    if (providerProfile) {
        isProviderOfQueue = providerProfile.id === appointment1.queue.sProviderId;
    }

    if (!isCustomerOfAppt1 && !isProviderOfQueue) {
      throw new HttpError(403, "Not authorized to request a swap for this appointment.");
    }

    // 3. Find appointment2 (the one immediately following appointment1 in the same queue on the same day)
    const appointment1StartTime = dayjs(appointment1.expectedAppointmentStartTime!)
    const startOfDay = appointment1StartTime.startOf('day').toDate();
    const endOfDay = appointment1StartTime.endOf('day').toDate();

    const appointment2 = await tx.appointment.findFirst({
      where: {
        id: args.appointment2Id,
        queueId: appointment1.queueId,
        expectedAppointmentStartTime: {
          gt: appointment1.expectedAppointmentStartTime!,
          gte: startOfDay, // Ensure it's on the same day
          lt: endOfDay,    // Ensure it's on the same day
        },
        status: { in: ['pending', 'confirmed'] }, // Only swap with active appointments
      },
      orderBy: {
        expectedAppointmentStartTime: 'asc',
      },
      include: {
        customerFolder: { select: { userId: true , customer: { select: { id: true , firstName: true, lastName: true } } } } // For subsequent authorization checks if needed
      }
    });

    if (!appointment2) {
      throw new HttpError(404, "No eligible subsequent appointment found in the queue for swapping.");
    }
    
    // 4. Check for existing active swap requests for either appointment
    const existingSwap = await tx.queueSwapRequest.findFirst({
        where: {
            OR: [
                { appointment1Id: appointment1.id, appointment2Id: appointment2.id },
                { appointment1Id: appointment2.id, appointment2Id: appointment1.id },
            ],
            status: { in: ['pending_customer2_approval', 'pending_provider_approval', 'approved'] } // Consider all non-final states
        }
    });

    if (existingSwap) {
        throw new HttpError(409, "An active swap request already exists involving one of these appointments.");
    }

    // 5. Determine expiration time for the swap request
    let expiresAt;
    const now = dayjs();
    const appt1Start = dayjs(appointment1.expectedAppointmentStartTime!)
    const appt2Start = dayjs(appointment2.expectedAppointmentStartTime!)

    // Default: 1 hour before appt1, but no less than 15 mins from now
    const oneHourBeforeAppt1 = appt1Start.subtract(1, 'hour');
    const fifteenMinFromNow = now.add(15, 'minutes');

    expiresAt = oneHourBeforeAppt1.isAfter(fifteenMinFromNow) ? oneHourBeforeAppt1 : fifteenMinFromNow;

    // Cap expiry at appt1Start or appt2Start (whichever is earlier)
    const earliestApptTime = appt1Start.isBefore(appt2Start) ? appt1Start : appt2Start;
    if (expiresAt.isAfter(earliestApptTime)) {
        expiresAt = earliestApptTime;
    }

    // If, after all calculations, expiresAt is in the past or too soon to act, throw error
    if (expiresAt.isBefore(now.add(5, 'minutes'))) { // 5 min buffer to act
        throw new HttpError(400, "Cannot request a swap, appointment time is too soon or has passed for a valid swap window.");
    }

    // 6. Create the QueueSwapRequest
    const newSwapRequest = await tx.queueSwapRequest.create({
      data: {
        appointment1Id: appointment1.id,
        appointment2Id: appointment2.id,
        requestedById: userId,
        status: 'pending_customer2_approval', 
        expiresAt: expiresAt.toDate(),
        notes: args.notes,
      },
      include: {
        appointment1: {
          include: { queue: { select: { id: true } } }
        },
        appointment2: {
          include: { queue: { select: { id: true } } }
        }
      }
    });

    // TODO: Consider notifications to original requester / provider
    console.log('[requestQueueSwap] newSwapRequest', newSwapRequest , 'sending notifications');
    await createNotificationEntry(tx.notification, {
      userId: appointment1.customerFolder?.userId,
      type: 'NEW_QUEUE_SWAP_REQUEST',
      title: 'New Queue Swap Request',
      message: `A new queue swap request has been made. Please review it at your earliest convenience.`,
      link: `/queueManagement`,
      actorId: userId,
    });

    await createNotificationEntry(tx.notification, {
      userId: appointment2.customerFolder?.userId,
      type: 'NEW_QUEUE_SWAP_REQUEST',
      title: 'New Queue Swap Request',
      message: `${appointment1.customerFolder?.customer?.firstName} ${appointment1.customerFolder?.customer?.lastName} has requested a swap with you. Please review it at your earliest convenience.`,
      link: `/queueManagement`,
      actorId: userId,
    });

    return newSwapRequest;
  });

  const io = getIoInstance();
  if(io) {
    await broadcastQueueStateUpdate( tt?.appointment1?.queue?.id!, context, io);
  } else {
      console.warn('[updateAppointment Operation] Socket.IO instance not available. Cannot notify queue change.');
  }

  return tt;
};

const respondToQueueSwapRequestInputSchema = z.object({
  swapRequestId: z.number().int().positive(),
  accept: z.boolean(),
  notes: z.string().max(500).optional(),
});

export const respondToQueueSwapRequest = async (
  rawArgs: z.infer<typeof respondToQueueSwapRequestInputSchema>,
  context: any
): Promise<QueueSwapRequest> => {
  if (!context.user?.id) {
    throw new HttpError(401, "User not authenticated");
  }
  const userId = context.user.id;

  const args = ensureArgsSchemaOrThrowHttpError(respondToQueueSwapRequestInputSchema, rawArgs);

  const tt = await prisma.$transaction(async (tx: Prisma.TransactionClient) => {
    const swapRequest = await tx.queueSwapRequest.findUnique({
      where: { id: args.swapRequestId },
      include: {
        appointment1: {
          include: { 
            queue: { select: { id: true, sProvidingPlace: { select: { provider: { select: { id: true } } } } } }, 
            service: {select: {title: true}},
            customerFolder: { select: { userId: true, customer: { select: { id: true, firstName: true, lastName: true } } , provider: { select: { userId: true } } } }
          }
      },
      appointment2: {
          include: { 
            queue: { select: { id: true, sProvidingPlace: { select: { provider: { select: { id: true } } } } } }, 
            service: {select: {title: true}},
            customerFolder: { select: { userId: true, customer: { select: { id: true, firstName: true, lastName: true } } } }
          }
      },
      },
    });

    if (!swapRequest) {
      throw new HttpError(404, "Swap request not found.");
    }

    if (swapRequest.appointment2?.customerFolder?.userId !== userId) {
      throw new HttpError(403, "Not authorized to respond to this swap request.");
    }

    if (swapRequest.status !== 'pending_customer2_approval') {
      throw new HttpError(400, `Cannot respond to swap request with status: ${swapRequest.status}.`);
    }

    if (!swapRequest.appointment1 || !swapRequest.appointment2) {
      throw new HttpError(404, "One or both appointments in the swap request not found.");
    }

    if (dayjs(swapRequest.expiresAt).isBefore(dayjs())) {
       // Optionally, update status to 'expired' before throwing
      await tx.queueSwapRequest.update({
        where: { id: swapRequest.id },
        data: { status: 'expired', notes: args.notes || 'Request expired before response.' },
      });
      throw new HttpError(400, "This swap request has expired.");
    }

    let newStatus = '';
    if (args.accept) {
      newStatus = 'approved'; // Provider will finalize
    } else {
      newStatus = 'rejected_by_customer2';
    }

    const updatedSwapRequest = await tx.queueSwapRequest.update({
      where: { id: swapRequest.id },
      data: {
        status: newStatus,
        notes: args.notes, // Update notes regardless of accept/reject
        // respondedById: userId, // Future: Add if tracking responder explicitly on model
      }
    });

    // TODO: Consider notifications to original requester / provider
    if(newStatus === 'approved') {
      const providerUserId = swapRequest.appointment1.customerFolder?.provider?.userId;
      if (!providerUserId) {
        throw new HttpError(404, "Provider not found.");
      }
      // Store original times for history and swapping
      const appt1OldStart = swapRequest.appointment1.expectedAppointmentStartTime;
      const appt1OldEnd = swapRequest.appointment1.expectedAppointmentEndTime;
      const appt1OriginalStatus = swapRequest.appointment1.status;
      const appt1ServiceId = swapRequest.appointment1.serviceId;
      const appt1QueueId = swapRequest.appointment1.queueId;

      const appt2OldStart = swapRequest.appointment2.expectedAppointmentStartTime;
      const appt2OldEnd = swapRequest.appointment2.expectedAppointmentEndTime;
      const appt2OriginalStatus = swapRequest.appointment2.status;
      const appt2ServiceId = swapRequest.appointment2.serviceId;
      const appt2QueueId = swapRequest.appointment2.queueId; 

      // Perform the swap
      await tx.appointment.update({
          where: { id: swapRequest.appointment1Id },
          data: {
              expectedAppointmentStartTime: appt2OldStart,
              expectedAppointmentEndTime: appt2OldEnd,
          },
      });

      await tx.appointment.update({
          where: { id: swapRequest.appointment2Id },
          data: {
              expectedAppointmentStartTime: appt1OldStart,
              expectedAppointmentEndTime: appt1OldEnd,
          },
      });

      // Create AppointmentHistory records
      await tx.appointmentHistory.createMany({
          data: [
              {
                  appointmentId: swapRequest.appointment1Id,
                  changedByUserId: providerUserId,
                  previousStartTime: appt1OldStart!,
                  previousEndTime: appt1OldEnd!,
                  newStartTime: appt2OldStart!,
                  newEndTime: appt2OldEnd!,
                  previousStatus: appt1OriginalStatus,
                  newStatus: appt1OriginalStatus, // Status doesn't change by swap itself
                  changeReason: `Queue position swapped with appt ID ${swapRequest.appointment2Id} (${swapRequest.appointment2.service?.title}). Old time: ${dayjs(appt1OldStart).format('HH:mm')}, New time: ${dayjs(appt2OldStart).format('HH:mm')}.`,
                  previousMotifId: appt1ServiceId,
                  newMotifId: appt1ServiceId,
                  previousAgendaId: appt1QueueId!,
                  newAgendaId: appt1QueueId!,
              },
              {
                  appointmentId: swapRequest.appointment2Id,
                  changedByUserId: providerUserId,
                  previousStartTime: appt2OldStart!,
                  previousEndTime: appt2OldEnd!,
                  newStartTime: appt1OldStart!,
                  newEndTime: appt1OldEnd!,
                  previousStatus: appt2OriginalStatus,
                  newStatus: appt2OriginalStatus,
                  changeReason: `Queue position swapped with appt ID ${swapRequest.appointment1Id} (${swapRequest.appointment1.service?.title}). Old time: ${dayjs(appt2OldStart).format('HH:mm')}, New time: ${dayjs(appt1OldStart).format('HH:mm')}.`,
                  previousMotifId: appt2ServiceId,
                  newMotifId: appt2ServiceId,
                  previousAgendaId: appt2QueueId!,
                  newAgendaId: appt2QueueId!,
              },
          ],
      });

      // Update QueueSwapRequest status
      const finalizedSwapRequest = await tx.queueSwapRequest.update({
          where: { id: args.swapRequestId },
          data: { status: 'completed' }, // Or 'completed_by_provider'
      });

      // TODO: Consider notifications to original requester / provider
      await createNotificationEntry(tx.notification, {
          userId: swapRequest.appointment1.customerFolder?.userId,
          type: 'QUEUE_SWAP_REQUEST_COMPLETED',
          title: 'Queue Swap Request Completed',
          message: `Your queue swap request with ${swapRequest.appointment2.customerFolder?.customer?.firstName} ${swapRequest.appointment2.customerFolder?.customer?.lastName} has been completed.`,
          link: `/queueManagement`,
          actorId: providerUserId,
      });

      await createNotificationEntry(tx.notification, {
          userId: swapRequest.appointment2.customerFolder?.userId,
          type: 'QUEUE_SWAP_REQUEST_COMPLETED',
          title: 'Queue Swap Request Completed',
          message: `Your queue swap request with ${swapRequest.appointment1.customerFolder?.customer?.firstName} ${swapRequest.appointment1.customerFolder?.customer?.lastName} has been completed.`,
          link: `/queueManagement`,
          actorId: providerUserId,
      });

      

      return finalizedSwapRequest;
    }
    return updatedSwapRequest;
  });

  const io = getIoInstance();
  if(io) {
    const srequest = await prisma.queueSwapRequest.findUnique({
      where: { id: args.swapRequestId },
      include: {
        appointment1: { include: { queue: { select: { id: true } } } }
      }
    });
    await broadcastQueueStateUpdate(srequest?.appointment1?.queue?.id!, context, io);
  } else {
      console.warn('[updateAppointment Operation] Socket.IO instance not available. Cannot notify queue change.');
  }

  return tt;
};

const finalizeQueueSwapInputSchema = z.object({
    swapRequestId: z.number().int().positive(),
});

export const finalizeQueueSwap = async (
    rawArgs: z.infer<typeof finalizeQueueSwapInputSchema>,
    context: any
): Promise<QueueSwapRequest> => {
    if (!context.user?.id) {
        throw new HttpError(401, "User not authenticated");
    }
    const providerUserId = context.user.id;

    const args = ensureArgsSchemaOrThrowHttpError(finalizeQueueSwapInputSchema, rawArgs);

    return prisma.$transaction(async (tx: Prisma.TransactionClient) => {
        const swapRequest = await tx.queueSwapRequest.findUnique({
            where: { id: args.swapRequestId },
            include: {
                appointment1: {
                    include: { 
                      queue: { select: { id: true, sProvidingPlace: { select: { provider: { select: { id: true } } } } } }, 
                      service: {select: {title: true}},
                      customerFolder: { select: { userId: true, customer: { select: { id: true, firstName: true, lastName: true } } } }
                    }
                },
                appointment2: {
                    include: { 
                      queue: { select: { id: true, sProvidingPlace: { select: { provider: { select: { id: true } } } } } }, 
                      service: {select: {title: true}},
                      customerFolder: { select: { userId: true, customer: { select: { id: true, firstName: true, lastName: true } } } }
                    }
                },
            },
        });

        if (!swapRequest) {
            throw new HttpError(404, "Swap request not found.");
        }

        if (!swapRequest.appointment1 || !swapRequest.appointment2) {
            throw new HttpError(404, "One or both appointments in the swap request not found.");
        }

        // // Verify provider ownership
        // const providerProfile = await tx.sProvider.findUnique({ 
        //     where: { userId: providerUserId }, 
        //     select: { id: true } 
        // });
        // if (!providerProfile || providerProfile.id !== swapRequest.appointment1.queue?.sProvidingPlace?.provider?.id) {
        //     throw new HttpError(403, "Not authorized to finalize this swap request for this queue.");
        // }

        // if (swapRequest.status !== 'approved') {
        //     throw new HttpError(400, `Cannot finalize swap request with status: ${swapRequest.status}. It must be approved.`);
        // }
        
        // Check if appointments are still swappable
        if (swapRequest.appointment1.status !== 'pending' && swapRequest.appointment1.status !== 'confirmed') {
            throw new HttpError(400, `Appointment ${swapRequest.appointment1.id} (${swapRequest.appointment1.service?.title}) is no longer in a swappable state (${swapRequest.appointment1.status}).`);
        }
        if (swapRequest.appointment2.status !== 'pending' && swapRequest.appointment2.status !== 'confirmed') {
            throw new HttpError(400, `Appointment ${swapRequest.appointment2.id} (${swapRequest.appointment2.service?.title}) is no longer in a swappable state (${swapRequest.appointment2.status}).`);
        }

        // Store original times for history and swapping
        const appt1OldStart = swapRequest.appointment1.expectedAppointmentStartTime;
        const appt1OldEnd = swapRequest.appointment1.expectedAppointmentEndTime;
        const appt1OriginalStatus = swapRequest.appointment1.status;
        const appt1ServiceId = swapRequest.appointment1.serviceId;
        const appt1QueueId = swapRequest.appointment1.queueId;

        const appt2OldStart = swapRequest.appointment2.expectedAppointmentStartTime;
        const appt2OldEnd = swapRequest.appointment2.expectedAppointmentEndTime;
        const appt2OriginalStatus = swapRequest.appointment2.status;
        const appt2ServiceId = swapRequest.appointment2.serviceId;
        const appt2QueueId = swapRequest.appointment2.queueId; 

        // Perform the swap
        await tx.appointment.update({
            where: { id: swapRequest.appointment1Id },
            data: {
                expectedAppointmentStartTime: appt2OldStart,
                expectedAppointmentEndTime: appt2OldEnd,
            },
        });

        await tx.appointment.update({
            where: { id: swapRequest.appointment2Id },
            data: {
                expectedAppointmentStartTime: appt1OldStart,
                expectedAppointmentEndTime: appt1OldEnd,
            },
        });

        // Create AppointmentHistory records
        await tx.appointmentHistory.createMany({
            data: [
                {
                    appointmentId: swapRequest.appointment1Id,
                    changedByUserId: providerUserId,
                    previousStartTime: appt1OldStart!,
                    previousEndTime: appt1OldEnd!,
                    newStartTime: appt2OldStart!,
                    newEndTime: appt2OldEnd!,
                    previousStatus: appt1OriginalStatus,
                    newStatus: appt1OriginalStatus, // Status doesn't change by swap itself
                    changeReason: `Queue position swapped with appt ID ${swapRequest.appointment2Id} (${swapRequest.appointment2.service?.title}). Old time: ${dayjs(appt1OldStart).format('HH:mm')}, New time: ${dayjs(appt2OldStart).format('HH:mm')}.`,
                    previousMotifId: appt1ServiceId,
                    newMotifId: appt1ServiceId,
                    previousAgendaId: appt1QueueId!,
                    newAgendaId: appt1QueueId!,
                },
                {
                    appointmentId: swapRequest.appointment2Id,
                    changedByUserId: providerUserId,
                    previousStartTime: appt2OldStart!,
                    previousEndTime: appt2OldEnd!,
                    newStartTime: appt1OldStart!,
                    newEndTime: appt1OldEnd!,
                    previousStatus: appt2OriginalStatus,
                    newStatus: appt2OriginalStatus,
                    changeReason: `Queue position swapped with appt ID ${swapRequest.appointment1Id} (${swapRequest.appointment1.service?.title}). Old time: ${dayjs(appt2OldStart).format('HH:mm')}, New time: ${dayjs(appt1OldStart).format('HH:mm')}.`,
                    previousMotifId: appt2ServiceId,
                    newMotifId: appt2ServiceId,
                    previousAgendaId: appt2QueueId!,
                    newAgendaId: appt2QueueId!,
                },
            ],
        });

        // Update QueueSwapRequest status
        const finalizedSwapRequest = await tx.queueSwapRequest.update({
            where: { id: args.swapRequestId },
            data: { status: 'completed' }, // Or 'completed_by_provider'
        });

        // TODO: Consider notifications to original requester / provider
        await createNotificationEntry(tx.notification, {
            userId: swapRequest.appointment1.customerFolder?.userId,
            type: 'QUEUE_SWAP_REQUEST_COMPLETED',
            title: 'Queue Swap Request Completed',
            message: `Your queue swap request with ${swapRequest.appointment2.customerFolder?.customer?.firstName} ${swapRequest.appointment2.customerFolder?.customer?.lastName} has been completed.`,
            link: `/queueManagement`,
            actorId: providerUserId,
        });

        await createNotificationEntry(tx.notification, {
            userId: swapRequest.appointment2.customerFolder?.userId,
            type: 'QUEUE_SWAP_REQUEST_COMPLETED',
            title: 'Queue Swap Request Completed',
            message: `Your queue swap request with ${swapRequest.appointment1.customerFolder?.customer?.firstName} ${swapRequest.appointment1.customerFolder?.customer?.lastName} has been completed.`,
            link: `/queueManagement`,
            actorId: providerUserId,
        });

        const io = getIoInstance();
        if(io) {
          await broadcastQueueStateUpdate(swapRequest.appointment1?.queue?.id!, context, io);
          await broadcastQueueStateUpdate(swapRequest.appointment2?.queue?.id!, context, io);
        } else {
            console.warn('[updateAppointment Operation] Socket.IO instance not available. Cannot notify queue change.');
        }

        return finalizedSwapRequest;
    });
};

export const getQueueSwapRequests = async (_args: unknown, context: any): Promise<QueueSwapRequest[]> => {
  if (!context.user?.id) { 
    throw new HttpError(401, "User not authenticated");
  }
  const userId = context.user.id;
  let requestingUserId = userId;
  let providerProfile;
  let whereClause: any = {
    OR: [
      { requestedById: userId },
    ]
  };
//   console.log('[getQueueSwapRequests] test', test[0]?.appointment1?.queue);
  if(context.user.role === Role.CLIENT) {
    console.log('[getQueueSwapRequests] context.user.role === Role.CLIENT');
    providerProfile = await prisma.sProvider.findUnique({ 
        where: { userId }, 
        select: { id: true , userId: true } 
    });
    console.log('[getQueueSwapRequests] providerProfile', providerProfile);
    requestingUserId = providerProfile?.userId;
    whereClause.OR.push({ appointment1: { queue:  { sProvidingPlace:  { provider:  {id: providerProfile?.id}}  } } });
    whereClause.OR.push({ appointment2: { queue:  { sProvidingPlace:  { provider:  {id: providerProfile?.id}}  } } });
  }

  if(context.user.role === Role.CUSTOMER) {
    console.log('[getQueueSwapRequests] context.user.role === Role.CUSTOMER');
    requestingUserId = userId;
    whereClause.OR.push({ appointment1: { customerFolder: { userId: userId } } });
    whereClause.OR.push({ appointment2: { customerFolder: { userId: userId } } });
  }

  const requests = await prisma.queueSwapRequest.findMany({
    where:whereClause,
    include: {
      requestedBy: { select: { id: true, firstName: true, lastName: true, email: true } }, // Include basic details of requester
      appointment1: {
        include: {
          service: { select: { id: true, title: true, duration: true } },
          customerFolder: { include: { customer: { select: { id: true, firstName: true, lastName: true } } } },
          queue: { select: { id: true, title: true } },
          place: { select: { id: true, name: true } }
        }
      },
      appointment2: {
        include: {
          service: { select: { id: true, title: true, duration: true } },
          customerFolder: { include: { customer: { select: { id: true, firstName: true, lastName: true } } } },
          queue: { select: { id: true, title: true } },
          place: { select: { id: true, name: true } }
        }
      }
    },
    orderBy: {
      createdAt: 'desc',
    },
  });

  // console.log('[getQueueSwapRequests] requests', requests);

  return requests;
} 