import { HttpError } from 'wasp/server';
import { 
    requestQueueSwap, 
    respondToQueueSwapRequest 
} from './operations'; // Import existing shared operations
import type { User, Appointment, Queue, CustomerFolder, SProvider, QueueSwapRequest, Notification, AppointmentHistory } from 'wasp/entities';
import type { PrismaClient } from '@prisma/client';
import { z } from 'zod';

// --- API Context Definition ---
// This context will be used by all API handlers in this file.
// It includes all entities potentially needed by the imported operations.
interface ApiContext {
  user?: User; // Wasp automatically populates this if auth: true
  entities: {
    User: PrismaClient['user'];
    Appointment: PrismaClient['appointment'];
    Queue: PrismaClient['queue'];
    CustomerFolder: PrismaClient['customerFolder'];
    SProvider: PrismaClient['sProvider'];
    QueueSwapRequest: PrismaClient['queueSwapRequest'];
    Notification: PrismaClient['notification'];
    AppointmentHistory: PrismaClient['appointmentHistory'];
    // Add other delegates if new operations require them
  }
}

// --- Request Queue Swap API Handler ---

// We can reuse the Zod schema from operations.ts if inputs are identical.
// If API layer needs different validation, define a new schema here.
const requestQueueSwapApiInputSchema = z.object({
  appointment1Id: z.number().int().positive() || z.string(),
  appointment2Id: z.number().int().positive() || z.string(),
  notes: z.string().max(500).optional(),
});

export const handleRequestQueueSwap = async (
  req: { body: z.infer<typeof requestQueueSwapApiInputSchema> },
  res: { status: (code: number) => any; json: (data: any) => void },
  context: ApiContext
): Promise<void> => {
  if (!context.user) {
    res.status(401).json({ error: 'User not authenticated.' });
    return;
  }

  try {
    // Validate request body against the schema
    const validatedArgs = requestQueueSwapApiInputSchema.parse(req.body);

    const swapRequest = await requestQueueSwap(validatedArgs, context);
    res.status(201).json(swapRequest); // 201 Created for new resource
  } catch (e: any) {
    console.error('[handleRequestQueueSwap] Error:', e);
    if (e instanceof z.ZodError) {
      res.status(400).json({ error: 'Invalid input', details: e.errors });
    } else if (e instanceof HttpError) {
      res.status(e.statusCode).json({ error: e.message });
    } else {
      res.status(500).json({ error: 'Failed to request queue swap.' });
    }
  }
};

// --- Respond to Queue Swap API Handler ---

const respondToQueueSwapApiInputSchema = z.object({
  swapRequestId: z.number().int().positive(),
  accept: z.boolean(),
  notes: z.string().max(500).optional(),
});

export const handleRespondToQueueSwap = async (
  req: { body: z.infer<typeof respondToQueueSwapApiInputSchema> },
  res: { status: (code: number) => any; json: (data: any) => void },
  context: ApiContext
): Promise<void> => {
  if (!context.user) {
    res.status(401).json({ error: 'User not authenticated.' });
    return;
  }

  try {
    const validatedArgs = respondToQueueSwapApiInputSchema.parse(req.body);

    const updatedSwapRequest = await respondToQueueSwapRequest(validatedArgs, context);
    res.status(200).json(updatedSwapRequest);
  } catch (e: any) {
    console.error('[handleRespondToQueueSwap] Error:', e);
    if (e instanceof z.ZodError) {
      res.status(400).json({ error: 'Invalid input', details: e.errors });
    } else if (e instanceof HttpError) {
      res.status(e.statusCode).json({ error: e.message });
    } else {
      res.status(500).json({ error: 'Failed to respond to queue swap request.' });
    }
  }
}; 