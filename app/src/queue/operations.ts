import { HttpError, prisma } from 'wasp/server';
import {
    type Queue,
    type Service,
    type QueueOpening,
    type QueueOpeningHours,
    type SProvidingPlace,
    type SProvider,
    type Appointment,
    type User
} from 'wasp/entities';

import { Prisma } from '@prisma/client'; 
import { z } from 'zod';
import { ensureArgsSchemaOrThrowHttpError } from '../server/validation'; // Assuming validation helper exists
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc.js';
import timezone from 'dayjs/plugin/timezone.js';
import { translateAndStore, deleteTranslations } from '../server/translations';
// Extend dayjs with plugins
dayjs.extend(utc);
dayjs.extend(timezone);

// Placeholder types for Wasp generated operations (replace with actual types once generated)
type CreateQueue = any;
type GetQueuesByPlace = any;
type UpdateQueue = any;
type DeleteQueue = any;
type AssignServicesToQueue = any;
type GetQueueOpenings = any;
type UpdateQueueOpenings = any;
type GetQueueAvailability = any;

// CONTEXT TYPES REFINED:
// Minimal context for simple queue queries/actions
type SimpleQueueContext = {
    user?: User & { queues?: number | null }; 
    entities: {
        Queue: any;
        SProvidingPlace: any;
        SProvider: any;
        User: any; // Add User entity delegate
    };
};

// Context for operations involving QueueOpenings relation
type QueueOpeningContext = {
    user?: User;
    entities: {
        Queue: any;
        SProvidingPlace: any;
        SProvider: any;
        QueueOpening: any;
        QueueOpeningHours: any;
    };
};

// Context for operations involving Appointment/Service relations
type FullQueueContext = { // Renamed for clarity
    user?: User;
    entities: {
        QueueOpening: any;
        QueueOpeningHours: any;
        Queue: any;
        SProvidingPlace: any;
        SProvider: any;
        Appointment: any;
        Service: any;
    };
};

// --- Queue CRUD ---

// Input validation schema for creating a queue
const createQueueInputSchema = z.object({
    title: z.string().min(1, "Queue title is required"),
    sProvidingPlaceId: z.number().int().positive("Valid providing place ID is required"),
    isActive: z.boolean().optional(),
    serviceIds: z.array(z.number().int().positive()).min(1, "At least one service must be selected"),
});

type CreateQueueData = z.infer<typeof createQueueInputSchema>;

export const createQueue: CreateQueue = async (rawArgs: any, context: SimpleQueueContext & { entities: { Service: any } }): Promise<Queue> => {
    if (!context.user?.id) {
        throw new HttpError(401, "User not authenticated.");
    }
    const userQueueLimit = context.user.queues;
    if (typeof userQueueLimit !== 'number') { 
        console.error('User queue limit (user.queues) missing or not a number in context.');
        throw new HttpError(500, "Could not determine queue limit for user.");
    }
    const args = ensureArgsSchemaOrThrowHttpError(createQueueInputSchema, rawArgs);
    const providerUserId = context.user.id;
    const provider = await context.entities.SProvider.findUnique({
        where: { userId: providerUserId },
        select: { id: true },
    });
    if (!provider) {
        throw new HttpError(404, 'Service provider profile not found for this user.');
    }
    const currentQueueCount = await context.entities.Queue.count({
        where: {
            sProvidingPlace: {
                sProviderId: provider.id
            }
        }
    });
    if (currentQueueCount >= userQueueLimit) {
        throw new HttpError(403, `Queue creation limit (${userQueueLimit}) reached. Please upgrade your plan to add more queues.`);
    }
    const place = await context.entities.SProvidingPlace.findFirst({
        where: {
            id: args.sProvidingPlaceId,
            sProviderId: provider.id
        },
        select: { id: true }
    });
    if (!place) {
        throw new HttpError(403, "Target providing place not found or does not belong to this provider.");
    }
    if (args.serviceIds.length > 0) {
        const services = await context.entities.Service.findMany({
            where: {
                id: { in: args.serviceIds },
                sProviderId: provider.id 
            },
            select: { id: true }
        });
        if (services.length !== args.serviceIds.length) {
            const foundIds = new Set(services.map((s: any) => s.id));
            const invalidIds = args.serviceIds.filter(id => !foundIds.has(id));
            throw new HttpError(400, `Invalid or unauthorized service IDs provided: ${invalidIds.join(', ')}`);
        }
    }
    const existingQueueWithTitle = await context.entities.Queue.findFirst({
        where: {
            title: args.title,
            sProvidingPlaceId: place.id,
        },
        select: { id: true },
    });
    if (existingQueueWithTitle) {
        throw new HttpError(400, `A queue named "${args.title}" already exists in this location.`);
    }
    try {
        const newQueue = await prisma.$transaction(async (tx) => {
            const created = await tx.queue.create({
                data: {
                    title: args.title,
                    sProvidingPlaceId: place.id,
                    isActive: args.isActive ?? true,
                    services: { 
                        connect: args.serviceIds.map(id => ({ id }))
                    }
                },
                include: { services: { select: { id: true, title: true } } }
            });
            if (created.title) {
                await translateAndStore(
                    context.entities, // Use the global prisma instance from wasps/server
                    String(created.id),
                    'Queue',
                    'title',
                    created.title
                );
            }
            return created;
        });
        return newQueue;
    } catch (error: any) {
        console.error(`Failed to create queue for user ${providerUserId}:`, error);
        if (error.code === 'P2002' && error.meta?.target?.includes('title') && error.meta?.target?.includes('sProvidingPlaceId')) {
             throw new HttpError(400, `A queue named "${args.title}" already exists in this location (concurrent creation?).`);
        }
        throw new HttpError(500, error.message || 'Failed to create queue.');
    }
};

// Schema for getting Queues by Place ID
const getQueuesByPlaceInputSchema = z.object({
    sProvidingPlaceId: z.number().int().positive("Valid providing place ID is required"),
});
type GetQueuesByPlaceData = z.infer<typeof getQueuesByPlaceInputSchema>;

export const getQueuesByPlace: GetQueuesByPlace = async (rawArgs: any, context: SimpleQueueContext): Promise<Queue[]> => {
    if (!context.user) {
        throw new HttpError(401, "User not authenticated.");
    }
    const args = ensureArgsSchemaOrThrowHttpError(getQueuesByPlaceInputSchema, rawArgs);
    const providerUserId = context.user.id;
    const place = await context.entities.SProvidingPlace.findFirst({
        where: {
            id: args.sProvidingPlaceId,
            provider: { userId: providerUserId }
        },
        select: { id: true }
    });
    if (!place) {
        return [];
    }
    const queues = await context.entities.Queue.findMany({
        where: {
            sProvidingPlaceId: args.sProvidingPlaceId,
        },
        include: {
            services: { select: { id: true, title: true } }
        },
        orderBy: { title: 'asc' }
    });
    return queues;
};


// Schema for updating a Queue
const updateQueueInputSchema = z.object({
    queueId: z.number().int().positive(),
    title: z.string().min(1, "Queue title cannot be empty").optional(),
    isActive: z.boolean().optional(),
    serviceIds: z.array(z.number().int().positive()).min(1, "At least one service must be selected").optional(), 
});
type UpdateQueueData = z.infer<typeof updateQueueInputSchema>;

export const updateQueue: UpdateQueue = async (rawArgs: any, context: SimpleQueueContext & { entities: { Service: any } }): Promise<Queue> => {
     if (!context.user) {
        throw new HttpError(401, "User not authenticated.");
    }
    const args = ensureArgsSchemaOrThrowHttpError(updateQueueInputSchema, rawArgs);
    const providerUserId = context.user.id;
    const queueToUpdate = await context.entities.Queue.findFirst({
        where: {
            id: args.queueId,
            sProvidingPlace: { provider: { userId: providerUserId } }
        },
        select: { id: true, sProvidingPlaceId: true, title: true, sProvidingPlace: { select: { sProviderId: true} } } 
    });
    if (!queueToUpdate || !queueToUpdate.sProvidingPlace?.sProviderId) {
        throw new HttpError(404, "Queue not found or you do not have permission to update it.");
    }
    const sProviderId = queueToUpdate.sProvidingPlace.sProviderId;
    if (args.title && args.title !== queueToUpdate.title) {
        const existingQueue = await context.entities.Queue.findFirst({
            where: {
                title: args.title,
                sProvidingPlaceId: queueToUpdate.sProvidingPlaceId,
                id: { not: args.queueId } 
            }
        });
        if (existingQueue) {
            throw new HttpError(400, `Another queue named "${args.title}" already exists in this place.`);
        }
    }
    if (args.serviceIds && args.serviceIds.length > 0) {
        const services = await context.entities.Service.findMany({
            where: {
                id: { in: args.serviceIds },
                sProviderId: sProviderId 
            },
            select: { id: true }
        });
        if (services.length !== args.serviceIds.length) {
            const foundIds = new Set(services.map((s: any) => s.id));
            const invalidIds = args.serviceIds.filter((id: number) => !foundIds.has(id));
            throw new HttpError(400, `Invalid or unauthorized service IDs provided for update: ${invalidIds.join(', ')}`);
        }
    }
    try {
        const updateData: Prisma.QueueUpdateInput = {};
        if (args.title !== undefined) updateData.title = args.title;
        if (args.isActive !== undefined) updateData.isActive = args.isActive;
        if (args.serviceIds !== undefined) {
            updateData.services = {
                set: args.serviceIds.map(id => ({ id: id }))
            };
        }
        if (Object.keys(updateData).length === 0) {
            const currentQueue = await context.entities.Queue.findUnique({ 
                where: { id: args.queueId },
                include: { services: { select: { id: true, title: true } } }
            });
            if (!currentQueue) throw new HttpError(404, "Queue not found."); 
            return currentQueue;
        }
        const updatedQueue = await context.entities.Queue.update({
            where: { id: args.queueId },
            data: updateData,
            include: { services: { select: { id: true, title: true } } }
        });

        if (updatedQueue.title) { // Or args.title if you prefer the input value that was definitely provided
            await translateAndStore(
                context.entities,
                String(updatedQueue.id),
                'Queue',
                'title',
                updatedQueue.title 
            );
        }

        return updatedQueue;
    } catch (error: any) {
         console.error(`Failed to update Queue ${args.queueId} by user ${providerUserId}:`, error);
         if (error instanceof Prisma.PrismaClientKnownRequestError) {
             if (error.code === 'P2002') {
                 throw new HttpError(409, `Another queue named "${args.title ?? queueToUpdate.title}" already exists in this place (concurrent update?).`);
             } else if (error.code === 'P2025') {
                  throw new HttpError(404, 'Queue not found (possibly deleted during update).');
             }
         }
        throw new HttpError(500, error.message || 'Failed to update queue.');
    }
};


// Schema for deleting a Queue
const deleteQueueInputSchema = z.object({
    queueId: z.number().int().positive(),
});
type DeleteQueueData = z.infer<typeof deleteQueueInputSchema>;

export const deleteQueue: DeleteQueue = async (rawArgs: any, context: FullQueueContext): Promise<Queue> => {
     if (!context.user) {
        throw new HttpError(401, "User not authenticated.");
    }
    const args = ensureArgsSchemaOrThrowHttpError(deleteQueueInputSchema, rawArgs);
    const providerUserId = context.user.id;

     // 1. Verify ownership of the queue via its place
    const queueToDelete = await context.entities.Queue.findFirst({
        where: {
            id: args.queueId,
            sProvidingPlace: { provider: { userId: providerUserId } }
        },
         // Include related data needed for checks
        include: {
            appointments: { // Check for non-terminal appointments
                where: { status: { notIn: ['canceled', 'completed', 'noshow'] } },
                select: { id: true },
                take: 1 // We only need to know if at least one exists
            },
             // Include openings to delete them in transaction
             openings: { include: { hours: true } }
        }
    });

    if (!queueToDelete) {
        throw new HttpError(404, "Queue not found or you do not have permission to delete it.");
    }

    // 2. Check for active appointments linked to this queue
    if (queueToDelete.appointments.length > 0) {
        throw new HttpError(400, "Cannot delete queue: It has active or upcoming appointments associated with it.");
    }


    // 3. Delete the Queue and related Openings/Hours within a transaction
    await deleteTranslations(context.entities, String(args.queueId), 'Queue');
    try {
        const deletedQueue = await prisma.$transaction(async (tx: Prisma.TransactionClient) => {
            // Delete related QueueOpeningHours first
            const openingIds = queueToDelete.openings.map((op: any) => op.id);
            if (openingIds.length > 0) {
                await tx.queueOpeningHours.deleteMany({
                    where: { queueOpeningId: { in: openingIds } }
                });
                // Then delete QueueOpenings
                await tx.queueOpening.deleteMany({
                    where: { id: { in: openingIds } }
                });
            }
            
            // Delete the Queue itself
            // Use the context version for consistency if possible, otherwise Prisma direct
            const q = await tx.queue.delete({
                 where: { id: args.queueId },
            });
            return q; // Return the deleted queue data
        });

        return deletedQueue;

    } catch (error: any) {
         console.error(`Failed to delete Queue ${args.queueId} by user ${providerUserId}:`, error);
         if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
             throw new HttpError(404, 'Queue not found (already deleted?).');
         }
        // Handle potential foreign key issues if appointments check wasn't strict enough
        throw new HttpError(500, error.message || 'Failed to delete queue.');
    }
};


// --- Service Assignment ---

const assignServicesToQueueInputSchema = z.object({
    queueId: z.number().int().positive(),
    serviceIds: z.array(z.number().int().positive()).optional().default([]), // Array of service IDs to associate
});
type AssignServicesToQueueData = z.infer<typeof assignServicesToQueueInputSchema>;

export const assignServicesToQueue: AssignServicesToQueue = async (rawArgs: any, context: FullQueueContext): Promise<Queue> => {
     if (!context.user) {
        throw new HttpError(401, "User not authenticated.");
    }
    const args = ensureArgsSchemaOrThrowHttpError(assignServicesToQueueInputSchema, rawArgs);
    const providerUserId = context.user.id;

    // 1. Verify ownership of the queue via its place
    const queue = await context.entities.Queue.findFirst({
        where: {
            id: args.queueId,
            sProvidingPlace: { provider: { userId: providerUserId } }
        },
        select: { id: true, sProvidingPlace: { select: { provider: { select: { id: true }} }} } // Get provider ID
    });

    if (!queue?.sProvidingPlace?.provider?.id) {
        throw new HttpError(404, "Queue not found or you do not have permission to manage its services.");
    }
    const sProviderId = queue.sProvidingPlace.provider.id;

    // 2. Verify all provided service IDs belong to the *same provider*
    if (args.serviceIds.length > 0) {
        const services = await context.entities.Service.findMany({
            where: {
                id: { in: args.serviceIds },
                sProviderId: sProviderId // Ensure services belong to the provider owning the queue
            },
            select: { id: true }
        });

        if (services.length !== args.serviceIds.length) {
             const foundIds = new Set(services.map((s: any) => s.id));
             const invalidIds = args.serviceIds.filter(id => !foundIds.has(id));
             throw new HttpError(400, `Invalid or unauthorized service IDs provided: ${invalidIds.join(', ')}`);
        }
    }

    // 3. Update the many-to-many relationship
    // Using `set` disconnects all existing services and connects only the provided ones.
    try {
        const updatedQueue = await context.entities.Queue.update({
            where: { id: args.queueId },
            data: {
                services: {
                    set: args.serviceIds.map(id => ({ id: id })) // Use the format expected by Prisma `set`
                }
            },
            include: { // Include services to return the updated list
                services: { select: { id: true, title: true } }
            }
        });
        return updatedQueue;
    } catch (error: any) {
         console.error(`Failed to assign services to Queue ${args.queueId} by user ${providerUserId}:`, error);
        if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
             throw new HttpError(404, 'Queue or Service not found during update.');
        }
        throw new HttpError(500, error.message || 'Failed to assign services to queue.');
    }
};


// --- Queue Openings Management ---

// Input schema for getQueueOpenings
const GetQueueOpeningsInputSchema = z.object({
    queueId: z.number().int(),
});
type GetQueueOpeningsInput = z.infer<typeof GetQueueOpeningsInputSchema>;

// Type annotation will be inferred by Wasp
// Return type should implicitly include hours after the change
export const getQueueOpenings = async (args: GetQueueOpeningsInput, context: QueueOpeningContext): Promise<QueueOpening[]> => {
    if (!context.user) throw new HttpError(401);
    const { queueId } = ensureArgsSchemaOrThrowHttpError(GetQueueOpeningsInputSchema, args);

    // Verify user owns the queue (via place -> provider)
    const queue = await context.entities.Queue.findUnique({
        where: { id: queueId },
        select: { 
            sProvidingPlace: { 
                select: { 
                    provider: { select: { userId: true } } 
                }
            }
        }
    });
    if (queue?.sProvidingPlace?.provider?.userId !== context.user.id) {
        throw new HttpError(403, "User not authorized to access openings for this queue.");
    }

    // Fetch openings and explicitly include hours
    return context.entities.QueueOpening.findMany({
        where: { queueId: queueId }, 
        include: {
            hours: { // Explicitly include the hours relation
                orderBy: { timeFrom: 'asc' } // Order hours within each opening
            } 
        },
        // Optional: Order by day if needed (requires custom sorting logic or DB enum)
        // orderBy: { /* Need a way to order days */ }
    });
};


// Schema definitions for updating openings (similar to provider/operations)
const queueOpeningHoursInputSchema = z.object({
    timeFrom: z.string().regex(/^\d{2}:\d{2}$/, "Time must be in HH:mm format"),
    timeTo: z.string().regex(/^\d{2}:\d{2}$/, "Time must be in HH:mm format"),
}).refine(data => data.timeFrom < data.timeTo, {
    message: "'From' time must be before 'To' time",
    path: ["timeTo"], // Attach error to 'timeTo' field
});

const dayQueueOpeningInputSchema = z.object({
    dayOfWeek: z.string(), // e.g., "Monday"
    hours: z.array(queueOpeningHoursInputSchema).optional().default([]),
    isActive: z.boolean().default(true),
});

const updateQueueOpeningsInputSchema = z.object({
    queueId: z.number().int().positive(),
    schedule: z.array(dayQueueOpeningInputSchema), // Array representing the week's schedule
});

type UpdateQueueOpeningsData = z.infer<typeof updateQueueOpeningsInputSchema>;

// Placeholder for updateQueueOpenings
export const updateQueueOpenings: UpdateQueueOpenings = async (
    rawArgs: any,
    context: QueueOpeningContext
): Promise<{ success: boolean }> => {
    if (!context.user) {
        throw new HttpError(401, "User not authenticated.");
    }
    const args = ensureArgsSchemaOrThrowHttpError(updateQueueOpeningsInputSchema, rawArgs);
    const providerUserId = context.user.id;

    // 1. Verify ownership of the queue
    const queue = await context.entities.Queue.findFirst({
        where: {
            id: args.queueId,
            sProvidingPlace: { 
                provider: { userId: providerUserId } 
            }
        },
        select: { id: true }
    });

    if (!queue) {
        throw new HttpError(404, "Queue not found or you do not have permission to update its openings.");
    }

    // 2. Perform updates within a transaction
    try {
        await prisma.$transaction(async (tx: Prisma.TransactionClient) => {
            // Process each day in the provided schedule
            for (const daySchedule of args.schedule) {
                // Find or create the 'regular' QueueOpening record for this day and queue
                // Ensure you use the correct Prisma model names (e.g., tx.queueOpening)
                const opening = await tx.queueOpening.upsert({
                    where: {
                        queueId_dayOfWeek_type: {
                            queueId: args.queueId,
                            dayOfWeek: daySchedule.dayOfWeek,
                            type: 'regular',
                        }
                    },
                    update: {
                        isActive: daySchedule.isActive,
                    },
                    create: {
                        queueId: args.queueId,
                        dayOfWeek: daySchedule.dayOfWeek,
                        type: 'regular',
                        isActive: daySchedule.isActive,
                    },
                });

                // Delete existing hours for this opening
                // Ensure you use the correct Prisma model names (e.g., tx.queueOpeningHours)
                await tx.queueOpeningHours.deleteMany({
                    where: { queueOpeningId: opening.id },
                });

                // Create new hours if the day is active and hours are provided
                if (daySchedule.isActive && daySchedule.hours.length > 0) {
                    const validatedHoursData = daySchedule.hours.map(h => ({
                        queueOpeningId: opening.id,
                        timeFrom: h.timeFrom,
                        timeTo: h.timeTo,
                    }));

                    // Ensure you use the correct Prisma model names (e.g., tx.queueOpeningHours)
                    await tx.queueOpeningHours.createMany({
                        data: validatedHoursData,
                    });
                }
            }
        });

        return { success: true };

    } catch (error: any) {
        console.error(`Failed to update openings for queue ${args.queueId} by user ${providerUserId}:`, error);
        // Handle specific errors like transaction failures if necessary
        if (error instanceof Prisma.PrismaClientValidationError) {
            // Often indicates issues with input data format vs schema expectations
            throw new HttpError(400, `Invalid schedule data provided: ${error.message}`)
        }
        throw new HttpError(500, error.message || 'Failed to update queue opening hours.');
    }
};


// --- Availability Calculation ---

// Input schema for fetching queue availability
const getQueueAvailabilityInputSchema = z.object({
  sProvidingPlaceId: z.number().int(),
  serviceId: z.number().int(),
  startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Start date must be YYYY-MM-DD"),
  endDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "End date must be YYYY-MM-DD"),
}).refine(data => new Date(data.endDate) >= new Date(data.startDate), {
    message: "End date must be after or same as start date",
    path: ["endDate"],
});

type GetQueueAvailabilityInput = z.infer<typeof getQueueAvailabilityInputSchema>;

// Define the structure for individual slots - returning available queue IDs
type AvailabilitySlot = {
    startTime: Date; // Full JS Date object (UTC) for the start of the slot
    availableQueueIds: number[]; // IDs of queues available at this exact start time
};

// Define the daily availability structure
type DailyAvailabilityResult = {
    date: string; // YYYY-MM-DD
    slots: AvailabilitySlot[]; // Array of slot objects for the day
};

// Placeholder for getQueueAvailability
// Remove incorrect generic usage, add explicit types for now
export const getQueueAvailability: GetQueueAvailability = async (
    rawArgs: GetQueueAvailabilityInput, // Use specific input type
    context: FullQueueContext // CHANGED: Use QueueContext
): Promise<DailyAvailabilityResult[]> => {
    const args = ensureArgsSchemaOrThrowHttpError(getQueueAvailabilityInputSchema, rawArgs);

    // Parse input dates as UTC day boundaries
    const startDate = dayjs.utc(args.startDate).startOf('day').toDate();
    const endDate = dayjs.utc(args.endDate).endOf('day').toDate();
    const endLoopDate = dayjs.utc(args.endDate).startOf('day'); // For loop comparison

    if (!dayjs(startDate).isValid() || !dayjs(endDate).isValid()) {
        throw new HttpError(400, "Invalid start or end date format.");
    }

    try {
        // 1. Fetch Service duration
        const service = await context.entities.Service.findUnique({
            where: { id: args.serviceId },
            select: { duration: true },
        });
        if (!service) throw new HttpError(404, "Service not found.");
        const serviceDuration = service.duration; // In minutes

        // 2. Fetch Providing Place Timezone
        const providingPlace = await context.entities.SProvidingPlace.findUnique({
            where: { id: args.sProvidingPlaceId },
            select: { timezone: true },
        });
        if (!providingPlace) throw new HttpError(404, "Providing place not found.");
        const providerTimezone = providingPlace.timezone || 'UTC'; // Use DB value or fallback
        // Basic timezone validation
        try { dayjs.tz('2023-01-01', providerTimezone); } catch (e) {
            console.error(`Invalid timezone '${providerTimezone}' for place ${args.sProvidingPlaceId}. Falling back to UTC.`);
            // providerTimezone = 'UTC'; // Or throw error as below
            throw new HttpError(500, `Invalid timezone configured for the providing place: ${providerTimezone}`);
        }

        // 3. Fetch all active Queues for the place that offer the service
        const relevantQueues = await context.entities.Queue.findMany({
            where: {
                sProvidingPlaceId: args.sProvidingPlaceId,
                isActive: true,
                services: { some: { id: args.serviceId } } // Filter by service offering
            },
            select: { id: true, title: true } // Select needed data
        });
        if (relevantQueues.length === 0) {
            console.log(`No active queues found for place ${args.sProvidingPlaceId} offering service ${args.serviceId}.`);
            return []; // No queues means no availability
        }
        const relevantQueueIds = relevantQueues.map((q: any) => q.id);

        // 4. Fetch Queue Openings for ALL relevant queues in one go
        const queueOpenings = await context.entities.QueueOpening.findMany({
            where: {
                queueId: { in: relevantQueueIds },
                type: 'regular',
                isActive: true,
            },
            include: { hours: { orderBy: { timeFrom: 'asc' } } },
        });

        // Organize openings by queueId and then by dayOfWeek for quick lookup
        const openingsByQueueAndDay: Record<number, Record<string, (QueueOpening & { hours: QueueOpeningHours[] })[]>> = {};
        relevantQueueIds.forEach((qid: number) => { openingsByQueueAndDay[qid] = {}; });
        queueOpenings.forEach((op: any) => {
            if (!openingsByQueueAndDay[op.queueId][op.dayOfWeek]) {
                openingsByQueueAndDay[op.queueId][op.dayOfWeek] = [];
            }
            openingsByQueueAndDay[op.queueId][op.dayOfWeek].push(op);
        });

        // 5. Fetch Appointments for the relevant queues within the date range
        const appointments = await context.entities.Appointment.findMany({
            where: {
                queueId: { in: relevantQueueIds }, // Only appointments for relevant queues
                serviceId: args.serviceId, // Match the service being booked
                status: { notIn: ['canceled', 'noshow'] },
                expectedAppointmentStartTime: { lt: endDate },
                expectedAppointmentEndTime: { gt: startDate },
            },
            select: {
                queueId: true, // Need to know which queue the appointment is for
                expectedAppointmentStartTime: true,
                expectedAppointmentEndTime: true,
            },
            orderBy: { expectedAppointmentStartTime: 'asc' }
        });

        // Organize booked slots by queueId for faster conflict checking
        const bookedSlotsByQueue: Record<number, { start: number, end: number }[]> = {};
        relevantQueueIds.forEach((qid: number) => { bookedSlotsByQueue[qid] = []; });
        appointments.forEach((appt: any) => {
            if (appt.queueId) { // Should always have queueId based on query
                bookedSlotsByQueue[appt.queueId].push({
                    start: appt.expectedAppointmentStartTime!.getTime(), // Milliseconds UTC
                    end: appt.expectedAppointmentEndTime!.getTime(),   // Milliseconds UTC
                });
            }
        });

        // 6. Calculate Availability Day by Day, Slot by Slot
        const availabilityResults: DailyAvailabilityResult[] = [];
        let currentDay = dayjs.utc(startDate);

        while (currentDay.isBefore(endLoopDate) || currentDay.isSame(endLoopDate, 'day')) {
            const isoDateString = currentDay.format('YYYY-MM-DD');
            const dayOfWeekName = currentDay.format('dddd'); // Get UTC day name
            const dailySlotsMap = new Map<number, number[]>(); // Map<startTimeMs, availableQueueIds[]>

            // Iterate through each relevant queue to generate its potential slots for the day
            for (const queueId of relevantQueueIds) {
                const queueDayOpenings = openingsByQueueAndDay[queueId]?.[dayOfWeekName] || [];

                for (const opening of queueDayOpenings) {
                    for (const hours of opening.hours) {
                        // Construct datetime strings and parse in provider's timezone
                        const openingStartStr = `${isoDateString}T${hours.timeFrom}:00`;
                        const openingEndStr = `${isoDateString}T${hours.timeTo}:00`;
                        let openingStartTimeTz = dayjs.tz(openingStartStr, providerTimezone);
                        const openingEndTimeTz = dayjs.tz(openingEndStr, providerTimezone);

                        if (!openingStartTimeTz.isValid() || !openingEndTimeTz.isValid()) continue;

                        let currentSlotStartTimeTz = openingStartTimeTz;
                        while (currentSlotStartTimeTz.add(serviceDuration, 'minutes').isBefore(openingEndTimeTz) || currentSlotStartTimeTz.add(serviceDuration, 'minutes').isSame(openingEndTimeTz)) {
                            const currentSlotEndTimeTz = currentSlotStartTimeTz.add(serviceDuration, 'minutes');
                            const potentialSlotStartDateTime = currentSlotStartTimeTz.toDate(); // UTC Date
                            const potentialSlotEndDateTime = currentSlotEndTimeTz.toDate();   // UTC Date
                            const potentialStartMs = potentialSlotStartDateTime.getTime();
                            const potentialEndMs = potentialSlotEndDateTime.getTime();

                            // --- Conflict Check for THIS queue ---
                            let isBookedForThisQueue = false;
                            const queueBookedSlots = bookedSlotsByQueue[queueId];
                            for (const booked of queueBookedSlots) {
                                if (booked.start < potentialEndMs && booked.end > potentialStartMs) {
                                    isBookedForThisQueue = true;
                                    break;
                                }
                            }

                            // If the slot is NOT booked for THIS queue, add the queueId to the map for this startTime
                            if (!isBookedForThisQueue) {
                                const existingEntry = dailySlotsMap.get(potentialStartMs) || [];
                                existingEntry.push(queueId);
                                dailySlotsMap.set(potentialStartMs, existingEntry);
                            }

                            // Move to the next potential slot start time
                            currentSlotStartTimeTz = currentSlotStartTimeTz.add(serviceDuration, 'minutes');
                        }
                    }
                }
            }

            // Convert the map to the final slot array structure for the day
            const finalDailySlots: AvailabilitySlot[] = [];
            const sortedTimes = Array.from(dailySlotsMap.keys()).sort();
            for (const startTimeMs of sortedTimes) {
                finalDailySlots.push({
                    startTime: new Date(startTimeMs), // Create Date object from UTC milliseconds
                    availableQueueIds: dailySlotsMap.get(startTimeMs)!,
                });
            }

            // Add the day's results if slots were found
            if (finalDailySlots.length > 0) {
                 availabilityResults.push({
                    date: isoDateString,
                    slots: finalDailySlots,
                 });
            }

            // Move to the next day
            currentDay = currentDay.add(1, 'day');
        }

        return availabilityResults;

    } catch (error: any) {
        console.error("Failed to get queue availability:", error);
        if (error instanceof HttpError) {
            throw error; // Re-throw HttpErrors
        }
        throw new HttpError(500, "An unexpected error occurred while fetching availability.");
    }
}; 