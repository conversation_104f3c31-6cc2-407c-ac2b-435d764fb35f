import { HttpError } from 'wasp/server';
import { saveFcmToken } from './fcmActions'; // Import the existing action
import {
    getUserNotifications,
    markNotificationAsRead,
    markAllNotificationsAsRead
} from './operations'; // Import existing notification operations
import { testFirebaseSetup, testSendNotificationToUser } from './testFirebase'; // Import test functions
import type { User, Notification, UserDeviceToken } from 'wasp/entities'; // Ensure Notification is imported
import type { PrismaClient } from '@prisma/client';

// Define the expected request body from the mobile app
interface MobileFcmTokenArgs {
  token: string;
  deviceType: 'android' | 'ios' | 'web'; // Restrict to mobile device types
}

// Define the Wasp API context, which should include the authenticated user
interface ApiContext {
  user?: User; // Wasp automatically populates this if auth: true
  entities: {
    User: PrismaClient['user']; // From fcmActions context
    Notification: PrismaClient['notification']; // Added Notification delegate
    UserDeviceToken: PrismaClient['userDeviceToken']; // From fcmActions context
  }
}

export const handleSaveMobileFcmToken = async (
  req: { body: MobileFcmTokenArgs }, // Express-like request object (Wasp provides this)
  res: { status: (code: number) => any; json: (data: any) => void }, // Express-like response object
  context: ApiContext
): Promise<void> => {
  if (!context.user) {
    // This should ideally be caught by Wasp's auth: true, but good to double-check
    res.status(401).json({ error: 'User not authenticated.' });
    return;
  }

  const { token, deviceType } = req.body;

  console.log('handleSaveMobileFcmToken requested by user:', context.user.firstName+' '+context.user.lastName , 'with token:', token, 'and deviceType:', deviceType);

  if (!token) {
    res.status(400).json({ error: 'FCM token is required.' });
    return;
  }
  if (!deviceType || (deviceType !== 'android' && deviceType !== 'ios' && deviceType !== 'web')) {
    res.status(400).json({ error: 'Valid deviceType (android or ios) is required.' });
    return;
  }

  try {
    // Re-construct the context expected by saveFcmToken if it's slightly different
    // For saveFcmToken, context needs: user, entities.UserDeviceToken
    // Our ApiContext provides context.user and context.entities.UserDeviceToken
    
    const savedToken = await saveFcmToken(
      { token, deviceType },
      context // Pass the ApiContext, which matches the structure fcmActions expects
    );
    res.status(200).json(savedToken);
  } catch (e: any) {
    console.error('[handleSaveMobileFcmToken] Error:', e);
    if (e instanceof HttpError) {
      res.status(e.statusCode).json({ error: e.message });
    } else {
      res.status(500).json({ error: 'Failed to save FCM token.' });
    }
  }
};

// --- Notification List, Mark Read, Mark All Read for Mobile --- 

// Handler for getting user notifications
interface GetNotificationsApiArgs {
    limit?: string; // Query params are strings
    offset?: string;
    unreadOnly?: string;
}

export const handleGetMobileUserNotifications = async (
  req: { query: GetNotificationsApiArgs }, // Wasp provides query params in req.query
  res: { status: (code: number) => any; json: (data: any) => void },
  context: ApiContext
): Promise<void> => {
  if (!context.user) {
    res.status(401).json({ error: 'User not authenticated.' });
    return;
  }
  try {
    const { limit, offset, unreadOnly } = req.query;
    const notifications = await getUserNotifications(
      { 
        limit: limit ? parseInt(limit, 10) : undefined,
        offset: offset ? parseInt(offset, 10) : undefined,
        unreadOnly: unreadOnly === 'true' ? true : unreadOnly === 'false' ? false : undefined,
      },
      context // Context from Wasp API matches what operations expect
    );
    res.status(200).json(notifications);
  } catch (e: any) {
    console.error('[handleGetMobileUserNotifications] Error:', e);
    if (e instanceof HttpError) {
      res.status(e.statusCode).json({ error: e.message });
    } else {
      res.status(500).json({ error: 'Failed to get notifications.' });
    }
  }
};

// Handler for marking a notification as read
interface MarkAsReadApiArgs {
  notificationId: string;
}
export const handleMarkMobileNotificationAsRead = async (
  req: { body: MarkAsReadApiArgs },
  res: { status: (code: number) => any; json: (data: any) => void },
  context: ApiContext
): Promise<void> => {
  if (!context.user) {
    res.status(401).json({ error: 'User not authenticated.' });
    return;
  }
  const { notificationId } = req.body;
  if (!notificationId) {
    res.status(400).json({ error: 'notificationId is required.' });
    return;
  }
  try {
    const updatedNotification = await markNotificationAsRead(
      { notificationId },
      context
    );
    res.status(200).json(updatedNotification);
  } catch (e: any) {
    console.error('[handleMarkMobileNotificationAsRead] Error:', e);
    if (e instanceof HttpError) {
      res.status(e.statusCode).json({ error: e.message });
    } else {
      res.status(500).json({ error: 'Failed to mark notification as read.' });
    }
  }
};

// Handler for marking all notifications as read
export const handleMarkAllMobileNotificationsAsRead = async (
  _req: unknown, // No body or query params needed for this one from client
  res: { status: (code: number) => any; json: (data: any) => void },
  context: ApiContext
): Promise<void> => {
  if (!context.user) {
    res.status(401).json({ error: 'User not authenticated.' });
    return;
  }
  try {
    const result = await markAllNotificationsAsRead(
      {}, // markAllNotificationsAsRead expects an empty object or specific type
      context
    );
    res.status(200).json(result);
  } catch (e: any) {
    console.error('[handleMarkAllMobileNotificationsAsRead] Error:', e);
    if (e instanceof HttpError) {
      res.status(e.statusCode).json({ error: e.message });
    } else {
      res.status(500).json({ error: 'Failed to mark all notifications as read.' });
    }
  }
};

// --- Firebase Testing Handlers ---

// Handler for testing Firebase setup (admin only)
export const handleTestFirebaseSetup = async (
  _req: unknown,
  res: { status: (code: number) => any; json: (data: any) => void },
  context: ApiContext
): Promise<void> => {
  // Check if user is admin (you can modify this check based on your admin logic)
  if (!context.user) {
    res.status(401).json({ error: 'User not authenticated.' });
    return;
  }

  // Optional: Add admin check here
  // if (!context.user.isAdmin) {
  //   res.status(403).json({ error: 'Admin access required.' });
  //   return;
  // }

  try {
    console.log('🔥 Firebase setup test requested by user:', context.user.id);
    const testResult = await testFirebaseSetup();

    if (testResult.success) {
      res.status(200).json({
        success: true,
        message: testResult.message,
        details: testResult.details,
      });
    } else {
      res.status(500).json({
        success: false,
        message: testResult.message,
        details: testResult.details,
      });
    }
  } catch (error: any) {
    console.error('[handleTestFirebaseSetup] Error:', error);
    res.status(500).json({
      success: false,
      message: 'Firebase test failed',
      error: error.message,
    });
  }
};

// Handler for testing notification send to current user
export const handleTestSendNotification = async (
  req: { body: { title?: string; body?: string } },
  res: { status: (code: number) => any; json: (data: any) => void },
  context: ApiContext
): Promise<void> => {
  if (!context.user) {
    res.status(401).json({ error: 'User not authenticated.' });
    return;
  }

  try {
    const { title, body } = req.body || {};
    console.log('🔥 Test notification send requested by user:', context.user.id);

    const testResult = await testSendNotificationToUser(
      context.user.id,
      title || 'Test Notification',
      body || 'This is a test notification from your Firebase backend'
    );

    if (testResult.success) {
      res.status(200).json({
        success: true,
        message: testResult.message,
        details: testResult.details,
      });
    } else {
      res.status(400).json({
        success: false,
        message: testResult.message,
        details: testResult.details,
      });
    }
  } catch (error: any) {
    console.error('[handleTestSendNotification] Error:', error);
    res.status(500).json({
      success: false,
      message: 'Test notification send failed',
      error: error.message,
    });
  }
};