// src/notifications/fcmActions.ts
import { HttpError } from 'wasp/server';
import type { User, UserDeviceToken } from 'wasp/entities'; // Wasp-provided Prisma types
import type { PrismaClient } from '@prisma/client';

type Context = {
  user?: User;
  entities: {
    User: PrismaClient['user'];
    UserDeviceToken: PrismaClient['userDeviceToken'];
  };
};

type SaveFcmTokenArgs = {
  token: string;
  deviceType: 'web' | 'android' | 'ios';
};

export const saveFcmToken = async (
  args: SaveFcmTokenArgs,
  context: Context
): Promise<UserDeviceToken> => {
  if (!context.user) {
    throw new HttpError(401, 'User not authenticated.');
  }
  if (!args.token) {
    throw new HttpError(400, 'FCM token is required.');
  }
  if (!args.deviceType) {
    throw new HttpError(400, 'Device type is required.');
  }

  // Check if this token already exists for any user to prevent duplicates if desired,
  // or just upsert for the current user.
  // Upsert is good: if token exists for this user, update its updatedAt. If not, create.
  const existingToken = await context.entities.UserDeviceToken.findUnique({
    where: { token: args.token },
  });

  if (existingToken && existingToken.userId !== context.user.id) {
    // Token belongs to another user. This is unusual.
    // Decide on policy: delete old one? block? For now, let's throw an error.
    console.warn(`FCM token ${args.token} already registered to another user ${existingToken.userId}. Current user ${context.user.id}`);
    throw new HttpError(409, "This device is already registered to another user.");
  }
  
  if (existingToken && existingToken.userId === context.user.id) {
    // Token already exists for this user, just update timestamp
    return context.entities.UserDeviceToken.update({
        where: { id: existingToken.id },
        data: { updatedAt: new Date() } // Keep it active
    });
  }

  // Create new token entry
  return context.entities.UserDeviceToken.create({
    data: {
      userId: context.user.id,
      token: args.token,
      deviceType: args.deviceType,
    },
  });
};