// src/notifications/testFirebase.ts
import admin from '../server/firebaseAdmin';
import { getMessaging } from 'firebase-admin/messaging';
import { prisma } from 'wasp/server';

/**
 * Test function to verify Firebase Admin SDK is properly configured
 * This function checks:
 * 1. Firebase Admin SDK initialization
 * 2. Firebase Cloud Messaging service availability
 * 3. Basic message structure validation
 */
export const testFirebaseSetup = async (): Promise<{
  success: boolean;
  message: string;
  details?: any;
}> => {
  try {
    console.log('🔥 Testing Firebase Admin SDK setup...');

    // 1. Check if Firebase Admin SDK is initialized
    if (!admin.apps.length) {
      return {
        success: false,
        message: 'Firebase Admin SDK is not initialized. Check your FIREBASE_ADMIN_SDK_CONFIG environment variable.',
      };
    }

    console.log('✅ Firebase Admin SDK is initialized');

    // 2. Check if we can access Firebase Cloud Messaging
    const messaging = getMessaging(admin.app());
    if (!messaging) {
      return {
        success: false,
        message: 'Firebase Cloud Messaging service is not available.',
      };
    }

    console.log('✅ Firebase Cloud Messaging service is available');

    // 3. Test message structure (without actually sending)
    const testMessage = {
      notification: {
        title: 'Test Notification',
        body: 'This is a test notification from your backend',
      },
      data: {
        test: 'true',
        timestamp: new Date().toISOString(),
      },
      // We'll use a dummy token for structure validation
      token: 'dummy-token-for-testing',
    };

    // Validate message structure (this won't send, just validates)
    try {
      // This will throw an error due to invalid token, but that's expected
      // We're just testing if the message structure is valid
      await messaging.send(testMessage);
    } catch (error: any) {
      // Expected error for dummy token - this is actually good!
      if (error.code === 'messaging/registration-token-not-registered' || 
          error.code === 'messaging/invalid-registration-token') {
        console.log('✅ Message structure is valid (dummy token rejected as expected)');
      } else {
        // Unexpected error - might be configuration issue
        console.error('❌ Unexpected error during message validation:', error);
        return {
          success: false,
          message: 'Firebase messaging configuration error',
          details: {
            errorCode: error.code,
            errorMessage: error.message,
          },
        };
      }
    }

    // 4. Check database connection for FCM tokens
    try {
      const tokenCount = await prisma.userDeviceToken.count();
      console.log(`✅ Database connection OK. Found ${tokenCount} FCM tokens in database`);
    } catch (error: any) {
      console.error('❌ Database connection error:', error);
      return {
        success: false,
        message: 'Database connection error when checking FCM tokens',
        details: {
          errorMessage: error.message,
        },
      };
    }

    return {
      success: true,
      message: 'Firebase backend setup is fully functional! 🎉',
      details: {
        projectId: admin.app().options.projectId,
        serviceAccount: admin.app().options.credential ? 'Configured' : 'Not configured',
        messagingAvailable: true,
        databaseConnected: true,
      },
    };

  } catch (error: any) {
    console.error('❌ Firebase setup test failed:', error);
    return {
      success: false,
      message: 'Firebase setup test failed',
      details: {
        errorMessage: error.message,
        errorStack: error.stack,
      },
    };
  }
};

/**
 * Test function to send a real notification to a specific user
 * Use this only after testFirebaseSetup() passes
 */
export const testSendNotificationToUser = async (
  userId: string,
  title: string = 'Test Notification',
  body: string = 'This is a test notification from your Firebase backend'
): Promise<{
  success: boolean;
  message: string;
  details?: any;
}> => {
  try {
    console.log(`🔥 Testing notification send to user: ${userId}`);

    // Check if user has FCM tokens
    const userTokens = await prisma.userDeviceToken.findMany({
      where: { userId: userId },
    });

    if (userTokens.length === 0) {
      return {
        success: false,
        message: `User ${userId} has no FCM tokens registered. Cannot send test notification.`,
      };
    }

    const tokens = userTokens.map(t => t.token);
    console.log(`Found ${tokens.length} FCM tokens for user ${userId}`);

    // Send test notification
    const messaging = getMessaging(admin.app());
    const message = {
      notification: {
        title: title,
        body: body,
      },
      data: {
        test: 'true',
        timestamp: new Date().toISOString(),
        userId: userId,
      },
      tokens: tokens,
    };

    const response = await messaging.sendEachForMulticast(message);
    
    console.log(`✅ Notification sent! Success: ${response.successCount}, Failures: ${response.failureCount}`);

    return {
      success: response.successCount > 0,
      message: `Notification test completed. Sent to ${response.successCount}/${tokens.length} devices.`,
      details: {
        successCount: response.successCount,
        failureCount: response.failureCount,
        totalTokens: tokens.length,
        responses: response.responses.map((resp, index) => ({
          token: tokens[index].substring(0, 20) + '...', // Truncate for security
          success: resp.success,
          error: resp.error?.message || null,
        })),
      },
    };

  } catch (error: any) {
    console.error('❌ Test notification send failed:', error);
    return {
      success: false,
      message: 'Test notification send failed',
      details: {
        errorMessage: error.message,
      },
    };
  }
};
