// File: app/src/notifications/operations.ts
import { HttpError, prisma } from 'wasp/server';
import type { User, Notification } from 'wasp/entities'; // Wasp-provided Prisma types
import type { PrismaClient } from '@prisma/client'; // For Prisma.TransactionClient and delegate types
import { sendPushNotificationToUser } from './fcmSender';

// Standard Wasp context often looks like this, specific entities are typed by Wasp
type Context = {
  user?: User; // The authenticated user, if any
  entities: {
    Notification: PrismaClient['notification']; // Delegate for Notification
    User: PrismaClient['user']; // Delegate for User, useful for fetching actor details if needed
    // Add other entity delegates here if your functions need them
  };
  // Other context properties Wasp might provide
};

// Type for the Prisma Transaction Client passed within prisma.$transaction
// This helps type the 'tx' object used in transactions.
type PrismaTransactionClient = Omit<PrismaClient, '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'>;

// Internal helper function to create a notification entry
export const createNotificationEntry = async (
  // Accepts either the main Prisma delegate (context.entities.Notification)
  // or the transactional Prisma delegate (tx.notification)
  notificationPrismaDelegate: PrismaClient['notification'] | PrismaTransactionClient['notification'],
  data: {
    userId: string;
    type: string;
    title: string;
    message: string;
    link?: string;
    actorId?: string;
  }
): Promise<Notification> => { // Returns Wasp's Notification entity type
  const createdNotification = await notificationPrismaDelegate.create({
    data: {
      userId: data.userId,
      type: data.type,
      title: data.title,
      message: data.message,
      link: data.link,
      actorId: data.actorId,
    },
  });
  if (createdNotification) {
    console.log('Sending push notification to user:', createdNotification.userId);
    await sendPushNotificationToUser(
      prisma, // Global prisma
      createdNotification.userId,
      createdNotification.title,
      createdNotification.message,
      createdNotification.link
    );
  }

  return createdNotification;
};

// Wasp Query: Get user notifications
export const getUserNotifications = async (
  args: { limit?: number; offset?: number; unreadOnly?: boolean },
  context: Context
): Promise<Notification[]> => {
  if (!context.user) {
    throw new HttpError(401, 'User not authenticated');
  }

  const { limit = 20, offset = 0, unreadOnly = false } = args;

  const notifications = await context.entities.Notification.findMany({
    where: {
      userId: context.user.id,
      ...(unreadOnly && { isRead: false }),
    },
    orderBy: { createdAt: 'desc' },
    take: limit,
    skip: offset,
    include: { 
      actor: { // actor is a relation to User on the Notification model
        select: {
          id: true,
          username: true, // Or other identifying fields like firstName/lastName
          firstName: true,
          lastName: true,
        }
      }
    }
  });
  return notifications;
};

// Wasp Action: Mark a single notification as read
export const markNotificationAsRead = async (
  args: { notificationId: string }, // For actions called from client, Zod schema is good practice
  context: Context
): Promise<Notification> => {
  if (!context.user) {
    throw new HttpError(401, 'User not authenticated');
  }

  const notification = await context.entities.Notification.findUnique({
    where: { id: args.notificationId },
  });

  if (!notification) {
    throw new HttpError(404, 'Notification not found');
  }

  if (notification.userId !== context.user.id) {
    throw new HttpError(403, 'User not authorized to update this notification');
  }

  if (notification.isRead) {
    return notification; 
  }

  return context.entities.Notification.update({
    where: { id: args.notificationId },
    data: { isRead: true, readAt: new Date() },
  });
};

// Wasp Action: Mark all unread notifications for a user as read
export const markAllNotificationsAsRead = async (
  _args: unknown, // No arguments from client needed for this one
  context: Context
): Promise<{ count: number }> => {
  if (!context.user) {
    throw new HttpError(401, 'User not authenticated');
  }

  const result = await context.entities.Notification.updateMany({
    where: {
      userId: context.user.id,
      isRead: false,
    },
    data: { isRead: true, readAt: new Date() },
  });

  return { count: result.count }; // Returns the number of notifications updated
};