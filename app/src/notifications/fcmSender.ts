// src/notifications/fcmSender.ts
import admin from '../server/firebaseAdmin'; // Your initialized Firebase Admin SDK
import { getMessaging } from 'firebase-admin/messaging'; // Import getMessaging
import { PrismaClient } from '@prisma/client'; // For type hints

// This function would typically be called from your Wasp actions
// after a notification is created and saved to your database.
export const sendPushNotificationToUser = async (
  prisma: PrismaClient, // Pass Prisma client for DB access
  userId: string,
  notificationTitle: string,
  notificationBody: string,
  notificationLink?: string | null, // The deep link for the notification
  // You can add more specific payload data if needed by clients
  customData?: { [key: string]: string } 
) => {
  if (!admin.apps.length) {
    console.error("Firebase Admin SDK not initialized. Push notification cannot be sent.");
    return;
  }

  try {
    // 1. Get FCM tokens for the user
    const userDeviceTokens = await prisma.userDeviceToken.findMany({
      where: { userId: userId },
    });


    if (userDeviceTokens.length === 0) {
      console.log(`No FCM tokens found for user ${userId}. Cannot send push notification.`);
      return;
    }

    const tokens = userDeviceTokens.map(dt => dt.token);

    // 2. Construct the message payload
    // For web push, the 'notification' and 'webpush.fcmOptions.link' fields are standard.
    // For mobile, payload structure might differ (e.g., 'data' field is often preferred).
    const messagePayload: admin.messaging.MulticastMessage = {
      notification: {
        title: notificationTitle,
        body: notificationBody,
        // icon: '/path/to/your/icon.png', // Optional: default icon can be set in manifest or service worker
      },
      webpush: { // Web-specific options
        notification: { // You can override or add web-specific notification fields
            // icon: '/custom-web-icon.png',
            // You can add actions, badges, etc. here:
            // https://firebase.google.com/docs/cloud-messaging/js/send-multiple#send_messages_to_multiple_devices
        },
        fcmOptions: {
          link: notificationLink || '/', // Link to open when notification is clicked
        },
      },
      data: { // Custom key-value pairs, accessible by clients (web and mobile)
        click_action: notificationLink || '/', // Standard for some platforms
        link: notificationLink || '/',
        ...(customData || {}), // Spread any additional custom data
        // Example: 'notificationId': 'some-uuid-from-your-db'
      },
      tokens: tokens, // Array of registration tokens
    };

    // 3. Send the message
    if (tokens.length > 0) {
      console.log(`Sending FCM message to user ${userId} with tokens:`, tokens);
      const response = await getMessaging(admin.app()).sendEachForMulticast(messagePayload);
      console.log('Successfully sent message:', response.successCount, 'successes,', response.failureCount, 'failures');

      if (response.failureCount > 0) {
        const tokensToDelete: string[] = [];
        for (let i = 0; i < response.responses.length; i++) {
          const resp = response.responses[i];
          if (!resp.success) {
            const token = tokens[i];
            console.error(`Failed to send to token ${token}:`, resp.error);
            // Handle failed tokens:
            // - If error indicates token is unregistered (e.g., 'messaging/registration-token-not-registered'),
            //   you should delete that token from your database.
            if (resp.error && (resp.error.code === 'messaging/registration-token-not-registered' || 
                               resp.error.code === 'messaging/invalid-registration-token')) {
              console.log(`Marking stale FCM token for deletion: ${token}`);
              tokensToDelete.push(token);
            }
          }
        }
        if (tokensToDelete.length > 0) {
          console.log('Deleting stale FCM tokens:', tokensToDelete);
          await prisma.userDeviceToken.deleteMany({
            where: {
              token: {
                in: tokensToDelete,
              },
            },
          });
        }
      }
    }
  } catch (error) {
    console.error('Error sending push notification:', error);
  }
};