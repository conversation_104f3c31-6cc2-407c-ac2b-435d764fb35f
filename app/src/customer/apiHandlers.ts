import { HttpError } from 'wasp/server';
import { type Request, type Response } from 'express';
import { getCustomerAppointments } from 'wasp/server/operations';
import { type Appointment } from 'wasp/entities';
import {
  createCustomerAddress,
  getCustomerAddresses,
  updateCustomerAddress,
  deleteCustomerAddress,
  getPublicProviderById,
} from './operations'; // Assuming operations are in the same directory or adjust path
import { z } from 'zod';
import { type User, type Address } from 'wasp/entities';
import { Role } from '@prisma/client';

export const handleGetCustomerAppointments = async (req: Request, res: Response, context: any) => {
  // Authentication is handled by <PERSON><PERSON> based on `auth: true` in main.wasp
  // context.user should be populated.
  if (!context.user) {
    // This is a safeguard, <PERSON><PERSON> should prevent unauthenticated access.
    return res.status(401).json({ message: "User not authenticated." });
  }

  try {
    // The getCustomerAppointments query takes only context as an argument when called directly.
    const appointments: Appointment[] = await getCustomerAppointments(context);
    res.status(200).json(appointments);
  } catch (error: any) {
    console.error("[API] Failed to get customer appointments:", error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error instanceof HttpError ? error.message : 'Failed to retrieve customer appointments.';
    res.status(statusCode).json({ message });
  }
};

// Helper to get authenticated customer user from context
const getAuthenticatedCustomer = (context: any): User => {
  if (!context.user || context.user.role !== Role.CUSTOMER) {
    throw new HttpError(401, 'User not authenticated or not a customer.');
  }
  return context.user as User;
};

// --- API Handler Schemas (mirroring operation schemas) ---
const addressInputSchemaForApi = z.object({
  address: z.string().min(1),
  city: z.string().min(1),
  state: z.string().optional(),
  postalCode: z.string().min(1),
  country: z.string().min(1).default("Algeria"),
  latitude: z.number({ coerce: true }),
  longitude: z.number({ coerce: true }),
  description: z.string().optional().nullable(),
  isPrimary: z.boolean().default(false),
});

const updateAddressInputSchemaForApi = addressInputSchemaForApi.extend({
  // addressId will come from URL parameter for typical REST, but here we'll expect it in body for simplicity with Wasp actions
  // If using pure REST, addressId would be a path param e.g. /api/auth/customer/addresses/:addressId
});

// --- Get Customer Addresses Handler ---
export const handleGetCustomerAddresses = async (req: Request, res: Response, context: any) => {
  try {
    getAuthenticatedCustomer(context); // Ensures user is an authenticated customer
    const addresses = await getCustomerAddresses({}, context); // Pass empty args, operation handles user from context
    res.json(addresses);
  } catch (error: any) {
    res.status(error instanceof HttpError ? error.statusCode : 500).json({ message: error.message || 'Failed to get addresses.' });
  }
};

// --- Create Customer Address Handler ---
export const handleCreateCustomerAddress = async (req: Request, res: Response, context: any) => {
  try {
    getAuthenticatedCustomer(context);
    console.log(req.body)
    const parsedBody = addressInputSchemaForApi.safeParse(req.body);
    console.log(parsedBody.error)
    if (!parsedBody.success) {
      throw new HttpError(400, 'Invalid input: ' + parsedBody.error.format()._errors.join(', '));
    }
    const newAddress = await createCustomerAddress(parsedBody.data, context);
    res.status(201).json(newAddress);
  } catch (error: any) {
    res.status(error instanceof HttpError ? error.statusCode : 500).json({ message: error.message || 'Failed to create address.' });
  }
};

// --- Update Customer Address Handler ---
// Assumes addressId is passed in the URL for a typical RESTful PUT
export const handleUpdateCustomerAddress = async (req: Request, res: Response, context: any) => {
  try {
    getAuthenticatedCustomer(context);
    const addressId = parseInt(req.params.addressId, 10);
    if (isNaN(addressId)) {
      throw new HttpError(400, 'Invalid Address ID in URL.');
    }

    const parsedBody = updateAddressInputSchemaForApi.safeParse(req.body);
    if (!parsedBody.success) {
      throw new HttpError(400, 'Invalid input: ' + parsedBody.error.format()._errors.join(', '));
    }
    
    // The operation `updateCustomerAddress` expects addressId in its arguments
    const updatedAddress = await updateCustomerAddress({ ...parsedBody.data, addressId }, context);
    res.json(updatedAddress);
  } catch (error: any) {
    res.status(error instanceof HttpError ? error.statusCode : 500).json({ message: error.message || 'Failed to update address.' });
  }
};

// --- Delete Customer Address Handler ---
// Assumes addressId is passed in the URL for a typical RESTful DELETE
export const handleDeleteCustomerAddress = async (req: Request, res: Response, context: any) => {
  try {
    getAuthenticatedCustomer(context);
    const addressId = parseInt(req.params.addressId, 10);
    if (isNaN(addressId)) {
      throw new HttpError(400, 'Invalid Address ID in URL.');
    }
    const deletedAddress = await deleteCustomerAddress({ addressId }, context);
    res.json(deletedAddress); // Or return 204 No Content
  } catch (error: any) {
    res.status(error instanceof HttpError ? error.statusCode : 500).json({ message: error.message || 'Failed to delete address.' });
  }
};

// --- Get Public Provider by ID Handler ---
export const handleGetPublicProviderById = async (req: Request, res: Response, context: any) => {
  try {
    const providerId = parseInt(req.params.providerId, 10);
    if (isNaN(providerId)) {
      throw new HttpError(400, 'Invalid Provider ID in URL.');
    }

    // The getPublicProviderById query expects an object with providerId
    const providerInfo = await getPublicProviderById({ providerId }, context);
    
    // providerInfo will be null if not found by the operation (which throws HttpError 404)
    // The operation itself handles the HttpError for not found, so we just return the data.
    res.status(200).json(providerInfo);

  } catch (error: any) {
    console.error(`[API] Failed to get public provider by ID:`, error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error.message || 'Failed to retrieve provider information.';
    res.status(statusCode).json({ message });
  }
}; 