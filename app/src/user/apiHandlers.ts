import { type UpdateUserPreferedLanguageApi } from 'wasp/server/api';
import { updateUserPreferedLanguage } from 'wasp/server/operations'; // Use the correct Wasp import for the action
import { HttpError } from 'wasp/server';
import { type LanguageCode } from '@prisma/client'; // Or your central enum location

// Ensure LanguageCode enum is defined and matches your prisma schema
const isValidLanguageCode = (lang: any): lang is LanguageCode => {
  return Object.values(['EN', 'AR', 'FR'] as const).includes(lang);
};

export const handleUpdateUserPreferedLanguage: UpdateUserPreferedLanguageApi = async (
  req,
  res,
  context: any
) => {
  if (!context.user) {
    // This check might be redundant due to `auth: true` in API definition,
    // but good for explicit safety.
    return res.status(401).json({ error: 'User not authenticated' });
  }

  const { preferedLanguage } = req.body as { preferedLanguage: LanguageCode };

  if (!preferedLanguage || !isValidLanguageCode(preferedLanguage)) {
    return res.status(400).json({ error: 'Invalid or missing preferedLanguage in request body' });
  }


  console.log(`[handleUpdateUserPreferedLanguage] Updating user prefered language to: ${preferedLanguage}`);
  try {
    const updatedUser = await updateUserPreferedLanguage(
      { preferedLanguage },
      context
    );
    return res.status(200).json(updatedUser);
  } catch (error: any) {
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error.message || 'Failed to update preferred language';
    return res.status(statusCode).json({ error: message });
  }
};