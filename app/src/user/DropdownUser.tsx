import { type User } from 'wasp/entities';
import { Button, Dropdown, Space, Typography } from 'antd';
import { CgProfile } from 'react-icons/cg';
import { UserMenuItems } from './UserMenuItems';
import { DownOutlined } from '@ant-design/icons';
import { Role } from '@prisma/client';
const DropdownUser = ({ user }: { user: Partial<User> }) => {
  return (
    <Dropdown
      dropdownRender={() => (
        <div className='bg-white dark:bg-boxdark dark:text-white rounded-sm border border-stroke dark:border-strokedark shadow-default min-w-[250px]'>
          <UserMenuItems user={user} />
        </div>
      )}
      trigger={['click']}
    >
      <Button size='large' className="dark:bg-boxdark" icon={<CgProfile size='1.1rem' className='ml-1 mt-[0.1rem] dark:text-white' />}>
        <Space className='flex items-center gap-4 duration-300 ease-in-out text-gray-900 hover:text-yellow-500'>
          <span className='hidden text-right lg:block'>
            <Typography.Text className='text-sm font-medium dark:text-white'>
              {user.role === Role.CUSTOMER ? `${user.firstName} ${user.lastName}` : user.username}
            </Typography.Text>
          </span>
        </Space>
      </Button>
    </Dropdown>
  );
};

export default DropdownUser;
