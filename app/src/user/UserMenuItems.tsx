import { Link as WaspRouterLink, routes } from 'wasp/client/router';
import { type User } from 'wasp/entities';
import { logout } from 'wasp/client/auth';
import { MdOutlineSpaceDashboard } from 'react-icons/md';
import { TfiDashboard } from 'react-icons/tfi';
import { Menu, Button } from 'antd';
import { SettingOutlined, LogoutOutlined, UserOutlined, CalendarOutlined } from '@ant-design/icons';

export const UserMenuItems = ({ user, setMobileMenuOpen }: { user?: Partial<User>; setMobileMenuOpen?: any }) => {
  const path = window.location.pathname;
  const landingPagePath = routes.LandingPageRoute.to;
  const adminDashboardPath = routes.AdminRoute.to;

  const handleMobileMenuClick = () => {
    if (setMobileMenuOpen) setMobileMenuOpen(false);
  };

  return (
    <Menu mode="inline" theme="light" selectedKeys={[path]}>
      {!!user && (user?.role === 'ADMIN' || user?.role === 'CLIENT') && (
        <Menu.Item
          key={routes.AdminRoute.to}
          icon={<TfiDashboard />}
          onClick={handleMobileMenuClick}
        >
          <WaspRouterLink to={routes.AdminRoute.to}>
            Admin Dashboard
          </WaspRouterLink>
        </Menu.Item>
      )}

      {!!user && (user?.role === 'CUSTOMER') && (
        <>
          <Menu.Item
            key={routes.MyProfileRoute.to}
            icon={<UserOutlined />}
            onClick={handleMobileMenuClick}
          >
            <WaspRouterLink to={routes.MyProfileRoute.to}>
              My Profile
            </WaspRouterLink>
          </Menu.Item>
          <Menu.Item
            key={routes.MyAppointmentsRoute.to}
            icon={<CalendarOutlined />}
            onClick={handleMobileMenuClick}
          >
            <WaspRouterLink to={routes.MyAppointmentsRoute.to}>
              My Appointments
            </WaspRouterLink>
          </Menu.Item>
        </>
      )}

      <Menu.Item
        key="logout"
        icon={<LogoutOutlined />}
        onClick={() => logout()}
      >
        Log Out
      </Menu.Item>
    </Menu>
  );
};
