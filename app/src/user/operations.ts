import * as z from 'zod';
import { type UpdateIsUserAdminById, type GetPaginatedUsers, type GiveFreePlanUser , type RemoveFreePlanUser, UpdateUserPreferedLanguage} from 'wasp/server/operations';
import { type User } from 'wasp/entities';
import { HttpError, prisma } from 'wasp/server';
import { PaymentPlanId, paymentPlans, SubscriptionStatus } from '../payment/plans';
import { LanguageCode, type Prisma } from '@prisma/client';
import { ensureArgsSchemaOrThrowHttpError } from '../server/validation';

const updateUserAdminByIdInputSchema = z.object({
  id: z.string().nonempty(),
  isAdmin: z.boolean(),
});

const giveFreePlanUserInputSchema = z.object({
  id: z.string().nonempty(),
});

const removeFreePlanUserInputSchema = z.object({
  id: z.string().nonempty(),
});

type GiveFreePlanUserInput = z.infer<typeof giveFreePlanUserInputSchema>;	
type UpdateUserAdminByIdInput = z.infer<typeof updateUserAdminByIdInputSchema>;
type RemoveFreePlanUserInput = z.infer<typeof removeFreePlanUserInputSchema>;



export const removeFreePlanUser: RemoveFreePlanUser<RemoveFreePlanUserInput, User> = async (
  rawArgs,
  context
) => {
  const { id } = ensureArgsSchemaOrThrowHttpError(removeFreePlanUserInputSchema, rawArgs);
  const planId = PaymentPlanId.Free;
  const amount = paymentPlans[planId].effect.amount;
  return context.entities.User.update({
    where: { id },
    data: {
      subscriptionPlan: null,
      subscriptionStatus: SubscriptionStatus.Deleted,
      datePaid: null,
      credits: { decrement: amount },
    },
  });
};
export const giveFreePlanUser: GiveFreePlanUser<GiveFreePlanUserInput, User> = async (
  rawArgs,
  context
) => {
  const { id } = ensureArgsSchemaOrThrowHttpError(giveFreePlanUserInputSchema, rawArgs);

  if (!context.user) {
    throw new HttpError(401, 'Only authenticated users are allowed to perform this operation');
  }
  const planId = PaymentPlanId.Free;
  const amount = paymentPlans[planId].effect.amount;
  const queues = paymentPlans[planId].effect?.queues || 1;
  return context.entities.User.update({
    where: { id },
    data: {
      subscriptionPlan: planId,
      subscriptionStatus: SubscriptionStatus.Active ,
      datePaid: new Date(),
      credits: { increment: amount},
      queues: queues
    },
  });
};

export const updateIsUserAdminById: UpdateIsUserAdminById<UpdateUserAdminByIdInput, User> = async (
  rawArgs,
  context
) => {
  const { id, isAdmin } = ensureArgsSchemaOrThrowHttpError(updateUserAdminByIdInputSchema, rawArgs);

  if (!context.user) {
    throw new HttpError(401, 'Only authenticated users are allowed to perform this operation');
  }

  if (!context.user.isAdmin) {
    throw new HttpError(403, 'Only admins are allowed to perform this operation');
  }

  return context.entities.User.update({
    where: { id },
    data: { isAdmin },
  });
};

type GetPaginatedUsersOutput = {
  users: Pick<
    User,
    'id' | 'email' | 'username' | 'subscriptionStatus' | 'paymentProcessorUserId' | 'isAdmin'
  >[];
  totalPages: number;
};

const getPaginatorArgsSchema = z.object({
  skipPages: z.number(),
  filter: z.object({
    emailContains: z.string().nonempty().optional(),
    isAdmin: z.boolean().optional(),
    subscriptionStatusIn: z.array(z.nativeEnum(SubscriptionStatus).nullable()).optional(),
  }),
});

type GetPaginatedUsersInput = z.infer<typeof getPaginatorArgsSchema>;

export const getPaginatedUsers: GetPaginatedUsers<GetPaginatedUsersInput, GetPaginatedUsersOutput> = async (
  rawArgs,
  context
) => {
  if (!context.user) {
    throw new HttpError(401, 'Only authenticated users are allowed to perform this operation');
  }

  // if (!context.user.isAdmin) {
  if (false) {
    throw new HttpError(403, 'Only admins are allowed to perform this operation');
  }

  const {
    skipPages,
    filter: { subscriptionStatusIn: subscriptionStatus, emailContains, isAdmin },
  } = ensureArgsSchemaOrThrowHttpError(getPaginatorArgsSchema, rawArgs);

  const includeUnsubscribedUsers = !!subscriptionStatus?.some((status) => status === null);
  const desiredSubscriptionStatuses = subscriptionStatus?.filter((status) => status !== null);

  const pageSize = 10;

  const userPageQuery: Prisma.UserFindManyArgs = {
    skip: skipPages * pageSize,
    take: pageSize,
    where: {
      AND: [
        {
          email: {
            contains: emailContains,
            mode: 'insensitive',
          },
          isAdmin,
        },
        {
          OR: [
            {
              subscriptionStatus: {
                in: desiredSubscriptionStatuses,
              },
            },
            {
              subscriptionStatus: includeUnsubscribedUsers ? null : undefined,
            },
          ],
        },
      ],
    },
    select: {
      id: true,
      email: true,
      username: true,
      isAdmin: true,
      subscriptionStatus: true,
      paymentProcessorUserId: true,
    },
    orderBy: {
      username: 'asc',
    },
  };

  const [pageOfUsers, totalUsers] = await prisma.$transaction([
    context.entities.User.findMany(userPageQuery),
    context.entities.User.count({ where: userPageQuery.where }),
  ]);
  const totalPages = Math.ceil(totalUsers / pageSize);

  return {
    users: pageOfUsers,
    totalPages,
  };
};



// Ensure LanguageCode enum is defined and matches your prisma schema
const isValidLanguageCode = (lang: any): lang is LanguageCode => {
  return Object.values(['EN', 'AR', 'FR'] as const).includes(lang);
};

type UpdateUserPreferedLanguagePayload = {
  preferedLanguage: LanguageCode;
};

export const updateUserPreferedLanguage: UpdateUserPreferedLanguage<
  UpdateUserPreferedLanguagePayload,
  any
> = async (args, context) => {
  if (!context.user) {
    throw new HttpError(401, 'User not authenticated');
  }

  const { preferedLanguage } = args;


  console.log(`[updateUserPreferedLanguage] Prefered language: ${preferedLanguage} for user ${context.user.id}`);
  if (!preferedLanguage || !isValidLanguageCode(preferedLanguage)) {
    throw new HttpError(400, 'Invalid or missing preferedLanguage');
  }

  return context.entities.User.update({
    where: {
      id: context.user.id,
    },
    data: {
      preferedLanguage: preferedLanguage,
    },
  });
};