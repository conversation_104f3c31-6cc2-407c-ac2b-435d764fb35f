import { type Request, type Response } from 'express';
import { HttpError } from 'wasp/server';
import { type File } from 'wasp/entities';
import * as z from 'zod';
import { createFile } from 'wasp/server/operations';
import { getDownloadFileSignedURLFromS3 } from '../file-upload/s3Utils';

// Image file types only for profile pictures (subset of ALLOWED_FILE_TYPES)
const IMAGE_FILE_TYPES = [
  'image/jpeg',
  'image/png'
] as const;

// Validation schemas
const uploadProfilePictureApiSchema = z.object({
  fileType: z.enum(IMAGE_FILE_TYPES),
  fileName: z.string().min(1, 'File name is required'),
});

type UploadProfilePictureApiInput = z.infer<typeof uploadProfilePictureApiSchema>;

/**
 * POST /api/auth/user/profile-picture
 * Upload a profile picture for the authenticated user
 */
export const handleUploadProfilePicture = async (req: Request, res: Response, context: any) => {
  try {
    // Check authentication
    if (!context.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    // Validate request body
    const validationResult = uploadProfilePictureApiSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        message: 'Invalid request data. Only image files are allowed for profile pictures.',
        errors: validationResult.error.format()
      });
    }

    const { fileType, fileName } = validationResult.data;

    // Create the file record using existing operation
    const result = await createFile({ fileType, fileName }, context);

    // Get the created file record to get its ID
    const fileRecord = await context.entities.File.findFirst({
      where: {
        userId: context.user.id,
        name: fileName,
        type: fileType,
        key: result.s3UploadFields.key || result.s3UploadUrl.split('/').pop()
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    if (!fileRecord) {
      throw new HttpError(500, 'Failed to create file record');
    }

    // Update user's profile picture reference
    const updatedUser = await context.entities.User.update({
      where: { id: context.user.id },
      data: { profilePictureId: fileRecord.id },
      include: {
        profilePicture: true
      }
    });

    return res.status(200).json({
      success: true,
      message: 'Profile picture upload URL generated successfully',
      data: {
        uploadUrl: result.s3UploadUrl,
        uploadFields: result.s3UploadFields,
        file: {
          id: fileRecord.id,
          name: fileName,
          type: fileType,
          key: fileRecord.key
        },
        user: {
          id: updatedUser.id,
          profilePictureId: updatedUser.profilePictureId
        }
      }
    });

  } catch (error: any) {
    console.error('[API] Error in profile picture upload:', error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error.message || 'Failed to generate profile picture upload URL';
    return res.status(statusCode).json({
      success: false,
      message
    });
  }
};

/**
 * DELETE /api/auth/user/profile-picture
 * Remove the profile picture for the authenticated user
 */
export const handleRemoveProfilePicture = async (_req: Request, res: Response, context: any) => {
  try {
    // Check authentication
    if (!context.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    // Get current user with profile picture
    const user = await context.entities.User.findUnique({
      where: { id: context.user.id },
      include: { profilePicture: true }
    });

    if (!user?.profilePictureId) {
      return res.status(404).json({
        success: false,
        message: 'No profile picture found'
      });
    }

    // Remove the profile picture reference from user
    await context.entities.User.update({
      where: { id: context.user.id },
      data: { profilePictureId: null }
    });

    // Optionally delete the file record (you might want to keep it for audit purposes)
    // await context.entities.File.delete({
    //   where: { id: user.profilePictureId }
    // });

    return res.status(200).json({
      success: true,
      message: 'Profile picture removed successfully'
    });

  } catch (error: any) {
    console.error('[API] Error removing profile picture:', error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error.message || 'Failed to remove profile picture';
    return res.status(statusCode).json({
      success: false,
      message
    });
  }
};

/**
 * GET /api/auth/user/profile-picture
 * Get the profile picture for the authenticated user
 */
export const handleGetProfilePicture = async (_req: Request, res: Response, context: any) => {
  try {
    // Check authentication
    if (!context.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    // Get current user with profile picture
    const user = await context.entities.User.findUnique({
      where: { id: context.user.id },
      include: { profilePicture: true }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // If no profile picture, return null data
    if (!user.profilePicture) {
      return res.status(200).json({
        success: true,
        data: {
          hasProfilePicture: false,
          profilePicture: null
        }
      });
    }

    // Get download URL for the profile picture
    let downloadUrl: string | null = null;
    try {
      downloadUrl = await getDownloadFileSignedURLFromS3({ key: user.profilePicture.key });
    } catch (error) {
      console.warn('[API] Could not generate download URL for profile picture:', error);
      // We can still return the data without the URL if S3 fails
    }

    return res.status(200).json({
      success: true,
      data: {
        hasProfilePicture: true,
        profilePicture: {
          id: user.profilePicture.id,
          name: user.profilePicture.name,
          type: user.profilePicture.type,
          key: user.profilePicture.key,
          downloadUrl,
          createdAt: user.profilePicture.createdAt
        }
      }
    });

  } catch (error: any) {
    console.error('[API] Error getting profile picture:', error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error.message || 'Failed to fetch profile picture';
    return res.status(statusCode).json({
      success: false,
      message
    });
  }
};
