import type { User } from 'wasp/entities';
import { SubscriptionStatus, prettyPaymentPlanName, parsePaymentPlanId, PaymentPlanId } from '../payment/plans';
import { getCustomerPortalUrl, useQuery } from 'wasp/client/operations';
import { Link as WaspRouterLink, routes } from 'wasp/client/router';
import { logout } from 'wasp/client/auth';
import { removeFreePlanUser, getUserServiceProvider } from 'wasp/client/operations';
import { useNavigate } from 'react-router-dom';
import { Typography, Descriptions, Button, Space, Spin, Grid, Divider, Tag } from 'antd';
import Card from 'antd/es/card/Card';
import { ProfilePictureUpload } from './components/ProfilePictureUpload';
import { PaymentProcessorLogo } from '../client/components/AlgerianPaymentIndicator';
import type { PaymentProcessorId } from '../client/utils/paymentMethodSelector';

const { Title, Text } = Typography;
const { useBreakpoint } = Grid;

/**
 * Detect which payment processor the user is using based on their data
 */
function detectUserPaymentProcessor(user: User): PaymentProcessorId | null {
  // Check for Chargily-specific fields
  if (user.chargilyCustomerId || user.chargilyPaymentMethod) {
    return 'chargily';
  }

  // Check for LemonSqueezy-specific fields
  if (user.lemonSqueezyCustomerPortalUrl || user.paymentProcessorUserId) {
    return 'lemonsqueezy';
  }

  // Default to LemonSqueezy if user has subscription but no specific processor indicators
  if (user.subscriptionStatus && user.subscriptionPlan) {
    return 'lemonsqueezy';
  }

  return null;
}

/**
 * Get payment processor display information
 */
function getPaymentProcessorInfo(processorId: PaymentProcessorId | null) {
  if (!processorId) return null;

  switch (processorId) {
    case 'chargily':
      return {
        name: 'Chargily Pay',
        description: 'Algerian Payment Gateway',
        color: 'green',
        supportedMethods: ['EDAHABIA', 'CIB']
      };
    case 'lemonsqueezy':
      return {
        name: 'Lemon Squeezy',
        description: 'Global Payment Processing',
        color: 'gold',
        supportedMethods: ['Credit Card', 'PayPal']
      };
    case 'stripe':
      return {
        name: 'Stripe',
        description: 'Global Payment Processing',
        color: 'purple',
        supportedMethods: ['Credit Card', 'Apple Pay', 'Google Pay']
      };
    default:
      return {
        name: processorId,
        description: 'Payment Processing',
        color: 'default',
        supportedMethods: []
      };
  }
}

export default function AccountPage({ user }: { user: User }) {
  const { data: serviceProvider } = useQuery(getUserServiceProvider, { userId: user.id });
  const screens = useBreakpoint();

  const descriptionLayout = screens.md ? 'horizontal' : 'vertical';

  return (
    <div className='p-6'>
      {/* Profile Picture Section */}
      <Card className='mb-6 dark:bg-boxdark-2 bg-white'>
        <div className="flex flex-col items-center">
          <Title level={5} className="mb-4">Profile Picture</Title>
          <ProfilePictureUpload 
            user={user} 
            onUploadSuccess={(fileId) => {
              console.log('Profile picture uploaded:', fileId);
            }}
            onRemoveSuccess={() => {
              console.log('Profile picture removed');
            }}
          />
        </div>
      </Card>

      <Divider />

      {/* Account Information Section */}
      <Descriptions layout={descriptionLayout} column={1} bordered className='dark:bg-boxdark-2 bg-white'>
        {!!user.email && (
          <Descriptions.Item label="Email address">
            {user.email}
          </Descriptions.Item>
        )}
        {!!user.username && (
          <Descriptions.Item label="Username">
            {user.username}
          </Descriptions.Item>
        )}
        {user.subscriptionPlan && (
          <Descriptions.Item label="Your Plan">
            <UserCurrentPaymentPlan
              subscriptionStatus={user.subscriptionStatus as SubscriptionStatus}
              subscriptionPlan={user.subscriptionPlan}
              datePaid={user.datePaid}
              credits={user.credits}
              user={user}
            />
          </Descriptions.Item>
        )}
        <Descriptions.Item label="Payment Method">
          <UserPaymentProcessorInfo user={user} />
        </Descriptions.Item>
        <Descriptions.Item label="Your Credits">
          <UserCurrentPaymentPlan
            credits={user.credits}
            user={user}
          />
        </Descriptions.Item>
        <Descriptions.Item label="Queues">
          <UserCurrentPaymentPlan
            queues={user.queues}
            user={user}
          />
        </Descriptions.Item>
        {!!serviceProvider && (
          <Descriptions.Item label="Your Service">
            {serviceProvider?.category?.title}
          </Descriptions.Item>
        )}
      </Descriptions>
    
      <div className='text-right mt-4'>
        <Button 
          type="primary"
          danger
          onClick={logout}
        >
          Logout
        </Button>
      </div>
    </div>
  );
}

type UserCurrentPaymentPlanProps = {
  subscriptionPlan?: string | null;
  subscriptionStatus?: SubscriptionStatus | null;
  datePaid?: Date | null;
  credits?: number | null;
  queues?: number | null;
  user?: User | null;
};

function UserCurrentPaymentPlan({
  subscriptionPlan,
  subscriptionStatus,
  datePaid,
  credits,
  queues,
  user,
}: UserCurrentPaymentPlanProps) {
  if (subscriptionStatus && subscriptionPlan && datePaid && !!user) {
    return (
      <Space direction="vertical" align="start">
        <Text>
          {getUserSubscriptionStatusDescription({ subscriptionPlan, subscriptionStatus, datePaid })}
        </Text>
        {subscriptionStatus !== SubscriptionStatus.Deleted ? <CustomerPortalButton user={user} /> : <BuyMoreButton />}
      </Space>
    );
  }

  return queues && queues > 0 ? (
        <Space direction="vertical" align="start">
          <Text>Queues allowed: {queues}</Text>
          <BuyMoreButton />
        </Space>
      ) : (
        <Space direction="vertical" align="start">
          <Text>Credits remaining: {credits}</Text>
          <BuyMoreButton />
        </Space>
      )
}

function getUserSubscriptionStatusDescription({
  subscriptionPlan,
  subscriptionStatus,
  datePaid,
}: {
  subscriptionPlan: string;
  subscriptionStatus: SubscriptionStatus;
  datePaid: Date;
}) {
  const planName = prettyPaymentPlanName(parsePaymentPlanId(subscriptionPlan));
  const endOfBillingPeriod = prettyPrintEndOfBillingPeriod(datePaid);
  return prettyPrintStatus(planName, subscriptionStatus, endOfBillingPeriod);
}

function prettyPrintStatus(
  planName: string,
  subscriptionStatus: SubscriptionStatus,
  endOfBillingPeriod: string
): string {
  const statusToMessage: Record<SubscriptionStatus, string> = {
    active: `${planName}`,
    past_due: `Payment for your ${planName} plan is past due! Please update your subscription payment information.`,
    cancel_at_period_end: `Your ${planName} plan subscription has been canceled, but remains active until the end of the current billing period${endOfBillingPeriod}`,
    deleted: `Your previous subscription has been canceled and is no longer active.`,
  };
  if (Object.keys(statusToMessage).includes(subscriptionStatus)) {
    return statusToMessage[subscriptionStatus];
  } else {
    throw new Error(`Invalid subscriptionStatus: ${subscriptionStatus}`);
  }
}

function prettyPrintEndOfBillingPeriod(date: Date) {
  const oneMonthFromNow = new Date(date);
  oneMonthFromNow.setMonth(oneMonthFromNow.getMonth() + 1);
  return ': ' + oneMonthFromNow.toLocaleDateString();
}

function BuyMoreButton() {
  return (
    <Button type="link">
      <WaspRouterLink to={routes.PricingPageRoute.to}>
        Buy More/Upgrade
      </WaspRouterLink>
    </Button>
  );
}

/**
 * Component to display user's payment processor information
 */
function UserPaymentProcessorInfo({ user }: { user: User }) {
  const paymentProcessor = detectUserPaymentProcessor(user);
  const processorInfo = getPaymentProcessorInfo(paymentProcessor);

  if (!processorInfo) {
    return (
      <Space direction="vertical" align="start">
        <Text type="secondary">No payment method configured</Text>
        <Button type="link">
          <WaspRouterLink to={routes.PricingPageRoute.to}>Set up payment</WaspRouterLink>
        </Button>
      </Space>
    );
  }

  return (
    <Space direction="vertical" align="start" size="small">
      <div className="flex items-center gap-2">
        {paymentProcessor && (
          <PaymentProcessorLogo
            processor={paymentProcessor}
            size="sm"
            showName={false}
          />
        )}
        <div>
          <Text strong>{processorInfo.name}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {processorInfo.description}
          </Text>
        </div>
        <Tag color={processorInfo.color}>
          {paymentProcessor === 'chargily' ? 'Algeria' : 'Global'}
        </Tag>
      </div>

      {processorInfo.supportedMethods.length > 0 && (
        <div>
          <Text type="secondary" style={{ fontSize: '11px' }}>
            Supports: {processorInfo.supportedMethods.join(', ')}
          </Text>
        </div>
      )}

      {/* Show Chargily-specific information */}
      {paymentProcessor === 'chargily' && user.chargilyPaymentMethod && (
        <div>
          <Text type="secondary" style={{ fontSize: '11px' }}>
            Active method: {user.chargilyPaymentMethod.toUpperCase()}
          </Text>
        </div>
      )}
    </Space>
  );
}

function CustomerPortalButton({ user }: { user: User | null }) {
  const {
    data: customerPortalUrl,
    isLoading: isCustomerPortalUrlLoading,
    error: customerPortalUrlError,
  } = useQuery(getCustomerPortalUrl);

  const paymentProcessor = user ? detectUserPaymentProcessor(user) : null;
  const processorInfo = getPaymentProcessorInfo(paymentProcessor);

  const handleClick = () => {
    if (customerPortalUrlError) {
      console.error('Error fetching customer portal url');
    }

    if (customerPortalUrl) {
      window.open(customerPortalUrl, '_blank');
    } else {
      console.error('Customer portal URL is not available');
    }
  };
  
  const navigate = useNavigate();
  const handleRemoveFreePlan = async () => {
    if (!user) {
      navigate('/login');
      return;
    }
    const result = await removeFreePlanUser({ id: user.id });
    console.log(result);
    navigate('/account');
  }

  if (isCustomerPortalUrlLoading) {
    return <Spin size="small" />;
  }

  const getButtonText = () => {
    if (!!user?.subscriptionPlan && user?.subscriptionPlan !== PaymentPlanId.Free) {
      if (paymentProcessor === 'chargily') {
        return 'Manage Chargily Subscription';
      } else if (paymentProcessor === 'stripe') {
        return 'Manage Stripe Subscription';
      } else {
        return 'Manage Subscription';
      }
    }
    return 'Remove Free Plan';
  };

  return (
    <Space direction="vertical" align="start" size="small">
      <Button
        type="link"
        onClick={!!user?.subscriptionPlan && user?.subscriptionPlan !== PaymentPlanId.Free ? handleClick : handleRemoveFreePlan}
      >
        {getButtonText()}
      </Button>

      {/* Show processor-specific note */}
      {paymentProcessor && !!user?.subscriptionPlan && user?.subscriptionPlan !== PaymentPlanId.Free && (
        <Text type="secondary" style={{ fontSize: '11px' }}>
          {paymentProcessor === 'chargily'
            ? 'Note: Chargily subscriptions are managed through your Chargily account'
            : `Managed through ${processorInfo?.name || 'payment provider'}`
          }
        </Text>
      )}
    </Space>
  );
}
