import React from 'react';
import { Link } from 'react-router-dom'; // Assuming you use react-router-dom

export default function GetStarted() {
  return (
    <div id="get-started" className="mx-auto mt-48 max-w-7xl px-6 lg:px-8 py-24 sm:py-32 bg-gray-100 dark:bg-boxdark">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-2xl lg:text-center">
          <h2 className="text-base font-semibold leading-7 text-yellow-600 dark:text-yellow-400">Get Started</h2>
          <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl">
            Ready to Dive In?
          </p>
          <p className="mt-6 text-lg leading-8 text-gray-600 dark:text-white">
            Choose the plan that's right for you. Start for free or unlock powerful features with our subscription.
          </p>
        </div>
        <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-4xl">
          <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-10 lg:max-w-none lg:grid-cols-2 lg:gap-y-16">
            {/* Free Plan Option */}
            <div className="relative bg-white dark:bg-boxdark-2 p-8 pt-12 rounded-lg shadow-md">
              <dt className="text-base font-semibold leading-7 text-gray-900 dark:text-white">
                <div className="absolute left-1/2 -translate-x-1/2 -top-10 flex h-20 w-20 items-center justify-center rounded-full bg-yellow-500 dark:bg-yellow-600">
                  <svg className="h-10 w-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.5 20.25v-.75A4.5 4.5 0 019 15h6a4.5 4.5 0 014.5 4.5v.75" />
                  </svg>
                </div>
                Try Our Free Plan
              </dt>
              <dd className="mt-2 text-base leading-7 text-gray-600 dark:text-white">
                Perfect for individuals or small teams just getting started. Enjoy access to core features including online booking,
                real-time queue display, and up to 30 appointment credits per month. Community support included.
              </dd>
              <dd className="mt-4">
                 <Link to="/signup" className="text-sm font-semibold leading-6 text-yellow-600 dark:text-yellow-400 hover:text-yellow-500">
                   Start for free &rarr;
                 </Link>
              </dd>
            </div>

            {/* Subscription Option */}
            <div className="relative bg-white dark:bg-boxdark-2 p-8 pt-12 rounded-lg shadow-md">
              <dt className="text-base font-semibold leading-7 text-gray-900 dark:text-white">
                <div className="absolute left-1/2 -translate-x-1/2 -top-10 flex h-20 w-20 items-center justify-center rounded-full bg-yellow-500 dark:bg-yellow-600">
                  <svg className="h-10 w-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M2.25 6.75A2.25 2.25 0 014.5 4.5h15a2.25 2.25 0 012.25 2.25v1.5H2.25v-1.5zM2.25 9.75h19.5v7.5A2.25 2.25 0 0119.5 19.5h-15a2.25 2.25 0 01-2.25-2.25v-7.5z" />
                  </svg>
                </div>
                Subscribe for More
              </dt>
              <dd className="mt-2 text-base leading-7 text-gray-600 dark:text-white">
                Unlock premium features, unlimited usage, priority support, and more. See our flexible pricing options.
              </dd>
              <dd className="mt-4">
                 <Link to="/pricing" className="text-sm font-semibold leading-6 text-yellow-600 dark:text-yellow-400 hover:text-yellow-500">
                   View Pricing &rarr;
                 </Link>
              </dd>
            </div>
          </dl>
        </div>
      </div>
    </div>
  );
} 