import { useAuth } from 'wasp/client/auth';
import { useQuery, getProviderCategories } from 'wasp/client/operations';
import { useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { routes } from 'wasp/client/router';
import { Form, Select, Input, Button, Spin, Card, Space, Row, Col, Typography } from 'antd';
import { type ProviderCategory } from 'wasp/entities';
import { SearchOutlined } from '@ant-design/icons';
import { Option } from 'antd/es/mentions';

// const { Option } = Select;
const { Text } = Typography;

export default function Hero() {
  const { data: user, isLoading: isLoadingUser } = useAuth();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [selectedParentId, setSelectedParentId] = useState<string | undefined>(undefined);
  const algerianWilayas = [
    "Adrar", "Chlef", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>um El Bouaghi", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", 
    "Tam<PERSON>rass<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>lemcen", "Tiaret", "<PERSON>izi <PERSON>uzou", "Alger", "Djelfa", "Jijel", "Sétif", "<PERSON>da", 
    "Skikda", "<PERSON>i Bel Abb<PERSON>", "<PERSON>ba", "<PERSON>uelma", "<PERSON>", "Médéa", "<PERSON>aganem", "<PERSON>'<PERSON>la", "Mascara", "Ouargla", 
    "<PERSON>an", "<PERSON> <PERSON>adh", "Illizi", "Bordj Bou Arréridj", "Boumerdès", "El Tarf", "Tindouf", "Tissemsilt", "El Oued", "Khenchela", 
    "Souk Ahras", "Tipaza", "Mila", "Aïn Defla", "Naâma", "Aïn Témouchent", "Ghardaïa", "Relizane", "Timimoun", "Bordj Badji Mokhtar", 
    "Ouled Djellal", "Béni Abbès", "In Salah", "In Guezzam", "Touggourt", "Djanet", "El M'Ghair", "El Meniaa"
  ];
  const {
    data: categories,
    isLoading: isLoadingCategories,
    error: categoriesError,
  } = useQuery(getProviderCategories);

  console.log(categories);
  const parentCategories = useMemo(() => {
    if (!Array.isArray(categories)) return [];
    return categories.filter((cat) => cat.parentId === null);
  }, [categories]);

  const childCategories = useMemo(() => {
    if (!Array.isArray(categories) || !selectedParentId) return [];
    const parentIdNum = parseInt(selectedParentId, 10);
    return categories.filter((cat) => cat.parentId === parentIdNum);
  }, [categories, selectedParentId]);

  const handleParentChange = (value: string) => {
    setSelectedParentId(value);
    form.setFieldsValue({ childCategory: undefined });
  };

  const onSearch = (values: any) => {
    const { parentCategory, childCategory, searchText, city } = values;
    console.log(`[onSearch] Values: ${JSON.stringify(values)}`);
    const queryParams = new URLSearchParams();
    if (childCategory) {
      queryParams.append('categoryId', childCategory);
    } else if (parentCategory) {
      queryParams.append('categoryId', parentCategory);
    }
    if (searchText) {
      queryParams.append('q', searchText);
    }
    if (city) {
      queryParams.append('city', city);
    }
    console.log(`[onSearch] Query Params: ${queryParams.toString()}`);
    navigate(`/search?${queryParams.toString()}`);
  };

  return (
    <div className='relative pt-14 w-full'>
      <TopGradient />
      <BottomGradient />
      <div className='py-24 sm:py-32'>
        <div className='mx-auto max-w-8xl px-6 lg:px-8'>
          <div className='lg:mb-18 mx-auto max-w-3xl text-center'>
            <h1 className='text-4xl font-bold text-gray-900 sm:text-6xl dark:text-white'>
            Bring Order to the Chaos of <span className='italic'>Bookings.</span>
            </h1>
            <p className='mt-6 mx-auto max-w-2xl text-lg leading-8 text-gray-600 dark:text-white'>
            Find and book services effortlessly. Give your clients clarity, give your team control.
            </p>
            {!user && !isLoadingUser && (
              <div className='mt-10 flex items-center justify-center gap-x-6'>
                <a
                  href={routes.SignupRoute.to}
                  className='rounded-md px-3.5 py-2.5 text-sm font-semibold text-gray-700 ring-1 ring-inset ring-gray-200 hover:ring-2 hover:ring-yellow-300 shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 dark:text-white'
                >
                  Get Started <span aria-hidden='true'>→</span>
                </a>
              </div>
            )}
          </div>

          <div className='mt-16 sm:mt-20 lg:mt-24'>
              <Form
                form={form}
                layout="vertical"
                onFinish={onSearch}
                size="large"
              >
                <Row gutter={16}>
                  <Col xs={24} sm={8}>
                    <Form.Item 
                      name="parentCategory" 
                      label="Domain"
                      rules={[{ required: true, message: 'Please select a domain!' }]}
                    >
                      <Select
                        placeholder="Select primary category"
                        loading={isLoadingCategories}
                        onChange={handleParentChange}
                        disabled={isLoadingCategories || !!categoriesError}
                        allowClear // Note: allowClear might conflict with required, but validation will handle it
                        showSearch
                        optionFilterProp="children"
                        filterOption={(input: string, option: any) => (option?.children as unknown as string ?? '').toLowerCase().includes(input.toLowerCase())}
                      >
                        {categoriesError && <Option value="error" disabled><Text type="danger">Error loading categories</Text></Option>}
                        {parentCategories.map((category: ProviderCategory) => (
                          <Option key={category.id} value={String(category.id)}>
                            {category.title}
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col xs={24} sm={8}>
                    <Form.Item 
                      name="childCategory" 
                      label="Service Type"
                      rules={[{ required: true, message: 'Please select a service type!' }]}
                    >
                      <Select
                        placeholder="Select specific service type"
                        disabled={!selectedParentId || childCategories.length === 0 || !!categoriesError}
                        loading={isLoadingCategories && !!selectedParentId}
                        allowClear // Note: allowClear might conflict with required, but validation will handle it
                        showSearch
                        optionFilterProp="children"
                        filterOption={(input: string, option: any) => (option?.children as unknown as string ?? '').toLowerCase().includes(input.toLowerCase())}
                      >
                        {childCategories.map((category: ProviderCategory) => (
                          <Option key={category.id} value={String(category.id)}>
                            {category.title}
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col xs={24} sm={8}>
                    <Form.Item
                         name="city"
                         label="City (Wilaya)"
                       >
                         <Select
                           showSearch
                           placeholder="Select or search Wilaya"
                           optionFilterProp="children"
                           filterOption={(input: string, option?: { label: string; value: string }) =>
                             (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                           }
                           options={algerianWilayas.map(w => ({ value: w, label: w }))}
                         />
                       </Form.Item>
                  </Col>
                </Row>
                <Form.Item name="searchText" label="Search by Name or Service">
                  <Input 
                    placeholder="e.g., Dentist, Dr. Smith, Checkup" 
                    prefix={<SearchOutlined style={{ color: 'rgba(0,0,0,.25)' }} />} 
                  />
                </Form.Item>
                <Form.Item>
                  <Button 
                    type="primary" 
                    htmlType="submit" 
                    block 
                    icon={<SearchOutlined />}
                    loading={isLoadingCategories}
                  >
                    Search Providers
                  </Button>
                </Form.Item>
              </Form>
          </div>
        </div>
      </div>
    </div>
  );
}

function TopGradient() {
  return (
    <div
      className='absolute top-0 right-0 -z-10 transform-gpu overflow-hidden w-full blur-3xl sm:top-0'
    aria-hidden='true'
  >
    <div
      className='aspect-[1020/880] w-[55rem] flex-none sm:right-1/4 sm:translate-x-1/2 dark:hidden bg-gradient-to-tr from-amber-400 to-purple-300 opacity-40'
      style={{
        clipPath: 'polygon(80% 20%, 90% 55%, 50% 100%, 70% 30%, 20% 50%, 50% 0)',
      }}
      />
    </div>
  );
}

function BottomGradient() {
  return (
    <div
      className='absolute inset-x-0 top-[calc(100%-40rem)] sm:top-[calc(100%-65rem)] -z-10 transform-gpu overflow-hidden blur-3xl'
    aria-hidden='true'
  >
    <div
      className='relative aspect-[1020/880] sm:-left-3/4 sm:translate-x-1/4 dark:hidden bg-gradient-to-br from-amber-400 to-purple-300  opacity-50 w-[72.1875rem]'
      style={{
        clipPath: 'ellipse(80% 30% at 80% 50%)',
      }}
    />
    </div>
  );
}