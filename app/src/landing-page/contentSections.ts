import type { NavigationItem } from '../client/components/NavBar/NavBar';
import { routes } from 'wasp/client/router';
import { DocsUrl, BlogUrl } from '../shared/common';
import daBoiAvatar from '../client/static/da-boi.webp';
import avatarPlaceholder from '../client/static/avatar-placeholder.webp';

export const landingPageNavigationItems: NavigationItem[] = [
  { name: 'Features', to: '#features' },
  { name: 'Pricing', to: routes.PricingPageRoute.to },
  // { name: 'Documentation', to: DocsUrl },
  { name: 'Support', to: '/support' },
];
export const features = [
  {
    name: 'Real-Time Queue Tracking',
    description: 'Let your clients see exactly where they are in line — live updates mean fewer calls and less waiting anxiety.',
    icon: '🕒',
    href: DocsUrl,
  },
  {
    name: 'Smart Appointment Booking',
    description: 'Easy-to-use online booking system with flexible time slots, rescheduling, and cancellations — all from any device.',
    icon: '📅',
    href: DocsUrl,
  },
  {
    name: 'Accurate Arrival Estimates',
    description: 'Clients get estimated arrival times so they can show up just in time — not hours early or minutes late.',
    icon: '⏰',
    href: DocsUrl,
  },
  {
    name: 'Credit-Based Flexibility',
    description: 'Pay only for what you use. 1 credit = 1 appointment. Simple, scalable, and perfect for growing businesses..',
    icon: '💳',
    href: DocsUrl,
  },
];
export const testimonials = [
  {
    name: 'Dr. Lina Hassan',
    role: 'Clinic Owner @ MedPlus Center',
    avatarSrc: avatarPlaceholder,
    socialUrl: '',
    quote: "Since switching to this system, our patients wait less and smile more. The real-time queue is a game changer!",
  },
  {
    name: 'Carlos Mendez',
    role: 'Barber & Shop Owner',
    avatarSrc: avatarPlaceholder,
    socialUrl: '#',
    quote: "Clients love knowing exactly when to show up. No more crowded waiting room or last-minute no-shows.",
  },
  {
    name: 'Sarah J.',
    role: 'Yoga Instructor',
    avatarSrc: avatarPlaceholder,
    socialUrl: '#',
    quote: "The credit-based plans are perfect for my business. I only pay for the appointments I actually get!",
  },
];


export const faqs = [
  {
    id: 1,
    question: 'Whats the meaning of life?',
    answer: '42.',
    href: 'https://en.wikipedia.org/wiki/42_(number)',
  },
];
export const footerNavigation = {
  app: [
    { name: 'Documentation', href: DocsUrl },
    { name: 'Blog', href: BlogUrl },
  ],
  company: [
    { name: 'About', href: 'https://wasp.sh' },
    { name: 'Privacy', href: '#' },
    { name: 'Terms of Service', href: '#' },
  ],
};
