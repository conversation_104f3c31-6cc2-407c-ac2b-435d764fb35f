import React, { useState, useEffect } from 'react';
import { useLocation, useSearchParams } from 'react-router-dom';
import { useQuery, searchProviders } from 'wasp/client/operations';
import { type SProvider, type User, type ProviderCategory, type Service, type SProvidingPlace, type Opening, type OpeningHours, type Queue } from 'wasp/entities';
import { Spin, Alert, Typography, Tag, Space, Empty, Pagination, Row, Col } from 'antd';
import { EnvironmentOutlined, UserOutlined } from '@ant-design/icons';
import ProviderResultCard from './components/ProviderResultCard';
import SearchFilters from './components/SearchFilters';

const AppLayout = ({ children }: { children: React.ReactNode }) => {
  return <div style={{ padding: '20px' }}>{children}</div>;
};

type SearchProviderResult = SProvider & {
  user: User;
  category?: ProviderCategory | null;
  services: Service[];
  // Expecting partial Queue data from searchProviders
  providingPlaces: (SProvidingPlace & { queues: Queue[] })[];
};

const SearchPage: React.FC = () => {
  const location = useLocation();
  const [searchParams, setSearchParams] = useSearchParams();

  const [currentPage, setCurrentPage] = useState<number>(1);
  const pageSize = 6; // Items per page

  // Read search params from URL
  const categoryId = searchParams.get('categoryId');
  const q = searchParams.get('q');
  const city = searchParams.get('city');

  // Prepare args for the useQuery hook, including pagination
  const queryArgs = {
    categoryId: categoryId ? parseInt(categoryId, 10) : undefined,
    q: q ?? undefined,
    city: city ?? undefined,
    skip: (currentPage - 1) * pageSize,
    take: pageSize,
  };

  const {
    data: providers,
    isLoading,
    error,
  } = useQuery(searchProviders, queryArgs, {
    // Re-run query if queryArgs change (e.g., page change)
    enabled: !!queryArgs.categoryId || !!queryArgs.q || !!queryArgs.city
  });

  console.log(providers);
  
  // Reset to page 1 when search terms change
  useEffect(() => {
    setCurrentPage(1);
  }, [q, categoryId]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // Optional: Update URL search params if desired
    // const newParams = new URLSearchParams(searchParams);
    // newParams.set('page', page.toString());
    // setSearchParams(newParams);
  };

  // Function to update search params based on filters
  const handleFilterChange = (newFilters: Record<string, string | undefined>) => {
    const currentParams = new URLSearchParams(searchParams);

    // Clear old filter keys that might be removed
    currentParams.delete('categoryId');
    currentParams.delete('city');
    // Keep 'q' unless explicitly cleared or changed

    // Set new filter values
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value) {
        currentParams.set(key, value);
      } else {
        // If a filter value becomes undefined/empty, remove it from params
        currentParams.delete(key);
      }
    });

    // Reset to page 1 when filters change
    currentParams.delete('page'); // Remove page param to default to 1
    setCurrentPage(1);

    setSearchParams(currentParams);
  };

  const renderContent = () => {
    if (error) {
      // Show error prominently
      return <Alert message="Error searching providers" description={error.message} type="error" showIcon style={{ marginBottom: '2rem' }} />;
    }

    const noResults = !Array.isArray(providers) || providers.length === 0;

    if (noResults && !isLoading) { // Only show no results message if not loading
        // Determine if it's an initial load state or no results found state
        if (!queryArgs.categoryId && !queryArgs.q) {
            return <Empty description="Please initiate a search using the form on the home page." /> 
        } else {
             return <Empty description="No providers found matching your criteria." /> 
        }
    }

    return (
      <>
        {isLoading && (
          <div style={{ textAlign: 'center', margin: '50px 0' }}><Spin size="large" tip="Loading providers..." /></div>
        )}
        {(providers as SearchProviderResult[])?.map((provider) => (
          <ProviderResultCard key={provider.id} provider={provider} />
        ))}
        <div style={{ textAlign: 'center', marginTop: '2rem' }}>
          {/* Only show pagination if there are results and more than one page potentially */}
          {!isLoading && providers && providers.length > 0 && (
            <Pagination
              current={currentPage}
              pageSize={pageSize}
              onChange={handlePageChange}
              style={{ display: providers.length < pageSize && currentPage === 1 ? 'none' : 'inline-block' }}
            />
          )}
        </div>
      </>
    );
  };

  return (
    <AppLayout>
      <Typography.Title level={2} style={{ marginBottom: '2rem' }}>
        Search Results
      </Typography.Title>
      <Row gutter={24}> {/* Add gutter for spacing between columns */}
        {/* Main Content Column */}
        <Col xs={24} sm={24} md={16} lg={18} xl={18}> {/* Responsive spans */}
          {renderContent()}
        </Col>

        {/* Filter Column */}        
        <Col xs={24} sm={24} md={8} lg={6} xl={6}> {/* Responsive spans */}
          <SearchFilters
            initialFilters={{
              categoryId: categoryId ?? undefined,
              city: city ?? undefined,
            }}
            onFilterChange={handleFilterChange}
          />
        </Col>
      </Row>
    </AppLayout>
  );
};

export default SearchPage; 