import React, { useState, useEffect, useMemo } from 'react';
import { useQuery, getProviderCategories } from 'wasp/client/operations';
import { Form, Select, Button, Spin, Typography, Space } from 'antd';
import { type ProviderCategory } from 'wasp/entities';
import { FilterOutlined, ClearOutlined } from '@ant-design/icons';

const { Text } = Typography;

// Define the props the component will accept
interface SearchFiltersProps {
  initialFilters: {
    categoryId?: string;
    city?: string;
    // Add other potential initial filters here if needed
  };
  onFilterChange: (filters: Record<string, string | undefined>) => void;
}

// TODO: Move this to a shared constants file?
const algerianWilayas = [
  "Adrar", "Chlef", "Laghouat", "Oum El Bouaghi", "Batna", "Béjaïa", "Biskra", "Béchar", "Blida", "Bouira", 
  "Tamanrasset", "Tébessa", "Tlemcen", "Tiaret", "<PERSON>izi Ouzou", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", 
  "<PERSON><PERSON><PERSON>", "Sidi Bel Abbès", "Anna<PERSON>", "Guel<PERSON>", "<PERSON>", "<PERSON>édéa", "<PERSON><PERSON><PERSON>", "M'Sila", "Mascara", "<PERSON>uarg<PERSON>", 
  "Or<PERSON>", "El <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>j <PERSON><PERSON> <PERSON>rr<PERSON><PERSON>j", "<PERSON><PERSON><PERSON><PERSON>", "El <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON>a", 
  "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON>az<PERSON>", "<PERSON>la", "Aïn Defla", "Naâma", "Aïn Témouchent", "Ghardaïa", "Relizane", "Timimoun", "Bordj Badji Mokhtar", 
  "Ouled Djellal", "Béni Abbès", "In Salah", "In Guezzam", "Touggourt", "Djanet", "El M'Ghair", "El Meniaa"
];

const SearchFilters: React.FC<SearchFiltersProps> = ({ initialFilters, onFilterChange }) => {
  const [form] = Form.useForm();
  const [selectedParentId, setSelectedParentId] = useState<string | undefined>(undefined);

  const {
    data: categories,
    isLoading: isLoadingCategories,
    error: categoriesError,
  } = useQuery(getProviderCategories);

  // Set initial form values based on props and fetched categories
  useEffect(() => {
    if (!categories || categories.length === 0) {
        // Categories not loaded yet, just set city if available
        form.setFieldsValue({
            parentCategory: undefined,
            childCategory: undefined,
            city: initialFilters.city,
        });
        setSelectedParentId(undefined);
        return;
    }

    let parentIdToSet: string | undefined = undefined;
    let childIdToSet: string | undefined = initialFilters.categoryId; // Assume it's a child initially

    if (initialFilters.categoryId) {
        const initialCategoryIdNum = parseInt(initialFilters.categoryId, 10);
        const initialCategory = categories.find(cat => cat.id === initialCategoryIdNum);

        if (initialCategory) {
            if (initialCategory.parentId === null) {
                // The initial category IS a parent category
                parentIdToSet = String(initialCategory.id);
                childIdToSet = undefined; // No child selected initially
            } else {
                // The initial category is a child category, find its parent
                parentIdToSet = String(initialCategory.parentId);
                // childIdToSet remains initialFilters.categoryId
            }
        } else {
            // Initial category ID not found, maybe it's invalid? Reset category fields.
            console.warn(`Initial category ID ${initialFilters.categoryId} not found in categories.`);
            parentIdToSet = undefined;
            childIdToSet = undefined;
        }
    } else {
        // No initial categoryId provided
        parentIdToSet = undefined;
        childIdToSet = undefined;
    }

    form.setFieldsValue({
        parentCategory: parentIdToSet,
        childCategory: childIdToSet,
        city: initialFilters.city,
    });
    // Update state to enable/populate child dropdown based on the determined parent
    setSelectedParentId(parentIdToSet);

  }, [initialFilters.categoryId, initialFilters.city, form, categories]); // Dependencies

  const parentCategories = useMemo(() => {
    if (!Array.isArray(categories)) return [];
    return categories.filter((cat) => cat.parentId === null);
  }, [categories]);

  const childCategories = useMemo(() => {
    if (!Array.isArray(categories) || !selectedParentId) return [];
    // Ensure selectedParentId is treated as a number for comparison
    const parentIdNum = parseInt(selectedParentId, 10);
    if (isNaN(parentIdNum)) return []; // Handle case where selectedParentId might not be a valid number string
    return categories.filter((cat) => cat.parentId === parentIdNum);
  }, [categories, selectedParentId]);

  const handleParentChange = (value: string | undefined) => {
    setSelectedParentId(value);
    form.setFieldsValue({ childCategory: undefined }); // Reset child when parent changes
  };

  const handleApplyFilters = (values: any) => {
    console.log('Applying filters:', values);
    const { parentCategory, childCategory, city } = values;
    
    // Prioritize childCategory if selected, otherwise use parentCategory
    const categoryIdToApply = childCategory ?? parentCategory; 

    const filtersToApply: Record<string, string | undefined> = {
      categoryId: categoryIdToApply, 
      city: city,
      // q: undefined, // Let the parent component manage 'q' based on URL or other inputs
    };
    
    // Remove undefined/null filters before calling callback
    Object.keys(filtersToApply).forEach(key => {
      if (filtersToApply[key] === undefined || filtersToApply[key] === null) {
        delete filtersToApply[key];
      }
    });

    onFilterChange(filtersToApply);
  };

  const handleClearFilters = () => {
    form.resetFields();
    setSelectedParentId(undefined);
    onFilterChange({}); // Send empty object to clear filters in parent
  };

  return (
    <div className="border border-gray-200 rounded-lg p-5 mb-4 bg-gray-100 dark:bg-gray-800">
      <Typography.Title level={5} style={{ marginBottom: '1rem' , marginTop: '0px' }}>Filters</Typography.Title>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleApplyFilters}
        // initialValues are now set via useEffect to handle async category loading
      >
        <Form.Item name="parentCategory" label="Domain">
          <Select
            placeholder="Select domain"
            loading={isLoadingCategories}
            onChange={handleParentChange} // Use the updated handler
            value={selectedParentId} // Control the component value via state
            disabled={isLoadingCategories || !!categoriesError}
            allowClear
            showSearch
            optionFilterProp="label"
            filterOption={(input: string, option?: { label: string; value: string }) => 
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
            options={parentCategories.map(category => ({
                value: String(category.id),
                label: category.title,
            }))}
          />
          {categoriesError && <Text type="danger">Error loading domains</Text>}
        </Form.Item>

        <Form.Item name="childCategory" label="Service Type">
          <Select
            placeholder={!selectedParentId ? "Select domain first" : "Select service type"}
            disabled={!selectedParentId || childCategories.length === 0 || isLoadingCategories || !!categoriesError}
            loading={isLoadingCategories && !!selectedParentId} // Show loading only when parent is selected and categories are still loading
            allowClear
            showSearch
            optionFilterProp="label"
            filterOption={(input: string, option?: { label: string; value: string }) => 
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
            options={childCategories.map(category => ({
                value: String(category.id),
                label: category.title,
            }))}
          />
        </Form.Item>

        <Form.Item name="city" label="City (Wilaya)">
          <Select
            showSearch
            placeholder="Select or search Wilaya"
            optionFilterProp="label" // Search by label (the Wilaya name)
            filterOption={(input: string, option?: { label: string; value: string }) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
            options={algerianWilayas.map(w => ({ value: w, label: w }))}
            allowClear
          />
        </Form.Item>

        <Space direction="vertical" style={{ width: '100%' }}>
          <Button 
            type="primary" 
            htmlType="submit" 
            block 
            icon={<FilterOutlined />}
            loading={isLoadingCategories} // Consider disabling/loading based on category fetch
          >
            Apply Filters
          </Button>
          <Button 
            block 
            icon={<ClearOutlined />}
            onClick={handleClearFilters}
          >
            Clear Filters
          </Button>
        </Space>
      </Form>
    </div>
  );
};

export default SearchFilters; 