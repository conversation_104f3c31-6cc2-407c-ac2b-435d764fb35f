import React, { useState } from 'react';
import { Modal, Button, Typography, Descriptions, Alert, message } from 'antd';
import { type User, type SProvidingPlace, type Service, Queue } from 'wasp/entities';
import { createCustomerAppointment } from 'wasp/client/operations'; // Import the action
import { useAction } from 'wasp/client/operations'; // Hook to use the action
import dayjs from 'dayjs';

const { Text } = Typography;

// Structure for the data passed to the modal
export interface SelectedSlotInfo {
  providerUser: User;
  place: SProvidingPlace & { queues: Queue[] };
  service: Service;
  startTime: Date;
  endTime: Date;
  queueId: number;
  providerData: any;
}

interface BookAppointmentModalProps {
  visible: boolean;
  onClose: () => void;
  slotDetails: SelectedSlotInfo | null;
}

const BookAppointmentModal: React.FC<BookAppointmentModalProps> = ({ visible, onClose, slotDetails }) => {
  const createAppointmentAction = useAction(createCustomerAppointment); // Get the action hook
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleConfirmBooking = async () => {
    if (!slotDetails || typeof slotDetails.queueId !== 'number') {
        console.error('Booking failed: Missing slot details or queueId.');
        setError('Cannot proceed with booking: Required information is missing.');
        return;
    }

    setIsLoading(true);
    setError(null);
    try {
      await createAppointmentAction({
        serviceId: slotDetails.service.id,
        placeId: slotDetails.place.id,
        queueId: slotDetails.queueId,
        startTime: slotDetails.startTime,
        endTime: slotDetails.endTime,
      });
      message.success('Appointment booked successfully!');
      onClose(); // Close modal on success
    } catch (err: any) {
      console.error('Booking failed:', err);
      setError(err.message || 'Failed to book appointment. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  if (!slotDetails) {
    return null; // Don't render modal without details
  }

  console.log(slotDetails);

  return (
    <Modal
      title="Confirm Appointment Booking"
      visible={visible}
      onCancel={onClose}
      width={600}
      footer={[
        <Button key="back" onClick={onClose} disabled={isLoading}>
          Cancel
        </Button>,
        <Button key="submit" type="primary" loading={isLoading} onClick={handleConfirmBooking}>
          Confirm Booking
        </Button>,
      ]}
    >
      {error && (
        <Alert message="Booking Error" description={error} type="error" showIcon style={{ marginBottom: 16 }} />
      )}
      <Descriptions bordered column={1} size="small">
        <Descriptions.Item label="Service">{slotDetails.service.title}</Descriptions.Item>
        <Descriptions.Item label="Queue">{slotDetails.place.queues.find((queue: any) => queue.id === slotDetails.queueId)?.title}</Descriptions.Item>
        <Descriptions.Item label="Duration">{slotDetails.service.duration} min</Descriptions.Item>
        <Descriptions.Item label="Provider">
          {`${slotDetails.providerData.title}`.trim()}
        </Descriptions.Item>
        <Descriptions.Item label="Location">{slotDetails.place.name}</Descriptions.Item>
        <Descriptions.Item label="Address">
          {slotDetails.place.address}{slotDetails.place.address && slotDetails.place.city ? ', ' : ''}{slotDetails.place.city}
        </Descriptions.Item>
        <Descriptions.Item label="Date">
          {dayjs(slotDetails.startTime).format('dddd, MMMM D, YYYY')}
        </Descriptions.Item>
        <Descriptions.Item label="Time">
          {dayjs(slotDetails.startTime).format('HH:mm')} - {dayjs(slotDetails.endTime).format('HH:mm')}
        </Descriptions.Item>
      </Descriptions>
      <Text type="secondary" style={{ display: 'block', marginTop: '10px', fontSize: '0.9em' }}>
          Please review the details above before confirming your appointment.
      </Text>
    </Modal>
  );
};

export default BookAppointmentModal; 