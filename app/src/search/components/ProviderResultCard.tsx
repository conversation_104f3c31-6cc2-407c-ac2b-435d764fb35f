import React, { useState, useEffect, useMemo } from 'react';
import { type SProvider, type User, type ProviderCategory, type Service, type SProvidingPlace, type Queue } from 'wasp/entities';
import { useQuery, getProviderAvailability } from 'wasp/client/operations';
import { Typography, Tag, Space, Button, Avatar, Spin, Alert, Select, Divider, Descriptions, Modal } from 'antd';
import { EnvironmentOutlined, UserOutlined, ClockCircleOutlined, LeftOutlined, RightOutlined, PhoneOutlined, CheckCircleOutlined, HomeOutlined, LoadingOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
dayjs.extend(isSameOrBefore);
import BookAppointmentModal from './BookAppointmentModal';

// UPDATED: Define the expected shape matching SearchPage.tsx
type ProviderResult = SProvider & {
    user: User;
    category?: ProviderCategory | null;
    services: Service[];
    // Expecting partial Queue data from searchProviders
    providingPlaces: (SProvidingPlace & { queues: Queue[] })[];
};

// Define the structure for individual slots received from backend
type AvailabilitySlot = {
    startTime: Date; // Full JS Date object (UTC)
    queueId: number; // ADDED: Include queueId from backend
    isBooked: boolean;
};

// Define the daily availability structure received from backend
type DailyAvailabilityResult = {
    date: string; // YYYY-MM-DD string (UTC date)
    slots: AvailabilitySlot[];
};

interface ProviderResultCardProps {
    provider: ProviderResult;
}

// Availability Component Props: ADD selectedQueueIdProp
interface AvailabilityDisplayProps {
    providerUser: User;
    place: SProvidingPlace & { queues: Queue[] };
    placeId: number;
    services: Service[];
    selectedServiceId: number | undefined;
    selectedQueueIdProp: number | undefined; // Renamed prop for clarity
    onSlotSelect: (slotInfo: SelectedSlotInfo) => void;
    isExpanded: boolean;
    providerData: ProviderResult;
    availabilityEnabled: boolean;
    setAvailabilityEnabled: (enabled: boolean) => void;
}

const AvailabilityDisplay: React.FC<AvailabilityDisplayProps> = ({
    providerUser,
    place,
    placeId,
    services,
    selectedServiceId,
    selectedQueueIdProp, // Use the passed prop
    onSlotSelect,
    isExpanded,
    providerData,
    availabilityEnabled,
    setAvailabilityEnabled
}) => {
    const [dateRange, setDateRange] = useState(() => {
        const start = dayjs();
        const end = start.add(6, 'day');
        return {
            startDate: start.format('YYYY-MM-DD'),
            endDate: end.format('YYYY-MM-DD'),
        };
    });

    // Prepare arguments for the query, conditionally adding queueId
    const availabilityQueryArgs = {
        sProvidingPlaceId: placeId,
        serviceId: selectedServiceId!,
        startDate: dateRange.startDate,
        endDate: dateRange.endDate,
        // Only include queueId in args if a specific queue (not 0 or undefined) is selected
        ...(selectedQueueIdProp && selectedQueueIdProp !== 0 && { queueId: selectedQueueIdProp }),
    };

    const { 
        data: availabilityData,
        isLoading: isLoadingAvailability,
        error: availabilityError,
        refetch: refetchAvailability
    } = useQuery<
        typeof availabilityQueryArgs, // Use the generated args type
        DailyAvailabilityResult[] 
    >(
        getProviderAvailability,
        availabilityQueryArgs, // Pass the constructed args object
        { enabled: availabilityEnabled && !!selectedServiceId } // Fetch only when enabled and service selected
    );

    // Refetch when service, queue, or date range changes while enabled
    useEffect(() => {
        if (availabilityEnabled && selectedServiceId) {
            refetchAvailability();
        }
    // Include selectedQueueIdProp in dependency array
    }, [selectedServiceId, selectedQueueIdProp, dateRange, availabilityEnabled, refetchAvailability]);

    const handleShowAvailability = () => {
        if (selectedServiceId) {
            setAvailabilityEnabled(true);
        } else {
            console.warn("No service selected to show availability.");
            // Consider using Ant Design message: message.info('Please select a service first');
        }
    };

    const handlePreviousWeek = () => {
        const currentStart = dayjs(dateRange.startDate);
        const newStart = currentStart.subtract(7, 'day');
        const newEnd = newStart.add(6, 'day');
        setDateRange({
            startDate: newStart.format('YYYY-MM-DD'),
            endDate: newEnd.format('YYYY-MM-DD'),
        });
    };

    const handleNextWeek = () => {
        const currentStart = dayjs(dateRange.startDate);
        const newStart = currentStart.add(7, 'day');
        const newEnd = newStart.add(6, 'day');
        setDateRange({
            startDate: newStart.format('YYYY-MM-DD'),
            endDate: newEnd.format('YYYY-MM-DD'),
        });
    };

    // Display when no service is selected
    if (!selectedServiceId) {
        return <Typography.Text type="secondary">Please select a service above to view availability.</Typography.Text>;
    }

    // Display button if availability check is not yet triggered
    if (!availabilityEnabled) {
        const selectedServiceName = services.find(s => s.id === selectedServiceId)?.title || 'Selected Service';

        // Create placeholder structure based on current dateRange state
        const startDay = dayjs(dateRange.startDate);
        const placeholderDays = Array.from({ length: 7 }).map((_, i) => startDay.add(i, 'day'));

        return (
            <div className="relative" style={{ width: '100%', minHeight: '250px' , padding:15 }}> {/* Container for positioning the overlay */}
                {/* Placeholder Grid */}
                <div className="flex overflow-x-auto pb-2 gap-5 opacity-50"> {/* Apply opacity to placeholder */}
                    {placeholderDays.map((day) => (
                        <div key={day.format('YYYY-MM-DD')} className="text-center">
                            <Typography.Text strong>{day.format('ddd')}</Typography.Text><br/>
                            <Typography.Text type="secondary" className="text-xs">{day.format('MMM D')}</Typography.Text>
                            <div className="mt-1 h-20 flex flex-col items-center justify-center"> {/* Fixed height for slot area */}
                                <Typography.Text type="secondary">-</Typography.Text> {/* Placeholder content */}
                            </div>
                        </div>
                    ))}
                </div>

                {/* Overlay with Button */}
                <div className="absolute inset-0 flex items-center justify-center bg-white/70 dark:bg-gray-800/70 rounded"> {/* Overlay */}
                     <Button onClick={handleShowAvailability} type="primary" icon={<ClockCircleOutlined />}>
                         Show Availability for {selectedServiceName}
                     </Button>
                 </div>
            </div>
        );
    }

    // Display loading state
    if (isLoadingAvailability) {
        return <Spin tip="Loading availability..." spinning={true}>
            <div className="flex items-center justify-center" style={{ width: '600px', height: '250px' }}>
                {/* <LoadingOutlined style={{ fontSize: 48 }} spin /> */}
            </div>
         </Spin>;
    }

    // Display error state
    if (availabilityError) {
        return <Alert message="Error loading availability" description={availabilityError.message || 'Could not retrieve availability slots.'} type="error" showIcon />;
    }

    // Check if data is empty AFTER loading and no error
    const isDataEmpty = !Array.isArray(availabilityData) || availabilityData.length === 0 || availabilityData.every(day => day.slots.length === 0);

    // Determine if the "Previous" button should be disabled (cannot go before today)
    const isPrevDisabled = dayjs(dateRange.startDate).isSameOrBefore(dayjs(), 'day');

    
    // Display availability slots with navigation
    return (
        <div style={{ 
            width: '100%' , 
            textAlign: 'center', 
            maxHeight: isExpanded ? '730px' : '440px',
            overflow: 'hidden'
        }}>
            {/* Navigation Buttons */}
            <Space style={{ marginBottom: '10px', width: '94%',  justifyContent: 'space-between' }}>
                <Button
                    icon={<LeftOutlined />}
                    onClick={handlePreviousWeek}
                    disabled={isPrevDisabled || isLoadingAvailability} // Disable if loading or already at/before today
                />
                 
                <Typography.Text strong>
                    {dayjs(dateRange.startDate).format('MMM D')} - {dayjs(dateRange.endDate).format('MMM D, YYYY')}
                </Typography.Text>
                <Button
                    icon={<RightOutlined />}
                    onClick={handleNextWeek}
                    disabled={isLoadingAvailability} // Disable if loading
                />
            </Space>
            <Divider />
            {/* Availability Grid */}
            {isDataEmpty ? (
                 <Typography.Text type="secondary">No available slots found for the selected service and date range.</Typography.Text>
            ) : (
            <>
                <div style={{ display: 'flex', overflowX: 'auto', paddingBottom: '10px',
                // gap: '20px' // Using flex gap below might be better
                }}>
                    {/* Filter out days with no slots before mapping */}
                    {Array.isArray(availabilityData) && availabilityData.map(({ date, slots }: DailyAvailabilityResult) => {
                        // Parse the UTC date string correctly. Append 'Z' to indicate UTC for reliable parsing.
                        // Or use dayjs.utc(date) if you have the UTC plugin
                        const displayDate = dayjs(date + 'T00:00:00Z'); // Treat date string as UTC midnight

                        return (
                            <div key={date} style={{
                                minWidth: '80px', // Use minWidth for flexible columns
                                textAlign: 'center',
                                // marginRight: '15px' // Add some space between columns
                                }}>
                                {/* Display date header using local time */}
                                <Typography.Text strong>{displayDate.format('ddd')}</Typography.Text><br/>
                                <Typography.Text type="secondary" style={{ fontSize: '0.8em' }}>{displayDate.format('MMM D')}</Typography.Text>
                                <div style={{ marginTop: '5px', maxWidth:"80px"}}>
                                    {/* Map through slots for the day */}
                                    {slots.map((slot: AvailabilitySlot, index: number) => {
                                        // slot.startTime is a JS Date object (UTC)
                                        // Convert UTC Date to local time using dayjs
                                        const localSlotTime = dayjs(slot.startTime);
                                        const selectedService = services.find(s => s.id === selectedServiceId);

                                        return (
                                            <Button 
                                                style={{ minWidth: '69px', margin: '2px' }} 
                                                disabled={slot.isBooked} 
                                                key={index} 
                                                size="large" 
                                                type={!slot.isBooked ? "primary" : "default"} 
                                                ghost={!slot.isBooked}
                                                onClick={() => {
                                                    if (!slot.isBooked && selectedService) {
                                                        const slotEndTime = dayjs(slot.startTime).add(selectedService.duration, 'minute').toDate();
                                                        onSlotSelect({
                                                            providerUser,
                                                            place,
                                                            service: selectedService,
                                                            startTime: slot.startTime,
                                                            endTime: slotEndTime,
                                                            queueId: slot.queueId,
                                                            providerData: providerData
                                                        });
                                                    }
                                                }}
                                            >
                                                {/* Format the local time */}
                                                {!slot.isBooked ? localSlotTime.format('HH:mm') : '-'}
                                            </Button>
                                        );
                                    })}
                                </div>
                            </div>
                        );
                    })}
                </div>
            </>
            )}
        </div>
    );
};

// Define structure for selected slot info passed to modal
export interface SelectedSlotInfo {
  providerUser: User;
  place: SProvidingPlace & { queues: Queue[] };
  service: Service;
  startTime: Date;
  endTime: Date;
  queueId: number;
  providerData: any;
}

const ProviderResultCard: React.FC<ProviderResultCardProps> = ({ provider }) => {
    const defaultServiceId = provider.services?.[0]?.id;
    const [selectedServiceIdForCard, setSelectedServiceIdForCard] = useState<number | undefined>(defaultServiceId);
    const primaryPlace = provider.providingPlaces?.[0]; 
    const [availabilityEnabled, setAvailabilityEnabled] = useState<boolean>(false);
    const [isExpanded, setIsExpanded] = useState(false);
    // Default selectedQueueId to 0 ("Any Queue")
    const [selectedQueueId, setSelectedQueueId] = useState<number | undefined>(0);
    const [selectedSlot, setSelectedSlot] = useState<SelectedSlotInfo & { providerData: ProviderResult } | null>(null);

    // --- Create options for Select components --- 
    const serviceOptions = useMemo(() => {
        if (!provider.services) return [];
        return provider.services.map(service => ({
            value: service.id,
            label: `${service.title} (${service.duration} min)`,
        }));
    }, [provider.services]);

    const queueOptions = useMemo(() => {
        const options = [{ value: 0, label: 'Any Queue' }]; // Start with "Any Queue"
        if (!primaryPlace?.queues) return options;
        // Map over the partial queue data (Pick<...>) is fine here
        primaryPlace.queues.forEach(queue => {
            options.push({ value: queue.id, label: queue.title });
        });
        return options;
    }, [primaryPlace?.queues]);
    // --- End options creation ---

    // Reset queue selection when place changes (if primaryPlace exists)
    useEffect(() => {
        setSelectedQueueId(0); // Reset to "Any Queue" when provider/place changes
    }, [primaryPlace]);

    // Effect for default service selection (remains the same)
    useEffect(() => {
        const newDefaultServiceId = provider.services?.[0]?.id;
        if (newDefaultServiceId !== selectedServiceIdForCard) {
             const currentSelectionIsValid = provider.services?.some(s => s.id === selectedServiceIdForCard);
             if (!currentSelectionIsValid) {
                 setSelectedServiceIdForCard(newDefaultServiceId);
             } else if (selectedServiceIdForCard === undefined) {
                 setSelectedServiceIdForCard(newDefaultServiceId);
             }
        }
    }, [provider.services, selectedServiceIdForCard]);

    const handleSlotSelect = (slotInfo: SelectedSlotInfo) => {
        setSelectedSlot({...slotInfo, providerData: provider});
    };

    const handleCloseModal = () => {
        setSelectedSlot(null);
    };

    return (
        <>
        <div className="border border-gray-200 rounded-lg p-5 mb-1 bg-gray-100 dark:bg-gray-800">
            <Space align="start" style={{ width: '100%' , justifyContent: 'space-between' }} wrap={false}>
                {/* Left Section: Provider Info - Using Descriptions */}
                <div style={{ flex: 1, width: '250px', paddingRight: '20px', display: 'flex', flexDirection: 'column' }}> 
                    <Descriptions column={1} size="small" style={{ marginBottom: '15px' }}>
                         {provider.title && provider.title !== provider.category?.title && (
                            <Descriptions.Item label="Provider">
                                {provider.title}
                            </Descriptions.Item>
                        )}
                        {provider.category?.title && (
                            <Descriptions.Item label="Specialty">
                                {provider.category.title}
                            </Descriptions.Item>
                        )}
                        {provider.phone && (
                            <Descriptions.Item label="Contact">
                                <PhoneOutlined style={{ marginRight: '5px' }}/> {provider.phone}
                            </Descriptions.Item>
                        )}
                        {primaryPlace && (
                            <Descriptions.Item label="Business Name">
                                <div><HomeOutlined style={{ marginRight: '5px' }}/> {primaryPlace.name}</div>
                            </Descriptions.Item>
                        )}
                        {primaryPlace && (
                            <Descriptions.Item label="City">
                                <div><EnvironmentOutlined style={{ marginRight: '5px' }}/> {primaryPlace.city}</div>
                            </Descriptions.Item>
                        )}
                         {primaryPlace && (
                            <Descriptions.Item label="Address">
                                <div><EnvironmentOutlined style={{ marginRight: '5px' }}/> {primaryPlace.address}</div>

                            </Descriptions.Item>
                        )}
                        {primaryPlace && (primaryPlace.parking || primaryPlace.elevator || primaryPlace.handicapAccess) && (
                            <Descriptions.Item label="Amenities">
                                <Space size={4} wrap>
                                    {primaryPlace.parking && <Tag>Parking</Tag>}
                                    {primaryPlace.elevator && <Tag>Elevator</Tag>}
                                    {primaryPlace.handicapAccess && <Tag>Handicap Access</Tag>}
                                </Space>
                            </Descriptions.Item>
                        )}
                    </Descriptions>
                    
                     {/* Service Selection Dropdown - Use options prop */}
                     {provider.services && provider.services.length > 0 ? (
                        <>
                            <Typography.Text strong style={{ marginBottom: '5px', display: 'block' }}>Select Service:</Typography.Text>
                            <Select<number>
                                value={selectedServiceIdForCard}
                                placeholder={!defaultServiceId ? "No services available" : "Select a service"}
                                style={{ width: '100%' }}
                                onChange={(value: number) => {
                                    setSelectedServiceIdForCard(value);
                                }}
                                allowClear={false}
                                disabled={!provider.services || provider.services.length === 0}
                                options={serviceOptions} // Use generated options
                            />
                        </>
                     ) : (
                         <Typography.Text type="secondary" style={{ marginTop: '10px' }}>No services offered.</Typography.Text>
                     )}

                     {/* Queue Selection Dropdown - Use options prop */}
                     {primaryPlace?.queues && primaryPlace.queues.length > 0 && (
                        <>
                            <Typography.Text strong style={{ marginTop: '10px', display: 'block' }}>Select Queue:</Typography.Text>
                            <Select<number | undefined> 
                                value={selectedQueueId} 
                                placeholder="Select a queue" 
                                style={{ width: '100%' }} 
                                onChange={(value: number | undefined) => setSelectedQueueId(value)} 
                                allowClear={false} 
                                options={queueOptions} // Use generated options
                            />
                        </>
                     )}
                     {/* TODO: Implement Booking Button */}
                     {/* <Button
                         type="primary"
                         style={{ marginTop: '10px' }}
                         disabled={!primaryPlace || !selectedServiceIdForCard} // Disable if no place or service
                     >
                         Book Appointment
                     </Button> */}
                </div>

                {/* Right Section: Availability */}
                <div style={{ flex: 2, overflow: 'hidden', paddingTop: '5px' , marginLeft: 'auto', minWidth:"550px" }}> {/* Added padding top */}
                    {primaryPlace ? (
                        <AvailabilityDisplay
                            availabilityEnabled={availabilityEnabled}
                            setAvailabilityEnabled={setAvailabilityEnabled}
                            providerUser={provider.user}
                            place={primaryPlace}
                            placeId={primaryPlace.id}
                            services={provider.services || []}
                            selectedServiceId={selectedServiceIdForCard}
                            selectedQueueIdProp={selectedQueueId} // PASS the selected queue ID down
                            onSlotSelect={handleSlotSelect}
                            isExpanded={isExpanded}
                            providerData={provider}
                        />
                    ) : (
                        <Typography.Text type="secondary">No primary location found for this provider.</Typography.Text>
                    )}
                </div>
            </Space>

            {/* Booking Confirmation Modal */}           
           <BookAppointmentModal
                 visible={!!selectedSlot}
                 onClose={handleCloseModal}
                 slotDetails={selectedSlot}
           />          
        </div>
        {availabilityEnabled && !isExpanded && (<div className="border border-gray-200 rounded-lg p-5 mb-4 bg-gray-100 dark:bg-gray-800" style={{
            display: 'flex',
            gap: '10px',
            justifyContent: 'center',
            alignItems: 'center',
        }}>

                <Button
                    type="primary"
                    onClick={() => setIsExpanded(true)}
                >
                    Show More Slots
                </Button>

        </div>)}
        {availabilityEnabled && isExpanded && (<div className="border border-gray-200 rounded-lg p-5 mb-4 bg-gray-100 dark:bg-gray-800" style={{
            display: 'flex',
            gap: '10px',
            justifyContent: 'center',
            alignItems: 'center',
        }}>
                    <Button
                        type="primary"
                        onClick={() => setIsExpanded(false)}
                    >
                        Check All Available Slots
                    </Button>
                    <Button
                        type="primary"
                        onClick={() => setIsExpanded(false)}
                    >
                        Show less Slots
                    </Button>
                </div>)}
        </>
      );
  };

export default ProviderResultCard;