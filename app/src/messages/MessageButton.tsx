import { Link as WaspRouterLink, routes } from 'wasp/client/router';
import { <PERSON><PERSON>, Button } from 'antd';
import { MessageOutlined } from '@ant-design/icons';

const MessageButton = () => {
  return (
    <Badge dot status="error" offset={[-5, 5]}>
      <WaspRouterLink to={routes.AdminMessagesRoute.to}>
        <Button
          type="text"
          icon={<MessageOutlined />}
          className="flex items-center justify-center dark:text-white"
        />
      </WaspRouterLink>
    </Badge>
  );
};

export default MessageButton;
