import { getChargilyClient } from './chargilyClient';
import { PaymentPlanId, paymentPlans, prettyPaymentPlanName } from '../plans';
import { HttpError } from 'wasp/server';
import * as z from 'zod';

/**
 * Validation schema for Chargily product data
 */
const chargilyProductSchema = z.object({
  name: z.string().min(1, 'Product name is required'),
  description: z.string().optional(),
  images: z.array(z.string().url()).optional(),
  metadata: z.record(z.any()).optional(),
});

/**
 * Validation schema for Chargily price data
 */
const chargilyPriceSchema = z.object({
  amount: z.number().positive('Amount must be positive'),
  currency: z.string().default('dzd'),
  product_id: z.string().min(1, 'Product ID is required'),
  metadata: z.record(z.any()).optional(),
});

/**
 * Create Chargily product
 */
export async function createChargilyProduct({
  name,
  description,
  images = [],
  metadata = {}
}: {
  name: string;
  description?: string;
  images?: string[];
  metadata?: Record<string, any>;
}) {
  try {
    const client = getChargilyClient();
    
    const productData = {
      name,
      description: description || `${name} - Subscription plan for appointment booking system`,
      images,
      metadata: {
        createdBy: 'wasp-app',
        createdAt: new Date().toISOString(),
        ...metadata,
      },
    };

    // Validate product data
    const validatedData = chargilyProductSchema.parse(productData);
    
    // Create product in Chargily
    const product = await client.createProduct(validatedData);
    
    console.log(`Chargily product created: ${product.id} - ${product.name}`);
    
    return product;
  } catch (error: any) {
    console.error('Error creating Chargily product:', error);
    throw new HttpError(500, `Failed to create Chargily product: ${error.message}`);
  }
}

/**
 * Update Chargily product
 */
export async function updateChargilyProduct(
  productId: string,
  updateData: {
    name?: string;
    description?: string;
    images?: string[];
    metadata?: Record<string, any>;
  }
) {
  try {
    const client = getChargilyClient();
    
    // Validate update data
    const validatedData = chargilyProductSchema.partial().parse(updateData);
    
    const product = await client.updateProduct(productId, validatedData);
    
    console.log(`Chargily product updated: ${productId}`);
    
    return product;
  } catch (error: any) {
    console.error('Error updating Chargily product:', error);
    throw new HttpError(500, `Failed to update Chargily product: ${error.message}`);
  }
}

/**
 * Create Chargily price for a product
 */
export async function createChargilyPrice({
  amount,
  currency = 'dzd',
  productId,
  metadata = {}
}: {
  amount: number;
  currency?: string;
  productId: string;
  metadata?: Record<string, any>;
}) {
  try {
    const client = getChargilyClient();
    
    const priceData = {
      amount: Math.round(amount * 100), // Convert to cents (Chargily expects amounts in cents)
      currency: currency.toLowerCase(),
      product_id: productId,
      metadata: {
        createdBy: 'wasp-app',
        createdAt: new Date().toISOString(),
        ...metadata,
      },
    };

    // Validate price data
    const validatedData = chargilyPriceSchema.parse(priceData);
    
    // Create price in Chargily
    const price = await client.createPrice(validatedData);
    
    console.log(`Chargily price created: ${price.id} - ${amount} ${currency.toUpperCase()}`);
    
    return price;
  } catch (error: any) {
    console.error('Error creating Chargily price:', error);
    throw new HttpError(500, `Failed to create Chargily price: ${error.message}`);
  }
}

/**
 * Update Chargily price
 */
export async function updateChargilyPrice(
  priceId: string,
  updateData: {
    metadata?: Record<string, any>;
  }
) {
  try {
    const client = getChargilyClient();

    // Ensure metadata is provided, use empty object if not
    const updateParams = {
      ...updateData,
      metadata: updateData.metadata || {}
    };

    // Note: Chargily typically only allows metadata updates for prices
    const price = await client.updatePrice(priceId, updateParams);
    
    console.log(`Chargily price updated: ${priceId}`);
    
    return price;
  } catch (error: any) {
    console.error('Error updating Chargily price:', error);
    throw new HttpError(500, `Failed to update Chargily price: ${error.message}`);
  }
}

/**
 * Map existing payment plans to Chargily products and prices
 * This function helps create Chargily equivalents of existing plans
 */
export async function createChargilyPlansFromExisting(): Promise<{
  products: Record<PaymentPlanId, any>;
  prices: Record<PaymentPlanId, any>;
}> {
  try {
    const products: Record<PaymentPlanId, any> = {} as any;
    const prices: Record<PaymentPlanId, any> = {} as any;

    // Define DZD pricing for each plan (these should be configured based on business requirements)
    const planPricing: Record<PaymentPlanId, number> = {
      [PaymentPlanId.Free]: 0, // Free plan
      [PaymentPlanId.Hobby]: 2500, // ~25 USD equivalent in DZD
      [PaymentPlanId.Pro]: 5000, // ~50 USD equivalent in DZD
      [PaymentPlanId.Credits10]: 1000, // ~10 USD equivalent in DZD
    };

    for (const [planId, plan] of Object.entries(paymentPlans)) {
      const paymentPlanId = planId as PaymentPlanId;
      const planName = prettyPaymentPlanName(paymentPlanId);
      const planEffect = plan.effect;
      
      // Create product
      const product = await createChargilyProduct({
        name: planName,
        description: `${planName} plan - ${planEffect.amount} credits${planEffect.kind === 'subscription' ? ' per month' : ' one-time'}${planEffect.queues ? `, ${planEffect.queues} queues` : ''}`,
        metadata: {
          planId: paymentPlanId,
          planType: planEffect.type,
          planKind: planEffect.kind,
          creditsAmount: planEffect.amount,
          queues: planEffect.queues,
        },
      });

      products[paymentPlanId] = product;

      // Create price (skip for free plan)
      if (planPricing[paymentPlanId] > 0) {
        const price = await createChargilyPrice({
          amount: planPricing[paymentPlanId],
          currency: 'dzd',
          productId: product.id,
          metadata: {
            planId: paymentPlanId,
            planType: planEffect.type,
            planKind: planEffect.kind,
          },
        });

        prices[paymentPlanId] = price;
      }
    }

    console.log('Successfully created Chargily products and prices for all plans');
    
    return { products, prices };
  } catch (error: any) {
    console.error('Error creating Chargily plans from existing:', error);
    throw new HttpError(500, `Failed to create Chargily plans: ${error.message}`);
  }
}

/**
 * Get Chargily product by ID
 */
export async function getChargilyProduct(productId: string) {
  try {
    const client = getChargilyClient();
    return await client.getProduct(productId);
  } catch (error: any) {
    console.error('Error fetching Chargily product:', error);
    throw new HttpError(500, `Failed to fetch Chargily product: ${error.message}`);
  }
}

/**
 * Get Chargily price by ID
 */
export async function getChargilyPrice(priceId: string) {
  try {
    const client = getChargilyClient();
    return await client.getPrice(priceId);
  } catch (error: any) {
    console.error('Error fetching Chargily price:', error);
    throw new HttpError(500, `Failed to fetch Chargily price: ${error.message}`);
  }
}

/**
 * List all Chargily products
 */
export async function listChargilyProducts() {
  try {
    const client = getChargilyClient();
    return await client.listProducts();
  } catch (error: any) {
    console.error('Error listing Chargily products:', error);
    throw new HttpError(500, `Failed to list Chargily products: ${error.message}`);
  }
}

/**
 * List all Chargily prices
 */
export async function listChargilyPrices() {
  try {
    const client = getChargilyClient();
    return await client.listPrices();
  } catch (error: any) {
    console.error('Error listing Chargily prices:', error);
    throw new HttpError(500, `Failed to list Chargily prices: ${error.message}`);
  }
}
