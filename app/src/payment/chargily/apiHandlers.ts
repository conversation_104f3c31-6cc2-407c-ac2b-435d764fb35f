import { type Request, type Response } from 'express';
import { HttpError } from 'wasp/server';
import {
  createChargilyCustomer,
  getChargilyCustomer,
  updateChargilyCustomer,
  deleteChargilyCustomer,
  getOrCreateChargilyCustomer
} from './customerUtils';
import { createChargilyCheckoutSession, createChargilyPaymentLink } from './checkoutUtils';
import { PaymentPlanId, paymentPlans, prettyPaymentPlanName } from '../plans';
import * as z from 'zod';

// Validation schemas
const createCustomerSchema = z.object({
  name: z.string().min(1, 'Name is required').optional(),
  phone: z.string().optional(),
  address: z.object({
    country: z.string().optional(),
    state: z.string().optional(),
    address: z.string().optional(),
  }).optional(),
});

const updateCustomerSchema = z.object({
  name: z.string().min(1, 'Name is required').optional(),
  email: z.string().email('Valid email is required').optional(),
  phone: z.string().optional(),
  address: z.object({
    country: z.string().optional(),
    state: z.string().optional(),
    address: z.string().optional(),
  }).optional(),
  metadata: z.record(z.any()).optional(),
});

const customerIdSchema = z.object({
  customerId: z.string().min(1, 'Customer ID is required'),
});

const checkoutSessionSchema = z.object({
  planId: z.nativeEnum(PaymentPlanId),
  paymentMethod: z.enum(['edahabia', 'cib']).optional(),
  metadata: z.record(z.any()).optional(),
});

const paymentLinkSchema = z.object({
  planId: z.nativeEnum(PaymentPlanId),
  name: z.string().optional(),
  metadata: z.record(z.any()).optional(),
});

type CreateCustomerInput = z.infer<typeof createCustomerSchema>;
type UpdateCustomerInput = z.infer<typeof updateCustomerSchema>;
type CustomerIdInput = z.infer<typeof customerIdSchema>;
type CheckoutSessionInput = z.infer<typeof checkoutSessionSchema>;
type PaymentLinkInput = z.infer<typeof paymentLinkSchema>;

/**
 * POST /api/auth/payment/chargily/customer
 * Create a new Chargily customer for the authenticated user
 */
export const handleCreateChargilyCustomer = async (req: Request, res: Response, context: any) => {
  try {
    if (!context.user) {
      throw new HttpError(401, 'Authentication required');
    }

    const validatedBody = createCustomerSchema.safeParse(req.body);
    if (!validatedBody.success) {
      const errorDetails = validatedBody.error.issues.map(issue => 
        `${issue.path.join('.')} - ${issue.message}`
      ).join('; ');
      throw new HttpError(400, `Invalid request body: ${errorDetails}`);
    }

    const { name, phone, address } = validatedBody.data;

    // Check if user already has a Chargily customer
    const existingUser = await context.entities.User.findUnique({
      where: { id: context.user.id },
      select: { chargilyCustomerId: true, firstName: true, lastName: true }
    });

    if (existingUser?.chargilyCustomerId) {
      return res.status(400).json({
        success: false,
        message: 'User already has a Chargily customer account'
      });
    }

    // Create Chargily customer
    const result = await createChargilyCustomer({
      userId: context.user.id,
      userEmail: context.user.email!,
      userName: name || (existingUser?.firstName && existingUser?.lastName 
        ? `${existingUser.firstName} ${existingUser.lastName}` 
        : undefined),
      userPhone: phone,
      userAddress: address,
      prismaUserDelegate: context.entities.User,
    });

    res.status(201).json({
      success: true,
      message: 'Chargily customer created successfully',
      data: {
        customerId: result.customerId,
        userId: context.user.id,
      }
    });
  } catch (error: any) {
    console.error('Error creating Chargily customer:', error);
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message
      });
    }
    return res.status(500).json({
      success: false,
      message: 'Failed to create Chargily customer'
    });
  }
};

/**
 * GET /api/auth/payment/chargily/customer
 * Get the current user's Chargily customer information
 */
export const handleGetChargilyCustomer = async (req: Request, res: Response, context: any) => {
  try {
    if (!context.user) {
      throw new HttpError(401, 'Authentication required');
    }

    // Get user's Chargily customer ID
    const user = await context.entities.User.findUnique({
      where: { id: context.user.id },
      select: { chargilyCustomerId: true }
    });

    if (!user?.chargilyCustomerId) {
      return res.status(404).json({
        success: false,
        message: 'No Chargily customer found for this user'
      });
    }

    // Fetch customer from Chargily
    const customer = await getChargilyCustomer(user.chargilyCustomerId);

    res.status(200).json({
      success: true,
      message: 'Chargily customer retrieved successfully',
      data: {
        customer,
        userId: context.user.id,
      }
    });
  } catch (error: any) {
    console.error('Error fetching Chargily customer:', error);
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message
      });
    }
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch Chargily customer'
    });
  }
};

/**
 * PUT /api/auth/payment/chargily/customer
 * Update the current user's Chargily customer information
 */
export const handleUpdateChargilyCustomer = async (req: Request, res: Response, context: any) => {
  try {
    if (!context.user) {
      throw new HttpError(401, 'Authentication required');
    }

    const validatedBody = updateCustomerSchema.safeParse(req.body);
    if (!validatedBody.success) {
      const errorDetails = validatedBody.error.issues.map(issue => 
        `${issue.path.join('.')} - ${issue.message}`
      ).join('; ');
      throw new HttpError(400, `Invalid request body: ${errorDetails}`);
    }

    // Get user's Chargily customer ID
    const user = await context.entities.User.findUnique({
      where: { id: context.user.id },
      select: { chargilyCustomerId: true }
    });

    if (!user?.chargilyCustomerId) {
      return res.status(404).json({
        success: false,
        message: 'No Chargily customer found for this user'
      });
    }

    // Update customer in Chargily
    const updatedCustomer = await updateChargilyCustomer(
      user.chargilyCustomerId,
      validatedBody.data
    );

    res.status(200).json({
      success: true,
      message: 'Chargily customer updated successfully',
      data: {
        customer: updatedCustomer,
        userId: context.user.id,
      }
    });
  } catch (error: any) {
    console.error('Error updating Chargily customer:', error);
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message
      });
    }
    return res.status(500).json({
      success: false,
      message: 'Failed to update Chargily customer'
    });
  }
};

/**
 * DELETE /api/auth/payment/chargily/customer
 * Delete the current user's Chargily customer
 */
export const handleDeleteChargilyCustomer = async (req: Request, res: Response, context: any) => {
  try {
    if (!context.user) {
      throw new HttpError(401, 'Authentication required');
    }

    // Get user's Chargily customer ID
    const user = await context.entities.User.findUnique({
      where: { id: context.user.id },
      select: { chargilyCustomerId: true }
    });

    if (!user?.chargilyCustomerId) {
      return res.status(404).json({
        success: false,
        message: 'No Chargily customer found for this user'
      });
    }

    // Delete customer from Chargily
    await deleteChargilyCustomer(user.chargilyCustomerId);

    // Clear customer ID from user record
    await context.entities.User.update({
      where: { id: context.user.id },
      data: {
        chargilyCustomerId: null,
        chargilyPaymentMethod: null,
        chargilyWebhookData: null,
      }
    });

    res.status(200).json({
      success: true,
      message: 'Chargily customer deleted successfully',
      data: {
        userId: context.user.id,
      }
    });
  } catch (error: any) {
    console.error('Error deleting Chargily customer:', error);
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message
      });
    }
    return res.status(500).json({
      success: false,
      message: 'Failed to delete Chargily customer'
    });
  }
};

/**
 * POST /api/auth/payment/chargily/customer/get-or-create
 * Get existing or create new Chargily customer for the authenticated user
 */
export const handleGetOrCreateChargilyCustomer = async (req: Request, res: Response, context: any) => {
  try {
    if (!context.user) {
      throw new HttpError(401, 'Authentication required');
    }

    const validatedBody = createCustomerSchema.safeParse(req.body);
    if (!validatedBody.success) {
      const errorDetails = validatedBody.error.issues.map(issue => 
        `${issue.path.join('.')} - ${issue.message}`
      ).join('; ');
      throw new HttpError(400, `Invalid request body: ${errorDetails}`);
    }

    const { name, phone, address } = validatedBody.data;

    // Get existing user data
    const existingUser = await context.entities.User.findUnique({
      where: { id: context.user.id },
      select: { firstName: true, lastName: true }
    });

    // Get or create Chargily customer
    const result = await getOrCreateChargilyCustomer({
      userId: context.user.id,
      userEmail: context.user.email!,
      userName: name || (existingUser?.firstName && existingUser?.lastName 
        ? `${existingUser.firstName} ${existingUser.lastName}` 
        : undefined),
      userPhone: phone,
      userAddress: address,
      prismaUserDelegate: context.entities.User,
    });

    res.status(result.isNew ? 201 : 200).json({
      success: true,
      message: result.isNew 
        ? 'Chargily customer created successfully' 
        : 'Chargily customer retrieved successfully',
      data: {
        customerId: result.customerId,
        userId: context.user.id,
        isNew: result.isNew,
      }
    });
  } catch (error: any) {
    console.error('Error getting or creating Chargily customer:', error);
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message
      });
    }
    return res.status(500).json({
      success: false,
      message: 'Failed to get or create Chargily customer'
    });
  }
};

/**
 * POST /api/auth/payment/chargily/checkout
 * Create a Chargily checkout session for the authenticated user
 */
export const handleCreateChargilyCheckout = async (req: Request, res: Response, context: any) => {
  try {
    if (!context.user) {
      throw new HttpError(401, 'Authentication required');
    }

    const validatedBody = checkoutSessionSchema.safeParse(req.body);
    if (!validatedBody.success) {
      const errorDetails = validatedBody.error.issues.map(issue =>
        `${issue.path.join('.')} - ${issue.message}`
      ).join('; ');
      throw new HttpError(400, `Invalid request body: ${errorDetails}`);
    }

    const { planId, paymentMethod, metadata } = validatedBody.data;


    console.log('validatedBody.data:', validatedBody.data);
    // Get payment plan
    const paymentPlan = paymentPlans[planId];
    if (!paymentPlan) {
      throw new HttpError(400, `Invalid plan ID: ${planId}`);
    }

    // Get or create Chargily customer
    const customerResult = await getOrCreateChargilyCustomer({
      userId: context.user.id,
      userEmail: context.user.email!,
      prismaUserDelegate: context.entities.User,
    });

    console.log('Customer result:', customerResult);
    // Create checkout session
    const session = await createChargilyCheckoutSession({
      userId: context.user.id,
      userEmail: context.user.email!,
      paymentPlan,
      chargilyCustomerId: customerResult.customerId,
      prismaUserDelegate: context.entities.User,
      planId,
    });

    console.log('Session result:', session);
    res.status(201).json({
      success: true,
      message: 'Chargily checkout session created successfully',
      data: {
        sessionId: session.id,
        sessionUrl: session.url,
        planId,
        planName: prettyPaymentPlanName(planId),
        customerId: customerResult.customerId,
      }
    });
  } catch (error: any) {
    console.error('Error creating Chargily checkout session:', error);
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message
      });
    }
    return res.status(500).json({
      success: false,
      message: 'Failed to create Chargily checkout session'
    });
  }
};

/**
 * POST /api/auth/payment/chargily/payment-links
 * Create a Chargily payment link for the authenticated user
 */
export const handleCreateChargilyPaymentLink = async (req: Request, res: Response, context: any) => {
  try {
    if (!context.user) {
      throw new HttpError(401, 'Authentication required');
    }

    const validatedBody = paymentLinkSchema.safeParse(req.body);
    if (!validatedBody.success) {
      const errorDetails = validatedBody.error.issues.map(issue =>
        `${issue.path.join('.')} - ${issue.message}`
      ).join('; ');
      throw new HttpError(400, `Invalid request body: ${errorDetails}`);
    }

    const { planId, name, metadata } = validatedBody.data;

    // Get payment plan
    const paymentPlan = paymentPlans[planId];
    if (!paymentPlan) {
      throw new HttpError(400, `Invalid plan ID: ${planId}`);
    }

    // Get or create Chargily customer
    const customerResult = await getOrCreateChargilyCustomer({
      userId: context.user.id,
      userEmail: context.user.email!,
      prismaUserDelegate: context.entities.User,
    });

    // Create payment link
    const paymentLink = await createChargilyPaymentLink({
      userId: context.user.id,
      paymentPlan,
      chargilyCustomerId: customerResult.customerId,
      metadata: {
        customName: name,
        ...metadata,
      },
    });

    res.status(201).json({
      success: true,
      message: 'Chargily payment link created successfully',
      data: {
        paymentLinkId: paymentLink.paymentLinkId,
        paymentLinkUrl: paymentLink.paymentLinkUrl,
        planId,
        planName: prettyPaymentPlanName(planId),
        customerId: customerResult.customerId,
        name: name || `${prettyPaymentPlanName(planId)} Payment`,
      }
    });
  } catch (error: any) {
    console.error('Error creating Chargily payment link:', error);
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message
      });
    }
    return res.status(500).json({
      success: false,
      message: 'Failed to create Chargily payment link'
    });
  }
};

/**
 * GET /api/auth/payment/chargily/payment-links
 * Get user's Chargily payment links and transactions
 */
export const handleGetChargilyPaymentLinks = async (req: Request, res: Response, context: any) => {
  try {
    if (!context.user) {
      throw new HttpError(401, 'Authentication required');
    }

    // Get user's Chargily payments from database
    const chargilyPayments = await context.entities.ChargilyPayment.findMany({
      where: {
        userId: context.user.id,
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: 50, // Limit to last 50 payments
    });

    // Get user's Chargily customer info
    const user = await context.entities.User.findUnique({
      where: { id: context.user.id },
      select: {
        chargilyCustomerId: true,
        chargilyPaymentMethod: true,
        chargilyWebhookData: true,
      }
    });

    // Group payments by type
    const paymentLinks = chargilyPayments.filter(p => p.chargilyPaymentLinkId);
    const checkoutSessions = chargilyPayments.filter(p => p.chargilyCheckoutId && !p.chargilyPaymentLinkId);

    res.status(200).json({
      success: true,
      message: 'Chargily payment data retrieved successfully',
      data: {
        customer: {
          id: user?.chargilyCustomerId,
          paymentMethod: user?.chargilyPaymentMethod,
        },
        paymentLinks: paymentLinks.map(payment => ({
          id: payment.id,
          chargilyPaymentLinkId: payment.chargilyPaymentLinkId,
          status: payment.status,
          amount: payment.amount,
          currency: payment.currency,
          planId: payment.planId,
          planType: payment.planType,
          creditsAllocated: payment.creditsAllocated,
          createdAt: payment.createdAt,
          updatedAt: payment.updatedAt,
        })),
        checkoutSessions: checkoutSessions.map(payment => ({
          id: payment.id,
          chargilyCheckoutId: payment.chargilyCheckoutId,
          status: payment.status,
          amount: payment.amount,
          currency: payment.currency,
          planId: payment.planId,
          planType: payment.planType,
          creditsAllocated: payment.creditsAllocated,
          createdAt: payment.createdAt,
          updatedAt: payment.updatedAt,
        })),
        summary: {
          totalPayments: chargilyPayments.length,
          totalPaymentLinks: paymentLinks.length,
          totalCheckoutSessions: checkoutSessions.length,
          totalCreditsFromChargily: chargilyPayments
            .filter(p => p.status === 'paid')
            .reduce((sum, p) => sum + (p.creditsAllocated || 0), 0),
        }
      }
    });
  } catch (error: any) {
    console.error('Error fetching Chargily payment links:', error);
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message
      });
    }
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch Chargily payment links'
    });
  }
};

/**
 * GET /api/auth/payment/chargily/status
 * Get comprehensive Chargily payment status for the authenticated user
 */
export const handleGetChargilyPaymentStatus = async (req: Request, res: Response, context: any) => {
  try {
    if (!context.user) {
      throw new HttpError(401, 'Authentication required');
    }

    // Get user's complete information
    const user = await context.entities.User.findUnique({
      where: { id: context.user.id },
      select: {
        id: true,
        email: true,
        subscriptionStatus: true,
        subscriptionPlan: true,
        credits: true,
        queues: true,
        datePaid: true,
        chargilyCustomerId: true,
        chargilyPaymentMethod: true,
        chargilyWebhookData: true,
      }
    });

    if (!user) {
      throw new HttpError(404, 'User not found');
    }

    // Get Chargily payment history
    const chargilyPayments = await context.entities.ChargilyPayment.findMany({
      where: {
        userId: context.user.id,
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: 20, // Last 20 payments
    });

    // Calculate Chargily-specific statistics
    const paidPayments = chargilyPayments.filter(p => p.status === 'paid');
    const pendingPayments = chargilyPayments.filter(p => p.status === 'pending');
    const failedPayments = chargilyPayments.filter(p => p.status === 'failed');

    const totalChargilyCredits = paidPayments.reduce((sum, p) => sum + (p.creditsAllocated || 0), 0);
    const totalChargilyAmount = paidPayments.reduce((sum, p) => sum + p.amount, 0);

    // Get latest payment
    const latestPayment = chargilyPayments[0];

    // Check if user has active Chargily subscription
    const hasChargilySubscription = paidPayments.some(p => p.planType === 'subscription');
    const latestSubscriptionPayment = paidPayments.find(p => p.planType === 'subscription');

    // Calculate current month usage (for subscription tracking)
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);

    const monthlyChargilyPayments = chargilyPayments.filter(p =>
      p.createdAt >= startOfMonth && p.createdAt <= endOfMonth && p.status === 'paid'
    );

    // Parse webhook data for additional insights
    let webhookData = null;
    try {
      webhookData = user.chargilyWebhookData ? JSON.parse(user.chargilyWebhookData) : null;
    } catch (error) {
      console.warn('Failed to parse Chargily webhook data:', error);
    }

    res.status(200).json({
      success: true,
      message: 'Chargily payment status retrieved successfully',
      data: {
        user: {
          id: user.id,
          email: user.email,
          credits: user.credits,
          queues: user.queues,
          datePaid: user.datePaid,
        },
        chargily: {
          customerId: user.chargilyCustomerId,
          paymentMethod: user.chargilyPaymentMethod,
          hasCustomer: !!user.chargilyCustomerId,
          hasSubscription: hasChargilySubscription,
          webhookData,
        },
        subscription: {
          status: user.subscriptionStatus,
          plan: user.subscriptionPlan,
          isActive: !!user.subscriptionStatus && user.subscriptionStatus !== 'deleted',
          latestChargilySubscription: latestSubscriptionPayment ? {
            id: latestSubscriptionPayment.id,
            planId: latestSubscriptionPayment.planId,
            creditsAllocated: latestSubscriptionPayment.creditsAllocated,
            amount: latestSubscriptionPayment.amount,
            currency: latestSubscriptionPayment.currency,
            createdAt: latestSubscriptionPayment.createdAt,
          } : null,
        },
        payments: {
          total: chargilyPayments.length,
          paid: paidPayments.length,
          pending: pendingPayments.length,
          failed: failedPayments.length,
          latest: latestPayment ? {
            id: latestPayment.id,
            status: latestPayment.status,
            amount: latestPayment.amount,
            currency: latestPayment.currency,
            planId: latestPayment.planId,
            planType: latestPayment.planType,
            createdAt: latestPayment.createdAt,
          } : null,
          thisMonth: monthlyChargilyPayments.length,
        },
        statistics: {
          totalCreditsFromChargily: totalChargilyCredits,
          totalAmountPaid: totalChargilyAmount,
          averagePaymentAmount: paidPayments.length > 0 ? totalChargilyAmount / paidPayments.length : 0,
          currency: 'dzd',
          firstPaymentDate: paidPayments.length > 0 ? paidPayments[paidPayments.length - 1].createdAt : null,
          lastPaymentDate: paidPayments.length > 0 ? paidPayments[0].createdAt : null,
        },
        recentPayments: chargilyPayments.slice(0, 5).map(payment => ({
          id: payment.id,
          status: payment.status,
          amount: payment.amount,
          currency: payment.currency,
          planId: payment.planId,
          planType: payment.planType,
          creditsAllocated: payment.creditsAllocated,
          paymentMethod: payment.paymentMethod,
          createdAt: payment.createdAt,
        })),
      }
    });
  } catch (error: any) {
    console.error('Error fetching Chargily payment status:', error);
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message
      });
    }
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch Chargily payment status'
    });
  }
};
