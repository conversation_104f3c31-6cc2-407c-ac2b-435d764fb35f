import type { CreateCustomerArgs } from '../paymentProcessor';
import { getChargilyClient } from './chargilyClient';
import { HttpError } from 'wasp/server';
import * as z from 'zod';

/**
 * Validation schema for Chargily customer data
 */
const chargilyCustomerSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Valid email is required'),
  phone: z.string().optional(),
  address: z.object({
    country: z.string().min(1, 'Country is required'),
    state: z.string().min(1, 'State is required'),
    address: z.string().min(1, 'Address is required'),
  }).optional(),
  metadata: z.record(z.any()).optional(),
});

/**
 * Create Chargily customer
 */
export async function createChargilyCustomer({
  userId,
  userEmail,
  userName,
  userPhone,
  userAddress,
  prismaUserDelegate
}: CreateCustomerArgs): Promise<{ customerId: string }> {
  try {
    const client = getChargilyClient();
    
    // Prepare customer data
    const customerData = {
      name: userName || userEmail.split('@')[0], // Use email prefix if no name provided
      email: userEmail,
      phone: userPhone || undefined,
      address: {
        country: userAddress?.country || 'DZ', // Default to Algeria
        state: userAddress?.state || 'Algiers', // Default to Algiers
        address: userAddress?.address || 'Default Address', // Provide default address
      },
      metadata: {
        userId,
        createdBy: 'wasp-app',
        createdAt: new Date().toISOString(),
      },
    };

    // Validate customer data
    const validatedData = chargilyCustomerSchema.parse(customerData);
    
    // Create customer in Chargily
    const customer = await client.createCustomer(validatedData);
    
    // Update user record with Chargily customer ID
    await prismaUserDelegate.update({
      where: { id: userId },
      data: {
        chargilyCustomerId: customer.id,
        chargilyWebhookData: JSON.stringify({
          customerCreated: new Date().toISOString(),
          customerData: {
            id: customer.id,
            email: customer.email,
            name: customer.name,
          }
        })
      }
    });

    console.log(`Chargily customer created for user ${userId}: ${customer.id}`);
    
    return { customerId: customer.id };
  } catch (error: any) {
    console.error('Error creating Chargily customer:', error);
    throw new HttpError(500, `Failed to create Chargily customer: ${error.message}`);
  }
}

/**
 * Get Chargily customer by ID
 */
export async function getChargilyCustomer(customerId: string) {
  try {
    const client = getChargilyClient();
    return await client.getCustomer(customerId);
  } catch (error: any) {
    console.error('Error fetching Chargily customer:', error);
    throw new HttpError(500, `Failed to fetch Chargily customer: ${error.message}`);
  }
}

/**
 * Update Chargily customer
 */
export async function updateChargilyCustomer(
  customerId: string,
  updateData: {
    name?: string;
    email?: string;
    phone?: string;
    address?: {
      country?: string;
      state?: string;
      address?: string;
    };
    metadata?: Record<string, any>;
  }
) {
  try {
    const client = getChargilyClient();
    
    // Validate update data
    const validatedData = chargilyCustomerSchema.partial().parse(updateData);
    
    return await client.updateCustomer(customerId, validatedData);
  } catch (error: any) {
    console.error('Error updating Chargily customer:', error);
    throw new HttpError(500, `Failed to update Chargily customer: ${error.message}`);
  }
}

/**
 * Delete Chargily customer
 */
export async function deleteChargilyCustomer(customerId: string) {
  try {
    const client = getChargilyClient();
    return await client.deleteCustomer(customerId);
  } catch (error: any) {
    console.error('Error deleting Chargily customer:', error);
    throw new HttpError(500, `Failed to delete Chargily customer: ${error.message}`);
  }
}

/**
 * Get or create Chargily customer for a user
 */
export async function getOrCreateChargilyCustomer({
  userId,
  userEmail,
  userName,
  userPhone,
  userAddress,
  prismaUserDelegate
}: CreateCustomerArgs): Promise<{ customerId: string; isNew: boolean }> {
  try {
    // Check if user already has a Chargily customer ID
    const existingUser = await prismaUserDelegate.findUnique({
      where: { id: userId },
      select: { chargilyCustomerId: true }
    });

    if (existingUser?.chargilyCustomerId) {
      // Verify the customer still exists in Chargily
      try {
        await getChargilyCustomer(existingUser.chargilyCustomerId);
        return { customerId: existingUser.chargilyCustomerId, isNew: false };
      } catch (error) {
        console.warn(`Chargily customer ${existingUser.chargilyCustomerId} not found, creating new one`);
        // Customer doesn't exist in Chargily, create a new one
      }
    }

    // Create new customer
    const result = await createChargilyCustomer({
      userId,
      userEmail,
      userName,
      userPhone,
      userAddress,
      prismaUserDelegate
    });

    return { customerId: result.customerId, isNew: true };
  } catch (error: any) {
    console.error('Error getting or creating Chargily customer:', error);
    throw new HttpError(500, `Failed to get or create Chargily customer: ${error.message}`);
  }
}
