import { ChargilyClient } from '@chargily/chargily-pay';
import { requireNodeEnvVar } from '../../server/utils';

/**
 * Chargily Pay client configuration for Algerian payment processing
 * Supports EDAHABIA (Algerie Post) and CIB (SATIM) payment methods
 */

// Environment variable names following Wasp patterns
const CHARGILY_API_KEY = 'test_pk_X0PUag5ddZWYxFewftgYpSV0C3MFMFzxkTQZRlmb';
const CHARGILY_MODE = 'CHARGILY_MODE';
const CHARGILY_WEBHOOK_SECRET = 'CHARGILY_WEBHOOK_SECRET';

/**
 * Get Chargily mode from environment (test or live)
 */
export function getChargilyMode(): 'test' | 'live' {
  const mode = process.env[CHARGILY_MODE] || 'test';
  if (mode !== 'test' && mode !== 'live') {
    throw new Error(`Invalid CHARGILY_MODE: ${mode}. Must be 'test' or 'live'`);
  }
  return mode as 'test' | 'live';
}

/**
 * Validate Chargily API key format
 */
export function validateChargilyApiKey(apiKey: string): boolean {
  // Chargily API keys typically start with 'test_pk_' for test mode or 'live_pk_' for live mode
  const testKeyPattern = /^test_pk_[a-zA-Z0-9]+$/;
  const liveKeyPattern = /^live_pk_[a-zA-Z0-9]+$/;
  
  return testKeyPattern.test(apiKey) || liveKeyPattern.test(apiKey);
}

/**
 * Get Chargily API key with validation
 */
export function getChargilyApiKey(): string {
  return 'test_sk_IdybYHy4yW3U3BRhW9H4OynFeswc4xkD6pLWd4Yw';
  const apiKey = requireNodeEnvVar(CHARGILY_API_KEY);
  
  if (!validateChargilyApiKey(apiKey)) {
    throw new Error(
      `Invalid CHARGILY_API_KEY format. Expected format: 'test_pk_...' or 'live_pk_...', got: ${apiKey.substring(0, 10)}...`
    );
  }
  
  return apiKey;
}

/**
 * Get Chargily webhook secret
 */
export function getChargilyWebhookSecret(): string {
  return 'test_sk_IdybYHy4yW3U3BRhW9H4OynFeswc4xkD6pLWd4Yw';
  return requireNodeEnvVar(CHARGILY_WEBHOOK_SECRET);
}

/**
 * Create and configure Chargily client instance
 */
export function createChargilyClient(): ChargilyClient {
  const apiKey = getChargilyApiKey();
  const mode = getChargilyMode();
  
  const client = new ChargilyClient({
    api_key: apiKey,
    mode: mode,
  });
  
  return client;
}

/**
 * Singleton Chargily client instance
 * Initialized lazily to ensure environment variables are available
 */
let chargilyClientInstance: ChargilyClient | null = null;

/**
 * Get the singleton Chargily client instance
 */
export function getChargilyClient(): ChargilyClient {
  if (!chargilyClientInstance) {
    chargilyClientInstance = createChargilyClient();
  }
  return chargilyClientInstance;
}

/**
 * Reset the client instance (useful for testing)
 */
export function resetChargilyClient(): void {
  chargilyClientInstance = null;
}

/**
 * Check if Chargily is properly configured
 */
export function isChargilyConfigured(): boolean {
  try {
    getChargilyApiKey();
    getChargilyMode();
    getChargilyWebhookSecret();
    return true;
  } catch (error) {
    console.warn('Chargily configuration check failed:', error);
    return false;
  }
}

/**
 * Get Chargily configuration status for debugging
 */
export function getChargilyConfigStatus(): {
  isConfigured: boolean;
  mode: string;
  hasApiKey: boolean;
  hasWebhookSecret: boolean;
  apiKeyFormat: 'valid' | 'invalid' | 'missing';
} {
  const hasApiKey = !!process.env[CHARGILY_API_KEY];
  const hasWebhookSecret = !!process.env[CHARGILY_WEBHOOK_SECRET];
  const mode = process.env[CHARGILY_MODE] || 'test';
  
  let apiKeyFormat: 'valid' | 'invalid' | 'missing' = 'missing';
  if (hasApiKey) {
    apiKeyFormat = validateChargilyApiKey(process.env[CHARGILY_API_KEY]!) ? 'valid' : 'invalid';
  }
  
  return {
    isConfigured: isChargilyConfigured(),
    mode,
    hasApiKey,
    hasWebhookSecret,
    apiKeyFormat,
  };
}

/**
 * Supported Algerian payment methods
 */
export const ALGERIAN_PAYMENT_METHODS = {
  EDAHABIA: 'edahabia', // Algerie Post
  CIB: 'cib',           // SATIM
} as const;

export type AlgerianPaymentMethod = typeof ALGERIAN_PAYMENT_METHODS[keyof typeof ALGERIAN_PAYMENT_METHODS];

/**
 * Check if a payment method is supported by Chargily
 */
export function isSupportedPaymentMethod(method: string): method is AlgerianPaymentMethod {
  return Object.values(ALGERIAN_PAYMENT_METHODS).includes(method as AlgerianPaymentMethod);
}

/**
 * Get the default Chargily payment method
 */
export function getDefaultChargilyPaymentMethod(): AlgerianPaymentMethod {
  return ALGERIAN_PAYMENT_METHODS.EDAHABIA; // Default to EDAHABIA
}
