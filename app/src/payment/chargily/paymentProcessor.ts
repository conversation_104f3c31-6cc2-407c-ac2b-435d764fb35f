import type { 
  CreateCheckoutSessionArgs, 
  FetchCustomerPortalUrlArgs, 
  PaymentProcessor,
  CreateCustomerArgs,
  CreatePaymentLinkArgs
} from '../paymentProcessor';
import { getChargilyClient, ALGERIAN_PAYMENT_METHODS } from './chargilyClient';
import { createChargilyCheckoutSession, createChargilyPaymentLink } from './checkoutUtils';
import { createChargilyCustomer } from './customerUtils';
import { chargilyWebhook, chargilyMiddlewareConfigFn } from './webhook';
import { HttpError } from 'wasp/server';

/**
 * Chargily Payment Processor Implementation
 * Supports Algerian payment methods: EDAHABIA (Algerie Post) and CIB (SATIM)
 */
export const chargilyPaymentProcessor: PaymentProcessor = {
  id: 'chargily',
  
  /**
   * Create checkout session for Chargily payments
   */
  createCheckoutSession: async ({ userId, userEmail, paymentPlan, prismaUserDelegate }: CreateCheckoutSessionArgs) => {
    if (!userId) {
      throw new Error('User ID needed to create Chargily Checkout Session');
    }
    
    if (!userEmail) {
      throw new Error('User email needed to create Chargily Checkout Session');
    }

    try {
      // Get or create Chargily customer
      let chargilyCustomerId: string;
      
      // Check if user already has a Chargily customer ID
      const existingUser = await prismaUserDelegate.findUnique({
        where: { id: userId },
        select: { chargilyCustomerId: true, firstName: true, lastName: true, mobileNumber: true }
      });

      if (existingUser?.chargilyCustomerId) {
        chargilyCustomerId = existingUser.chargilyCustomerId;
      } else {
        // Create new Chargily customer
        const customerResult = await createChargilyCustomer({
          userId,
          userEmail,
          userName: existingUser?.firstName && existingUser?.lastName 
            ? `${existingUser.firstName} ${existingUser.lastName}` 
            : undefined,
          userPhone: existingUser?.mobileNumber || undefined,
          prismaUserDelegate
        });
        chargilyCustomerId = customerResult.customerId;
      }

      // Create checkout session
      const session = await createChargilyCheckoutSession({
        userId,
        userEmail,
        paymentPlan,
        chargilyCustomerId,
        prismaUserDelegate
      });

      return { session };
    } catch (error: any) {
      console.error('Error creating Chargily checkout session:', error);
      throw new HttpError(500, `Failed to create Chargily checkout session: ${error.message}`);
    }
  },

  /**
   * Fetch customer portal URL (Chargily doesn't have a built-in customer portal like Stripe)
   * Returns null as Chargily manages subscriptions differently
   */
  fetchCustomerPortalUrl: async (args: FetchCustomerPortalUrlArgs) => {
    // Chargily doesn't provide a customer portal like Stripe/LemonSqueezy
    // Users can manage their payments through the main application interface
    console.log('Chargily customer portal requested for user:', args.userId);
    return null;
  },

  /**
   * Webhook handler for Chargily payment events
   */
  webhook: chargilyWebhook,

  /**
   * Webhook middleware configuration
   */
  webhookMiddlewareConfigFn: chargilyMiddlewareConfigFn,

  /**
   * Create Chargily customer
   */
  createCustomer: async (args: CreateCustomerArgs) => {
    return await createChargilyCustomer(args);
  },

  /**
   * Create Chargily payment link
   */
  createPaymentLink: async ({ userId, paymentPlan, customerId, metadata }: CreatePaymentLinkArgs) => {
    try {
      const result = await createChargilyPaymentLink({
        userId,
        paymentPlan,
        chargilyCustomerId: customerId,
        metadata,
      });

      return {
        paymentLinkUrl: result.paymentLinkUrl,
        paymentLinkId: result.paymentLinkId,
      };
    } catch (error: any) {
      console.error('Error creating Chargily payment link:', error);
      throw new HttpError(500, `Failed to create Chargily payment link: ${error.message}`);
    }
  },

  /**
   * Check if Chargily is available for a specific region
   * Chargily is specifically designed for Algeria (DZ)
   */
  isAvailableForRegion: (countryCode?: string) => {
    // Chargily is primarily for Algeria, but can be used by other regions
    // Prioritize for Algerian customers
    if (countryCode === 'DZ' || countryCode === 'dz') {
      return true;
    }
    // Available as an option for other regions, but not prioritized
    return true;
  },
};

/**
 * Helper function to validate Chargily payment method
 */
export function validateChargilyPaymentMethod(method: string): boolean {
  return Object.values(ALGERIAN_PAYMENT_METHODS).includes(method as any);
}

/**
 * Get default payment method for Chargily (EDAHABIA is most common)
 */
export function getDefaultChargilyPaymentMethod(): string {
  return ALGERIAN_PAYMENT_METHODS.EDAHABIA;
}

/**
 * Check if Chargily processor is properly configured
 */
export function isChargilyProcessorReady(): boolean {
  try {
    getChargilyClient();
    return true;
  } catch (error) {
    console.warn('Chargily processor not ready:', error);
    return false;
  }
}
