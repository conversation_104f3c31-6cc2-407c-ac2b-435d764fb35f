import type { PaymentPlan, PaymentPlanId } from '../plans';
import type { PrismaClient } from '@prisma/client';
import { getChargilyClient, getDefaultChargilyPaymentMethod } from './chargilyClient';
import { getChargilyPrice } from './productUtils';
import { HttpError } from 'wasp/server';

/**
 * Arguments for creating Chargily checkout session
 */
export interface CreateChargilyCheckoutSessionArgs {
  userId: string;
  userEmail: string;
  paymentPlan: PaymentPlan;
  chargilyCustomerId?: string;
  prismaUserDelegate: PrismaClient['user'];
  planId?: PaymentPlanId;
}

/**
 * Create Chargily checkout session
 */
export async function createChargilyCheckoutSession({
  userId,
  userEmail,
  paymentPlan,
  chargilyCustomerId,
  prismaUserDelegate,
  planId,
}: CreateChargilyCheckoutSessionArgs): Promise<{ id: string; url: string }> {
  try {
    const client = getChargilyClient();
    
    // Get plan details
    const planEffect = paymentPlan.effect;
    console.log('planEffect:', planEffect);
    const chargilyPriceId = paymentPlan.getPaymentProcessorPlanId('chargily');

    console.log('chargilyPriceId:', chargilyPriceId);
    // const chargilyPriceId = '01k0xk3cqk4024525nh6b15wvp';

    // Verify the Chargily price exists (optional validation)
    try {
      await getChargilyPrice(chargilyPriceId);
    } catch (error) {
      console.warn(`Chargily price ${chargilyPriceId} not found, proceeding with checkout anyway`);
    }

    // Create checkout session
    const checkout = await client.createCheckout({
      items: [
        {
          price: chargilyPriceId, // Chargily price ID from environment variables
          quantity: 1,
        },
      ],
      // success_url: `${process.env.WASP_WEB_CLIENT_URL}/payment/success?session_id={CHECKOUT_SESSION_ID}`,
      success_url: 'https://dalti.adscloud.org/subscription',
      // failure_url: `${process.env.WASP_WEB_CLIENT_URL}/payment/failure?session_id={CHECKOUT_SESSION_ID}`,
      failure_url: 'https://dalti.adscloud.org/subscription',
      payment_method: getDefaultChargilyPaymentMethod(), // 'edahabia' by default
      locale: 'ar', // Arabic locale for Algerian customers
      pass_fees_to_customer: false, // Absorb fees by default
      metadata: {
        userId,
        userEmail,
        planId: planId,
        planType: planEffect.type,
        creditsAmount: planEffect.amount.toString(),
        ...(planEffect.kind === 'subscription' && { queues: planEffect.queues?.toString() })
      },
    });

    // Store checkout session in database for tracking
    await prismaUserDelegate.update({
      where: { id: userId },
      data: {
        chargilyWebhookData: JSON.stringify({
          lastCheckoutId: checkout.id,
          lastCheckoutCreated: new Date().toISOString(),
        })
      }
    });

    return {
      id: checkout.id,
      url: checkout.checkout_url,
    };
  } catch (error: any) {
    console.error('Error creating Chargily checkout session:', error);
    throw new HttpError(500, `Failed to create Chargily checkout: ${error.message}`);
  }
}

/**
 * Create Chargily payment link (for reusable payment URLs)
 */
export async function createChargilyPaymentLink({
  userId,
  paymentPlan,
  chargilyCustomerId,
  metadata = {}
}: {
  userId: string;
  paymentPlan: PaymentPlan;
  chargilyCustomerId?: string;
  metadata?: Record<string, any>;
}): Promise<{ paymentLinkUrl: string; paymentLinkId: string }> {
  try {
    const client = getChargilyClient();
    
    const planEffect = paymentPlan.effect;
    const planId = paymentPlan.getPaymentProcessorPlanId('chargily');
    
    // Create payment link
    const paymentLink = await client.createPaymentLink({
      name: `${planEffect.kind === 'subscription' ? 'Subscription' : 'Credits'} Payment`,
      items: [
        {
          price: planId,
          quantity: 1,
          adjustable_quantity: false,
        },
      ],
      after_completion_message: 'Thank you for your payment! Your account has been updated.',
      locale: 'ar',
      pass_fees_to_customer: false,
      metadata: {
        userId,
        planId: planEffect.kind,
        planType: planEffect.type,
        creditsAmount: planEffect.amount.toString(),
        ...metadata,
      },
    });

    return {
      paymentLinkUrl: paymentLink.url,
      paymentLinkId: paymentLink.id,
    };
  } catch (error: any) {
    console.error('Error creating Chargily payment link:', error);
    throw new HttpError(500, `Failed to create Chargily payment link: ${error.message}`);
  }
}
