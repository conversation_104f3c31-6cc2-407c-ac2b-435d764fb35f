import { type MiddlewareConfigFn, HttpError } from 'wasp/server';
import { type PaymentsWebhook } from 'wasp/server/api';
import { type PrismaClient } from '@prisma/client';
import express from 'express';
import { verifySignature } from '@chargily/chargily-pay';
import { getChargilyWebhookSecret } from './chargilyClient';
import crypto  from 'crypto';
/**
 * Chargily webhook handler
 * Processes payment events from Chargily Pay
 */
export const chargilyWebhook: PaymentsWebhook = async (request, response, context) => {
  try {
    console.log('Chargily webhook received');
    const rawBody = request.body;
    console.log('webhook rawBody:', JSON.stringify(rawBody));
    const payload = JSON.stringify(request.body);
    const signature = request.get('signature');
    console.log('webhook signature:', signature);
    if (!signature) {
      throw new HttpError(400, 'Chargily Webhook Signature Not Provided');
    }

    // Verify webhook signature
    const webhookSecret = getChargilyWebhookSecret();
    // console.log('webhook secret:', webhookSecret);
    // const isValidSignature = verifySignature(rawBody, signature, webhookSecret);
    // console.log('is valid signature:', isValidSignature);
   

    const isValidSignature = crypto.createHmac('sha256', webhookSecret)
        .update(payload)
        .digest('hex');


    if (!isValidSignature) {
      throw new HttpError(400, 'Invalid Chargily webhook signature');
    }

    const event = request.body;
    console.log('Chargily webhook event received:', event.type);

    // Extract user ID from metadata
    const userId = event.data?.metadata?.userId;
    if (!userId) {
      console.warn('No userId found in Chargily webhook metadata');
      return response.status(200).json({ received: true });
    }

    const prismaUserDelegate = context.entities.User;
    
    // Handle different event types
    switch (event.type) {
      case 'checkout.paid':
        await handleCheckoutPaid(event, userId, prismaUserDelegate, context);
        break;
      case 'checkout.failed':
        await handleCheckoutFailed(event, userId, prismaUserDelegate, context);
        break;
      case 'invoice.payment_succeeded':
        await handleInvoicePaymentSucceeded(event, userId, prismaUserDelegate, context);
        break;
      case 'invoice.payment_failed':
        await handleInvoicePaymentFailed(event, userId, prismaUserDelegate, context);
        break;
      default:
        console.log(`Unhandled Chargily webhook event type: ${event.type}`);
    }

    response.status(200).json({ received: true });
  } catch (error: any) {
    console.error('Error processing Chargily webhook:', error);
    if (error instanceof HttpError) {
      return response.status(error.statusCode).json({ error: error.message });
    }
    return response.status(500).json({ error: 'Internal server error' });
  }
};

/**
 * Handle successful checkout payment
 */
async function handleCheckoutPaid(
  event: any,
  userId: string,
  prismaUserDelegate: PrismaClient['user'],
  context: any
) {
  try {
    const checkoutData = event.data;
    const metadata = checkoutData.metadata || {};
    
    console.log(`Processing paid checkout for user ${userId}`);

    // Extract payment details
    const amount = checkoutData.amount;
    const currency = checkoutData.currency || 'dzd';
    const planType = metadata.planType;
    const creditsAmount = parseInt(metadata.creditsAmount) || 0;
    const queues = parseInt(metadata.queues) || 1;

    // Create payment record
    await context.entities.ChargilyPayment.create({
      data: {
        userId,
        chargilyPaymentId: checkoutData.id,
        chargilyCustomerId: checkoutData.customer_id,
        chargilyCheckoutId: checkoutData.id,
        status: 'paid',
        amount: amount / 100, // Convert from cents to main currency unit
        currency,
        paymentMethod: checkoutData.payment_method,
        planId: metadata.planId,
        planType,
        creditsAllocated: creditsAmount,
        metadata: JSON.stringify(metadata),
        webhookData: JSON.stringify(event),
      }
    });

    // Update user account based on plan type
    if (planType === 'recurrent') {
      // Handle subscription
      await prismaUserDelegate.update({
        where: { id: userId },
        data: {
          subscriptionStatus: 'active',
          subscriptionPlan: metadata.planId,
          credits: { increment: creditsAmount },
          queues: queues,
          datePaid: new Date(),
          chargilyWebhookData: JSON.stringify({
            lastPayment: {
              id: checkoutData.id,
              amount,
              currency,
              paidAt: new Date().toISOString(),
            }
          })
        }
      });
    } else if (planType === 'one-time') {
      // Handle one-time credit purchase
      await prismaUserDelegate.update({
        where: { id: userId },
        data: {
          credits: { increment: creditsAmount },
          datePaid: new Date(),
          chargilyWebhookData: JSON.stringify({
            lastPayment: {
              id: checkoutData.id,
              amount,
              currency,
              paidAt: new Date().toISOString(),
            }
          })
        }
      });
    }

    console.log(`Successfully processed Chargily payment for user ${userId}`);
  } catch (error: any) {
    console.error('Error handling Chargily checkout paid:', error);
    throw error;
  }
}

/**
 * Handle failed checkout payment
 */
async function handleCheckoutFailed(
  event: any,
  userId: string,
  prismaUserDelegate: PrismaClient['user'],
  context: any
) {
  try {
    const checkoutData = event.data;
    
    console.log(`Processing failed checkout for user ${userId}`);

    // Create payment record for failed payment
    await context.entities.ChargilyPayment.create({
      data: {
        userId,
        chargilyPaymentId: checkoutData.id,
        chargilyCustomerId: checkoutData.customer_id,
        chargilyCheckoutId: checkoutData.id,
        status: 'failed',
        amount: checkoutData.amount / 100,
        currency: checkoutData.currency || 'dzd',
        paymentMethod: checkoutData.payment_method,
        metadata: JSON.stringify(checkoutData.metadata || {}),
        webhookData: JSON.stringify(event),
      }
    });

    console.log(`Recorded failed Chargily payment for user ${userId}`);
  } catch (error: any) {
    console.error('Error handling Chargily checkout failed:', error);
    throw error;
  }
}

/**
 * Handle successful invoice payment (for subscriptions)
 */
async function handleInvoicePaymentSucceeded(
  event: any,
  userId: string,
  prismaUserDelegate: PrismaClient['user'],
  context: any
) {
  // Similar to handleCheckoutPaid but for recurring payments
  console.log(`Processing successful invoice payment for user ${userId}`);
  // Implementation would be similar to handleCheckoutPaid
}

/**
 * Handle failed invoice payment (for subscriptions)
 */
async function handleInvoicePaymentFailed(
  event: any,
  userId: string,
  prismaUserDelegate: PrismaClient['user'],
  context: any
) {
  console.log(`Processing failed invoice payment for user ${userId}`);
  // Implementation would handle subscription suspension/retry logic
}

/**
 * Middleware configuration for Chargily webhooks
 */
export const chargilyMiddlewareConfigFn: MiddlewareConfigFn = (middlewareConfig) => {
  middlewareConfig.set('express.raw', express.raw({ type: 'application/json' }));
  return middlewareConfig;
};
