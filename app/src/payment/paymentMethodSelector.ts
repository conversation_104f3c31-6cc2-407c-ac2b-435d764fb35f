import { getAvailableProcessorsForRegion, getPaymentProcessor, type PaymentProcessorId } from './paymentProcessor';

/**
 * Payment method information for UI display
 */
export interface PaymentMethodInfo {
  id: PaymentProcessorId;
  name: string;
  displayName: string;
  description: string;
  supportedMethods: string[];
  currency: string;
  isRecommended: boolean;
  isAvailable: boolean;
  priority: number; // Lower number = higher priority
  region: string[];
  logo?: string;
  features: string[];
}

/**
 * User preferences for payment method selection
 */
export interface UserPaymentPreferences {
  preferredProcessor?: PaymentProcessorId;
  countryCode?: string;
  currency?: string;
  excludeProcessors?: PaymentProcessorId[];
}

/**
 * Get available payment methods based on user location and preferences
 */
export function getAvailablePaymentMethods(
  userPreferences: UserPaymentPreferences = {}
): PaymentMethodInfo[] {
  const { countryCode, preferredProcessor, excludeProcessors = [] } = userPreferences;
  
  // Get base available processors for the region
  const availableProcessorIds = getAvailableProcessorsForRegion(countryCode)
    .filter(id => !excludeProcessors.includes(id));

  // Map processors to payment method info
  const paymentMethods: PaymentMethodInfo[] = availableProcessorIds.map(processorId => {
    const processor = getPaymentProcessor(processorId);
    
    switch (processorId) {
      case 'chargily':
        return {
          id: 'chargily',
          name: 'Chargily Pay',
          displayName: 'Chargily Pay',
          description: 'Algerian payment gateway supporting EDAHABIA and CIB cards',
          supportedMethods: ['edahabia', 'cib'],
          currency: 'DZD',
          isRecommended: countryCode === 'DZ' || countryCode === 'dz',
          isAvailable: true, // Client-side can't check server config
          priority: countryCode === 'DZ' || countryCode === 'dz' ? 1 : 3,
          region: ['DZ', 'Algeria'],
          logo: '/static/chargily-logo.png',
          features: [
            'EDAHABIA (Algerie Post)',
            'CIB (SATIM)',
            'Local Algerian payment methods',
            'DZD currency support'
          ]
        };
      
      case 'lemonsqueezy':
        return {
          id: 'lemonsqueezy',
          name: 'Lemon Squeezy',
          displayName: 'Credit/Debit Card',
          description: 'Global payment processing with credit and debit cards',
          supportedMethods: ['card', 'paypal'],
          currency: 'USD',
          isRecommended: countryCode !== 'DZ' && countryCode !== 'dz',
          isAvailable: true,
          priority: countryCode === 'DZ' || countryCode === 'dz' ? 2 : 1,
          region: ['Global'],
          logo: '/static/lemonsqueezy-logo.png',
          features: [
            'Credit/Debit Cards',
            'PayPal',
            'Global coverage',
            'USD pricing'
          ]
        };
      
      case 'stripe':
        return {
          id: 'stripe',
          name: 'Stripe',
          displayName: 'Credit/Debit Card (Stripe)',
          description: 'Global payment processing with extensive payment method support',
          supportedMethods: ['card', 'apple_pay', 'google_pay'],
          currency: 'USD',
          isRecommended: false, // Not primary option
          isAvailable: true,
          priority: 3,
          region: ['Global'],
          logo: '/static/stripe-logo.png',
          features: [
            'Credit/Debit Cards',
            'Apple Pay',
            'Google Pay',
            'Global coverage'
          ]
        };
      
      default:
        return {
          id: processorId,
          name: processorId,
          displayName: processorId,
          description: 'Payment processor',
          supportedMethods: ['card'],
          currency: 'USD',
          isRecommended: false,
          isAvailable: true,
          priority: 5,
          region: ['Global'],
          features: []
        };
    }
  });

  // Apply user preferences
  const processedMethods = paymentMethods.map(method => ({
    ...method,
    isRecommended: method.isRecommended || method.id === preferredProcessor,
    priority: method.id === preferredProcessor ? 0 : method.priority
  }));

  // Sort by priority (lower number = higher priority)
  return processedMethods.sort((a, b) => a.priority - b.priority);
}

/**
 * Get the recommended payment method for a user
 */
export function getRecommendedPaymentMethod(
  userPreferences: UserPaymentPreferences = {}
): PaymentMethodInfo | null {
  const availableMethods = getAvailablePaymentMethods(userPreferences);
  return availableMethods.find(method => method.isRecommended) || availableMethods[0] || null;
}

/**
 * Get payment method by processor ID
 */
export function getPaymentMethodInfo(
  processorId: PaymentProcessorId,
  userPreferences: UserPaymentPreferences = {}
): PaymentMethodInfo | null {
  const availableMethods = getAvailablePaymentMethods(userPreferences);
  return availableMethods.find(method => method.id === processorId) || null;
}

/**
 * Check if a payment processor is available for the user
 */
export function isPaymentProcessorAvailable(
  processorId: PaymentProcessorId,
  userPreferences: UserPaymentPreferences = {}
): boolean {
  const method = getPaymentMethodInfo(processorId, userPreferences);
  return method?.isAvailable || false;
}

/**
 * Detect user's country code from various sources
 */
export function detectUserCountryCode(request?: {
  headers?: Record<string, string | string[] | undefined>;
  ip?: string;
}): string | undefined {
  if (!request?.headers) return undefined;

  // Try various header sources for country detection
  const countryHeaders = [
    'cf-ipcountry',        // Cloudflare
    'x-country-code',      // Custom header
    'x-forwarded-country', // Some proxies
    'x-real-country',      // Some CDNs
  ];

  for (const header of countryHeaders) {
    const value = request.headers[header];
    if (typeof value === 'string' && value.length === 2) {
      return value.toUpperCase();
    }
  }

  return undefined;
}

/**
 * Get payment method selection recommendations based on context
 */
export function getPaymentMethodRecommendations(
  userPreferences: UserPaymentPreferences = {}
): {
  primary: PaymentMethodInfo | null;
  alternatives: PaymentMethodInfo[];
  algerian: PaymentMethodInfo | null;
  global: PaymentMethodInfo[];
} {
  const availableMethods = getAvailablePaymentMethods(userPreferences);
  
  const primary = getRecommendedPaymentMethod(userPreferences);
  const alternatives = availableMethods.filter(method => method.id !== primary?.id);
  
  const algerian = availableMethods.find(method => method.id === 'chargily') || null;
  const global = availableMethods.filter(method => 
    method.region.includes('Global') && method.id !== primary?.id
  );

  return {
    primary,
    alternatives,
    algerian,
    global
  };
}

/**
 * Validate payment processor selection
 */
export function validatePaymentProcessorSelection(
  processorId: PaymentProcessorId,
  userPreferences: UserPaymentPreferences = {}
): { isValid: boolean; reason?: string } {
  const availableMethods = getAvailablePaymentMethods(userPreferences);
  const method = availableMethods.find(m => m.id === processorId);

  if (!method) {
    return { isValid: false, reason: 'Payment processor not available in your region' };
  }

  if (!method.isAvailable) {
    return { isValid: false, reason: 'Payment processor is currently unavailable' };
  }

  return { isValid: true };
}

/**
 * Get currency information for a payment processor
 */
export function getPaymentProcessorCurrency(processorId: PaymentProcessorId): string {
  switch (processorId) {
    case 'chargily':
      return 'DZD';
    case 'lemonsqueezy':
    case 'stripe':
    default:
      return 'USD';
  }
}

/**
 * Format currency amount for display
 */
export function formatCurrencyAmount(amount: number, currency: string): string {
  const formatters: Record<string, Intl.NumberFormat> = {
    'DZD': new Intl.NumberFormat('ar-DZ', { style: 'currency', currency: 'DZD' }),
    'USD': new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }),
  };

  const formatter = formatters[currency] || formatters['USD'];
  return formatter.format(amount);
}
