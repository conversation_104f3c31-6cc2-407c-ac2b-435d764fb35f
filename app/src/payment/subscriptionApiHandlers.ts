import { type Request, type Response } from 'express';
import { HttpError } from 'wasp/server';
import { generateCheckoutSession } from 'wasp/server/operations';
import { PaymentPlanId, paymentPlans, prettyPaymentPlanName, SubscriptionStatus, paymentPlanCards } from './plans';
import { paymentProcessor, getPaymentProcessor, getAvailableProcessorsForRegion, type PaymentProcessorId } from './paymentProcessor';
import * as z from 'zod';

// Validation schemas
const checkoutSessionSchema = z.object({
  planId: z.nativeEnum(PaymentPlanId),
  paymentProcessor: z.enum(['lemonsqueezy', 'stripe', 'chargily']).optional()
});

const usageQuerySchema = z.object({
  period: z.enum(['week', 'month', 'year']).optional().default('month')
});

type CheckoutSessionInput = z.infer<typeof checkoutSessionSchema>;
type UsageQueryInput = z.infer<typeof usageQuerySchema>;

/**
 * GET /api/auth/subscription/plans
 * Fetch all available subscription plans
 */
export const handleGetSubscriptionPlans = async (req: Request, res: Response, context: any) => {
  try {
    const plans = Object.entries(paymentPlans).map(([planId, plan]) => ({
      id: planId,
      name: prettyPaymentPlanName(planId as PaymentPlanId),
      description: paymentPlanCards[planId as PaymentPlanId].description,
      price: paymentPlanCards[planId as PaymentPlanId].price,
      features: paymentPlanCards[planId as PaymentPlanId].features,
      effect: plan.effect,
      isSubscription: plan.effect.type === 'recurrent',
      isOneTime: plan.effect.type === 'one-time'
    }));

    res.status(200).json({
      success: true,
      message: 'Subscription plans retrieved successfully',
      data: {
        plans
      }
    });
  } catch (error: any) {
    console.error('Error fetching subscription plans:', error);
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message
      });
    }
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch subscription plans'
    });
  }
};

/**
 * GET /api/auth/subscription/status
 * Get current user's subscription status and credits
 */
export const handleGetUserSubscriptionStatus = async (req: Request, res: Response, context: any) => {
  try {
    if (!context.user) {
      throw new HttpError(401, 'Authentication required');
    }

    const user = await context.entities.User.findUnique({
      where: { id: context.user.id },
      select: {
        id: true,
        email: true,
        subscriptionStatus: true,
        subscriptionPlan: true,
        credits: true,
        queues: true,
        datePaid: true,
        paymentProcessorUserId: true,
        lemonSqueezyCustomerPortalUrl: true
      }
    });

    if (!user) {
      throw new HttpError(404, 'User not found');
    }

    const isSubscribed = !!user.subscriptionStatus && user.subscriptionStatus !== SubscriptionStatus.Deleted;
    const currentPlan = user.subscriptionPlan ? paymentPlans[user.subscriptionPlan as PaymentPlanId] : null;

    res.status(200).json({
      success: true,
      message: 'User subscription status retrieved successfully',
      data: {
        user: {
          id: user.id,
          email: user.email,
          credits: user.credits,
          queues: user.queues,
          datePaid: user.datePaid
        },
        subscription: {
          isActive: isSubscribed,
          status: user.subscriptionStatus,
          planId: user.subscriptionPlan,
          planName: user.subscriptionPlan ? prettyPaymentPlanName(user.subscriptionPlan as PaymentPlanId) : null,
          planDetails: currentPlan ? {
            effect: currentPlan.effect,
            features: paymentPlanCards[user.subscriptionPlan as PaymentPlanId]?.features || []
          } : null
        },
        hasCustomerPortal: !!user.lemonSqueezyCustomerPortalUrl
      }
    });
  } catch (error: any) {
    console.error('Error fetching user subscription status:', error);
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message
      });
    }
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch subscription status'
    });
  }
};

/**
 * POST /api/auth/subscription/checkout
 * Create checkout session for a subscription plan
 */
export const handleCreateCheckoutSession = async (req: Request, res: Response, context: any) => {
  try {
    if (!context.user) {
      throw new HttpError(401, 'Authentication required');
    }

    const validatedBody = checkoutSessionSchema.safeParse(req.body);
    if (!validatedBody.success) {
      const errorDetails = validatedBody.error.issues.map(issue =>
        `${issue.path.join('.')} - ${issue.message}`
      ).join('; ');
      throw new HttpError(400, `Invalid request body: ${errorDetails}`);
    }

    const { planId, paymentProcessor: requestedProcessor } = validatedBody.data;

    // Determine which payment processor to use
    let selectedProcessor: PaymentProcessorId;

    if (requestedProcessor) {
      // Use the requested processor if specified
      selectedProcessor = requestedProcessor;
    } else {
      // Default to the current default processor for backward compatibility
      selectedProcessor = 'lemonsqueezy';
    }

    // Get the payment processor instance
    const processor = getPaymentProcessor(selectedProcessor);

    // Get the payment plan
    const paymentPlan = paymentPlans[planId];
    if (!paymentPlan) {
      throw new HttpError(400, `Invalid plan ID: ${planId}`);
    }

    // Create checkout session using the selected processor
    const checkoutSession = await processor.createCheckoutSession({
      userId: context.user.id,
      userEmail: context.user.email!,
      paymentPlan,
      prismaUserDelegate: context.entities.User,
    });

    res.status(200).json({
      success: true,
      message: 'Checkout session created successfully',
      data: {
        sessionUrl: checkoutSession.session.url,
        sessionId: checkoutSession.session.id,
        planId,
        planName: prettyPaymentPlanName(planId),
        paymentProcessor: selectedProcessor
      }
    });
  } catch (error: any) {
    console.error('Error creating checkout session:', error);
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message
      });
    }
    return res.status(500).json({
      success: false,
      message: 'Failed to create checkout session'
    });
  }
};

/**
 * GET /api/auth/subscription/customer-portal
 * Get customer portal URL for subscription management
 */
export const handleGetCustomerPortalUrl = async (req: Request, res: Response, context: any) => {
  try {
    if (!context.user) {
      throw new HttpError(401, 'Authentication required');
    }

    // Use payment processor directly to get customer portal URL
    const customerPortalUrl = await paymentProcessor.fetchCustomerPortalUrl({
      userId: context.user.id,
      prismaUserDelegate: context.entities.User,
    });

    if (!customerPortalUrl) {
      return res.status(404).json({
        success: false,
        message: 'Customer portal not available. You need to have an active subscription first.'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Customer portal URL retrieved successfully',
      data: {
        customerPortalUrl
      }
    });
  } catch (error: any) {
    console.error('Error fetching customer portal URL:', error);
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message
      });
    }
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch customer portal URL'
    });
  }
};

/**
 * GET /api/auth/payment/credits/used
 * Get user's used credits for the current month (completed appointments)
 */
export const handleGetUsedCredits = async (req: Request, res: Response, context: any) => {
  try {
    if (!context.user) {
      throw new HttpError(401, 'Authentication required');
    }

    // Calculate current month date range
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);

    // Count completed appointments for this month
    // Assuming appointments are linked to users through CustomerFolder
    const completedAppointments = await context.entities.Appointment.count({
      where: {
        customerFolder: {
          userId: context.user.id
        },
        status: 'completed',
        createdAt: {
          gte: startOfMonth,
          lte: endOfMonth
        }
      }
    });

    // Get user's current credit balance
    const user = await context.entities.User.findUnique({
      where: { id: context.user.id },
      select: {
        credits: true,
        subscriptionPlan: true
      }
    });

    if (!user) {
      throw new HttpError(404, 'User not found');
    }

    // Calculate total credits allocated this month (from subscription plan)
    const currentPlan = user.subscriptionPlan ? paymentPlans[user.subscriptionPlan as PaymentPlanId] : null;
    const monthlyCreditsAllocated = currentPlan?.effect.kind === 'subscription' ? currentPlan.effect.amount : 0;

    res.status(200).json({
      success: true,
      message: 'Used credits retrieved successfully',
      data: {
        period: {
          month: now.getMonth() + 1,
          year: now.getFullYear(),
          startDate: startOfMonth.toISOString(),
          endDate: endOfMonth.toISOString()
        },
        credits: {
          used: completedAppointments,
          remaining: user.credits,
          monthlyAllocated: monthlyCreditsAllocated,
          totalAvailable: user.credits + completedAppointments
        },
        appointments: {
          completedThisMonth: completedAppointments
        },
        subscription: {
          planId: user.subscriptionPlan,
          planName: user.subscriptionPlan ? prettyPaymentPlanName(user.subscriptionPlan as PaymentPlanId) : null
        }
      }
    });
  } catch (error: any) {
    console.error('Error fetching used credits:', error);
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message
      });
    }
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch used credits'
    });
  }
};

/**
 * GET /api/auth/payment/usage
 * Get user's current usage statistics and limits (same as credits/used but with period support)
 */
export const handleGetUsageStats = async (req: Request, res: Response, context: any) => {
  try {
    if (!context.user) {
      throw new HttpError(401, 'Authentication required');
    }

    const validatedQuery = usageQuerySchema.safeParse(req.query);
    if (!validatedQuery.success) {
      throw new HttpError(400, 'Invalid query parameters');
    }

    const { period } = validatedQuery.data;

    // Calculate date range based on period
    const now = new Date();
    let startDate: Date;
    let endDate: Date;

    switch (period) {
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        endDate = new Date();
        break;
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);
        break;
      case 'year':
        startDate = new Date(now.getFullYear(), 0, 1);
        endDate = new Date(now.getFullYear(), 11, 31, 23, 59, 59, 999);
        break;
      default:
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);
    }

    // Count completed appointments for the specified period
    const completedAppointments = await context.entities.Appointment.count({
      where: {
        customerFolder: {
          userId: context.user.id
        },
        status: 'completed',
        createdAt: {
          gte: startDate,
          lte: endDate
        }
      }
    });

    // Get user's current credit balance and subscription info
    const user = await context.entities.User.findUnique({
      where: { id: context.user.id },
      select: {
        credits: true,
        queues: true,
        subscriptionPlan: true,
        subscriptionStatus: true
      }
    });

    if (!user) {
      throw new HttpError(404, 'User not found');
    }

    // Calculate credits allocated based on subscription plan
    const currentPlan = user.subscriptionPlan ? paymentPlans[user.subscriptionPlan as PaymentPlanId] : null;
    const creditsAllocated = currentPlan?.effect.kind === 'subscription' ? currentPlan.effect.amount : 0;
    const queuesUsed = await context.entities.Queue.count({
      where: {
        isActive: true,
        sProvidingPlace: {
          provider: {
            userId: context.user.id
          }
        }
      }
    });
    res.status(200).json({
      success: true,
      message: 'Usage statistics retrieved successfully',
      data: {
        period: {
          type: period,
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          ...(period === 'month' && {
            month: now.getMonth() + 1,
            year: now.getFullYear()
          })
        },
        credits: {
          used: completedAppointments,
          remaining: user.credits,
          allocated: creditsAllocated,
          totalAvailable: user.credits + completedAppointments
        },
        appointments: {
          [`completed${period.charAt(0).toUpperCase() + period.slice(1)}`]: completedAppointments
        },
        limits: {
          queues: currentPlan?.effect.queues || 0,
          usedQueues: queuesUsed
        },
        subscription: {
          planId: user.subscriptionPlan,
          planName: user.subscriptionPlan ? prettyPaymentPlanName(user.subscriptionPlan as PaymentPlanId) : null,
          status: user.subscriptionStatus,
          isActive: !!user.subscriptionStatus && user.subscriptionStatus !== SubscriptionStatus.Deleted
        },
        currentPlan
      }
    });
  } catch (error: any) {
    console.error('Error fetching usage statistics:', error);
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message
      });
    }
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch usage statistics'
    });
  }
};

/**
 * GET /api/auth/payment/processors
 * Get available payment processors for the authenticated user
 */
export const handleGetAvailablePaymentProcessors = async (req: Request, res: Response, context: any) => {
  try {
    if (!context.user) {
      throw new HttpError(401, 'Authentication required');
    }

    // Get user's country code (you might want to implement geolocation or user profile-based detection)
    // For now, we'll use a simple approach - you can enhance this later
    const userCountryCode = req.headers['cf-ipcountry'] as string ||
                           req.headers['x-country-code'] as string ||
                           undefined;

    // Get available processors for the user's region
    const availableProcessors = getAvailableProcessorsForRegion(userCountryCode);

    // Get processor details
    const processorDetails = availableProcessors.map(processorId => {
      const processor = getPaymentProcessor(processorId);
      return {
        id: processorId,
        name: processorId === 'lemonsqueezy' ? 'Lemon Squeezy' :
              processorId === 'stripe' ? 'Stripe' :
              processorId === 'chargily' ? 'Chargily Pay' : processorId,
        description: processorId === 'lemonsqueezy' ? 'Global payment processing' :
                    processorId === 'stripe' ? 'Global payment processing' :
                    processorId === 'chargily' ? 'Algerian payment gateway (EDAHABIA/CIB)' : '',
        supportedMethods: processorId === 'chargily' ? ['edahabia', 'cib'] : ['card'],
        currency: processorId === 'chargily' ? 'DZD' : 'USD',
        isRecommended: processorId === 'chargily' && userCountryCode === 'DZ',
        isAvailable: processor.isAvailableForRegion ? processor.isAvailableForRegion(userCountryCode) : true,
      };
    });

    res.status(200).json({
      success: true,
      message: 'Available payment processors retrieved successfully',
      data: {
        processors: processorDetails,
        userCountryCode,
        defaultProcessor: 'lemonsqueezy', // Default for backward compatibility
        recommendedProcessor: processorDetails.find(p => p.isRecommended)?.id || 'lemonsqueezy',
      }
    });
  } catch (error: any) {
    console.error('Error fetching available payment processors:', error);
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message
      });
    }
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch available payment processors'
    });
  }
};
