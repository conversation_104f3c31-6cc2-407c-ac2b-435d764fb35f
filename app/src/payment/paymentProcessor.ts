import type { PaymentPlan } from './plans';
import type { PaymentsWebhook } from 'wasp/server/api';
import type { MiddlewareConfigFn } from 'wasp/server';
import { PrismaClient } from '@prisma/client';
import { stripePaymentProcessor } from './stripe/paymentProcessor';
import { lemonSqueezyPaymentProcessor } from './lemonSqueezy/paymentProcessor';
import { chargilyPaymentProcessor } from './chargily/paymentProcessor';

export interface CreateCheckoutSessionArgs {
  userId: string;
  userEmail: string;
  paymentPlan: PaymentPlan;
  prismaUserDelegate: PrismaClient['user'];
}

export interface FetchCustomerPortalUrlArgs {
  userId: string;
  prismaUserDelegate: PrismaClient['user'];
};

export interface CreateCustomerArgs {
  userId: string;
  userEmail: string;
  userName?: string;
  userPhone?: string;
  userAddress?: {
    country?: string;
    state?: string;
    address?: string;
  };
  prismaUserDelegate: PrismaClient['user'];
}

export interface CreatePaymentLinkArgs {
  userId: string;
  paymentPlan: PaymentPlan;
  customerId?: string;
  metadata?: Record<string, any>;
  prismaUserDelegate: PrismaClient['user'];
}

export type PaymentProcessorId = 'stripe' | 'lemonsqueezy' | 'chargily';

export interface PaymentProcessor {
  id: PaymentProcessorId;
  createCheckoutSession: (args: CreateCheckoutSessionArgs) => Promise<{ session: { id: string; url: string }; }>;
  fetchCustomerPortalUrl: (args: FetchCustomerPortalUrlArgs) => Promise<string | null>;
  webhook: PaymentsWebhook;
  webhookMiddlewareConfigFn: MiddlewareConfigFn;

  // Optional Chargily-specific methods (not all processors need to implement these)
  createCustomer?: (args: CreateCustomerArgs) => Promise<{ customerId: string; }>;
  createPaymentLink?: (args: CreatePaymentLinkArgs) => Promise<{ paymentLinkUrl: string; paymentLinkId: string; }>;

  // Region/availability check
  isAvailableForRegion?: (countryCode?: string) => boolean;
}

/**
 * Available payment processors - supports multiple concurrent processors
 * for different regions and use cases
 */
export const availablePaymentProcessors: Record<PaymentProcessorId, PaymentProcessor> = {
  lemonsqueezy: lemonSqueezyPaymentProcessor,
  stripe: stripePaymentProcessor,
  chargily: chargilyPaymentProcessor,
};

/**
 * Default payment processor (for backward compatibility)
 * Choose which payment processor you'd like to use as default
 */
export const paymentProcessor: PaymentProcessor = lemonSqueezyPaymentProcessor;

/**
 * Get payment processor by ID
 */
export function getPaymentProcessor(processorId: PaymentProcessorId): PaymentProcessor {
  const processor = availablePaymentProcessors[processorId];
  if (!processor) {
    throw new Error(`Payment processor '${processorId}' is not available or not implemented`);
  }
  return processor;
}

/**
 * Get available payment processors for a specific region
 */
export function getAvailableProcessorsForRegion(countryCode?: string): PaymentProcessorId[] {
  return Object.entries(availablePaymentProcessors)
    .filter(([_, processor]) => {
      if (!processor) return false;
      if (processor.isAvailableForRegion) {
        return processor.isAvailableForRegion(countryCode);
      }
      // If no region check is implemented, assume it's available globally
      return true;
    })
    .map(([id, _]) => id as PaymentProcessorId);
}
