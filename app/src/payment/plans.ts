import * as z from 'zod';
import { requireNodeEnvVar } from '../server/utils';
import type { PaymentProcessorId } from './paymentProcessor';

export enum SubscriptionStatus {
  PastDue = 'past_due',
  CancelAtPeriodEnd = 'cancel_at_period_end',
  Active = 'active',
  Deleted = 'deleted',
}

export enum PaymentPlanId {
  Free = 'free',
  Hobby = 'hobby',
  Pro = 'pro',
  Credits10 = 'credits10',
}

export interface PaymentPlan {
  // Returns the id under which this payment plan is identified on your payment processor.
  // E.g. this might be price id on Stripe, or variant id on LemonSqueezy.
  getPaymentProcessorPlanId: (processorId?: PaymentProcessorId) => string;
  effect: PaymentPlanEffect;
}

export type PaymentPlanEffect = { type: 'recurrent', kind: 'subscription';  amount: number , queues: number | null } | { type: 'one-time', kind: 'credits'; amount: number, queues: number | null };

/**
 * Helper function to get payment processor plan ID based on processor type
 */
function getProcessorPlanId(planId: PaymentPlanId, processorId?: PaymentProcessorId): string {
  // Default to LemonSqueezy if no processor specified (for backward compatibility)
  const processor = processorId || 'lemonsqueezy';

  switch (processor) {
    case 'lemonsqueezy':
      switch (planId) {
        case PaymentPlanId.Hobby:
          return requireNodeEnvVar('PAYMENTS_HOBBY_SUBSCRIPTION_PLAN_ID');
        case PaymentPlanId.Pro:
          return requireNodeEnvVar('PAYMENTS_PRO_SUBSCRIPTION_PLAN_ID');
        case PaymentPlanId.Credits10:
          return requireNodeEnvVar('PAYMENTS_CREDITS_10_PLAN_ID');
        case PaymentPlanId.Free:
          return requireNodeEnvVar('PAYMENTS_FREE_PLAN_ID');
        default:
          throw new Error(`Unknown plan ID: ${planId}`);
      }

    case 'stripe':
      // Stripe uses the same environment variables as LemonSqueezy for now
      return getProcessorPlanId(planId, 'lemonsqueezy');

    case 'chargily':
      switch (planId) {
        case PaymentPlanId.Hobby:
          return requireNodeEnvVar('CHARGILY_HOBBY_PLAN_ID');
        case PaymentPlanId.Pro:
          return requireNodeEnvVar('CHARGILY_PRO_PLAN_ID');
        case PaymentPlanId.Credits10:
          return requireNodeEnvVar('CHARGILY_CREDITS_10_PLAN_ID');
        case PaymentPlanId.Free:
          return requireNodeEnvVar('CHARGILY_FREE_PLAN_ID');
        default:
          throw new Error(`Unknown Chargily plan ID: ${planId}`);
      }

    default:
      throw new Error(`Unknown payment processor: ${processor}`);
  }
}

export const paymentPlans: Record<PaymentPlanId, PaymentPlan> = {
  [PaymentPlanId.Hobby]: {
    getPaymentProcessorPlanId: (processorId?: PaymentProcessorId) => getProcessorPlanId(PaymentPlanId.Hobby, processorId),
    effect: { type: 'recurrent', kind: 'subscription', amount: 200, queues: 3 },
  },
  [PaymentPlanId.Pro]: {
    getPaymentProcessorPlanId: (processorId?: PaymentProcessorId) => getProcessorPlanId(PaymentPlanId.Pro, processorId),
    effect: { type: 'recurrent', kind: 'subscription', amount: 1000, queues: 10 },
  },
  [PaymentPlanId.Credits10]: {
    getPaymentProcessorPlanId: (processorId?: PaymentProcessorId) => getProcessorPlanId(PaymentPlanId.Credits10, processorId),
    effect: { type: 'one-time', kind: 'credits', amount: 100, queues: null },
  },
  [PaymentPlanId.Free]: {
    getPaymentProcessorPlanId: (processorId?: PaymentProcessorId) => getProcessorPlanId(PaymentPlanId.Free, processorId),
    effect: { type: 'recurrent', kind: 'subscription', amount: 30, queues: 1 },
  },
};
export function prettyPaymentPlanName(planId: PaymentPlanId): string {
  const planToName: Record<PaymentPlanId, string> = {
    [PaymentPlanId.Hobby]: 'Starter',
    [PaymentPlanId.Pro]: 'Business',
    [PaymentPlanId.Credits10]: '100 Credits',
    [PaymentPlanId.Free]: 'Free',
  };
  return planToName[planId];
}

export function parsePaymentPlanId(planId: string): PaymentPlanId {
  // This function takes a string and checks if it matches one of the valid PaymentPlanId enum values
  // If the string matches a valid plan ID (hobby, pro, or credits10), it returns that PaymentPlanId
  // If the string doesn't match any valid plan ID, it throws an error
  if ((Object.values(PaymentPlanId) as string[]).includes(planId)) {
    return planId as PaymentPlanId;
  } else {
    throw new Error(`Invalid PaymentPlanId: ${planId}`);
  }
}

/**
 * Returns an array of PaymentPlanIds that represent subscription-based payment plans.
 * 
 * This function:
 * 1. Gets all payment plan IDs from the PaymentPlanId enum using Object.values()
 * 2. Filters them to only include plans where effect.kind is 'subscription'
 * 3. Excludes plans with effect.kind 'credits'
 * 
 * For example, given the current plans:
 * - Returns [PaymentPlanId.Hobby, PaymentPlanId.Pro]
 * - Excludes PaymentPlanId.Credits10 since it's a credits-based plan
 */
export function getSubscriptionPaymentPlanIds(): PaymentPlanId[] {
  return Object.values(PaymentPlanId).filter((planId) => paymentPlans[planId].effect.kind === 'subscription');
}

export interface PaymentPlanCard {
  name: string;
  price: string;
  description: string;
  features: string[];
}

export const paymentPlanCards: Record<PaymentPlanId, PaymentPlanCard> = {
  [PaymentPlanId.Free]: {
    name: prettyPaymentPlanName(PaymentPlanId.Free),
    price: 'Free',
    description: 'Free plan for everyone',
    features: ['Basic support', '1 queue'],
  },
  [PaymentPlanId.Hobby]: {
    name: prettyPaymentPlanName(PaymentPlanId.Hobby),
    price: '€9.99',
    description: 'All you need to get started',
    features: ['Basic support', '3 queues'],
  },
  [PaymentPlanId.Pro]: {
    name: prettyPaymentPlanName(PaymentPlanId.Pro),
    price: '€19.99',
    description: 'Our most popular plan',
    features: ['Priority customer support', '10 queues'],
  },
  [PaymentPlanId.Credits10]: {
    name: prettyPaymentPlanName(PaymentPlanId.Credits10),
    price: '€4.99',
    description: 'One-time purchase of 100 credits for your account',
    features: ['No expiration date'],
  },
};
