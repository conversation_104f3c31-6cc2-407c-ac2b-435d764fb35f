import { z } from 'zod';

// Validation schema for subscription plans
export const SubscriptionSchema = z.object({
  id: z.string().uuid().optional(), // Optional for creation
  name: z.string().min(1, 'Name is required'),
  description: z.string().min(1, 'Description is required'),
  price: z.number().nonnegative('Price must be non-negative'),
  duration: z.number().int().positive('Duration must be a positive integer'),
  interval: z.enum(['monthly', 'yearly', 'one-time'], {
    errorMap: () => ({ message: 'Interval must be monthly, yearly, or one-time' }),
  }),
  creditsIncluded: z.number().int().nonnegative('Credits must be non-negative'),
  features: z.string().min(1, 'Features are required'), // JSON string of features
  isActive: z.boolean().default(true),
});

// Validation schema for user subscriptions
export const UserSubscriptionSchema = z.object({
  id: z.string().uuid().optional(), // Optional for creation
  userId: z.string().uuid(),
  subscriptionId: z.string().uuid(),
  status: z.enum(['active', 'expired', 'cancelled', 'pending'], {
    errorMap: () => ({ message: 'Status must be active, expired, cancelled, or pending' }),
  }),
  startDate: z.date(),
  endDate: z.date().optional(),
  creditsAllocated: z.number().int().nonnegative(),
  paymentProcessorSubscriptionId: z.string().optional(),
});

// Validation schema for subscribing to a subscription plan
export const SubscribeToSubscriptionSchema = z.object({
  subscriptionId: z.string().uuid(),
  paymentMethodDetails: z.object({
    // Payment details if needed
    // This can be expanded based on your payment processor requirements
    paymentMethodId: z.string().optional(),
  }).optional(),
});

// Validation schema for query parameters
export const GetSubscriptionsQuerySchema = z.object({
  isActive: z.boolean().optional().default(true),
  page: z.number().int().positive().optional().default(1),
  limit: z.number().int().positive().max(100).optional().default(10),
});

// Export types for use in handlers
export type SubscriptionInput = z.infer<typeof SubscriptionSchema>;
export type UserSubscriptionInput = z.infer<typeof UserSubscriptionSchema>;
export type SubscribeToSubscriptionInput = z.infer<typeof SubscribeToSubscriptionSchema>;
export type GetSubscriptionsQueryInput = z.infer<typeof GetSubscriptionsQuerySchema>;
