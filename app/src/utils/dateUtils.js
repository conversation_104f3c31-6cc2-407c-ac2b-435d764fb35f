/**
 * Formats a date string, Date object, or timestamp into a localized string.
 *
 * @param {string | Date | number | null | undefined} dateInput The date to format.
 * @param {Intl.DateTimeFormatOptions} options Formatting options (e.g., { dateStyle: 'long', timeStyle: 'short' }).
 *                                            Defaults to { dateStyle: 'medium' }.
 * @param {string} locale The locale to use (e.g., 'en-US', 'fr-FR'). Defaults to browser default.
 * @returns {string} The formatted date string, or an empty string if input is invalid.
 */
export const formatDate = (dateInput, options = { dateStyle: 'medium' }, locale = undefined) => {
  if (!dateInput) {
    return ''; // Return empty for null/undefined input
  }

  try {
    const date = new Date(dateInput);
    // Check if the date is valid after parsing
    if (isNaN(date.getTime())) {
        console.warn(`Invalid date input provided to formatDate: ${dateInput}`);
        return 'Invalid Date'; // Or return empty string: ''
    }
    // If locale is explicitly undefined, Intl uses the browser's default
    return new Intl.DateTimeFormat(locale, options).format(date);
  } catch (error) {
    console.error("Error formatting date:", error);
    return 'Error'; // Return 'Error' or similar on formatting failure
  }
};

// Example Usage:
// formatDate(new Date()) // -> "Oct 27, 2023" (or similar depending on locale)
// formatDate('2023-11-01T10:00:00Z') // -> "Nov 1, 2023"
// formatDate(Date.now(), { dateStyle: 'full', timeStyle: 'long' }) // -> "Friday, October 27, 2023 at 10:30:00 AM GMT+1"
// formatDate(appt.expectedAppointmentStartTime, { timeStyle: 'short' }) // -> "9:00 AM"

/**
 * Checks if a given date input represents today's date.
 *
 * @param {string | Date | number | null | undefined} dateInput The date to check.
 * @returns {boolean} True if the date is today, false otherwise or if input is invalid.
 */
export const isToday = (dateInput) => {
  if (!dateInput) {
    return false;
  }
  try {
    const dateToCheck = new Date(dateInput);
    if (isNaN(dateToCheck.getTime())) {
      console.warn(`Invalid date input provided to isToday: ${dateInput}`);
      return false;
    }
    const today = new Date();
    return (
      dateToCheck.getFullYear() === today.getFullYear() &&
      dateToCheck.getMonth() === today.getMonth() &&
      dateToCheck.getDate() === today.getDate()
    );
  } catch (error) {
    console.error("Error checking if date is today:", error);
    return false;
  }
}; 