// src/client/notifications/NotificationBell.tsx
import React, { useState, useEffect } from 'react';
import { useQuery, useAction } from 'wasp/client/operations';
import { getUserNotifications, markNotificationAsRead, markAllNotificationsAsRead } from 'wasp/client/operations';
import { useNavigate } from 'react-router-dom'; // Wasp's Link might not work directly in Menu.Item onClick
import { Dropdown, Menu, Badge, List, Button, Typography, Spin, Empty } from 'antd';
import { BellOutlined, CheckCircleOutlined, ReadOutlined } from '@ant-design/icons'; // Ant Design icons
// import './NotificationBell.css'; // Optional: for any custom overrides

const { Text, Paragraph } = Typography;

const NotificationBell = () => {
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);
  const navigate = useNavigate();

  const { data: notificationsData, isLoading: isLoadingNotifications, error: notificationsError, refetch: refetchNotifications } = useQuery(
    getUserNotifications,
    { limit: 7, offset: 0 }, // Fetch a few for the dropdown
    // { refetchInterval: 60000 } // Keep this if you like periodic polling
  );
  
  const unreadCount = notificationsData?.filter(n => !n.isRead).length || 0;

  const markNotificationAsReadAction = useAction(markNotificationAsRead);
  const markAllNotificationsAsReadAction = useAction(markAllNotificationsAsRead);

  const handleNotificationClick = async (notification: any) => { // Use 'any' for now, or define a proper Notification type from Wasp
    if (!notification.isRead) {
      try {
        await markNotificationAsReadAction({ notificationId: notification.id });
        // refetchNotifications(); // Refetch after marking as read
      } catch (e) {
        console.error("Failed to mark notification as read", e);
      }
    }
    if (notification.link) {
      navigate(notification.link);
    }
    setIsDropdownVisible(false); // Close dropdown after click
  };
  
  const handleMarkAllRead = async () => {
      try {
          await markAllNotificationsAsReadAction({});
          // refetchNotifications(); // Refetch after marking all as read
      } catch (e) {
          console.error("Failed to mark all as read", e);
      }
      setIsDropdownVisible(false);
  };

  // Wasp's useQuery cache invalidation should handle refetching after actions.
  // If not, you can manually call refetchNotifications().

  const menuOverlay = (
    <div
    className='dark:bg-gray-800 dark:text-white bg-white text-black' 
    style={{
        width: '400px',
        borderRadius: '8px',
        boxShadow: '0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05)',
        // className: "notification-dropdown-antd-container"
    }}>
      <div style={{ padding: '12px 16px', borderBottom: '1px solid #f0f0f0', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Text strong>Notifications</Text>
        {unreadCount > 0 && (
          <Button type="link" onClick={handleMarkAllRead} size="small" icon={<CheckCircleOutlined />}>
            Mark all as read
          </Button>
        )}
      </div>
      {isLoadingNotifications ? (
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100px' }}><Spin /></div>
      ) : notificationsData && notificationsData.length > 0 ? (
        <List
          itemLayout="horizontal"
          dataSource={notificationsData}
          style={{maxHeight: '300px', overflow: 'auto'}}
          renderItem={(item: any) => ( // Use 'any' or proper Notification type
            <List.Item
              onClick={() => handleNotificationClick(item)}
              style={{ 
                cursor: 'pointer', 
                padding: '12px 16px',
                backgroundColor: item.isRead ? 'transparent' : 'rgba(24, 144, 255, 0.05)',
              }}
              // className: item.isRead ? "notification-list-item read" : "notification-list-item unread"
            >
              <List.Item.Meta
                // avatar={ <Avatar icon={<UserOutlined />} /> } // Example avatar
                title={
                    <div style={{display: 'flex', justifyContent: 'space-between', alignItems: 'center'}}>
                        <Text strong={!item.isRead} style={{color: item.isRead ? '#8c8c8c' : '#262626'}}>
                            {item.title}
                        </Text>
                        {!item.isRead && (
                             <Badge dot status="processing" style={{marginTop: '4px'}} />
                        )}
                    </div>
                }
                description={
                    <>
                        <Paragraph ellipsis={{ rows: 2 }} style={{ marginBottom: '4px', color: item.isRead ? '#bfbfbf' : '#595959' }}>
                            {item.message}
                        </Paragraph>
                        <Text type="secondary" style={{ fontSize: '0.75rem' }}>
                            {new Date(item.createdAt).toLocaleString([], { dateStyle: 'short', timeStyle: 'short' })}
                        </Text>
                    </>
                }
              />
            </List.Item>
          )}
        />
      ) : (
        <Empty description="No new notifications" style={{ padding: '20px 0' }} image={Empty.PRESENTED_IMAGE_SIMPLE} />
      )}
      {notificationsData && notificationsData.length > 0 && unreadCount > 0 && (
        <div style={{ padding: '8px 16px', borderTop: '1px solid #f0f0f0', textAlign: 'center' }}>
          <Button 
            type="link" 
            icon={<ReadOutlined />} 
            onClick={() => { 
              handleMarkAllRead(); 
            }} 
          > 
            Mark all as read
          </Button> 
        </div>
      )}
    </div>
  );

  return (
    <Dropdown 
        overlay={menuOverlay} 
        trigger={['click']} // Open on click
        open={isDropdownVisible} // Controlled mode
        onOpenChange={setIsDropdownVisible} // Control visibility
        placement="bottomRight"


    >
      <Badge count={unreadCount} size="small" offset={[-2, 2]}> {/* Badge on the bell icon */}
        <Button 
            type="text" 
            icon={<BellOutlined style={{ fontSize: '20px' }} />} 
            aria-label="Notifications"
            style={{ padding: '0 8px', height: 'auto', lineHeight: 'inherit' }}
        />
      </Badge>
    </Dropdown>
  );
};

export default NotificationBell;