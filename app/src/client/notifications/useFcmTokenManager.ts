// src/client/notifications/useFcmTokenManager.ts
import { useEffect, useState } from 'react';
import { messaging, getToken, onMessage } from '../firebase'; // Your firebase.ts initialization
import { useAction } from 'wasp/client/operations';
import { saveFcmToken } from 'wasp/client/operations'; // Wasp action
import { useAuth } from 'wasp/client/auth';

const VAPID_KEY = "BCnfc7F06_ygUBHEWBul7-t9uyTLYHWGlhOQWXSSUiGOoYzWUUMOKC1DsgfHlzx-Ux9lGFGqjNe9WEPloopDy3o"; // Replace this!

export const useFcmTokenManager = () => {
  const { data: user } = useAuth();
  const saveFcmTokenAction = useAction(saveFcmToken);
  const [fcmToken, setFcmToken] = useState<string | null>(null);
  const [notificationPermission, setNotificationPermission] = useState<NotificationPermission | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const getPermissionAndToken = async () => {
      if (!user || !messaging) return; // Only run if user is logged in and messaging is initialized

      try {
        // 1. Check current permission status
        let currentPermission = Notification.permission;
        // console.log('Current permission:', currentPermission);
        setNotificationPermission(currentPermission);

        if (currentPermission === 'granted') {
          // Permission already granted, try to get token
        } else if (currentPermission === 'default') {
          // Need to request permission
          console.log('Requesting notification permission...');
          currentPermission = await Notification.requestPermission();
          setNotificationPermission(currentPermission);
        }

        if (currentPermission === 'granted' && !fcmToken) {
          console.log('Notification permission granted.');
          const currentToken = await getToken(messaging, { vapidKey: VAPID_KEY });
          if (currentToken) {
            // console.log('FCM Token:', currentToken);
            setFcmToken(currentToken);
            // Send the token to your server to save it
            await saveFcmTokenAction({ token: currentToken, deviceType: 'web' });
            console.log('FCM token saved to server.');
            // Optional: Store token in localStorage to avoid re-saving on every load
            localStorage.setItem('fcmToken', currentToken);
          } else {
            console.log('No registration token available. Request permission to generate one.');
            setError('Failed to get FCM token. Permission might be needed or already denied.');
          }
        } else {
          console.log('Unable to get permission to notify.');
          setError('Notification permission denied by user.');
        }
      } catch (err: any) {
        console.error('An error occurred while retrieving token or requesting permission. ', err);
        setError(`Error: ${err.message || 'Failed to setup notifications.'}`);
      }
    };

    // Optional: Check if token is already in localStorage to avoid asking too often
    const storedToken = localStorage.getItem('fcmToken');
    if (user && !storedToken) {
      getPermissionAndToken();
    } else if (user && storedToken) {
      setFcmToken(storedToken);
      setNotificationPermission(Notification.permission);
      console.log('Using stored FCM token:', storedToken);
    }
    
    // if (user) { // Only attempt if user is logged in
    //     getPermissionAndToken();
    // }

  }, [user]);

  // Listen for foreground messages (when app is active)
  useEffect(() => {
    if (!messaging) return;

    const unsubscribe = onMessage(messaging, (payload) => {
      console.log('Message received in foreground. ', payload);
      // Customize how you want to handle the foreground message.
      // For example, show a custom toast notification.
      // You might use a library like react-toastify here.
      alert(`New Notification:\n${payload.notification?.title}\n${payload.notification?.body}`);
      // Potentially refetch notifications list here if NotificationBell doesn't pick it up via WebSocket yet
    });

    return () => {
      unsubscribe(); // Unsubscribe when component unmounts or user changes
    };
  }, []);


  return { fcmToken, notificationPermission, error };
};