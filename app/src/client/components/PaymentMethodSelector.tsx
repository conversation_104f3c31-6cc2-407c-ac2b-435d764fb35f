import React, { useState, useEffect } from 'react';
import { cn } from '../cn';
import type { PaymentProcessorId } from '../utils/paymentMethodSelector';

/**
 * Payment method information for UI display
 */
export interface PaymentMethodInfo {
  id: PaymentProcessorId;
  name: string;
  displayName: string;
  description: string;
  supportedMethods: string[];
  currency: string;
  isRecommended: boolean;
  isAvailable: boolean;
  priority: number;
  region: string[];
  logo?: string;
  features: string[];
}

interface PaymentMethodSelectorProps {
  selectedProcessor?: PaymentProcessorId;
  onProcessorChange: (processorId: PaymentProcessorId) => void;
  availableMethods?: PaymentMethodInfo[];
  className?: string;
  disabled?: boolean;
  showFeatures?: boolean;
  layout?: 'radio' | 'cards';
}

/**
 * Default payment methods (fallback when API is not available)
 */
const defaultPaymentMethods: PaymentMethodInfo[] = [
  {
    id: 'lemonsqueezy',
    name: 'Lemon Squeezy',
    displayName: 'Credit/Debit Card',
    description: 'Global payment processing with credit and debit cards',
    supportedMethods: ['card', 'paypal'],
    currency: 'USD',
    isRecommended: true,
    isAvailable: true,
    priority: 1,
    region: ['Global'],
    features: ['Credit/Debit Cards', 'PayPal', 'Global coverage', 'USD pricing']
  },
  {
    id: 'chargily',
    name: 'Chargily Pay',
    displayName: 'Chargily Pay',
    description: 'Algerian payment gateway supporting EDAHABIA and CIB cards',
    supportedMethods: ['edahabia', 'cib'],
    currency: 'DZD',
    isRecommended: false,
    isAvailable: true,
    priority: 2,
    region: ['DZ', 'Algeria'],
    features: ['EDAHABIA (Algerie Post)', 'CIB (SATIM)', 'Local Algerian payment methods', 'DZD currency support']
  }
];

/**
 * Payment method icons/badges
 */
const PaymentMethodBadge: React.FC<{ method: string; className?: string }> = ({ method, className }) => {
  const badgeClass = cn(
    'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
    className
  );

  switch (method.toLowerCase()) {
    case 'edahabia':
      return (
        <span className={cn(badgeClass, 'bg-green-100 text-green-800 dark:bg-green-200 dark:text-green-900')}>
          🏛️ EDAHABIA
        </span>
      );
    case 'cib':
      return (
        <span className={cn(badgeClass, 'bg-blue-100 text-blue-800 dark:bg-blue-200 dark:text-blue-900')}>
          💳 CIB
        </span>
      );
    case 'card':
      return (
        <span className={cn(badgeClass, 'bg-gray-100 text-gray-800 dark:bg-gray-200 dark:text-gray-900')}>
          💳 Card
        </span>
      );
    case 'paypal':
      return (
        <span className={cn(badgeClass, 'bg-blue-100 text-blue-800 dark:bg-blue-200 dark:text-blue-900')}>
          🅿️ PayPal
        </span>
      );
    case 'apple_pay':
      return (
        <span className={cn(badgeClass, 'bg-gray-100 text-gray-800 dark:bg-gray-200 dark:text-gray-900')}>
          🍎 Apple Pay
        </span>
      );
    case 'google_pay':
      return (
        <span className={cn(badgeClass, 'bg-red-100 text-red-800 dark:bg-red-200 dark:text-red-900')}>
          🔴 Google Pay
        </span>
      );
    default:
      return (
        <span className={cn(badgeClass, 'bg-gray-100 text-gray-800 dark:bg-gray-200 dark:text-gray-900')}>
          {method}
        </span>
      );
  }
};

/**
 * PaymentMethodSelector Component
 */
export const PaymentMethodSelector: React.FC<PaymentMethodSelectorProps> = ({
  selectedProcessor,
  onProcessorChange,
  availableMethods = defaultPaymentMethods,
  className,
  disabled = false,
  showFeatures = true,
  layout = 'cards'
}) => {
  const [selectedMethod, setSelectedMethod] = useState<PaymentProcessorId>(
    selectedProcessor || availableMethods[0]?.id || 'lemonsqueezy'
  );

  useEffect(() => {
    if (selectedProcessor && selectedProcessor !== selectedMethod) {
      setSelectedMethod(selectedProcessor);
    }
  }, [selectedProcessor]);

  const handleMethodChange = (processorId: PaymentProcessorId) => {
    if (disabled) return;
    setSelectedMethod(processorId);
    onProcessorChange(processorId);
  };

  const sortedMethods = availableMethods
    .filter(method => method.isAvailable)
    .sort((a, b) => a.priority - b.priority);

  if (layout === 'radio') {
    return (
      <div className={cn('space-y-3', className)}>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          Choose Payment Method
        </h3>
        <div className="space-y-2">
          {sortedMethods.map((method) => (
            <label
              key={method.id}
              className={cn(
                'relative flex cursor-pointer rounded-lg border p-4 focus:outline-none',
                {
                  'border-yellow-500 ring-2 ring-yellow-500': selectedMethod === method.id,
                  'border-gray-300 dark:border-gray-600': selectedMethod !== method.id,
                  'opacity-50 cursor-not-allowed': disabled,
                }
              )}
            >
              <input
                type="radio"
                name="payment-method"
                value={method.id}
                checked={selectedMethod === method.id}
                onChange={() => handleMethodChange(method.id)}
                disabled={disabled}
                className="sr-only"
              />
              <div className="flex w-full items-center justify-between">
                <div className="flex items-center">
                  <div className="text-sm">
                    <div className="font-medium text-gray-900 dark:text-white">
                      {method.displayName}
                      {method.isRecommended && (
                        <span className="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-200 dark:text-yellow-900">
                          Recommended
                        </span>
                      )}
                    </div>
                    <div className="text-gray-500 dark:text-gray-400">
                      {method.description}
                    </div>
                    <div className="mt-1 flex flex-wrap gap-1">
                      {method.supportedMethods.map((supportedMethod) => (
                        <PaymentMethodBadge key={supportedMethod} method={supportedMethod} />
                      ))}
                    </div>
                  </div>
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {method.currency}
                </div>
              </div>
            </label>
          ))}
        </div>
      </div>
    );
  }

  // Cards layout
  return (
    <div className={cn('space-y-4', className)}>
      <h3 className="text-lg font-medium text-gray-900 dark:text-white">
        Choose Payment Method
      </h3>
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
        {sortedMethods.map((method) => (
          <div
            key={method.id}
            className={cn(
              'relative cursor-pointer rounded-lg border p-4 focus:outline-none',
              {
                'border-yellow-500 ring-2 ring-yellow-500 bg-yellow-50 dark:bg-yellow-900/20': 
                  selectedMethod === method.id,
                'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500': 
                  selectedMethod !== method.id,
                'opacity-50 cursor-not-allowed': disabled,
              }
            )}
            onClick={() => handleMethodChange(method.id)}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  type="radio"
                  name="payment-method"
                  value={method.id}
                  checked={selectedMethod === method.id}
                  onChange={() => handleMethodChange(method.id)}
                  disabled={disabled}
                  className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300"
                />
                <div className="ml-3">
                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                    {method.displayName}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    {method.currency}
                  </div>
                </div>
              </div>
              {method.isRecommended && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-200 dark:text-yellow-900">
                  Recommended
                </span>
              )}
            </div>
            
            <div className="mt-2">
              <div className="text-sm text-gray-600 dark:text-gray-300">
                {method.description}
              </div>
              
              <div className="mt-2 flex flex-wrap gap-1">
                {method.supportedMethods.map((supportedMethod) => (
                  <PaymentMethodBadge key={supportedMethod} method={supportedMethod} />
                ))}
              </div>
              
              {showFeatures && method.features.length > 0 && (
                <div className="mt-2">
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Features: {method.features.slice(0, 2).join(', ')}
                    {method.features.length > 2 && '...'}
                  </div>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default PaymentMethodSelector;
