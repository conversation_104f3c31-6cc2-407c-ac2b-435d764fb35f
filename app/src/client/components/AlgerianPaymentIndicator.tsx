import React from 'react';
import { cn } from '../cn';

interface AlgerianPaymentIndicatorProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showTitle?: boolean;
  showLogos?: boolean;
  layout?: 'horizontal' | 'vertical';
}

/**
 * Algerian Payment Methods Indicator
 * Special component for highlighting Algerian payment support (EDAHABIA/CIB)
 */
export const AlgerianPaymentIndicator: React.FC<AlgerianPaymentIndicatorProps> = ({
  className,
  size = 'md',
  showTitle = true,
  showLogos = true,
  layout = 'horizontal'
}) => {
  const sizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  };

  const logoSizes = {
    sm: 'w-8 h-6',
    md: 'w-12 h-8',
    lg: 'w-16 h-10'
  };

  const containerClass = cn(
    'flex items-center gap-3',
    {
      'flex-col': layout === 'vertical',
      'flex-row': layout === 'horizontal'
    },
    className
  );

  return (
    <div className={containerClass}>
      {showTitle && (
        <div className={cn('font-medium text-gray-700 dark:text-gray-300 flex items-center gap-2', sizeClasses[size])}>
          <span className="text-lg">🇩🇿</span>
          <span>Algerian Payment Methods</span>
        </div>
      )}
      
      <div className={cn('flex gap-3', layout === 'vertical' ? 'flex-col' : 'flex-row')}>
        {/* EDAHABIA */}
        <div className="flex items-center gap-2 p-2 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
          {showLogos && (
            <img 
              src="/static/edahabia-logo.svg" 
              alt="EDAHABIA" 
              className={cn(logoSizes[size], 'object-contain')}
            />
          )}
          <div className="flex flex-col">
            <div className={cn('font-semibold text-green-800 dark:text-green-200', sizeClasses[size])}>
              🏛️ EDAHABIA
            </div>
            <div className={cn('text-green-600 dark:text-green-400', size === 'lg' ? 'text-sm' : 'text-xs')}>
              Algerie Post
            </div>
          </div>
        </div>

        {/* CIB */}
        <div className="flex items-center gap-2 p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
          {showLogos && (
            <img 
              src="/static/cib-logo.svg" 
              alt="CIB" 
              className={cn(logoSizes[size], 'object-contain')}
            />
          )}
          <div className="flex flex-col">
            <div className={cn('font-semibold text-blue-800 dark:text-blue-200', sizeClasses[size])}>
              💳 CIB
            </div>
            <div className={cn('text-blue-600 dark:text-blue-400', size === 'lg' ? 'text-sm' : 'text-xs')}>
              SATIM
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * Payment Processor Logo Component
 */
export const PaymentProcessorLogo: React.FC<{
  processor: 'chargily' | 'lemonsqueezy' | 'stripe';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  showName?: boolean;
}> = ({ 
  processor, 
  size = 'md',
  className,
  showName = false
}) => {
  const logoSizes = {
    sm: 'w-8 h-6',
    md: 'w-12 h-8',
    lg: 'w-16 h-10'
  };

  const logoSources = {
    chargily: '/static/chargily-logo.svg',
    lemonsqueezy: '/static/lemonsqueezy-logo.svg',
    stripe: '/static/stripe-logo.svg'
  };

  const processorNames = {
    chargily: 'Chargily Pay',
    lemonsqueezy: 'Lemon Squeezy',
    stripe: 'Stripe'
  };

  return (
    <div className={cn('flex items-center gap-2', className)}>
      <img 
        src={logoSources[processor]} 
        alt={processorNames[processor]}
        className={cn(logoSizes[size], 'object-contain')}
      />
      {showName && (
        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
          {processorNames[processor]}
        </span>
      )}
    </div>
  );
};

/**
 * Payment Methods Showcase
 * Displays all available payment methods with their logos
 */
export const PaymentMethodsShowcase: React.FC<{
  className?: string;
  showAlgerianMethods?: boolean;
  showGlobalMethods?: boolean;
  size?: 'sm' | 'md' | 'lg';
}> = ({
  className,
  showAlgerianMethods = true,
  showGlobalMethods = true,
  size = 'md'
}) => {
  return (
    <div className={cn('space-y-6', className)}>
      {showAlgerianMethods && (
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
            🇩🇿 Algerian Payment Methods
          </h3>
          <AlgerianPaymentIndicator 
            size={size}
            showTitle={false}
            layout="horizontal"
          />
        </div>
      )}
      
      {showGlobalMethods && (
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
            🌍 Global Payment Methods
          </h3>
          <div className="flex gap-4">
            <PaymentProcessorLogo 
              processor="lemonsqueezy" 
              size={size}
              showName={true}
            />
            <PaymentProcessorLogo 
              processor="stripe" 
              size={size}
              showName={true}
            />
          </div>
        </div>
      )}
    </div>
  );
};

/**
 * Chargily Pay Branding Component
 */
export const ChargilyPayBranding: React.FC<{
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showFeatures?: boolean;
}> = ({
  className,
  size = 'md',
  showFeatures = true
}) => {
  const features = [
    'EDAHABIA (Algerie Post)',
    'CIB (SATIM)',
    'DZD Currency',
    'Local Support'
  ];

  return (
    <div className={cn('bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800', className)}>
      <div className="flex items-center gap-3 mb-3">
        <PaymentProcessorLogo processor="chargily" size={size} />
        <div>
          <div className="text-green-800 dark:text-green-200 font-semibold">
            Chargily Pay
          </div>
          <div className="text-green-600 dark:text-green-400 text-sm">
            Algerian Payment Gateway
          </div>
        </div>
      </div>
      
      {showFeatures && (
        <div className="space-y-2">
          <div className="text-sm font-medium text-green-800 dark:text-green-200">
            Supported Methods:
          </div>
          <div className="flex flex-wrap gap-2">
            {features.map((feature, index) => (
              <span 
                key={index}
                className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-200 dark:text-green-900"
              >
                {feature}
              </span>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default AlgerianPaymentIndicator;
