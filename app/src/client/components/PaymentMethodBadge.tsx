import React from 'react';
import { cn } from '../cn';

interface PaymentMethodBadgeProps {
  method: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'outline' | 'solid';
  className?: string;
  showIcon?: boolean;
  showText?: boolean;
}

/**
 * Payment method badge component for displaying supported payment methods
 * Includes special support for Algerian payment methods (EDAHABIA/CIB)
 */
export const PaymentMethodBadge: React.FC<PaymentMethodBadgeProps> = ({
  method,
  size = 'md',
  variant = 'default',
  className,
  showIcon = true,
  showText = true
}) => {
  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-1.5 text-sm',
    lg: 'px-4 py-2 text-base'
  };

  const baseClasses = cn(
    'inline-flex items-center rounded-full font-medium',
    sizeClasses[size],
    className
  );

  const getMethodConfig = (methodName: string) => {
    const normalizedMethod = methodName.toLowerCase().replace(/[_-]/g, '');
    
    switch (normalizedMethod) {
      case 'edahabia':
        return {
          icon: '🏛️',
          text: 'EDAHABIA',
          description: 'Algerie Post',
          colors: {
            default: 'bg-green-100 text-green-800 dark:bg-green-200 dark:text-green-900',
            outline: 'border border-green-300 text-green-700 dark:border-green-600 dark:text-green-400',
            solid: 'bg-green-600 text-white'
          }
        };
      
      case 'cib':
        return {
          icon: '💳',
          text: 'CIB',
          description: 'SATIM',
          colors: {
            default: 'bg-blue-100 text-blue-800 dark:bg-blue-200 dark:text-blue-900',
            outline: 'border border-blue-300 text-blue-700 dark:border-blue-600 dark:text-blue-400',
            solid: 'bg-blue-600 text-white'
          }
        };
      
      case 'card':
      case 'creditcard':
      case 'debitcard':
        return {
          icon: '💳',
          text: 'Card',
          description: 'Credit/Debit',
          colors: {
            default: 'bg-gray-100 text-gray-800 dark:bg-gray-200 dark:text-gray-900',
            outline: 'border border-gray-300 text-gray-700 dark:border-gray-600 dark:text-gray-400',
            solid: 'bg-gray-600 text-white'
          }
        };
      
      case 'paypal':
        return {
          icon: '🅿️',
          text: 'PayPal',
          description: 'PayPal',
          colors: {
            default: 'bg-blue-100 text-blue-800 dark:bg-blue-200 dark:text-blue-900',
            outline: 'border border-blue-300 text-blue-700 dark:border-blue-600 dark:text-blue-400',
            solid: 'bg-blue-600 text-white'
          }
        };
      
      case 'applepay':
        return {
          icon: '🍎',
          text: 'Apple Pay',
          description: 'Apple Pay',
          colors: {
            default: 'bg-gray-100 text-gray-800 dark:bg-gray-200 dark:text-gray-900',
            outline: 'border border-gray-300 text-gray-700 dark:border-gray-600 dark:text-gray-400',
            solid: 'bg-gray-900 text-white'
          }
        };
      
      case 'googlepay':
        return {
          icon: '🔴',
          text: 'Google Pay',
          description: 'Google Pay',
          colors: {
            default: 'bg-red-100 text-red-800 dark:bg-red-200 dark:text-red-900',
            outline: 'border border-red-300 text-red-700 dark:border-red-600 dark:text-red-400',
            solid: 'bg-red-600 text-white'
          }
        };
      
      case 'banktransfer':
      case 'wiretransfer':
        return {
          icon: '🏦',
          text: 'Bank Transfer',
          description: 'Wire Transfer',
          colors: {
            default: 'bg-indigo-100 text-indigo-800 dark:bg-indigo-200 dark:text-indigo-900',
            outline: 'border border-indigo-300 text-indigo-700 dark:border-indigo-600 dark:text-indigo-400',
            solid: 'bg-indigo-600 text-white'
          }
        };
      
      default:
        return {
          icon: '💰',
          text: methodName,
          description: methodName,
          colors: {
            default: 'bg-gray-100 text-gray-800 dark:bg-gray-200 dark:text-gray-900',
            outline: 'border border-gray-300 text-gray-700 dark:border-gray-600 dark:text-gray-400',
            solid: 'bg-gray-600 text-white'
          }
        };
    }
  };

  const config = getMethodConfig(method);
  const colorClass = config.colors[variant];

  return (
    <span 
      className={cn(baseClasses, colorClass)}
      title={config.description}
    >
      {showIcon && (
        <span className={cn('flex-shrink-0', showText && 'mr-1')}>
          {config.icon}
        </span>
      )}
      {showText && (
        <span>{config.text}</span>
      )}
    </span>
  );
};

/**
 * Algerian Payment Methods Indicator
 * Special component for highlighting Algerian payment support
 */
export const AlgerianPaymentIndicator: React.FC<{
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showTitle?: boolean;
}> = ({ 
  className, 
  size = 'md',
  showTitle = true 
}) => {
  return (
    <div className={cn('flex flex-col space-y-2', className)}>
      {showTitle && (
        <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
          🇩🇿 Algerian Payment Methods
        </div>
      )}
      <div className="flex flex-wrap gap-2">
        <PaymentMethodBadge 
          method="edahabia" 
          size={size}
          variant="default"
        />
        <PaymentMethodBadge 
          method="cib" 
          size={size}
          variant="default"
        />
      </div>
    </div>
  );
};

/**
 * Payment Methods Grid
 * Display multiple payment methods in a grid layout
 */
export const PaymentMethodsGrid: React.FC<{
  methods: string[];
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'outline' | 'solid';
  maxColumns?: number;
}> = ({ 
  methods, 
  className, 
  size = 'md',
  variant = 'default',
  maxColumns = 4
}) => {
  const gridClass = cn(
    'grid gap-2',
    {
      'grid-cols-2': maxColumns === 2,
      'grid-cols-3': maxColumns === 3,
      'grid-cols-4': maxColumns === 4,
      'grid-cols-2 sm:grid-cols-3 lg:grid-cols-4': maxColumns > 4
    }
  );

  return (
    <div className={cn(gridClass, className)}>
      {methods.map((method, index) => (
        <PaymentMethodBadge
          key={`${method}-${index}`}
          method={method}
          size={size}
          variant={variant}
        />
      ))}
    </div>
  );
};

/**
 * Payment Processor Logo/Badge
 * Display payment processor branding
 */
export const PaymentProcessorBadge: React.FC<{
  processor: 'chargily' | 'lemonsqueezy' | 'stripe';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  showName?: boolean;
}> = ({ 
  processor, 
  size = 'md',
  className,
  showName = true
}) => {
  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-1.5 text-sm',
    lg: 'px-4 py-2 text-base'
  };

  const processorConfig = {
    chargily: {
      name: 'Chargily Pay',
      icon: '🇩🇿',
      colors: 'bg-green-100 text-green-800 dark:bg-green-200 dark:text-green-900'
    },
    lemonsqueezy: {
      name: 'Lemon Squeezy',
      icon: '🍋',
      colors: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-200 dark:text-yellow-900'
    },
    stripe: {
      name: 'Stripe',
      icon: '💳',
      colors: 'bg-purple-100 text-purple-800 dark:bg-purple-200 dark:text-purple-900'
    }
  };

  const config = processorConfig[processor];

  return (
    <span className={cn(
      'inline-flex items-center rounded-full font-medium',
      sizeClasses[size],
      config.colors,
      className
    )}>
      <span className={cn('flex-shrink-0', showName && 'mr-1')}>
        {config.icon}
      </span>
      {showName && (
        <span>{config.name}</span>
      )}
    </span>
  );
};

export default PaymentMethodBadge;
