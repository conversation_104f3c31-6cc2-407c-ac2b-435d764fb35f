// DarkModeSwitcher.tsx
import { Switch } from 'antd';
import {useColorMode} from '../hooks/useColorMode';
import { BulbOutlined, BulbFilled } from '@ant-design/icons';

const DarkModeSwitcher = () => {
  const { colorMode, setColorMode } = useColorMode();
  const isInLightMode = colorMode === 'light';

  return (
    <Switch
      checked={!isInLightMode}
      className='bg-gray-500'
      onChange={(checked: boolean) => {
        if (typeof setColorMode === 'function') {
          setColorMode(checked ? 'dark' : 'light');
        }
      }}
      checkedChildren={<BulbOutlined />}
      unCheckedChildren={<BulbFilled />}
    />
  );
};

export default DarkModeSwitcher;
