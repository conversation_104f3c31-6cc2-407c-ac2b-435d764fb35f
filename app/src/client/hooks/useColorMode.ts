import React, { createContext, useContext, useEffect, ReactNode } from 'react';
import useLocalStorage from './useLocalStorage';

type ColorMode = 'dark' | 'light';

// Define the shape of the context value
interface ColorModeContextType {
  colorMode: ColorMode;
  setColorMode: (mode: ColorMode) => void;
}

// Create the context with a default value of undefined
const ColorModeContext = createContext<ColorModeContextType | undefined>(undefined);

// Define the props for the provider component
interface ColorModeProviderProps {
  children: ReactNode;
}

export const ColorModeProvider: React.FC<ColorModeProviderProps> = ({ children }) => {
  // Use the custom hook to manage color mode state in local storage
  // Explicitly type the state and setter from useLocalStorage if the hook doesn't infer well
  const [colorMode, setColorMode] = useLocalStorage<ColorMode>('color-theme', 'light');

  useEffect(() => {
    const className = 'dark';
    const bodyClass = window.document.body.classList;

    // Add or remove the 'dark' class based on the color mode
    if (colorMode === 'dark') {
      bodyClass.add(className);
    } else {
      bodyClass.remove(className);
    }
    // Dependency array ensures this effect runs when colorMode changes
  }, [colorMode]);

  // Provide the color mode state and setter to children components
  // Use React.createElement to avoid JSX syntax issues in .ts files if that's the cause
  // Or ensure the file is .tsx if JSX is preferred
  const contextValue = { colorMode, setColorMode };

  return React.createElement(
    ColorModeContext.Provider,
    { value: contextValue },
    children
  );

  /*
  // Original JSX version (requires file to be .tsx)
  return (
     <ColorModeContext.Provider value={{ colorMode, setColorMode }}>
       {children}
     </ColorModeContext.Provider>
  );
  */
};

// Custom hook to consume the color mode context
export const useColorMode = (): ColorModeContextType => {
  const context = useContext(ColorModeContext);
  // Ensure the hook is used within a ColorModeProvider
  if (context === undefined) {
    throw new Error('useColorMode must be used within a ColorModeProvider');
  }
  return context;
};