import { useEffect, useState } from 'react';

type SetValue<T> = T | ((val: T) => T);

function useLocalStorage<T>(
  key: string,
  initialValue: T
): [T, (value: SetValue<T>) => void] {
  // State to store our value
  // Pass initial state function to useState so logic is only executed once
  const [storedValue, setStoredValue] = useState<T>(() => {
    if (typeof window === 'undefined') {
      return initialValue;
    }
    try {
      const item = window.localStorage.getItem(key);
      return item ? (JSON.parse(item) as T) : initialValue;
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  // Effect to update local storage when the state changes *internally*
  useEffect(() => {
    if (typeof window === 'undefined') {
      return;
    }
    try {
      // valueToStore is simply the current storedValue
      const valueToStore = storedValue;
      window.localStorage.setItem(key, JSON.stringify(valueToStore));
      // Optional: Dispatch storage event even for internal changes if needed for same-tab listeners
      // window.dispatchEvent(new StorageEvent('storage', { key: key, newValue: JSON.stringify(valueToStore) }));
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  }, [key, storedValue]); // Runs when key or storedValue changes

  // Listen for storage changes from other tabs/windows
  useEffect(() => {
    if (typeof window === 'undefined') {
      return;
    }

    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === key && event.storageArea === window.localStorage) {
        try {
          const newValueFromStorage = event.newValue ? (JSON.parse(event.newValue) as T) : initialValue;
          // Only update state if the value actually changed
          if (JSON.stringify(storedValue) !== JSON.stringify(newValueFromStorage)) {
             setStoredValue(newValueFromStorage);
          }
        } catch (error) {
          console.error(`Error parsing storage event value for key "${key}":`, error);
          // Optionally reset to initialValue on error, or maybe just log
          // setStoredValue(initialValue);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);

    // No need to re-check initial value here if the listener handles updates

    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
    // Depend only on key and initialValue for setup/cleanup.
    // Adding storedValue here caused issues in previous attempt, let's avoid it.
    // The listener itself uses storedValue via closure but doesn't need it as a dep.
  }, [key, initialValue]);

  // Return the state value and the original state setter function
  return [storedValue, setStoredValue];
}

export default useLocalStorage;
