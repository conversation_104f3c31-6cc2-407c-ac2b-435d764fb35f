// src/client/firebase.ts
import { initializeApp, getApp, getApps } from "firebase/app";
import { getMessaging, getToken, onMessage } from "firebase/messaging";
// import { getAnalytics } from "firebase/analytics"; // Optional, if you use Analytics

// Your web app's Firebase configuration
// IMPORTANT: Consider using environment variables for these, especially if your repo is public.
// Wasp has .env files for server, for client-side config, you might hardcode it here or
// use a build-time environment variable solution if <PERSON><PERSON> supports it easily for client.
// For FCM, this config is generally considered safe to be in client code as it's needed to identify your app.
const firebaseConfig = {
  apiKey: "YOUR_PRODUCTION_API_KEY", // Get this from Firebase Console → Project Settings → General → Web app config
  authDomain: "dalti-prod.firebaseapp.com",
  projectId: "dalti-prod",
  storageBucket: "dalti-prod.appspot.com", // This might be different, check Firebase Console
  messagingSenderId: "YOUR_PRODUCTION_SENDER_ID", // Get this from Firebase Console
  appId: "YOUR_PRODUCTION_APP_ID", // Get this from Firebase Console
  measurementId: "YOUR_PRODUCTION_MEASUREMENT_ID" // Optional, get this from Firebase Console if using Analytics
};

// Initialize Firebase
let app;
if (!getApps().length) {
  app = initializeApp(firebaseConfig);
} else {
  app = getApp(); // If already initialized, get the existing instance
}

// const analytics = getAnalytics(app); // Optional
const messaging = getMessaging(app);

export { app, messaging, getToken, onMessage };