// src/client/firebase.ts
import { initializeApp, getApp, getApps } from "firebase/app";
import { getMessaging, getToken, onMessage } from "firebase/messaging";
// import { getAnalytics } from "firebase/analytics"; // Optional, if you use Analytics

// Your web app's Firebase configuration for dalti-prod
// Get these values from Firebase Console → Project Settings → General → Your apps → Web app
// IMPORTANT: Replace these placeholder values with your actual dalti-prod configuration
const firebaseConfig = {
  apiKey: "REPLACE_WITH_DALTI_PROD_API_KEY", // From Firebase Console
  authDomain: "dalti-prod.firebaseapp.com",
  projectId: "dalti-prod",
  storageBucket: "dalti-prod.appspot.com", // Verify this in Firebase Console
  messagingSenderId: "REPLACE_WITH_DALTI_PROD_SENDER_ID", // From Firebase Console
  appId: "REPLACE_WITH_DALTI_PROD_APP_ID", // From Firebase Console
  measurementId: "REPLACE_WITH_DALTI_PROD_MEASUREMENT_ID" // Optional, for Analytics
};

// Initialize Firebase
let app;
if (!getApps().length) {
  app = initializeApp(firebaseConfig);
} else {
  app = getApp(); // If already initialized, get the existing instance
}

// const analytics = getAnalytics(app); // Optional
const messaging = getMessaging(app);

export { app, messaging, getToken, onMessage };