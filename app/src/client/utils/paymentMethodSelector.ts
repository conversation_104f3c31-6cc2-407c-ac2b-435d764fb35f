/**
 * Client-side payment method selection utilities
 * This file is safe for client-side use and doesn't import server-side code
 */

export type PaymentProcessorId = 'lemonsqueezy' | 'stripe' | 'chargily';

/**
 * Payment method information for UI display
 */
export interface PaymentMethodInfo {
  id: PaymentProcessorId;
  name: string;
  displayName: string;
  description: string;
  supportedMethods: string[];
  currency: string;
  isRecommended: boolean;
  isAvailable: boolean;
  priority: number; // Lower number = higher priority
  region: string[];
  logo?: string;
  features: string[];
}

/**
 * User preferences for payment method selection
 */
export interface UserPaymentPreferences {
  preferredProcessor?: PaymentProcessorId;
  countryCode?: string;
  currency?: string;
  excludeProcessors?: PaymentProcessorId[];
}

/**
 * Get available payment processors for a region (client-side version)
 */
export function getAvailableProcessorsForRegion(countryCode?: string): PaymentProcessorId[] {
  const processors: PaymentProcessorId[] = ['lemonsqueezy', 'stripe'];
  
  // Add Chargily for Algerian users
  if (countryCode === 'DZ' || countryCode === 'dz') {
    processors.unshift('chargily'); // Add at beginning for priority
  } else {
    processors.push('chargily'); // Add at end as option
  }
  
  return processors;
}

/**
 * Get available payment methods based on user location and preferences (client-side)
 */
export function getAvailablePaymentMethods(
  userPreferences: UserPaymentPreferences = {}
): PaymentMethodInfo[] {
  const { countryCode, preferredProcessor, excludeProcessors = [] } = userPreferences;
  
  // Get base available processors for the region
  const availableProcessorIds = getAvailableProcessorsForRegion(countryCode)
    .filter(id => !excludeProcessors.includes(id));

  // Map processors to payment method info
  const paymentMethods: PaymentMethodInfo[] = availableProcessorIds.map(processorId => {
    switch (processorId) {
      case 'chargily':
        return {
          id: 'chargily',
          name: 'Chargily Pay',
          displayName: 'Chargily Pay',
          description: 'Algerian payment gateway supporting EDAHABIA and CIB cards',
          supportedMethods: ['edahabia', 'cib'],
          currency: 'DZD',
          isRecommended: countryCode === 'DZ' || countryCode === 'dz',
          isAvailable: true, // Client-side assumes available
          priority: countryCode === 'DZ' || countryCode === 'dz' ? 1 : 3,
          region: ['DZ', 'Algeria'],
          logo: '/static/chargily-logo.svg',
          features: [
            'EDAHABIA (Algerie Post)',
            'CIB (SATIM)',
            'Local Algerian payment methods',
            'DZD currency support'
          ]
        };
      
      case 'lemonsqueezy':
        return {
          id: 'lemonsqueezy',
          name: 'Lemon Squeezy',
          displayName: 'Credit/Debit Card',
          description: 'Global payment processing with credit and debit cards',
          supportedMethods: ['card', 'paypal'],
          currency: 'USD',
          isRecommended: countryCode !== 'DZ' && countryCode !== 'dz',
          isAvailable: true,
          priority: countryCode === 'DZ' || countryCode === 'dz' ? 2 : 1,
          region: ['Global'],
          logo: '/static/lemonsqueezy-logo.svg',
          features: [
            'Credit/Debit Cards',
            'PayPal',
            'Global coverage',
            'USD pricing'
          ]
        };
      
      case 'stripe':
        return {
          id: 'stripe',
          name: 'Stripe',
          displayName: 'Credit/Debit Card (Stripe)',
          description: 'Global payment processing with extensive payment method support',
          supportedMethods: ['card', 'apple_pay', 'google_pay'],
          currency: 'USD',
          isRecommended: false, // Not primary option
          isAvailable: true,
          priority: 3,
          region: ['Global'],
          logo: '/static/stripe-logo.svg',
          features: [
            'Credit/Debit Cards',
            'Apple Pay',
            'Google Pay',
            'Global coverage'
          ]
        };
      
      default:
        return {
          id: processorId,
          name: processorId,
          displayName: processorId,
          description: 'Payment processor',
          supportedMethods: ['card'],
          currency: 'USD',
          isRecommended: false,
          isAvailable: true,
          priority: 5,
          region: ['Global'],
          features: []
        };
    }
  });

  // Apply user preferences
  const processedMethods = paymentMethods.map(method => ({
    ...method,
    isRecommended: method.isRecommended || method.id === preferredProcessor,
    priority: method.id === preferredProcessor ? 0 : method.priority
  }));

  // Sort by priority (lower number = higher priority)
  return processedMethods.sort((a, b) => a.priority - b.priority);
}

/**
 * Get the recommended payment method for a user
 */
export function getRecommendedPaymentMethod(
  userPreferences: UserPaymentPreferences = {}
): PaymentMethodInfo | null {
  const availableMethods = getAvailablePaymentMethods(userPreferences);
  return availableMethods.find(method => method.isRecommended) || availableMethods[0] || null;
}

/**
 * Get payment method by processor ID
 */
export function getPaymentMethodInfo(
  processorId: PaymentProcessorId,
  userPreferences: UserPaymentPreferences = {}
): PaymentMethodInfo | null {
  const availableMethods = getAvailablePaymentMethods(userPreferences);
  return availableMethods.find(method => method.id === processorId) || null;
}

/**
 * Check if a payment processor is available for the user
 */
export function isPaymentProcessorAvailable(
  processorId: PaymentProcessorId,
  userPreferences: UserPaymentPreferences = {}
): boolean {
  const method = getPaymentMethodInfo(processorId, userPreferences);
  return method?.isAvailable || false;
}

/**
 * Detect user's country code from various sources (client-side)
 */
export function detectUserCountryCode(request?: {
  headers?: Record<string, string | string[] | undefined>;
  ip?: string;
}): string | undefined {
  // Client-side detection is limited, but we can try some approaches
  
  // Try to get from browser language
  if (typeof navigator !== 'undefined') {
    const language = navigator.language || (navigator as any).userLanguage;
    if (language) {
      // Extract country code from language (e.g., 'ar-DZ' -> 'DZ')
      const parts = language.split('-');
      if (parts.length > 1) {
        return parts[1].toUpperCase();
      }
    }
  }
  
  // Try timezone-based detection (rough approximation)
  if (typeof Intl !== 'undefined') {
    try {
      const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      if (timezone === 'Africa/Algiers') {
        return 'DZ';
      }
    } catch (error) {
      // Ignore timezone detection errors
    }
  }
  
  return undefined;
}

/**
 * Get payment method selection recommendations based on context
 */
export function getPaymentMethodRecommendations(
  userPreferences: UserPaymentPreferences = {}
): {
  primary: PaymentMethodInfo | null;
  alternatives: PaymentMethodInfo[];
  algerian: PaymentMethodInfo | null;
  global: PaymentMethodInfo[];
} {
  const availableMethods = getAvailablePaymentMethods(userPreferences);
  
  const primary = getRecommendedPaymentMethod(userPreferences);
  const alternatives = availableMethods.filter(method => method.id !== primary?.id);
  
  const algerian = availableMethods.find(method => method.id === 'chargily') || null;
  const global = availableMethods.filter(method => 
    method.region.includes('Global') && method.id !== primary?.id
  );

  return {
    primary,
    alternatives,
    algerian,
    global
  };
}

/**
 * Validate payment processor selection
 */
export function validatePaymentProcessorSelection(
  processorId: PaymentProcessorId,
  userPreferences: UserPaymentPreferences = {}
): { isValid: boolean; reason?: string } {
  const availableMethods = getAvailablePaymentMethods(userPreferences);
  const method = availableMethods.find(m => m.id === processorId);

  if (!method) {
    return { isValid: false, reason: 'Payment processor not available in your region' };
  }

  if (!method.isAvailable) {
    return { isValid: false, reason: 'Payment processor is currently unavailable' };
  }

  return { isValid: true };
}

/**
 * Get currency information for a payment processor
 */
export function getPaymentProcessorCurrency(processorId: PaymentProcessorId): string {
  switch (processorId) {
    case 'chargily':
      return 'DZD';
    case 'lemonsqueezy':
    case 'stripe':
    default:
      return 'USD';
  }
}

/**
 * Format currency amount for display
 */
export function formatCurrencyAmount(amount: number, currency: string): string {
  const formatters: Record<string, Intl.NumberFormat> = {
    'DZD': new Intl.NumberFormat('ar-DZ', { style: 'currency', currency: 'DZD' }),
    'USD': new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }),
  };

  const formatter = formatters[currency] || formatters['USD'];
  return formatter.format(amount);
}
