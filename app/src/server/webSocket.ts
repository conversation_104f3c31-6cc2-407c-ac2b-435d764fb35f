import { HttpError } from 'wasp/server';
import { type WebSocketDefinition, type WaspSocketData } from 'wasp/server/webSocket';
import { User, Appointment, Queue, Service } from 'wasp/entities'; // Use regular import for runtime access
import { getFirstProviderUserId } from 'wasp/auth';
// Import Prisma directly for more specific types if needed, though context should suffice
import { type Prisma } from '@prisma/client';
import { type Server, type Socket } from 'socket.io'; // Added import for Server type, and Socket for context

// WebSocket Logger Prefix
const WS_LOG_PREFIX = '[WebSocket]';

// Define the types for our WebSocket events and payloads
// ==============================================================================

// Payload for a new message, similar to AugmentedMessageInfo but for WebSocket
interface NewMessagePayload {
  id: number;
  conversationId: number;
  senderId: string;
  senderDomain: string;
  content: string;
  hasAttachments: boolean;
  createdAt: string; // ISO string
  sender: {
    id: string;
    firstName: string | null;
    lastName: string | null;
  } | null;
  // Add other relevant fields client might need immediately
}

// Payload for a new or updated conversation, similar to AugmentedConversation
interface ConversationPayloadParticipantUser {
  id: string;
  firstName: string | null;
  lastName: string | null;
  email?: string | null;
}
interface ConversationPayloadParticipant {
  id: number;
  userId: string;
  userDomain: string;
  canWrite: boolean;
  canDownload: boolean;
  unreadCount: number;
  user: ConversationPayloadParticipantUser | null; // User can be null if not fully populated
}

interface ConversationPayload {
  id: number;
  name: string | null;
  isGroup: boolean;
  ownerDomain: string;
  ownerId: string;
  createdAt: string; // ISO string
  updatedAt: string; // ISO string
  lastMessageId: number | null;
  unread: number;
  displayName: string;
  displayImage: string | null;
  otherParticipantIds: string[];
  participants: ConversationPayloadParticipant[];
  lastMessage: NewMessagePayload | null; // Can reuse NewMessagePayload for last message info
  owner: ConversationPayloadParticipantUser | null;
}

// Payload for when a message is read
interface MessageReadPayload {
  messageId: number;
  conversationId: number;
  readerId: string; // ID of the user who read the message
  readAt: string;   // ISO string timestamp
}

interface ServerToClientEvents {
  /** Emitted to a specific user with their current queue status */
  queueUpdate: (payload: {
    queueId: number;
    position: number | null; // Current position (1-based) or null if not in queue
    estimatedStartTime: string | null; // ISO string or null
    estimatedWaitMinutes: number | null; // Wait time in minutes or null
    error?: string; // Optional error message
  }) => void;

  /** Emitted to conversation participants when a new message is sent */
  newMessage: (payload: NewMessagePayload) => void;
  
  /** Emitted to relevant users when a conversation is started or updated */
  conversationStarted: (payload: ConversationPayload) => void;
  // TODO: Could be merged into a generic conversationUpdate if preferred

  /** Emitted to the message sender when their message is read by someone */
  messageRead: (payload: MessageReadPayload) => void;

  /** Emitted to all conversation participants (except perhaps the reader) for UI updates */
  messageReadReceipt: (payload: MessageReadPayload) => void;
}

interface ClientToServerEvents {
  /** Client requests their current queue status (implicitly for today) */
  requestQueueStatus: () => void; // No payload needed if always checking for 'today'

  /** Client notifies server that a queue state might have changed */
  notifyQueueChange: (payload: { queueId: number }) => void;
}

interface InterServerEvents {
  // Currently no server-to-server events needed
}

/**
 * Data attached to each socket instance.
 * Wasp automatically adds `user` if the connection is authenticated.
 */
interface SocketData extends WaspSocketData {
  // Add custom fields here if needed later, e.g.:
  // watchingQueueIds?: number[];
}

// Type definition for the main WebSocket function passed to Wasp
type WebSocketFn = WebSocketDefinition<
  ClientToServerEvents,
  ServerToClientEvents,
  InterServerEvents,
  SocketData
>;

// Store the Socket.IO server instance
let currentIo: Server<ClientToServerEvents, ServerToClientEvents, InterServerEvents, SocketData> | null = null;

/**
 * Returns the current Socket.IO server instance.
 * Can be used by other backend modules to emit events.
 */
export function getIoInstance(): Server<ClientToServerEvents, ServerToClientEvents, InterServerEvents, SocketData> | null {
  // console.log(`${WS_LOG_PREFIX} getIoInstance() called.`);
  if (!currentIo) {
    console.warn(`${WS_LOG_PREFIX} getIoInstance(): IO instance is null.`);
  }

  console.log(`${WS_LOG_PREFIX} getIoInstance(): returned IO instance.`);
  return currentIo;
}

/**
 * Calculates queue position and estimated wait times for a user in a specific queue.
 * This function is now exported for use in other modules.
 */
// calculateQueuePositionAndEstimate function will be exported at the end of the file where it's defined.

/**
 * Broadcasts queue state updates to all relevant users in a given queue.
 * This function encapsulates the logic previously in the 'notifyQueueChange' handler.
 * @param queueId The ID of the queue that has changed.
 * @param context The Wasp context object containing database entities.
 * @param io The Socket.IO server instance.
 * @param triggeringSocket Optional: the socket that triggered the broadcast, to customize logging.
 */
export async function broadcastQueueStateUpdate(
  queueId: number,
  context: Parameters<WebSocketFn>[1], // Context from WebSocketFn or a compatible one from operations
  io: Server<ClientToServerEvents, ServerToClientEvents, InterServerEvents, SocketData>,
  triggeringSocket?: Socket<ClientToServerEvents, ServerToClientEvents, InterServerEvents, SocketData> // Optional socket for logging context
) {
  const triggerSource = triggeringSocket ? `client ${triggeringSocket.data.user?.id}` : 'server logic';
  console.log(`${WS_LOG_PREFIX} broadcastQueueStateUpdate: Starting for queueId: ${queueId}, triggered by: ${triggerSource}`);

  try {
    const todayStart = new Date();
    todayStart.setHours(0, 0, 0, 0);
    const todayEnd = new Date();
    todayEnd.setHours(23, 59, 59, 999);
    // console.log(`${WS_LOG_PREFIX} broadcastQueueStateUpdate: Date range for query: ${todayStart.toISOString()} - ${todayEnd.toISOString()}`);

    // console.log(`${WS_LOG_PREFIX} broadcastQueueStateUpdate: Fetching appointments for queue ${queueId} today.`);
    const appointmentsInQueueToday = await context.entities.Appointment.findMany({
       where: {
          queueId: queueId,
          status: { in: ['pending', 'confirmed', 'InProgress'] },
          expectedAppointmentStartTime: {
             gte: todayStart,
             lte: todayEnd,
          },
       },
       select: {
          customerFolder: { select: { userId: true } },
       },
       distinct: ['customerFolderId']
    });
    // console.log(`${WS_LOG_PREFIX} broadcastQueueStateUpdate: Found ${appointmentsInQueueToday.length} appointments, leading to ${appointmentsInQueueToday.map(a => a.customerFolder.userId).length} unique users.`);

    const uniqueUserIds = appointmentsInQueueToday.map(appt => appt.customerFolder.userId);
    console.log(`${WS_LOG_PREFIX} broadcastQueueStateUpdate: Queue ${queueId} change potentially affects users:`, uniqueUserIds);

    for (const targetUserId of uniqueUserIds) {
       // console.log(`${WS_LOG_PREFIX} broadcastQueueStateUpdate: Processing user ${targetUserId} for queue ${queueId}.`);
       try {
          // console.log(`${WS_LOG_PREFIX} broadcastQueueStateUpdate: Calculating position for user ${targetUserId}, queue ${queueId}.`);
          const result = await calculateQueuePositionAndEstimate(targetUserId, queueId, new Date(), context);
          // console.log(`${WS_LOG_PREFIX} broadcastQueueStateUpdate: Calculation result for user ${targetUserId}, queue ${queueId}:`, result);
          
          const isSelf = triggeringSocket && triggeringSocket.data.user?.id === targetUserId;
          const roomName = `user_${targetUserId}`;
          // console.log(`${WS_LOG_PREFIX} broadcastQueueStateUpdate: Emitting 'queueUpdate' to room ${roomName} for user ${targetUserId}${isSelf ? ' (self)' : ''}, queue ${queueId}. Payload:`, { queueId, ...result });
          io.to(roomName).emit('queueUpdate', { queueId, ...result });
          console.log(`${WS_LOG_PREFIX} broadcastQueueStateUpdate: Successfully emitted 'queueUpdate' to user ${targetUserId} for queue ${queueId}.`);
       } catch (userUpdateError: any) {
           console.error(`${WS_LOG_PREFIX} broadcastQueueStateUpdate: Failed to recalculate/update user ${targetUserId} for queue ${queueId}:`, userUpdateError);
           io.to(`user_${targetUserId}`).emit('queueUpdate', { queueId, position: null, estimatedStartTime: null, estimatedWaitMinutes: null, error: 'Failed to update queue status.' });
       }
    }
    // console.log(`${WS_LOG_PREFIX} broadcastQueueStateUpdate: Finished processing for queueId: ${queueId}.`);
  } catch (error: any) {
     // console.error(`${WS_LOG_PREFIX} broadcastQueueStateUpdate: General error for queue ${queueId}:`, error);
  }
}

// WebSocket Server Logic Implementation
// ==============================================================================

/**
 * Main function defining the WebSocket server behavior.
 * `io` is the Socket.IO server instance.
 * `context` provides access to database entities (Prisma).
 */
export const webSocketFn: WebSocketFn = (io, context) => {
  // console.log(`${WS_LOG_PREFIX} webSocketFn: WebSocket server function initialized.`);
  currentIo = io; // Store the io instance when WebSocket server starts
  // console.log(`${WS_LOG_PREFIX} webSocketFn: Global IO instance has been set.`);

  io.on('connection', (socket) => {
    console.log(`${WS_LOG_PREFIX} Connection: New client connected. Socket ID: ${socket.id}`);
    const user: User | undefined = socket.data.user;

    if (!user) {
      console.warn(`${WS_LOG_PREFIX} Connection: Connection attempt without authentication for socket ID: ${socket.id}.`);
    } else {
       console.log(`${WS_LOG_PREFIX} Connection: User authenticated. Socket ID: ${socket.id}, User ID: ${user.id}, Role: ${user.role}`);
       const userRoom = `user_${user.id}`;
       socket.join(userRoom);
       console.log(`${WS_LOG_PREFIX} Connection: User ${user.id} joined room: ${userRoom}`);
    }

    socket.on('requestQueueStatus', async () => {
       // console.log(`${WS_LOG_PREFIX} requestQueueStatus: Received from socket ID: ${socket.id}, User: ${user?.id || 'Unauthenticated'}`);
       if (!user) {
          // console.warn(`${WS_LOG_PREFIX} requestQueueStatus: User not authenticated for socket ID: ${socket.id}. Emitting error.`);
          socket.emit('queueUpdate', { queueId: 0, position: null, estimatedStartTime: null, estimatedWaitMinutes: null, error: 'Authentication required.' });
          return;
       }
       // console.log(`${WS_LOG_PREFIX} requestQueueStatus: User ${user.id} requested their queue status.`);
       try {
         const todayStart = new Date();
         todayStart.setHours(0, 0, 0, 0);
         const todayEnd = new Date();
         todayEnd.setHours(23, 59, 59, 999);
         // console.log(`${WS_LOG_PREFIX} requestQueueStatus: Date range for query: ${todayStart.toISOString()} - ${todayEnd.toISOString()}`);

         // console.log(`${WS_LOG_PREFIX} requestQueueStatus: Fetching appointments for user ${user.id} today.`);
         const userAppointmentsToday = await context.entities.Appointment.findMany({
           where: {
             customerFolder: { userId: user.id },
             queueId: { not: null },
             status: { in: ['pending', 'confirmed', 'InProgress'] },
             expectedAppointmentStartTime: {
               gte: todayStart,
               lte: todayEnd,
             },
           },
           select: {
             queueId: true,
             id: true,
           },
           distinct: ['queueId'],
         });
         // console.log(`${WS_LOG_PREFIX} requestQueueStatus: Found ${userAppointmentsToday.length} distinct queues for user ${user.id}.`);

         const relevantQueueIds = userAppointmentsToday.map(appt => appt.queueId).filter(id => id !== null) as number[];

         if (relevantQueueIds.length === 0) {
             // console.log(`${WS_LOG_PREFIX} requestQueueStatus: User ${user.id} has no active appointments with queues for today.`);
             return;
         }

         // console.log(`${WS_LOG_PREFIX} requestQueueStatus: Calculating status for user ${user.id} in queues:`, relevantQueueIds);

         for (const queueId of relevantQueueIds) {
           // console.log(`${WS_LOG_PREFIX} requestQueueStatus: Processing queue ${queueId} for user ${user.id}.`);
           try {
              // console.log(`${WS_LOG_PREFIX} requestQueueStatus: Calculating position for user ${user.id}, queue ${queueId}.`);
              const result = await calculateQueuePositionAndEstimate(user.id, queueId, new Date(), context);
              // console.log(`${WS_LOG_PREFIX} requestQueueStatus: Calculation result for user ${user.id}, queue ${queueId}:`, result);
              
              // console.log(`${WS_LOG_PREFIX} requestQueueStatus: Emitting 'queueUpdate' to user ${user.id} for queue ${queueId}. Payload:`, { queueId, ...result });
              socket.emit('queueUpdate', { queueId, ...result });
              // console.log(`${WS_LOG_PREFIX} requestQueueStatus: Successfully emitted 'queueUpdate' to user ${user.id} for queue ${queueId}.`);
           } catch (calcError: any) {
              // console.error(`${WS_LOG_PREFIX} requestQueueStatus: Error calculating queue status for user ${user.id}, queue ${queueId}:`, calcError);
              socket.emit('queueUpdate', { queueId, position: null, estimatedStartTime: null, estimatedWaitMinutes: null, error: `Failed to calculate status for queue ${queueId}.` });
           }
         }
         // console.log(`${WS_LOG_PREFIX} requestQueueStatus: Finished processing for user ${user.id}.`);
       } catch (error: any) {
         // console.error(`${WS_LOG_PREFIX} requestQueueStatus: General error for user ${user.id}:`, error);
         const errorMessage = error instanceof HttpError ? error.message : 'Failed to get queue status.';
         socket.emit('queueUpdate', { queueId: 0, position: null, estimatedStartTime: null, estimatedWaitMinutes: null, error: errorMessage });
       }
    });

    socket.on('notifyQueueChange', async ({ queueId }: { queueId: number }) => {
      // console.log(`${WS_LOG_PREFIX} notifyQueueChange: Received from socket ID: ${socket.id}, User: ${socket.data.user?.id || 'Unauthenticated'}, Queue ID: ${queueId}`);
      if (!socket.data.user) {
        // console.warn(`${WS_LOG_PREFIX} notifyQueueChange: Unauthenticated user (socket ID: ${socket.id}) tried to notify queue change. Aborting.`);
        return;
      }
      // console.log(`${WS_LOG_PREFIX} notifyQueueChange: Calling broadcastQueueStateUpdate for queue ${queueId}, triggered by user ${socket.data.user.id}.`);
      await broadcastQueueStateUpdate(queueId, context, io, socket);
      // console.log(`${WS_LOG_PREFIX} notifyQueueChange: broadcastQueueStateUpdate finished for queue ${queueId}.`);
    });

    socket.on('disconnect', (reason: string) => {
      if (user) {
         // console.log(`${WS_LOG_PREFIX} Disconnect: User ${user.id} (Socket ID: ${socket.id}) disconnected. Reason: ${reason}`);
      } else {
         // console.log(`${WS_LOG_PREFIX} Disconnect: Unauthenticated user (Socket ID: ${socket.id}) disconnected. Reason: ${reason}`);
      }
    });

    socket.on('error', (err: Error) => {
       // console.error(`${WS_LOG_PREFIX} Error: Socket error on socket ID: ${socket.id}, User: ${user?.id || 'Unauthenticated'}. Error:`, err);
    });
  });
  // console.log(`${WS_LOG_PREFIX} webSocketFn: Event handlers for 'connection' have been set up.`);
};

// Calculation Logic Implementation - Simplified Model
// ==============================================================================

const MIN_APPOINTMENT_DURATION_MINUTES = 5; // Default duration if service/duration is missing

// Add export to calculateQueuePositionAndEstimate
export async function calculateQueuePositionAndEstimate(
  userId: string,
  queueId: number,
  date: Date, // The current date (used to filter appointments for today)
  context: Parameters<WebSocketFn>[1]
): Promise<any> {

  // console.log(`${WS_LOG_PREFIX} calculateQueuePositionAndEstimate: Starting for userId: ${userId}, queueId: ${queueId}, date: ${date.toISOString().split('T')[0]}`);

  const todayStart = new Date(date); 
  todayStart.setHours(0, 0, 0, 0);
  const todayEnd = new Date(date);
  todayEnd.setHours(23, 59, 59, 999);
  const now = new Date();
  // console.log(`${WS_LOG_PREFIX} calculateQueuePositionAndEstimate: Time context: todayStart=${todayStart.toISOString()}, todayEnd=${todayEnd.toISOString()}, now=${now.toISOString()}`);

  // console.log(`${WS_LOG_PREFIX} calculateQueuePositionAndEstimate: Fetching appointments for queue ${queueId} between ${todayStart.toISOString()} and ${todayEnd.toISOString()}.`);
  const appointments = await context.entities.Appointment.findMany({
    where: {
      queueId: queueId,
      status: { in: ['pending', 'confirmed', 'InProgress'] },
      expectedAppointmentStartTime: {
        gte: todayStart,
        lte: todayEnd,
      },
    },
    select: {
      // Scalar fields from the Appointment model
      id: true,
      status: true, // Explicitly selecting status as requested
      queueId: true, // Foreign key, useful to have explicitly
      expectedAppointmentStartTime: true,
      expectedAppointmentEndTime: true,
      // Add any other scalar fields from Appointment you might need, e.g., notes

      // Relations (previously under `include`)
      involvedSwapRequests: {
        select: {
          id: true,
          status: true,
          createdAt: true,
          updatedAt: true,
          requestedBy: true,
          appointment1: true,
          appointment2: true,
        }
      },
      initiatedSwapRequests: {
        select: {
          id: true,
          status: true,
          createdAt: true,
          updatedAt: true,
          requestedBy: true,
          appointment1: true,
          appointment2: true,
        }
      },
      service: {
        select: {
          duration: true,
          title: true,
          provider: {
            select: {
              id: true,
              title: true,
              phone: true,
              category: {
                select: { id: true, title: true }
              }
            }
          }
        }
      },
      customerFolder: {
        select: {
          id: true,
          userId: true,
          customer: {
            select: { id: true, firstName: true, lastName: true }
          }
        }
      },
      queue: { // This is the 'queue' relation
        select: {
          id: true,
          title: true
        }
      }
    },
    orderBy: {
      expectedAppointmentStartTime: 'asc',
    },
  });
  // console.log(`${WS_LOG_PREFIX} calculateQueuePositionAndEstimate: Found ${appointments.length} appointments in queue ${queueId} for today.`);

  const userAppointmentIndex = appointments.findIndex((appt: typeof appointments[number]) => appt.customerFolder.userId === userId);
  // console.log(`${WS_LOG_PREFIX} calculateQueuePositionAndEstimate: User ${userId} index in appointments array: ${userAppointmentIndex}.`);

  if (userAppointmentIndex === -1) {
    // console.log(`${WS_LOG_PREFIX} calculateQueuePositionAndEstimate: User ${userId} not found in queue ${queueId} for today. Returning error.`);
    return { position: null, estimatedStartTime: null, estimatedWaitMinutes: null, error: "You are not currently in this queue for today." };
  }

  const position = userAppointmentIndex + 1;
  // console.log(`${WS_LOG_PREFIX} calculateQueuePositionAndEstimate: User ${userId} position in queue: ${position}.`);
  const userAppointment = appointments[userAppointmentIndex];

  let precedingDurationMinutes = 0;
  if (userAppointmentIndex > 0) {
    // console.log(`${WS_LOG_PREFIX} calculateQueuePositionAndEstimate: Calculating duration for ${userAppointmentIndex} preceding appointments.`);
    for (let i = 0; i < userAppointmentIndex; i++) {
      const precedingAppt = appointments[i];
      let addedDurationForThisAppt = 0;

      if (precedingAppt.status === 'InProgress') {
        if (precedingAppt.expectedAppointmentEndTime) { // Check if not null
          const now = new Date(); // 'now' is defined outside the selection but available in this scope
          const expectedEndTime = new Date(precedingAppt.expectedAppointmentEndTime); // Now safe
          const timeDiffMinutes = Math.round((expectedEndTime.getTime() - now.getTime()) / (1000 * 60));

          if (timeDiffMinutes > 0) {
            // addedDurationForThisAppt = timeDiffMinutes;
            // console.log(`${WS_LOG_PREFIX} calculateQueuePositionAndEstimate: Preceding InProgress appt ${precedingAppt.id} adds ${addedDurationForThisAppt} min (time remaining until expected end).`);
          } else {
            addedDurationForThisAppt = 0; // Already past or at expected end
            // console.log(`${WS_LOG_PREFIX} calculateQueuePositionAndEstimate: Preceding InProgress appt ${precedingAppt.id} is past its expected end time or at its end. Adding 0 min.`);
          }
        } else {
          // Fallback for InProgress with null expectedAppointmentEndTime
          // MIN_APPOINTMENT_DURATION_MINUTES is assumed to be defined elsewhere
          addedDurationForThisAppt = precedingAppt.service?.duration ?? MIN_APPOINTMENT_DURATION_MINUTES;
          // console.warn(`${WS_LOG_PREFIX} calculateQueuePositionAndEstimate: Preceding InProgress appt ${precedingAppt.id} has null expectedAppointmentEndTime. Adding its full service duration ${addedDurationForThisAppt} min as fallback.`);
        }
      } else { // For appointments NOT InProgress (e.g., pending, confirmed)
        addedDurationForThisAppt = precedingAppt.service?.duration ?? MIN_APPOINTMENT_DURATION_MINUTES;
        // console.log(`${WS_LOG_PREFIX} calculateQueuePositionAndEstimate: Preceding (not InProgress) appt ${precedingAppt.id} adds its service duration ${addedDurationForThisAppt} min.`);
      }
      precedingDurationMinutes += addedDurationForThisAppt;
      // console.log(`${WS_LOG_PREFIX} calculateQueuePositionAndEstimate: After appt ${precedingAppt.id} (status: ${precedingAppt.status}), total precedingDurationMinutes: ${precedingDurationMinutes}.`);
    }
  } else {
    // console.log(`${WS_LOG_PREFIX} calculateQueuePositionAndEstimate: User ${userId} is first in queue, no preceding duration to calculate.`);
  }
  // console.log(`${WS_LOG_PREFIX} calculateQueuePositionAndEstimate: Total preceding duration for user ${userId}: ${precedingDurationMinutes} minutes.`);
  let precedingDurationSeconds = precedingDurationMinutes * 60;
  let timeUntilStartSeconds = 0; // Changed from timeUntilStartMinutes
  let timeUntilEndSeconds = 0;
  let totalWaitSeconds = 0;
  if (appointments[0]?.expectedAppointmentStartTime) {
    const firstApptStartTime = appointments[0].expectedAppointmentStartTime;
    // 'now' is defined outside the selection and is available in this scope.
    // console.log(`${WS_LOG_PREFIX} calculateQueuePositionAndEstimate: First appointment in queue starts at ${firstApptStartTime.toISOString()}. Current time is ${now.toISOString()}.`);
    const timeUntilStartMillis = Math.max(0, firstApptStartTime.getTime() - now.getTime());
    timeUntilStartSeconds = Math.round(timeUntilStartMillis / 1000); // Calculate in seconds
  } else {
    // console.warn(`${WS_LOG_PREFIX} calculateQueuePositionAndEstimate: First appointment in queue (user ${userId}'s appointment ${userAppointment.id}) is missing expectedStartTime. Cannot calculate time until start. Using 0.`);
  }

  if(appointments[0]?.expectedAppointmentEndTime) {
    const firstApptEndTime = appointments[0].expectedAppointmentEndTime;
    const timeUntilEndMillis = Math.max(0, firstApptEndTime.getTime() - now.getTime());
    timeUntilEndSeconds = Math.round(timeUntilEndMillis / 1000); // Calculate in seconds
  } else {
    // console.warn(`${WS_LOG_PREFIX} calculateQueuePositionAndEstimate: First appointment in queue (user ${userId}'s appointment ${userAppointment.id}) is missing expectedEndTime. Cannot calculate time until end. Using 0.`);
  }
  // console.log(`${WS_LOG_PREFIX} calculateQueuePositionAndEstimate: Time until first appointment starts for user ${userId}: ${timeUntilStartSeconds} seconds.`);

  // Convert the seconds back to minutes for the total wait time calculation, if totalWaitMinutes is desired in minutes.
  const timeUntilStartComponentMinutes = Math.round(timeUntilStartSeconds / 60);
  const totalWaitMinutes = precedingDurationMinutes + timeUntilStartComponentMinutes;
  if(appointments[0]?.status === 'InProgress') {
    totalWaitSeconds = precedingDurationSeconds + timeUntilEndSeconds;
  } else {
    totalWaitSeconds = precedingDurationSeconds + timeUntilStartSeconds;
  }
  // console.log(`${WS_LOG_PREFIX} calculateQueuePositionAndEstimate: User ${userId} in queue ${queueId}: Position ${position}, Preceding Mins: ${precedingDurationMinutes}, TimeToStart Mins: ${timeUntilStartComponentMinutes}, Est. Wait: ${totalWaitMinutes} min.`);
  console.log(`${WS_LOG_PREFIX} userAppointment.initiatedSwapRequests for user`, `${userAppointment.customerFolder.customer.firstName} ${userAppointment.customerFolder.customer.lastName}`, userAppointment.initiatedSwapRequests.length);
  console.log(`${WS_LOG_PREFIX} userAppointment.involvedSwapRequests for user`, `${userAppointment.customerFolder.customer.firstName} ${userAppointment.customerFolder.customer.lastName}`, userAppointment.involvedSwapRequests.length);
  console.log('--------------------------------');
  const result = {
    queueId: queueId,
    currentUserPosition: position,
    status: userAppointment.status,
    serviceName: userAppointment.service?.title,
    currentUserAppointmentId: userAppointment.id,
    appointmentDate: userAppointment.expectedAppointmentStartTime,
    appointmentTime: userAppointment.expectedAppointmentStartTime,
    initiatedSwapRequests: userAppointment.initiatedSwapRequests,
    involvedSwapRequests: userAppointment.involvedSwapRequests,
    // estimatedStartTime: null, // Still null in this simplified model
    currentUserEstimatedWaitMinutes: totalWaitMinutes, // This remains in minutes
    currentUserEstimatedWaitSeconds: totalWaitSeconds,
    timeUntilStartSeconds:timeUntilStartSeconds,
    totalActiveInQueue: appointments.length,
    professionalDetails: userAppointment.service?.provider,
    queueMembers: appointments.map((appt: typeof appointments[number]) => ({
      id: appt.id,
      status: appt.status,
      displayName: `${appt.customerFolder.customer.firstName} ${appt.customerFolder.customer.lastName}`,
      serviceDurationMinutes: appt.service?.duration,
      serviceName: appt.service?.title,
      appointmentDate: appt.expectedAppointmentStartTime,
      appointmentTime: appt.expectedAppointmentStartTime,
      initiatedSwapRequests: appt.initiatedSwapRequests,
      involvedSwapRequests: appt.involvedSwapRequests,
    })),
  };
  // console.log(`${WS_LOG_PREFIX} calculateQueuePositionAndEstimate: Final result for userId ${userId}, queueId ${queueId}:`, result);
  return result;
} 