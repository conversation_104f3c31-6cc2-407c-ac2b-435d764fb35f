import { type Appointment } from 'wasp/entities';

// Define context type expected by Wasp jobs
type JobContext = {
    entities: {
        Appointment: any; // Prisma Appointment delegate
    }
}

/**
 * Wasp Job to automatically cancel past appointments that are still pending or confirmed.
 */
export const autoCancelPastAppointments = async (_args: unknown, context: JobContext): Promise<void> => {
    console.log('[Job] Running autoCancelPastAppointments...');
    const now = new Date();

    try {
        // Find appointments where the end time is in the past and status is pending or confirmed
        const appointmentsToCancel = await context.entities.Appointment.findMany({
            where: {
                expectedAppointmentEndTime: {
                    lt: now // less than now means it's in the past
                },
                status: {
                    in: ['pending', 'confirmed']
                }
            },
            select: {
                id: true // Select only IDs for the updateMany query
            }
        });

        if (appointmentsToCancel.length === 0) {
            console.log('[Job] No past pending/confirmed appointments found to cancel.');
            return; // Nothing to do
        }

        // Explicitly type 'appt' here
        const appointmentIds = appointmentsToCancel.map((appt: { id: number }) => appt.id);
        console.log(`[Job] Found ${appointmentIds.length} appointments to auto-cancel: IDs ${appointmentIds.join(', ')}`);

        // Update the status of these appointments to 'canceled'
        const updateResult = await context.entities.Appointment.updateMany({
            where: {
                id: {
                    in: appointmentIds
                }
            },
            data: {
                status: 'canceled',
                canceledAt: now // Set cancellation timestamp
            }
        });

        console.log(`[Job] Successfully auto-canceled ${updateResult.count} appointments.`);

    } catch (error: any) {
        console.error('[Job] Error during autoCancelPastAppointments:', error);
        // Consider throwing the error if you want Wasp/PgBoss to potentially retry
        // throw error;
    }
    console.log('[Job] autoCancelPastAppointments finished.');
}; 