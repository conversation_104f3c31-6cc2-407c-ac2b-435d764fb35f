import axios from 'axios';

// Configuration for your SMS service
const SMS_API_URL = 'http://sms-dalti.ddns.net:8080/message';
const SMS_API_USERNAME = 'sms';
const SMS_API_PASSWORD = '5NZ8VdhE'; // IMPORTANT: Consider moving this to environment variables for better security

interface SmsResponse {
  // Define this based on the actual response structure of your SMS API
  // For example:
  // success: boolean;
  // messageId?: string;
  // error?: string;
}

/**
 * Sends an OTP SMS to the given phone number.
 * @param phoneNumber The destination phone number.
 * @param otp The One-Time Password to send.
 * @param firstName The first name of the user, to personalize the SMS.
 * @returns True if the SMS was likely sent successfully, false otherwise.
 */
export async function sendOtpSms(phoneNumber: string, otp: string, firstName?: string): Promise<boolean> {
  const messageContent = `Dalti OTP: ${otp}`;

  try {
    console.log(`Attempting to send OTP to ${phoneNumber}`);
    const response = await axios.post<SmsResponse>(
      SMS_API_URL,
      {
        message: messageContent,
        phoneNumbers: [phoneNumber],
      },
      {
        headers: {
          'Content-Type': 'application/json',
        },
        auth: {
          username: SMS_API_USERNAME,
          password: SMS_API_PASSWORD,
        },
        timeout: 10000, // 10 seconds timeout
      }
    );

    if (response.status === 202 || response.status === 200) {
      console.log(`OTP SMS sent successfully to ${phoneNumber}. Status: ${response.status}`);
      return true;
    } else {
      console.warn(`SMS API responded with status: ${response.status}`, response.data);
      return false;
    }
  } catch (error: any) {
    console.error(`Error sending OTP SMS to ${phoneNumber}:`, error.isAxiosError ? error.message : error);
    if (axios.isAxiosError(error) && error.response) {
      console.error('SMS API Error Response:', error.response.data);
    }
    return false;
  }
} 