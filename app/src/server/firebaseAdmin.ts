    // src/server/firebaseAdmin.ts
    import admin from 'firebase-admin';
    console.log('Initializing Firebase Admin SDK' , admin);
    if (!admin?.apps?.length) {
      try {
        const serviceAccountConfigString = process.env.FIREBASE_ADMIN_SDK_CONFIG;
        // console.log('serviceAccountConfigString', serviceAccountConfigString);
        if (!serviceAccountConfigString) {
          throw new Error('FIREBASE_ADMIN_SDK_CONFIG environment variable is not set.');
        }

        // Parse the full service account JSON from environment variable
        let serviceAccount: any;
        try {
          serviceAccount = JSON.parse(serviceAccountConfigString);
        } catch (parseError) {
          throw new Error('FIREBASE_ADMIN_SDK_CONFIG must be a valid JSON string containing the service account configuration.');
        }

        // Validate required fields
        if (!serviceAccount.project_id || !serviceAccount.private_key || !serviceAccount.client_email) {
          throw new Error('FIREBASE_ADMIN_SDK_CONFIG must contain project_id, private_key, and client_email fields.');
        }

        console.log(`Using Firebase project: ${serviceAccount.project_id}`);

        admin.initializeApp({
          credential: admin.credential.cert(serviceAccount),
          // databaseURL: `https://${serviceAccount.project_id}.firebaseio.com` // Optional: if using Realtime Database
        });
        console.log('Firebase Admin SDK initialized successfully.');
      } catch (error: any) {
        console.error('Firebase Admin SDK initialization error:', error.message);
        // You might want to throw the error or handle it more gracefully depending on your app's needs
      }
    }

    export default admin;