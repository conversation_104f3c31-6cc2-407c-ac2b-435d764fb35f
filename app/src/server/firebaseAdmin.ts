    // src/server/firebaseAdmin.ts
    import admin from 'firebase-admin';
    console.log('Initializing Firebase Admin SDK' , admin);
    if (!admin?.apps?.length) {
      try {
        const serviceAccountConfigString = process.env.FIREBASE_ADMIN_SDK_CONFIG;
        // console.log('serviceAccountConfigString', serviceAccountConfigString);
        if (!serviceAccountConfigString) {
          throw new Error('FIREBASE_ADMIN_SDK_CONFIG environment variable is not set.');
        }
        const serviceAccount:any = {
            "type": "service_account",
            "project_id": "dalti-prod",
            "private_key_id": "a6c827b758610728c73d3ce47ca9bc1ccc4c8d7b",
            "private_key": serviceAccountConfigString,
            "client_email": "<EMAIL>",
            "client_id": "101488669664043246428",
            "auth_uri": "https://accounts.google.com/o/oauth2/auth",
            "token_uri": "https://oauth2.googleapis.com/token",
            "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
            "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-fbsvc%40dalti-prod.iam.gserviceaccount.com",
            "universe_domain": "googleapis.com"
          }

        admin.initializeApp({
          credential: admin.credential.cert(serviceAccount),
          // databaseURL: `https://${serviceAccount.project_id}.firebaseio.com` // Optional: if using Realtime Database
        });
        console.log('Firebase Admin SDK initialized successfully.');
      } catch (error: any) {
        console.error('Firebase Admin SDK initialization error:', error.message);
        // You might want to throw the error or handle it more gracefully depending on your app's needs
      }
    }

    export default admin;