    // src/server/firebaseAdmin.ts
    import admin from 'firebase-admin';
    import * as fs from 'fs';
    import * as path from 'path';

    console.log('Initializing Firebase Admin SDK' , admin);
    if (!admin?.apps?.length) {
      try {
        // Try to read from environment variable first, then fall back to file
        let serviceAccount: any;

        const serviceAccountConfigString = process.env.FIREBASE_ADMIN_SDK_CONFIG;
        if (serviceAccountConfigString && serviceAccountConfigString !== 'REPLACE_WITH_DALTI_PROD_SERVICE_ACCOUNT_JSON') {
          try {
            serviceAccount = JSON.parse(serviceAccountConfigString);
            console.log('✅ Using Firebase service account from environment variable');
          } catch (parseError) {
            console.warn('⚠️ Failed to parse environment variable, trying file...');
            serviceAccount = null;
          }
        }

        // If environment variable failed or not set, try reading from file
        if (!serviceAccount) {
          const serviceAccountPath = path.join(process.cwd(), 'firebase-service-account.json');
          if (fs.existsSync(serviceAccountPath)) {
            const serviceAccountFile = fs.readFileSync(serviceAccountPath, 'utf8');
            serviceAccount = JSON.parse(serviceAccountFile);
            console.log('✅ Using Firebase service account from file');
          } else {
            throw new Error('Firebase service account not found in environment variable or file');
          }
        }

        // Validate required fields
        if (!serviceAccount.project_id || !serviceAccount.private_key || !serviceAccount.client_email) {
          throw new Error('FIREBASE_ADMIN_SDK_CONFIG must contain project_id, private_key, and client_email fields.');
        }

        console.log(`Using Firebase project: ${serviceAccount.project_id}`);

        admin.initializeApp({
          credential: admin.credential.cert(serviceAccount),
          // databaseURL: `https://${serviceAccount.project_id}.firebaseio.com` // Optional: if using Realtime Database
        });
        console.log('Firebase Admin SDK initialized successfully.');
      } catch (error: any) {
        console.error('Firebase Admin SDK initialization error:', error.message);
        // You might want to throw the error or handle it more gracefully depending on your app's needs
      }
    }

    export default admin;