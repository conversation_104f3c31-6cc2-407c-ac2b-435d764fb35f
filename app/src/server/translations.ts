import { type PrismaClient, LanguageCode } from '@prisma/client';
import { eld } from 'eld'; // Language detection library
import deepl from 'deepl-node'; // And 'deepl-node'

// --- Your existing translation provider setup ---
const supportedLanguages: LanguageCode[] = [LanguageCode.AR, LanguageCode.EN, LanguageCode.FR];

const providers = [{
  name: "deepL",
  translator: async (key: string, text: string, srcLang: LanguageCode, target: LanguageCode) => {
    const api = new deepl.Translator(key);
    // DeepL uses specific language codes (e.g., 'en-US' for English, 'ar' for Arabic, 'fr' for French)
    // You'll need to map your LanguageCode enum to these.
    const deepLTargetLang = target === LanguageCode.EN ? "en-US" : target.toLowerCase();
    const deepLSrcLang = srcLang.toLowerCase();
    const result = await api.translateText(text, deepLSrcLang as deepl.SourceLanguageCode | null, deepLTargetLang as deepl.TargetLanguageCode);
    return result.text;
  }
},
{
  name: "google", // Placeholder for Google Translate or other providers
  translator: async (key: string, text: string, srcLang: LanguageCode, target: LanguageCode) => {
    console.warn("Google Translate provider is not implemented yet.");
    // Implement Google Translate API call here if needed
    return null; // Or throw an error
  }
}];

const keys = {
  deepL: [
    process.env.DEEPL_API_KEY_1, // It's better to use environment variables for API keys
    // process.env.DEEPL_API_KEY_2,
  ].filter(key => !!key) as string[], // Filter out undefined keys
  google: [] // Add Google API keys here if you implement it
};

async function detectLanguage(text: string): Promise<LanguageCode> {
  // Use the imported eld library for language detection
  const result = eld.detect(text, 1);
  const detectedLang = result.language.toUpperCase();
  // Ensure the detected language is one of your supported LanguageCodes
  if (supportedLanguages.includes(detectedLang as LanguageCode)) {
    return detectedLang as LanguageCode;
  }
  console.warn(`Detected language ${detectedLang} is not in supported languages. Defaulting to EN.`);
  return LanguageCode.EN; // Or handle this case as an error
}

async function translateAttempt(text: string, srcLang: LanguageCode, targetLang: LanguageCode): Promise<{ response: string | null, provider?: string, apiKey?: string }> {
  let resultText: string | null = null;
  for (const provider of providers) {
    const providerKeys = keys[provider.name as keyof typeof keys];
    if (!providerKeys || providerKeys.length === 0) {
      continue;
    }
    for (const key of providerKeys) {
      try {
        resultText = await provider.translator(key, text, srcLang, targetLang);
        if (resultText) {
          console.log(`Successfully translated using ${provider.name} (key: ${key.substring(0, 5)}...): "${text}" (${srcLang}) -> "${resultText}" (${targetLang})`);
          return { response: resultText, provider: provider.name, apiKey: key };
        }
      } catch (error) {
        console.error(`Error with provider ${provider.name} (key: ${key.substring(0, 5)}...):`, error);
      }
    }
  }
  if (!resultText) {
    console.warn(`Failed to translate "${text}" from ${srcLang} to ${targetLang} using all providers.`);
  }
  return { response: resultText };
}

// --- Core Translation Function ---
export async function translateAndStore(
  prisma: any,
  refId: string, // Changed to string to be more generic (can be int or uuid)
  tableName: string,
  refField: string,
  originalText: string | null | undefined
) {
  if (!originalText) {
    console.log(`Skipping translation for ${tableName}.${refField} (ID: ${refId}) as original text is empty.`);
    return;
  }

  try {
    const sourceLanguage = await detectLanguage(originalText);
    console.log(`Source language: ${sourceLanguage}`);
    // 1. Store the original text
    await prisma.Translation.upsert({
      where: { unique_translation_entry: { tableName, refId, refField, languageCode: sourceLanguage } },
      update: { text: originalText },
      create: { tableName, refId, refField, languageCode: sourceLanguage, text: originalText },
    });
    console.log(`Stored original text for ${tableName}.${refField} (ID: ${refId}) in ${sourceLanguage}: "${originalText}"`);

    // 2. Translate and store for other supported languages
    const targetLanguages = supportedLanguages.filter(lang => lang !== sourceLanguage);
    console.log(`Target languages: ${targetLanguages}`);
    for (const targetLang of targetLanguages) {
      const translationResult = await translateAttempt(originalText, sourceLanguage, targetLang);
      console.log(`Translation result: ${translationResult}`);
      if (translationResult.response) {
        await prisma.Translation.upsert({
          where: { unique_translation_entry: { tableName, refId, refField, languageCode: targetLang } },
          update: { text: translationResult.response },
          create: { tableName, refId, refField, languageCode: targetLang, text: translationResult.response },
        });
        console.log(`Stored translated text for ${tableName}.${refField} (ID: ${refId}) in ${targetLang}: "${translationResult.response}"`);
      } else {
        console.warn(`Could not translate ${tableName}.${refField} (ID: ${refId}) from ${sourceLanguage} to ${targetLang}.`);
      }
    }
  } catch (error) {
    console.error(`Error in translateAndStore for ${tableName}.${refField} (ID: ${refId}, Text: "${originalText}"):`, error);
    // Depending on your error handling strategy, you might want to rethrow or handle silently
  }
}

// Helper function to remove translations when an entity is deleted
export async function deleteTranslations(
  prisma: any,
  refId: string,
  tableName: string
) {
  try {
    const { count } = await prisma.Translation.deleteMany({
      where: {
        refId: refId,
        tableName: tableName,
      },
    });
    console.log(`Deleted ${count} translations for ${tableName} (ID: ${refId})`);
  } catch (error) {
    console.error(`Error deleting translations for ${tableName} (ID: ${refId}):`, error);
  }
}


// ... existing code in app/src/server/translations.ts ...

// ADDED: Helper function to get a single translated string
export async function getTranslatedString(
  prisma: any,
  tableName: string,
  refId: string,
  refField: string,
  targetLanguage: LanguageCode,
  fallbackLanguage: LanguageCode = LanguageCode.EN // Default fallback
): Promise<string | null> {
  if (!refId || !tableName || !refField || !targetLanguage) {
    console.warn('[getTranslatedString] Missing required arguments:', { tableName, refId, refField, targetLanguage });
    return null; 
  }

  try {
    // 1. Attempt to get the target language translation
    let translation = await prisma.Translation.findUnique({
      where: {
        unique_translation_entry: { tableName, refId, refField, languageCode: targetLanguage },
      },
      select: { text: true }
    });

    if (translation?.text) {
      return translation.text;
    }

    // 2. Attempt to get the fallback language translation
    if (targetLanguage !== fallbackLanguage) { 
        translation = await prisma.Translation.findUnique({
            where: {
                unique_translation_entry: { tableName, refId, refField, languageCode: fallbackLanguage },
            },
            select: { text: true }
        });
        if (translation?.text) {
            return translation.text;
        }
    }
    
    // 3. As a last resort, try to find *any* translation for this field and assume it's the original
    // This is less precise. Storing the "original language" explicitly would be better.
    const anyTranslation = await prisma.Translation.findFirst({
        where: { tableName, refId, refField },
        select: { text: true },
        // Consider ordering by createdAt or a specific language if you want more control over "any"
    });
    return anyTranslation?.text || null;

  } catch (error) {
    console.error(`Error fetching translation for ${tableName}.${refField} (ID: ${refId}, Lang: ${targetLanguage}):`, error);
    return null; // Return null on error to avoid crashing the main operation
  }
}