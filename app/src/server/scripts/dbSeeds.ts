import { faker } from '@faker-js/faker';
import { LanguageCode, Role, SProvidingPlace, type PrismaClient, type User as PrismaUser } from '@prisma/client';
import { getSubscriptionPaymentPlanIds, SubscriptionStatus } from '../../payment/plans';
import dayjs from 'dayjs';
import {
  createEmailVerificationLink,
  sendEmailVerificationEmail,
  createUser,
  createProviderId,
  sanitizeAndSerializeProviderData,
  defineUserSignupFields,
} from 'wasp/server/auth';
import { rethrowPossibleAuthError, validateAndGetUserFields } from 'wasp/auth/utils';
import { z as z$1 } from 'zod';

type MockUserData = Omit<PrismaUser, 'id'>;
const emailDataSchema = z$1.object({
  email: z$1.string().email(),
  // These fields might be passed as additional data during signup
  role: z$1.nativeEnum(Role).optional(),
  firstName: z$1.string().optional(),
  lastName: z$1.string().optional()
});
const adminEmails = process.env.ADMIN_EMAILS?.split(",") || [];
const getEmailUserFields = defineUserSignupFields({
  email: (data) => {
    const basicData = z$1.object({ email: z$1.string().email() }).parse(data);
    return basicData.email;
  },
  username: (data) => {
    const basicData = z$1.object({ email: z$1.string().email() }).parse(data);
    return basicData.email;
  },
  isAdmin: (data) => {
    const basicData = z$1.object({ email: z$1.string().email() }).parse(data);
    return adminEmails.includes(basicData.email);
  },
  // Add field definitions for role, firstName, lastName
  role: (data) => {
    const extendedData = emailDataSchema.safeParse(data);
    const roleValue = extendedData.success ? extendedData.data.role : void 0;
    return roleValue === "ADMIN" /* ADMIN */ || roleValue === "USER" /* USER */ || roleValue === "CUSTOMER" /* CUSTOMER */ || roleValue === "CLIENT" /* CLIENT */ ? roleValue : "CLIENT" /* CLIENT */;
  },
  firstName: (data) => {
    const extendedData = emailDataSchema.safeParse(data);
    return extendedData.success ? extendedData.data.firstName : void 0;
  },
  lastName: (data) => {
    const extendedData = emailDataSchema.safeParse(data);
    return extendedData.success ? extendedData.data.lastName : void 0;
  }
});

type SignupFields = {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
};

async function signup(fields:SignupFields , role:Role) {
  
  const providerId = createProviderId("email", fields.email);
  const userFields = await validateAndGetUserFields(fields, getEmailUserFields);
  const newUserProviderData = await sanitizeAndSerializeProviderData(
    {
      hashedPassword: fields.password,
      isEmailVerified: true,
      emailVerificationSentAt: null,
      passwordResetSentAt: null
    }
  );
  try {
    // await onBeforeSignupHook({ req, providerId });
    const user = await createUser(
      providerId,
      newUserProviderData,
      // Using any here because we want to avoid TypeScript errors and
      // rely on Prisma to validate the data.
      {
          ...userFields,
          role
      }
    );
    // await onAfterSignupHook({ req, providerId, user });
  } catch (e) {
    rethrowPossibleAuthError(e);
  }
  return ({ success: true });
};






export async function seedMockUsers(prismaClient: PrismaClient) {
  const DEFAULT_SEED_PASSWORD = 'password123'; // Define a default password for seeded users

  console.log('Cleaning up previous provider-related data (if any)...');
  const providerEmail = '<EMAIL>';
  const specialCustomerEmail = '<EMAIL>';

  // Find Provider (required for cleanup and linking)
  // This part of cleanup relies on the provider user potentially existing to find its related entities.
  const testProviderForCleanup = await prismaClient.user.findUnique({
    where: { email: providerEmail },
    include: { serviceProvider: { select: {  providingPlaces: { select: { id: true , name: true } } ,id: true, queues: { select: { id: true } } } } },
  });

  // This variable is used if provider data is found and cleaned up.
  // It's distinct from the providerSProviderId used later for the actual seeding.
  // if (testProviderForCleanup && testProviderForCleanup.serviceProvider) {
  //   const sProviderIdForCleanup = testProviderForCleanup.serviceProvider.id;
  //   // Delete provider's related data (SProvider, Place, Service, Queue, Appts, Folders)
  //   await prismaClient.appointment.deleteMany({ where: { place: { sProviderId: sProviderIdForCleanup } } });
  //   await prismaClient.customerFolder.deleteMany({ where: { sProviderId: sProviderIdForCleanup } });
  //   for (const queue of testProviderForCleanup.serviceProvider.queues) {
  //       await prismaClient.queueOpeningHours.deleteMany({ where: { queueOpening: { queueId: queue.id } }});
  //       await prismaClient.queueOpening.deleteMany({ where: { queueId: queue.id } });
  //   }
  //   await prismaClient.queue.deleteMany({ where: { sProvidingPlace: { sProviderId: sProviderIdForCleanup } } });
  //   await prismaClient.service.deleteMany({ where: { sProviderId: sProviderIdForCleanup } });
  //   await prismaClient.sProvidingPlace.deleteMany({ where: { sProviderId: sProviderIdForCleanup } });
  //   await prismaClient.sProvider.deleteMany({ where: { userId: testProviderForCleanup.id } });
  //   console.log('Deleted old provider-related data based on existing provider user.');
  // } else {
  //    console.warn(`Provider user ${providerEmail} or their SProvider record not found during initial cleanup. Some cleanup steps might be skipped.`);
  // }

  // --- DO NOT DELETE THE PROVIDER OR SPECIAL CUSTOMER USER during this phase --- 
  // The signup function will be used to ensure they exist.

  // Delete the OTHER 9 test customer users individually 
  console.log('Cleaning up previous generic test customer users...');
  for (let i = 2; i <= 10; i++) { // Start from 2 to skip the special customer email pattern
    await prismaClient.user.deleteMany({ where: { email: `customer${i}@example.com` } });
  }
  console.log('Deleted old generic test customer users (customer2@... to customer10@...).');
  // --- End Cleanup ---

  // --- Ensure Provider User Exists & Setup Provider Data ---
  console.log(`Ensuring provider user ${providerEmail} exists via signup and setting up provider data...`);
  let providerUser: PrismaUser;
  let currentSProviderId: number;
  let providingPlace: {id: number , name: string};
  if(!testProviderForCleanup) {
    try {
      await signup(
        {
          email: providerEmail,
          password: DEFAULT_SEED_PASSWORD,
          firstName: 'Nacereddine', 
          lastName: 'Houidi',
        },
        Role.CLIENT
      );
      console.log(`  Signup call for provider ${providerEmail} completed.`);
      
      providerUser = await prismaClient.user.findUniqueOrThrow({
        where: { email: providerEmail },
      });
      providerUser = await prismaClient.user.update({
        where: { id: providerUser.id },
        data: { credits: 100 , queues:5 },
      });
      console.log(`  Successfully fetched provider user: ${providerUser.email} (ID: ${providerUser.id})`);
    } catch (error) {
      console.error(`Error: Could not ensure provider user with email ${providerEmail}. Signup or subsequent fetch failed: ${error instanceof Error ? error.message : String(error)}`);
      throw error; // Stop seeding if provider user cannot be established
    }

    const doctorCategory = await prismaClient.providerCategory.findUnique({ where: { title: 'Doctor' } });
    const sprovider = await prismaClient.sProvider.upsert({
      where: { userId: providerUser.id },
      update: { // Ensure these fields are set/updated if SProvider already exists
        title: "Dr. Houidi's General Clinic",
        phone: '**********',
        presentation: 'General medical consultations and check-ups.',
        isVerified: true,
        providerCategoryId: doctorCategory?.id,
      },
      create: {
        userId: providerUser.id,
        title: "Dr. Houidi's General Clinic",
        phone: '**********',
        presentation: 'General medical consultations and check-ups.',
        isVerified: true,
        providerCategoryId: doctorCategory?.id,
      },
    });
    currentSProviderId = sprovider.id; 
    console.log(`Upserted SProvider: ${sprovider.title} (ID: ${currentSProviderId}) for user ${providerUser.id}`);
    console.log(`Cleaning up existing places, services, and queues for SProvider ID: ${currentSProviderId} before recreation...`);
    const placesOfSprovider = await prismaClient.sProvidingPlace.findMany({where: {sProviderId: currentSProviderId}, select: {id: true}});
    for (const place of placesOfSprovider) {
      const queuesInPlace = await prismaClient.queue.findMany({where: {sProvidingPlaceId: place.id}, select: {id: true}});
      for (const queue of queuesInPlace) {
          await prismaClient.queueOpeningHours.deleteMany({ where: { queueOpening: { queueId: queue.id } }});
          await prismaClient.queueOpening.deleteMany({ where: { queueId: queue.id } });
      }
      await prismaClient.queue.deleteMany({ where: { sProvidingPlaceId: place.id } });
    }
    await prismaClient.service.deleteMany({ where: { sProviderId: currentSProviderId } });
    await prismaClient.sProvidingPlace.deleteMany({ where: { sProviderId: currentSProviderId } });
    providingPlace = await prismaClient.sProvidingPlace.create({
      data: { sProviderId: currentSProviderId, name: 'Downtown Clinic Branch', address: '123 Health Avenue', city: 'Biskra', timezone: 'Africa/Algiers' },
    });
    console.log(`Created SProvidingPlace: ${providingPlace.name} (ID: ${providingPlace.id})`);
  } else {
    currentSProviderId = testProviderForCleanup.serviceProvider?.id || 0;
    providingPlace = testProviderForCleanup.serviceProvider?.providingPlaces[0] || { id: 0 , name: '' };
  }


 
  // This is the SProvider ID for the current seeding run.

  // Clean up existing places, services, queues for THIS sProvider before creating new ones.
  // This ensures a consistent state for the provider's setup on each seed run.

  // Note: Appointments related to these places/services would also need cleanup if not covered by broader appointment cleanup.
  // The initial cleanup for provider data handles appointments more broadly if the provider existed.
  // For a fully idempotent setup for this specific provider, one might also delete appointments linked to its places here.
  // However, the current structure seems to handle it via the initial cleanup and then customer-specific appointment creation.

  
  const serviceDuration = 5;
  const testService = await prismaClient.service.create({
    data: { sProviderId: currentSProviderId, title: 'Standard Consultation (5min)', duration: serviceDuration, pointsRequirements: 2 },
  });
  console.log(`Created Service: ${testService.title} (ID: ${testService.id})`);
  const testQueue = await prismaClient.queue.create({
    data: { sProvidingPlaceId: providingPlace.id, title: 'Consultation Room 1 Queue', isActive: true , services: { connect: { id: testService.id } } },
  });
  console.log(`Created Queue: ${testQueue.title} (ID: ${testQueue.id})`);
  const today = new Date();
  const dayOfWeekString = today.toLocaleDateString('en-US', { weekday: 'long' }); 
  const queueOpening = await prismaClient.queueOpening.create({
    data: { queueId: testQueue.id, dayOfWeek: dayOfWeekString, type: 'regular', isActive: true },
  });
  await prismaClient.queueOpeningHours.create({
    data: { queueOpeningId: queueOpening.id, timeFrom: '08:00', timeTo: '22:00' }, // Restored original time
  });
  console.log(`Created opening hours for ${testQueue.title} on ${dayOfWeekString} (08:00-22:00)`);
  // --- End Provider Setup ---

  // --- Seed Customer Users & Appointments (Handling Special Customer) ---
  console.log('Seeding customer users (incl. special) via signup and their appointments...');
  let currentAppointmentStartTime = new Date();
  currentAppointmentStartTime.setHours(17, 0, 0, 0); // Start appointments at 5 PM today

  for (let i = 1; i <= 10; i++) {
    let customerUser: PrismaUser | null = null;
    let customerEmail: string;
    let customerFirstName: string;
    let customerLastName: string;

    if (i === 1) { // Handle the special customer
      customerEmail = specialCustomerEmail;
      customerFirstName = 'Dadnas';
      customerLastName = '211';
      console.log(`  Preparing special customer ${customerEmail}...`);
    } else { // Handle generic customers
      customerEmail = `customer${i}@example.com`;
      customerFirstName = faker.person.firstName();
      customerLastName = faker.person.lastName();
      console.log(`  Preparing generic customer ${customerEmail}...`);
    }

    try {
      await signup(
        { email: customerEmail, password: DEFAULT_SEED_PASSWORD, firstName: customerFirstName, lastName: customerLastName },
        Role.CUSTOMER
      );
      console.log(`    Signup call for customer ${customerEmail} completed.`);
      customerUser = await prismaClient.user.findUnique({ where: { email: customerEmail } });

      if (customerUser) {
        console.log(`    Successfully fetched customer user: ${customerUser.email} (ID: ${customerUser.id})`);
        // Ensure credits are set for customer users, as signup doesn't handle this field.
        if (customerUser.credits !== 50) {
          customerUser = await prismaClient.user.update({
            where: { id: customerUser.id }, // Use ID for update
            data: { credits: 50 },
          });
          console.log(`      Updated credits for ${customerUser.email} to 50.`);
        }
      } else {
        console.error(`    Failed to find customer user ${customerEmail} after signup attempt. Skipping operations for this user.`);
        continue; // Skip to next customer
      }
    } catch (error) {
      console.error(`    Error during signup or fetch for customer ${customerEmail}: ${error instanceof Error ? error.message : String(error)}. Skipping operations for this user.`);
      continue; // Skip to next customer
    }
    
    // Ensure customerUser is not null before proceeding (already handled by `continue` but good for clarity)
    if (!customerUser) {
        console.error(`  Unexpected: customerUser is null for ${customerEmail} after checks. Skipping.`);
        continue;
    }

    // Find or Create CustomerFolder (using upsert to handle re-runs)
    const customerFolder = await prismaClient.customerFolder.upsert({
        where: { sProviderId_userId: { sProviderId: currentSProviderId, userId: customerUser.id } },
        update: { isActive: true },
        create: {
            userId: customerUser.id,
            sProviderId: currentSProviderId,
            isActive: true, // Ensure isActive is true on create
        },
    });

    // const appointmentEndTime = new Date(currentAppointmentStartTime.getTime() + serviceDuration * 60000);

    // // Create Appointment
    // await prismaClient.appointment.create({
    //   data: {
    //     customerFolderId: customerFolder.id,
    //     placeId: providingPlace.id,
    //     serviceId: testService.id,
    //     queueId: testQueue.id,
    //     status: 'confirmed',
    //     typeEvent: 'clinic',
    //     expectedAppointmentStartTime: currentAppointmentStartTime,
    //     expectedAppointmentEndTime: appointmentEndTime,
    //     serviceDuration: serviceDuration,
    //     notes: `Customer ${i} (${customerEmail}) - Test Appointment`,
    //   },
    // });
    // console.log(`    Created appointment for ${customerEmail} @ ${currentAppointmentStartTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', hour12: true })}`);
    // currentAppointmentStartTime = new Date(appointmentEndTime.getTime()); 
  }
  console.log('Finished seeding customer appointments.');
  // --- End Customer Seeding ---
  
  console.log('Database seeding for queue system test finished successfully!');
}

// Export the subscription seed function
export { seedSubscriptionPlans } from './subscriptionSeed';

/**
 * This function, which we've imported in `app.db.seeds` in the `main.wasp` file,
 * seeds the database with mock users via the `wasp db seed` command.
 * For more info see: https://wasp.sh/docs/data-model/backends#seeding-the-database
 */
export async function clearAppointments(prismaClient: PrismaClient) {
  console.log('Clearing all appointments and related data...');

  // Delete all AppointmentHistory records first due to foreign key constraints
  const deletedHistory = await prismaClient.appointmentHistory.deleteMany({});
  console.log(`Deleted ${deletedHistory.count} appointment history records.`);


  const deletedSwapRequests = await prismaClient.queueSwapRequest.deleteMany({});
  console.log(`Deleted ${deletedSwapRequests.count} swap request records.`);

  // Then delete all Appointment records
  const deletedAppointments = await prismaClient.appointment.deleteMany({});
  console.log(`Deleted ${deletedAppointments.count} appointment records.`);

  // Potentially clear other related tables if necessary, e.g., if there are
  // specific records in `CustomerFolder` or `Queue` that are only relevant
  // to appointments and should be cleared. For now, focusing on direct appointment data.

  console.log('Appointments and related data cleared.');
}
export async function seedProviderCategories(prismaClient: PrismaClient) {
  console.log('Seeding provider categories...');
  const languagesToSeed: LanguageCode[] = [LanguageCode.EN, LanguageCode.FR, LanguageCode.AR];

  const exampleTranslations: Record<string, Record<LanguageCode, string>> = {
    // Parent Categories
    'Healthcare': { [LanguageCode.EN]: 'Healthcare', [LanguageCode.FR]: 'Soins de santé', [LanguageCode.AR]: 'الرعاية الصحية' },
    'Legal': { [LanguageCode.EN]: 'Legal', [LanguageCode.FR]: 'Juridique', [LanguageCode.AR]: 'قانوني' },
    'Financial': { [LanguageCode.EN]: 'Financial', [LanguageCode.FR]: 'Financier', [LanguageCode.AR]: 'مالي' },
    'Automotive': { [LanguageCode.EN]: 'Automotive', [LanguageCode.FR]: 'Automobile', [LanguageCode.AR]: 'سيارات' },
    'Home Services': { [LanguageCode.EN]: 'Home Services', [LanguageCode.FR]: 'Services à domicile', [LanguageCode.AR]: 'خدمات منزلية' },
    'Education': { [LanguageCode.EN]: 'Education', [LanguageCode.FR]: 'Éducation', [LanguageCode.AR]: 'تعليم' },
    'Beauty & Wellness': { [LanguageCode.EN]: 'Beauty & Wellness', [LanguageCode.FR]: 'Beauté et Bien-être', [LanguageCode.AR]: 'الجمال والعافية' },
    'Professional Services': { [LanguageCode.EN]: 'Professional Services', [LanguageCode.FR]: 'Services professionnels', [LanguageCode.AR]: 'خدمات احترافية' },
    'Food & Dining': { [LanguageCode.EN]: 'Food & Dining', [LanguageCode.FR]: 'Restauration et Alimentation', [LanguageCode.AR]: 'الأطعمة والمطاعم' },
    'Retail': { [LanguageCode.EN]: 'Retail', [LanguageCode.FR]: 'Vente au détail', [LanguageCode.AR]: 'تجارة التجزئة' },
    'Government & Public Services': { [LanguageCode.EN]: 'Government & Public Services', [LanguageCode.FR]: 'Services gouvernementaux et publics', [LanguageCode.AR]: 'الخدمات الحكومية والعامة' },
    'Arts & Entertainment': { [LanguageCode.EN]: 'Arts & Entertainment', [LanguageCode.FR]: 'Arts et Divertissements', [LanguageCode.AR]: 'الفنون والترفيه' },
    'Sports & Recreation': { [LanguageCode.EN]: 'Sports & Recreation', [LanguageCode.FR]: 'Sports et Loisirs', [LanguageCode.AR]: 'الرياضة والترفيه' },
    'Pet Services': { [LanguageCode.EN]: 'Pet Services', [LanguageCode.FR]: 'Services pour animaux de compagnie', [LanguageCode.AR]: 'خدمات الحيوانات الأليفة' },
    'Trades & Construction': { [LanguageCode.EN]: 'Trades & Construction', [LanguageCode.FR]: 'Métiers et Construction', [LanguageCode.AR]: 'الحرف والبناء' },

    // Child Categories - Healthcare
    'Doctor': { [LanguageCode.EN]: 'Doctor', [LanguageCode.FR]: 'Médecin', [LanguageCode.AR]: 'طبيب' },
    'Dentist': { [LanguageCode.EN]: 'Dentist', [LanguageCode.FR]: 'Dentiste', [LanguageCode.AR]: 'طبيب أسنان' },
    'Physiotherapist': { [LanguageCode.EN]: 'Physiotherapist', [LanguageCode.FR]: 'Kinésithérapeute', [LanguageCode.AR]: 'أخصائي علاج طبيعي' },
    'Optometrist': { [LanguageCode.EN]: 'Optometrist', [LanguageCode.FR]: 'Optométriste', [LanguageCode.AR]: 'أخصائي بصريات' },
    'Chiropractor': { [LanguageCode.EN]: 'Chiropractor', [LanguageCode.FR]: 'Chiropracteur', [LanguageCode.AR]: 'مقوم العظام' },
    'Pharmacy': { [LanguageCode.EN]: 'Pharmacy', [LanguageCode.FR]: 'Pharmacie', [LanguageCode.AR]: 'صيدلية' },

    // Child Categories - Legal
    'Lawyer': { [LanguageCode.EN]: 'Lawyer', [LanguageCode.FR]: 'Avocat', [LanguageCode.AR]: 'محامي' },
    'Notary': { [LanguageCode.EN]: 'Notary', [LanguageCode.FR]: 'Notaire', [LanguageCode.AR]: 'كاتب عدل' },
    'Paralegal': { [LanguageCode.EN]: 'Paralegal', [LanguageCode.FR]: 'Technicien juridique', [LanguageCode.AR]: 'مساعد قانوني' },
    'Mediator': { [LanguageCode.EN]: 'Mediator', [LanguageCode.FR]: 'Médiateur', [LanguageCode.AR]: 'وسيط' },

    // Child Categories - Financial
    'Accountant': { [LanguageCode.EN]: 'Accountant', [LanguageCode.FR]: 'Comptable', [LanguageCode.AR]: 'محاسب' },
    'Financial Advisor': { [LanguageCode.EN]: 'Financial Advisor', [LanguageCode.FR]: 'Conseiller financier', [LanguageCode.AR]: 'مستشار مالي' },
    'Insurance Agent': { [LanguageCode.EN]: 'Insurance Agent', [LanguageCode.FR]: 'Agent d\'assurance', [LanguageCode.AR]: 'وكيل تأمين' },
    'Bookkeeper': { [LanguageCode.EN]: 'Bookkeeper', [LanguageCode.FR]: 'Aide-comptable', [LanguageCode.AR]: 'مسك الدفاتر' },

    // Child Categories - Automotive
    'Mechanic': { [LanguageCode.EN]: 'Mechanic', [LanguageCode.FR]: 'Mécanicien', [LanguageCode.AR]: 'ميكانيكي' },
    'Car Wash': { [LanguageCode.EN]: 'Car Wash', [LanguageCode.FR]: 'Lavage auto', [LanguageCode.AR]: 'غسيل سيارات' },
    'Tire Shop': { [LanguageCode.EN]: 'Tire Shop', [LanguageCode.FR]: 'Magasin de pneus', [LanguageCode.AR]: 'متجر إطارات' },
    'Body Shop': { [LanguageCode.EN]: 'Body Shop', [LanguageCode.FR]: 'Carrosserie', [LanguageCode.AR]: 'ورشة هياكل سيارات' },

    // Child Categories - Home Services
    'Plumber': { [LanguageCode.EN]: 'Plumber', [LanguageCode.FR]: 'Plombier', [LanguageCode.AR]: 'سباك' },
    'Electrician': { [LanguageCode.EN]: 'Electrician', [LanguageCode.FR]: 'Électricien', [LanguageCode.AR]: 'كهربائي' },
    'Landscaper': { [LanguageCode.EN]: 'Landscaper', [LanguageCode.FR]: 'Paysagiste', [LanguageCode.AR]: 'منسق حدائق' },
    'HVAC Technician': { [LanguageCode.EN]: 'HVAC Technician', [LanguageCode.FR]: 'Technicien CVC', [LanguageCode.AR]: 'فني تدفئة وتهوية وتكييف' },
    'Painter': { [LanguageCode.EN]: 'Painter', [LanguageCode.FR]: 'Peintre', [LanguageCode.AR]: 'دهان' },
    'Cleaning Service': { [LanguageCode.EN]: 'Cleaning Service', [LanguageCode.FR]: 'Service de nettoyage', [LanguageCode.AR]: 'خدمة تنظيف' },

    // Child Categories - Education
    'Tutor': { [LanguageCode.EN]: 'Tutor', [LanguageCode.FR]: 'Tuteur', [LanguageCode.AR]: 'مدرس خصوصي' },
    'Music Teacher': { [LanguageCode.EN]: 'Music Teacher', [LanguageCode.FR]: 'Professeur de musique', [LanguageCode.AR]: 'معلم موسيقى' },
    'Driving School': { [LanguageCode.EN]: 'Driving School', [LanguageCode.FR]: 'Auto-école', [LanguageCode.AR]: 'مدرسة تعليم قيادة' },
    'Language School': { [LanguageCode.EN]: 'Language School', [LanguageCode.FR]: 'École de langues', [LanguageCode.AR]: 'مدرسة لغات' },

    // Child Categories - Beauty & Wellness
    'Hair Salon': { [LanguageCode.EN]: 'Hair Salon', [LanguageCode.FR]: 'Salon de coiffure', [LanguageCode.AR]: 'صالون تجميل' },
    'Barber Shop': { [LanguageCode.EN]: 'Barber Shop', [LanguageCode.FR]: 'Barbier', [LanguageCode.AR]: 'محل حلاقة' },
    'Nail Salon': { [LanguageCode.EN]: 'Nail Salon', [LanguageCode.FR]: 'Salon de manucure', [LanguageCode.AR]: 'صالون أظافر' },
    'Spa': { [LanguageCode.EN]: 'Spa', [LanguageCode.FR]: 'Spa', [LanguageCode.AR]: 'منتجع صحي' },
    'Massage Therapist': { [LanguageCode.EN]: 'Massage Therapist', [LanguageCode.FR]: 'Massothérapeute', [LanguageCode.AR]: 'معالج بالتدليك' },
    'Gym': { [LanguageCode.EN]: 'Gym', [LanguageCode.FR]: 'Salle de sport', [LanguageCode.AR]: 'صالة رياضية' },

    // Child Categories - Professional Services
    'Consultant': { [LanguageCode.EN]: 'Consultant', [LanguageCode.FR]: 'Consultant', [LanguageCode.AR]: 'مستشار' },
    'Marketing Agency': { [LanguageCode.EN]: 'Marketing Agency', [LanguageCode.FR]: 'Agence de marketing', [LanguageCode.AR]: 'وكالة تسويق' },
    'Web Developer': { [LanguageCode.EN]: 'Web Developer', [LanguageCode.FR]: 'Développeur web', [LanguageCode.AR]: 'مطور ويب' },
    'Graphic Designer': { [LanguageCode.EN]: 'Graphic Designer', [LanguageCode.FR]: 'Graphiste', [LanguageCode.AR]: 'مصمم جرافيك' },
    'Photographer': { [LanguageCode.EN]: 'Photographer', [LanguageCode.FR]: 'Photographe', [LanguageCode.AR]: 'مصور فوتوغرافي' },

    // Child Categories - Food & Dining
    'Restaurant': { [LanguageCode.EN]: 'Restaurant', [LanguageCode.FR]: 'Restaurant', [LanguageCode.AR]: 'مطعم' },
    'Cafe': { [LanguageCode.EN]: 'Cafe', [LanguageCode.FR]: 'Café', [LanguageCode.AR]: 'مقهى' },
    'Bakery': { [LanguageCode.EN]: 'Bakery', [LanguageCode.FR]: 'Boulangerie', [LanguageCode.AR]: 'مخبز' },
    'Catering': { [LanguageCode.EN]: 'Catering', [LanguageCode.FR]: 'Traiteur', [LanguageCode.AR]: 'خدمات تموين' },

    // Child Categories - Retail
    'Boutique': { [LanguageCode.EN]: 'Boutique', [LanguageCode.FR]: 'Boutique', [LanguageCode.AR]: 'بوتيك' },
    'Bookstore': { [LanguageCode.EN]: 'Bookstore', [LanguageCode.FR]: 'Librairie', [LanguageCode.AR]: 'مكتبة' },
    'Electronics Store': { [LanguageCode.EN]: 'Electronics Store', [LanguageCode.FR]: 'Magasin d\'électronique', [LanguageCode.AR]: 'متجر إلكترونيات' },
    'Grocery Store': { [LanguageCode.EN]: 'Grocery Store', [LanguageCode.FR]: 'Épicerie', [LanguageCode.AR]: 'متجر بقالة' },

    // Child Categories - Government & Public Services
    'Post Office': { [LanguageCode.EN]: 'Post Office', [LanguageCode.FR]: 'Bureau de poste', [LanguageCode.AR]: 'مكتب بريد' },
    'Library': { [LanguageCode.EN]: 'Library', [LanguageCode.FR]: 'Bibliothèque', [LanguageCode.AR]: 'مكتبة عامة' },
    'DMV/Registry': { [LanguageCode.EN]: 'DMV/Registry', [LanguageCode.FR]: 'Service des immatriculations', [LanguageCode.AR]: 'إدارة المرور/السجل' }, // DMV is US specific, generic FR/AR

    // Child Categories - Arts & Entertainment
    'Art Gallery': { [LanguageCode.EN]: 'Art Gallery', [LanguageCode.FR]: 'Galerie d\'art', [LanguageCode.AR]: 'معرض فني' },
    'Museum': { [LanguageCode.EN]: 'Museum', [LanguageCode.FR]: 'Musée', [LanguageCode.AR]: 'متحف' },
    'Cinema': { [LanguageCode.EN]: 'Cinema', [LanguageCode.FR]: 'Cinéma', [LanguageCode.AR]: 'سينما' },
    'Theater': { [LanguageCode.EN]: 'Theater', [LanguageCode.FR]: 'Théâtre', [LanguageCode.AR]: 'مسرح' },

    // Child Categories - Sports & Recreation
    'Sports Club': { [LanguageCode.EN]: 'Sports Club', [LanguageCode.FR]: 'Club de sport', [LanguageCode.AR]: 'نادي رياضي' },
    'Yoga Studio': { [LanguageCode.EN]: 'Yoga Studio', [LanguageCode.FR]: 'Studio de yoga', [LanguageCode.AR]: 'استوديو يوجا' },
    'Golf Course': { [LanguageCode.EN]: 'Golf Course', [LanguageCode.FR]: 'Terrain de golf', [LanguageCode.AR]: 'ملعب جولف' },
    'Personal Trainer': { [LanguageCode.EN]: 'Personal Trainer', [LanguageCode.FR]: 'Entraîneur personnel', [LanguageCode.AR]: 'مدرب شخصي' },

    // Child Categories - Pet Services
    'Veterinarian': { [LanguageCode.EN]: 'Veterinarian', [LanguageCode.FR]: 'Vétérinaire', [LanguageCode.AR]: 'طبيب بيطري' },
    'Pet Groomer': { [LanguageCode.EN]: 'Pet Groomer', [LanguageCode.FR]: 'Toiletteur pour animaux', [LanguageCode.AR]: 'مربي حيوانات أليفة' },
    'Dog Walker': { [LanguageCode.EN]: 'Dog Walker', [LanguageCode.FR]: 'Promeneur de chiens', [LanguageCode.AR]: 'ممشى كلاب' },
    'Pet Sitter': { [LanguageCode.EN]: 'Pet Sitter', [LanguageCode.FR]: 'Gardien d\'animaux', [LanguageCode.AR]: 'جليس حيوانات أليفة' },

    // Child Categories - Trades & Construction
    'Carpenter': { [LanguageCode.EN]: 'Carpenter', [LanguageCode.FR]: 'Charpentier', [LanguageCode.AR]: 'نجار' },
    'Roofer': { [LanguageCode.EN]: 'Roofer', [LanguageCode.FR]: 'Couvreur', [LanguageCode.AR]: 'عامل أسقف' },
    'Welder': { [LanguageCode.EN]: 'Welder', [LanguageCode.FR]: 'Soudeur', [LanguageCode.AR]: 'لحام' },
    'General Contractor': { [LanguageCode.EN]: 'General Contractor', [LanguageCode.FR]: 'Entrepreneur général', [LanguageCode.AR]: 'مقاول عام' },
  };

  const addTranslations = async (categoryId: number, title: string) => {
    for (const lang of languagesToSeed) {
      let translatedText = title; // Default to original title if no translation found

      if (exampleTranslations[title] && exampleTranslations[title][lang]) {
        translatedText = exampleTranslations[title][lang];
      } else {
        // This fallback logic can be enhanced if needed, but with the comprehensive map, it should rarely be hit.
        console.warn(`No specific translation found for title "${title}" in language "${lang}". Using default or English title.`);
        if (lang === LanguageCode.FR) translatedText = `${title} (FR)`; // Simple placeholder if truly missing
        if (lang === LanguageCode.AR) translatedText = `${title} (AR)`; // Simple placeholder if truly missing
      }

      await prismaClient.translation.upsert({
        where: {
          unique_translation_entry: {
            tableName: 'ProviderCategory',
            refId: String(categoryId),
            refField: 'title',
            languageCode: lang,
          },
        },
        update: { text: translatedText },
        create: {
          tableName: 'ProviderCategory',
          refId: String(categoryId),
          refField: 'title',
          languageCode: lang,
          text: translatedText,
        },
      });
    }
  };

  const parentCategoriesData = [
    { title: 'Healthcare' }, { title: 'Legal' }, { title: 'Financial' },
    { title: 'Automotive' }, { title: 'Home Services' },
    { title: 'Education' }, { title: 'Beauty & Wellness' }, { title: 'Professional Services' },
    { title: 'Food & Dining' }, { title: 'Retail' }, { title: 'Government & Public Services' },
    { title: 'Arts & Entertainment' }, { title: 'Sports & Recreation' }, { title: 'Pet Services' },
    { title: 'Trades & Construction' }
  ];
  const createdParentCategories: { [key: string]: { id: number } } = {};
  for (const categoryData of parentCategoriesData) {
    const category = await prismaClient.providerCategory.upsert({
      where: { title: categoryData.title }, update: {}, create: categoryData,
      select: { id: true, title: true },
    });
    createdParentCategories[category.title] = { id: category.id };
    await addTranslations(category.id, category.title); // Add translations for parent
  }

  const childCategoriesData = [
    // Healthcare
    { title: 'Doctor', parentTitle: 'Healthcare' }, { title: 'Dentist', parentTitle: 'Healthcare' },
    { title: 'Physiotherapist', parentTitle: 'Healthcare' }, { title: 'Optometrist', parentTitle: 'Healthcare' },
    { title: 'Chiropractor', parentTitle: 'Healthcare' }, { title: 'Pharmacy', parentTitle: 'Healthcare' },
    // Legal
    { title: 'Lawyer', parentTitle: 'Legal' }, { title: 'Notary', parentTitle: 'Legal' },
    { title: 'Paralegal', parentTitle: 'Legal' }, { title: 'Mediator', parentTitle: 'Legal' },
    // Financial
    { title: 'Accountant', parentTitle: 'Financial' }, { title: 'Financial Advisor', parentTitle: 'Financial' },
    { title: 'Insurance Agent', parentTitle: 'Financial' }, { title: 'Bookkeeper', parentTitle: 'Financial' },
    // Automotive
    { title: 'Mechanic', parentTitle: 'Automotive' }, { title: 'Car Wash', parentTitle: 'Automotive' },
    { title: 'Tire Shop', parentTitle: 'Automotive' }, { title: 'Body Shop', parentTitle: 'Automotive' },
    // Home Services
    { title: 'Plumber', parentTitle: 'Home Services' }, { title: 'Electrician', parentTitle: 'Home Services' },
    { title: 'Landscaper', parentTitle: 'Home Services' }, { title: 'HVAC Technician', parentTitle: 'Home Services' },
    { title: 'Painter', parentTitle: 'Home Services' }, { title: 'Cleaning Service', parentTitle: 'Home Services' },
    // Education
    { title: 'Tutor', parentTitle: 'Education' }, { title: 'Music Teacher', parentTitle: 'Education' },
    { title: 'Driving School', parentTitle: 'Education' }, { title: 'Language School', parentTitle: 'Education' },
    // Beauty & Wellness
    { title: 'Hair Salon', parentTitle: 'Beauty & Wellness' }, { title: 'Barber Shop', parentTitle: 'Beauty & Wellness' },
    { title: 'Nail Salon', parentTitle: 'Beauty & Wellness' }, { title: 'Spa', parentTitle: 'Beauty & Wellness' },
    { title: 'Massage Therapist', parentTitle: 'Beauty & Wellness' }, { title: 'Gym', parentTitle: 'Beauty & Wellness' },
    // Professional Services
    { title: 'Consultant', parentTitle: 'Professional Services' }, { title: 'Marketing Agency', parentTitle: 'Professional Services' },
    { title: 'Web Developer', parentTitle: 'Professional Services' }, { title: 'Graphic Designer', parentTitle: 'Professional Services' },
    { title: 'Photographer', parentTitle: 'Professional Services' },
    // Food & Dining
    { title: 'Restaurant', parentTitle: 'Food & Dining' }, { title: 'Cafe', parentTitle: 'Food & Dining' },
    { title: 'Bakery', parentTitle: 'Food & Dining' }, { title: 'Catering', parentTitle: 'Food & Dining' },
    // Retail
    { title: 'Boutique', parentTitle: 'Retail' }, { title: 'Bookstore', parentTitle: 'Retail' },
    { title: 'Electronics Store', parentTitle: 'Retail' }, { title: 'Grocery Store', parentTitle: 'Retail' },
    // Government & Public Services
    { title: 'Post Office', parentTitle: 'Government & Public Services' }, { title: 'Library', parentTitle: 'Government & Public Services' },
    { title: 'DMV/Registry', parentTitle: 'Government & Public Services' },
    // Arts & Entertainment
    { title: 'Art Gallery', parentTitle: 'Arts & Entertainment' }, { title: 'Museum', parentTitle: 'Arts & Entertainment' },
    { title: 'Cinema', parentTitle: 'Arts & Entertainment' }, { title: 'Theater', parentTitle: 'Arts & Entertainment' },
    // Sports & Recreation
    { title: 'Sports Club', parentTitle: 'Sports & Recreation' }, { title: 'Yoga Studio', parentTitle: 'Sports & Recreation' },
    { title: 'Golf Course', parentTitle: 'Sports & Recreation' }, { title: 'Personal Trainer', parentTitle: 'Sports & Recreation' },
    // Pet Services
    { title: 'Veterinarian', parentTitle: 'Pet Services' }, { title: 'Pet Groomer', parentTitle: 'Pet Services' },
    { title: 'Dog Walker', parentTitle: 'Pet Services' }, { title: 'Pet Sitter', parentTitle: 'Pet Services' },
    // Trades & Construction
    { title: 'Carpenter', parentTitle: 'Trades & Construction' }, { title: 'Roofer', parentTitle: 'Trades & Construction' },
    { title: 'Welder', parentTitle: 'Trades & Construction' }, { title: 'General Contractor', parentTitle: 'Trades & Construction' },
  ];
  for (const childData of childCategoriesData) {
    const parent = createdParentCategories[childData.parentTitle];
    if (parent) {
      const childCategory = await prismaClient.providerCategory.upsert({ // Store the result
        where: { title: childData.title }, update: { parentId: parent.id },
        create: { title: childData.title, parentId: parent.id },
        select: { id: true, title: true }, // Select id and title
      });
      await addTranslations(childCategory.id, childCategory.title); // Add translations for child
    } else {
       console.warn(`Parent category "${childData.parentTitle}" not found for child "${childData.title}". Skipping.`);
    }
  }
  console.log('Provider categories and their translations seeded.');
  // --- End Provider Categories ---
}
export async function seedAdditionalAppointmentsForCustomers(prismaClient: PrismaClient) {
  console.log('Starting to seed additional appointments for existing customers...');
  const providerEmail = '<EMAIL>';

  // 1. Fetch Provider and related entities
  const providerUser = await prismaClient.user.findUnique({
      where: { email: providerEmail },
      include: {
          serviceProvider: {
              include: {
                  providingPlaces: {
                      include: {
                          queues: {
                              include: {
                                  services: true // Assuming services are linked to queues, or fetch services separately
                              }
                          }
                      }
                  },
                  services: true // Fallback if services not directly on queue
              }
          }
      }
  });

  if (!providerUser || !providerUser.serviceProvider) {
      console.error(`Provider with email ${providerEmail} or their service provider record not found. Skipping additional appointments.`);
      return;
  }
  const sprovider = providerUser.serviceProvider;

  const place = sprovider.providingPlaces?.[0];
  if (!place) {
      console.error(`No providing place found for provider ${sprovider.id}. Skipping additional appointments.`);
      return;
  }

  // Try to get a service from the first queue, or fallback to first service of provider
  let serviceToUse = place.queues?.[0]?.services?.[0];
  if (!serviceToUse) {
      serviceToUse = sprovider.services?.[0];
  }
  if (!serviceToUse) {
      console.error(`No service found for provider ${sprovider.id} (either in queue or directly). Skipping additional appointments.`);
      return;
  }
  const serviceDuration = serviceToUse.duration;


  const queueToUse = place.queues?.[0];
  if (!queueToUse) {
      console.error(`No queue found for place ${place.id}. Skipping additional appointments.`);
      return;
  }
  console.log(`Using Provider: ${sprovider.id}, Place: ${place.id}, Service: ${serviceToUse.id} (Duration: ${serviceDuration}min), Queue: ${queueToUse.id}`);


  // 2. Fetch existing CUSTOMER users (excluding the provider)
  const customerUsers = await prismaClient.user.findMany({
      where: {
          role: Role.CUSTOMER,
          NOT: { email: providerEmail }
      }
  });

  if (customerUsers.length === 0) {
      console.log('No existing customer users found to seed additional appointments for.');
      return;
  }
  console.log(`Found ${customerUsers.length} customer users to process.`);

  // 3. Define a start date for these new appointments (e.g., next Monday 10 AM)
  let appointmentStartDay = dayjs().add(1, 'week').startOf('week').add(1, 'day'); // Next Monday
  
  for (const customerUser of customerUsers) {
      console.log(`  Processing customer: ${customerUser.email}`);
      // Upsert CustomerFolder
      const customerFolder = await prismaClient.customerFolder.upsert({
          where: { sProviderId_userId: { sProviderId: sprovider.id, userId: customerUser.id } },
          update: { isActive: true },
          create: {
              userId: customerUser.id,
              sProviderId: sprovider.id,
              notes: `Folder created by seedAdditionalAppointments`
          },
      });

      // Schedule one appointment for this customer on the current `appointmentStartDay` at 10:00 AM
      const appointmentDateTime = appointmentStartDay.hour(10).minute(0).second(0).millisecond(0).toDate();
      const appointmentEndTime = dayjs(appointmentDateTime).add(serviceDuration, 'minutes').toDate();

      try {
          await prismaClient.appointment.create({
              data: {
                  customerFolderId: customerFolder.id,
                  placeId: place.id,
                  serviceId: serviceToUse.id,
                  queueId: queueToUse.id,
                  status: 'confirmed', 
                  typeEvent: 'clinic_additional_seed',
                  expectedAppointmentStartTime: appointmentDateTime,
                  expectedAppointmentEndTime: appointmentEndTime,
                  serviceDuration: serviceDuration,
                  notes: `Additional seeded appointment for ${customerUser.email} on ${dayjs(appointmentDateTime).format('YYYY-MM-DD HH:mm')}`,
              },
          });
          console.log(`    Created additional appointment for ${customerUser.email} at ${dayjs(appointmentDateTime).format('YYYY-MM-DD HH:mm')}`);
      } catch (e: any) {
          // This might happen if there's an unforeseen conflict (e.g. unique constraint on time for a queue if not handled)
          // For a seed script, logging is often sufficient.
          console.error(`    Failed to create additional appointment for ${customerUser.email}: ${e.message}`);
      }

      // Move to the next day for the next customer's appointment to avoid overlaps simply
      appointmentStartDay = appointmentStartDay.add(1, 'day');
  }

  console.log('Finished seeding additional appointments for existing customers.');
}
function generateMockUsersData(numOfUsers: number): MockUserData[] {
  return faker.helpers.multiple(generateMockUserData, { count: numOfUsers });
}

function generateMockUserData(): MockUserData {
  const firstName = faker.person.firstName();
  const lastName = faker.person.lastName();
  const subscriptionStatus = faker.helpers.arrayElement<SubscriptionStatus | null>([
    ...Object.values(SubscriptionStatus),
    null,
  ]);
  const now = new Date();
  const createdAt = faker.date.past({ refDate: now });
  const timePaid = faker.date.between({ from: createdAt, to: now });
  const credits = subscriptionStatus ? 0 : faker.number.int({ min: 0, max: 10 });
  const queues = 1;
  const hasUserPaidOnStripe = !!subscriptionStatus || credits > 3;
  return {
    email: faker.internet.email({ firstName, lastName }),
    username: faker.internet.userName({ firstName, lastName }),
    preferedLanguage: faker.helpers.arrayElement<LanguageCode>([LanguageCode.EN, LanguageCode.AR]),
    createdAt,
    isAdmin: false,
    firstName,
    lastName,
    phoneOtp: null,
    phoneOtpExpiresAt: null,
    isPhoneVerified: false,
    lastOtpSentAt: null,
    emailOtp: null,
    emailOtpExpiresAt: null,
    lastEmailOtpSentAt: null,
    isEmailVerified: false,
    passwordResetToken: null,
    passwordResetTokenExpiresAt: null,
    mobileNumber: faker.phone.number(),
    nationalId: faker.string.uuid(), 
    profilePictureId: null,
    credits,
    queues,
    role: faker.helpers.arrayElement<Role>([Role.CUSTOMER, Role.CLIENT]),
    subscriptionStatus,
    lemonSqueezyCustomerPortalUrl: null,
    paymentProcessorUserId: hasUserPaidOnStripe ? `cus_test_${faker.string.uuid()}` : null,
    datePaid: hasUserPaidOnStripe ? faker.date.between({ from: createdAt, to: timePaid }) : null,
    subscriptionPlan: subscriptionStatus ? faker.helpers.arrayElement(getSubscriptionPaymentPlanIds()) : null,
    // Chargily fields
    chargilyCustomerId: null,
    chargilyPaymentMethod: null,
    chargilyWebhookData: null,
  };
}

// === ADMIN USER SEEDING ===
export async function seedAdminUser(prismaClient: PrismaClient) {
  console.log('Seeding admin user...');

  const adminEmail = '<EMAIL>';
  const adminPassword = 'Dadanasro07';

  try {
    // Check if admin already exists
    const existingAdmin = await prismaClient.user.findUnique({
      where: { email: adminEmail }
    });

    if (existingAdmin) {
      console.log(`Admin user with email ${adminEmail} already exists. Skipping creation.`);
      return;
    }

    // Use Wasp's authentication functions for proper setup
    const providerId = createProviderId('email', adminEmail);
    const providerData = await sanitizeAndSerializeProviderData({ hashedPassword: adminPassword });

    // Create the admin user using Wasp's createUser function
    const adminUser = await createUser(
      providerId,
      providerData,
      {
        email: adminEmail,
        username: 'admin',
        firstName: 'Admin',
        lastName: 'User',
        isAdmin: true,
        role: Role.ADMIN,
        isEmailVerified: true,
        credits: 100, // Give admin plenty of credits
        queues: 10,
        preferedLanguage: 'EN'
      }
    );

    console.log(`Created admin user with ID: ${adminUser.id}`);

    // Mark email as verified in AuthIdentity
    const authRecord = await prismaClient.auth.findUnique({
      where: { userId: adminUser.id },
      include: { identities: true }
    });

    if (authRecord && authRecord.identities.length > 0) {
      const emailIdentity = authRecord.identities.find(identity => identity.providerName === 'email');
      if (emailIdentity) {
        // Update the provider data to mark email as verified
        const currentProviderData = JSON.parse(emailIdentity.providerData);
        const updatedProviderData = {
          ...currentProviderData,
          isEmailVerified: true,
          emailVerificationSentAt: null
        };

        await prismaClient.authIdentity.update({
          where: {
            providerName_providerUserId: {
              providerName: 'email',
              providerUserId: adminEmail
            }
          },
          data: {
            providerData: JSON.stringify(updatedProviderData)
          }
        });

        console.log(`Marked admin email as verified in AuthIdentity`);
      }
    }
    console.log(`Admin credentials: ${adminEmail} / ${adminPassword}`);

  } catch (error) {
    console.error('Error creating admin user:', error);
    throw error;
  }
}
