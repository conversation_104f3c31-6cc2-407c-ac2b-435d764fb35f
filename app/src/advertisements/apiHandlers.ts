import { type Request, type Response } from 'express';
import { HttpError } from 'wasp/server';
import { type Advertisement } from 'wasp/entities';

type Context = {
  entities: {
    Advertisement: any;
  };
};

/**
 * GET /api/public/advertisements
 * Get all active advertisements for public consumption
 */
export const handleGetPublicAdvertisements = async (
  req: Request,
  res: Response,
  context: Context
) => {
  try {
    const page = req.query.page ? parseInt(req.query.page as string) : 1;
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 10;
    
    // Validate pagination parameters
    if (page < 1 || limit < 1 || limit > 50) {
      return res.status(400).json({ 
        success: false,
        message: 'Invalid pagination parameters. Page must be >= 1, limit must be between 1 and 50.' 
      });
    }

    const skip = (page - 1) * limit;

    // Get only active advertisements
    const [advertisements, totalCount] = await Promise.all([
      context.entities.Advertisement.findMany({
        where: {
          isActive: true,
        },
        include: {
          backgroundImage: {
            select: {
              id: true,
              name: true,
              type: true,
              uploadUrl: true,
            }
          },
          pngImage: {
            select: {
              id: true,
              name: true,
              type: true,
              uploadUrl: true,
            }
          },
        },
        orderBy: [
          { sortOrder: 'asc' },
          { createdAt: 'desc' }
        ],
        skip,
        take: limit,
      }),
      context.entities.Advertisement.count({
        where: {
          isActive: true,
        },
      }),
    ]);

    // Transform the data to include proper image URLs and clean structure
    const transformedAdvertisements = advertisements.map((ad: any) => ({
      id: ad.id,
      title: ad.title,
      subtitle: ad.subtitle,
      description: ad.description,
      callToActionText: ad.callToActionText,
      callToActionLink: ad.callToActionLink,
      isExternal: ad.isExternal,
      sortOrder: ad.sortOrder,
      createdAt: ad.createdAt,
      backgroundImage: ad.backgroundImage ? {
        id: ad.backgroundImage.id,
        name: ad.backgroundImage.name,
        type: ad.backgroundImage.type,
        url: ad.backgroundImage.uploadUrl,
      } : null,
      pngImage: ad.pngImage ? {
        id: ad.pngImage.id,
        name: ad.pngImage.name,
        type: ad.pngImage.type,
        url: ad.pngImage.uploadUrl,
      } : null,
    }));

    res.status(200).json({
      success: true,
      data: {
        advertisements: transformedAdvertisements,
        pagination: {
          page,
          limit,
          totalCount,
          totalPages: Math.ceil(totalCount / limit),
          hasNextPage: page < Math.ceil(totalCount / limit),
          hasPreviousPage: page > 1,
        }
      }
    });

  } catch (error: any) {
    console.error('Get public advertisements failed:', error);
    
    if (error instanceof HttpError) {
      res.status(error.statusCode).json({ 
        success: false,
        message: error.message 
      });
    } else {
      res.status(500).json({ 
        success: false,
        message: 'Internal server error' 
      });
    }
  }
};
