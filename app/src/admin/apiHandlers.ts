import { HttpError, prisma } from 'wasp/server';
import { type AuthUser } from 'wasp/auth';
import { z } from 'zod';
import { ensureArgsSchemaOrThrowHttpError } from '../server/validation';
import { verifyPassword } from 'wasp/auth/password';
import { createInvalidCredentialsError } from 'wasp/auth/utils';
import { createSession } from 'wasp/auth/session';
import { getProviderDataWithPassword } from 'wasp/server/auth';
import { Role } from '@prisma/client';
import { createFile } from 'wasp/server/operations';
import {
  GetAdvertisementsQuerySchema,
  CreateAdvertisementSchema,
  UpdateAdvertisementSchema,
  DeleteAdvertisementSchema,
  AdvertisementIdSchema,
  UploadImageSchema,
} from './validation';

// Admin login handler
const AdminLoginInputSchema = z.object({
    email: z.string().email(),
    password: z.string().min(1),
});

export const handleAdminLogin = async (req: any, res: any, context: any) => {
    try {
        const args = ensureArgsSchemaOrThrowHttpError(AdminLoginInputSchema, req.body);
        
        // Find user by email
        const user = await context.entities.User.findUnique({
            where: { 
                email: args.email,
                role: Role.ADMIN // Only allow admin users
            },
            include: {
                auth: {
                    include: {
                        identities: true
                    }
                }
            }
        });
        
        if (!user) {
            throw createInvalidCredentialsError();
        }
        
        // Check if user is admin
        if (user.role !== Role.ADMIN) {
            throw new HttpError(403, 'Admin access required');
        }
        
        // Get provider data for password verification
        const identity = user.auth.identities.find((i: any) => i.providerName === 'email');
        if (!identity) {
            throw createInvalidCredentialsError();
        }
        
        const providerData = await getProviderDataWithPassword(identity.providerData);
        
        // Verify password - ensure we have EmailProviderData
        if (!providerData || !('hashedPassword' in providerData)) {
            throw createInvalidCredentialsError();
        }
        
        try {
            await verifyPassword(providerData.hashedPassword, args.password);
        } catch (e) {
            throw createInvalidCredentialsError();
        }
        
        // Create session
        const session = await createSession(user.auth.id);
        
        res.status(200).json({
            sessionId: session.id,
            user: {
                id: user.id,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                role: user.role,
                isAdmin: user.isAdmin,
            }
        });
        
    } catch (error: any) {
        console.error('Admin login failed:', error);
        if (error instanceof HttpError) {
            res.status(error.statusCode || 500).json({
                error: error.message
            });
        } else {
            res.status(500).json({
                error: 'Login failed'
            });
        }
    }
};

// Providers list handler
export const handleGetAdminProviders = async (req: any, res: any, context: any) => {
    try {
        // Ensure admin access
        if (!context.user || context.user.role !== Role.ADMIN) {
            throw new HttpError(403, 'Admin access required');
        }
        
        const page = parseInt(req.query.page) || 1;
        const limit = Math.min(parseInt(req.query.limit) || 20, 100);
        const isVerified = req.query.isVerified !== undefined ? req.query.isVerified === 'true' : undefined;
        const search = req.query.search || '';
        
        const skip = (page - 1) * limit;
        
        const whereClause: any = {};
        
        if (isVerified !== undefined) {
            whereClause.isVerified = isVerified;
        }
        
        if (search) {
            whereClause.OR = [
                { title: { contains: search, mode: 'insensitive' } },
                { user: { firstName: { contains: search, mode: 'insensitive' } } },
                { user: { lastName: { contains: search, mode: 'insensitive' } } },
                { user: { email: { contains: search, mode: 'insensitive' } } },
            ];
        }
        
        const [providers, totalCount] = await Promise.all([
            context.entities.SProvider.findMany({
                where: whereClause,
                include: {
                    user: {
                        select: {
                            id: true,
                            firstName: true,
                            lastName: true,
                            email: true,
                            mobileNumber: true,
                            createdAt: true,
                            isPhoneVerified: true,
                            isEmailVerified: true,
                        }
                    },
                    category: {
                        select: {
                            id: true,
                            title: true,
                        }
                    },
                    _count: {
                        select: {
                            services: true,
                            customerFolders: true,
                            reviewsReceived: true,
                        }
                    }
                },
                skip,
                take: limit,
                orderBy: { createdAt: 'desc' },
            }),
            context.entities.SProvider.count({ where: whereClause })
        ]);
        
        res.status(200).json({
            providers,
            pagination: {
                page,
                limit,
                totalCount,
                totalPages: Math.ceil(totalCount / limit),
            }
        });
        
    } catch (error: any) {
        console.error('Get admin providers failed:', error);
        if (error instanceof HttpError) {
            res.status(error.statusCode || 500).json({
                error: error.message
            });
        } else {
            res.status(500).json({
                error: 'Failed to retrieve providers'
            });
        }
    }
};

// Customers list handler
export const handleGetAdminCustomers = async (req: any, res: any, context: any) => {
    try {
        // Ensure admin access
        if (!context.user || context.user.role !== Role.ADMIN) {
            throw new HttpError(403, 'Admin access required');
        }
        
        const page = parseInt(req.query.page) || 1;
        const limit = Math.min(parseInt(req.query.limit) || 20, 100);
        const search = req.query.search || '';
        
        const skip = (page - 1) * limit;
        
        const whereClause: any = {
            OR: [
                { role: 'CUSTOMER' },
                { customerFolders: { some: {} } }, // Users who have customer folders
            ]
        };
        
        if (search) {
            whereClause.AND = [
                whereClause,
                {
                    OR: [
                        { firstName: { contains: search, mode: 'insensitive' } },
                        { lastName: { contains: search, mode: 'insensitive' } },
                        { email: { contains: search, mode: 'insensitive' } },
                        { mobileNumber: { contains: search, mode: 'insensitive' } },
                    ]
                }
            ];
        }
        
        const [customers, totalCount] = await Promise.all([
            context.entities.User.findMany({
                where: whereClause,
                select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                    mobileNumber: true,
                    createdAt: true,
                    isPhoneVerified: true,
                    isEmailVerified: true,
                    role: true,
                    _count: {
                        select: {
                            customerFolders: true,
                        }
                    }
                },
                skip,
                take: limit,
                orderBy: { createdAt: 'desc' },
            }),
            context.entities.User.count({ where: whereClause })
        ]);
        
        res.status(200).json({
            customers,
            pagination: {
                page,
                limit,
                totalCount,
                totalPages: Math.ceil(totalCount / limit),
            }
        });
        
    } catch (error: any) {
        console.error('Get admin customers failed:', error);
        if (error instanceof HttpError) {
            res.status(error.statusCode || 500).json({
                error: error.message
            });
        } else {
            res.status(500).json({
                error: 'Failed to retrieve customers'
            });
        }
    }
};

// Get single provider handler
export const handleGetAdminProvider = async (req: any, res: any, context: any) => {
    try {
        // Ensure admin access
        if (!context.user || context.user.role !== Role.ADMIN) {
            throw new HttpError(403, 'Admin access required');
        }

        const providerId = parseInt(req.params.providerId);
        if (!providerId) {
            throw new HttpError(400, 'Invalid provider ID');
        }

        const provider = await context.entities.SProvider.findUnique({
            where: { id: providerId },
            include: {
                user: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        mobileNumber: true,
                        createdAt: true,
                        isPhoneVerified: true,
                        isEmailVerified: true,
                    }
                },
                category: {
                    select: {
                        id: true,
                        title: true,
                    }
                },
                services: {
                    select: {
                        id: true,
                        title: true,
                        description: true,
                        price: true,
                        duration: true,
                        acceptOnline: true,
                        acceptNew: true,
                        notificationOn: true,
                        pointsRequirements: true,
                        isPublic: true,
                        deliveryType: true,
                        color: true,
                    }
                },
                queues: {
                    select: {
                        id: true,
                        title: true,
                        isActive: true,
                        sProvidingPlaceId: true,
                    }
                },
                _count: {
                    select: {
                        services: true,
                        customerFolders: true,
                        reviewsReceived: true,
                        queues: true,
                    }
                }
            }
        });

        if (!provider) {
            throw new HttpError(404, 'Provider not found');
        }

        res.status(200).json({
            success: true,
            data: provider,
            message: 'Provider retrieved successfully'
        });

    } catch (error: any) {
        console.error('Get admin provider failed:', error);
        if (error instanceof HttpError) {
            res.status(error.statusCode || 500).json({
                error: error.message
            });
        } else {
            res.status(500).json({
                error: 'Failed to retrieve provider'
            });
        }
    }
};

// Get provider locations handler
export const handleGetAdminProviderLocations = async (req: any, res: any, context: any) => {
    try {
        // Ensure admin access
        if (!context.user || context.user.role !== Role.ADMIN) {
            throw new HttpError(403, 'Admin access required');
        }

        const providerId = parseInt(req.params.providerId);
        if (!providerId) {
            throw new HttpError(400, 'Invalid provider ID');
        }

        // Verify provider exists
        const provider = await context.entities.SProvider.findUnique({
            where: { id: providerId },
            select: { id: true }
        });

        if (!provider) {
            throw new HttpError(404, 'Provider not found');
        }

        const locations = await context.entities.SProvidingPlace.findMany({
            where: { sProviderId: providerId },
            include: {
                detailedAddress: {
                    select: {
                        id: true,
                        address: true,
                        city: true,
                        state: true,
                        postalCode: true,
                        country: true,
                        latitude: true,
                        longitude: true,
                        description: true,
                        isPrimary: true,
                    }
                },
                openings: {
                    include: {
                        hours: {
                            select: {
                                id: true,
                                timeFrom: true,
                                timeTo: true,
                            }
                        }
                    },
                    orderBy: {
                        dayOfWeek: 'asc'
                    }
                },
                queues: {
                    select: {
                        id: true,
                        title: true,
                        isActive: true,
                    }
                },
                _count: {
                    select: {
                        appointments: true,
                        queues: true,
                    }
                }
            },
            orderBy: { createdAt: 'asc' }
        });

        res.status(200).json({
            success: true,
            data: locations,
            message: 'Provider locations retrieved successfully'
        });

    } catch (error: any) {
        console.error('Get admin provider locations failed:', error);
        if (error instanceof HttpError) {
            res.status(error.statusCode || 500).json({
                error: error.message
            });
        } else {
            res.status(500).json({
                error: 'Failed to retrieve provider locations'
            });
        }
    }
};

// Get provider services handler
export const handleGetAdminProviderServices = async (req: any, res: any, context: any) => {
    try {
        // Ensure admin access
        if (!context.user || context.user.role !== Role.ADMIN) {
            throw new HttpError(403, 'Admin access required');
        }

        const providerId = parseInt(req.params.providerId);
        if (!providerId) {
            throw new HttpError(400, 'Invalid provider ID');
        }

        // Verify provider exists
        const provider = await context.entities.SProvider.findUnique({
            where: { id: providerId },
            select: { id: true }
        });

        if (!provider) {
            throw new HttpError(404, 'Provider not found');
        }

        const services = await context.entities.Service.findMany({
            where: { sProviderId: providerId },
            include: {
                category: {
                    select: {
                        id: true,
                        title: true,
                    }
                },
                queues: {
                    select: {
                        id: true,
                        title: true,
                        isActive: true,
                        sProvidingPlace: {
                            select: {
                                id: true,
                                name: true,
                            }
                        }
                    }
                },
                _count: {
                    select: {
                        appointments: true,
                        queues: true,
                    }
                }
            },
            orderBy: { createdAt: 'asc' }
        });

        res.status(200).json({
            success: true,
            data: services,
            message: 'Provider services retrieved successfully'
        });

    } catch (error: any) {
        console.error('Get admin provider services failed:', error);
        if (error instanceof HttpError) {
            res.status(error.statusCode || 500).json({
                error: error.message
            });
        } else {
            res.status(500).json({
                error: 'Failed to retrieve provider services'
            });
        }
    }
};

// Get provider stats handler
export const handleGetAdminProviderStats = async (req: any, res: any, context: any) => {
    try {
        // Ensure admin access
        if (!context.user || context.user.role !== Role.ADMIN) {
            throw new HttpError(403, 'Admin access required');
        }

        const providerId = parseInt(req.params.providerId);
        if (!providerId) {
            throw new HttpError(400, 'Invalid provider ID');
        }

        // Verify provider exists
        const provider = await context.entities.SProvider.findUnique({
            where: { id: providerId },
            select: {
                id: true,
                createdAt: true,
                averageRating: true,
                totalReviews: true,
                isVerified: true,
                isSetupComplete: true,
            }
        });

        if (!provider) {
            throw new HttpError(404, 'Provider not found');
        }

        // Get current date for time-based queries
        const now = new Date();
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const startOfWeek = new Date(now);
        startOfWeek.setDate(now.getDate() - now.getDay());
        const startOfToday = new Date(now.getFullYear(), now.getMonth(), now.getDate());

        // Get comprehensive stats
        const [
            totalAppointments,
            monthlyAppointments,
            weeklyAppointments,
            todayAppointments,
            totalCustomers,
            totalServices,
            totalLocations,
            totalQueues,
            appointmentsByStatus,
            recentAppointments,
            monthlyRevenueAppointments
        ] = await Promise.all([
            // Total appointments
            context.entities.Appointment.count({
                where: {
                    service: { sProviderId: providerId }
                }
            }),
            // Monthly appointments
            context.entities.Appointment.count({
                where: {
                    service: { sProviderId: providerId },
                    createdAt: { gte: startOfMonth }
                }
            }),
            // Weekly appointments
            context.entities.Appointment.count({
                where: {
                    service: { sProviderId: providerId },
                    createdAt: { gte: startOfWeek }
                }
            }),
            // Today's appointments
            context.entities.Appointment.count({
                where: {
                    service: { sProviderId: providerId },
                    createdAt: { gte: startOfToday }
                }
            }),
            // Total unique customers
            context.entities.CustomerFolder.count({
                where: { sProviderId: providerId }
            }),
            // Total services
            context.entities.Service.count({
                where: { sProviderId: providerId }
            }),
            // Total locations
            context.entities.SProvidingPlace.count({
                where: { sProviderId: providerId }
            }),
            // Total queues
            context.entities.Queue.count({
                where: { sProvidingPlace: { sProviderId: providerId } }
            }),
            // Appointments by status
            context.entities.Appointment.groupBy({
                by: ['status'],
                where: {
                    service: { sProviderId: providerId }
                },
                _count: {
                    status: true
                }
            }),
            // Recent appointments (last 10)
            context.entities.Appointment.findMany({
                where: {
                    service: { sProviderId: providerId }
                },
                include: {
                    customerFolder: {
                        include: {
                            customer: {
                                select: {
                                    firstName: true,
                                    lastName: true,
                                    email: true,
                                }
                            }
                        }
                    },
                    service: {
                        select: {
                            title: true,
                            duration: true,
                        }
                    },
                    place: {
                        select: {
                            name: true,
                        }
                    }
                },
                orderBy: { createdAt: 'desc' },
                take: 10
            }),
            // Monthly revenue calculation (we'll calculate this separately)
            context.entities.Appointment.findMany({
                where: {
                    service: {
                        sProviderId: providerId,
                        price: { not: null }
                    },
                    createdAt: { gte: startOfMonth },
                    status: { in: ['completed', 'confirmed'] }
                },
                include: {
                    service: {
                        select: {
                            price: true
                        }
                    }
                }
            })
        ]);

        // Format appointment status data
        const statusStats = appointmentsByStatus.reduce((acc: any, item: any) => {
            acc[item.status] = item._count.status;
            return acc;
        }, {});

        // Calculate monthly revenue
        const monthlyRevenue = monthlyRevenueAppointments.reduce((total: number, apt: any) => {
            return total + (apt.service.price || 0);
        }, 0);

        const stats = {
            overview: {
                totalAppointments,
                monthlyAppointments,
                weeklyAppointments,
                todayAppointments,
                totalCustomers,
                totalServices,
                totalLocations,
                totalQueues,
                averageRating: provider.averageRating,
                totalReviews: provider.totalReviews,
                isVerified: provider.isVerified,
                isSetupComplete: provider.isSetupComplete,
                memberSince: provider.createdAt,
            },
            appointments: {
                byStatus: statusStats,
                recent: recentAppointments.map((apt: any) => ({
                    id: apt.id,
                    status: apt.status,
                    createdAt: apt.createdAt,
                    expectedStartTime: apt.expectedAppointmentStartTime,
                    customer: {
                        name: `${apt.customerFolder.customer.firstName || ''} ${apt.customerFolder.customer.lastName || ''}`.trim(),
                        email: apt.customerFolder.customer.email,
                    },
                    service: {
                        title: apt.service.title,
                        duration: apt.service.duration,
                    },
                    location: {
                        name: apt.place.name,
                    }
                }))
            },
            revenue: {
                monthlyTotal: monthlyRevenue,
                currency: 'DZD' // Assuming Algerian Dinar
            }
        };

        res.status(200).json({
            success: true,
            data: stats,
            message: 'Provider statistics retrieved successfully'
        });

    } catch (error: any) {
        console.error('Get admin provider stats failed:', error);
        if (error instanceof HttpError) {
            res.status(error.statusCode || 500).json({
                error: error.message
            });
        } else {
            res.status(500).json({
                error: 'Failed to retrieve provider statistics'
            });
        }
    }
};

// Create admin user handler
const CreateAdminUserInputSchema = z.object({
    email: z.string().email(),
    firstName: z.string().min(1),
    lastName: z.string().min(1),
    password: z.string().min(8),
    role: z.enum([Role.ADMIN, 'CLIENT']).optional().default(Role.ADMIN),
});

export const handleCreateAdminUser = async (req: any, res: any, context: any) => {
    try {
        // Ensure admin access
        if (!context.user || context.user.role !== Role.ADMIN) {
            throw new HttpError(403, 'Admin access required');
        }
        
        const args = ensureArgsSchemaOrThrowHttpError(CreateAdminUserInputSchema, req.body);
        
        // Check if user already exists
        const existingUser = await context.entities.User.findUnique({
            where: { email: args.email }
        });
        
        if (existingUser) {
            throw new HttpError(400, 'User with this email already exists');
        }
        
        // Create the admin user (this will be handled by the auth system)
        const user = await context.entities.User.create({
            data: {
                email: args.email,
                username: args.email,
                firstName: args.firstName,
                lastName: args.lastName,
                role: args.role,
                isAdmin: args.role === Role.ADMIN,
                isEmailVerified: true, // Admin users are pre-verified
            },
            select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
                role: true,
                isAdmin: true,
                createdAt: true,
            }
        });
        
        res.status(201).json({
            user,
            message: 'Admin user created successfully'
        });
        
    } catch (error: any) {
        console.error('Create admin user failed:', error);
        if (error instanceof HttpError) {
            res.status(error.statusCode || 500).json({
                error: error.message
            });
        } else {
            res.status(500).json({
                error: 'Failed to create admin user'
            });
        }
    }
};

// Update provider verification status handler
const UpdateProviderStatusInputSchema = z.object({
    isVerified: z.boolean(),
    reason: z.string().optional(),
});

export const handleUpdateProviderStatus = async (req: any, res: any, context: any) => {
    try {
        // Ensure admin access
        if (!context.user || context.user.role !== Role.ADMIN) {
            throw new HttpError(403, 'Admin access required');
        }
        
        const providerId = parseInt(req.params.providerId);
        if (!providerId) {
            throw new HttpError(400, 'Invalid provider ID');
        }
        
        const args = ensureArgsSchemaOrThrowHttpError(UpdateProviderStatusInputSchema, req.body);
        
        const provider = await context.entities.SProvider.findUnique({
            where: { id: providerId },
            include: { user: true }
        });
        
        if (!provider) {
            throw new HttpError(404, 'Provider not found');
        }
        
        const updatedProvider = await context.entities.SProvider.update({
            where: { id: providerId },
            data: {
                isVerified: args.isVerified,
                updatedAt: new Date(),
            },
            include: {
                user: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        mobileNumber: true,
                    }
                },
                category: {
                    select: {
                        id: true,
                        title: true,
                    }
                }
            }
        });
        
        // Log the status change
        console.log(`Provider ${provider.user.firstName} ${provider.user.lastName} verification status updated to: ${args.isVerified}`);
        
        res.status(200).json({
            provider: updatedProvider,
            message: `Provider ${args.isVerified ? 'approved' : 'rejected'} successfully`
        });
        
    } catch (error: any) {
        console.error('Update provider status failed:', error);
        if (error instanceof HttpError) {
            res.status(error.statusCode || 500).json({
                error: error.message
            });
        } else {
            res.status(500).json({
                error: 'Failed to update provider status'
            });
        }
    }
};

// Get single provider category handler
export const handleGetAdminProviderCategory = async (req: any, res: any, context: any) => {
    try {
        // Ensure admin access
        if (!context.user || context.user.role !== Role.ADMIN) {
            throw new HttpError(403, 'Admin access required');
        }

        const categoryId = parseInt(req.params.categoryId);
        if (!categoryId) {
            throw new HttpError(400, 'Invalid category ID');
        }

        const category = await context.entities.ProviderCategory.findUnique({
            where: { id: categoryId },
            include: {
                parent: {
                    select: {
                        id: true,
                        title: true,
                    }
                },
                children: {
                    select: {
                        id: true,
                        title: true,
                        imageId: true,
                        _count: {
                            select: {
                                providers: true,
                                children: true,
                            }
                        }
                    }
                },
                image: {
                    select: {
                        id: true,
                        name: true,
                        type: true,
                        key: true,
                        uploadUrl: true,
                        createdAt: true,
                    }
                },
                providers: {
                    select: {
                        id: true,
                        title: true,
                        isVerified: true,
                        user: {
                            select: {
                                firstName: true,
                                lastName: true,
                                email: true,
                            }
                        }
                    },
                    take: 10, // Limit to first 10 providers for performance
                    orderBy: { createdAt: 'desc' }
                },
                _count: {
                    select: {
                        providers: true,
                        children: true,
                    }
                }
            }
        });

        if (!category) {
            throw new HttpError(404, 'Provider category not found');
        }

        res.status(200).json({
            success: true,
            data: category,
            message: 'Provider category retrieved successfully'
        });

    } catch (error: any) {
        console.error('Get admin provider category failed:', error);
        if (error instanceof HttpError) {
            res.status(error.statusCode || 500).json({
                error: error.message
            });
        } else {
            res.status(500).json({
                error: 'Failed to retrieve provider category'
            });
        }
    }
};

// Provider categories CRUD handlers
export const handleGetProviderCategories = async (req: any, res: any, context: any) => {
    try {

        console.log(" context.user info in handleGetProviderCategories: ",context.user)
        // Ensure admin access
        if (!context.user || context.user.role !== Role.ADMIN) {
            throw new HttpError(403, 'Admin access required');
        }
        
        const includeHierarchy = req.query.includeHierarchy !== 'false';
        
        const include = includeHierarchy ? {
            parent: true,
            children: true,
            image: true,
            _count: {
                select: {
                    providers: true,
                    children: true,
                }
            }
        } : {
            image: true,
            _count: {
                select: {
                    providers: true,
                }
            }
        };
        
        const categories = await context.entities.ProviderCategory.findMany({
            include,
            orderBy: { id: 'asc' }
        });
        
        res.status(200).json({
            success: true,
            data:categories,
            message: 'Provider categories fetched successfully'
        });
        
    } catch (error: any) {
        console.error('Get provider categories failed:', error);
        if (error instanceof HttpError) {
            res.status(error.statusCode || 500).json({
                error: error.message
            });
        } else {
            res.status(500).json({
                error: 'Failed to retrieve provider categories'
            });
        }
    }
};

const CreateProviderCategoryInputSchema = z.object({
    title: z.string().min(1),
    description: z.string().optional(),
    parentId: z.union([
        z.number().int().positive(),
        z.string().transform((val) => {
            if (val === '' || val === null || val === undefined) return undefined;
            const num = parseInt(val);
            if (isNaN(num) || num <= 0) throw new Error('Invalid parentId');
            return num;
        })
    ]).optional(),
    isActive: z.boolean().optional().default(true),
    sortOrder: z.union([
        z.number().int(),
        z.string().transform((val) => {
            const num = parseInt(val);
            if (isNaN(num)) throw new Error('Invalid sortOrder');
            return num;
        })
    ]).optional().default(0),
    metadata: z.object({
        icon: z.string().optional().default(""),
        color: z.string().optional().default("#EF4444"),
        keywords: z.array(z.string()).optional().default([]),
        seoTitle: z.string().optional().default(""),
        seoDescription: z.string().optional().default("")
    }).optional()
});

export const handleCreateProviderCategory = async (req: any, res: any, context: any) => {
    try {
        // Ensure admin access
        if (!context.user || context.user.role !== Role.ADMIN) {
            throw new HttpError(403, 'Admin access required');
        }
        
        const args = ensureArgsSchemaOrThrowHttpError(CreateProviderCategoryInputSchema, req.body);
        
        // Check if category with this title already exists
        const existingCategory = await context.entities.ProviderCategory.findUnique({
            where: { title: args.title }
        });
        
        if (existingCategory) {
            throw new HttpError(400, 'Category with this title already exists');
        }
        
        // If parentId is provided, check if parent exists
        if (args.parentId) {
            const parentCategory = await context.entities.ProviderCategory.findUnique({
                where: { id: args.parentId }
            });
            
            if (!parentCategory) {
                throw new HttpError(400, 'Parent category not found');
            }
        }
        
        const category = await context.entities.ProviderCategory.create({
            data: {
                title: args.title,
                description: args.description || null,
                parentId: args.parentId || null,
                isActive: args.isActive,
                sortOrder: args.sortOrder,
                metadata: args.metadata || null,
            },
            include: {
                parent: true,
                children: true,
                image: true,
                _count: {
                    select: {
                        providers: true,
                        children: true,
                    }
                }
            }
        });
        
        res.status(201).json({
            category,
            message: 'Provider category created successfully'
        });
        
    } catch (error: any) {
        console.error('Create provider category failed:', error);
        if (error instanceof HttpError) {
            res.status(error.statusCode || 500).json({
                error: error.message
            });
        } else {
            res.status(500).json({
                error: 'Failed to create provider category'
            });
        }
    }
};

const UpdateProviderCategoryInputSchema = z.object({
    title: z.string().min(1).optional(),
    description: z.string().optional(),
    parentId: z.union([
        z.number().int().positive(),
        z.string().transform((val) => {
            if (val === '' || val === null || val === undefined) return undefined;
            const num = parseInt(val);
            if (isNaN(num) || num <= 0) throw new Error('Invalid parentId');
            return num;
        })
    ]).optional(),
    isActive: z.boolean().optional(),
    sortOrder: z.union([
        z.number().int(),
        z.string().transform((val) => {
            const num = parseInt(val);
            if (isNaN(num)) throw new Error('Invalid sortOrder');
            return num;
        })
    ]).optional(),
    metadata: z.object({
        icon: z.string().optional(),
        color: z.string().optional(),
        keywords: z.array(z.string()).optional(),
        seoTitle: z.string().optional(),
        seoDescription: z.string().optional()
    }).optional()
});

export const handleUpdateProviderCategory = async (req: any, res: any, context: any) => {
    try {
        // Ensure admin access
        if (!context.user || context.user.role !== Role.ADMIN) {
            throw new HttpError(403, 'Admin access required');
        }
        
        const categoryId = parseInt(req.params.categoryId);
        if (!categoryId) {
            throw new HttpError(400, 'Invalid category ID');
        }
        
        const args = ensureArgsSchemaOrThrowHttpError(UpdateProviderCategoryInputSchema, req.body);
        
        const existingCategory = await context.entities.ProviderCategory.findUnique({
            where: { id: categoryId }
        });
        
        if (!existingCategory) {
            throw new HttpError(404, 'Category not found');
        }
        
        // Check if another category with this title exists (excluding current one)
        const titleConflict = await context.entities.ProviderCategory.findFirst({
            where: {
                title: args.title,
                id: { not: categoryId }
            }
        });
        
        if (titleConflict) {
            throw new HttpError(400, 'Category with this title already exists');
        }
        
        // If parentId is provided, check if parent exists and prevent circular references
        if (args.parentId) {
            if (args.parentId === categoryId) {
                throw new HttpError(400, 'Category cannot be its own parent');
            }
            
            const parentCategory = await context.entities.ProviderCategory.findUnique({
                where: { id: args.parentId }
            });
            
            if (!parentCategory) {
                throw new HttpError(400, 'Parent category not found');
            }
            
            // Check for circular reference (parent should not be a child of this category)
            const descendants = await context.entities.ProviderCategory.findMany({
                where: { parentId: categoryId }
            });
            
            for (const descendant of descendants) {
                if (descendant.id === args.parentId) {
                    throw new HttpError(400, 'Cannot create circular reference');
                }
            }
        }
        
        // Build update data object with only provided fields
        const updateData: any = {};
        if (args.title !== undefined) updateData.title = args.title;
        if (args.description !== undefined) updateData.description = args.description;
        if (args.parentId !== undefined) updateData.parentId = args.parentId;
        if (args.isActive !== undefined) updateData.isActive = args.isActive;
        if (args.sortOrder !== undefined) updateData.sortOrder = args.sortOrder;
        if (args.metadata !== undefined) updateData.metadata = args.metadata;

        const category = await context.entities.ProviderCategory.update({
            where: { id: categoryId },
            data: updateData,
            include: {
                parent: true,
                children: true,
                image: true,
                _count: {
                    select: {
                        providers: true,
                        children: true,
                    }
                }
            }
        });
        
        res.status(200).json({
            category,
            message: 'Provider category updated successfully'
        });
        
    } catch (error: any) {
        console.error('Update provider category failed:', error);
        if (error instanceof HttpError) {
            res.status(error.statusCode || 500).json({
                error: error.message
            });
        } else {
            res.status(500).json({
                error: 'Failed to update provider category'
            });
        }
    }
};

export const handleDeleteProviderCategory = async (req: any, res: any, context: any) => {
    try {
        // Ensure admin access
        if (!context.user || context.user.role !== Role.ADMIN) {
            throw new HttpError(403, 'Admin access required');
        }
        
        const categoryId = parseInt(req.params.categoryId);
        if (!categoryId) {
            throw new HttpError(400, 'Invalid category ID');
        }
        
        const category = await context.entities.ProviderCategory.findUnique({
            where: { id: categoryId },
            include: {
                children: true,
                providers: true,
            }
        });
        
        if (!category) {
            throw new HttpError(404, 'Category not found');
        }
        
        // Check if category has children
        if (category.children.length > 0) {
            throw new HttpError(400, 'Cannot delete category with child categories. Please delete or reassign child categories first.');
        }
        
        // Check if category has providers
        if (category.providers.length > 0) {
            throw new HttpError(400, 'Cannot delete category with assigned providers. Please reassign providers first.');
        }
        
        await context.entities.ProviderCategory.delete({
            where: { id: categoryId }
        });
        
        res.status(200).json({
            message: 'Provider category deleted successfully'
        });
        
    } catch (error: any) {
        console.error('Delete provider category failed:', error);
        if (error instanceof HttpError) {
            res.status(error.statusCode || 500).json({
                error: error.message
            });
        } else {
            res.status(500).json({
                error: 'Failed to delete provider category'
            });
        }
    }
};

// Advertisement Management Handlers
export const handleGetAdvertisements = async (req: any, res: any, context: any) => {
    try {
        // Check admin access
        if (!context.user || context.user.role !== 'ADMIN') {
            return res.status(403).json({
                error: 'Admin access required'
            });
        }

        const { page, limit, isActive, search } = GetAdvertisementsQuerySchema.parse(req.query);

        const skip = (page - 1) * limit;

        const whereClause: any = {};

        if (isActive) {
            whereClause.isActive = isActive;
        }

        if (search) {
            whereClause.OR = [
                { title: { contains: search, mode: 'insensitive' } },
                { subtitle: { contains: search, mode: 'insensitive' } },
                { description: { contains: search, mode: 'insensitive' } },
            ];
        }

        console.log("whereClause in handleGetAdvertisements: ",whereClause)

        const [advertisements, totalCount] = await Promise.all([
            context.entities.Advertisement.findMany({
                where: whereClause,
                include: {
                    backgroundImage: true,
                    pngImage: true,
                },
                orderBy: [
                    { sortOrder: 'asc' },
                    { createdAt: 'desc' }
                ],
                skip,
                take: limit,
            }),
            context.entities.Advertisement.count({
                where: whereClause,
            }),
        ]);

        res.status(200).json({
            success: true,
            data: {
                advertisements,
                pagination: {
                    page,
                    limit,
                    totalCount,
                    totalPages: Math.ceil(totalCount / limit),
                }
            }
        });

    } catch (error: any) {
        console.error('Get advertisements failed:', error);

        if (error.name === 'ZodError') {
            res.status(400).json({
                success: false,
                error: 'Invalid query parameters',
                details: error.errors
            });
        } else {
            res.status(500).json({
                success: false,
                error: 'Failed to retrieve advertisements'
            });
        }
    }
};

export const handleCreateAdvertisement = async (req: any, res: any, context: any) => {
    try {
        // Check admin access
        if (!context.user || context.user.role !== 'ADMIN') {
            return res.status(403).json({
                error: 'Admin access required'
            });
        }

        const validatedData = CreateAdvertisementSchema.parse(req.body);

        // Validate that image IDs exist if provided
        if (validatedData.backgroundImageId) {
            const backgroundImage = await context.entities.File.findUnique({
                where: { id: validatedData.backgroundImageId }
            });
            if (!backgroundImage) {
                return res.status(400).json({
                    success: false,
                    error: 'Background image not found'
                });
            }
        }

        if (validatedData.pngImageId) {
            const pngImage = await context.entities.File.findUnique({
                where: { id: validatedData.pngImageId }
            });
            if (!pngImage) {
                return res.status(400).json({
                    success: false,
                    error: 'PNG image not found'
                });
            }
        }

        const advertisement = await context.entities.Advertisement.create({
            data: {
                title: validatedData.title,
                subtitle: validatedData.subtitle,
                description: validatedData.description,
                callToActionText: validatedData.callToActionText,
                callToActionLink: validatedData.callToActionLink,
                isExternal: validatedData.isExternal ?? false,
                isActive: validatedData.isActive ?? true,
                sortOrder: validatedData.sortOrder ?? 0,
                backgroundImageId: validatedData.backgroundImageId,
                pngImageId: validatedData.pngImageId,
            },
            include: {
                backgroundImage: true,
                pngImage: true,
            }
        });

        res.status(201).json({
            success: true,
            message: 'Advertisement created successfully',
            data: advertisement
        });

    } catch (error: any) {
        console.error('Create advertisement failed:', error);

        if (error.name === 'ZodError') {
            res.status(400).json({
                success: false,
                error: 'Invalid request data',
                details: error.errors
            });
        } else {
            res.status(500).json({
                success: false,
                error: 'Failed to create advertisement'
            });
        }
    }
};

export const handleUpdateAdvertisement = async (req: any, res: any, context: any) => {
    try {
        // Check admin access
        if (!context.user || context.user.role !== 'ADMIN') {
            return res.status(403).json({
                error: 'Admin access required'
            });
        }

        const { advertisementId } = AdvertisementIdSchema.parse(req.params);
        const validatedData = UpdateAdvertisementSchema.parse(req.body);

        const existingAdvertisement = await context.entities.Advertisement.findUnique({
            where: { id: advertisementId }
        });

        if (!existingAdvertisement) {
            return res.status(404).json({
                success: false,
                error: 'Advertisement not found'
            });
        }

        // Validate that image IDs exist if provided
        if (validatedData.backgroundImageId) {
            const backgroundImage = await context.entities.File.findUnique({
                where: { id: validatedData.backgroundImageId }
            });
            if (!backgroundImage) {
                return res.status(400).json({
                    success: false,
                    error: 'Background image not found'
                });
            }
        }

        if (validatedData.pngImageId) {
            const pngImage = await context.entities.File.findUnique({
                where: { id: validatedData.pngImageId }
            });
            if (!pngImage) {
                return res.status(400).json({
                    success: false,
                    error: 'PNG image not found'
                });
            }
        }

        const updateData: any = {};

        if (validatedData.title !== undefined) updateData.title = validatedData.title;
        if (validatedData.subtitle !== undefined) updateData.subtitle = validatedData.subtitle;
        if (validatedData.description !== undefined) updateData.description = validatedData.description;
        if (validatedData.callToActionText !== undefined) updateData.callToActionText = validatedData.callToActionText;
        if (validatedData.callToActionLink !== undefined) updateData.callToActionLink = validatedData.callToActionLink;
        if (validatedData.isExternal !== undefined) updateData.isExternal = validatedData.isExternal;
        if (validatedData.isActive !== undefined) updateData.isActive = validatedData.isActive;
        if (validatedData.sortOrder !== undefined) updateData.sortOrder = validatedData.sortOrder;
        if (validatedData.backgroundImageId !== undefined) updateData.backgroundImageId = validatedData.backgroundImageId;
        if (validatedData.pngImageId !== undefined) updateData.pngImageId = validatedData.pngImageId;

        const advertisement = await context.entities.Advertisement.update({
            where: { id: advertisementId },
            data: updateData,
            include: {
                backgroundImage: true,
                pngImage: true,
            }
        });

        res.status(200).json({
            success: true,
            message: 'Advertisement updated successfully',
            data: advertisement
        });

    } catch (error: any) {
        console.error('Update advertisement failed:', error);

        if (error.name === 'ZodError') {
            res.status(400).json({
                success: false,
                error: 'Invalid request data',
                details: error.errors
            });
        } else {
            res.status(500).json({
                success: false,
                error: 'Failed to update advertisement'
            });
        }
    }
};

export const handleDeleteAdvertisement = async (req: any, res: any, context: any) => {
    try {
        // Check admin access
        if (!context.user || context.user.role !== 'ADMIN') {
            return res.status(403).json({
                error: 'Admin access required'
            });
        }

        const { advertisementId } = AdvertisementIdSchema.parse(req.params);

        const advertisement = await context.entities.Advertisement.findUnique({
            where: { id: advertisementId }
        });

        if (!advertisement) {
            return res.status(404).json({
                success: false,
                error: 'Advertisement not found'
            });
        }

        await context.entities.Advertisement.delete({
            where: { id: advertisementId }
        });

        res.status(200).json({
            success: true,
            message: 'Advertisement deleted successfully'
        });

    } catch (error: any) {
        console.error('Delete advertisement failed:', error);

        if (error.name === 'ZodError') {
            res.status(400).json({
                success: false,
                error: 'Invalid request parameters',
                details: error.errors
            });
        } else {
            res.status(500).json({
                success: false,
                error: 'Failed to delete advertisement'
            });
        }
    }
};

// Advertisement Image Upload Handlers
export const handleUploadAdvertisementBackgroundImage = async (req: any, res: any, context: any) => {
    try {
        // Check admin access
        if (!context.user || context.user.role !== 'ADMIN') {
            return res.status(403).json({
                success: false,
                error: 'Admin access required'
            });
        }

        const { advertisementId } = AdvertisementIdSchema.parse(req.params);
        const { fileType, fileName } = UploadImageSchema.parse(req.body);

        // Check if advertisement exists
        const advertisement = await context.entities.Advertisement.findUnique({
            where: { id: advertisementId },
            include: { backgroundImage: true }
        });

        if (!advertisement) {
            return res.status(404).json({
                success: false,
                error: 'Advertisement not found'
            });
        }

        // Create the file record using existing operation
        const result = await createFile({ fileType, fileName }, context);

        // Get the created file record to get its ID
        const fileRecord = await context.entities.File.findFirst({
            where: {
                userId: context.user.id,
                name: fileName,
                type: fileType,
                key: result.s3UploadFields.key || result.s3UploadUrl.split('/').pop()
            },
            orderBy: {
                createdAt: 'desc'
            }
        });

        if (!fileRecord) {
            throw new HttpError(500, 'Failed to create file record');
        }

        // Update advertisement's background image reference
        const updatedAdvertisement = await context.entities.Advertisement.update({
            where: { id: advertisementId },
            data: { backgroundImageId: fileRecord.id },
            include: {
                backgroundImage: true,
                pngImage: true
            }
        });

        return res.status(200).json({
            success: true,
            message: 'Advertisement background image upload URL generated successfully',
            data: {
                uploadUrl: result.s3UploadUrl,
                uploadFields: result.s3UploadFields,
                file: {
                    id: fileRecord.id,
                    name: fileName,
                    type: fileType,
                    key: fileRecord.key
                },
                advertisement: {
                    id: updatedAdvertisement.id,
                    backgroundImageId: updatedAdvertisement.backgroundImageId,
                    pngImageId: updatedAdvertisement.pngImageId
                }
            }
        });

    } catch (error: any) {
        console.error('Upload advertisement background image failed:', error);

        if (error instanceof HttpError) {
            res.status(error.statusCode).json({
                success: false,
                error: error.message
            });
        } else {
            res.status(500).json({
                success: false,
                error: 'Internal server error'
            });
        }
    }
};

export const handleUploadAdvertisementPngImage = async (req: any, res: any, context: any) => {
    try {
        // Check admin access
        if (!context.user || context.user.role !== 'ADMIN') {
            return res.status(403).json({
                success: false,
                error: 'Admin access required'
            });
        }

        const { advertisementId } = AdvertisementIdSchema.parse(req.params);
        const { fileType, fileName } = UploadImageSchema.parse(req.body);

        // Check if advertisement exists
        const advertisement = await context.entities.Advertisement.findUnique({
            where: { id: advertisementId },
            include: { pngImage: true }
        });

        if (!advertisement) {
            return res.status(404).json({
                success: false,
                error: 'Advertisement not found'
            });
        }

        // Create the file record using existing operation
        const result = await createFile({ fileType, fileName }, context);

        // Get the created file record to get its ID
        const fileRecord = await context.entities.File.findFirst({
            where: {
                userId: context.user.id,
                name: fileName,
                type: fileType,
                key: result.s3UploadFields.key || result.s3UploadUrl.split('/').pop()
            },
            orderBy: {
                createdAt: 'desc'
            }
        });

        if (!fileRecord) {
            throw new HttpError(500, 'Failed to create file record');
        }

        // Update advertisement's PNG image reference
        const updatedAdvertisement = await context.entities.Advertisement.update({
            where: { id: advertisementId },
            data: { pngImageId: fileRecord.id },
            include: {
                backgroundImage: true,
                pngImage: true
            }
        });

        return res.status(200).json({
            success: true,
            message: 'Advertisement PNG image upload URL generated successfully',
            data: {
                uploadUrl: result.s3UploadUrl,
                uploadFields: result.s3UploadFields,
                file: {
                    id: fileRecord.id,
                    name: fileName,
                    type: fileType,
                    key: fileRecord.key
                },
                advertisement: {
                    id: updatedAdvertisement.id,
                    backgroundImageId: updatedAdvertisement.backgroundImageId,
                    pngImageId: updatedAdvertisement.pngImageId
                }
            }
        });

    } catch (error: any) {
        console.error('Upload advertisement PNG image failed:', error);

        if (error instanceof HttpError) {
            res.status(error.statusCode).json({
                success: false,
                error: error.message
            });
        } else {
            res.status(500).json({
                success: false,
                error: 'Internal server error'
            });
        }
    }
};