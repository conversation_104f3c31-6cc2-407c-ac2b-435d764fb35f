import { type AuthUser } from 'wasp/auth';
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Role, type PrismaClient } from '@prisma/client';
export function useRedirectHomeUnlessUserIsAdmin({ user }: { user: AuthUser }) {
  const navigate = useNavigate();

  useEffect(() => {
    if (user.role !== Role.CLIENT && user.role !== Role.ADMIN) {
      navigate('/');
    }
  }, [user, history]);
}
