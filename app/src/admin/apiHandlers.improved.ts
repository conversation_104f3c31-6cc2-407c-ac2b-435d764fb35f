import { HttpError } from 'wasp/server';
import { verifyPassword } from 'wasp/auth/password';
import { createInvalidCredentialsError } from 'wasp/auth/utils';
import { createSession } from 'wasp/auth/session';
import { getProviderDataWithPassword } from 'wasp/server/auth';
import { withAdminAuth, ensureAdminMiddleware } from './middleware';
import {
  AdminLoginSchema,
  GetProvidersQuerySchema,
  UpdateProviderStatusSchema,
  GetCustomersQuerySchema,
  CreateAdminUserSchema,
  GetCategoriesQuerySchema,
  CreateCategorySchema,
  UpdateCategorySchema,
  DeleteCategorySchema,
  ProviderIdSchema,
  CategoryIdSchema,
  GetAdvertisementsQuerySchema,
  CreateAdvertisementSchema,
  UpdateAdvertisementSchema,
  DeleteAdvertisementSchema,
  AdvertisementIdSchema,
} from './validation';

/**
 * Admin Authentication Handler
 */
export const handleAdminLogin = async (req: any, res: any, context: any) => {
  try {
    const { email, password } = AdminLoginSchema.parse(req.body);
    
    // Find admin user by email
    const user = await context.entities.User.findUnique({
      where: { 
        email,
        role: 'ADMIN'
      },
      include: {
        auth: {
          include: {
            identities: true
          }
        }
      }
    });
    
    if (!user) {
      throw createInvalidCredentialsError();
    }
    
    // Get and verify password
    const identity = user.auth.identities.find((i: any) => i.providerName === 'email');
    if (!identity) {
      throw createInvalidCredentialsError();
    }
    
    const providerData = await getProviderDataWithPassword(identity.providerData);
    
    // Verify password - ensure we have EmailProviderData
    if (!providerData || !('hashedPassword' in providerData)) {
      throw createInvalidCredentialsError();
    }
    
    try {
      await verifyPassword(providerData.hashedPassword, password);
    } catch (e) {
      throw createInvalidCredentialsError();
    }
    
    // Create session
    const session = await createSession(user.auth.id);
    
    res.status(200).json({
      sessionId: session.id,
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        isAdmin: user.isAdmin,
      }
    });
    
  } catch (error: any) {
    console.error('Admin login failed:', error);
    
    if (error instanceof HttpError) {
      res.status(error.statusCode || 500).json({
        error: error.message
      });
    } else if (error.name === 'ZodError') {
      res.status(400).json({
        error: 'Invalid input data',
        details: error.errors
      });
    } else {
      res.status(500).json({
        error: 'Login failed'
      });
    }
  }
};

/**
 * Provider Management Handlers
 */
export const handleGetAdminProviders = withAdminAuth(async (req: any, res: any, context: any) => {
  try {
    const { page, limit, isVerified, search } = GetProvidersQuerySchema.parse(req.query);
    
    const skip = (page - 1) * limit;
    
    const whereClause: any = {};
    
    if (isVerified !== undefined) {
      whereClause.isVerified = isVerified;
    }
    
    if (search) {
      whereClause.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { user: { firstName: { contains: search, mode: 'insensitive' } } },
        { user: { lastName: { contains: search, mode: 'insensitive' } } },
        { user: { email: { contains: search, mode: 'insensitive' } } },
      ];
    }
    
    const [providers, totalCount] = await Promise.all([
      context.entities.SProvider.findMany({
        where: whereClause,
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              mobileNumber: true,
              createdAt: true,
              isPhoneVerified: true,
              isEmailVerified: true,
            }
          },
          category: {
            select: {
              id: true,
              title: true,
            }
          },
          _count: {
            select: {
              services: true,
              customerFolders: true,
              reviewsReceived: true,
            }
          }
        },
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
      }),
      context.entities.SProvider.count({ where: whereClause })
    ]);
    
    res.status(200).json({
      providers,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
      }
    });
    
  } catch (error: any) {
    console.error('Get admin providers failed:', error);
    
    if (error.name === 'ZodError') {
      res.status(400).json({
        error: 'Invalid query parameters',
        details: error.errors
      });
    } else {
      res.status(500).json({
        error: 'Failed to retrieve providers'
      });
    }
  }
});

export const handleUpdateProviderStatus = withAdminAuth(async (req: any, res: any, context: any) => {
  try {
    const { providerId } = ProviderIdSchema.parse(req.params);
    const { isVerified, reason } = UpdateProviderStatusSchema.parse(req.body);
    
    const provider = await context.entities.SProvider.findUnique({
      where: { id: providerId },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          }
        }
      }
    });
    
    if (!provider) {
      throw new HttpError(404, 'Provider not found');
    }
    
    const updatedProvider = await context.entities.SProvider.update({
      where: { id: providerId },
      data: { isVerified },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          }
        },
        category: {
          select: {
            id: true,
            title: true,
          }
        }
      }
    });
    
    // TODO: Send notification to provider about status change
    // await sendProviderStatusNotification(provider.user.email, isVerified, reason);
    
    res.status(200).json({
      message: `Provider ${isVerified ? 'approved' : 'rejected'} successfully`,
      provider: updatedProvider
    });
    
  } catch (error: any) {
    console.error('Update provider status failed:', error);
    
    if (error instanceof HttpError) {
      res.status(error.statusCode || 500).json({
        error: error.message
      });
    } else if (error.name === 'ZodError') {
      res.status(400).json({
        error: 'Invalid input data',
        details: error.errors
      });
    } else {
      res.status(500).json({
        error: 'Failed to update provider status'
      });
    }
  }
});

/**
 * Customer Management Handler
 */
export const handleGetAdminCustomers = withAdminAuth(async (req: any, res: any, context: any) => {
  try {
    const { page, limit, search } = GetCustomersQuerySchema.parse(req.query);
    
    const skip = (page - 1) * limit;
    
    const whereClause: any = {
      OR: [
        { role: 'CUSTOMER' },
        { customerFolders: { some: {} } },
      ]
    };
    
    if (search) {
      whereClause.AND = [
        whereClause.OR ? { OR: whereClause.OR } : {},
        {
          OR: [
            { firstName: { contains: search, mode: 'insensitive' } },
            { lastName: { contains: search, mode: 'insensitive' } },
            { email: { contains: search, mode: 'insensitive' } },
            { mobileNumber: { contains: search, mode: 'insensitive' } },
          ]
        }
      ];
      delete whereClause.OR;
    }
    
    const [customers, totalCount] = await Promise.all([
      context.entities.User.findMany({
        where: whereClause,
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          mobileNumber: true,
          createdAt: true,
          isPhoneVerified: true,
          isEmailVerified: true,
          _count: {
            select: {
              customerFolders: true,
            }
          }
        },
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
      }),
      context.entities.User.count({ where: whereClause })
    ]);
    
    res.status(200).json({
      customers,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
      }
    });
    
  } catch (error: any) {
    console.error('Get admin customers failed:', error);
    
    if (error.name === 'ZodError') {
      res.status(400).json({
        error: 'Invalid query parameters',
        details: error.errors
      });
    } else {
      res.status(500).json({
        error: 'Failed to retrieve customers'
      });
    }
  }
});

/**
 * Admin User Management Handler
 */
export const handleCreateAdminUser = withAdminAuth(async (req: any, res: any, context: any) => {
  try {
    const { email, firstName, lastName, password, mobileNumber } = CreateAdminUserSchema.parse(req.body);
    
    // Check if user already exists
    const existingUser = await context.entities.User.findUnique({
      where: { email }
    });
    
    if (existingUser) {
      throw new HttpError(400, 'User with this email already exists');
    }
    
    // Create admin user using Wasp's auth system
    // This is a simplified version - you may need to integrate with your auth provider
    const newUser = await context.entities.User.create({
      data: {
        email,
        firstName,
        lastName,
        mobileNumber,
        role: 'ADMIN',
        isAdmin: true,
        // Note: Password handling depends on your auth setup
        // You may need to create auth identity separately
      }
    });
    
    res.status(201).json({
      message: 'Admin user created successfully',
      user: {
        id: newUser.id,
        email: newUser.email,
        firstName: newUser.firstName,
        lastName: newUser.lastName,
        role: newUser.role,
        isAdmin: newUser.isAdmin,
      }
    });
    
  } catch (error: any) {
    console.error('Create admin user failed:', error);
    
    if (error instanceof HttpError) {
      res.status(error.statusCode || 500).json({
        error: error.message
      });
    } else if (error.name === 'ZodError') {
      res.status(400).json({
        error: 'Invalid input data',
        details: error.errors
      });
    } else {
      res.status(500).json({
        error: 'Failed to create admin user'
      });
    }
  }
});

/**
 * Provider Category Management Handlers
 */
export const handleGetProviderCategories = withAdminAuth(async (req: any, res: any, context: any) => {
  try {
    const { includeHierarchy } = GetCategoriesQuerySchema.parse(req.query);
    
    const include = includeHierarchy ? {
      parent: true,
      children: true,
      _count: {
        select: {
          providers: true,
          children: true,
        }
      }
    } : {
      _count: {
        select: {
          providers: true,
        }
      }
    };
    
    const categories = await context.entities.ProviderCategory.findMany({
      include,
      orderBy: { id: 'asc' }
    });
    
    res.status(200).json({
      categories
    });
    
  } catch (error: any) {
    console.error('Get provider categories failed:', error);
    
    if (error.name === 'ZodError') {
      res.status(400).json({
        error: 'Invalid query parameters',
        details: error.errors
      });
    } else {
      res.status(500).json({
        error: 'Failed to retrieve categories'
      });
    }
  }
});

export const handleCreateProviderCategory = withAdminAuth(async (req: any, res: any, context: any) => {
  try {
    const { title, parentId } = CreateCategorySchema.parse(req.body);
    
    // Check if category exists
    const existingCategory = await context.entities.ProviderCategory.findUnique({
      where: { title }
    });
    
    if (existingCategory) {
      throw new HttpError(400, 'Category with this title already exists');
    }
    
    // Validate parent if provided
    if (parentId) {
      const parentCategory = await context.entities.ProviderCategory.findUnique({
        where: { id: parentId }
      });
      
      if (!parentCategory) {
        throw new HttpError(400, 'Parent category not found');
      }
    }
    
    const category = await context.entities.ProviderCategory.create({
      data: {
        title,
        parentId: parentId || null,
      },
      include: {
        parent: true,
        children: true,
        _count: {
          select: {
            providers: true,
            children: true,
          }
        }
      }
    });
    
    res.status(201).json({
      message: 'Category created successfully',
      category
    });
    
  } catch (error: any) {
    console.error('Create provider category failed:', error);
    
    if (error instanceof HttpError) {
      res.status(error.statusCode || 500).json({
        error: error.message
      });
    } else if (error.name === 'ZodError') {
      res.status(400).json({
        error: 'Invalid input data',
        details: error.errors
      });
    } else {
      res.status(500).json({
        error: 'Failed to create category'
      });
    }
  }
});

export const handleUpdateProviderCategory = withAdminAuth(async (req: any, res: any, context: any) => {
  try {
    const { categoryId } = CategoryIdSchema.parse(req.params);
    const { title, parentId } = UpdateCategorySchema.parse(req.body);
    
    const category = await context.entities.ProviderCategory.findUnique({
      where: { id: categoryId }
    });
    
    if (!category) {
      throw new HttpError(404, 'Category not found');
    }
    
    // Check for circular reference if updating parent
    if (parentId && parentId === categoryId) {
      throw new HttpError(400, 'Category cannot be its own parent');
    }
    
    // Additional circular reference check
    if (parentId) {
      const parentCategory = await context.entities.ProviderCategory.findUnique({
        where: { id: parentId }
      });
      
      if (!parentCategory) {
        throw new HttpError(400, 'Parent category not found');
      }
      
      // Check if the parent is a descendant of the current category
      const isCircular = await checkCircularReference(context, categoryId, parentId);
      if (isCircular) {
        throw new HttpError(400, 'Circular reference detected');
      }
    }
    
    const updatedCategory = await context.entities.ProviderCategory.update({
      where: { id: categoryId },
      data: {
        ...(title && { title }),
        ...(parentId !== undefined && { parentId }),
      },
      include: {
        parent: true,
        children: true,
        _count: {
          select: {
            providers: true,
            children: true,
          }
        }
      }
    });
    
    res.status(200).json({
      message: 'Category updated successfully',
      category: updatedCategory
    });
    
  } catch (error: any) {
    console.error('Update provider category failed:', error);
    
    if (error instanceof HttpError) {
      res.status(error.statusCode || 500).json({
        error: error.message
      });
    } else if (error.name === 'ZodError') {
      res.status(400).json({
        error: 'Invalid input data',
        details: error.errors
      });
    } else {
      res.status(500).json({
        error: 'Failed to update category'
      });
    }
  }
});

export const handleDeleteProviderCategory = withAdminAuth(async (req: any, res: any, context: any) => {
  try {
    const { categoryId } = CategoryIdSchema.parse(req.params);
    
    const category = await context.entities.ProviderCategory.findUnique({
      where: { id: categoryId },
      include: {
        children: true,
        providers: true,
      }
    });
    
    if (!category) {
      throw new HttpError(404, 'Category not found');
    }
    
    if (category.children.length > 0) {
      throw new HttpError(400, 'Cannot delete category with child categories');
    }
    
    if (category.providers.length > 0) {
      throw new HttpError(400, 'Cannot delete category with assigned providers');
    }
    
    await context.entities.ProviderCategory.delete({
      where: { id: categoryId }
    });
    
    res.status(200).json({
      message: 'Category deleted successfully'
    });
    
  } catch (error: any) {
    console.error('Delete provider category failed:', error);
    
    if (error instanceof HttpError) {
      res.status(error.statusCode || 500).json({
        error: error.message
      });
    } else if (error.name === 'ZodError') {
      res.status(400).json({
        error: 'Invalid input data',
        details: error.errors
      });
    } else {
      res.status(500).json({
        error: 'Failed to delete category'
      });
    }
  }
});

/**
 * Helper function to check circular reference in category hierarchy
 */
async function checkCircularReference(context: any, categoryId: number, parentId: number): Promise<boolean> {
  const visited = new Set<number>();
  let currentId = parentId;
  
  while (currentId) {
    if (visited.has(currentId)) {
      return true; // Circular reference detected
    }
    
    if (currentId === categoryId) {
      return true; // Direct circular reference
    }
    
    visited.add(currentId);
    
    const parent = await context.entities.ProviderCategory.findUnique({
      where: { id: currentId },
      select: { parentId: true }
    });
    
    currentId = parent?.parentId || null;
  }
  
  return false;
}

/**
 * Advertisement Management Handlers
 */
export const handleGetAdvertisements = withAdminAuth(async (req: any, res: any, context: any) => {
  try {
    const { page, limit, isActive, search } = GetAdvertisementsQuerySchema.parse(req.query);

    const skip = (page - 1) * limit;

    const whereClause: any = {};

    if (isActive !== undefined) {
      whereClause.isActive = isActive;
    }

    if (search) {
      whereClause.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { subtitle: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    const [advertisements, totalCount] = await Promise.all([
      context.entities.Advertisement.findMany({
        where: whereClause,
        include: {
          backgroundImage: true,
          pngImage: true,
        },
        orderBy: [
          { sortOrder: 'asc' },
          { createdAt: 'desc' }
        ],
        skip,
        take: limit,
      }),
      context.entities.Advertisement.count({
        where: whereClause,
      }),
    ]);

    res.status(200).json({
      success: true,
      data: {
        advertisements,
        pagination: {
          page,
          limit,
          totalCount,
          totalPages: Math.ceil(totalCount / limit),
        }
      }
    });

  } catch (error: any) {
    console.error('Get advertisements failed:', error);

    if (error.name === 'ZodError') {
      res.status(400).json({
        success: false,
        error: 'Invalid query parameters',
        details: error.errors
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve advertisements'
      });
    }
  }
});

export const handleCreateAdvertisement = withAdminAuth(async (req: any, res: any, context: any) => {
  try {
    const validatedData = CreateAdvertisementSchema.parse(req.body);

    // Validate that image IDs exist if provided
    if (validatedData.backgroundImageId) {
      const backgroundImage = await context.entities.File.findUnique({
        where: { id: validatedData.backgroundImageId }
      });
      if (!backgroundImage) {
        return res.status(400).json({
          success: false,
          error: 'Background image not found'
        });
      }
    }

    if (validatedData.pngImageId) {
      const pngImage = await context.entities.File.findUnique({
        where: { id: validatedData.pngImageId }
      });
      if (!pngImage) {
        return res.status(400).json({
          success: false,
          error: 'PNG image not found'
        });
      }
    }

    const advertisement = await context.entities.Advertisement.create({
      data: {
        title: validatedData.title,
        subtitle: validatedData.subtitle,
        description: validatedData.description,
        callToActionText: validatedData.callToActionText,
        callToActionLink: validatedData.callToActionLink,
        isExternal: validatedData.isExternal ?? false,
        isActive: validatedData.isActive ?? true,
        sortOrder: validatedData.sortOrder ?? 0,
        backgroundImageId: validatedData.backgroundImageId,
        pngImageId: validatedData.pngImageId,
      },
      include: {
        backgroundImage: true,
        pngImage: true,
      }
    });

    res.status(201).json({
      success: true,
      message: 'Advertisement created successfully',
      data: advertisement
    });

  } catch (error: any) {
    console.error('Create advertisement failed:', error);

    if (error.name === 'ZodError') {
      res.status(400).json({
        success: false,
        error: 'Invalid request data',
        details: error.errors
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Failed to create advertisement'
      });
    }
  }
});

export const handleUpdateAdvertisement = withAdminAuth(async (req: any, res: any, context: any) => {
  try {
    const { advertisementId } = AdvertisementIdSchema.parse(req.params);
    const validatedData = UpdateAdvertisementSchema.parse(req.body);

    const existingAdvertisement = await context.entities.Advertisement.findUnique({
      where: { id: advertisementId }
    });

    if (!existingAdvertisement) {
      return res.status(404).json({
        success: false,
        error: 'Advertisement not found'
      });
    }

    // Validate that image IDs exist if provided
    if (validatedData.backgroundImageId) {
      const backgroundImage = await context.entities.File.findUnique({
        where: { id: validatedData.backgroundImageId }
      });
      if (!backgroundImage) {
        return res.status(400).json({
          success: false,
          error: 'Background image not found'
        });
      }
    }

    if (validatedData.pngImageId) {
      const pngImage = await context.entities.File.findUnique({
        where: { id: validatedData.pngImageId }
      });
      if (!pngImage) {
        return res.status(400).json({
          success: false,
          error: 'PNG image not found'
        });
      }
    }

    const updateData: any = {};

    if (validatedData.title !== undefined) updateData.title = validatedData.title;
    if (validatedData.subtitle !== undefined) updateData.subtitle = validatedData.subtitle;
    if (validatedData.description !== undefined) updateData.description = validatedData.description;
    if (validatedData.callToActionText !== undefined) updateData.callToActionText = validatedData.callToActionText;
    if (validatedData.callToActionLink !== undefined) updateData.callToActionLink = validatedData.callToActionLink;
    if (validatedData.isExternal !== undefined) updateData.isExternal = validatedData.isExternal;
    if (validatedData.isActive !== undefined) updateData.isActive = validatedData.isActive;
    if (validatedData.sortOrder !== undefined) updateData.sortOrder = validatedData.sortOrder;
    if (validatedData.backgroundImageId !== undefined) updateData.backgroundImageId = validatedData.backgroundImageId;
    if (validatedData.pngImageId !== undefined) updateData.pngImageId = validatedData.pngImageId;

    const advertisement = await context.entities.Advertisement.update({
      where: { id: advertisementId },
      data: updateData,
      include: {
        backgroundImage: true,
        pngImage: true,
      }
    });

    res.status(200).json({
      success: true,
      message: 'Advertisement updated successfully',
      data: advertisement
    });

  } catch (error: any) {
    console.error('Update advertisement failed:', error);

    if (error.name === 'ZodError') {
      res.status(400).json({
        success: false,
        error: 'Invalid request data',
        details: error.errors
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Failed to update advertisement'
      });
    }
  }
});

export const handleDeleteAdvertisement = withAdminAuth(async (req: any, res: any, context: any) => {
  try {
    const { advertisementId } = AdvertisementIdSchema.parse(req.params);

    const advertisement = await context.entities.Advertisement.findUnique({
      where: { id: advertisementId }
    });

    if (!advertisement) {
      return res.status(404).json({
        success: false,
        error: 'Advertisement not found'
      });
    }

    await context.entities.Advertisement.delete({
      where: { id: advertisementId }
    });

    res.status(200).json({
      success: true,
      message: 'Advertisement deleted successfully'
    });

  } catch (error: any) {
    console.error('Delete advertisement failed:', error);

    if (error.name === 'ZodError') {
      res.status(400).json({
        success: false,
        error: 'Invalid request parameters',
        details: error.errors
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Failed to delete advertisement'
      });
    }
  }
});