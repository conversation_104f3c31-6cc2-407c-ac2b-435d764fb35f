/**
 * Database utility functions for admin operations
 */

/**
 * Provider-related database operations
 */
export const ProviderQueries = {
  /**
   * Get provider with full details including user and category
   */
  async getProviderWithDetails(context: any, providerId: number) {
    return await context.entities.SProvider.findUnique({
      where: { id: providerId },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            mobileNumber: true,
            createdAt: true,
            isPhoneVerified: true,
            isEmailVerified: true,
          }
        },
        category: {
          select: {
            id: true,
            title: true,
            parent: {
              select: {
                id: true,
                title: true,
              }
            }
          }
        },
        services: {
          select: {
            id: true,
            title: true,
            isActive: true,
          }
        },
        _count: {
          select: {
            services: true,
            customerFolders: true,
            reviewsReceived: true,
            queues: true,
          }
        }
      }
    });
  },

  /**
   * Get providers with pagination and filtering
   */
  async getProvidersWithPagination(context: any, options: {
    page: number;
    limit: number;
    isVerified?: boolean;
    search?: string;
    categoryId?: number;
    sortBy?: 'newest' | 'oldest' | 'name';
  }) {
    const { page, limit, isVerified, search, categoryId, sortBy = 'newest' } = options;
    const skip = (page - 1) * limit;
    
    const whereClause: any = {};
    
    if (isVerified !== undefined) {
      whereClause.isVerified = isVerified;
    }
    
    if (categoryId) {
      whereClause.providerCategoryId = categoryId;
    }
    
    if (search) {
      whereClause.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { user: { firstName: { contains: search, mode: 'insensitive' } } },
        { user: { lastName: { contains: search, mode: 'insensitive' } } },
        { user: { email: { contains: search, mode: 'insensitive' } } },
        { presentation: { contains: search, mode: 'insensitive' } },
      ];
    }
    
    const orderBy = sortBy === 'name' 
      ? { title: 'asc' }
      : sortBy === 'oldest' 
        ? { createdAt: 'asc' }
        : { createdAt: 'desc' };
    
    const [providers, totalCount] = await Promise.all([
      context.entities.SProvider.findMany({
        where: whereClause,
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              mobileNumber: true,
              createdAt: true,
              isPhoneVerified: true,
              isEmailVerified: true,
            }
          },
          category: {
            select: {
              id: true,
              title: true,
            }
          },
          _count: {
            select: {
              services: true,
              customerFolders: true,
              reviewsReceived: true,
            }
          }
        },
        skip,
        take: limit,
        orderBy,
      }),
      context.entities.SProvider.count({ where: whereClause })
    ]);
    
    return {
      providers,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        hasNextPage: page < Math.ceil(totalCount / limit),
        hasPreviousPage: page > 1,
      }
    };
  },

  /**
   * Get provider statistics
   */
  async getProviderStats(context: any) {
    const [
      totalProviders,
      verifiedProviders,
      pendingProviders,
      providersWithServices,
      recentProviders
    ] = await Promise.all([
      context.entities.SProvider.count(),
      context.entities.SProvider.count({ where: { isVerified: true } }),
      context.entities.SProvider.count({ where: { isVerified: false } }),
      context.entities.SProvider.count({ where: { services: { some: {} } } }),
      context.entities.SProvider.count({
        where: {
          createdAt: {
            gte: new Date(new Date().getTime() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
          }
        }
      })
    ]);
    
    return {
      totalProviders,
      verifiedProviders,
      pendingProviders,
      providersWithServices,
      recentProviders,
      verificationRate: totalProviders > 0 ? (verifiedProviders / totalProviders) * 100 : 0,
    };
  }
};

/**
 * Customer-related database operations
 */
export const CustomerQueries = {
  /**
   * Get customers with pagination and filtering
   */
  async getCustomersWithPagination(context: any, options: {
    page: number;
    limit: number;
    search?: string;
    isVerified?: boolean;
    sortBy?: 'newest' | 'oldest' | 'name';
  }) {
    const { page, limit, search, isVerified, sortBy = 'newest' } = options;
    const skip = (page - 1) * limit;
    
    const whereClause: any = {
      OR: [
        { role: 'CUSTOMER' },
        { customerFolders: { some: {} } },
      ]
    };
    
    if (isVerified !== undefined) {
      whereClause.AND = [
        { OR: whereClause.OR },
        { isEmailVerified: isVerified }
      ];
      delete whereClause.OR;
    }
    
    if (search) {
      const searchCondition = {
        OR: [
          { firstName: { contains: search, mode: 'insensitive' } },
          { lastName: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } },
          { mobileNumber: { contains: search, mode: 'insensitive' } },
        ]
      };
      
      if (whereClause.AND) {
        whereClause.AND.push(searchCondition);
      } else {
        whereClause.AND = [
          { OR: whereClause.OR },
          searchCondition
        ];
        delete whereClause.OR;
      }
    }
    
    const orderBy = sortBy === 'name' 
      ? { firstName: 'asc' }
      : sortBy === 'oldest' 
        ? { createdAt: 'asc' }
        : { createdAt: 'desc' };
    
    const [customers, totalCount] = await Promise.all([
      context.entities.User.findMany({
        where: whereClause,
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          mobileNumber: true,
          createdAt: true,
          isPhoneVerified: true,
          isEmailVerified: true,
          role: true,
          _count: {
            select: {
              customerFolders: true,
            }
          }
        },
        skip,
        take: limit,
        orderBy,
      }),
      context.entities.User.count({ where: whereClause })
    ]);
    
    return {
      customers,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        hasNextPage: page < Math.ceil(totalCount / limit),
        hasPreviousPage: page > 1,
      }
    };
  },

  /**
   * Get customer statistics
   */
  async getCustomerStats(context: any) {
    const [
      totalCustomers,
      verifiedCustomers,
      activeCustomers,
      recentCustomers
    ] = await Promise.all([
      context.entities.User.count({
        where: {
          OR: [
            { role: 'CUSTOMER' },
            { customerFolders: { some: {} } },
          ]
        }
      }),
      context.entities.User.count({
        where: {
          OR: [
            { role: 'CUSTOMER' },
            { customerFolders: { some: {} } },
          ],
          isEmailVerified: true
        }
      }),
      context.entities.User.count({
        where: {
          customerFolders: { some: {} }
        }
      }),
      context.entities.User.count({
        where: {
          OR: [
            { role: 'CUSTOMER' },
            { customerFolders: { some: {} } },
          ],
          createdAt: {
            gte: new Date(new Date().getTime() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
          }
        }
      })
    ]);
    
    return {
      totalCustomers,
      verifiedCustomers,
      activeCustomers,
      recentCustomers,
      verificationRate: totalCustomers > 0 ? (verifiedCustomers / totalCustomers) * 100 : 0,
    };
  }
};

/**
 * Category-related database operations
 */
export const CategoryQueries = {
  /**
   * Get category with full hierarchy
   */
  async getCategoryWithHierarchy(context: any, categoryId: number) {
    return await context.entities.ProviderCategory.findUnique({
      where: { id: categoryId },
      include: {
        parent: {
          include: {
            parent: true // Include grandparent
          }
        },
        children: {
          include: {
            children: true, // Include grandchildren
            _count: {
              select: {
                providers: true,
              }
            }
          }
        },
        providers: {
          select: {
            id: true,
            title: true,
            isVerified: true,
            user: {
              select: {
                firstName: true,
                lastName: true,
                email: true,
              }
            }
          }
        },
        _count: {
          select: {
            providers: true,
            children: true,
          }
        }
      }
    });
  },

  /**
   * Get all categories with hierarchy structure
   */
  async getCategoriesWithHierarchy(context: any) {
    const categories = await context.entities.ProviderCategory.findMany({
      include: {
        parent: true,
        children: {
          include: {
            children: true,
            _count: {
              select: {
                providers: true,
              }
            }
          }
        },
        _count: {
          select: {
            providers: true,
            children: true,
          }
        }
      },
      orderBy: [
        { parentId: 'asc' }, // Group by parent
        { title: 'asc' }     // Sort by name within groups
      ]
    });
    
    // Organize into tree structure
    const categoryMap = new Map<number, any>();
    const rootCategories: any[] = [];
    
    categories.forEach((category: any) => {
      categoryMap.set(category.id, { ...category, children: [] });
    });
    
    categories.forEach((category: any) => {
      const categoryWithChildren = categoryMap.get(category.id);
      if (category.parentId) {
        const parent = categoryMap.get(category.parentId);
        if (parent) {
          parent.children.push(categoryWithChildren);
        }
      } else {
        rootCategories.push(categoryWithChildren);
      }
    });
    
    return rootCategories;
  },

  /**
   * Check if category can be safely deleted
   */
  async canDeleteCategory(context: any, categoryId: number) {
    const category = await context.entities.ProviderCategory.findUnique({
      where: { id: categoryId },
      include: {
        children: true,
        providers: true,
      }
    });
    
    if (!category) {
      return { canDelete: false, reason: 'Category not found' };
    }
    
    if (category.children.length > 0) {
      return { canDelete: false, reason: 'Category has child categories' };
    }
    
    if (category.providers.length > 0) {
      return { canDelete: false, reason: 'Category has assigned providers' };
    }
    
    return { canDelete: true };
  },

  /**
   * Get category statistics
   */
  async getCategoryStats(context: any) {
    const [
      totalCategories,
      rootCategories,
      categoriesWithProviders,
      categoriesWithChildren
    ] = await Promise.all([
      context.entities.ProviderCategory.count(),
      context.entities.ProviderCategory.count({ where: { parentId: null } }),
      context.entities.ProviderCategory.count({ where: { providers: { some: {} } } }),
      context.entities.ProviderCategory.count({ where: { children: { some: {} } } })
    ]);
    
    return {
      totalCategories,
      rootCategories,
      categoriesWithProviders,
      categoriesWithChildren,
      averageProvidersPerCategory: totalCategories > 0 ? categoriesWithProviders / totalCategories : 0,
    };
  }
};

/**
 * Admin user operations
 */
export const AdminQueries = {
  /**
   * Get admin user statistics
   */
  async getAdminStats(context: any) {
    const [
      totalAdmins,
      activeAdmins,
      recentAdmins
    ] = await Promise.all([
      context.entities.User.count({ where: { role: 'ADMIN' } }),
      context.entities.User.count({ where: { role: 'ADMIN', isEmailVerified: true } }),
      context.entities.User.count({
        where: {
          role: 'ADMIN',
          createdAt: {
            gte: new Date(new Date().getTime() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
          }
        }
      })
    ]);
    
    return {
      totalAdmins,
      activeAdmins,
      recentAdmins,
    };
  },

  /**
   * Check if email is already taken
   */
  async isEmailTaken(context: any, email: string) {
    const user = await context.entities.User.findUnique({
      where: { email }
    });
    return !!user;
  }
}; 