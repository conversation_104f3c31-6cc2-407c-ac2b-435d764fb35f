import { type AuthUser } from 'wasp/auth';
import AccountPage from '../../../user/AccountPage';
import Breadcrumb from '../../layout/Breadcrumb';
import DefaultLayout from '../../layout/DefaultLayout';
import { useRedirectHomeUnlessUserIsAdmin } from '../../useRedirectHomeUnlessUserIsAdmin';

const UserAccountPage = ({ user }: { user: AuthUser }) => {
  useRedirectHomeUnlessUserIsAdmin({user})

  return (
    <DefaultLayout user={user}>
      <Breadcrumb pageName='User Account' />
      <div className='flex flex-col gap-10'>
        <AccountPage user={user} />
      </div>
    </DefaultLayout>
  );
};

export default UserAccountPage;
