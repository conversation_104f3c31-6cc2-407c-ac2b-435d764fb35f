import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { useQuery, useAction } from 'wasp/client/operations';
import { 
  getAppointments, 
  updateAppointment, 
  completeAppointment, 
  noShowAppointment,
  getOverflowedAppointments,
  getRescheduleRequests,
  handleOverflowedAppointment,
  respondToRescheduleRequest,
  getQueueSwapRequests,
  requestQueueSwap,
  finalizeQueueSwap,
  getProviderAvailability,
  closeEarlyForToday,
  createRescheduleRequest
} from 'wasp/client/operations';
import { type Appointment, type User as WaspUser, type Service, type Queue, type QueueSwapRequest as WaspQueueSwapRequest } from 'wasp/entities';
import { type AuthUser } from 'wasp/auth';
import { type GetAppointments, type GetOverflowedAppointments, type GetRescheduleRequests, type GetQueueSwapRequests } from 'wasp/server/operations';
import DefaultLayout from '../../layout/DefaultLayout';
import Breadcrumb from '../../layout/Breadcrumb';
import { Spin, Alert, Table, Tag, Button, Space, Empty, message, Modal, List, Typography, Select, Badge, Descriptions, Row, Col, Collapse } from 'antd';
import { 
  CheckCircleOutlined, 
  CloseCircleOutlined, 
  CheckOutlined, 
  StopOutlined, 
  UserOutlined, 
  ClockCircleOutlined, 
  EnvironmentOutlined, 
  ToolOutlined, 
  CalendarOutlined, 
  PlusOutlined,
  WarningOutlined,
  SwapOutlined,
  ClockCircleTwoTone
} from '@ant-design/icons';
import { formatDate } from '../../../utils/dateUtils';
import dayjs, { Dayjs } from 'dayjs';
import isToday from 'dayjs/plugin/isToday';
import isBetween from 'dayjs/plugin/isBetween';
import Card from 'antd/es/card/Card';
import UpcomingAppointmentCard from './UpcomingAppointmentCard';
import NewAppointmentModal from '../../elements/calendar/NewAppointmentModal';
import { Divider, Input } from 'antd';
import { DatePicker } from 'antd';

dayjs.extend(isToday);
dayjs.extend(isBetween);

const { Text } = Typography;

// Define UI-specific types
interface AppointmentUI {
  id: number;
  status: string;
  serviceId: number;
  placeId: number;
  queueId: number | null;
  notes: string | null;
  expectedAppointmentStartTime: Date | null;
  expectedAppointmentEndTime: Date | null;
  isOverflowed: boolean;
  overflowReason: string | null;
  overflowProcessingStatus: string | null;
  overflowDetectedAt: Date | null;
  overflowProcessedAt: Date | null;
  
  service?: {
    id: number;
    title: string;
    duration: number;
    createdAt: Date;
    updatedAt: Date;
    color: string | null;
    sProviderId: number;
    serviceCategoryId: number | null;
    minDuration: number | null;
    maxDuration: number | null;
    queue: number | null;
    acceptOnline: boolean;
    acceptNew: boolean;
    notificationOn: boolean;
    pointsRequirements: number;
    // New fields (optional until migration is complete)
    price?: number | null;
    isPublic?: boolean;
    deliveryType?: string | null;
    servedRegions?: string | null;
  };
  customerFolder?: {
    userId?: string;
    customer?: {
      id: string;
      firstName?: string | null;
      lastName?: string | null;
    };
  };
  place?: {
    id: number;
    name: string;
  };
  queue?: {
    id: number;
    title: string;
  } | null;
}

interface RescheduleRequestUI {
  id: number;
  status: string;
  appointment: AppointmentUI;
  suggestedStartTime: Date;
  suggestedEndTime: Date;
  reason?: string;
}

// --- UI Type for Queue Swap Request ---
interface QueueSwapRequestUI {
  id: number;
  appointment1Id: number;
  appointment2Id: number;
  requestedById: string;
  status: string;
  expiresAt?: Date | null;
  notes?: string | null;
  appointment1?: AppointmentUI; // Optional, assuming backend populates this
  appointment2?: AppointmentUI; // Optional
  requestedBy?: { firstName?: string | null; lastName?: string | null; }; // Optional
}
// --- End UI Type for Queue Swap Request ---

// Helper function to convert Wasp appointment to UI appointment
const toAppointmentUI = (appointment: any): AppointmentUI => ({
  id: appointment.id,
  status: appointment.status,
  serviceId: appointment.serviceId,
  placeId: appointment.placeId,
  queueId: appointment.queueId,
  notes: appointment.notes,
  expectedAppointmentStartTime: appointment.expectedAppointmentStartTime,
  expectedAppointmentEndTime: appointment.expectedAppointmentEndTime,
  isOverflowed: appointment.isOverflowed || false,
  overflowReason: appointment.overflowReason || null,
  overflowProcessingStatus: appointment.overflowProcessingStatus || null,
  overflowDetectedAt: appointment.overflowDetectedAt || null,
  overflowProcessedAt: appointment.overflowProcessedAt || null,
  
  service: appointment.service ? {
    id: appointment.service.id,
    title: appointment.service.title,
    duration: appointment.service.duration || 0,
    createdAt: appointment.service.createdAt,
    updatedAt: appointment.service.updatedAt,
    color: appointment.service.color,
    sProviderId: appointment.service.sProviderId,
    serviceCategoryId: appointment.service.serviceCategoryId,
    minDuration: appointment.service.minDuration || null,
    maxDuration: appointment.service.maxDuration || null,
    queue: appointment.service.queue || null,
    acceptOnline: appointment.service.acceptOnline || false,
    acceptNew: appointment.service.acceptNew || false,
    notificationOn: appointment.service.notificationOn || false,
    pointsRequirements: appointment.service.pointsRequirements || 0,
    // New optional fields (will be undefined until migration)
    price: (appointment.service as any).price || null,
    isPublic: (appointment.service as any).isPublic !== undefined ? (appointment.service as any).isPublic : true,
    deliveryType: (appointment.service as any).deliveryType || null,
    servedRegions: (appointment.service as any).servedRegions || null,
  } : undefined,
  
  customerFolder: appointment.customerFolder,
  place: appointment.place && {
    id: appointment.place.id,
    name: appointment.place.name,
  },
  queue: appointment.queue && {
    id: appointment.queue.id,
    title: appointment.queue.title,
  },
});

// Add interface for the overflowed appointment
interface OverflowActionModalProps {
  appointment: AppointmentUI | null;
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

// Update RescheduleRequest interface to use the correct appointment type
interface RescheduleRequest {
  id: number;
  status: string;
  appointment: AppointmentUI;
  suggestedStartTime: Date;
  suggestedEndTime: Date;
  reason?: string;
}

interface RescheduleRequestCardProps {
  request: RescheduleRequestUI;
  onRespond: () => void;
}

// Update the OverflowActionModal component with proper types
const OverflowActionModal: React.FC<OverflowActionModalProps> = ({ 
  appointment, 
  visible, 
  onClose, 
  onSuccess 
}) => {
  const [loading, setLoading] = useState(false);
  const [reason, setReason] = useState('');
  const [selectedSlot, setSelectedSlot] = useState<{ startTime: string; queueId: number } | null>(null);
  const handleOverflowedAppointmentAction = useAction(handleOverflowedAppointment);

  // Fetch slots using useQuery (getProviderAvailability)
  const {
    data: availableSlots = [],
    isLoading: slotsLoading,
    error: slotsError,
  } = useQuery(
    getProviderAvailability,
    appointment && visible
      ? {
          sProvidingPlaceId: appointment.placeId,
          serviceId: appointment.serviceId,
          startDate: dayjs().format('YYYY-MM-DD'),
          endDate: dayjs().add(7, 'day').format('YYYY-MM-DD'),
        }
      : undefined,
    { enabled: !!appointment && visible }
  );

  useEffect(() => {
    if (visible) {
      setSelectedSlot(null);
      setReason('');
    }
  }, [visible, appointment]);

  const handleAction = async (action: 'cancel' | 'confirm_overtime' | 'request_reschedule') => {
    if (!appointment) return;
    
    setLoading(true);
    try {
      let suggestedStartTime: any = undefined;
      let suggestedEndTime: any = undefined;
      let queueId: any = undefined;
      if (action === 'request_reschedule') {
        if (!selectedSlot) {
          message.error('Please select a slot for rescheduling');
          setLoading(false);
          return;
        }
        suggestedStartTime = dayjs(selectedSlot.startTime).toDate();
        suggestedEndTime = dayjs(selectedSlot.startTime).add(appointment.service?.duration || 0, 'minute').toDate();
        queueId = selectedSlot.queueId;
      }
      await handleOverflowedAppointmentAction({
        appointmentId: appointment.id,
        action,
        reason,
        suggestedStartTime,
        suggestedEndTime,
      });
      message.success('Appointment handled successfully');
      onSuccess();
      onClose();
    } catch (error) {
      message.error(error instanceof Error ? error.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title="Handle Overflowed Appointment"
      open={visible}
      onCancel={onClose}
      footer={null}
    >
      <Space direction="vertical" className="w-full">
        <Alert
          message="This appointment has been shifted outside of queue hours"
          description="Please choose how to handle this situation:"
          type="warning"
          showIcon
        />
        
        <Button 
          block 
          type="primary" 
          onClick={() => handleAction('confirm_overtime')}
          loading={loading}
        >
          Confirm Overtime Work
        </Button>

        <Button 
          block 
          danger 
          onClick={() => handleAction('cancel')}
          loading={loading}
        >
          Cancel Appointment (Will lose credits)
        </Button>

        <Divider>OR</Divider>

        <div>
          <Typography.Title level={5}>Suggest New Time</Typography.Title>
          {/* Render available slots grouped by day using Collapse */}
          {slotsLoading ? <Spin /> : slotsError ? (
            <div style={{ color: 'red' }}>{slotsError.message || String(slotsError)}</div>
          ) : !availableSlots.length ? (
            <div>No available slots found.</div>
          ) : (
            <Collapse accordion>
              {availableSlots.map((day: any) => (
                <Collapse.Panel header={dayjs(day.date).format('dddd, MMM D')} key={day.date}>
                  <Space wrap>
                    {day.slots
                      .filter((slot: any) => !slot.isBooked)
                      .map((slot: any) => (
                        <Button
                          key={slot.startTime + '-' + slot.queueId}
                          type={selectedSlot && selectedSlot.startTime === slot.startTime && selectedSlot.queueId === slot.queueId ? 'primary' : 'default'}
                          onClick={() => setSelectedSlot({ startTime: slot.startTime, queueId: slot.queueId })}
                        >
                          {dayjs(slot.startTime).format('HH:mm')} (Queue {slot.queueId})
                        </Button>
                      ))}
                  </Space>
                </Collapse.Panel>
              ))}
            </Collapse>
          )}
          <Input.TextArea
            rows={4}
            placeholder="Reason for rescheduling"
            value={reason}
            onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setReason(e.target.value)}
            className="mt-2"
          />
          <Button 
            block 
            type="primary" 
            ghost
            className="mt-2"
            onClick={() => handleAction('request_reschedule')}
            loading={loading}
            disabled={!selectedSlot || !reason}
          >
            Send Reschedule Request
          </Button>
        </div>
      </Space>
    </Modal>
  );
};

// Update the RescheduleRequestCard component with proper types
const RescheduleRequestCard: React.FC<RescheduleRequestCardProps> = ({ request, onRespond }) => {
  const respondToRescheduleRequestAction = useAction(respondToRescheduleRequest);
  const [loading, setLoading] = useState(false);

  const handleResponse = async (accept: boolean) => {
    setLoading(true);
    try {
      await respondToRescheduleRequestAction({
        requestId: request.id,
        accept
      });
      message.success(`Request ${accept ? 'accepted' : 'rejected'}`);
      onRespond();
    } catch (error) {
      message.error(error instanceof Error ? error.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card size="small" className="mb-2">
      <Space direction="vertical" className="w-full">
        <div className="flex justify-between">
          <Text>{request.appointment.service?.title || 'Unknown Service'}</Text>
          <Tag color={request.status === 'pending' ? 'gold' : request.status === 'accepted' ? 'green' : 'red'}>
            {request.status}
          </Tag>
        </div>
        <Text type="secondary">
          From: {dayjs(request.appointment.expectedAppointmentStartTime).format('MMM D, HH:mm')}
        </Text>
        <Text type="secondary">
          To: {dayjs(request.suggestedStartTime).format('MMM D, HH:mm')}
        </Text>
        {request.reason && <Text type="secondary">Reason: {request.reason}</Text>}
        
        {request.status === 'pending' && (
          <Space className="w-full justify-end">
            <Button 
              size="small" 
              type="primary" 
              onClick={() => handleResponse(true)}
              loading={loading}
            >
              Accept
            </Button>
            <Button 
              size="small" 
              danger 
              onClick={() => handleResponse(false)}
              loading={loading}
            >
              Reject
            </Button>
          </Space>
        )}
      </Space>
    </Card>
  );
};

// Add type for the query responses
type GetAppointmentsResponse = AppointmentUI[];
type GetOverflowedAppointmentsResponse = AppointmentUI[];
type GetRescheduleRequestsResponse = RescheduleRequestUI[];

// Define proper query result types
type AppointmentsQueryResult = {
  data: AppointmentUI[] | undefined;
  isLoading: boolean;
  error: Error | null;
  refetch: () => void;
};

type OverflowedAppointmentsQueryResult = {
  data: AppointmentUI[] | undefined;
  refetch: () => void;
};

type RescheduleRequestsQueryResult = {
  data: RescheduleRequestUI[] | undefined;
  refetch: () => void;
};

// Update component props
interface AppointmentsPageProps {
  user: AuthUser;
}

const AppointmentsPage: React.FC<AppointmentsPageProps> = ({ user }) => {
  // Update query types to use UI types
  const { 
    data: rawAppointments = [], 
    isLoading, 
    error, 
    refetch: refetchAppointments 
  } = useQuery(getAppointments);

  const appointments = useMemo(() => 
    (rawAppointments as Appointment[]).map(toAppointmentUI),
    [rawAppointments]
  );

  const { 
    data: rawOverflowedAppointments = [], 
    refetch: refetchOverflowed 
  } = useQuery(getOverflowedAppointments);

  const overflowedAppointments = useMemo(() => 
    (rawOverflowedAppointments as Appointment[]).map(toAppointmentUI),
    [rawOverflowedAppointments]
  );

  const { 
    data: rawRescheduleRequests = [], 
    refetch: refetchRequests 
  } = useQuery(getRescheduleRequests);

  // --- Query for Queue Swap Requests ---
  const {
    data: rawQueueSwapRequests = [],
    isLoading: isLoadingQueueSwaps,
    error: errorQueueSwaps,
    refetch: refetchQueueSwaps,
  } = useQuery(getQueueSwapRequests);
  // --- End Query for Queue Swap Requests ---

  console.log('rawQueueSwapRequests', rawQueueSwapRequests);
  const rescheduleRequests = useMemo(() => 
    (rawRescheduleRequests as any[]).map((req: any) => ({
      ...req,
      appointment: toAppointmentUI(req.appointment),
    })) as RescheduleRequestUI[],
    [rawRescheduleRequests]
  );

  // --- Memoized Queue Swap Requests ---
  const queueSwapRequests = useMemo(() => {
    return (rawQueueSwapRequests as (WaspQueueSwapRequest & { 
      appointment1?: Appointment, 
      appointment2?: Appointment, 
      requestedBy?: { id: string, firstName?: string | null, lastName?: string | null }
    })[]).map(req => ({
      ...req,
      appointment1: req.appointment1 ? toAppointmentUI(req.appointment1) : undefined,
      appointment2: req.appointment2 ? toAppointmentUI(req.appointment2) : undefined,
      // requestedBy might need specific mapping if its structure is different
    })) as QueueSwapRequestUI[];
  }, [rawQueueSwapRequests]);
  // --- End Memoized Queue Swap Requests ---

  // Get the action functions from useAction
  const updateAppointmentAction = useAction(updateAppointment);
  const completeAppointmentAction = useAction(completeAppointment);
  const noShowAppointmentAction = useAction(noShowAppointment);
  const finalizeQueueSwapAction = useAction(finalizeQueueSwap);
  const requestQueueSwapAction = useAction(requestQueueSwap);

  const [isOverallSubmitting, setIsOverallSubmitting] = useState(false); // Renamed from isSubmitting
  // Separate state for each filter
  const [selectedPendingServiceId, setSelectedPendingServiceId] = useState<number | undefined>(undefined);
  const [selectedUpcomingServiceId, setSelectedUpcomingServiceId] = useState<number | undefined>(undefined);
  const [isNewModalOpen, setIsNewModalOpen] = useState(false);

  const [selectedOverflowedAppointment, setSelectedOverflowedAppointment] = 
    useState<AppointmentUI | null>(null);

  // --- Early Closure State ---
  const [isEarlyCloseModalOpen, setIsEarlyCloseModalOpen] = useState(false);
  const [isEarlyCloseSubmitting, setIsEarlyCloseSubmitting] = useState(false);
  const closeEarlyForTodayAction = useAction(closeEarlyForToday);

  // --- Early Close Reschedule Modal State ---
  const [isRescheduleModalOpen, setIsRescheduleModalOpen] = useState(false);
  const [rescheduleReason, setRescheduleReason] = useState('');
  const [rescheduleStatus, setRescheduleStatus] = useState<{ [appointmentId: number]: 'pending' | 'success' | 'error' }>({});
  const [activeRescheduleId, setActiveRescheduleId] = useState<number | null>(null);
  const [slotPickers, setSlotPickers] = useState<{ [appointmentId: number]: boolean }>({});
  const createRescheduleRequestAction = useAction(createRescheduleRequest);

  // --- Slot Picker Modal State ---
  const [slotPickerModalOpen, setSlotPickerModalOpen] = useState(false);
  const [slotPickerAppointment, setSlotPickerAppointment] = useState<AppointmentUI | null>(null);
  const [slotPickerSelectedSlot, setSlotPickerSelectedSlot] = useState<{ startTime: string; queueId: number } | null>(null);
  const [slotPickerLoading, setSlotPickerLoading] = useState(false);

  // Helper: Get today's date range
  const todayStart = dayjs().startOf('day');
  const todayEnd = dayjs().endOf('day');

  // Helper: Filter today's remaining appointments (pending/confirmed/InProgress, start time after now)
  const remainingAppointmentsToday = useMemo(() => {
    return appointments.filter(appt => {
      const start = appt.expectedAppointmentStartTime ? dayjs(appt.expectedAppointmentStartTime) : null;
      return (
        start &&
        start.isAfter(dayjs()) &&
        start.isBefore(todayEnd) &&
        ['pending', 'confirmed', 'InProgress'].includes(appt.status)
      );
    });
  }, [appointments, todayEnd]);

  // --- Early Close Modal Handlers ---
  const handleEarlyCloseCancelAll = async () => {
    setIsEarlyCloseSubmitting(true);
    try {
      await closeEarlyForTodayAction({ actionType: 'cancel_all' });
      message.success('All remaining appointments for today have been cancelled.');
      setIsEarlyCloseModalOpen(false);
      refetchAll();
    } catch (err: any) {
      message.error(err.message || 'Failed to cancel appointments.');
    } finally {
      setIsEarlyCloseSubmitting(false);
    }
  };

  // --- Early Close Reschedule Handlers ---
  const handleOpenRescheduleModal = () => {
    setIsEarlyCloseModalOpen(false);
    setIsRescheduleModalOpen(true);
    setRescheduleReason('');
    setRescheduleStatus({});
    setSlotPickers({});
  };

  const handleOpenSlotPickerModal = (appointment: AppointmentUI) => {
    setSlotPickerAppointment(appointment);
    setSlotPickerSelectedSlot(null);
    setSlotPickerModalOpen(true);
  };

  const handleConfirmSlotPicker = async () => {
    if (!slotPickerAppointment || !slotPickerSelectedSlot) return;
    setSlotPickerLoading(true);
    setRescheduleStatus(prev => ({ ...prev, [slotPickerAppointment.id]: 'pending' }));
    try {
      await createRescheduleRequestAction({
        appointmentId: slotPickerAppointment.id,
        suggestedStartTime: dayjs(slotPickerSelectedSlot.startTime).toDate(),
        suggestedEndTime: dayjs(slotPickerSelectedSlot.startTime).add(slotPickerAppointment.service?.duration || 0, 'minute').toDate(),
        reason: rescheduleReason,
      });
      setRescheduleStatus(prev => ({ ...prev, [slotPickerAppointment.id]: 'success' }));
      message.success(`Reschedule request sent for appointment #${slotPickerAppointment.id}`);
      setSlotPickerModalOpen(false);
      setSlotPickerAppointment(null);
      setSlotPickerSelectedSlot(null);
    } catch (err: any) {
      setRescheduleStatus(prev => ({ ...prev, [slotPickerAppointment.id]: 'error' }));
      message.error(err.message || 'Failed to reschedule appointment.');
    } finally {
      setSlotPickerLoading(false);
    }
  };
  const handleCancelSlotPicker = () => {
    setSlotPickerModalOpen(false);
    setSlotPickerAppointment(null);
    setSlotPickerSelectedSlot(null);
  };

  const allRescheduled = remainingAppointmentsToday.length > 0 && remainingAppointmentsToday.every(appt => rescheduleStatus[appt.id] === 'success');

  // Define refetch function first
  const refetchAll = useCallback(() => {
    refetchAppointments();
    refetchOverflowed();
    refetchRequests();
    refetchQueueSwaps();
  }, [refetchAppointments, refetchOverflowed, refetchRequests, refetchQueueSwaps]);

  // Update handlers with proper types and null checks
  const handleConfirm = useCallback((appointment: AppointmentUI) => {
    const customerId = appointment.customerFolder?.customer?.id;
    if (!customerId) {
      message.error('Cannot update: Missing customer information');
      return;
    }

    Modal.confirm({
      title: 'Confirm Appointment',
      content: 'Are you sure you want to confirm this appointment?',
      onOk: async () => { 
        try {
          await updateAppointmentAction({
            appointmentId: appointment.id,
            status: 'confirmed',
            placeId: appointment.placeId,
            serviceId: appointment.serviceId,
            customerUserId: customerId,
            startTime: appointment.expectedAppointmentStartTime || new Date(),
            endTime: appointment.expectedAppointmentEndTime || new Date(),
            notes: appointment.notes,
            queueId: appointment.queueId || undefined,
          });
          message.success('Appointment confirmed successfully');
          refetchAll();
        } catch (error) {
          message.error(error instanceof Error ? error.message : 'Failed to confirm appointment');
        }
      },
    });
  }, [updateAppointmentAction, refetchAll]);

  const handleCancel = useCallback((appointment: AppointmentUI) => {
    const customerId = appointment.customerFolder?.customer?.id;
    if (!customerId) {
      message.error('Cannot update: Missing customer information');
      return;
    }

     Modal.confirm({
      title: 'Cancel Appointment',
      content: 'Are you sure you want to cancel this appointment?',
      onOk: async () => { 
        try {
          await updateAppointmentAction({
          appointmentId: appointment.id,
            status: 'canceled',
            placeId: appointment.placeId,
          serviceId: appointment.serviceId,
            customerUserId: customerId,
            startTime: appointment.expectedAppointmentStartTime || new Date(),
            endTime: appointment.expectedAppointmentEndTime || new Date(),
          notes: appointment.notes,
            queueId: appointment.queueId || undefined,
          });
          message.success('Appointment cancelled successfully');
          refetchAll();
        } catch (error) {
          message.error(error instanceof Error ? error.message : 'Failed to cancel appointment');
        }
      },
    });
  }, [updateAppointmentAction, refetchAll]);

  const handleNoShow = useCallback((appointment: AppointmentUI): void => {
    Modal.confirm({
      title: 'Mark as No Show',
      content: 'Are you sure you want to mark this appointment as no show?',
      onOk: async () => {
        try {
          await noShowAppointmentAction({
            appointmentId: appointment.id,
          });
          message.success('Appointment marked as no show');
           refetchAppointments();
        } catch (error) {
          message.error(error instanceof Error ? error.message : 'Failed to mark as no show');
        }
      },
    });
  }, [noShowAppointmentAction, refetchAppointments]);

  const handleComplete = (appointmentId: number) => {
     Modal.confirm({
      title: 'Complete Appointment',
      content: `Are you sure you want to mark this appointment as completed? (Customer credits will be refunded)`,
      onOk: async () => { 
        if (isOverallSubmitting) return;
        setIsOverallSubmitting(true);
        try {
          await completeAppointmentAction({ appointmentId }); 
          message.success('Appointment marked as completed!');
          refetchAppointments();
        } catch (err: any) {
          message.error(`Error completing appointment: ${err.message}`);
        } finally {
           setIsOverallSubmitting(false);
        }
      },
      confirmLoading: isOverallSubmitting,
    });
  };

  // --- Define handleFinalizeSwap ---
  const handleFinalizeSwap = useCallback((requestId: number) => {
     Modal.confirm({
      title: 'Finalize Queue Swap',
      content: 'Are you sure you want to finalize this queue swap? This will exchange the appointment times.',
      okText: 'Finalize',
      cancelText: 'Cancel',
      onOk: async () => {
        try {
          await finalizeQueueSwapAction({ swapRequestId: requestId });
          message.success('Queue swap finalized successfully!');
          refetchAll();
        } catch (err: any) {
          message.error(err.message || 'Failed to finalize queue swap.');
        }
      },
    });
  }, [finalizeQueueSwapAction, refetchAll]);
  // --- End define handleFinalizeSwap ---

  // Update the table columns to use AppointmentUI
  const columns = [
    {
      title: 'Date',
      dataIndex: 'expectedAppointmentStartTime',
      key: 'date',
      render: (text: Date | string) => text ? formatDate(text) : '-',
      sorter: (a: AppointmentUI, b: AppointmentUI) => 
        dayjs(a.expectedAppointmentStartTime!).unix() - dayjs(b.expectedAppointmentStartTime!).unix(),
      defaultSortOrder: 'ascend' as const,
    },
    {
      title: 'Time',
      key: 'time',
      render: (_: any, record: AppointmentUI) =>
        record.expectedAppointmentStartTime && record.expectedAppointmentEndTime
        ? `${formatDate(record.expectedAppointmentStartTime, { timeStyle: 'short' })} - ${formatDate(record.expectedAppointmentEndTime, { timeStyle: 'short' })}`
        : '-',
    },
    {
      title: 'Customer',
      key: 'customer',
      render: (_: any, record: AppointmentUI) => 
        `${record.customerFolder?.customer?.firstName || ''} ${record.customerFolder?.customer?.lastName || ''}`.trim() || '-',
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: any, record: AppointmentUI) => (
        <Space size="middle">
          <Button 
            type="primary" 
            icon={<CheckCircleOutlined />} 
            onClick={() => handleConfirm(record)} 
            size="small" 
            loading={isOverallSubmitting}
          >
            Confirm
          </Button>
          <Button 
            danger 
            icon={<CloseCircleOutlined />} 
            onClick={() => handleCancel(record)} 
            size="small" 
            loading={isOverallSubmitting}
          >
            Cancel
          </Button>
        </Space>
      ),
    },
  ];

  // Update the filter functions to use AppointmentUI
  const filterByService = (appointments: AppointmentUI[], serviceId?: number) => {
    return appointments.filter(appt => 
      serviceId === undefined || appt.serviceId === serviceId
    );
  };

  const filterByStatus = (appointments: AppointmentUI[], status: string | string[]) => {
    const statusArray = Array.isArray(status) ? status : [status];
    return appointments.filter(appt => statusArray.includes(appt.status));
  };

  const sortByStartTime = (appointments: AppointmentUI[]) => {
    return [...appointments].sort((a, b) => 
      dayjs(a.expectedAppointmentStartTime!).unix() - dayjs(b.expectedAppointmentStartTime!).unix()
    );
  };

  const now = dayjs();

  // Update array operations to handle undefined data safely
  const uniqueServices = useMemo(() => {
    const appts = (appointments || []) as AppointmentUI[];
    const servicesMap = new Map<number, any>();

    appts.forEach((appt: AppointmentUI) => {
       if (appt.service && !servicesMap.has(appt.service.id)) {
         servicesMap.set(appt.service.id, appt.service);
       }
     });

    return Array.from(servicesMap.values()).sort((a: any, b: any) =>
      a.title.localeCompare(b.title)
    );
  }, [appointments]);

   // --- Options for Select dropdowns --- 
   const serviceOptions = useMemo(() => 
      uniqueServices.map(service => ({
          label: service.title,
          value: service.id,
      })),
      [uniqueServices]
  );

  const pendingAppointments = useMemo(() => {
    return filterByStatus(filterByService(appointments, selectedPendingServiceId), 'pending')
      .filter(appt => 
        appt.expectedAppointmentStartTime && 
        dayjs(appt.expectedAppointmentStartTime).isAfter(now)
      )
      .sort((a, b) => 
        dayjs(a.expectedAppointmentStartTime!).unix() - dayjs(b.expectedAppointmentStartTime!).unix()
      );
  }, [appointments, selectedPendingServiceId, now]);

  const upcomingAndInProgressAppointments = useMemo(() => {
    return filterByStatus(
      filterByService(appointments, selectedUpcomingServiceId),
      ['confirmed', 'InProgress']
    ).sort((a, b) => {
        if (a.status === 'InProgress' && b.status !== 'InProgress') return -1;
        if (a.status !== 'InProgress' && b.status === 'InProgress') return 1;
      return dayjs(a.expectedAppointmentStartTime!).unix() - dayjs(b.expectedAppointmentStartTime!).unix();
    });
  }, [appointments, selectedUpcomingServiceId]);

  if (isLoading) {
    return <div className="flex justify-center items-center h-64"><Spin size="large" /></div>;
  }

  if (error) {
    return <Alert message="Error loading appointments" description={error.message} type="error" showIcon />;
  }

  // --- Helper function to render a Select filter ---
  const renderServiceFilter = (value: number | undefined, onChange: (value: number | undefined) => void) => (
      <Select<number | undefined>
        allowClear
        placeholder="Filter by Service"
        style={{ width: 180 }} // Adjust width if needed
        onChange={onChange}
        value={value}
        options={serviceOptions}
        size="small" // Make filter smaller to fit in card header
      />
  );

  // Update the List rendering to use proper type checking
  const renderAppointmentMeta = (appointment: AppointmentUI) => (
    <List.Item.Meta
      title={
        `${appointment.service?.title || 'Unknown Service'} - ` +
        `${appointment.customerFolder?.customer?.firstName || ''} ` +
        `${appointment.customerFolder?.customer?.lastName || ''}`
      }
      description={
        <Space direction="vertical">
          <Text type="danger">Shifted outside queue hours!</Text>
          <Text>
            Original Time: {appointment.expectedAppointmentStartTime 
              ? dayjs(appointment.expectedAppointmentStartTime).format('MMM D, HH:mm')
              : 'Unknown'
            }
          </Text>
          <Text>
            Current Time: {appointment.expectedAppointmentEndTime
              ? dayjs(appointment.expectedAppointmentEndTime).format('MMM D, HH:mm')
              : 'Unknown'
            }
          </Text>
        </Space>
      }
      />
  );

  return (
    <DefaultLayout user={user}>
      <Breadcrumb pageName="Appointments" />
      
      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'flex-end' , gap:"10px" }}>
        <Button 
          type="primary" 
          icon={<PlusOutlined />} 
          onClick={() => setIsNewModalOpen(true)}
        >
          Create New Appointment
        </Button>
        <Button
          type="primary"
          danger
          onClick={() => setIsEarlyCloseModalOpen(true)}
        >
          Close Early for Today
        </Button>
      </div>
      <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
        {/* Left Column: Pending Appointments Table */}
        <div className="md:col-span-2">
          <Card 
             title={`Pending Appointments (${pendingAppointments.length})`}
             extra={renderServiceFilter(selectedPendingServiceId, setSelectedPendingServiceId)} // Add filter to card extra
           >
             <Table
                 columns={columns} 
                 dataSource={pendingAppointments}
                 rowKey="id"
                 pagination={{ pageSize: 5 }}
                 size="small"
                 scroll={{ x: 'max-content' }}
                 locale={{ emptyText: pendingAppointments.length === 0 && selectedPendingServiceId !== undefined ? 'No pending appointments match the selected service.' : 'No pending appointments require action.' }}
               />
           </Card>
           {/* Add Overflowed Appointments Section */}
        <div className="mt-6">
          <Card 
            title={
              <Space>
                <WarningOutlined style={{ color: '#faad14' }} />
                <span>Overflowed Appointments</span>
                <Badge count={overflowedAppointments.length} style={{ backgroundColor: '#faad14' }} />
              </Space>
            }
          >
            {overflowedAppointments.length > 0 ? (
              <List
                dataSource={overflowedAppointments}
                renderItem={(appointment: AppointmentUI) => (
                  <List.Item
                    actions={[
                      <Button 
                        type="primary" 
                        danger 
                        onClick={() => setSelectedOverflowedAppointment(appointment)}
                      >
                        Handle Overflow
                      </Button>
                    ]}
                  >
                    {renderAppointmentMeta(appointment)}
                  </List.Item>
                )}
              />
            ) : (
              <Empty description="No overflowed appointments" />
            )}
          </Card>
        </div>

        {/* Add Reschedule Requests Section */}
        <div className="mt-6">
          <Card 
            title={
              <Space>
                <SwapOutlined style={{ color: '#1890ff' }} />
                <span>Reschedule Requests</span>
                <Badge 
                  count={
                    ((rescheduleRequests || []) as RescheduleRequest[]).filter((r: RescheduleRequest) => r.status === 'pending').length
                  } 
                />
              </Space>
            }
          >
            {((rescheduleRequests || []) as RescheduleRequest[]).length > 0 ? (
              ((rescheduleRequests || []) as RescheduleRequest[]).map((request) => (
                <RescheduleRequestCard
                  key={request.id}
                  request={request}
                  onRespond={refetchAll}
                />
              ))
            ) : (
              <Empty description="No reschedule requests" />
            )}
          </Card>
        </div>
              {/* --- Section for Provider Queue Swap Requests --- */}
      <div className="mt-6">
        <Card
          title={
            <Space>
              <SwapOutlined style={{ color: '#52c41a' }} /> {/* Different color for provider swap icon */}
              <span>Queue Swap Requests</span>
              <Badge
                count={queueSwapRequests.filter(r => r.status === 'approved' || r.status === 'pending_customer2_approval').length}
                style={{ backgroundColor: '#52c41a' }}
              />
            </Space>
          }
        >
          {isLoadingQueueSwaps && <Spin />}
          {errorQueueSwaps && <Alert message="Error loading swap requests" type="error" />}
          {!isLoadingQueueSwaps && !errorQueueSwaps && (
            queueSwapRequests.length > 0 ? (
              queueSwapRequests.map((req) => (
                <ProviderQueueSwapRequestCard 
                  key={req.id} 
                  request={req} 
                  onFinalize={handleFinalizeSwap} 
                />
              ))
            ) : (
              <Empty description="No active queue swap requests." />
            )
          )}
        </Card>
      </div>
      {/* --- End Section for Provider Queue Swap Requests --- */}
        </div>

        {/* Right Column: Upcoming & InProgress Appointments List */}
        <div className="md:col-span-1">
          <Card 
             title={`Upcoming & In Progress (${upcomingAndInProgressAppointments.length})`}
             extra={renderServiceFilter(selectedUpcomingServiceId, setSelectedUpcomingServiceId)} // Add filter to card extra
           >
              <List
                 itemLayout="vertical"
                 dataSource={upcomingAndInProgressAppointments}
                 renderItem={(item: AppointmentUI) => (
                   <UpcomingAppointmentCard 
                     key={item.id}
                     appointment={item} 
                     onComplete={handleComplete} 
                     onNoShow={handleNoShow}
                     isLoading={isOverallSubmitting}
                   />
                 )}
                 pagination={{ pageSize: 5, size: 'small' }}
                 locale={{ emptyText: upcomingAndInProgressAppointments.length === 0 && selectedUpcomingServiceId !== undefined ? 'No upcoming/in-progress appointments match the selected service.' : 'No upcoming or in-progress appointments.' }}
               />
           </Card>
        </div>
      </div>

      
      <Modal
        title="Close Early for Today"
        open={isEarlyCloseModalOpen}
        onCancel={() => setIsEarlyCloseModalOpen(false)}
        footer={null}
      >
        <Alert
          message="You are about to close early."
          description="What do you want to do with the remaining appointments for today?"
          type="warning"
          showIcon
          className="mb-4"
        />
        <div className="mb-4">
          <b>Remaining appointments today:</b> {remainingAppointmentsToday.length}
        </div>
        <Space direction="vertical" className="w-full">
          <Button
            block
            danger
            loading={isEarlyCloseSubmitting}
            onClick={handleEarlyCloseCancelAll}
            disabled={remainingAppointmentsToday.length === 0}
          >
            Cancel All Remaining Appointments
          </Button>
          <Button
            block
            type="primary"
            ghost
            disabled={remainingAppointmentsToday.length === 0}
            onClick={handleOpenRescheduleModal}
          >
            Reschedule Individually
          </Button>
        </Space>
      </Modal>
      {/* --- End Early Close Modal --- */}

      {/* --- Early Close Reschedule Modal --- */}
      <Modal
        title="Reschedule Remaining Appointments"
        open={isRescheduleModalOpen}
        onCancel={() => setIsRescheduleModalOpen(false)}
        footer={null}
        width={700}
      >
        <Alert
          message="You are about to reschedule all remaining appointments for today."
          description="Please provide a reason and reschedule each appointment individually."
          type="info"
          showIcon
          className="mb-4"
        />
        <Input.TextArea
          rows={3}
          placeholder="Reason for rescheduling (required)"
          value={rescheduleReason}
          onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setRescheduleReason(e.target.value)}
          className="mb-4"
        />
        <div>
          {remainingAppointmentsToday.length === 0 ? (
            <Empty description="No appointments to reschedule." />
          ) : (
            <List
              dataSource={remainingAppointmentsToday}
              renderItem={appt => (
                <List.Item
                  key={appt.id}
                  actions={[
                    rescheduleStatus[appt.id] === 'success' ? (
                      <Tag color="green">Rescheduled</Tag>
                    ) : rescheduleStatus[appt.id] === 'error' ? (
                      <Tag color="red">Error</Tag>
                    ) : (
                      <Button
                        type="primary"
                        size="small"
                        onClick={() => handleOpenSlotPickerModal(appt)}
                        disabled={!rescheduleReason}
                        loading={rescheduleStatus[appt.id] === 'pending'}
                      >
                        Reschedule
                      </Button>
                    )
                  ]}
                >
                  <List.Item.Meta
                    title={
                      <span>
                        {appt.customerFolder?.customer?.firstName} {appt.customerFolder?.customer?.lastName} - {appt.service?.title}
                      </span>
                    }
                    description={
                      <span>
                        {dayjs(appt.expectedAppointmentStartTime).format('MMM D, HH:mm')} - {dayjs(appt.expectedAppointmentEndTime).format('HH:mm')}
                      </span>
                    }
                  />
                </List.Item>
              )}
            />
          )}
        </div>
        <div className="mt-4 text-right">
          <Button
            type="primary"
            onClick={() => setIsRescheduleModalOpen(false)}
            disabled={!allRescheduled}
          >
            Finish
          </Button>
        </div>
      </Modal>
      {/* --- End Early Close Reschedule Modal --- */}

      {/* Add Overflow Action Modal */}
      <OverflowActionModal
        appointment={selectedOverflowedAppointment}
        visible={!!selectedOverflowedAppointment}
        onClose={() => setSelectedOverflowedAppointment(null)}
        onSuccess={refetchAll}
      />

      <NewAppointmentModal 
        isOpen={isNewModalOpen}
        onClose={() => setIsNewModalOpen(false)}
        startDate={null} 
        endDate={null}
        onSuccess={() => {
            refetchAppointments();
        }}
      />

      {/* --- Slot Picker Modal --- */}
      <Modal
        title={slotPickerAppointment ? `Pick a new slot for ${slotPickerAppointment.customerFolder?.customer?.firstName} ${slotPickerAppointment.customerFolder?.customer?.lastName}` : 'Pick a new slot'}
        open={slotPickerModalOpen}
        onCancel={handleCancelSlotPicker}
        footer={null}
        width={600}
      >
        {slotPickerAppointment && (
          <>
            <RescheduleSlotPicker
              appointment={slotPickerAppointment}
              selectedSlot={slotPickerSelectedSlot}
              onSelectSlot={setSlotPickerSelectedSlot}
            />
            <div className="mt-4 text-right">
              <Button
                type="primary"
                onClick={handleConfirmSlotPicker}
                disabled={!slotPickerSelectedSlot}
                loading={slotPickerLoading}
              >
                Confirm Slot
              </Button>
              <Button
                className="ml-2"
                onClick={handleCancelSlotPicker}
                disabled={slotPickerLoading}
              >
                Cancel
              </Button>
            </div>
          </>
        )}
      </Modal>
      {/* --- End Slot Picker Modal --- */}

    </DefaultLayout>
  );
};

// --- Placeholder for ProviderQueueSwapRequestCard ---
interface ProviderQueueSwapRequestCardProps {
  request: QueueSwapRequestUI;
  onFinalize: (requestId: number) => void;
}

const ProviderQueueSwapRequestCard: React.FC<ProviderQueueSwapRequestCardProps> = ({ request, onFinalize }) => {
  const { appointment1, appointment2, requestedBy, status, notes, expiresAt } = request;

  const getStatusTag = (status: string) => {
    switch (status) {
      case 'pending_customer2_approval': return <Tag color="processing">Pending Customer 2 Approval</Tag>;
      case 'approved': return <Tag color="success">Approved (Ready to Finalize)</Tag>;
      case 'rejected_by_customer2': return <Tag color="error">Rejected by Customer 2</Tag>;
      case 'completed': return <Tag color="green">Completed</Tag>;
      case 'expired': return <Tag color="warning">Expired</Tag>;
      case 'canceled_by_requester': return <Tag color="default">Canceled by Requester</Tag>;
      default: return <Tag>{status}</Tag>;
    }
  };

  return (
    <Card 
      size="small" 
      className="mb-3 shadow-sm" 
      title={`Swap Request ID: ${request.id}`}
      extra={getStatusTag(status)}
    >
      <Descriptions bordered column={1} size="small">
        <Descriptions.Item label="Status">{getStatusTag(status)}</Descriptions.Item>
        {requestedBy && (
          <Descriptions.Item label="Requested By">
            {requestedBy.firstName} {requestedBy.lastName || ''}
          </Descriptions.Item>
        )}
        {expiresAt && (
          <Descriptions.Item label="Expires At">
            {dayjs(expiresAt).format('MMM D, YYYY h:mm A')}
          </Descriptions.Item>
        )}
        {notes && (
          <Descriptions.Item label="Notes">
            {notes}
          </Descriptions.Item>
        )}
      </Descriptions>

      <Row gutter={16} className="mt-3">
        <Col span={12}>
          <Card type="inner" title="Appointment 1 (Wants to move TO this time)">
            {appointment1 ? (
              <Descriptions layout="vertical" size="small" bordered column={1}>
                <Descriptions.Item label="Customer">
                  {appointment1.customerFolder?.customer?.firstName} {appointment1.customerFolder?.customer?.lastName}
                </Descriptions.Item>
                <Descriptions.Item label="Service">{appointment1.service?.title}</Descriptions.Item>
                <Descriptions.Item label="Original Time">
                  {dayjs(appointment1.expectedAppointmentStartTime).format('MMM D, YYYY h:mm A')}
                </Descriptions.Item>
              </Descriptions>
            ) : <Text type="secondary">Details not available.</Text>}
          </Card>
        </Col>
        <Col span={12}>
          <Card type="inner" title="Appointment 2 (Will move FROM this time)">
            {appointment2 ? (
              <Descriptions layout="vertical" size="small" bordered column={1}>
                <Descriptions.Item label="Customer">
                  {appointment2.customerFolder?.customer?.firstName} {appointment2.customerFolder?.customer?.lastName}
                </Descriptions.Item>
                <Descriptions.Item label="Service">{appointment2.service?.title}</Descriptions.Item>
                <Descriptions.Item label="Original Time">
                  {dayjs(appointment2.expectedAppointmentStartTime).format('MMM D, YYYY h:mm A')}
                </Descriptions.Item>
              </Descriptions>
            ) : <Text type="secondary">Details not available.</Text>}
          </Card>
        </Col>
      </Row>

      {status === 'approved' && (
        <div style={{ marginTop: '16px', textAlign: 'right' }}>
          <Button 
            type="primary" 
            icon={<CheckCircleOutlined />} 
            onClick={() => onFinalize(request.id)} 
            size="middle"
          >
            Finalize Swap
          </Button>
        </div>
      )}
    </Card>
  );
};

// --- Slot Picker Component for Reschedule (Modal version) ---
interface RescheduleSlotPickerProps {
  appointment: AppointmentUI;
  selectedSlot: { startTime: string; queueId: number } | null;
  onSelectSlot: (slot: { startTime: string; queueId: number }) => void;
}

const RescheduleSlotPicker: React.FC<RescheduleSlotPickerProps> = ({ appointment, selectedSlot, onSelectSlot }) => {
  const {
    data: availableSlots = [],
    isLoading: slotsLoading,
    error: slotsError,
  } = useQuery(
    getProviderAvailability,
    appointment
      ? {
          sProvidingPlaceId: appointment.placeId,
          serviceId: appointment.serviceId,
          startDate: dayjs().format('YYYY-MM-DD'),
          endDate: dayjs().add(7, 'day').format('YYYY-MM-DD'),
        }
      : undefined,
    { enabled: !!appointment }
  );

  return (
    <div className="mt-2 mb-2 p-2 border rounded bg-gray-50">
      <div className="mb-2 font-semibold">Pick a new slot:</div>
      {slotsLoading ? <Spin /> : slotsError ? (
        <div style={{ color: 'red' }}>{slotsError.message || String(slotsError)}</div>
      ) : !availableSlots.length ? (
        <div>No available slots found.</div>
      ) : (
        <Collapse accordion>
          {availableSlots.map((day: any) => (
            <Collapse.Panel header={dayjs(day.date).format('dddd, MMM D')} key={day.date}>
              <Space wrap>
                {day.slots
                  .filter((slot: any) => !slot.isBooked)
                  .map((slot: any) => (
                    <Button
                      key={slot.startTime + '-' + slot.queueId}
                      type={selectedSlot && selectedSlot.startTime === slot.startTime && selectedSlot.queueId === slot.queueId ? 'primary' : 'default'}
                      onClick={() => onSelectSlot({ startTime: slot.startTime, queueId: slot.queueId })}
                    >
                      {dayjs(slot.startTime).format('HH:mm')} (Queue {slot.queueId})
                    </Button>
                  ))}
              </Space>
            </Collapse.Panel>
          ))}
        </Collapse>
      )}
    </div>
  );
};

export default AppointmentsPage; 