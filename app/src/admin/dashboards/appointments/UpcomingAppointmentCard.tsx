import React, { useState, useEffect } from 'react';
import { Service, type Appointment } from 'wasp/entities';
import { updateAppointment } from 'wasp/client/operations';
import { useAction } from 'wasp/client/operations';
import { Button, List, Space, Tag, Typography, message, Modal } from 'antd';
import { CheckOutlined, UserOutlined, ClockCircleOutlined, EnvironmentOutlined, ToolOutlined, CalendarOutlined, StopOutlined, PlayCircleOutlined, CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { formatDate } from '../../../utils/dateUtils';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import updateLocale from 'dayjs/plugin/updateLocale';
import isToday from 'dayjs/plugin/isToday';
import Card from 'antd/es/card/Card';

dayjs.extend(relativeTime);
dayjs.extend(updateLocale);
dayjs.extend(isToday);

// Customize relative time thresholds (optional, but makes "in a minute" appear sooner)
dayjs.updateLocale('en', {
  relativeTime: {
    future: 'in %s',
    past: '%s ago',
    s: 'a few seconds',
    m: 'a minute',
    mm: '%d minutes',
    h: 'an hour',
    hh: '%d hours',
    d: 'a day',
    dd: '%d days',
    M: 'a month',
    MM: '%d months',
    y: 'a year',
    yy: '%d years'
  }
});

const { Text } = Typography;

// Helper function to format appointment time
const formatAppointmentTime = (startTime: Date | null, endTime: Date | null): string => {
  if (!startTime || !endTime) return 'Time not set';
  
  const startDayjs = dayjs(startTime);
  const isAppointmentToday = startDayjs.isToday();
  
  if (isAppointmentToday) {
    // Only show times for today's appointments
    return `Today, ${formatDate(startTime, { timeStyle: 'short' })} - ${formatDate(endTime, { timeStyle: 'short' })}`;
  } else {
    // Show full date and time for future/past appointments
    return `${formatDate(startTime, { dateStyle: 'medium' })}, ${formatDate(startTime, { timeStyle: 'short' })} - ${formatDate(endTime, { timeStyle: 'short' })}`;
  }
};

// Define interface matching the data structure from AppointmentsPage
interface AppointmentUI {
  id: number;
  status: string;
  serviceId: number;
  placeId: number;
  queueId: number | null;
  notes: string | null;
  expectedAppointmentStartTime: Date | null;
  expectedAppointmentEndTime: Date | null;
  isOverflowed: boolean;
  overflowReason: string | null;
  overflowProcessingStatus: string | null;
  overflowDetectedAt: Date | null;
  overflowProcessedAt: Date | null;
  
  service?: {
    id: number;
    title: string;
    duration: number;
    createdAt: Date;
    updatedAt: Date;
    color: string | null;
    sProviderId: number;
    serviceCategoryId: number | null;
    minDuration: number | null;
    maxDuration: number | null;
    queue: number | null;
    acceptOnline: boolean;
    acceptNew: boolean;
    notificationOn: boolean;
    pointsRequirements: number;
  };
  customerFolder?: {
    userId?: string;
    customer?: {
      id: string;
      firstName?: string | null;
      lastName?: string | null;
    };
  };
  place?: {
    id: number;
    name: string;
  };
  queue?: {
    id: number;
    title: string;
  } | null;
}

interface UpcomingAppointmentCardProps {
  appointment: AppointmentUI;
  onComplete: (appointmentId: number) => void;
  onNoShow: (appointment: AppointmentUI) => void;
  isLoading?: boolean;
}

const UpcomingAppointmentCard: React.FC<UpcomingAppointmentCardProps> = ({
  appointment,
  onComplete,
  onNoShow,
  isLoading = false,
}) => {
  const updateAppointmentAction = useAction(updateAppointment);
  const customerName = `${appointment.customerFolder?.customer?.firstName || ''} ${appointment.customerFolder?.customer?.lastName || ''}`.trim() || 'Unknown Customer';
  const serviceName = appointment.service?.title || 'Unknown Service';
  const queueName = appointment.queue?.title || 'No Queue';
  const locationName = appointment.place?.name || 'Unknown Location';

  const handleStartSession = async () => {
    if (!appointment.customerFolder?.customer?.id) {
      message.error('Cannot start session: Missing customer information');
      return;
    }

    try {
      await updateAppointmentAction({
        appointmentId: appointment.id,
        status: 'InProgress',
        customerUserId: appointment.customerFolder.customer.id,
        serviceId: appointment.serviceId,
        placeId: appointment.placeId,
        queueId: appointment.queueId || undefined,
        startTime: appointment.expectedAppointmentStartTime || new Date(),
        endTime: appointment.expectedAppointmentEndTime || new Date(),
        notes: appointment.notes,
      });
      message.success('Session started successfully');
    } catch (error) {
      message.error(error instanceof Error ? error.message : 'Failed to start session');
    }
  };

  const renderActionButtons = () => {
    if (appointment.status === 'confirmed') {
      return (
        <Button
          type="primary"
          icon={<PlayCircleOutlined />}
          onClick={handleStartSession}
          size="small"
          loading={isLoading}
        >
          Start Session
        </Button>
      );
    }

    if (appointment.status === 'InProgress') {
      return (
        <Button
          type="primary"
          icon={<CheckCircleOutlined />}
          onClick={() => onComplete(appointment.id)}
          size="small"
          loading={isLoading}
        >
          Complete
        </Button>
      );
    }

    return null;
  };

  return (
    <Card size="small" className="mb-2">
      <Space direction="vertical" className="w-full">
        <div className="flex justify-between">
          <Text strong>{customerName}</Text>
          <Tag color={appointment.status === 'InProgress' ? 'processing' : 'success'}>
            {appointment.status}
          </Tag>
        </div>
        <Space>
          <ClockCircleOutlined />
          <Text>
            {formatAppointmentTime(appointment.expectedAppointmentStartTime, appointment.expectedAppointmentEndTime)}
          </Text>
          <Tag color="purple" style={{ marginLeft: 8 }}>
            { appointment.status !== 'InProgress' ? dayjs(appointment.expectedAppointmentStartTime).fromNow() : '-'}
          </Tag>
        </Space>
        <Text type="secondary">{serviceName} at {locationName} ({queueName})</Text>
        <Space className="mt-2">
          {renderActionButtons()}
          <Button
            danger
            icon={<CloseCircleOutlined />}
            onClick={() => onNoShow(appointment)}
            size="small"
            loading={isLoading}
          >
            No Show
          </Button>
        </Space>
      </Space>
    </Card>
  );
};

export default UpcomingAppointmentCard; 