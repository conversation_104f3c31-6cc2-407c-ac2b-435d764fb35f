import React, { useEffect } from 'react';
import { createProviderCustomer, useAction } from 'wasp/client/operations'; // Keep useAction if needed later, import action directly
import toast from 'react-hot-toast';
import { Modal, Form, Input, Button } from 'antd'; // Import Ant Design components

interface AddCustomerModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void; // To refetch the customer list
}

// Matches the backend Zod schema's expected input for createProviderCustomer
interface AddCustomerFormData {
  firstName: string;
  lastName: string;
  mobileNumber: string;
  email?: string;      // Optional fields
  nationalId?: string; // Optional fields
  notes?: string;      // Optional fields
}

const AddCustomerModal: React.FC<AddCustomerModalProps> = ({ isOpen, onClose, onSuccess }) => {
  const [form] = Form.useForm<AddCustomerFormData>();
  const createCustomerAction = useAction(createProviderCustomer); // Hook for the action
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  // Reset form when modal becomes hidden
  useEffect(() => {
    if (!isOpen) {
      form.resetFields();
    }
  }, [isOpen, form]);

  const handleFinish = async (values: AddCustomerFormData) => {
    setIsSubmitting(true);
    try {
      console.log("Submitting form data:", values);
      await createCustomerAction({
        firstName: values.firstName,
        lastName: values.lastName,
        mobileNumber: values.mobileNumber,
        // Handle optional fields: send undefined if empty string or nullish
        email: values.email || undefined,
        nationalId: values.nationalId || undefined, // Changed to undefined for consistency
        notes: values.notes || undefined,           // Changed to undefined for consistency
      });
      toast.success(`Customer "${values.firstName} ${values.lastName}" added successfully!`);
      onSuccess(); // Call success callback (e.g., refetch list)
      onClose();   // Close the modal
    } catch (error: any) {
      let errorMessage = `Failed to add customer: ${error.message || 'Unknown error'}`;
      if (error.statusCode === 409) { // Check for specific conflict error
          errorMessage = `Error: ${error.message}`; // Use the specific message from the backend
      }
      toast.error(errorMessage);
      console.error("Error submitting customer form:", error);
    } finally {
      // No need to manually reset isSubmitting if modal closes on success
      // If there's an error, keep the modal open and allow retry
      if (isOpen) { // Check if modal is still open (error case)
          setIsSubmitting(false);
      }
    }
  };

  return (
    <Modal
      title="Add New Customer"
      open={isOpen}
      onCancel={onClose}
      confirmLoading={isSubmitting}
      className="ant-modal-content"
      footer={[
        <Button key="back" onClick={onClose} disabled={isSubmitting}>
          Cancel
        </Button>,
        <Button key="submit" type="primary" loading={isSubmitting} onClick={() => form.submit()}>
          Add Customer
        </Button>,
      ]}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleFinish}
        className="ant-form"
        initialValues={{
            firstName: '',
            lastName: '',
            mobileNumber: '',
            email: '',
            nationalId: '',
            notes: '',
        }}
      >
        <Form.Item
          name="firstName"
          label="First Name"
          rules={[{ required: true, message: 'Please enter the first name' }]}
        >
          <Input className="ant-input" />
        </Form.Item>

        <Form.Item
          name="lastName"
          label="Last Name"
          rules={[{ required: true, message: 'Please enter the last name' }]}
        >
          <Input className="ant-input" />
        </Form.Item>

        <Form.Item
          name="mobileNumber"
          label="Mobile Number"
          rules={[
            { required: true, message: 'Please enter the mobile number' },
          ]}
        >
          <Input type="tel" placeholder="e.g., 1234567890" className="ant-input" />
        </Form.Item>

        <Form.Item
          name="email"
          label="Email (Optional)"
          rules={[{ type: 'email', message: 'Please enter a valid email' }]}
        >
          <Input type="email" className="ant-input" />
        </Form.Item>

        <Form.Item
          name="nationalId"
          label="National ID (Optional)"
        >
          <Input className="ant-input" />
        </Form.Item>

        <Form.Item
          name="notes"
          label="Notes (Optional)"
        >
          <Input.TextArea rows={3} className="ant-input" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AddCustomerModal;