import React, { useState } from 'react';
import { useQuery, getProviderCustomers } from 'wasp/client/operations';
import { type User } from 'wasp/entities';
import { Table, Button, Spin, Alert, Space, Typography, Input } from 'antd';
import { PlusOutlined, UserOutlined, SearchOutlined } from '@ant-design/icons';
import AddCustomerModal from './AddCustomerModal';
import { Link } from 'wasp/client/router';
import { routes } from 'wasp/client/router';
// Import Grid for responsive layout if needed, though flex/Tailwind might suffice
// import { Grid } from 'antd';
// const { useBreakpoint } = Grid;

const { Text } = Typography;

const ProviderCustomersTable = () => {
  const { data: customers, isLoading, error, refetch } = useQuery(getProviderCustomers);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [nameFilter, setNameFilter] = useState('');
  const [mobileFilter, setMobileFilter] = useState('');

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  const handleSuccess = () => {
    refetch();
  };

  const columns = [
    {
      title: 'Name',
      dataIndex: 'firstName',
      key: 'name',
      render: (_: any, record: any) => `${record.customer.firstName || ''} ${record.customer.lastName || ''}`,
      sorter: (a: User, b: User) => (a.lastName || '').localeCompare(b.lastName || ''),
      filteredValue: nameFilter ? [nameFilter] : null,
      onFilter: (value: string, record: any) => {
        const fullName = `${record.customer.firstName || ''} ${record.customer.lastName || ''}`.toLowerCase();
        return fullName.includes(value.toLowerCase());
      }
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      render: (_:any, record: any) => record?.customer?.email || '-',
      sorter: (a: User, b: User) => (a.email || '').localeCompare(b.email || ''),
    },
    {
      title: 'Mobile',
      render: (_:any , record: any) => record?.customer?.mobileNumber || '-',
      sorter: (a: User, b: User) => (a.mobileNumber || '').localeCompare(b.mobileNumber || ''),
    },
    {
      title: 'National ID',
      render: (_:any , record: any) => record?.customer?.nationalId || '-',
      sorter: (a: User, b: User) => (a.nationalId || '').localeCompare(b.nationalId || ''),
    },
    {
      title: 'Appointments',
      key: 'appointments',
      dataIndex: 'appointments',
      render: (_:any, user: any) => user?.appointments?.length || 0,
    },
    {
      title: 'Actions',
      key: 'actions',
      align: 'right' as const,
      render: (_: any, record: any) => {
        const customerId = record.customer?.id;
        if (!customerId) return null;

        return (
          <Space size="middle">
            <Link 
              to={routes.AdminCustomerProfileRoute.to}
              params={{ customerId: customerId }}
            >
              <Button 
                type="default" 
                icon={<UserOutlined />} 
              >
                Profile
              </Button>
            </Link>
          </Space>
        );
      },
    },
  ];

  if (error) {
    return <Alert message="Error loading customers" description={error.message} type="error" showIcon />;
  }

  return (
    // Use Tailwind for main container styling if preferred, or keep simple div
    <div className='flex flex-col gap-4'> 
      {/* Responsive top bar: stack vertically below sm, row layout sm and up */}
      <div className='flex flex-col sm:flex-row justify-between items-center gap-4'>
        {/* Group search inputs, stack them vertically below sm */}
        <Space direction="vertical" className='w-full sm:w-auto sm:flex sm:flex-row sm:gap-4'>
          <Input
            placeholder="Search by name"
            prefix={<SearchOutlined />}
            value={nameFilter}
            onChange={(e: any) => setNameFilter(e.target.value)}
            className="w-full sm:w-[200px]" // Full width on small, fixed on sm+
          />
          {/* Add similar responsiveness to the mobile search input */}
          {/* <Input
            placeholder="Search by mobile"
            prefix={<SearchOutlined />}
            value={mobileFilter}
            onChange={(e: any) => setMobileFilter(e.target.value)}
            className="w-full sm:w-[200px]"
          /> */}
        </Space>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={openModal}
          className="w-full sm:w-auto" // Full width on small, auto on sm+
        >
          New Customer
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={customers}
        loading={isLoading}
        rowKey="id"
        pagination={{ pageSize: 10 }}
        bordered
        scroll={{ x: 'max-content' }} // Ensure horizontal scrolling
      />

      <AddCustomerModal
        isOpen={isModalOpen}
        onClose={closeModal}
        onSuccess={handleSuccess}
      />
    </div>
  );
};

export default ProviderCustomersTable;
