import React, { useState, useEffect } from 'react';
import { createProviderCustomer } from 'wasp/client/operations';
import toast from 'react-hot-toast';
// Import Ant Design components
import { Modal, Form, Input, Button, Spin, Row, Col } from 'antd';
import { useForm } from 'antd/es/form/Form'; // Import useForm

interface AddCustomerModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void; // To refetch the customer list
}

// Define form values structure for Ant Design
interface AddCustomerFormValues {
  firstName: string;
  lastName: string;
  mobileNumber: string;
  email?: string;
  nationalId?: string;
  notes?: string;
}

const AddCustomerModal: React.FC<AddCustomerModalProps> = ({ isOpen, onClose, onSuccess }) => {
  const [form] = useForm<AddCustomerFormValues>(); // Ant Design form instance
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      form.resetFields(); // Reset Ant Design form
    } else {
      // Ensure submitting state is reset if modal is closed externally
      // while submitting (e.g., clicking outside)
      setIsSubmitting(false);
    }
  }, [isOpen, form]);

  const handleFinish = async (values: AddCustomerFormValues) => {
    setIsSubmitting(true);

    try {
      console.log("Submitting form data:", values);
      await createProviderCustomer({
        firstName: values.firstName,
        lastName: values.lastName,
        mobileNumber: values.mobileNumber,
        email: values.email || undefined, // Send undefined if empty/null
        nationalId: values.nationalId || null, // Send null if empty
        notes: values.notes || null, // Send null if empty
      });
      toast.success(`Customer "${values.firstName} ${values.lastName}" added successfully!`);
      onSuccess(); // Call success callback (e.g., refetch list)
      onClose();   // Close the modal
    } catch (error: any) {
      let errorMessage = `Failed to add customer: ${error.message || 'Unknown error'}`;
      if (error.statusCode === 409) {
        errorMessage = `Error: ${error.message}`;
        // Optionally add validation feedback to the form field
        form.setFields([
          {
            name: 'mobileNumber',
            errors: [error.message || 'This mobile number is already registered.'],
          },
        ]);
      } else {
         toast.error(errorMessage);
      }
      console.error(error);
      // Don't reset submitting state here if validation error shown on field
      // Keep submitting false only for general errors
      if (error.statusCode !== 409) {
         setIsSubmitting(false);
      }
    } finally {
      // Ensure isSubmitting is false if the operation completed (successfully or with a non-409 error)
      // If it was a 409, isSubmitting might still be true if the form field error was set.
      // We can check if the form still has errors for the mobile number field.
      const mobileFieldErrors = form.getFieldError('mobileNumber');
      if (!mobileFieldErrors || mobileFieldErrors.length === 0) {
        // If there are no specific mobile errors (meaning success or general error), set submitting to false.
        setIsSubmitting(false);
      }
      // If there *are* mobile field errors (409 case), isSubmitting remains true, 
      // allowing the user to see the loading state on the button until they fix the input.
    }
  };

  const handleFinishFailed = (errorInfo: any) => {
    console.log('Failed:', errorInfo);
    toast.error('Please fill in all required fields correctly.');
  };

  return (
    <Modal
      title="Add New Customer"
      open={isOpen} // Use 'open' prop
      onCancel={onClose} // Use 'onCancel' prop
      width={600} // Set a reasonable width
      footer={[
        <Button key="back" onClick={onClose} disabled={isSubmitting}>
          Cancel
        </Button>,
        <Button key="submit" type="primary" loading={isSubmitting} onClick={() => form.submit()}> 
          {isSubmitting ? 'Adding...' : 'Add Customer'}
        </Button>,
      ]}
    >
      <Spin spinning={isSubmitting}> {/* Wrap form in Spin for loading state */} 
        <Form
          form={form}
          layout="vertical"
          onFinish={handleFinish}
          onFinishFailed={handleFinishFailed}
          initialValues={{
            firstName: '',
            lastName: '',
            mobileNumber: '',
            email: '',
            nationalId: '',
            notes: '',
          }} // Provide initial structure
        >
          <Row gutter={16}> {/* Use Row/Col for layout */} 
            <Col xs={24} sm={12}>
              <Form.Item
                label="First Name"
                name="firstName"
                rules={[{ required: true, message: 'Please enter the first name.' }]}
              >
                <Input placeholder="Customer's first name" />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                label="Last Name"
                name="lastName"
                rules={[{ required: true, message: 'Please enter the last name.' }]}
              >
                <Input placeholder="Customer's last name" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="Mobile Number"
            name="mobileNumber"
            rules={[
              { required: true, message: 'Please enter the mobile number.' },
              // Add potential regex validation if needed
              // { pattern: /^[0-9]+$/, message: 'Please enter a valid mobile number.' }
            ]}
          >
            <Input placeholder="e.g., 1234567890" type="tel" />
          </Form.Item>

          <Form.Item
            label="Email (Optional)"
            name="email"
            rules={[{ type: 'email', message: 'Please enter a valid email address.' }]}
          >
            <Input placeholder="<EMAIL>" type="email" />
          </Form.Item>

          <Form.Item
            label="National ID (Optional)"
            name="nationalId"
          >
            <Input placeholder="Customer's national ID number" />
          </Form.Item>

          <Form.Item
            label="Notes (Optional)"
            name="notes"
          >
            <Input.TextArea rows={3} placeholder="Any relevant notes about the customer" />
          </Form.Item>

        </Form>
      </Spin>
    </Modal>
  );
};

export default AddCustomerModal; 