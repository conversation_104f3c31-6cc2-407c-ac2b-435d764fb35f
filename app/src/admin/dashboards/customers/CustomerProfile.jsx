import React from 'react';
import { useParams } from 'react-router-dom';
import { useQuery } from 'wasp/client/operations';
import { getAdminCustomerDetails } from 'wasp/client/operations';
import { Spin, Alert, Card, Descriptions, Typography, Tag, Table, Empty, Statistic, Row, Col } from 'antd';
import { Link } from 'wasp/client/router';
import { routes } from 'wasp/client/router';
import { formatDate } from '../../../utils/dateUtils'; // Adjust path as needed
import dayjs from 'dayjs'; // Import dayjs for sorting
// import PieChart from '../../elements/charts/PieChart'; // Import PieChart
import AppointmentStatusPieChart from '../../elements/charts/AppointmentStatusPieChart'; // Import the new chart

const { Title, Text } = Typography;

// Define columns for the appointments table
const appointmentColumns = [
  {
    title: 'Date',
    dataIndex: 'expectedAppointmentStartTime',
    key: 'date',
    render: (text) => text ? formatDate(text) : '-',
    sorter: (a, b) => dayjs(a.expectedAppointmentStartTime).unix() - dayjs(b.expectedAppointmentStartTime).unix(),
  },
  {
    title: 'Time',
    key: 'time',
    render: (_, record) => 
        record.expectedAppointmentStartTime && record.expectedAppointmentEndTime
        ? `${formatDate(record.expectedAppointmentStartTime, { timeStyle: 'short' })} - ${formatDate(record.expectedAppointmentEndTime, { timeStyle: 'short' })}`
        : '-',
  },
  {
    title: 'Service',
    dataIndex: ['service', 'title'],
    key: 'service',
    render: (text) => text || '-',
  },
   {
    title: 'Location',
    dataIndex: ['place', 'name'],
    key: 'location',
    render: (text) => text || '-',
  },
  {
    title: 'Status',
    dataIndex: 'status',
    key: 'status',
    render: (status) => (
      <Tag color={status === 'completed' ? 'success' : status === 'confirmed' ? 'blue' : status === 'canceled' || status === 'noshow' ? 'error' : 'default'}>
        {status ? status.charAt(0).toUpperCase() + status.slice(1) : '-'}
      </Tag>
    ),
    filters: [
      { text: 'Pending', value: 'pending' },
      { text: 'Confirmed', value: 'confirmed' },
      { text: 'Completed', value: 'completed' },
      { text: 'Canceled', value: 'canceled' },
      { text: 'No Show', value: 'noshow' },
    ],
    onFilter: (value, record) => record.status === value,
  },
];

const AdminCustomerProfilePage = ({ customerId }) => {
  const { data: customer, isLoading, error } = useQuery(
    getAdminCustomerDetails,
    { customerId: customerId }, // Pass customerId as argument
    { enabled: !!customerId } // Only run query if customerId is available
  );

  // console.log(customer);

  if (!customerId) {
    return <Alert message="Error" description="Customer ID is missing from URL." type="error" showIcon />;
  }

  if (isLoading) {
    return <div className="flex justify-center items-center h-64"><Spin size="large" /></div>;
  }

  if (error) {
    return <Alert message="Error loading customer details" description={error.message} type="error" showIcon />;
  }

  if (!customer) {
    return <Alert message="Not Found" description={`Customer with ID ${customerId} not found.`} type="warning" showIcon />;
  }

  // Prepare items for the main Descriptions component
  const customerItems = [
    { key: '1', label: 'Name', children: `${customer.firstName || ''} ${customer.lastName || ''}`.trim() || '-' },
    { key: '2', label: 'Email', children: customer.email || '-' },
    { key: '3', label: 'Mobile', children: customer.mobileNumber || '-' },
    { key: '4', label: 'Credits', children: customer.credits ?? '-' },
    { key: '5', label: 'National ID', children: customer.nationalId || '-' },
    { key: '6', label: 'Role', children: customer.role ? customer.role.charAt(0).toUpperCase() + customer.role.slice(1).toLowerCase() : '-' },
    { key: '7', label: 'Account Created', children: formatDate(customer.createdAt, { dateStyle: 'long' }) },
  ];

  // Combine appointments from all folders (if needed, or display per folder)
  const allAppointments = customer.customerFolders?.flatMap(folder => folder.appointments || []) || [];
  // console.log(allAppointments)
  const whoCanceledAppointment = (appointment) => {
    if(appointment.history.length > 0) {
      // Sort history descendingly by date to get the latest change first.
      // Note: .sort() modifies the array in place. Create a copy if the original order is needed elsewhere: [...appointment.history].sort(...)
      const [latestHistory] = appointment.history.sort((a, b) => new Date(b.changeDate) - new Date(a.changeDate));
      console.log(latestHistory , customer.id)
      if(latestHistory.newStatus === 'canceled' && latestHistory.changedByUserId === customer.id) {
        return 'Customer';
      } else {
        return 'Provider';
      }
    } else {
      return 'unknown';
    }
  }

  // Calculate appointment status counts for the chart
  const statusCounts = allAppointments.reduce((acc, appointment) => {
    const status = appointment.status || 'unknown';

    
    if (status === 'canceled') {
      const whoCanceled = whoCanceledAppointment(appointment);
      console.log(whoCanceled)
      if (whoCanceled === 'Customer') {
        acc['canceled_customer'] = (acc['canceled_customer'] || 0) + 1;
      } else if (whoCanceled === 'Provider') {
        acc['canceled_provider'] = (acc['canceled_provider'] || 0) + 1;
      } else {
        acc['canceled_unknown'] = (acc['canceled_unknown'] || 0) + 1; // Handle cases where role isn't set
      }
    } else {
        acc[status] = (acc[status] || 0) + 1;
    }
    return acc;
  }, {});

  const chartData = [
    { type: 'Completed', value: statusCounts.completed || 0 },
    { type: 'Canceled by Customer', value: statusCounts.canceled_customer || 0 },
    { type: 'Canceled by Provider', value: statusCounts.canceled_provider || 0 },
    // Add other statuses if needed and available
    // { type: 'Canceled (Other)', value: statusCounts.canceled_unknown || 0 },
    { type: 'Confirmed', value: statusCounts.confirmed || 0 },
    { type: 'Pending', value: statusCounts.pending || 0 },
    { type: 'No Show', value: statusCounts.noshow || 0 },
  ].filter(item => item.value > 0); // Filter out statuses with zero count

  const totalAppointmentsForChart = chartData.reduce((sum, item) => sum + item.value, 0);

  // Calculate No-Show Rate
  const noShowCount = statusCounts.noshow || 0;
  // Relevant total: Completed + Canceled (both) + No Show
  const totalRelevantAppointments = (statusCounts.completed || 0) + 
                                    (statusCounts.canceled_customer || 0) + 
                                    (statusCounts.canceled_provider || 0) + 
                                    noShowCount;
  const noShowRate = totalRelevantAppointments > 0 ? (noShowCount / totalRelevantAppointments) * 100 : 0;

  return (
    <div className="flex flex-col gap-4">
      <Card title="Customer Details">
        <Descriptions bordered layout="vertical" column={{ xxl: 3, xl: 2, lg: 2, md: 1, sm: 1, xs: 1 }} items={customerItems} />
      </Card>

      {/* <Card title="Recent Appointments (Last 10 per Provider)">
        {allAppointments.length > 0 ? (
          <Table 
            columns={appointmentColumns}
            dataSource={allAppointments}
            rowKey="id"
            pagination={{ pageSize: 5 }} // Smaller pagination for profile view
            size="small"
            scroll={{ x: 'max-content' }}
          />
        ) : (
          <Empty description="No recent appointments found for this customer." />
        )}
      </Card> */}

      {/* Appointment Stats Card */}
      {(totalAppointmentsForChart > 0 || noShowCount > 0) && (
         <Card title="Appointment Status Overview">
            <Row gutter={[16, 16]} align="middle" justify="center">
              {/* Pie Chart Column */} 
              {totalAppointmentsForChart > 0 && (
                 <Col xs={24} sm={12} md={14} lg={16} className="flex justify-center">
                     <AppointmentStatusPieChart data={chartData} />
                 </Col>
              )}
             {/* No-Show Stat Column */} 
             <Col xs={24} sm={12} md={10} lg={8} className="flex justify-center items-center">
                <Statistic 
                    title="No-Show Rate" 
                    value={noShowRate} 
                    precision={1} 
                    suffix="%" 
                    valueStyle={{ color: noShowRate > 10 ? '#cf1322' : '#3f8600' }} // Example color logic
                 />
                 <Text type="secondary" style={{ marginLeft: 8 }}>({noShowCount} appointments)</Text>
             </Col>
            </Row>
         </Card>
      )}

      {/* Add sections for Customer Folders if needed */}
      {/* <Card title="Provider Relationships">
        {customer.customerFolders?.map(folder => (
          <div key={folder.id}>Folder with Provider: {folder.provider?.title}</div>
        ))}
      </Card> */}

      {/* Add Admin actions here - e.g., Edit User, Add Credits, View Full History */}

    </div>
  );
};

export default AdminCustomerProfilePage; 