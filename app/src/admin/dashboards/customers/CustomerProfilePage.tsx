import { type AuthUser } from 'wasp/auth';
import CustomerProfile from './CustomerProfile';
import Breadcrumb from '../../layout/Breadcrumb';
import DefaultLayout from '../../layout/DefaultLayout';
import { useRedirectHomeUnlessUserIsAdmin } from '../../useRedirectHomeUnlessUserIsAdmin';
import { useParams } from 'react-router-dom';

const CustomerProfilePage = ({ user }: { user: AuthUser }) => {
  useRedirectHomeUnlessUserIsAdmin({user})
  const { customerId } = useParams();
  return (
    <DefaultLayout user={user}>
      <Breadcrumb pageName='Customer Profile' />
      <div className='flex flex-col gap-10'>
        <CustomerProfile customerId={customerId} />
      </div>
    </DefaultLayout>
  );
};

export default CustomerProfilePage;
