import { type Request, type Response } from 'express';
import { HttpError } from 'wasp/server';
import { type File } from 'wasp/entities';
import * as z from 'zod';
import { createFile } from 'wasp/server/operations';
import { Role } from '@prisma/client';

// Image file types only for category images (subset of ALLOWED_FILE_TYPES)
const IMAGE_FILE_TYPES = [
  'image/jpeg',
  'image/png'
] as const;

// Validation schemas
const uploadCategoryImageApiSchema = z.object({
  fileType: z.enum(IMAGE_FILE_TYPES),
  fileName: z.string().min(1, 'File name is required'),
  categoryId: z.number().int().positive('Category ID must be a positive integer'),
});

const getCategoryImageApiSchema = z.object({
  categoryId: z.coerce.number().int().positive('Category ID must be a positive integer'),
});

const removeCategoryImageApiSchema = z.object({
  categoryId: z.coerce.number().int().positive('Category ID must be a positive integer'),
});

type UploadCategoryImageApiInput = z.infer<typeof uploadCategoryImageApiSchema>;
type GetCategoryImageApiInput = z.infer<typeof getCategoryImageApiSchema>;
type RemoveCategoryImageApiInput = z.infer<typeof removeCategoryImageApiSchema>;

/**
 * POST /api/auth/admin/provider-categories/:categoryId/image
 * Upload an image for a provider category (admin only)
 */
export const handleUploadCategoryImage = async (req: Request, res: Response, context: any) => {
  try {
    // Check authentication and admin access
    if (!context.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    if (context.user.role !== Role.ADMIN) {
      return res.status(403).json({
        success: false,
        message: 'Admin access required'
      });
    }

    // Get category ID from URL params
    const categoryId = parseInt(req.params.categoryId);
    if (!categoryId) {
      return res.status(400).json({
        success: false,
        message: 'Invalid category ID'
      });
    }

    // Validate request body
    const validationResult = uploadCategoryImageApiSchema.safeParse({
      ...req.body,
      categoryId
    });
    
    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        message: 'Invalid request data. Only image files are allowed for category images.',
        errors: validationResult.error.format()
      });
    }

    const { fileType, fileName } = validationResult.data;

    // Check if category exists
    const category = await context.entities.ProviderCategory.findUnique({
      where: { id: categoryId }
    });

    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Provider category not found'
      });
    }

    // Create the file record using existing operation
    const result = await createFile({ fileType, fileName }, context);

    // Get the created file record to get its ID
    const fileRecord = await context.entities.File.findFirst({
      where: {
        userId: context.user.id,
        name: fileName,
        type: fileType,
        key: result.s3UploadFields.key || result.s3UploadUrl.split('/').pop()
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    if (!fileRecord) {
      throw new HttpError(500, 'Failed to create file record');
    }

    // Update category's image reference
    const updatedCategory = await context.entities.ProviderCategory.update({
      where: { id: categoryId },
      data: { imageId: fileRecord.id },
      include: {
        image: true,
        parent: true,
        children: true,
        _count: {
          select: {
            providers: true,
            children: true,
          }
        }
      }
    });

    return res.status(200).json({
      success: true,
      message: 'Category image upload URL generated successfully',
      data: {
        uploadUrl: result.s3UploadUrl,
        uploadFields: result.s3UploadFields,
        file: {
          id: fileRecord.id,
          name: fileName,
          type: fileType,
          key: fileRecord.key
        },
        category: {
          id: updatedCategory.id,
          title: updatedCategory.title,
          imageId: updatedCategory.imageId,
          parentId: updatedCategory.parentId,
          _count: updatedCategory._count
        }
      }
    });

  } catch (error: any) {
    console.error('[API] Error in category image upload:', error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error.message || 'Failed to generate category image upload URL';
    return res.status(statusCode).json({
      success: false,
      message
    });
  }
};

/**
 * GET /api/auth/admin/provider-categories/:categoryId/image
 * Get the image for a provider category (admin only)
 */
export const handleGetCategoryImage = async (req: Request, res: Response, context: any) => {
  try {
    // Check authentication and admin access
    if (!context.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    if (context.user.role !== Role.ADMIN) {
      return res.status(403).json({
        success: false,
        message: 'Admin access required'
      });
    }

    // Get category ID from URL params
    const categoryId = parseInt(req.params.categoryId);
    if (!categoryId) {
      return res.status(400).json({
        success: false,
        message: 'Invalid category ID'
      });
    }

    // Find category with image
    const category = await context.entities.ProviderCategory.findUnique({
      where: { id: categoryId },
      include: {
        image: true
      }
    });

    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Provider category not found'
      });
    }

    if (!category.image) {
      return res.status(404).json({
        success: false,
        message: 'No image found for this category'
      });
    }

    return res.status(200).json({
      success: true,
      message: 'Category image retrieved successfully',
      data: {
        category: {
          id: category.id,
          title: category.title,
          imageId: category.imageId
        },
        image: {
          id: category.image.id,
          name: category.image.name,
          type: category.image.type,
          key: category.image.key,
          uploadUrl: category.image.uploadUrl,
          createdAt: category.image.createdAt
        }
      }
    });

  } catch (error: any) {
    console.error('[API] Error getting category image:', error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error.message || 'Failed to get category image';
    return res.status(statusCode).json({
      success: false,
      message
    });
  }
};

/**
 * DELETE /api/auth/admin/provider-categories/:categoryId/image
 * Remove the image from a provider category (admin only)
 */
export const handleRemoveCategoryImage = async (req: Request, res: Response, context: any) => {
  try {
    // Check authentication and admin access
    if (!context.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    if (context.user.role !== Role.ADMIN) {
      return res.status(403).json({
        success: false,
        message: 'Admin access required'
      });
    }

    // Get category ID from URL params
    const categoryId = parseInt(req.params.categoryId);
    if (!categoryId) {
      return res.status(400).json({
        success: false,
        message: 'Invalid category ID'
      });
    }

    // Find category with current image
    const category = await context.entities.ProviderCategory.findUnique({
      where: { id: categoryId },
      include: {
        image: true
      }
    });

    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Provider category not found'
      });
    }

    if (!category.image) {
      return res.status(404).json({
        success: false,
        message: 'No image found for this category'
      });
    }

    // Remove image reference from category
    const updatedCategory = await context.entities.ProviderCategory.update({
      where: { id: categoryId },
      data: { imageId: null },
      include: {
        parent: true,
        children: true,
        _count: {
          select: {
            providers: true,
            children: true,
          }
        }
      }
    });

    // Note: We don't delete the File record itself as it might be referenced elsewhere
    // The file cleanup can be handled by a separate background job

    return res.status(200).json({
      success: true,
      message: 'Category image removed successfully',
      data: {
        category: {
          id: updatedCategory.id,
          title: updatedCategory.title,
          imageId: updatedCategory.imageId,
          parentId: updatedCategory.parentId,
          _count: updatedCategory._count
        }
      }
    });

  } catch (error: any) {
    console.error('[API] Error removing category image:', error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error.message || 'Failed to remove category image';
    return res.status(statusCode).json({
      success: false,
      message
    });
  }
};
