import { HttpError } from 'wasp/server';
import { type User, type Appointment, type SProvider, type ProviderCategory, type Advertisement } from 'wasp/entities'; // Import necessary types
import type {
    GetAdminCustomerDetails,
    GetAdminProviders,
    GetAdminCustomers,
    CreateAdminUser,
    UpdateProviderStatus,
    GetProviderCategories,
    CreateProviderCategory,
    UpdateProviderCategory,
    DeleteProviderCategory,
    GetAdvertisements,
    CreateAdvertisement,
    UpdateAdvertisement,
    DeleteAdvertisement
} from 'wasp/server/operations';
import { z } from 'zod';
import { ensureArgsSchemaOrThrowHttpError } from '../server/validation';
import { sanitizeAndSerializeProviderData, createProviderId } from 'wasp/server/auth';
import { Prisma, Role } from '@prisma/client';
// Define expected context, assuming admin role is checked
type AdminContext = {
    user?: { id: string, role: string };
    entities: any;
}

// Define input schemas
const GetAdminCustomerDetailsInputSchema = z.object({
    customerId: z.string().uuid(), // Expecting a UUID for the customer ID
});

const GetAdminProvidersInputSchema = z.object({
    page: z.number().min(1).optional().default(1),
    limit: z.number().min(1).max(100).optional().default(20),
    isVerified: z.boolean().optional(),
    search: z.string().optional(),
});

const GetAdminCustomersInputSchema = z.object({
    page: z.number().min(1).optional().default(1),
    limit: z.number().min(1).max(100).optional().default(20),
    search: z.string().optional(),
});

const CreateAdminUserInputSchema = z.object({
    email: z.string().email(),
    firstName: z.string().min(1),
    lastName: z.string().min(1),
    password: z.string().min(8),
    role: z.enum(['ADMIN', 'CLIENT']).optional().default('ADMIN'),
});

const UpdateProviderStatusInputSchema = z.object({
    providerId: z.number().int().positive(),
    isVerified: z.boolean(),
    reason: z.string().optional(),
});

const GetProviderCategoriesInputSchema = z.object({
    includeHierarchy: z.boolean().optional().default(true),
});

const CreateProviderCategoryInputSchema = z.object({
    title: z.string().min(1),
    description: z.string().optional(),
    parentId: z.union([
        z.number().int().positive(),
        z.string().transform((val) => {
            if (val === '' || val === null || val === undefined) return undefined;
            const num = parseInt(val);
            if (isNaN(num) || num <= 0) throw new Error('Invalid parentId');
            return num;
        })
    ]).optional(),
    isActive: z.boolean().optional().default(true),
    sortOrder: z.union([
        z.number().int(),
        z.string().transform((val) => {
            const num = parseInt(val);
            if (isNaN(num)) throw new Error('Invalid sortOrder');
            return num;
        })
    ]).optional().default(0),
    metadata: z.object({
        icon: z.string().optional().default(""),
        color: z.string().optional().default("#EF4444"),
        keywords: z.array(z.string()).optional().default([]),
        seoTitle: z.string().optional().default(""),
        seoDescription: z.string().optional().default("")
    }).optional()
});

const UpdateProviderCategoryInputSchema = z.object({
    id: z.number().int().positive(),
    title: z.string().min(1).optional(),
    description: z.string().optional(),
    parentId: z.union([
        z.number().int().positive(),
        z.string().transform((val) => {
            if (val === '' || val === null || val === undefined) return undefined;
            const num = parseInt(val);
            if (isNaN(num) || num <= 0) throw new Error('Invalid parentId');
            return num;
        })
    ]).optional(),
    isActive: z.boolean().optional(),
    sortOrder: z.union([
        z.number().int(),
        z.string().transform((val) => {
            const num = parseInt(val);
            if (isNaN(num)) throw new Error('Invalid sortOrder');
            return num;
        })
    ]).optional(),
    metadata: z.object({
        icon: z.string().optional(),
        color: z.string().optional(),
        keywords: z.array(z.string()).optional(),
        seoTitle: z.string().optional(),
        seoDescription: z.string().optional()
    }).optional()
});

const DeleteProviderCategoryInputSchema = z.object({
    id: z.number().int().positive(),
});

const GetAdvertisementsInputSchema = z.object({
    page: z.number().min(1).optional().default(1),
    limit: z.number().min(1).max(100).optional().default(20),
    isActive: z.boolean().optional(),
    search: z.string().optional(),
});

const CreateAdvertisementInputSchema = z.object({
    title: z.string().min(1),
    subtitle: z.string().optional(),
    description: z.string().optional(),
    callToActionText: z.string().min(1),
    callToActionLink: z.string().url(),
    isExternal: z.boolean().optional().default(false),
    isActive: z.boolean().optional().default(true),
    sortOrder: z.number().int().optional().default(0),
    backgroundImageId: z.string().uuid().optional(),
    pngImageId: z.string().uuid().optional(),
});

const UpdateAdvertisementInputSchema = z.object({
    id: z.number().int().positive(),
    title: z.string().min(1).optional(),
    subtitle: z.string().optional(),
    description: z.string().optional(),
    callToActionText: z.string().min(1).optional(),
    callToActionLink: z.string().url().optional(),
    isExternal: z.boolean().optional(),
    isActive: z.boolean().optional(),
    sortOrder: z.number().int().optional(),
    backgroundImageId: z.string().uuid().optional(),
    pngImageId: z.string().uuid().optional(),
});

const DeleteAdvertisementInputSchema = z.object({
    id: z.number().int().positive(),
});

type GetAdminCustomerDetailsInput = z.infer<typeof GetAdminCustomerDetailsInputSchema>;
type GetAdminProvidersInput = z.infer<typeof GetAdminProvidersInputSchema>;
type GetAdminCustomersInput = z.infer<typeof GetAdminCustomersInputSchema>;
type CreateAdminUserInput = z.infer<typeof CreateAdminUserInputSchema>;
type UpdateProviderStatusInput = z.infer<typeof UpdateProviderStatusInputSchema>;
type GetProviderCategoriesInput = z.infer<typeof GetProviderCategoriesInputSchema>;
type CreateProviderCategoryInput = z.infer<typeof CreateProviderCategoryInputSchema>;
type UpdateProviderCategoryInput = z.infer<typeof UpdateProviderCategoryInputSchema>;
type DeleteProviderCategoryInput = z.infer<typeof DeleteProviderCategoryInputSchema>;
type GetAdvertisementsInput = z.infer<typeof GetAdvertisementsInputSchema>;
type CreateAdvertisementInput = z.infer<typeof CreateAdvertisementInputSchema>;
type UpdateAdvertisementInput = z.infer<typeof UpdateAdvertisementInputSchema>;
type DeleteAdvertisementInput = z.infer<typeof DeleteAdvertisementInputSchema>;

// Helper function to check admin authorization
function ensureAdminAccess(context: AdminContext): void {
    if (!context.user || context.user.role !== 'ADMIN') {
        throw new HttpError(403, 'Admin access required');
    }
}

// Existing function
export const getAdminCustomerDetails: GetAdminCustomerDetails<GetAdminCustomerDetailsInput, User | null> = async (
    rawArgs: GetAdminCustomerDetailsInput,
    context: AdminContext
): Promise<User | null> => {
    // Authorization: Ensure the caller is an Admin or appropriate role
    if (!context.user || (context.user.role !== 'ADMIN' && context.user.role !== 'CLIENT' )) {
        throw new HttpError(403, 'User is not authorized to view customer details.');
    }

    const args = ensureArgsSchemaOrThrowHttpError(GetAdminCustomerDetailsInputSchema, rawArgs);

    try {
        const customer = await context.entities.User.findUnique({
            where: {
                id: args.customerId,
                customerFolders: {
                    some: {
                        provider: {
                            userId: context.user.id
                        }
                    }
                }
                // Optional: Add role check if needed, though ID should be specific
                // role: 'CUSTOMER', 
            },
            include: {
                // Include relevant details for the admin profile view
                customerFolders: {
                    include: {
                        provider: {
                             select: { id: true, title: true, user: { select: { firstName: true, lastName: true }} }
                        },
                        appointments: {
                            orderBy: { expectedAppointmentStartTime: 'desc' }, // Show recent first
                            // take: 10, // Limit number of appointments shown initially
                            include: {
                                service: { select: { title: true } },
                                place: { select: { name: true } },
                                history: true
                                // canceledByRole: true // Add this field
                            }
                        },
                        
                    }
                },
                // You might want to exclude sensitive auth details
                // auth: false, 
            }
        });

        if (!customer) {
            // Return null or throw 404 depending on desired frontend handling
            // throw new HttpError(404, 'Customer not found.');
            return null;
        }

        // Consider filtering or transforming data before sending if needed
        // e.g., removing sensitive fields

        return customer;

    } catch (error: any) {
        console.error(`Failed to get customer details for ID ${args.customerId}:`, error);
        if (error instanceof HttpError) {
            throw error;
        }
        throw new HttpError(500, 'Failed to retrieve customer details.');
    }
};

// New admin operations
export const getAdminProviders: GetAdminProviders<GetAdminProvidersInput, any> = async (
    rawArgs: GetAdminProvidersInput,
    context: AdminContext
): Promise<any> => {
    ensureAdminAccess(context);
    
    const args = ensureArgsSchemaOrThrowHttpError(GetAdminProvidersInputSchema, rawArgs);
    
    try {
        const skip = (args.page - 1) * args.limit;
        
        const whereClause: any = {};
        
        if (args.isVerified !== undefined) {
            whereClause.isVerified = args.isVerified;
        }
        
        if (args.search) {
            whereClause.OR = [
                { title: { contains: args.search, mode: 'insensitive' } },
                { user: { firstName: { contains: args.search, mode: 'insensitive' } } },
                { user: { lastName: { contains: args.search, mode: 'insensitive' } } },
                { user: { email: { contains: args.search, mode: 'insensitive' } } },
            ];
        }
        
        const [providers, totalCount] = await Promise.all([
            context.entities.SProvider.findMany({
                where: whereClause,
                include: {
                    user: {
                        select: {
                            id: true,
                            firstName: true,
                            lastName: true,
                            email: true,
                            mobileNumber: true,
                            createdAt: true,
                        }
                    },
                    category: {
                        select: {
                            id: true,
                            title: true,
                        }
                    },
                    _count: {
                        select: {
                            services: true,
                            customerFolders: true,
                            reviewsReceived: true,
                        }
                    }
                },
                skip,
                take: args.limit,
                orderBy: { createdAt: 'desc' },
            }),
            context.entities.SProvider.count({ where: whereClause })
        ]);
        
        return {
            providers,
            pagination: {
                page: args.page,
                limit: args.limit,
                totalCount,
                totalPages: Math.ceil(totalCount / args.limit),
            }
        };
        
    } catch (error: any) {
        console.error('Failed to get admin providers:', error);
        if (error instanceof HttpError) {
            throw error;
        }
        throw new HttpError(500, 'Failed to retrieve providers.');
    }
};

export const getAdminCustomers: GetAdminCustomers<GetAdminCustomersInput, any> = async (
    rawArgs: GetAdminCustomersInput,
    context: AdminContext
): Promise<any> => {
    ensureAdminAccess(context);
    
    const args = ensureArgsSchemaOrThrowHttpError(GetAdminCustomersInputSchema, rawArgs);
    
    try {
        const skip = (args.page - 1) * args.limit;
        
        const whereClause: any = {
            OR: [
                { role: 'CUSTOMER' },
                { customerFolders: { some: {} } }, // Users who have customer folders
            ]
        };
        
        if (args.search) {
            whereClause.AND = [
                whereClause,
                {
                    OR: [
                        { firstName: { contains: args.search, mode: 'insensitive' } },
                        { lastName: { contains: args.search, mode: 'insensitive' } },
                        { email: { contains: args.search, mode: 'insensitive' } },
                        { mobileNumber: { contains: args.search, mode: 'insensitive' } },
                    ]
                }
            ];
        }
        
        const [customers, totalCount] = await Promise.all([
            context.entities.User.findMany({
                where: whereClause,
                select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                    mobileNumber: true,
                    createdAt: true,
                    isPhoneVerified: true,
                    isEmailVerified: true,
                    _count: {
                        select: {
                            customerFolders: true,
                        }
                    }
                },
                skip,
                take: args.limit,
                orderBy: { createdAt: 'desc' },
            }),
            context.entities.User.count({ where: whereClause })
        ]);
        
        return {
            customers,
            pagination: {
                page: args.page,
                limit: args.limit,
                totalCount,
                totalPages: Math.ceil(totalCount / args.limit),
            }
        };
        
    } catch (error: any) {
        console.error('Failed to get admin customers:', error);
        if (error instanceof HttpError) {
            throw error;
        }
        throw new HttpError(500, 'Failed to retrieve customers.');
    }
};

export const createAdminUser: CreateAdminUser<CreateAdminUserInput, User> = async (
    rawArgs: CreateAdminUserInput,
    context: AdminContext
): Promise<User> => {
    ensureAdminAccess(context);
    
    const args = ensureArgsSchemaOrThrowHttpError(CreateAdminUserInputSchema, rawArgs);
    
    try {
        // Check if user already exists
        const existingUser = await context.entities.User.findUnique({
            where: { email: args.email }
        });
        
        if (existingUser) {
            throw new HttpError(400, 'User with this email already exists');
        }
        
        // Create the user with admin role
        const { hash } = await import('bcryptjs');
        const hashedPassword = await hash(args.password, 12);
        
        const providerData = await sanitizeAndSerializeProviderData({ 
            hashedPassword: hashedPassword 
        });
        
        const user = await context.entities.User.create({
            data: {
                email: args.email,
                username: args.email,
                firstName: args.firstName,
                lastName: args.lastName,
                role: args.role,
                isAdmin: args.role === 'ADMIN',
                isEmailVerified: true, // Admin users are pre-verified
                auth: {
                    create: {
                        identities: {
                            create: {
                                providerName: 'email',
                                providerUserId: args.email,
                                providerData: JSON.stringify(providerData),
                            }
                        }
                    }
                }
            },
            include: {
                auth: true,
            }
        });
        
        return user;
        
    } catch (error: any) {
        console.error('Failed to create admin user:', error);
        if (error instanceof HttpError) {
            throw error;
        }
        throw new HttpError(500, 'Failed to create admin user.');
    }
};

export const updateProviderStatus: UpdateProviderStatus<UpdateProviderStatusInput, SProvider> = async (
    rawArgs: UpdateProviderStatusInput,
    context: AdminContext
): Promise<SProvider> => {
    ensureAdminAccess(context);
    
    const args = ensureArgsSchemaOrThrowHttpError(UpdateProviderStatusInputSchema, rawArgs);
    
    try {
        const provider = await context.entities.SProvider.findUnique({
            where: { id: args.providerId },
            include: { user: true }
        });
        
        if (!provider) {
            throw new HttpError(404, 'Provider not found');
        }
        
        const updatedProvider = await context.entities.SProvider.update({
            where: { id: args.providerId },
            data: {
                isVerified: args.isVerified,
                updatedAt: new Date(),
            },
            include: {
                user: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        mobileNumber: true,
                    }
                },
                category: {
                    select: {
                        id: true,
                        title: true,
                    }
                }
            }
        });
        
        // TODO: Send notification to provider about status change
        console.log(`Provider ${provider.user.firstName} ${provider.user.lastName} verification status updated to: ${args.isVerified}`);
        
        return updatedProvider;
        
    } catch (error: any) {
        console.error('Failed to update provider status:', error);
        if (error instanceof HttpError) {
            throw error;
        }
        throw new HttpError(500, 'Failed to update provider status.');
    }
};

export const getAdminProviderCategories: GetProviderCategories<GetProviderCategoriesInput, ProviderCategory[]> = async (
    rawArgs: GetProviderCategoriesInput,
    context: AdminContext
): Promise<ProviderCategory[]> => {
    ensureAdminAccess(context);
    
    const args = ensureArgsSchemaOrThrowHttpError(GetProviderCategoriesInputSchema, rawArgs);
    
    try {
        const include = args.includeHierarchy ? {
            parent: true,
            children: true,
            _count: {
                select: {
                    providers: true,
                    children: true,
                }
            }
        } : {
            _count: {
                select: {
                    providers: true,
                }
            }
        };
        
        const categories = await context.entities.ProviderCategory.findMany({
            include,
            orderBy: { id: 'asc' }
        });
        
        return categories;
        
    } catch (error: any) {
        console.error('Failed to get provider categories:', error);
        if (error instanceof HttpError) {
            throw error;
        }
        throw new HttpError(500, 'Failed to retrieve provider categories.');
    }
};

export const createProviderCategory: CreateProviderCategory<CreateProviderCategoryInput, ProviderCategory> = async (
    rawArgs: CreateProviderCategoryInput,
    context: AdminContext
): Promise<ProviderCategory> => {
    ensureAdminAccess(context);
    
    const args = ensureArgsSchemaOrThrowHttpError(CreateProviderCategoryInputSchema, rawArgs);
    
    try {
        // Check if category with this title already exists
        const existingCategory = await context.entities.ProviderCategory.findUnique({
            where: { title: args.title }
        });
        
        if (existingCategory) {
            throw new HttpError(400, 'Category with this title already exists');
        }
        
        // If parentId is provided, check if parent exists
        if (args.parentId) {
            const parentCategory = await context.entities.ProviderCategory.findUnique({
                where: { id: args.parentId }
            });
            
            if (!parentCategory) {
                throw new HttpError(400, 'Parent category not found');
            }
        }
        
        const category = await context.entities.ProviderCategory.create({
            data: {
                title: args.title,
                description: args.description || null,
                parentId: args.parentId || null,
                isActive: args.isActive,
                sortOrder: args.sortOrder,
                metadata: args.metadata || null,
            },
            include: {
                parent: true,
                children: true,
                _count: {
                    select: {
                        providers: true,
                        children: true,
                    }
                }
            }
        });
        
        return category;
        
    } catch (error: any) {
        console.error('Failed to create provider category:', error);
        if (error instanceof HttpError) {
            throw error;
        }
        throw new HttpError(500, 'Failed to create provider category.');
    }
};

export const updateProviderCategory: UpdateProviderCategory<UpdateProviderCategoryInput, ProviderCategory> = async (
    rawArgs: UpdateProviderCategoryInput,
    context: AdminContext
): Promise<ProviderCategory> => {
    ensureAdminAccess(context);
    
    const args = ensureArgsSchemaOrThrowHttpError(UpdateProviderCategoryInputSchema, rawArgs);
    
    try {
        const existingCategory = await context.entities.ProviderCategory.findUnique({
            where: { id: args.id }
        });
        
        if (!existingCategory) {
            throw new HttpError(404, 'Category not found');
        }
        
        // Check if another category with this title exists (excluding current one) - only if title is being updated
        if (args.title) {
            const titleConflict = await context.entities.ProviderCategory.findFirst({
                where: {
                    title: args.title,
                    id: { not: args.id }
                }
            });

            if (titleConflict) {
                throw new HttpError(400, 'Category with this title already exists');
            }
        }
        
        // If parentId is provided, check if parent exists and prevent circular references
        if (args.parentId) {
            if (args.parentId === args.id) {
                throw new HttpError(400, 'Category cannot be its own parent');
            }
            
            const parentCategory = await context.entities.ProviderCategory.findUnique({
                where: { id: args.parentId }
            });
            
            if (!parentCategory) {
                throw new HttpError(400, 'Parent category not found');
            }
            
            // Check for circular reference (parent should not be a child of this category)
            const descendants = await context.entities.ProviderCategory.findMany({
                where: { parentId: args.id }
            });
            
            for (const descendant of descendants) {
                if (descendant.id === args.parentId) {
                    throw new HttpError(400, 'Cannot create circular reference');
                }
            }
        }
        
        // Build update data object with only provided fields
        const updateData: any = {};
        if (args.title !== undefined) updateData.title = args.title;
        if (args.description !== undefined) updateData.description = args.description;
        if (args.parentId !== undefined) updateData.parentId = args.parentId;
        if (args.isActive !== undefined) updateData.isActive = args.isActive;
        if (args.sortOrder !== undefined) updateData.sortOrder = args.sortOrder;
        if (args.metadata !== undefined) updateData.metadata = args.metadata;

        const category = await context.entities.ProviderCategory.update({
            where: { id: args.id },
            data: updateData,
            include: {
                parent: true,
                children: true,
                _count: {
                    select: {
                        providers: true,
                        children: true,
                    }
                }
            }
        });
        
        return category;
        
    } catch (error: any) {
        console.error('Failed to update provider category:', error);
        if (error instanceof HttpError) {
            throw error;
        }
        throw new HttpError(500, 'Failed to update provider category.');
    }
};

export const deleteProviderCategory: DeleteProviderCategory<DeleteProviderCategoryInput, { success: boolean }> = async (
    rawArgs: DeleteProviderCategoryInput,
    context: AdminContext
): Promise<{ success: boolean }> => {
    ensureAdminAccess(context);
    
    const args = ensureArgsSchemaOrThrowHttpError(DeleteProviderCategoryInputSchema, rawArgs);
    
    try {
        const category = await context.entities.ProviderCategory.findUnique({
            where: { id: args.id },
            include: {
                children: true,
                providers: true,
            }
        });
        
        if (!category) {
            throw new HttpError(404, 'Category not found');
        }
        
        // Check if category has children
        if (category.children.length > 0) {
            throw new HttpError(400, 'Cannot delete category with child categories. Please delete or reassign child categories first.');
        }
        
        // Check if category has providers
        if (category.providers.length > 0) {
            throw new HttpError(400, 'Cannot delete category with assigned providers. Please reassign providers first.');
        }
        
        await context.entities.ProviderCategory.delete({
            where: { id: args.id }
        });
        
        return { success: true };
        
    } catch (error: any) {
        console.error('Failed to delete provider category:', error);
        if (error instanceof HttpError) {
            throw error;
        }
        throw new HttpError(500, 'Failed to delete provider category.');
    }
};

// Advertisement Operations
export const getAdvertisements: GetAdvertisements<GetAdvertisementsInput, { advertisements: Advertisement[], totalCount: number }> = async (
    rawArgs: GetAdvertisementsInput,
    context: AdminContext
): Promise<{ advertisements: Advertisement[], totalCount: number }> => {
    ensureAdminAccess(context);

    const args = ensureArgsSchemaOrThrowHttpError(GetAdvertisementsInputSchema, rawArgs);

    try {
        const skip = (args.page - 1) * args.limit;

        const whereClause: any = {};

        if (args.isActive !== undefined) {
            whereClause.isActive = args.isActive;
        }

        if (args.search) {
            whereClause.OR = [
                { title: { contains: args.search, mode: 'insensitive' } },
                { subtitle: { contains: args.search, mode: 'insensitive' } },
                { description: { contains: args.search, mode: 'insensitive' } },
            ];
        }

        const [advertisements, totalCount] = await Promise.all([
            context.entities.Advertisement.findMany({
                where: whereClause,
                include: {
                    backgroundImage: true,
                    pngImage: true,
                },
                orderBy: [
                    { sortOrder: 'asc' },
                    { createdAt: 'desc' }
                ],
                skip,
                take: args.limit,
            }),
            context.entities.Advertisement.count({
                where: whereClause,
            }),
        ]);

        return { advertisements, totalCount };

    } catch (error: any) {
        console.error('Failed to retrieve advertisements:', error);
        if (error instanceof HttpError) {
            throw error;
        }
        throw new HttpError(500, 'Failed to retrieve advertisements.');
    }
};

export const createAdvertisement: CreateAdvertisement<CreateAdvertisementInput, Advertisement> = async (
    rawArgs: CreateAdvertisementInput,
    context: AdminContext
): Promise<Advertisement> => {
    ensureAdminAccess(context);

    const args = ensureArgsSchemaOrThrowHttpError(CreateAdvertisementInputSchema, rawArgs);

    try {
        // Validate that image IDs exist if provided
        if (args.backgroundImageId) {
            const backgroundImage = await context.entities.File.findUnique({
                where: { id: args.backgroundImageId }
            });
            if (!backgroundImage) {
                throw new HttpError(400, 'Background image not found');
            }
        }

        if (args.pngImageId) {
            const pngImage = await context.entities.File.findUnique({
                where: { id: args.pngImageId }
            });
            if (!pngImage) {
                throw new HttpError(400, 'PNG image not found');
            }
        }

        const advertisement = await context.entities.Advertisement.create({
            data: {
                title: args.title,
                subtitle: args.subtitle,
                description: args.description,
                callToActionText: args.callToActionText,
                callToActionLink: args.callToActionLink,
                isExternal: args.isExternal ?? false,
                isActive: args.isActive ?? true,
                sortOrder: args.sortOrder ?? 0,
                backgroundImageId: args.backgroundImageId,
                pngImageId: args.pngImageId,
            },
            include: {
                backgroundImage: true,
                pngImage: true,
            }
        });

        return advertisement;

    } catch (error: any) {
        console.error('Failed to create advertisement:', error);
        if (error instanceof HttpError) {
            throw error;
        }
        throw new HttpError(500, 'Failed to create advertisement.');
    }
};

export const updateAdvertisement: UpdateAdvertisement<UpdateAdvertisementInput, Advertisement> = async (
    rawArgs: UpdateAdvertisementInput,
    context: AdminContext
): Promise<Advertisement> => {
    ensureAdminAccess(context);

    const args = ensureArgsSchemaOrThrowHttpError(UpdateAdvertisementInputSchema, rawArgs);

    try {
        const existingAdvertisement = await context.entities.Advertisement.findUnique({
            where: { id: args.id }
        });

        if (!existingAdvertisement) {
            throw new HttpError(404, 'Advertisement not found');
        }

        // Validate that image IDs exist if provided
        if (args.backgroundImageId) {
            const backgroundImage = await context.entities.File.findUnique({
                where: { id: args.backgroundImageId }
            });
            if (!backgroundImage) {
                throw new HttpError(400, 'Background image not found');
            }
        }

        if (args.pngImageId) {
            const pngImage = await context.entities.File.findUnique({
                where: { id: args.pngImageId }
            });
            if (!pngImage) {
                throw new HttpError(400, 'PNG image not found');
            }
        }

        const updateData: any = {};

        if (args.title !== undefined) updateData.title = args.title;
        if (args.subtitle !== undefined) updateData.subtitle = args.subtitle;
        if (args.description !== undefined) updateData.description = args.description;
        if (args.callToActionText !== undefined) updateData.callToActionText = args.callToActionText;
        if (args.callToActionLink !== undefined) updateData.callToActionLink = args.callToActionLink;
        if (args.isExternal !== undefined) updateData.isExternal = args.isExternal;
        if (args.isActive !== undefined) updateData.isActive = args.isActive;
        if (args.sortOrder !== undefined) updateData.sortOrder = args.sortOrder;
        if (args.backgroundImageId !== undefined) updateData.backgroundImageId = args.backgroundImageId;
        if (args.pngImageId !== undefined) updateData.pngImageId = args.pngImageId;

        const advertisement = await context.entities.Advertisement.update({
            where: { id: args.id },
            data: updateData,
            include: {
                backgroundImage: true,
                pngImage: true,
            }
        });

        return advertisement;

    } catch (error: any) {
        console.error('Failed to update advertisement:', error);
        if (error instanceof HttpError) {
            throw error;
        }
        throw new HttpError(500, 'Failed to update advertisement.');
    }
};

export const deleteAdvertisement: DeleteAdvertisement<DeleteAdvertisementInput, { success: boolean }> = async (
    rawArgs: DeleteAdvertisementInput,
    context: AdminContext
): Promise<{ success: boolean }> => {
    ensureAdminAccess(context);

    const args = ensureArgsSchemaOrThrowHttpError(DeleteAdvertisementInputSchema, rawArgs);

    try {
        const advertisement = await context.entities.Advertisement.findUnique({
            where: { id: args.id }
        });

        if (!advertisement) {
            throw new HttpError(404, 'Advertisement not found');
        }

        await context.entities.Advertisement.delete({
            where: { id: args.id }
        });

        return { success: true };

    } catch (error: any) {
        console.error('Failed to delete advertisement:', error);
        if (error instanceof HttpError) {
            throw error;
        }
        throw new HttpError(500, 'Failed to delete advertisement.');
    }
};