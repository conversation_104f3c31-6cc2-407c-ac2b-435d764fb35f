import { type AuthUser } from 'wasp/auth';
import { useState, ReactNode, FC, useMemo, useEffect } from 'react'; // Import useMemo
import Header from './Header';
import Sidebar from './Sidebar';
import { Toaster } from 'react-hot-toast';
import { Layout } from 'antd';
import { ProfileCompletionCard, isProfileCompletionCardDismissed } from '../../provider/components/ProfileCompletionCard';
import { getUserServiceProvider, useQuery } from 'wasp/client/operations';
import { ProfileCompletionData } from '../../provider/utils/profileCompletion';

interface Props {
  user: AuthUser;
  children?: ReactNode;
}

const DefaultLayout: FC<Props> = ({ children, user }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [isCardVisible, setIsCardVisible] = useState(true);
  
  const { 
    data: providerData, 
    isLoading, 
    error 
  } = useQuery(getUserServiceProvider, 
    { userId: user.id },
    { 
      enabled: !!user.id,
      // Include all relations needed for completion calculation
      select: (data: any) => data ? {
        ...data,
        providingPlaces: data.providingPlaces || [],
        services: data.services || [],
        queues: data.queues || []
      } : null
    }
  );
  
  // Check if card was dismissed on component mount
  useEffect(() => {
    const wasDismissed = isProfileCompletionCardDismissed(user.id);
    setIsCardVisible(!wasDismissed);
  }, [user.id]);

  const handleCardClose = () => {
    setIsCardVisible(false);
  };
  return (
      <Layout className='min-h-screen'>
        <Toaster position="top-center" />
        <Layout hasSider>
          <Sidebar sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />
          <Layout className='dark:bg-boxdark-2 dark:text-bodydark'>
            {/* Header contains the DarkModeSwitcher which triggers the colorMode change */}
            <Header sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} user={user} />
            <Layout.Content className='overflow-y-auto overflow-x-hidden'>
              {/* Only show ProfileCompletionCard if providerData exists, not loading, and not dismissed */}
              {!isLoading && !error && providerData && isCardVisible && (
                <div className="p-4">
                  <ProfileCompletionCard 
                    data={{
                      user: user as any,
                      provider: providerData as any
                    }}
                    defaultExpanded={false}
                    onClose={handleCardClose}
                  />
                </div>
              )}
              <div className='mx-auto max-w-screen-2xl p-4 md:p-6 2xl:p-10'>
                {children}
              </div>
            </Layout.Content>
          </Layout>
        </Layout>
      </Layout>
  );
};

export default DefaultLayout;
