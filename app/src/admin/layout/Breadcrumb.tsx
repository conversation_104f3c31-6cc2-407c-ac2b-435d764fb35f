import { Link as Wasp<PERSON><PERSON><PERSON><PERSON><PERSON>, routes } from 'wasp/client/router';
import { Breadcrumb as AntBreadcrumb, Typography } from 'antd';
import { HomeOutlined } from '@ant-design/icons';

interface BreadcrumbProps {
  pageName: string;
}

const Breadcrumb = ({ pageName }: BreadcrumbProps) => {
  return (
    <div className='mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between'>
      <Typography.Title level={2} className='!text-black dark:!text-white !m-0'>
        {pageName}
      </Typography.Title>

      <AntBreadcrumb
        items={[
          {
            href: routes.AdminRoute.to,
            title: (
              <>
                <HomeOutlined /> Dashboard
              </>
            ),
          },
          {
            title: pageName,
          },
        ]}
      />
    </div>
  );
};

export default Breadcrumb;
