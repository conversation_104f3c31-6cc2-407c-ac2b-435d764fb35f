import { type AuthUser } from 'wasp/auth';
import MessageButton from '../../messages/MessageButton';
import DropdownUser from '../../user/DropdownUser';
import DarkModeSwitcher from '../../client/components/DarkModeSwitcher';
import { Layout, Button, Space, Alert } from 'antd';
import { MenuFoldOutlined, MenuOutlined, MenuUnfoldOutlined, CreditCardOutlined } from '@ant-design/icons';
import NotificationBell from '../../client/notifications/NotificationBell';

const Header = (props: {
  sidebarOpen: string | boolean | undefined;
  setSidebarOpen: (arg0: boolean) => void;
  user: AuthUser;
}) => {
  return (
    <Layout.Header className='!px-8 !h-auto sticky top-0 z-50 flex bg-white dark:bg-boxdark dark:drop-shadow-none'>
      <div className='flex flex-grow items-center justify-between lg:justify-end sm:gap-5 py-5'>
        <div className='flex items-center gap-2 sm:gap-4 lg:hidden'>
          <Button
            type="text"
            size='large'
            icon={props.sidebarOpen ? <MenuOutlined /> : <MenuOutlined />}
            onClick={(e:any) => {
              e.stopPropagation();
              props.setSidebarOpen(!props.sidebarOpen);
            }}
            className='lg:hidden'
          />
         
        </div>

        {props.user.credits <= 0 && (
            <Alert action={
              <Button type="primary" size='small' onClick={() => {
                const host = window.location.origin;
                window.open(`${host}/pricing`, '_blank');
              }}>
                Buy Credits
              </Button>
            } 
            icon={<CreditCardOutlined />} 
            showIcon 
            message="You have no credits left" 
            type="error" 
            style={{ 
              marginRight: 'auto',
              minWidth: '350px'
            }} />
        )}
        <NotificationBell /> 
        <Space size="middle" className='flex items-center'>
          <DropdownUser user={props.user} />
        </Space>
      </div>
    </Layout.Header>
  );
};

export default Header;
