import React, { useEffect, useRef } from 'react';
import { NavLink, useLocation, useNavigate } from 'react-router-dom';
import Logo from '../../client/static/logo.webp';
import {
  FaTachometerAlt,
  FaCalendarAlt,
  FaUsers,
  FaConciergeBell,
  FaFolderOpen,
  FaUserCircle,
  FaCog,
  FaLayerGroup,
  FaEnvelope,
} from 'react-icons/fa';
import { Layout, Menu, Button, Divider } from 'antd';
import type { MenuProps } from 'antd';
import type { MenuInfo } from 'rc-menu/lib/interface';
import { CloseOutlined } from '@ant-design/icons';
import DarkModeSwitcher from '../../client/components/DarkModeSwitcher';
import { CalendarOutlined } from '@ant-design/icons';

interface SidebarProps {
  sidebarOpen: boolean;
  setSidebarOpen: (arg: boolean) => void;
}

type MenuItem = Required<MenuProps>['items'][number];

function getItem(
  label: React.ReactNode,
  key: React.Key,
  icon?: React.ReactNode,
  children?: MenuItem[],
  type?: 'group'
): MenuItem {
  return {
    key,
    icon,
    children,
    label,
    type,
  } as MenuItem;
}

const Sidebar = ({ sidebarOpen, setSidebarOpen }: SidebarProps) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { pathname } = location;

  const menuItems: MenuProps['items'] = React.useMemo(() => [
    getItem('MENU', 'grp1', null, [
        getItem('Dashboard', '/admin', <FaTachometerAlt />),
        getItem('Calendar', '/admin/calendar', <FaCalendarAlt />),
        getItem('Appointments', '/admin/appointments', <CalendarOutlined />),
        getItem('Customers', '/admin/clients', <FaUsers />),
        getItem('Services', '/admin/services', <FaConciergeBell />),
        getItem('Queues', '/admin/queues', <FaLayerGroup />),
        getItem('Categories', '/admin/categories', <FaFolderOpen />),
    ], 'group'),

    getItem('OTHER', 'grp2', null, [
        getItem('Messages', '/admin/messages', <FaEnvelope />),
        getItem('Account', '/admin/account', <FaUserCircle />),
        getItem('Settings', '/admin/settings', <FaCog />),
    ], 'group'),
  ], []);

  const handleMenuClick = React.useCallback((e: MenuInfo) => {
    navigate(e.key);
    if (window.innerWidth < 1024) {
       setSidebarOpen(false);
    }
  }, [navigate, setSidebarOpen]);

  const getSelectedKeys = React.useCallback(() => {
    if (pathname === '/admin') return ['/admin'];
    const allItems = menuItems?.flatMap(group => (group as any)?.children || []) || [];
    const matchingKey = allItems
        .map(item => item?.key as string)
        .filter(key => key && key !== '/admin' && pathname.startsWith(key))
        .sort((a, b) => b.length - a.length)[0];
    return matchingKey ? [matchingKey] : [pathname];
  }, [pathname, menuItems]);

  return (
    <Layout.Sider
        collapsed={!sidebarOpen}
        breakpoint="lg"
        collapsedWidth="0"
        trigger={null}
        onCollapse={(collapsed: boolean, type: 'responsive' | 'clickTrigger') => {
            if (type === 'responsive') {
                setSidebarOpen(!collapsed);
            }
        }}
        theme="dark"
        width={290}
        className='!fixed lg:!sticky left-0 top-0 z-9999 h-screen overflow-y-auto no-scrollbar'
        style={{ height: '100vh' }}
    >
      <div className='mt-4 flex items-center justify-between gap-2 px-6 py-5.5 lg:py-6.5 h-[68px]'>
        <NavLink to='/'>
          {!sidebarOpen && window.innerWidth >= 1024 ? null : <img src={Logo} alt='Logo' width={50} />}
        </NavLink>
        <Button
            type="text"
            icon={<CloseOutlined />}
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden text-bodydark1 hover:text-white"
            aria-label="Close sidebar"
        />
      </div>

      <div className='no-scrollbar flex flex-col overflow-y-auto duration-300 ease-linear'>
         <Menu
            theme="dark"
            mode="inline"
            selectedKeys={getSelectedKeys()}
            items={menuItems}
            onClick={handleMenuClick}
            style={{ background: 'transparent', borderRight: 0 }}
            className="mt-5 py-4 px-4 lg:mt-9 lg:px-6"
            inlineCollapsed={!sidebarOpen}
          />
          <Divider />
          <div className='mt-auto flex justify-between items-center gap-2 px-6 py-1'>
            <span className='text-sm text-gray-500'>Dark Mode</span>
            <DarkModeSwitcher />
          </div>
      </div>
    </Layout.Sider>
  );
};

export default Sidebar;
