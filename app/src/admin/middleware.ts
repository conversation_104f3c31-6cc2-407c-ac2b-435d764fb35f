import { HttpError } from 'wasp/server';

/**
 * Middleware to ensure user is an admin
 */
export const ensureAdminMiddleware = (req: any, res: any, context: any) => {
  if (!context.user) {
    throw new HttpError(401, 'Authentication required');
  }
  
  if (context.user.role !== 'ADMIN') {
    throw new HttpError(403, 'Admin access required');
  }
  
  return context;
};

/**
 * Wrap admin API handlers with authentication check
 */
export const withAdminAuth = (handler: Function) => {
  return async (req: any, res: any, context: any) => {
    try {
      ensureAdminMiddleware(req, res, context);
      return await handler(req, res, context);
    } catch (error: any) {
      if (error instanceof HttpError) {
        res.status(error.statusCode || 500).json({
          error: error.message
        });
      } else {
        res.status(500).json({
          error: 'Internal server error'
        });
      }
    }
  };
}; 