import React, { useState, FormEvent, useEffect, useMemo } from 'react'; // Import useMemo
import { type AuthUser } from 'wasp/auth';
import { type Service,  } from 'wasp/entities';
import {
    useQuery,
    useAction,
    getServices,
    createService,
    updateService,
    deleteService,
    getServiceCategories
} from 'wasp/client/operations';
import DefaultLayout from '../../layout/DefaultLayout';
import Breadcrumb from '../../layout/Breadcrumb';
import { useRedirectHomeUnlessUserIsAdmin } from '../../useRedirectHomeUnlessUserIsAdmin';
// Replace the existing Ant Design import with the following:
import Table from 'antd/es/table';
import Button from 'antd/es/button';
import Modal from 'antd/es/modal';
import Form from 'antd/es/form';
import Input from 'antd/es/input';
import Select from 'antd/es/select';
import InputNumber from 'antd/es/input-number';
import Checkbox from 'antd/es/checkbox';
import Space from 'antd/es/space';
import message from 'antd/es/message';
import Spin from 'antd/es/spin';
import Alert from 'antd/es/alert';
import ColorPicker from 'antd/es/color-picker';
// If Ant Design CSS isn't imported globally (e.g., in main.tsx or layout),
// you might need to add CSS imports here or ensure they are loaded elsewhere.
// Example: import 'antd/dist/reset.css';

import { EditOutlined, DeleteOutlined, PlusOutlined } from '@ant-design/icons';

// Define the shape of the form data
interface ServiceFormData {
    title: string;
    // Update the color type to accept string or the ColorPicker object type
    color: string | { toHexString: () => string };
    duration: number | string;
    price: number | string;
    pointsRequirements: number | string;
    isPublic: boolean;
    deliveryType: 'at_location' | 'at_customer' | 'both';
    servedRegions: string[];
    description?: string;
    acceptOnline: boolean;
    acceptNew: boolean;
    notificationOn: boolean;
}

const initialFormData: ServiceFormData = {
    title: '',
    color: '#000000', // Keep initial value as string or adjust if needed
    duration: '60',
    price: '0',
    pointsRequirements: '1',
    isPublic: true,
    deliveryType: 'at_location',
    servedRegions: [],
    description: '',
    acceptOnline: true,
    acceptNew: true,
    notificationOn: true
};

const ServicesPage = ({ user }: { user: AuthUser }) => {
  useRedirectHomeUnlessUserIsAdmin({ user });
  const [form] = Form.useForm();

  // --- Data Fetching ---
  const { data: services, isLoading: isLoadingServices, error: servicesError, refetch: refetchServices } =
    useQuery(getServices);
  const { data: categories, isLoading: isLoadingCategories, error: categoriesError } =
    useQuery(getServiceCategories);

    console.log('Services:', services);
  // --- State ---
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [serviceToEdit, setServiceToEdit] = useState<Service | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [serviceToDelete, setServiceToDelete] = useState<Service | null>(null);
  // Add state for filtering
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | undefined>(undefined);


  // --- Actions ---
  const createServiceAction = useAction(createService);
  const updateServiceAction = useAction(updateService);
  const deleteServiceAction = useAction(deleteService);

  // --- Effects ---
  useEffect(() => {
    if (isEditing && serviceToEdit) {
      form.setFieldsValue({
        title: serviceToEdit.title,
        color: serviceToEdit.color,
        serviceCategoryId: serviceToEdit.serviceCategoryId,
        duration: serviceToEdit.duration,
        minDuration: serviceToEdit.minDuration,
        maxDuration: serviceToEdit.maxDuration,
        queue: serviceToEdit.queue,
        acceptOnline: serviceToEdit.acceptOnline,
        acceptNew: serviceToEdit.acceptNew,
        notificationOn: serviceToEdit.notificationOn,
        pointsRequirements: serviceToEdit.pointsRequirements,
      });
    } else {
      form.resetFields();
    }
  }, [isEditing, serviceToEdit, form]);

  // --- Handlers ---
  const openAddModal = () => {
    setIsEditing(false);
    setServiceToEdit(null);
    form.resetFields();
    setIsModalOpen(true);
  };

  const openEditModal = (service: Service) => {
    setIsEditing(true);
    setServiceToEdit(service);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setIsEditing(false);
    setServiceToEdit(null);
    form.resetFields();
  };

  const handleFormSubmit = async (values: ServiceFormData) => {
    setIsSubmitting(true);

    try {
      console.log('Form values:', values);
      // Check if color is the ColorPicker object before calling toHexString
      const colorValue = typeof values.color === 'object' && values.color && 'toHexString' in values.color
        ? values.color?.toHexString()
        : typeof values.color === 'string' ? values.color : null;

      const serviceData = {
        ...values,
        duration: Number(values.duration),
        price: Number(values.price),
        pointsRequirements: Number(values.pointsRequirements),
        // Convert null to undefined for color to match action expectations
        color: colorValue || '#000000',
        servedRegions: values.servedRegions || [],
        deliveryType: values.deliveryType || 'at_location',
        isPublic: values.isPublic !== undefined ? values.isPublic : true
      };

      if (isEditing && serviceToEdit) {
        // The type error occurs here because updateServiceAction expects color?: string | undefined
        await updateServiceAction({ serviceId: serviceToEdit.id, ...serviceData });
        message.success(`Service "${values.title}" updated successfully!`);
      } else {
        // The type error occurs here because createServiceAction expects color?: string | undefined
        await createServiceAction(serviceData);
        message.success(`Service "${values.title}" added successfully!`);
      }
      refetchServices();
      closeModal();
    } catch (error: any) {
      message.error(`Failed to ${isEditing ? 'update' : 'add'} service: ${error.message || 'Unknown error'}`);
      console.error(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async () => {
    if (!serviceToDelete) return;
    setIsSubmitting(true);
    try {
      await deleteServiceAction({ serviceId: serviceToDelete.id });
      message.success(`Service "${serviceToDelete.title}" deleted successfully!`);
      refetchServices();
      setIsDeleteModalOpen(false);
    } catch (error: any) {
      message.error(`Failed to delete service: ${error.message || 'Unknown error'}`);
      console.error(error);
    } finally {
      setIsSubmitting(false);
      setServiceToDelete(null);
    }
  };

  // --- Filtered Data ---
  const filteredServices = useMemo(() => {
    if (!services) return [];
    return services.filter(service => {
      const matchesSearch = searchTerm
        ? service.title.toLowerCase().includes(searchTerm.toLowerCase())
        : true;
      const matchesCategory = selectedCategoryId
        ? service.serviceCategoryId === selectedCategoryId
        : true;
      return matchesSearch && matchesCategory;
    });
  }, [services, searchTerm, selectedCategoryId]);


  // --- Columns --- (No changes needed here)
  const columns = [
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
    },
    {
      title: 'Category',
      dataIndex: 'serviceCategoryId', // Use serviceCategoryId
      key: 'category',
      // Find the category title from the categories list
      render: (serviceCategoryId: number | null) => {
        if (!serviceCategoryId) return '-';
        const category = categories?.find((cat: {id: number, title:string}) => cat.id === serviceCategoryId);
        return category?.title || '-';
      }
    },
    {
      title: 'Duration',
      dataIndex: 'duration',
      key: 'duration',
      render: (duration: number) => `${duration} min`
    },
    {
      title: 'Color',
      dataIndex: 'color',
      key: 'color',
      render: (color: string) => color ? (
        <div className="h-4 w-4 rounded-full" style={{ backgroundColor: color }} />
      ) : '-'
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: any, record: Service) => (
        <Space>
          <Button 
            type="text" 
            icon={<EditOutlined />} 
            onClick={() => openEditModal(record)} 
          />
          <Button 
            type="text" 
            danger 
            icon={<DeleteOutlined />} 
            onClick={() => {
              setServiceToDelete(record);
              setIsDeleteModalOpen(true);
            }} 
          />
        </Space>
      )
    }
  ];

  if (isLoadingServices || isLoadingCategories) {
    return (
      <DefaultLayout user={user}>
        <div className="p-4 flex justify-center">
          <Spin size="large" />
        </div>
      </DefaultLayout>
    );
  }

  if (servicesError || categoriesError) {
    return (
      <DefaultLayout user={user}>
        <Alert
          message="Error"
          description={servicesError?.message || categoriesError?.message}
          type="error"
          showIcon
        />
      </DefaultLayout>
    );
  }

  return (
    <DefaultLayout user={user}>
      <div className='w-full'>
        <Breadcrumb pageName='Services Management' />

        <div className='mb-6 flex flex-col sm:flex-row justify-between items-center gap-4'>
          <div className="flex flex-col sm:flex-row items-center gap-4 w-full sm:w-auto">
            <Input
              placeholder="Search services by title..."
              className="w-full sm:w-[250px]" 
              allowClear
              onChange={(e:{target: {value: string}}) => {
                setSearchTerm(e.target.value || '');
              }}
            />
            <Select
              placeholder="Filter by category"
              className="w-full sm:w-[200px]" 
              allowClear
              value={selectedCategoryId}
              onChange={(value: number | undefined) => {
                setSelectedCategoryId(value);
              }}
              options={categories?.map((cat: {id:number, title:string}) => ({
                value: cat.id,
                label: cat.title,
              }))}
            />
          </div>
          <Button 
            type="primary"
            icon={<PlusOutlined />}
            onClick={openAddModal}
            className="w-full sm:w-auto" 
          >
            Add New Service
          </Button>
        </div>

        <Table 
          columns={columns}
          dataSource={filteredServices}
          rowKey="id"
          bordered
          scroll={{ x: 'max-content' }} 
        />

        <Modal
          title={isEditing ? 'Edit Service' : 'Add New Service'}
          open={isModalOpen}
          onCancel={closeModal}
          onOk={form.submit}
          okText={isSubmitting ? 'Saving...' : (isEditing ? 'Update Service' : 'Add Service')}
          okButtonProps={{ loading: isSubmitting }}
          cancelText="Cancel"
          width={720}
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleFormSubmit}
            initialValues={initialFormData}
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Form.Item
                name="title"
                label="Service Title"
                rules={[{ required: true, message: 'Please enter service title' }]}
              >
                <Input />
              </Form.Item>

              <Form.Item
                name="serviceCategoryId"
                label="Category"
              >
                <Select
                  allowClear
                  placeholder="Select category"
                  options={categories?.map((cat: {id:number, title:string}) => ({
                    value: cat.id,
                    label: cat.title,
                  }))}
                />
              </Form.Item>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
              <Form.Item
                name="duration"
                label="Duration (minutes)"
                rules={[{ required: true, message: 'Please enter duration' }]}
              >
                <InputNumber min={1} style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item
                name="minDuration"
                label="Min Duration (Optional)"
              >
                <InputNumber min={1} style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item
                name="maxDuration"
                label="Max Duration (Optional)"
              >
                <InputNumber min={1} style={{ width: '100%' }} />
              </Form.Item>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
              <Form.Item
                name="queue"
                label="How Many Queues (Optional)"
              >
                <InputNumber min={0} style={{ width: '100%' }} />
              </Form.Item>
              <Form.Item
                name="pointsRequirements"
                label="Points Required"
                rules={[{ required: true, message: 'Please enter points requirement' }]}
              >
              <InputNumber min={0} style={{ width: '100%' }} />
             </Form.Item>
             <Form.Item
                name="color"
                label="Color"
              >
                <ColorPicker
                  defaultValue={initialFormData.color}
                  showText={(color: any) => <span>Service Color ({color.toHexString()})</span>}
                />
              </Form.Item>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mt-4">
              <Form.Item name="acceptOnline" valuePropName="checked">
                <Checkbox>Accept Online Bookings</Checkbox>
              </Form.Item>

              <Form.Item name="acceptNew" valuePropName="checked">
                <Checkbox>Accept New Customers</Checkbox>
              </Form.Item>

              <Form.Item name="notificationOn" valuePropName="checked">
                <Checkbox>Send Notifications</Checkbox>
              </Form.Item>
            </div>
          </Form>
        </Modal>

        <Modal
          title="Confirm Deletion"
          open={isDeleteModalOpen}
          onCancel={() => setIsDeleteModalOpen(false)}
          footer={[
            <Button key="cancel" onClick={() => setIsDeleteModalOpen(false)}>
              Cancel
            </Button>,
            <Button
              key="delete"
              type="primary"
              danger
              loading={isSubmitting}
              onClick={handleDelete}
            >
              Delete
            </Button>
          ]}
        >
          <p>Are you sure you want to delete the service "{serviceToDelete?.title}"? This action cannot be undone.</p>
        </Modal>
      </div>
    </DefaultLayout>
  );
};

export default ServicesPage;