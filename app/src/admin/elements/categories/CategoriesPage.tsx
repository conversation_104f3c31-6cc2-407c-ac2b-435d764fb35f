import React, { useState, useEffect, useMemo } from 'react'; // Added useMemo
import { type AuthUser } from 'wasp/auth';
import { useRedirectHomeUnlessUserIsAdmin } from '../../useRedirectHomeUnlessUserIsAdmin';
import DefaultLayout from '../../layout/DefaultLayout';
import Breadcrumb from '../../layout/Breadcrumb';

// Import necessary Wasp queries/actions
import {
  useAction,
  createServiceCategory,
  useQuery,
  getServiceCategories,
  deleteServiceCategory
} from 'wasp/client/operations';
import { type ServiceCategory } from 'wasp/entities';

// Import Ant Design components
import Button from 'antd/es/button';
import Input from 'antd/es/input';
import Form from 'antd/es/form';
// Removed List import
import Spin from 'antd/es/spin';
import Alert from 'antd/es/alert';
import message from 'antd/es/message';
import Modal from 'antd/es/modal';
// Removed Space import as it's not used directly here anymore
import Table from 'antd/es/table'; // Added Table import
import Typography from 'antd/es/typography';

// Removed the custom card component import
// import CustomCard from '../../components/Card';

import { DeleteOutlined, PlusOutlined, SearchOutlined } from '@ant-design/icons'; // Added SearchOutlined

const { Title } = Typography;
const { confirm } = Modal; // Use Modal.confirm for deletion

const AdminCategoriesPage = ({ user }: { user: AuthUser }) => {
  useRedirectHomeUnlessUserIsAdmin({ user });
  const [addForm] = Form.useForm(); // Form instance for the add modal

  // --- State ---
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [filterText, setFilterText] = useState('');
  // Removed state for delete modal, using Modal.confirm instead
  // const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  // const [categoryToDelete, setCategoryToDelete] = useState<ServiceCategory | null>(null);

  // --- Actions ---
  const createServiceCategoryAction = useAction(createServiceCategory);
  const deleteServiceCategoryAction = useAction(deleteServiceCategory);

  // --- Queries ---
  const {
    data: serviceCategories,
    isLoading: isLoadingCategories,
    error: categoriesError,
    refetch: refetchServiceCategories
  } = useQuery(getServiceCategories);

  // --- Filtered Data ---
  const filteredCategories = useMemo(() => {
    if (!serviceCategories) return [];
    if (!filterText) return serviceCategories;
    return serviceCategories.filter(category =>
      category.title.toLowerCase().includes(filterText.toLowerCase())
    );
  }, [serviceCategories, filterText]);

  // --- Handlers ---
  const openAddModal = () => setIsAddModalOpen(true);
  const closeAddModal = () => {
    setIsAddModalOpen(false);
    addForm.resetFields(); // Reset form when closing modal
  };

  const handleCreateCategorySubmit = async (values: { title: string }) => {
    if (!values.title?.trim()) {
      message.error('Category title cannot be empty.');
      return;
    }
    setIsSubmitting(true);
    try {
      await createServiceCategoryAction({ title: values.title.trim() });
      message.success(`Category "${values.title.trim()}" created successfully!`);
      closeAddModal(); // Close modal on success
      refetchServiceCategories();
    } catch (error: any) {
      message.error(`Failed to create category: ${error.message || 'Unknown error'}`);
      console.error(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const showDeleteConfirm = (category: ServiceCategory) => {
    confirm({
      title: 'Confirm Deletion',
      content: `Are you sure you want to delete the category "${category.title}"? This action cannot be undone.`,
      okText: 'Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: async () => {
        setIsSubmitting(true); // Indicate loading state on the button/action
        try {
          await deleteServiceCategoryAction({ categoryId: category.id });
          message.success(`Category "${category.title}" deleted successfully!`);
          refetchServiceCategories();
        } catch (error: any) {
          message.error(`Failed to delete category: ${error.message || 'Unknown error'}`);
          console.error(error);
        } finally {
          setIsSubmitting(false);
        }
      },
      onCancel() {
        // Optional: console.log('Cancel delete');
      },
      // Make modal buttons use loading state
      confirmLoading: isSubmitting,
    });
  };

  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilterText(e.target.value);
  };

  // --- Table Columns ---
  const columns = [
    {
      title: 'Category Title',
      dataIndex: 'title',
      key: 'title',
      sorter: (a: ServiceCategory, b: ServiceCategory) => a.title.localeCompare(b.title),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: any, record: ServiceCategory) => (
        <Button
          type="text"
          danger
          icon={<DeleteOutlined />}
          onClick={() => showDeleteConfirm(record)}
          // Removed loading state from individual delete button as confirm modal handles it
          // loading={isSubmitting}
        >
          Delete
        </Button>
      ),
    },
  ];

  // --- Render Logic ---
  if (isLoadingCategories && !serviceCategories) { // Show loading only on initial load
    return (
      <DefaultLayout user={user}>
        <Breadcrumb pageName='Service Categories' />
        <div className="p-4 flex justify-center items-center h-64"> {/* Added height for better centering */}
          <Spin size="large" />
        </div>
      </DefaultLayout>
    );
  }

  if (categoriesError) {
    return (
      <DefaultLayout user={user}>
         <Breadcrumb pageName='Service Categories' />
         <Alert
           message="Error Loading Categories"
           description={categoriesError.message || 'Unknown error'}
           type="error"
           showIcon
           className="m-4" // Keep margin for spacing
         />
      </DefaultLayout>
    );
  }

  return (
    <DefaultLayout user={user}>
      <div className='w-full'>
        <Breadcrumb pageName='Service Categories' />

        {/* Removed CustomCard wrapper */}
        {/* <div className="bg-white dark:bg-boxdark shadow-default rounded-sm border border-stroke dark:border-strokedark p-4 md:p-6 xl:p-7.5"> Added styling similar to CustomCard */}
          {/* <Title level={4} className="mb-4">Manage Service Categories</Title> Added Title */}
          <div className="mb-4 flex flex-col sm:flex-row justify-between items-center gap-4"> {/* Adjusted layout for responsiveness */}
            <Input
              placeholder="Filter by category name"
              prefix={<SearchOutlined />}
              value={filterText}
              onChange={handleFilterChange}
              style={{ width: '100%', maxWidth: 300 }} // Adjusted width
              allowClear
            />
            <Button
              type='primary'
              icon={<PlusOutlined />}
              onClick={openAddModal}
              className="w-full sm:w-auto" // Full width on small screens
            >
              Add New Category
            </Button>
          </div>

          <Table
            columns={columns}
            dataSource={filteredCategories}
            rowKey="id"
            loading={isLoadingCategories} // Show loading indicator on table during refetch
            pagination={{ pageSize: 10, showSizeChanger: true, pageSizeOptions: ['10', '20', '50'] }} // Enhanced pagination
            locale={{ emptyText: 'No service categories found.' }}
            scroll={{ x: 'max-content' }} // Ensure table scrolls horizontally if needed
          />
        

        {/* Add Category Modal */}
        <Modal
          title="Add New Service Category"
          open={isAddModalOpen}
          onCancel={closeAddModal}
          footer={[
            <Button key="back" onClick={closeAddModal} disabled={isSubmitting}>
              Cancel
            </Button>,
            <Button
              key="submit"
              type="primary"
              loading={isSubmitting}
              onClick={() => addForm.submit()} // Trigger form submission
            >
              Add Category
            </Button>,
          ]}
        >
          <Form
            form={addForm}
            layout="vertical"
            onFinish={handleCreateCategorySubmit}
            requiredMark={false}
          >
            <Form.Item
              name="title"
              label="Category Title"
              rules={[{ required: true, message: 'Please enter category title', whitespace: true }]}
            >
              <Input placeholder='e.g., Consultation, Check-up' disabled={isSubmitting} />
            </Form.Item>
          </Form>
        </Modal>

        {/* Delete Confirmation is now handled by Modal.confirm, no separate modal needed */}
      </div>
    </DefaultLayout>
  );
};

export default AdminCategoriesPage;