import { ApexOptions } from 'apexcharts';
import React from 'react';
import ReactApex<PERSON>hart from 'react-apexcharts';

// Define the expected data structure for the props
interface ChartDataItem {
  type: string;
  value: number;
}

interface AppointmentStatusPieChartProps {
  data: ChartDataItem[];
}

// Define suitable colors for appointment statuses
const statusColors: { [key: string]: string } = {
  Completed: '#10B981', // Green
  'Canceled by Customer': '#FCA5A5', // Lighter Red for customer cancel
  'Canceled by Provider': '#F87171', // Original Red for provider cancel
  'Canceled (Other)': '#FECACA', // Even lighter red or different color for unknown cancels
  Confirmed: '#3B82F6', // Blue
  Pending: '#FBBF24', // Amber
  'No Show': '#EF4444', // Darker Red / Error
  Unknown: '#9CA3AF', // Gray
};

const AppointmentStatusPieChart: React.FC<AppointmentStatusPieChartProps> = ({ data }) => {
  // Extract labels and series data from the props
  const labels = data.map(item => item.type);
  const series = data.map(item => item.value);
  const colors = data.map(item => statusColors[item.type] || statusColors.Unknown);

  const totalValue = series.reduce((sum, value) => sum + value, 0);

  const options: ApexOptions = {
    chart: {
      type: 'donut',
    },
    colors: colors,
    labels: labels,
    legend: {
      show: false, // Hide default legend, we'll create a custom one
      position: 'bottom',
    },
    plotOptions: {
      pie: {
        donut: {
          size: '65%',
          background: 'transparent',
        },
      },
    },
    dataLabels: {
      enabled: false,
    },
    responsive: [
      {
        breakpoint: 2600,
        options: {
          chart: {
            width: 380, // Adjust width as needed
          },
        },
      },
      {
        breakpoint: 640,
        options: {
          chart: {
            width: 250, // Adjust width for smaller screens
          },
        },
      },
    ],
  };


  return (
    // Removed outer container with specific styling, let the parent handle it
    <>
      <div className="mb-2">
        <div id="appointmentStatusChart" className="mx-auto flex justify-center">
          <ReactApexChart
            options={options}
            series={series}
            type="donut"
            width={options?.responsive?.[0]?.options?.chart?.width ?? 380} // Ensure width is applied
          />
        </div>
      </div>

      {/* Custom Legend/Summary */}
      <div className="flex flex-col items-center justify-center gap-y-1">
        {data.map((item, index) => (
          <div key={item.type} className="w-full max-w-[200px] px-2">
            <div className="flex items-center">
              <span
                className="mr-2 block h-3 w-3 flex-shrink-0 rounded-full"
                style={{ backgroundColor: colors[index] }}
              ></span>
              <p className="flex flex-1 justify-between text-sm font-medium text-black dark:text-white">
                <span>{item.type}</span>
                <span>
                  {totalValue > 0 ? `${((item.value / totalValue) * 100).toFixed(0)}%` : '0%'} ({item.value})
                </span>
              </p>
            </div>
          </div>
        ))}
        {data.length === 0 && (
           <p className="text-sm text-gray-500 dark:text-gray-400">No data to display.</p>
        )}
      </div>
    </>
  );
};

export default AppointmentStatusPieChart; 