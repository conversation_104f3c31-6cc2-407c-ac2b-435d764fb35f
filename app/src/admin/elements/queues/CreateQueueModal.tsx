import React from 'react';
import { Modal, Form, Input, Button, Alert, Select, Spin } from 'antd';
import { useAction, useQuery } from 'wasp/client/operations';
import { createQueue, getServices } from 'wasp/client/operations';
import { type Queue, type Service } from 'wasp/entities';
import toast from 'react-hot-toast';

interface CreateQueueModalProps {
  isOpen: boolean;
  onClose: () => void;
  sProvidingPlaceId: number;
  onSuccess: () => void; // Callback to refetch queues
}

interface CreateQueueFormValues {
  title: string;
  serviceIds: number[];
}

const CreateQueueModal: React.FC<CreateQueueModalProps> = ({
  isOpen,
  onClose,
  sProvidingPlaceId,
  onSuccess,
}) => {
  const [form] = Form.useForm<CreateQueueFormValues>();
  const createQueueAction = useAction(createQueue);
  const [error, setError] = React.useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  const { data: services, isLoading: isLoadingServices, error: servicesError } = useQuery(getServices);

  const serviceOptions = React.useMemo(() => {
    if (!services) return [];
    return services.map((service: Service) => ({
      label: service.title,
      value: service.id,
    }));
  }, [services]);

  const handleFormSubmit = async (values: CreateQueueFormValues) => {
    setError(null);
    setIsSubmitting(true);
    try {
      await createQueueAction({
        sProvidingPlaceId: sProvidingPlaceId,
        title: values.title,
        serviceIds: values.serviceIds,
      });
      toast.success('Queue created successfully!');
      onSuccess();
      form.resetFields();
      onClose();
    } catch (err: any) {
      console.error('Failed to create queue:', err);
      setError(err.message || 'An unexpected error occurred.');
      toast.error(`Failed to create queue: ${err.message || 'Error'}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  React.useEffect(() => {
    if (!isOpen) {
      form.resetFields();
      setError(null);
    }
  }, [isOpen, form]);

  return (
    <Modal
      title="Create New Queue"
      visible={isOpen}
      onCancel={onClose}
      footer={[
        <Button key="back" onClick={onClose} disabled={isSubmitting}>
          Cancel
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={isSubmitting}
          onClick={() => form.submit()}
        >
          Create Queue
        </Button>,
      ]}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleFormSubmit}
        initialValues={{ title: '', serviceIds: [] }}
      >
        {error && (
            <Alert message="Error" description={error} type="error" showIcon style={{ marginBottom: 16 }} />
        )}
        <Form.Item
          name="title"
          label="Queue Title"
          rules={[
            { required: true, message: 'Please enter a title for the queue' },
            { max: 100, message: 'Title cannot exceed 100 characters' }
           ]}
        >
          <Input placeholder="e.g., Consultation Room 1" />
        </Form.Item>
        <Form.Item
          name="serviceIds"
          label="Services Provided by this Queue"
          rules={[
            { required: true, type: 'array', min: 1, message: 'Please select at least one service for this queue' },
           ]}
        >
          <Select
            mode="multiple"
            allowClear
            style={{ width: '100%' }}
            placeholder="Select services"
            options={serviceOptions}
            loading={isLoadingServices}
            disabled={isLoadingServices || !!servicesError}
          />
        </Form.Item>
        {isLoadingServices && <Spin tip="Loading services..." />}
        {servicesError && (
            <Alert message="Error loading services" description={servicesError.message || 'Could not fetch services.'} type="error" showIcon />
        )}
      </Form>
    </Modal>
  );
};

export default CreateQueueModal; 