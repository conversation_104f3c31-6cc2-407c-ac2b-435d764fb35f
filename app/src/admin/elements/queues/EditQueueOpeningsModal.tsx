import React, { useState, useEffect, useCallback } from 'react';
import {
  Modal,
  Button,
  Form,
  TimePicker,
  Checkbox,
  Spin,
  Alert,
  Row,
  Col,
  Divider,
  Typography,
} from 'antd';
import { useQuery, useAction } from 'wasp/client/operations';
import { getQueueOpenings, updateQueueOpenings } from 'wasp/client/operations';
import { type QueueOpening, type QueueOpeningHours } from 'wasp/entities';
import toast from 'react-hot-toast';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import type { CheckboxChangeEvent } from 'antd/es/checkbox';
dayjs.extend(customParseFormat);

const { Text } = Typography;

// Interface for the schedule structure used in the form
interface DaySchedule {
  dayOfWeek: string;
  isActive: boolean;
  hours: { timeFrom: dayjs.Dayjs | null; timeTo: dayjs.Dayjs | null }[];
}

// Extend QueueOpening type locally if needed, or rely on backend include
type QueueOpeningWithHours = QueueOpening & { hours: QueueOpeningHours[] };

interface EditQueueOpeningsModalProps {
  isOpen: boolean;
  onClose: () => void;
  queueId: number;
  onSuccess: () => void; // Callback after successful update
}

const DAYS_ORDER = [
  'Monday',
  'Tuesday',
  'Wednesday',
  'Thursday',
  'Friday',
  'Saturday',
  'Sunday',
];

const EditQueueOpeningsModal: React.FC<EditQueueOpeningsModalProps> = ({
  isOpen,
  onClose,
  queueId,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const { data: openingsData, isLoading, error } = useQuery(
    getQueueOpenings,
    { queueId },
    { enabled: isOpen } // Fetch only when modal is open
  );
  const updateOpeningsAction = useAction(updateQueueOpenings);
  const [formSchedule, setFormSchedule] = useState<DaySchedule[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  // Helper to initialize/reset form schedule
  const initializeSchedule = useCallback((data: QueueOpeningWithHours[] | undefined | null) => {
    const initialSchedule = DAYS_ORDER.map(day => {
        // Now `opening.hours` should be available thanks to backend include
        const opening = data?.find(op => op.dayOfWeek === day);
        return {
            dayOfWeek: day,
            isActive: opening?.isActive ?? false,
            hours: opening?.hours?.map((h: QueueOpeningHours) => ({ // Added type for h
                timeFrom: h.timeFrom ? dayjs(h.timeFrom, 'HH:mm') : null,
                timeTo: h.timeTo ? dayjs(h.timeTo, 'HH:mm') : null,
            })) ?? [{ timeFrom: null, timeTo: null }] // Default one empty slot if no hours
        };
    });
    setFormSchedule(initialSchedule);
    form.setFieldsValue({ schedule: initialSchedule }); // Sync antd form state
  }, [form]);

  // Initialize form state when data loads or modal opens
  useEffect(() => {
    if (isOpen && openingsData) {
      // Cast data if necessary, assuming backend now includes hours
      initializeSchedule(openingsData as QueueOpeningWithHours[]);
    } else if (!isOpen) {
      // Reset state when modal closes if desired
      initializeSchedule(undefined);
    }
  }, [isOpen, openingsData, initializeSchedule]);

  // Handle adding a new time slot for a day
  const addHourSlot = (dayIndex: number) => {
    const updatedSchedule = [...formSchedule];
    updatedSchedule[dayIndex].hours.push({ timeFrom: null, timeTo: null });
    setFormSchedule(updatedSchedule);
    form.setFieldsValue({ schedule: updatedSchedule });
  };

  // Handle removing a time slot for a day
  const removeHourSlot = (dayIndex: number, hourIndex: number) => {
    const updatedSchedule = [...formSchedule];
    updatedSchedule[dayIndex].hours.splice(hourIndex, 1);
    if (updatedSchedule[dayIndex].hours.length === 0) {
      updatedSchedule[dayIndex].hours.push({ timeFrom: null, timeTo: null });
    }
    setFormSchedule(updatedSchedule);
    form.setFieldsValue({ schedule: updatedSchedule });
  };

  // Handle updating time value
  const handleTimeChange = (
    dayIndex: number,
    hourIndex: number,
    field: 'timeFrom' | 'timeTo',
    value: dayjs.Dayjs | null // Explicit type
  ) => {
    const updatedSchedule = [...formSchedule];
    updatedSchedule[dayIndex].hours[hourIndex][field] = value;
    setFormSchedule(updatedSchedule);
  };

  // Handle toggling isActive checkbox
  const handleActiveToggle = (dayIndex: number, e: CheckboxChangeEvent) => { // Use CheckboxChangeEvent
    const updatedSchedule = [...formSchedule];
    updatedSchedule[dayIndex].isActive = e.target.checked;
    setFormSchedule(updatedSchedule);
    form.setFieldsValue({ schedule: updatedSchedule });
  };

  // Handle form submission
  const handleSave = async () => {
    setErrorMessage(null);
    setIsSubmitting(true);

    const formattedSchedule = formSchedule.map(day => ({
        dayOfWeek: day.dayOfWeek,
        isActive: day.isActive,
        hours: day.hours
            .filter(h => h.timeFrom && h.timeTo && h.timeFrom.isValid() && h.timeTo.isValid())
            .map(h => ({
                timeFrom: h.timeFrom!.format('HH:mm'),
                timeTo: h.timeTo!.format('HH:mm'),
            }))
            .filter(h => h.timeFrom < h.timeTo)
    }));

    let hasInvalidRange = false;
    for (const day of formattedSchedule) {
        if (!day.isActive || day.hours.length <= 1) continue;
        const sortedHours = [...day.hours].sort((a, b) => a.timeFrom.localeCompare(b.timeFrom));
        for (let i = 0; i < sortedHours.length - 1; i++) {
            if (sortedHours[i].timeTo > sortedHours[i + 1].timeFrom) {
                hasInvalidRange = true;
                setErrorMessage(`Invalid overlap detected on ${day.dayOfWeek}.`);
                break;
            }
        }
        if (hasInvalidRange) break;
    }

    if (hasInvalidRange) {
        setIsSubmitting(false);
        return;
    }

    try {
        await updateOpeningsAction({ queueId, schedule: formattedSchedule });
        toast.success('Queue opening hours updated successfully!');
        onSuccess();
        onClose();
    } catch (err: any) {
        console.error('Failed to update queue openings:', err);
        setErrorMessage(err.message || 'An unexpected error occurred.');
        toast.error(`Update failed: ${err.message || 'Error'}`);
    } finally {
        setIsSubmitting(false);
    }
  };

  return (
    <Modal
      title={`Edit Opening Hours for Queue ID: ${queueId}`}
      visible={isOpen}
      onCancel={onClose}
      width={800}
      footer={[
        <Button key="back" onClick={onClose} disabled={isSubmitting}>
          Cancel
        </Button>,
        <Button key="submit" type="primary" loading={isSubmitting} onClick={handleSave}>
          Save Changes
        </Button>,
      ]}
      destroyOnClose
    >
      {isLoading && <Spin tip="Loading schedule..." />}
      {error && (
        <Alert message="Error Loading Schedule" description={error.message || 'Failed to load schedule.'} type="error" showIcon />
      )}
      {!isLoading && !error && openingsData && (
        <Form form={form} layout="vertical">
          {errorMessage && (
            <Alert message="Validation Error" description={errorMessage} type="error" showIcon style={{ marginBottom: 16 }} />
          )}
          {formSchedule.map((day, dayIndex) => (
            <div key={day.dayOfWeek}>
              <Row gutter={16} align="middle" style={{ marginBottom: '8px' }}>
                <Col span={4}>
                  <Text strong>{day.dayOfWeek}</Text>
                </Col>
                <Col span={4}>
                  <Checkbox
                    checked={day.isActive}
                    onChange={(e: CheckboxChangeEvent) => handleActiveToggle(dayIndex, e)} // Ensure event type is correct here too
                  >
                    Open
                  </Checkbox>
                </Col>
              </Row>
              {day.isActive && day.hours.map((hour, hourIndex) => (
                   <Row key={hourIndex} gutter={16} align="middle" style={{ marginBottom: '8px', marginLeft: '20px' }}>
                       <Col span={6}>
                           <TimePicker 
                             value={hour.timeFrom}
                             format="HH:mm"
                             minuteStep={15}
                             onChange={(time: dayjs.Dayjs | null) => handleTimeChange(dayIndex, hourIndex, 'timeFrom', time)}
                             placeholder="From"
                             style={{ width: '100%' }}
                           />
                       </Col>
                       <Col span={6}>
                           <TimePicker 
                             value={hour.timeTo}
                             format="HH:mm"
                             minuteStep={15}
                             onChange={(time: dayjs.Dayjs | null) => handleTimeChange(dayIndex, hourIndex, 'timeTo', time)}
                             placeholder="To"
                             style={{ width: '100%' }}
                           />
                       </Col>
                       <Col span={4}>
                           <Button 
                             type="dashed" 
                             onClick={() => removeHourSlot(dayIndex, hourIndex)} 
                             danger
                             disabled={day.hours.length <= 1} 
                           >
                             Remove
                           </Button>
                       </Col>
                   </Row>
              ))}
              {day.isActive && (
                  <Row style={{ marginLeft: '20px', marginBottom: '16px' }}>
                      <Col>
                          <Button type="dashed" onClick={() => addHourSlot(dayIndex)}>
                              + Add Time Slot
                          </Button>
                      </Col>
                  </Row>
              )}
              <Divider />
            </div>
          ))}
        </Form>
      )}
    </Modal>
  );
};

export default EditQueueOpeningsModal; 