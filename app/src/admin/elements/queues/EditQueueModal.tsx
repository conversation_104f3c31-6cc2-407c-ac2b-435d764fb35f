import React, { useEffect, useState, useMemo } from 'react';
import { Modal, Form, Input, Switch, Button, message, Select, Spin, Alert } from 'antd';
import { useAction, useQuery } from 'wasp/client/operations';
import { updateQueue, getServices } from 'wasp/client/operations';
import { type Queue, type Service } from 'wasp/entities';

interface EditQueueModalProps {
  isOpen: boolean;
  onClose: () => void;
  queue: Queue & { services?: Pick<Service, 'id'>[] } | null;
  onSuccess?: () => void;
}

interface EditQueueFormValues {
    title: string;
    isActive: boolean;
    serviceIds: number[];
}

const EditQueueModal: React.FC<EditQueueModalProps> = ({ isOpen, onClose, queue, onSuccess }) => {
  const [form] = Form.useForm<EditQueueFormValues>();
  const performUpdateQueue = useAction(updateQueue);
  const [isLoading, setIsLoading] = useState(false);

  const { data: services, isLoading: isLoadingServices, error: servicesError } = useQuery(getServices);

  const serviceOptions = useMemo(() => {
    if (!services) return [];
    return services.map((service) => ({
      label: service.title,
      value: service.id,
    }));
  }, [services]);

  useEffect(() => {
    if (isOpen && queue) {
      form.setFieldsValue({
        title: queue.title,
        isActive: queue.isActive,
        serviceIds: queue.services?.map(s => s.id) || [],
      });
    } else {
      form.resetFields();
      setIsLoading(false);
    }
  }, [isOpen, queue, form]);

  const handleOk = async () => {
    if (!queue) return;

    try {
      const values = await form.validateFields() as EditQueueFormValues;
      
      const currentServiceIds = queue.services?.map(s => s.id).sort() || [];
      const newServiceIds = values.serviceIds.slice().sort();

      if (values.title === queue.title && 
          values.isActive === queue.isActive && 
          JSON.stringify(currentServiceIds) === JSON.stringify(newServiceIds)) {
        message.info('No changes detected.');
        onClose();
        return;
      }

      setIsLoading(true);
      await performUpdateQueue({
        queueId: queue.id,
        title: values.title,
        isActive: values.isActive,
        serviceIds: values.serviceIds,
      });
      
      message.success('Queue updated successfully!');
      if (onSuccess) {
          onSuccess();
      }
      onClose();

    } catch (error: any) {
        if (error?.errorFields) {
            console.log('Form validation failed:', error.errorFields);
        } else {
            console.error('Failed to update queue action:', error);
            message.error(`Failed to update queue: ${error.message || 'An unexpected error occurred.'}`);
        }
    } finally {
        setIsLoading(false);
    }
  };

  return (
    <Modal
      title="Edit Queue" 
      open={isOpen}
      onOk={handleOk}
      onCancel={onClose}
      confirmLoading={isLoading}
      destroyOnClose
      footer={[
          <Button key="back" onClick={onClose} disabled={isLoading}>Cancel</Button>,
          <Button key="submit" type="primary" loading={isLoading} onClick={handleOk}>
              Save Changes
          </Button>,
      ]}
    >
      <Form<EditQueueFormValues>
        form={form}
        layout="vertical"
        name="edit_queue_form"
      >
        <Form.Item
          name="title"
          label="Queue Title"
          rules={[{ required: true, message: 'Please input the queue title!' }, { whitespace: true, message: 'Title cannot be empty spaces.' }]}
        >
          <Input />
        </Form.Item>

        <Form.Item
          name="serviceIds"
          label="Services Provided by this Queue"
          rules={[
            { required: true, type: 'array', min: 1, message: 'Please select at least one service for this queue' },
          ]}
        >
          <Select
            mode="multiple"
            allowClear
            style={{ width: '100%' }}
            placeholder="Select services"
            options={serviceOptions}
            loading={isLoadingServices}
            disabled={isLoadingServices || !!servicesError}
          />
        </Form.Item>
        {isLoadingServices && <Spin tip="Loading services..." />}
        {servicesError && (
            <Alert message="Error loading services" description={servicesError.message || 'Could not fetch services.'} type="error" showIcon />
        )}

        <Form.Item name="isActive" label="Active Status" valuePropName="checked">
          <Switch checkedChildren="Active" unCheckedChildren="Inactive" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default EditQueueModal; 