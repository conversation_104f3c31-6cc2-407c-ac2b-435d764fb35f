import React, { useState, useEffect, useMemo } from 'react';
import Breadcrumb from '../../layout/Breadcrumb';
import DefaultLayout from '../../layout/DefaultLayout';
import {
  Typography,
  Spin,
  Alert,
  List,
  Tag,
  Space,
  Button,
  Select,
  Popconfirm,
  message,
  Table,
} from 'antd';
import { useAuth } from 'wasp/client/auth';
import { useQuery, useAction } from 'wasp/client/operations';
import {
  getSProvidingPlaces,
  getQueuesByPlace,
} from 'wasp/client/operations';
import { type SProvidingPlace, type Queue } from 'wasp/entities';
import { PlusOutlined, EditOutlined, DeleteOutlined, ClockCircleOutlined } from '@ant-design/icons';
import CreateQueueModal from './CreateQueueModal';
import EditQueueOpeningsModal from './EditQueueOpeningsModal';
import EditQueueModal from './EditQueueModal';
import Card from 'antd/es/card/Card';
import { deleteQueue } from 'wasp/client/operations';

const { Title, Text } = Typography;

const AdminQueuesPage: React.FC = () => {
  const { data: user, isLoading: isLoadingUser } = useAuth();
  const [selectedPlaceId, setSelectedPlaceId] = useState<number | null>(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditOpeningsModalOpen, setIsEditOpeningsModalOpen] = useState(false);
  const [editingQueueOpeningsId, setEditingQueueOpeningsId] = useState<number | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingQueue, setEditingQueue] = useState<Queue | null>(null);

  const {
    data: places,
    isLoading: isLoadingPlaces,
    error: placesError,
  } = useQuery(getSProvidingPlaces);

  const {
    data: queues,
    isLoading: isLoadingQueues,
    error: queuesError,
    refetch: refetchQueues,
  } = useQuery(
    getQueuesByPlace,
    { sProvidingPlaceId: selectedPlaceId! },
    { enabled: !!selectedPlaceId }
  );

  const placeOptions = useMemo(() => {
    if (!places) {
        return [];
    }
    return places.map((place) => ({
      value: place.id,
      label: place.name,
    }));
  }, [places]);

  useEffect(() => {
    if (places && places.length > 0 && !selectedPlaceId) {
      setSelectedPlaceId(places[0].id);
    }
  }, [places, selectedPlaceId]);

  const handleOpenCreateModal = () => {
    if (selectedPlaceId) {
        setIsCreateModalOpen(true);
    } else {
        alert('Please select a providing place first.');
    }
  }

  const handleCloseCreateModal = () => {
    setIsCreateModalOpen(false);
  }

  const handleQueueCreated = () => {
      refetchQueues();
  }

  const handleEditOpenings = (queueId: number) => {
    setEditingQueueOpeningsId(queueId);
    setIsEditOpeningsModalOpen(true);
  }

  const handleCloseEditOpeningsModal = () => {
      setIsEditOpeningsModalOpen(false);
      setEditingQueueOpeningsId(null);
  }

  const handleQueueHoursUpdated = () => {
      // No need to refetch queues here, just close modal
      // Optionally show a success message
      // refetchQueues(); 
  }

  const performDeleteQueue = useAction(deleteQueue);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async (queueId: number) => {
    setIsDeleting(true);
    try {
      await performDeleteQueue({ queueId });
      message.success('Queue deleted successfully');
      refetchQueues();
    } catch (error: any) {
      message.error(`Error deleting queue: ${error.message}`);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleEditQueue = (queue: Queue) => {
      setEditingQueue(queue);
      setIsEditModalOpen(true);
  }

  const handleCloseEditQueueModal = () => {
      setIsEditModalOpen(false);
      setEditingQueue(null);
  }

  const handleQueueEdited = () => {
      refetchQueues();
  }

  if (isLoadingUser || isLoadingPlaces) {
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
        }}
      >
        <Spin size="large" />
      </div>
    );
  }

  if (!user) {
    return (
      <div style={{ padding: '20px' }}>
        <p>Please log in to manage queues.</p>
      </div>
    );
  }

  if (placesError) {
    return (
      <DefaultLayout user={user}>
        <Alert
          message="Error Loading Places"
          description={placesError.message || 'Could not fetch providing places.'}
          type="error"
          showIcon
        />
      </DefaultLayout>
    );
  }

  if (!places || places.length === 0) {
     return (
       <DefaultLayout user={user}>
         <Breadcrumb pageName="Queues Management" />
         <div className="container mx-auto p-4">
             <Title level={2}>Manage Your Queues</Title>
             <Alert message="No Providing Places Found" description="You need to add a providing place before you can manage queues." type="info" showIcon />
         </div>
       </DefaultLayout>
     );
  }

  const columns = [
    { title: 'ID', dataIndex: 'id', key: 'id' },
    { title: 'Title', dataIndex: 'title', key: 'title' },
    {
      title: 'Status',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive: boolean) => (isActive ? 'Active' : 'Inactive'),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: any, record: Queue) => (
        <Space size="middle">
          <Button onClick={() => handleEditQueue(record)}>Edit</Button>
          <Button onClick={() => handleEditOpenings(record.id)}>Hours</Button>
          <Popconfirm
            title="Delete the queue?"
            description="Are you sure you want to delete this queue? This action cannot be undone."
            onConfirm={() => handleDelete(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Button danger loading={isDeleting}>Delete</Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <DefaultLayout user={user}>
      <Breadcrumb pageName="Queues Management" />
      <div className="container mx-auto p-4">
        <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            {/* <Title level={2} style={{ margin: 0 }}>Manage Your Queues</Title> */}
             
        
        
          <div style={{ marginBottom: '16px' }}>
              <Text strong>Select Place: </Text>
              <Select
                  style={{ minWidth: 200 }}
                  value={selectedPlaceId}
                  onChange={(value: number) => setSelectedPlaceId(value)}
                  loading={isLoadingPlaces}
                  options={placeOptions}
              />
          </div>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleOpenCreateModal} disabled={!selectedPlaceId || isLoadingPlaces}>
              Create Queue
           </Button>
        </div>

        <Card
          title={`Queues for ${placeOptions.find((p) => p.value === selectedPlaceId)?.label || 'Selected Place'}`}
        >
          {isLoadingQueues && <Spin tip="Loading queues..." />}
          {queuesError && (
            <Alert
              message="Error Loading Queues"
              description={
                queuesError.message || 'Could not fetch queues for this place.'
              }
              type="error"
              showIcon
            />
          )}
          {!isLoadingQueues && !queuesError && (
            <Table columns={columns} dataSource={queues} rowKey="id" />
          )}
        </Card>

        {selectedPlaceId && (
            <CreateQueueModal
                isOpen={isCreateModalOpen}
                onClose={handleCloseCreateModal}
                sProvidingPlaceId={selectedPlaceId}
                onSuccess={handleQueueCreated}
            />
        )}

        {editingQueueOpeningsId && (
            <EditQueueOpeningsModal
                isOpen={isEditOpeningsModalOpen}
                onClose={handleCloseEditOpeningsModal}
                queueId={editingQueueOpeningsId}
                onSuccess={handleQueueHoursUpdated} 
            />
        )}

        {editingQueue && (
            <EditQueueModal
                isOpen={isEditModalOpen}
                onClose={handleCloseEditQueueModal}
                queue={editingQueue}
                onSuccess={handleQueueEdited}
            />
        )}

      </div>
    </DefaultLayout>
  );
};

export default AdminQueuesPage; 