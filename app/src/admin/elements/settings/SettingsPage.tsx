import { type AuthUser } from 'wasp/auth';
import React, { FormEvent, useEffect, useState } from 'react';
import Breadcrumb from '../../layout/Breadcrumb';
import DefaultLayout from '../../layout/DefaultLayout';
import { useRedirectHomeUnlessUserIsAdmin } from '../../useRedirectHomeUnlessUserIsAdmin';
import { 
    useQuery, 
    useAction, 
    getUserServiceProvider, 
    getProviderCategories, 
    updateSProvider, 
    addSProvidingPlace, 
    getSProvidingPlaces,
    updateSProvidingPlace,
    getSProviderOpenings,
    updateSProviderOpenings
} from 'wasp/client/operations';
import { type SProvider, type ProviderCategory, type SProvidingPlace, type Opening, type OpeningHours } from 'wasp/entities';

// Import Ant Design components
import { Form, Input, Select, Button, Switch, TimePicker, Space, Alert, Spin, message, Divider, Typography, Checkbox, Descriptions, Tag, Row, Col, InputNumber } from 'antd';
import type { TimePickerProps } from 'antd';
import dayjs from 'dayjs';
import { 
    DeleteOutlined, 
    EnvironmentOutlined, 
    PhoneOutlined, 
    PrinterOutlined, 
    CheckCircleOutlined, 
    CloseCircleOutlined,
    EditOutlined,
    AimOutlined
} from '@ant-design/icons';

import Card from "../../components/Card"
import { ProviderLogoUpload } from "../../../provider/components/ProviderLogoUpload"

const { Title, Text } = Typography;

// Algerian Wilayas (58)
const algerianWilayas = [
  "Adrar", "Chlef", "Laghouat", "Oum El Bouaghi", "Batna", "Béjaïa", "Biskra", "Béchar", "Blida", "Bouira", 
  "Tamanrasset", "Tébessa", "Tlemcen", "Tiaret", "Tizi Ouzou", "Alger", "Djelfa", "Jijel", "Sétif", "Saïda", 
  "Skikda", "Sidi Bel Abbès", "Annaba", "Guelma", "Constantine", "Médéa", "Mostaganem", "M'Sila", "Mascara", "Ouargla", 
  "Oran", "El Bayadh", "Illizi", "Bordj Bou Arréridj", "Boumerdès", "El Tarf", "Tindouf", "Tissemsilt", "El Oued", "Khenchela", 
  "Souk Ahras", "Tipaza", "Mila", "Aïn Defla", "Naâma", "Aïn Témouchent", "Ghardaïa", "Relizane", "Timimoun", "Bordj Badji Mokhtar", 
  "Ouled Djellal", "Béni Abbès", "In Salah", "In Guezzam", "Touggourt", "Djanet", "El M'Ghair", "El Meniaa"
];

// Sample French Cities
const frenchCities = [
  "Paris", "Marseille", "Lyon", "Toulouse", "Nice", "Nantes", "Strasbourg", "Montpellier", "Bordeaux", "Lille",
  // Add more French cities as needed
];

const countries = [
  { value: 'Algeria', label: 'Algeria' },
  { value: 'France', label: 'France' },
];

// Define common IANA timezone names
const commonTimezones = [
  'UTC',
  'Africa/Algiers',
  'America/New_York',
  'America/Chicago',
  'America/Denver',
  'America/Los_Angeles',
  'Europe/London',
  'Europe/Paris',
  'Europe/Berlin',
  'Asia/Tokyo',
  'Asia/Dubai',
  'Australia/Sydney',
  // Add more as needed
];

// Function to get browser timezone
const getBrowserTimezone = () => {
  try {
    return Intl.DateTimeFormat().resolvedOptions().timeZone;
  } catch (e) {
    return 'UTC'; // Fallback
  }
};

// Define types for schedule state
interface TimeSlot {
    id?: number;
    timeFrom: string;
    timeTo: string;
}

interface DaySchedule {
    dayOfWeek: string;
    isActive: boolean;
    hours: TimeSlot[];
}

// Helper to initialize schedule state
const initializeSchedule = (openingsData: Opening[] | undefined | null): DaySchedule[] => {
    const days = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"];
    const scheduleMap = new Map<string, DaySchedule>();

    if (openingsData) {
        openingsData.forEach((opening: any) => {
            const hours = opening.hours?.map((h: OpeningHours) => ({ id: h.id, timeFrom: h.timeFrom, timeTo: h.timeTo })) || [];
            scheduleMap.set(opening.dayOfWeek, {
                dayOfWeek: opening.dayOfWeek,
                isActive: opening.isActive,
                hours: hours,
            });
        });
    }

    return days.map(day => {
        if (scheduleMap.has(day)) {
            return scheduleMap.get(day)!;
        } else {
            return { dayOfWeek: day, isActive: true, hours: [{ timeFrom: '09:00', timeTo: '17:00' }] };
        }
    });
};

const SettingsPage = ({ user }: { user: AuthUser }) => {
  useRedirectHomeUnlessUserIsAdmin({ user });
  const [providerForm] = Form.useForm();
  const [placeForm] = Form.useForm();

  // --- Data Fetching ---
  const { data: sProviderData, isLoading: isLoadingProvider, error: providerError } = 
    useQuery(getUserServiceProvider, { userId: user.id }, { enabled: !!user.id });
  const { data: categories, isLoading: isLoadingCategories, error: categoriesError } = 
    useQuery(getProviderCategories);
  const { data: existingPlaces, isLoading: isLoadingPlaces, error: placesError, refetch: refetchPlaces } = 
    useQuery(getSProvidingPlaces);

  const primaryPlace = existingPlaces && existingPlaces.length > 0 ? existingPlaces[0] : null;

  const { data: openingsData, isLoading: isLoadingOpenings, error: openingsError, refetch: refetchOpenings } =
      useQuery(getSProviderOpenings, undefined, { enabled: !!primaryPlace }) as any;

  // --- State ---
  const [isAddingOrEditingPlace, setIsAddingOrEditingPlace] = useState(false);
  const [isEditingPlace, setIsEditingPlace] = useState(false);
  const [placeToEditId, setPlaceToEditId] = useState<number | null>(null);
  const [schedule, setSchedule] = useState<DaySchedule[]>(initializeSchedule(openingsData));
  const [selectedCountry, setSelectedCountry] = useState<string>('Algeria');
  const [citiesForSelectedCountry, setCitiesForSelectedCountry] = useState<string[]>(algerianWilayas);
  // New state for coordinates and geocoding status
  const [latitude, setLatitude] = useState<number | null>(null);
  const [longitude, setLongitude] = useState<number | null>(null);
  const [isGeocoding, setIsGeocoding] = useState<boolean>(false);
  const [geocodingError, setGeocodingError] = useState<string | null>(null);

  // --- Effects ---
  useEffect(() => {
    if (openingsData) {
      setSchedule(initializeSchedule(openingsData));
    }
  }, [openingsData]);

  useEffect(() => {
    if (sProviderData && typeof sProviderData === 'object' && 'id' in sProviderData) {
      providerForm.setFieldsValue({
        title: sProviderData.title || '',
        phone: sProviderData.phone || '',
        presentation: sProviderData.presentation || '',
        providerCategoryId: sProviderData.providerCategoryId?.toString() || '',
      });
    }
  }, [sProviderData, providerForm]);

  useEffect(() => {
    if (isEditingPlace && primaryPlace) {
      const country = (primaryPlace as any).detailedAddress?.country || 'Algeria';
      setSelectedCountry(country);
      if (country === 'Algeria') {
        setCitiesForSelectedCountry(algerianWilayas);
      } else if (country === 'France') {
        setCitiesForSelectedCountry(frenchCities);
      } else {
        setCitiesForSelectedCountry([]);
      }
      const lat = (primaryPlace as any).detailedAddress?.latitude;
      const lng = (primaryPlace as any).detailedAddress?.longitude;
      setLatitude(lat || null);
      setLongitude(lng || null);
      setGeocodingError(null);
      placeForm.setFieldsValue({
        name: primaryPlace.name || '',
        shortName: primaryPlace.shortName || '',
        address: (primaryPlace as any).detailedAddress?.address || primaryPlace.address || '',
        city: (primaryPlace as any).detailedAddress?.city || primaryPlace.city || undefined,
        postalCode: (primaryPlace as any).detailedAddress?.postalCode || '',
        country: country,
        timezone: primaryPlace.timezone || getBrowserTimezone(),
        mobile: primaryPlace.mobile || '',
        fax: primaryPlace.fax || '',
        floor: primaryPlace.floor || '',
        parking: primaryPlace.parking || false,
        elevator: primaryPlace.elevator || false,
        handicapAccess: primaryPlace.handicapAccess || false,
      });
    } else {
      // Defaults for new places
      setSelectedCountry('Algeria');
      setCitiesForSelectedCountry(algerianWilayas);
      setLatitude(null); // Reset coordinates for new form
      setLongitude(null); // Reset coordinates for new form
      setGeocodingError(null);
      placeForm.setFieldsValue({ 
        timezone: getBrowserTimezone(),
        country: 'Algeria', 
        city: undefined, 
        address: '', // Clear address fields for new form
        postalCode: '',
      });
    }
  }, [isEditingPlace, primaryPlace, placeForm]);

  const handleCountryChange = (value: string) => {
    setSelectedCountry(value);
    placeForm.setFieldsValue({ city: undefined }); // Reset city on country change
    if (value === 'Algeria') {
      setCitiesForSelectedCountry(algerianWilayas);
    } else if (value === 'France') {
      setCitiesForSelectedCountry(frenchCities);
    } else {
      setCitiesForSelectedCountry([]);
    }
  };

  // --- Handlers ---
  const handleUpdateProviderSubmit = async (values: any) => {
    try {
      await updateSProvider({
        title: values.title || undefined,
        phone: values.phone || undefined,
        presentation: values.presentation || undefined,
      });
      message.success('Provider information saved successfully!');
    } catch (error: any) {
      message.error(`Failed to save provider info: ${error.message || 'Unknown error'}`);
      console.error(error);
    }
  };

  const handleEditPlaceClick = (place: SProvidingPlace) => {
    setIsEditingPlace(true);
    setPlaceToEditId(place.id);
    const country = (place as any).detailedAddress?.country || 'Algeria';
    const lat = (place as any).detailedAddress?.latitude;
    const lng = (place as any).detailedAddress?.longitude;
    setSelectedCountry(country);
    setLatitude(lat || null);
    setLongitude(lng || null);
    setGeocodingError(null);

    if (country === 'Algeria') {
      setCitiesForSelectedCountry(algerianWilayas);
    } else if (country === 'France') {
      setCitiesForSelectedCountry(frenchCities);
    } else {
      setCitiesForSelectedCountry([]);
    }
    placeForm.setFieldsValue({
      name: place.name || '',
      shortName: place.shortName || '',
      address: (place as any).detailedAddress?.address || place.address || '',
      city: (place as any).detailedAddress?.city || place.city || undefined,
      postalCode: (place as any).detailedAddress?.postalCode || '',
      country: country,
      timezone: place.timezone || getBrowserTimezone(),
      mobile: place.mobile || '',
      fax: place.fax || '',
      floor: place.floor || '',
      parking: place.parking || false,
      elevator: place.elevator || false,
      handicapAccess: place.handicapAccess || false,
    });
  };

  const handleCancelEdit = () => {
    setIsEditingPlace(false);
    setPlaceToEditId(null);
    setLatitude(null); // Reset coordinates
    setLongitude(null);
    setGeocodingError(null);
    placeForm.resetFields();
     // Reset country/city state for new form after cancellation
    setSelectedCountry('Algeria');
    setCitiesForSelectedCountry(algerianWilayas);
    placeForm.setFieldsValue({ country: 'Algeria', city: undefined, timezone: getBrowserTimezone() });
  };

  const handleGetCoordinates = async () => {
    if (!navigator.geolocation) {
      message.error('Geolocation is not supported by your browser.');
      setGeocodingError('Geolocation not supported.');
      return;
    }

    setIsGeocoding(true);
    setGeocodingError(null);
    setLatitude(null);
    setLongitude(null);

    navigator.geolocation.getCurrentPosition(
      (position) => {
        setIsGeocoding(false);
        setLatitude(position.coords.latitude);
        setLongitude(position.coords.longitude);
        message.success('Coordinates fetched successfully!');
      },
      (error) => {
        setIsGeocoding(false);
        let errorMsg = 'Failed to fetch coordinates.';
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMsg = "User denied the request for Geolocation.";
            break;
          case error.POSITION_UNAVAILABLE:
            errorMsg = "Location information is unavailable.";
            break;
          case error.TIMEOUT:
            errorMsg = "The request to get user location timed out.";
            break;
          default:
            errorMsg = "An unknown error occurred while fetching location.";
            break;
        }
        message.error(errorMsg);
        setGeocodingError(errorMsg);
        console.error('Browser Geolocation error:', error);
      }
    );
  };

  const handlePlaceFormSubmit = async (values: any) => {
    if (!values.name.trim()) {
      message.error('Place name is required.');
      return;
    }
    setIsAddingOrEditingPlace(true);
    
    // --- Coordinate Validation ---
    if (latitude === null || longitude === null) {
      message.error('Coordinates are missing. Please fetch coordinates before saving.');
      setIsAddingOrEditingPlace(false);
      setGeocodingError('Coordinates must be fetched using the "Get My Current Location" button.');
      return;
    }
    // --- End Coordinate Validation ---
    
    const placeData = {
      name: values.name,
      shortName: values.shortName || undefined,
      address: values.address || undefined,
      city: values.city || undefined,
      postalCode: values.postalCode || undefined,
      country: values.country || undefined,
      latitude: latitude,
      longitude: longitude,
      mobile: values.mobile || undefined,
      fax: values.fax || undefined,
      floor: values.floor || undefined,
      parking: values.parking,
      elevator: values.elevator,
      handicapAccess: values.handicapAccess,
      timezone: values.timezone,
    };

    let hasInvalidTime = false;
    let errorMessage = "";
    const scheduleDataForAction = schedule.map(day => {
      const validHours = day.hours.filter(slot => {
        if (!slot.timeFrom || !slot.timeTo || !/^\d{2}:\d{2}$/.test(slot.timeFrom) || !/^\d{2}:\d{2}$/.test(slot.timeTo)) {
          hasInvalidTime = true;
          errorMessage = "Please ensure all time slots are in HH:mm format.";
          return false;
        } else if (slot.timeFrom >= slot.timeTo) {
          hasInvalidTime = true;
          errorMessage = "Invalid time range: 'From' time must be before 'To' time.";
          return false;
        }
        return true;
      }).map(h => ({ timeFrom: h.timeFrom, timeTo: h.timeTo }));

      return {
        dayOfWeek: day.dayOfWeek,
        isActive: day.isActive,
        hours: validHours,
      };
    });

    if (hasInvalidTime) {
      message.error(errorMessage);
      setIsAddingOrEditingPlace(false);
      return;
    }

    try {
      let successMessage = '';
      if (isEditingPlace && placeToEditId) {
        await updateSProvidingPlace({ placeId: placeToEditId, ...placeData });
        await updateSProviderOpenings({ placeId: placeToEditId, schedule: scheduleDataForAction });
        successMessage = `Place "${values.name}" and opening hours updated successfully!`;
        setIsEditingPlace(false);
        setPlaceToEditId(null);
        refetchOpenings();
    } else {
        await addSProvidingPlace(placeData);
        successMessage = `Place "${values.name}" added successfully! You can now edit it to add opening hours.`;
        handleCancelEdit();
      }
      message.success(successMessage);
      refetchPlaces();
    } catch (error: any) {
      let op = isEditingPlace ? 'update' : 'add';
      let target = isEditingPlace ? 'place and hours' : 'place';
      if (error.message && error.message.includes('opening hours')) {
        target = 'opening hours';
      }
      message.error(`Failed to ${op} ${target}: ${error.message || 'Unknown error'}`);
      console.error(error);
    } finally {
      setIsAddingOrEditingPlace(false);
    }
  };

  // --- Opening Hours Handlers ---
  const handleTimeChange = (dayIndex: number, slotIndex: number, field: 'timeFrom' | 'timeTo', value: string) => {
    setSchedule(currentSchedule => {
      const newSchedule = [...currentSchedule];
      const day = { ...newSchedule[dayIndex] };
      const slot = { ...day.hours[slotIndex] };
      slot[field] = value;
      day.hours = [...day.hours];
      day.hours[slotIndex] = slot;
      newSchedule[dayIndex] = day;
      return newSchedule;
    });
  };

  const handleAddSlot = (dayIndex: number) => {
    setSchedule(currentSchedule => {
      const newSchedule = [...currentSchedule];
      const day = { ...newSchedule[dayIndex] };
      const lastSlot = day.hours[day.hours.length - 1];
      const newTimeFrom = lastSlot ? incrementTime(lastSlot.timeTo) : '09:00';
      const newTimeTo = incrementTime(newTimeFrom);
      day.hours = [...day.hours, { timeFrom: newTimeFrom, timeTo: newTimeTo }];
      newSchedule[dayIndex] = day;
      return newSchedule;
    });
  };

  const handleRemoveSlot = (dayIndex: number, slotIndex: number) => {
    setSchedule(currentSchedule => {
      const newSchedule = [...currentSchedule];
      const day = { ...newSchedule[dayIndex] };
      day.hours = day.hours.filter((_, index) => index !== slotIndex);
      newSchedule[dayIndex] = day;
      return newSchedule;
    });
  };

  const handleToggleDayActive = (dayIndex: number) => {
    setSchedule(currentSchedule => {
      const newSchedule = [...currentSchedule];
      const day = { ...newSchedule[dayIndex] };
      day.isActive = !day.isActive;
      if (day.isActive && day.hours.length === 0) {
        day.hours = [{ timeFrom: '09:00', timeTo: '17:00' }];
      }
      newSchedule[dayIndex] = day;
      return newSchedule;
    });
  };

  const incrementTime = (time: string): string => {
    try {
      const [hours, minutes] = time.split(':').map(Number);
      const date = new Date();
      date.setHours(hours, minutes, 0);
      date.setHours(date.getHours() + 1);
      return date.toTimeString().substring(0, 5);
    } catch (e) {
      return '10:00';
    }
  };

  // --- Render Logic ---
  const isLoading = isLoadingProvider || isLoadingCategories || isLoadingPlaces || (!!primaryPlace && isLoadingOpenings);

  if (isLoading) {
    return (
      <DefaultLayout user={user}>
        <div className="p-4 flex justify-center">
          <Spin size="large" />
        </div>
      </DefaultLayout>
    );
  }

  if (providerError || categoriesError || placesError || openingsError) {
    return (
      <DefaultLayout user={user}>
        <Alert
          message="Error"
          description={providerError?.message || categoriesError?.message || placesError?.message || openingsError?.message}
          type="error"
          showIcon
        />
      </DefaultLayout>
    );
  }

  const hasValidProviderData = sProviderData && typeof sProviderData === 'object' && 'id' in sProviderData;
  const hasExistingPlace = !!primaryPlace;

  return (
    <DefaultLayout user={user}>
      <div className='w-full'>
        <Breadcrumb pageName='Provider Settings' />

        <div className='grid grid-cols-1 gap-9'>
          {/* Provider Information Card */}
          {/* <Card title="Service Provider Information"> */}
              {hasValidProviderData ? (
              <Form
                form={providerForm}
                layout="vertical"
                onFinish={handleUpdateProviderSubmit}
              >
                <Form.Item
                  name="title"
                  label="Provider"
                  rules={[{ required: true, message: 'Please enter provider title' }]}
                >
                  <Input placeholder="e.g., My Clinic, Legal Services PLLC" />
                </Form.Item>

                <Form.Item
                  name="phone"
                  label="Contact Phone"
                >
                  <Input placeholder="Enter contact phone number" />
                </Form.Item>

                <Form.Item
                  name="providerCategoryId"
                  label="Provider Category (Cannot be changed)"
                >
                  <Select
                    disabled
                    placeholder="Select category"
                    options={categories?.map((cat: ProviderCategory) => ({
                      value: cat.id.toString(),
                      label: cat.title,
                    }))}
                  />
                </Form.Item>

                <Form.Item
                  name="presentation"
                  label="Presentation / Bio"
                >
                  <Input.TextArea
                    rows={6}
                    placeholder="Describe your services, experience, etc."
                  />
                </Form.Item>

                {/* Provider Logo Upload */}
                <Form.Item
                  label="Provider Logo"
                >
                  <div className="flex flex-col items-center">
                    <ProviderLogoUpload 
                      provider={sProviderData as any}
                      onUploadSuccess={(fileId) => {
                        console.log('Provider logo uploaded:', fileId);
                        message.success('Logo uploaded successfully!');
                      }}
                      onRemoveSuccess={() => {
                        console.log('Provider logo removed');
                        message.success('Logo removed successfully!');
                      }}
                      size={100}
                    />
                    <Text type="secondary" className="mt-2 text-center">
                      Upload your business logo (JPEG or PNG, max 5MB)
                    </Text>
                  </div>
                </Form.Item>

                <Form.Item className="mt-4 float-right">
                  <Button type="primary" htmlType="submit" >
                    Save Provider Info
                  </Button>
                </Form.Item>
              </Form>
            ) : (
              <Text type="secondary">
                Service provider profile not found or not yet set up.
              </Text>
            )}
          {/* </Card> */}
          <Divider />
          {/* Place Management Card */}
          {hasValidProviderData && !placesError && hasExistingPlace && !isEditingPlace && primaryPlace && (
                <Descriptions 
                  bordered 
                  column={{ xxl: 2, xl: 2, lg: 2, md: 1, sm: 1, xs: 1 }}
                  title={primaryPlace.name}
                  extra={
                    <Button 
                      type="link" 
                      icon={<EditOutlined />} 
                      onClick={() => handleEditPlaceClick(primaryPlace)}
                    >
                      Edit Place Details
                    </Button>
                  }
                  className="mb-6"
                >
                  {primaryPlace.shortName && (
                    <Descriptions.Item label="Short Name">{primaryPlace.shortName}</Descriptions.Item>
                  )}
                  {primaryPlace.address && (
                    <Descriptions.Item label={<><EnvironmentOutlined /> Address</>}>
                      {primaryPlace.address}
                    </Descriptions.Item>
                  )}
                  {primaryPlace.city && (
                    <Descriptions.Item label={<><EnvironmentOutlined /> City (Wilaya)</>}>
                      {primaryPlace.city}
                    </Descriptions.Item>
                  )}
                  {primaryPlace.mobile && (
                    <Descriptions.Item label={<><PhoneOutlined /> Mobile</>}>{primaryPlace.mobile}</Descriptions.Item>
                  )}
                  {primaryPlace.fax && (
                    <Descriptions.Item label={<><PrinterOutlined /> Fax</>}>{primaryPlace.fax}</Descriptions.Item>
                  )}
                   {primaryPlace.floor && (
                    <Descriptions.Item label="Floor/Details">{primaryPlace.floor}</Descriptions.Item>
                  )}
                  <Descriptions.Item label="Amenities" span={2}>
                      <Space wrap>
                          <Tag icon={<CheckCircleOutlined />} color={primaryPlace.parking ? "success" : "default"}>
                              Parking {primaryPlace.parking ? "Available" : "Not Available"}
                          </Tag>
                          <Tag icon={primaryPlace.elevator ? <CheckCircleOutlined /> : <CloseCircleOutlined />} color={primaryPlace.elevator ? "success" : "default"}>
                              Elevator {primaryPlace.elevator ? "Access" : "No Access"}
                          </Tag>
                          <Tag icon={primaryPlace.handicapAccess ? <CheckCircleOutlined /> : <CloseCircleOutlined />} color={primaryPlace.handicapAccess ? "success" : "default"}>
                              Handicap {primaryPlace.handicapAccess ? "Access" : "No Access"}
                          </Tag>
                      </Space>
                   </Descriptions.Item>
                </Descriptions>
              )}

              {(!hasExistingPlace || isEditingPlace) && (
                <>
                <Space direction="horizontal" className="w-full justify-between mb-6 align-center">
                  <Title level={4} className="mb-4">
                    {isEditingPlace ? 'Edit Place Details' : 'Add Your Providing Place'}
                  </Title>
                  <Form.Item className="mb-6 mt-6 ml-auto">
                    <Space>
                      {isEditingPlace && (
                        <Button onClick={handleCancelEdit}>
                          Cancel Edit
                        </Button>
                      )}
                      <Button
                        type="primary"
                        htmlType="submit"
                        loading={isAddingOrEditingPlace}
                      >
                        {isEditingPlace ? 'Update Place & Hours' : 'Add Place'}
                      </Button>
                    </Space>
                  </Form.Item>
                </Space>
                  

                  {/* Top Action Buttons */}
                 
                  
                  <Form
                    form={placeForm}
                    layout='vertical'
                    onFinish={handlePlaceFormSubmit}
                    initialValues={{
                      parking: false,
                      elevator: false,
                      handicapAccess: false,
                      timezone: getBrowserTimezone(),
                      country: 'Algeria',
                    }}
                  >
                    <div style={{ marginBottom: 24 }}>
                      <Card title={<Text strong>Basic Information</Text>}>
                        <Row gutter={16}>
                          <Col xs={24} sm={12}>
                            <Form.Item
                              name='name'
                              label='Place Name'
                              rules={[{ required: true, message: 'Please enter the place name' }]}
                            >
                              <Input placeholder='e.g., Downtown Office, Main Clinic' />
                            </Form.Item>
                          </Col>
                          <Col xs={24} sm={12}>
                            <Form.Item
                              name='shortName'
                              label='Short Name (Optional)'
                            >
                              <Input placeholder='e.g., DT, MC' />
                            </Form.Item>
                          </Col>
                        </Row>
                      </Card>
                    </div>

                    <div style={{ marginBottom: 24 }}>
                      <Card title={<Text strong>Address Details</Text>}>
                        <Row gutter={16}>
                          <Col xs={24} sm={12}>
                            <Form.Item
                              name='country'
                              label='Country'
                              rules={[{ required: true, message: 'Please select a country' }]}
                            >
                              <Select
                                placeholder='Select Country'
                                onChange={handleCountryChange}
                                options={countries}
                              />
                            </Form.Item>
                          </Col>
                          <Col xs={24} sm={12}>
                            <Form.Item
                              name='city'
                              label='City'
                              rules={[{ required: true, message: 'Please select a city' }]}
                            >
                              <Select
                                showSearch
                                placeholder='Select City/Wilaya'
                                options={citiesForSelectedCountry.map(c => ({ value: c, label: c }))}
                                filterOption={(input: string, option?: { label: string; value: string }) =>
                                  (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                }
                              />
                            </Form.Item>
                          </Col>
                        </Row>

                        <Row gutter={16}>
                          <Col xs={24} sm={12}>
                            <Form.Item
                              name='address'
                              label='Street Address'
                              rules={[{ required: true, message: 'Please input the street address' }]}
                            >
                              <Input placeholder='e.g., 123 Main St' />
                            </Form.Item>
                          </Col>
                          <Col xs={24} sm={12}>
                            <Form.Item
                              name='postalCode'
                              label='Postal Code'
                              rules={[{ required: true, message: 'Please input the postal code' }]}
                            >
                              <Input placeholder='e.g., 90210' />
                            </Form.Item>
                          </Col>
                        </Row>

                        <Row gutter={16}>
                          <Col xs={24} sm={12}>
                            <Form.Item
                              name='timezone'
                              label='Timezone'
                              rules={[{ required: true, message: 'Please select a timezone' }]}
                            >
                              <Select
                                showSearch
                                placeholder='Select Timezone'
                                options={commonTimezones.map(tz => ({ value: tz, label: tz }))}
                                filterOption={(input: string, option?: { label: string; value: string }) =>
                                  (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                }
                              />
                            </Form.Item>
                          </Col>
                          <Col xs={24} sm={12}>
                            {/* Empty column for spacing or future use */}
                          </Col>
                        </Row>

                        <Divider orientation='left' dashed>
                          <EnvironmentOutlined /> Location Coordinates
                        </Divider>
                        <Row gutter={16}>
                          <Col xs={24}>
                            <Form.Item>
                              <Button 
                                type="dashed" 
                                onClick={handleGetCoordinates} 
                                loading={isGeocoding} 
                                icon={<AimOutlined />}
                              >
                                Get My Current Location
                              </Button>
                              {latitude && longitude && (
                                <Text style={{ marginLeft: 16 }}>
                                  Lat: {latitude.toFixed(6)}, Lon: {longitude.toFixed(6)}
                                </Text>
                              )}
                              {geocodingError && <Alert message={geocodingError} type="error" showIcon style={{ marginTop: 8}}/>}
                              {!latitude && !longitude && !isEditingPlace && (
                                <Alert 
                                  message="Please provide your location by clicking the button above. This is required to submit the form." 
                                  type="warning" 
                                  showIcon 
                                  style={{ marginTop: 8}}
                                />
                              )}
                            </Form.Item>
                          </Col>
                        </Row>
                      </Card>
                    </div>

                    <div style={{ marginBottom: 24 }}>
                      <Card title={<Text strong>Contact & Access</Text>}>
                        <Row gutter={16}>
                          <Col xs={24} sm={12}>
                            <Form.Item
                              name='mobile'
                              label='Mobile (Optional)'
                            >
                              <Input placeholder='Place-specific contact number' />
                            </Form.Item>
                          </Col>
                          <Col xs={24} sm={12}>
                            <Form.Item
                              name='fax'
                              label='Fax (Optional)'
                            >
                              <Input placeholder='Fax number' />
                            </Form.Item>
                          </Col>
                        </Row>
                      </Card>
                    </div>

                    <div style={{ marginBottom: 24 }}>
                      <Card title={<Text strong>Amenities</Text>}>
                        <Row gutter={16}>
                          <Col xs={24} sm={8}>
                            <Form.Item name='parking' valuePropName='checked'>
                              <Checkbox>Parking Available</Checkbox>
                            </Form.Item>
                          </Col>
                          <Col xs={24} sm={8}>
                            <Form.Item name='elevator' valuePropName='checked'>
                              <Checkbox>Elevator Access</Checkbox>
                            </Form.Item>
                          </Col>
                          <Col xs={24} sm={8}>
                            <Form.Item name='handicapAccess' valuePropName='checked'>
                              <Checkbox>Handicap Access</Checkbox>
                            </Form.Item>
                          </Col>
                        </Row>
                      </Card>
                    </div>

                    <Form.Item className='mt-6'>
                      <Space>
                        <Button type='primary' htmlType='submit' loading={isAddingOrEditingPlace}>
                          {isEditingPlace ? 'Save Changes' : 'Add Place'}
                        </Button>
                        <Button onClick={handleCancelEdit}>Cancel</Button>
                      </Space>
                    </Form.Item>
                  </Form>
                </>
              )}
          {/* </Card> */}
          
        </div>
      </div>
    </DefaultLayout>
  );
};

export default SettingsPage;
