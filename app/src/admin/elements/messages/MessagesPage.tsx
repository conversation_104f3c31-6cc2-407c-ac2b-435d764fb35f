import React, { useState, useEffect, useRef } from 'react';
import { type AuthUser } from 'wasp/auth';
import { useRedirectHomeUnlessUserIsAdmin } from '../../useRedirectHomeUnlessUserIsAdmin';
import DefaultLayout from '../../layout/DefaultLayout';
import { Link } from 'react-router-dom';
import { useQuery } from 'wasp/client/operations';
import { getConversations } from 'wasp/client/operations';
import { getMessages } from 'wasp/client/operations';
import { sendMessage } from 'wasp/client/operations';
import { startConversation } from 'wasp/client/operations';
import { getProviderCustomers } from 'wasp/client/operations';
import { Input, Spin } from 'antd';
import { SearchOutlined } from '@ant-design/icons';

// Import icons
import {
  FaHome,
  FaPhone,
  FaVideo,
  FaRunning,
  FaShoppingCart,
  FaHeart,
  FaPlus,
  <PERSON>aPaperc<PERSON>,
  <PERSON>aPaper<PERSON>lane,
} from 'react-icons/fa';

// Types from our chat system
interface AugmentedChatUser {
  id: string;
  firstName: string | null;
  lastName: string | null;
  email?: string | null;
}

interface AugmentedParticipant {
  id: number;
  userId: string;
  userDomain: string;
  canWrite: boolean;
  canDownload: boolean;
  unreadCount: number;
  user: AugmentedChatUser;
}

interface AugmentedMessageInfo {
  id: number;
  content: string;
  createdAt: string;
  senderId: string;
  sender: AugmentedChatUser;
}

interface AugmentedConversation {
  id: number;
  name: string | null;
  isGroup: boolean;
  ownerDomain: string;
  ownerId: string;
  createdAt: string;
  updatedAt: string;
  lastMessageId: number | null;
  unread: number;
  displayName: string;
  displayImage: string | null;
  otherParticipantIds: string[];
  lastMessageContent?: string | null;
  lastMessageSenderName?: string;
  lastMessageCreatedAt?: string | null;
  participants: AugmentedParticipant[];
  lastMessage: AugmentedMessageInfo | null;
  owner: AugmentedChatUser;
}

interface Message {
  id: number;
  conversationId: number;
  content: string;
  createdAt: Date;
  senderId: string;
  senderDomain: string;
  hasAttachments: boolean;
  updatedAt: Date;
  sender: {
    id: string;
    firstName: string | null;
    lastName: string | null;
  };
}

interface ConversationResult {
  id: number;
  name: string | null;
  isGroup: boolean;
  ownerDomain: string;
  ownerId: string;
  createdAt: Date;
  updatedAt: Date;
  lastMessageId: number | null;
  participants: Array<{
    id: number;
    userId: string;
    userDomain: string;
    canWrite: boolean;
    canDownload: boolean;
    unreadCount: number;
    user: AugmentedChatUser;
  }>;
  owner: AugmentedChatUser;
}

interface ContactListItemProps {
  conversation: AugmentedConversation;
  isActive: boolean;
  onClick: () => void;
}

interface Customer {
  id: string;
  customer: {
    id: string;
    firstName: string | null;
    lastName: string | null;
    email: string | null;
    mobileNumber: string | null;
  };
}

const ContactListItem: React.FC<ContactListItemProps> = ({ conversation, isActive, onClick }) => (
  <div
    onClick={onClick}
    className={`relative flex items-center p-8 cursor-pointer ${
      isActive ? 'bg-blue-50' : 'hover:bg-gray-50'
    }`}
  >
    <div className="relative mr-3">
      <img
        src={conversation.displayImage || `https://ui-avatars.com/api/?name=${encodeURIComponent(conversation.displayName)}`}
        alt={conversation.displayName}
        className="w-10 h-10 rounded-full"
      />
      {/* Online status could be added later with WebSocket */}
    </div>
    <div className="flex-grow min-w-0 relative">
      <div className="flex justify-between items-center mb-0.5">
        <h4 className={`font-medium text-sm truncate ${isActive ? 'text-blue-700' : 'text-black'} m-0`}>
          {conversation.displayName}
        </h4>
        <span className={`text-xs ${isActive ? 'text-blue-600' : 'text-gray-400'}`}>
          {new Date(conversation.updatedAt).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
        </span>
      </div>
      <p className={`m-0 text-xs truncate ${isActive ? 'text-blue-500' : 'text-gray-500'}`}>
        {conversation.lastMessageContent || 'No messages yet'}
      </p>
    </div>
    {conversation.unread > 0 && (
      <span className="ml-3 bg-blue-500 text-white text-xs font-semibold rounded-full h-5 w-5 flex items-center justify-center absolute" style={{right: '10px',top: '10px'}}>
        {conversation.unread}
      </span>
    )}
  </div>
);

const CustomerListItem: React.FC<{
  customer: Customer;
  onClick: () => void;
}> = ({ customer, onClick }) => (
  <div
    onClick={onClick}
    className="flex items-center p-4 hover:bg-gray-50 cursor-pointer border-b border-gray-100"
  >
    <div className="relative mr-3">
      <img
        src={`https://ui-avatars.com/api/?name=${encodeURIComponent(
          `${customer.customer.firstName || ''} ${customer.customer.lastName || ''}`
        )}`}
        alt={`${customer.customer.firstName || ''} ${customer.customer.lastName || ''}`}
        className="w-10 h-10 rounded-full"
      />
    </div>
    <div>
      <h4 className="font-medium text-sm">
        {customer.customer.firstName} {customer.customer.lastName}
      </h4>
      <span className="text-xs text-gray-500">
        {customer.customer.email || customer.customer.mobileNumber || 'No contact info'}
      </span>
    </div>
  </div>
);

const AdminMessagesPage = ({ user }: { user: AuthUser }) => {
  useRedirectHomeUnlessUserIsAdmin({ user });
  const [activeTab, setActiveTab] = useState('messages');
  const [selectedChat, setSelectedChat] = useState<AugmentedConversation | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [newMessage, setNewMessage] = useState('');
  const [isStartingChat, setIsStartingChat] = useState(false);
  const [newChatUserId, setNewChatUserId] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [isCreatingConversation, setIsCreatingConversation] = useState(false);

  // Fetch conversations
  const { data: conversations, isLoading: isLoadingConversations, error: conversationsError } = 
    useQuery(getConversations, {});

  // Fetch messages for selected chat
  const { data: messages, isLoading: isLoadingMessages, error: messagesError } = 
    useQuery(getMessages, { conversationId: selectedChat?.id || 0 }, { enabled: !!selectedChat });

  // Fetch customers
  const { 
    data: customers, 
    isLoading: isLoadingCustomers 
  } = useQuery(getProviderCustomers);

  // Filter customers based on search query
  const filteredCustomers = customers?.filter((customer: Customer) => {
    const fullName = `${customer.customer.firstName || ''} ${customer.customer.lastName || ''}`.toLowerCase();
    const email = (customer.customer.email || '').toLowerCase();
    const mobile = (customer.customer.mobileNumber || '').toLowerCase();
    const query = searchQuery.toLowerCase();
    
    return fullName.includes(query) || email.includes(query) || mobile.includes(query);
  });

  useEffect(() => {
    if (conversations?.length && !selectedChat) {
      setSelectedChat(conversations[0]);
    }
  }, [conversations]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSendMessage = async () => {
    if (!selectedChat || !newMessage.trim()) return;

    try {
      await sendMessage({
        conversationId: selectedChat.id,
        content: newMessage,
      });
      setNewMessage('');
    } catch (error) {
      console.error('Failed to send message:', error);
      // You might want to show an error toast here
    }
  };

  const handleStartNewChat = async (customerId: string) => {
    setIsCreatingConversation(true);
    try {
      const result = await startConversation({
        otherUserIds: [customerId],
        isGroup: false,
      }) as ConversationResult;
      
      const newConversation: AugmentedConversation = {
        id: result.id,
        name: result.name,
        isGroup: result.isGroup,
        ownerDomain: result.ownerDomain,
        ownerId: result.ownerId,
        createdAt: result.createdAt.toISOString(),
        updatedAt: result.updatedAt.toISOString(),
        lastMessageId: result.lastMessageId,
        unread: 0,
        displayName: result.name || result.participants[0]?.user?.firstName || 'New Chat',
        displayImage: null,
        otherParticipantIds: result.participants.map(p => p.userId).filter(id => id !== user.id),
        participants: result.participants.map(p => ({
          id: p.id,
          userId: p.userId,
          userDomain: p.userDomain,
          canWrite: p.canWrite,
          canDownload: p.canDownload,
          unreadCount: p.unreadCount,
          user: p.user
        })),
        lastMessage: null,
        owner: result.owner
      };
      
      setIsStartingChat(false);
      setSearchQuery('');
      setSelectedChat(newConversation);
    } catch (error) {
      console.error('Failed to start conversation:', error);
      // You might want to show an error message to the user
    } finally {
      setIsCreatingConversation(false);
    }
  };

  if (isLoadingConversations) {
    return <div>Loading conversations...</div>;
  }

  if (conversationsError) {
    return <div>Error loading conversations: {conversationsError.message}</div>;
  }

  return (
    <DefaultLayout user={user}>
      <div className="md:px-6 md:py-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-800">Chat</h2>
          </div>
          <button 
            onClick={() => setIsStartingChat(true)}
            style={{border: 'none'}} 
            className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg shadow-sm flex items-center text-sm"
          >
            <FaPlus className="mr-2" />
            New Chat
          </button>
        </div>

        {/* Start New Chat Modal */}
        {isStartingChat && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white p-6 rounded-lg shadow-xl w-full max-w-md">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">Start New Chat</h3>
                <button
                  onClick={() => {
                    setIsStartingChat(false);
                    setSearchQuery('');
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>
              
              <Input
                placeholder="Search customers..."
                prefix={<SearchOutlined className="text-gray-400" />}
                value={searchQuery}
                onChange={(e: { target: { value: string } }) => setSearchQuery(e.target.value)}
                className="mb-4"
              />

              <div className="max-h-96 overflow-y-auto">
                {isLoadingCustomers ? (
                  <div className="flex justify-center py-4">
                    <Spin />
                  </div>
                ) : filteredCustomers?.length === 0 ? (
                  <div className="text-center py-4 text-gray-500">
                    No customers found
                  </div>
                ) : (
                  filteredCustomers?.map((customer: Customer) => (
                    <CustomerListItem
                      key={customer.customer.id}
                      customer={customer}
                      onClick={() => !isCreatingConversation && handleStartNewChat(customer.customer.id)}
                    />
                  ))
                )}
              </div>
            </div>
          </div>
        )}

        {/* Main Content Grid */}
        <div className="grid grid-cols-12 gap-6">
          {/* Left Sidebar - Contact List */}
          <div className="col-span-12 md:col-span-4 bg-white rounded-xl shadow-lg overflow-hidden">
            <div className="">
              <div className="flex">
                <button
                  style={{
                    border:"none", 
                    borderBottom: activeTab === 'messages' ? '2px solid #3b82f6' : 'none',
                    color: activeTab === 'messages' ? '#3b82f6' : '#000'
                  }}  
                  onClick={() => setActiveTab('messages')}
                  className={`flex-1 py-3 px-2 text-sm font-medium text-center`}
                >
                  Messages
                </button>
                <button
                  style={{
                    border:"none", 
                    borderBottom: activeTab === 'contacts' ? '2px solid #3b82f6' : 'none',
                    color: activeTab === 'contacts' ? '#3b82f6' : '#000'
                  }}  
                  onClick={() => setActiveTab('contacts')}
                  className={`flex-1 py-3 px-2 text-sm font-medium text-center`}
                >
                  Contacts
                </button>
              </div>
            </div>
            <div className="h-[calc(100vh-250px)] overflow-y-auto no-scrollbar">
              {conversations?.map((conversation) => (
                <ContactListItem
                  key={conversation.id}
                  conversation={conversation}
                  isActive={selectedChat?.id === conversation.id}
                  onClick={() => setSelectedChat(conversation)}
                />
              ))}
            </div>
          </div>

          {/* Middle - Chat Area */}
          <div className="col-span-12 md:col-span-8 bg-white rounded-xl shadow-lg flex flex-col">
            {selectedChat ? (
              <>
                {/* Chat Header */}
                <div className="p-4 border-b border-gray-200">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="relative mr-3">
                        <img
                          src={selectedChat.displayImage || `https://ui-avatars.com/api/?name=${encodeURIComponent(selectedChat.displayName)}`}
                          alt={selectedChat.displayName}
                          className="w-10 h-10 rounded-full"
                        />
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-800 m-0">
                          {selectedChat.displayName}
                        </h4>
                        <span className="text-xs text-gray-500">
                          {selectedChat.isGroup ? `${selectedChat.participants.length} participants` : 'Direct Message'}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button style={{border: 'none'}} className="p-2 text-gray-500 hover:bg-gray-100 rounded-full focus:outline-none">
                        <FaPhone size={18} />
                      </button>
                      <button style={{border: 'none'}} className="p-2 text-gray-500 hover:bg-gray-100 rounded-full focus:outline-none">
                        <FaVideo size={18} />
                      </button>
                    </div>
                  </div>
                </div>

                {/* Messages Area */}
                <div className="flex-grow p-4 md:p-6 space-y-4 overflow-y-auto no-scrollbar h-[calc(100vh-380px)] bg-gray-50">
                  {isLoadingMessages ? (
                    <div>Loading messages...</div>
                  ) : messagesError ? (
                    <div>Error loading messages: {messagesError.message}</div>
                  ) : (messages as Message[])?.map((message) => (
                    <div
                      key={message.id}
                      className={`flex items-end ${
                        message.senderId === user.id ? 'justify-end' : 'justify-start'
                      }`}
                    >
                      {message.senderId !== user.id && (
                        <img
                          src={`https://ui-avatars.com/api/?name=${encodeURIComponent(message.sender.firstName || '')}`}
                          alt={message.sender.firstName || 'User'}
                          className="w-8 h-8 rounded-full mr-2 mb-1 shadow-sm"
                        />
                      )}
                      <div
                        className={`max-w-[70%] p-3 rounded-xl shadow-md flex flex-col ${
                          message.senderId === user.id
                            ? 'bg-blue-600 text-white rounded-br-none'
                            : 'bg-white text-gray-800 rounded-bl-none border border-gray-200'
                        }`}
                      >
                        <span className="text-sm leading-snug w-100">{message.content}</span>
                        <span className={`text-xs mt-1.5 text-right ${
                          message.senderId === user.id ? 'text-blue-100 opacity-80' : 'text-gray-400'
                        }`}>
                          {new Date(message.createdAt).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                        </span>
                      </div>
                      {message.senderId === user.id && (
                        <img
                          src={`https://ui-avatars.com/api/?name=${encodeURIComponent(user.firstName || '')}`}
                          alt={user.firstName || 'Me'}
                          className="w-8 h-8 rounded-full ml-2 mb-1 shadow-sm"
                        />
                      )}
                    </div>
                  ))}
                  <div ref={messagesEndRef} />
                </div>

                {/* Message Input Area */}
                <div className="p-4 bg-white">
                  <div className="flex items-center bg-white border border-gray-300 rounded-xl p-1">
                    <button style={{border: 'none'}} className="p-2.5 text-gray-500 hover:text-gray-700 focus:outline-none focus:ring-1 focus:ring-gray-300 rounded-full">
                      <FaPaperclip size={18} />
                    </button>
                    <input
                      style={{border: 'none'}}
                      type="text"
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      onKeyPress={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                          e.preventDefault();
                          handleSendMessage();
                        }
                      }}
                      placeholder="Type a message..."
                      className="flex-1 bg-transparent px-3 py-2 text-sm text-gray-800 placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-300"
                    />
                    <button 
                      onClick={handleSendMessage}
                      style={{border: 'none'}} 
                      className="p-2.5 text-blue-600 hover:text-blue-700 focus:outline-none focus:ring-1 focus:ring-blue-300 rounded-full"
                    >
                      <FaPaperPlane size={18} />
                    </button>
                  </div>
                </div>
              </>
            ) : (
              <div className="h-full flex-grow flex flex-col items-center justify-center text-gray-400 p-4">
                <svg className="w-24 h-24 text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"></path></svg>
                <p>Select a conversation to start chatting</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </DefaultLayout>
  );
};

export default AdminMessagesPage;

// Reminder for global styles if needed (e.g., for .no-scrollbar)
// .no-scrollbar::-webkit-scrollbar { display: none; }
// .no-scrollbar { -ms-overflow-style: none; scrollbar-width: none; } 