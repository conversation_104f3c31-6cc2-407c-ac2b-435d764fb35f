import React, { useState, useEffect } from 'react';
import { useQuery, useAction } from 'wasp/client/operations';
import {
  getProviderCustomers,
  getServices,
  getSProvidingPlaces,
  getQueuesByPlace,
  createAppointment, // Import the action
} from 'wasp/client/operations';
import { type User, type Service, type SProvidingPlace, type Queue } from 'wasp/entities';
import toast from 'react-hot-toast';
import {
  Modal,
  Form,
  DatePicker,
  TimePicker,
  Input,
  Button,
  Spin,
  Alert,
  AutoComplete,
  Row,
  Col,
  Space,
} from 'antd';
import { useWatch } from 'antd/es/form/Form'; // Import useWatch
import dayjs from 'dayjs';
import AddCustomerModal from '../../dashboards/users/AddCustomerModal'; // Import AddCustomerModal
import Select from 'antd/es/select';
import { Option } from 'antd/es/mentions';


interface NewAppointmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  startDate: Date | null;
  endDate: Date | null; // Keep this prop for initial duration calculation
  onSuccess: () => void;
}

// Ant Design Form values structure (using dayjs for date/time)
interface AppointmentFormValues {
  customerId?: string; // Store the selected ID
  customerSearch?: string; // For the AutoComplete input
  serviceId?: number;
  placeId?: number;
  queueId?: number;
  date?: dayjs.Dayjs;
  startTime?: dayjs.Dayjs;
  notes?: string;
}

const NewAppointmentModal: React.FC<NewAppointmentModalProps> = ({
  isOpen,
  onClose,
  startDate,
  endDate, // Use this for initial duration
  onSuccess,
}) => {
  const [form] = Form.useForm<AppointmentFormValues>();

  // --- Data Fetching & Actions ---
  const {
    data: customers,
    isLoading: isLoadingCustomers,
    error: customersError,
    refetch: refetchCustomers,
  } = useQuery(getProviderCustomers); // Refetch needed after adding new customer
  const {
    data: services,
    isLoading: isLoadingServices,
    error: servicesError,
  } = useQuery(getServices);
  const {
    data: places,
    isLoading: isLoadingPlaces,
    error: placesError,
  } = useQuery(getSProvidingPlaces);
  const selectedPlaceId = Form.useWatch('placeId', form);
  const {
    data: queues,
    isLoading: isLoadingQueues,
    error: queuesError,
  } = useQuery(
    getQueuesByPlace,
    { sProvidingPlaceId: selectedPlaceId! },
    { enabled: !!selectedPlaceId }
  );
  const createAppointmentAction = useAction(createAppointment); // Use the action

  // --- State ---
  const [calculatedEndTime, setCalculatedEndTime] = useState<dayjs.Dayjs | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isAddCustomerModalOpen, setIsAddCustomerModalOpen] = useState(false); // State for Add Customer modal
  const [customerOptions, setCustomerOptions] = useState<{ value: string; label: React.ReactNode; customer: User }[]>([]); // State for AutoComplete options
  const [selectedCustomerId, setSelectedCustomerId] = useState<string | undefined>(undefined); // Explicitly track selected customer ID
  const [selectedQueueId, setSelectedQueueId] = useState<number | undefined>(undefined);

  // --- Watch Form Values for End Time Calculation ---
  const watchedDate = useWatch('date', form);
  const watchedStartTime = useWatch('startTime', form);
  const watchedServiceId = useWatch('serviceId', form);

  // --- Effects ---
  // Pre-fill form and auto-select place when modal opens or start date changes
  useEffect(() => {
    if (isOpen) {
        let initialDate: dayjs.Dayjs | undefined = undefined;
        let initialStartTime: dayjs.Dayjs | undefined = undefined;

        if (startDate) {
            // Case 1: Opened from Calendar with a start date
            initialDate = dayjs(startDate);
            initialStartTime = dayjs(startDate);
            console.log("Modal opened from Calendar, StartDate:", startDate);
        } else {
            // Case 2: Opened from AppointmentsPage (startDate is null)
            // Default to current date and time (e.g., start of the next hour or current time)
            initialDate = dayjs(); // Current date
            initialStartTime = dayjs().minute(0).second(0).add(1, 'hour'); // Start of next hour, or use dayjs() for current time
            console.log("Modal opened without StartDate, defaulting date/time.");
        }

        const initialPlaceId = places?.length === 1 ? places[0].id : undefined;

        form.setFieldsValue({
            date: initialDate,
            startTime: initialStartTime,
            placeId: initialPlaceId,
            // Reset other fields explicitly
            customerId: undefined,
            customerSearch: '',
            serviceId: undefined,
            queueId: undefined,
            notes: '',
        });
        
        // Reset non-form state variables
        setSelectedCustomerId(undefined); 
        setSelectedQueueId(undefined);
        setCalculatedEndTime(null); 
        setCustomerOptions([]); 

    } else if (!isOpen) {
      // Clear form on close
      form.resetFields();
      setCalculatedEndTime(null);
      setIsSubmitting(false);
      setCustomerOptions([]);
      setSelectedCustomerId(undefined);
      setSelectedQueueId(undefined);
    }
  // Dependencies now only need isOpen and places (for initial placeId)
  // startDate is handled internally
  }, [isOpen, places, form]); // Removed startDate from dependencies

  // Calculate end time whenever watched date, start time, or service changes
  useEffect(() => {
    if (watchedDate && watchedStartTime && watchedServiceId && services) {
      const selectedService = services.find(s => s.id === watchedServiceId);
      if (selectedService) {
        try {
          // Combine date and time into a single dayjs object
          const startDateTime = watchedDate
            .hour(watchedStartTime.hour())
            .minute(watchedStartTime.minute())
            .second(0)
            .millisecond(0);

          if (startDateTime.isValid()) {
            const durationMinutes = selectedService.duration;
            const endDateTime = startDateTime.add(durationMinutes, 'minute');
            setCalculatedEndTime(endDateTime);
          } else {
            setCalculatedEndTime(null); // Invalid start date/time
          }
        } catch (e) {
          console.error('Error calculating end time:', e);
          setCalculatedEndTime(null); // Reset on error
        }
      } else {
        setCalculatedEndTime(null); // No service selected or found
      }
    } else {
      setCalculatedEndTime(null); // Reset if inputs are missing
    }
  }, [watchedDate, watchedStartTime, watchedServiceId, services]); // Depend on watched values

  // --- Filter Queues based on selected Service ---
  const filteredQueues = React.useMemo(() => {
    if (!queues || !watchedServiceId) {
      return [];
    }
    // Need to know which services are associated with each queue.
    // getQueuesByPlace doesn't return this yet.
    // TEMPORARY: Return all active queues for the place until query is updated.
    // TODO: Update getQueuesByPlace query to include associated services OR filter based on Service data.
    // Ensure queues is treated as an array before filtering
    const queuesArray = Array.isArray(queues) ? queues : [];
    return queuesArray.filter((q: Queue) => q.isActive);
  }, [queues, watchedServiceId]);

  // --- Generate Queue Select Options ---
  const queueOptions = React.useMemo(() => {
     if (filteredQueues.length > 0) {
         return filteredQueues.map((queue: Queue) => ({ // ADDED: Type annotation for queue
             value: queue.id,
             label: queue.title,
         }));
     } else if (selectedPlaceId && watchedServiceId && !isLoadingQueues) {
         // Only show 'No queues' message if place/service are selected and loading is done
         return [{ value: undefined, label: 'No queues available for this service/place.', disabled: true }];
     }
     return []; // Otherwise, return empty array (Select shows placeholder)
  }, [filteredQueues, selectedPlaceId, watchedServiceId, isLoadingQueues]);

  // --- Handlers ---

  // Updates the dropdown options based on the search text
  const handleCustomerSearch = (searchText: string) => {
    if (!customers || searchText.trim() === '') {
      setCustomerOptions([]);
      return;
    }

    const searchTerms = searchText.toLowerCase().trim().split(/\s+/);
    
    const filteredCustomers = customers.filter(customer => {
      const firstName = (customer?.customer.firstName || '').toLowerCase();
      const lastName = (customer?.customer.lastName || '').toLowerCase();
      const mobile = (customer?.customer.mobileNumber || '').toLowerCase();
      const fullName = `${firstName} ${lastName}`.toLowerCase();

      // Check if ALL search terms are found in either the name or mobile number
      return searchTerms.every(term => 
        fullName.includes(term) || mobile.includes(term)
      );
    });

    const options = filteredCustomers.map(customer => ({
      value: customer?.customer?.id, // Use ID as value for better selection handling
      label: (
        <div>
          <div style={{ fontSize: '12px', fontWeight: 'bold' }}>{`${customer?.customer.firstName || ''} ${customer?.customer.lastName || ''}`}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>{customer?.customer.mobileNumber || 'No mobile'}</div>
        </div>
      ),
      customer: customer.customer
    }));

    setCustomerOptions(options);
  };

  // Handles selecting a customer from the dropdown
  const handleCustomerSelect = (value: string, option: { value: string; label: React.ReactNode; customer: User }) => {
    setSelectedCustomerId(option.customer.id);
    const displayText = `${option.customer.firstName || ''} ${option.customer.lastName || ''} (${option.customer.mobileNumber || 'N/A'})`;
    form.setFieldsValue({
      customerId: option.customer.id,
      customerSearch: displayText
    });
    setCustomerOptions([]); // Clear dropdown options after selection
  };

  // Handles changes to the AutoComplete input (typing, clearing, selecting)
  const handleCustomerInputChange = (value: string) => {
    form.setFieldsValue({ customerSearch: value });
    
    if (!value) {
      setSelectedCustomerId(undefined);
      form.setFieldsValue({ customerId: undefined });
      setCustomerOptions([]);
    } else {
      handleCustomerSearch(value);
    }
  };

  const openAddCustomerModal = () => setIsAddCustomerModalOpen(true);
  const closeAddCustomerModal = () => setIsAddCustomerModalOpen(false);

  const handleCustomerAdded = () => {
    refetchCustomers(); // Refetch customer list
    toast.success('New customer added. Please search for them again to select.');
  };

  const handlePlaceChange = () => {
    form.setFieldsValue({ queueId: undefined });
    setSelectedQueueId(undefined);
  };

  const handleServiceChange = () => {
    form.setFieldsValue({ queueId: undefined });
    setSelectedQueueId(undefined);
  };

  const handleQueueChange = (value: number) => {
    setSelectedQueueId(value);
    form.setFieldsValue({ queueId: value });
  };

  const onFinish = async (values: AppointmentFormValues) => {
    // Use the state variable for the customer ID check, as form value might be briefly out of sync
    if (!selectedCustomerId) {
      toast.error('Please search and select a customer.');
      form.validateFields(['customerSearch']); // Trigger validation feedback
      return;
    }
    // ADDED: Queue Validation
    if (!selectedQueueId) {
      toast.error('Please select a queue.');
      form.validateFields(['queueId']);
      return;
    }
    // Ensure calculatedEndTime is valid and derived from the latest form values
    if (!values.serviceId || !values.placeId || !values.date || !values.startTime || !calculatedEndTime || !calculatedEndTime.isValid()) {
      toast.error('Please select a service, place, and ensure date/time are set correctly, resulting in a valid end time.');
      form.validateFields(['serviceId', 'placeId', 'date', 'startTime']);
      return;
    }

    setIsSubmitting(true);
    try {
      // Construct final Date objects from dayjs objects using the latest watched values
      // This correctly converts the LOCAL time selection to its UTC equivalent Date object
      const finalStartTime = watchedDate!
        .hour(watchedStartTime!.hour())
        .minute(watchedStartTime!.minute())
        .second(0)
        .millisecond(0)
        .toDate(); // Convert local dayjs to UTC Date

      const finalEndTime = calculatedEndTime!.toDate(); // Convert local dayjs to UTC Date

      if (isNaN(finalStartTime.getTime()) || isNaN(finalEndTime.getTime())) {
         toast.error('Invalid date or time constructed.');
         setIsSubmitting(false);
         return;
      }

      console.log('Submitting data (Local to UTC):', { // Updated log description
        customerUserId: selectedCustomerId, // Use the tracked ID from state
        serviceId: values.serviceId,
        placeId: values.placeId,
        queueId: selectedQueueId,
        startTime: finalStartTime, // Send the standard UTC Date object
        endTime: finalEndTime,   // Send the standard UTC Date object
        notes: values.notes || null,
      });

      // Call the actual action
      await createAppointmentAction({
        customerUserId: selectedCustomerId, // Use the tracked ID
        serviceId: values.serviceId,
        placeId: values.placeId,
        queueId: selectedQueueId,
        startTime: finalStartTime,
        endTime: finalEndTime,
        notes: values.notes || null,
      });

      toast.success('Appointment created successfully!');
      onSuccess(); // Call the success callback (e.g., to refetch calendar events)
      onClose();   // Close the modal on success (will trigger form reset via useEffect)

    } catch (error: any) {
      toast.error(`Failed to create appointment: ${error.message || 'Unknown error'}`);
      console.error(error);
      setIsSubmitting(false); // Allow retry
    }
  };

  const onFinishFailed = (errorInfo: any) => {
    console.log('Failed:', errorInfo);
    // Check if the specific error is for customerSearch and refine the message
    const customerError = errorInfo.errorFields?.find((field: any) => field.name.includes('customerSearch'));
    if (customerError) {
        // Use the error message generated by the validator if available
        toast.error(customerError.errors?.[0] || 'Please search and select a customer.');
    } else {
        toast.error('Please fill in all required fields correctly.');
    }
  };

  const isLoading = isLoadingCustomers || isLoadingServices || isLoadingPlaces || isLoadingQueues;
  const hasError = customersError || servicesError || placesError || queuesError;
  const errorMessage = customersError?.message || servicesError?.message || placesError?.message || queuesError?.message;

  return (
    <>
      <Modal
        title="New Appointment"
        open={isOpen}
        onCancel={onClose}
        footer={[
          <Button key="back" onClick={onClose}>
            Cancel
          </Button>,
          <Button key="submit" type="primary" loading={isSubmitting} onClick={() => form.submit()}>
            Create Appointment
          </Button>,
        ]}
        width={600} // Adjust width as needed
      >
        <Spin spinning={isLoading || isSubmitting}> {/* Also spin while submitting */}
          {hasError && <Alert message="Error loading data" description={errorMessage} type="error" showIcon className="mb-4" />}

          {!hasError && (
            <Form
              form={form}
              layout="vertical"
              onFinish={onFinish}
              onFinishFailed={onFinishFailed}
              initialValues={{ // Set initial structure, actual values set in useEffect
                date: undefined,
                startTime: undefined,
                customerSearch: '',
                serviceId: undefined,
                placeId: undefined,
                queueId: undefined,
                notes: '',
              }}
            >
              {/* Hidden field to store customerId, linked via selectedCustomerId state */}
               <Form.Item name="customerId" hidden noStyle>
                 <Input />
               </Form.Item>

              {/* Customer Search + Add Button */}
              <Form.Item
                label="Customer"
                name="customerSearch" // This name connects the AutoComplete to the form state
                rules={[
                  // Custom validator to ensure an ID is selected *at the time of submission*
                  { validator: async (_, value) => {
                      // Check the state variable `selectedCustomerId` directly
                      if (!selectedCustomerId) {
                          // If the field has text but no ID is selected, prompt to select from list
                          if (value) {
                              throw new Error('Please select a customer from the list.');
                          } else { // If field is empty
                              throw new Error('Please search for and select a customer.');
                          }
                      }
                      // If selectedCustomerId exists, validation passes
                    },
                    // Validate only on submit or when form.validateFields is explicitly called.
                    // Avoid validating 'onChange' which can be annoying during typing.
                    validateTrigger: 'onSubmit'
                  }
                ]}
                // Help text is managed by the validator's error message
              >
                <Space.Compact style={{ width: '100%' }}>
                  <AutoComplete
                    options={customerOptions}
                    onSelect={handleCustomerSelect} // When an item is selected from dropdown
                    onChange={handleCustomerInputChange} // Crucial: For handling input changes (typing, clearing, selecting) to manage state
                    placeholder="Search by name or mobile"
                    style={{ width: '100%' }}
                    allowClear // Allows clearing the input easily via 'x' button
                  />
                  <Button type="primary" onClick={openAddCustomerModal}>
                    + Add New
                  </Button>
                </Space.Compact>
              </Form.Item>

              <Row gutter={16}>
                {/* Providing Place Selection */}
                <Col xs={24} sm={12}>
                  <Form.Item
                    label="Place"
                    name="placeId"
                    rules={[{ required: true, message: 'Please select a place.' }]}
                  >
                    <Select
                      placeholder="-- Select Place --"
                      loading={isLoadingPlaces}
                      disabled={places?.length === 1}
                      allowClear
                      onChange={handlePlaceChange}
                    >
                      {places?.map((place) => (
                        <Option key={place.id} value={place.id}>
                          {place.name}
                        </Option>
                      ))}
                      {places?.length === 0 && (
                        <Option value="" disabled>
                          No providing places found
                        </Option>
                      )}
                    </Select>
                  </Form.Item>
                </Col>

                {/* Service Selection */}
                <Col xs={24} sm={12}>
                  <Form.Item
                    label="Service"
                    name="serviceId"
                    rules={[{ required: true, message: 'Please select a service.' }]}
                  >
                    <Select
                      placeholder="-- Select Service --"
                      loading={isLoadingServices}
                      disabled={isLoadingServices || services?.length === 0}
                      allowClear
                      onChange={handleServiceChange}
                    >
                      {services?.map((service) => (
                        <Option key={service.id} value={service.id}>
                          {service.title} ({service.duration} min)
                        </Option>
                      ))}
                      {services?.length === 0 && (
                        <Option value="" disabled>
                          No services found
                        </Option>
                      )}
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              {/* Queue Selection */}
              <Form.Item
                name="queueId"
                label="Queue"
                rules={[{ required: true, message: 'Please select a queue' }]}
              >
                <Select
                  placeholder="Select Queue"
                  loading={isLoadingQueues}
                  disabled={!selectedPlaceId || !watchedServiceId || isLoadingQueues || queueOptions.length === 0 || (queueOptions.length === 1 && queueOptions[0].label?.startsWith('No queues'))}
                  value={selectedQueueId}
                  onChange={handleQueueChange}
                  options={queueOptions} // Use the generated options array
                />
              </Form.Item>

              {/* Date, Start Time, End Time (Inline) */}
              <Row gutter={16}>
                <Col xs={24} sm={8}>
                  <Form.Item
                    label="Date"
                    name="date"
                    rules={[{ required: true, message: 'Please select a date.' }]}
                  >
                    <DatePicker style={{ width: '100%' }} format="YYYY-MM-DD" />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={8}>
                  <Form.Item
                    label="Start Time"
                    name="startTime"
                    rules={[{ required: true, message: 'Please select a start time.' }]}
                  >
                    <TimePicker style={{ width: '100%' }} format="HH:mm" minuteStep={5} showNow={false} />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={8}>
                  <Form.Item label="End Time">
                    <TimePicker
                      style={{ width: '100%' }}
                      format="HH:mm"
                      value={calculatedEndTime} // Display calculated time
                      disabled // Always disabled
                      minuteStep={5}
                      // No name prop needed as it's not part of the submittable form data directly
                    />
                  </Form.Item>
                </Col>
              </Row>

              {/* Notes */}
              <Form.Item label="Notes (Optional)" name="notes">
                <Input.TextArea rows={3} placeholder="Add any relevant notes here" />
              </Form.Item>
            </Form>
          )}
        </Spin>
      </Modal>

      {/* Add Customer Modal (controlled by this component) */}
      <AddCustomerModal
        isOpen={isAddCustomerModalOpen}
        onClose={closeAddCustomerModal}
        onSuccess={handleCustomerAdded}
      />
    </>
  );
};

export default NewAppointmentModal;