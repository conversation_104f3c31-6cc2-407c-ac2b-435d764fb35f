/* Custom styles for CalendarPage.tsx */

/* ========================================= */
/* ========= Light Mode Styles =========== */
/* ========================================= */

/* Style filter sections for light mode */
.filter-card-wrapper {
  background-color: #ffffff; /* Ensure white background */
  /* border: 1px solid #e8e8e8;  */
  border-radius: 4px;
  overflow: hidden;
}
.filter-card-wrapper h5 {
  margin-top: 0 !important;
}
.filter-card-header {
  padding: 12px 16px;
  border-bottom: 1px solid #e8e8e8; /* Subtle bottom border */
  background-color: #fafafa; /* Very light gray header background */
}

.filter-card-header .ant-typography {
  color: rgba(0, 0, 0, 0.85); /* Default dark text */
  margin-bottom: 0 !important;
}

.filter-card-content {
  padding: 16px;
}

/* Style the status icons for light mode */
.status-icon,
.fc-event-status-icon svg {
  color: rgba(0, 0, 0, 0.65); /* Medium gray for icons */
  vertical-align: middle;
}

/* Keep any specific light-mode status colors if desired (uncommented) */
/* Example:
.status-icon-confirmed,
.status-icon-completed {
  color: #52c41a; 
}
.status-icon-canceled {
  color: #ff4d4f; 
}
*/

/* ========================================= */
/* === Mini Calendar Base Styles (Light) === */
/* ========================================= */

/* Attempt to force square aspect ratio and center content for mini calendar */
.mini-calendar-wrapper .fc-daygrid-day-frame {
  /* Ensure it tries to maintain aspect ratio */
  aspect-ratio: 1 / 1;
  /* Remove default padding that might interfere */
  padding: 0 !important;
  /* Center content vertically and horizontally */
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Ensure the inner link/number element doesn't interfere */
.mini-calendar-wrapper .fc-daygrid-day-number {
  /* Adjust styles if needed, e.g., remove padding/margin */
  padding: 0;
  margin: 0;
  /* Ensure it doesn't prevent centering */
  display: block;
}

/* Add hover effect to day cells */
.mini-calendar-wrapper .fc-daygrid-day:hover .fc-daygrid-day-frame {
  /* Target the frame for background to avoid interfering with selection highlight */
  background-color: #f0f0f0 !important; /* Light gray background on hover */
  cursor: pointer;
}

/* Make day headers smaller */
.mini-calendar-wrapper .fc-col-header-cell-cushion { 
  font-size: 0.7rem !important; /* Smaller font size for Mon, Tue, etc. */
  padding: 2px 4px !important; /* Adjust padding */
  display: inline-block; /* Ensure padding takes effect */
  width: 100%; /* Ensure it fills the header cell */
  text-align: center;
  color: rgba(0, 0, 0, 0.75) !important; /* Slightly darker gray for headers */
}

/* Optional: Adjust header/toolbar padding if needed */
.mini-calendar-wrapper .fc-header-toolbar {
  margin-bottom: 0.5em !important; /* Reduce space below header */
  padding: 0 !important;
}
.mini-calendar-wrapper .fc-toolbar-chunk {
  padding: 0 !important;
}

/* Optional: Adjust font size */
.mini-calendar-wrapper .fc-daygrid-body,
.mini-calendar-wrapper .fc-toolbar-title {
  font-size: 0.8rem; /* Make font slightly smaller */
}

/* Optional: Adjust header button sizes */
.mini-calendar-wrapper .fc-button {
    padding: 0.1em 0.3em !important; /* Smaller button padding */
    font-size: 0.9em !important; /* Slightly smaller button font */
}

/* Style the main calendar title */
#main-calendar-wrapper .fc-toolbar-title {
    font-size: 1.2em !important; /* Adjust size as needed */
    font-weight: bold !important;
    color: rgba(0, 0, 0, 0.85) !important; /* Standard dark text for title */
}

/* Mini Calendar Text Colors (Light Mode) */
.mini-calendar-wrapper .fc-daygrid-day-number,
.mini-calendar-wrapper .fc-daygrid-day-number a { /* Target links too */
  color: rgba(0, 0, 0, 0.65) !important; /* Medium gray */
  text-decoration: none;
}

.mini-calendar-wrapper .fc-toolbar-title {
  /* ... existing size styles ... */
  color: rgba(0, 0, 0, 0.85) !important; /* Standard dark text for title */
}

.mini-calendar-wrapper .fc-button {
    /* ... existing size/padding styles ... */
    /* Use default AntD button colors for light mode */
}

/* ========================================= */
/* === Main Calendar Base Styles (Light) === */
/* ========================================= */

/* Main Calendar Text Colors (Light Mode) */
#main-calendar-wrapper .fc-col-header-cell-cushion,
#main-calendar-wrapper .fc-col-header-cell-cushion a { /* Target links too */
  color: rgba(0, 0, 0, 0.75) !important; /* Darker gray for headers */
  text-decoration: none;
}

#main-calendar-wrapper .fc-timegrid-axis-cushion { /* Time labels (8am, 9am...) */
  color: rgba(0, 0, 0, 0.65) !important; /* Medium gray */
}

/* Main Calendar Buttons (Light Mode) */
#main-calendar-wrapper .fc-button {
    /* Use default AntD button colors for light mode */
}

/* ========================================= */
/* ========= Dark Mode Overrides ========= */
/* ========================================= */

/* Target FullCalendar instances within a .dark container */
.dark #mini-calendar-container .fc,
.dark #main-calendar-wrapper .fc {
  /* Borders: Use a subtle, semi-transparent white */
  --fc-border-color: rgba(255, 255, 255, 0.15);

  /* Backgrounds: Generally keep transparent to inherit page background */
  --fc-daygrid-day-bg-color: transparent;
  --fc-timegrid-slot-lane-bg-color: transparent;
  /* Maybe a very subtle background for the current day */
  --fc-today-bg-color: rgba(255, 255, 255, 0.05);

  /* Text Colors: Ensure readability */
  --fc-daygrid-day-number-color: rgba(255, 255, 255, 0.8);
  --fc-col-header-cell-cushion-color: rgba(255, 255, 255, 0.9);
  --fc-timegrid-axis-cushion-color: rgba(255, 255, 255, 0.7);
  --fc-list-day-text-color: rgba(255, 255, 255, 0.9);

  /* Button / Header Styles */
  --fc-button-bg-color: rgba(255, 255, 255, 0.1);
  --fc-button-border-color: rgba(255, 255, 255, 0.15);
  /* Ensure this default button text color is applied */
  --fc-button-text-color: rgba(255, 255, 255, 0.9);
  --fc-button-hover-bg-color: rgba(255, 255, 255, 0.15);
  --fc-button-hover-border-color: rgba(255, 255, 255, 0.2);
  --fc-button-active-bg-color: rgba(255, 255, 255, 0.2);
  --fc-button-active-border-color: rgba(255, 255, 255, 0.25);
  
  /* Event Styling (ensure text is visible) */
  --fc-event-text-color: #ffffff; /* Default event text to white */
}

/* Explicitly set text color for Mini Calendar Day Numbers */
.dark .mini-calendar-wrapper .fc-daygrid-day-number {
  color: rgba(255, 255, 255, 0.85) !important;
}
/* Ensure links within day numbers are also styled (if applicable) */
.dark .mini-calendar-wrapper .fc-daygrid-day-number a {
  color: rgba(255, 255, 255, 0.85) !important;
}

/* Explicitly set text color for Mini Calendar Day Headers (Sun, Mon, etc.) */
.dark .mini-calendar-wrapper .fc-col-header-cell-cushion {
  color: rgba(255, 255, 255, 0.8) !important; /* Consistent light color */
}

/* Explicitly set text color for Main Calendar Column Headers */
.dark #main-calendar-wrapper .fc-col-header-cell-cushion { /* Targets 'Sun 4/27', etc. */
  color: rgba(255, 255, 255, 0.9) !important;
}
/* Ensure links within headers are also styled (if applicable) */
.dark #main-calendar-wrapper .fc-col-header-cell-cushion a {
  color: rgba(255, 255, 255, 0.9) !important;
}

/* Explicitly set background color for Main Calendar Column Header CELLS */
.dark #main-calendar-wrapper .fc .fc-col-header { 
  background-color: #1a222c !important;
}

/* Explicitly set text color for common button states in dark mode */
.dark .fc .fc-button,
.dark .fc .fc-button:not(:disabled) { /* Target non-disabled buttons */
  color: rgba(255, 255, 255, 0.9) !important; /* Use light color, !important if needed */
}

/* Ensure active/primary button text is also light */
.dark .fc .fc-button-primary:not(:disabled),
.dark .fc .fc-button-primary:disabled { /* Cover disabled state too if necessary */
  color: #ffffff !important; /* Often white is better for primary buttons */
  /* Keep background color adjustments if separate */
  background-color: var(--fc-button-active-bg-color) !important;
  border-color: var(--fc-button-active-border-color) !important;
}

/* Hover states */
.dark .fc .fc-button:not(:disabled):hover {
    color: #ffffff !important;
    background-color: var(--fc-button-hover-bg-color) !important;
    border-color: var(--fc-button-hover-border-color) !important;
}

/* Apply a filter to tone down event colors in dark mode */
.dark .fc-event {
  filter: saturate(0.8) brightness(0.9);
}

/* Ensure event text within specific views is readable */
.dark .fc-daygrid-event .fc-event-title,
.dark .fc-timegrid-event .fc-event-title {
  color: #ffffff; /* Force white text on events */
}

/* Dark mode hover effect for mini calendar days */
.dark .mini-calendar-wrapper .fc-daygrid-day:hover .fc-daygrid-day-frame {
  background-color: rgba(255, 255, 255, 0.08) !important; /* Subtle dark hover */
}

/* Specific overrides for the mini calendar title - INCREASED SPECIFICITY */
/* Target the title within the specific container IDs/classes */
.dark div.mini-calendar-wrapper #mini-calendar-container .fc .fc-col-header-cell {
    font-size: 1em; /* Adjust title size if needed */
    color: rgba(255, 255, 255, 0.9) !important;
}

/* Optional: Subtle background for the filter divs in dark mode */
.dark .filter-card-wrapper {
  background-color: rgba(255, 255, 255, 0.03); /* Very subtle background */
  border-radius: 4px; /* Add slight rounding if desired */
  overflow: hidden; /* Prevent content overflow issues */
}

.dark .filter-card-wrapper h5 {
  margin-top: 0 !important;
}
.dark .filter-card-header {
  margin-top: 0 !important;
  background-color: rgba(255, 255, 255, 0.03) !important;
}

/* Styling for the new filter card header */
.dark .filter-card-header {
  padding: 12px 16px; /* Adjust padding as needed */
  border-bottom: 1px solid rgba(255, 255, 255, 0.1); /* Subtle bottom border */
}

/* Ensure header title color is correct */
.dark .filter-card-header .ant-typography {
  color: rgba(255, 255, 255, 0.9) !important;
  margin-bottom: 0 !important; /* Remove default margin if needed */
}

/* Styling for the new filter card content */
.dark .filter-card-content {
  padding: 16px; /* Adjust padding as needed */
}

/* Adjust styling for the 'Status' sub-title within the content */
.dark .filter-card-content .ant-typography {
   color: rgba(255, 255, 255, 0.85); /* Ensure readability */
}

/* Style the new status icons in dark mode (both filters and event icons) */
.dark .status-icon,
.dark .fc-event-status-icon svg { /* Target the SVG within the event icon container */
  color: rgba(255, 255, 255, 0.7) !important; /* Consistent light gray color */
  vertical-align: middle; /* Align better with text if needed */
}

/* Optionally slightly different color for specific statuses */
.dark .status-icon-confirmed,
.dark .status-icon-completed {
  /* color: #4ade80cc !important; */ /* Example: subtle green */
}
.dark .status-icon-canceled {
  /* color: #f87171cc !important; */ /* Example: subtle red */
}

/* Ensure AntD Checkbox labels are readable in dark mode if theme isn't perfect */
.dark .ant-checkbox-wrapper span:not(.ant-checkbox) {
  color: rgba(255, 255, 255, 0.85); /* Adjust as needed */
}

/* Explicitly set text color for Mini Calendar Title (targeting h2) */
.dark div.mini-calendar-wrapper #mini-calendar-container .fc-toolbar-title h2 {
    color: rgba(255, 255, 255, 0.9) !important;
    /* Reset font-size if targeting h2 directly */
    font-size: 1em !important; 
}
/* Fallback for direct title if h2 isn't present */
.dark div.mini-calendar-wrapper #mini-calendar-container .fc-toolbar-title {
    color: rgba(255, 255, 255, 0.9) !important;
}

/* Explicitly set text color for Main Calendar Title */
.dark #main-calendar-wrapper .fc-toolbar-title {
  color: rgba(255, 255, 255, 0.9) !important;
}
/* Target potential h2 inside main calendar title */
.dark #main-calendar-wrapper .fc-toolbar-title h2 {
  color: rgba(255, 255, 255, 0.9) !important;
}

/* Style Collapse Component (Light Mode) */
/* Ensure Collapse itself is transparent */
.custom-collapse {
  background-color: transparent !important;
}

.custom-collapse > .ant-collapse-item {
  background-color: #ffffff !important;
  border: 1px solid #e8e8e8 !important;
  border-radius: 4px !important;
  /* Restore margin-bottom */
  margin-bottom: 16px;
}
.custom-collapse > .ant-collapse-item:last-child {
    /* Restore rule to remove bottom margin from last item */
    margin-bottom: 0 !important;
}

.custom-collapse .ant-collapse-header {
  padding: 12px 16px !important; 
  /* background-color: #fafafa !important; /* Optional: header background */
  border-radius: 4px 4px 0 0 !important; /* Round top corners */
}

.custom-collapse .ant-collapse-content {
  border-top: 1px solid #e8e8e8 !important;
}

.custom-collapse .ant-collapse-content > .ant-collapse-content-box {
  padding: 16px !important;
}

/* Style Collapse Component (Dark Mode) */
/* Ensure Collapse itself is transparent */
.dark .custom-collapse {
  background-color: transparent !important;
}

.dark .custom-collapse > .ant-collapse-item {
  background-color: rgba(255, 255, 255, 0.03) !important; /* Use the subtle bg */
  border: 1px solid rgba(255, 255, 255, 0.1) !important; /* Use the subtle border */
  border-radius: 4px !important;
  /* Restore margin-bottom */
  margin-bottom: 16px;
}
.dark .custom-collapse > .ant-collapse-item:last-child {
    /* Restore rule to remove bottom margin from last item */
    margin-bottom: 0 !important;
}

.dark .custom-collapse .ant-collapse-header {
  padding: 12px 16px !important;
  /* background-color: rgba(255, 255, 255, 0.05) !important; /* Optional: slightly different header bg */
  border-radius: 4px 4px 0 0 !important;
  color: rgba(255, 255, 255, 0.9) !important; /* Ensure header text color */
}

/* Ensure expand icon is light */
.dark .custom-collapse .ant-collapse-expand-icon .ant-collapse-arrow {
    color: rgba(255, 255, 255, 0.7) !important;
}

.dark .custom-collapse .ant-collapse-content {
  border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
  background-color: transparent !important; /* Keep content background transparent */
  color: rgba(255, 255, 255, 0.85) !important; /* Ensure content text color */
}

.dark .custom-collapse .ant-collapse-content > .ant-collapse-content-box {
  padding: 16px !important;
} 