import React, { useState, useEffect, useMemo } from 'react';
import { useQuery, useAction } from 'wasp/client/operations';
import {
  getServices,
  getSProvidingPlaces,
  updateAppointment,
  completeAppointment,
  getQueuesByPlace,
} from 'wasp/client/operations';
import { type Service, type SProvidingPlace, type Appointment, type Queue } from 'wasp/entities';
import {
  Modal,
  Form,
  DatePicker,
  TimePicker,
  Input,
  Button,
  Spin,
  Alert,
  Select,
  Row,
  Col,
  Space,
  Popconfirm,
  message,
} from 'antd';
import { useWatch } from 'antd/es/form/Form';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { Option } from 'antd/es/mentions';

dayjs.extend(utc);
dayjs.extend(timezone);

// Define the expected shape of the appointment data passed as prop
// This should match the data structure fetched by getAppointments
export type AppointmentWithDetails = Appointment & {
  service: { id: number; title: string; duration: number; color: string | null } | null; // Ensure service has duration
  customerFolder: {
    id: number;
    customer: { id: string; firstName: string | null; lastName: string | null; mobileNumber: string | null } | null;
  } | null;
  queueId?: number | null;
};

interface EditAppointmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  appointment: AppointmentWithDetails | null; // Appointment data to edit
  onSuccess: () => void; // To refetch calendar events or update UI
}

// Ant Design Form values structure
interface EditAppointmentFormValues {
  customerDisplay: string; // Read-only display
  customerId: string; // Hidden or stored separately, needed for submission
  serviceId: number | undefined;
  placeId: number | undefined;
  queueId: number | undefined;
  appointmentDate: dayjs.Dayjs | undefined; // Separate date field
  startTime: dayjs.Dayjs | undefined; // Separate start time field
  notes?: string;
  status: string;
}

// Mapping of current status to allowed next statuses (mirrors backend)
const allowedStatusTransitions: Record<string, string[]> = {
    pending: ['pending', 'confirmed', 'completed', 'canceled', 'noshow'],
    confirmed: ['confirmed', 'completed', 'canceled', 'noshow'],
    completed: ['completed'],
    canceled: ['canceled'],
    noshow: ['noshow'],
};

const EditAppointmentModal: React.FC<EditAppointmentModalProps> = ({
  isOpen,
  onClose,
  appointment,
  onSuccess,
}) => {
  const [form] = Form.useForm<EditAppointmentFormValues>();

  // Action Hooks (just get the function)
  const updateAppointmentAction = useAction(updateAppointment);
  const completeAppointmentAction = useAction(completeAppointment);

  // Query Hooks (remain the same)
  const { data: services, isLoading: isLoadingServices, error: servicesError } = useQuery(getServices);
  const { data: places, isLoading: isLoadingPlaces, error: placesError } = useQuery(getSProvidingPlaces);

  // --- Queue Fetching Logic ---
  const watchedPlaceId = useWatch('placeId', form);
  const { data: queues, isLoading: isLoadingQueues, error: queuesError } = useQuery(
    getQueuesByPlace,
    { sProvidingPlaceId: watchedPlaceId! }, // Use watched place ID
    { enabled: !!watchedPlaceId } // Only run query if placeId is selected
  );
  // --- End Queue Fetching Logic ---

  // State
  const [isSubmitting, setIsSubmitting] = useState(false); // Central submitting state
  const [calculatedEndTime, setCalculatedEndTime] = useState<dayjs.Dayjs | null>(null);
  const [customerName, setCustomerName] = useState<string>('');
  const [customerId, setCustomerId] = useState<string>(''); // Store customer ID separately

  // Determine if the original appointment had a queue assigned
  const isOriginalQueueSet = useMemo(() => !!appointment?.queueId, [appointment]);

  // Watch relevant form fields to calculate end time
  const serviceId = useWatch('serviceId', form);
  const appointmentDate = useWatch('appointmentDate', form);
  const startTime = useWatch('startTime', form);
  const statusValue = useWatch('status', form); // <-- Watch status field

  // --- Create options for Select components ---
  const queueOptions = useMemo(() => {
    if (!queues) return [];
    // Ensure queues is treated as an array
    if (!Array.isArray(queues)) {
        console.warn('Queues data is not an array:', queues);
        return [];
    }
    return queues.map((queue: Queue) => ({
        value: queue.id,
        label: queue.title,
    }));
  }, [queues]);
  // --- End options creation ---

  // Pre-fill form when modal opens or appointment changes
  useEffect(() => {
    if (isOpen && appointment && services) { // Ensure services are loaded before setting form values
      const startDateTime = appointment.expectedAppointmentStartTime ? dayjs(appointment.expectedAppointmentStartTime) : null;
      const endDateTime = appointment.expectedAppointmentEndTime ? dayjs(appointment.expectedAppointmentEndTime) : null;
      const customer = appointment.customerFolder?.customer;
      const name = customer
        ? `${customer.firstName || ''} ${customer.lastName || ''} (${customer.mobileNumber || 'N/A'})`
        : 'Customer not found';

      setCustomerName(name);
      setCustomerId(customer?.id || ''); // Store the customer ID

      form.setFieldsValue({
        customerDisplay: name, // Set display name
        customerId: customer?.id || '', // Keep customerId in form values if needed, or just use state
        serviceId: appointment.serviceId ?? undefined,
        placeId: appointment.placeId ?? undefined,
        queueId: appointment.queueId ?? undefined,
        // Set separate date and time fields
        appointmentDate: startDateTime ? startDateTime.startOf('day') : undefined,
        startTime: startDateTime ? startDateTime : undefined, // Keep time part
        notes: appointment.notes || '',
        status: appointment.status || 'pending',
      });

      // Set initial calculated end time based on existing data or recalculate
      if (endDateTime) {
        setCalculatedEndTime(endDateTime);
      } else if (startDateTime && appointment.serviceId) {
        const selectedService = services?.find(s => s.id === appointment.serviceId);
        if (selectedService) {
          setCalculatedEndTime(startDateTime.add(selectedService.duration, 'minute'));
        } else {
          setCalculatedEndTime(null);
        }
      } else {
         setCalculatedEndTime(null);
      }

    } else if (!isOpen) {
      form.resetFields();
      setCalculatedEndTime(null);
      setIsSubmitting(false);
      setCustomerName('');
      setCustomerId('');
    }
  }, [isOpen, appointment, form, services]); // Add services dependency

  // Calculate end time effect based on Ant Design form values
  useEffect(() => {
    // Depend on appointmentDate, startTime, serviceId
    if (appointmentDate && startTime && serviceId && services) {
      const selectedService = services.find((s) => s.id === serviceId);

      // Combine date and time to get the full start moment
      // Ensure startTime is valid before accessing its properties
      if (startTime.isValid()) {
        const fullStartTime = appointmentDate
          .hour(startTime.hour())
          .minute(startTime.minute())
          .second(startTime.second());

        if (selectedService && fullStartTime && fullStartTime.isValid()) {
          const durationMinutes = selectedService.duration;
          const endTime = fullStartTime.add(durationMinutes, 'minute');
          setCalculatedEndTime(endTime);
        } else {
          setCalculatedEndTime(null);
        }
      } else {
         setCalculatedEndTime(null); // startTime is invalid
      }
    } else {
      setCalculatedEndTime(null); // One of the dependencies is missing
    }
  }, [appointmentDate, startTime, serviceId, services]); // Update dependencies

  // Unified handleFinish with manual state and feedback
  const handleFinish = async (values: EditAppointmentFormValues) => {
    if (!appointment) return;

    setIsSubmitting(true);
    try {
      // --- Execute Regular Update Appointment Action --- 
      if (!customerId) {
        message.error('Customer information is missing.');
        setIsSubmitting(false); 
        return;
      }
      if (!values.appointmentDate || !values.startTime || !values.serviceId || !values.placeId || !values.queueId || !calculatedEndTime) {
        message.error('Please ensure service, place, queue, date, and start time are set correctly.');
        setIsSubmitting(false);
        return;
      }

      const finalStartTimeLocal = values.appointmentDate!
          .hour(values.startTime!.hour())
          .minute(values.startTime!.minute())
          .second(values.startTime!.second());
          
      if (!finalStartTimeLocal || !finalStartTimeLocal.isValid() || !calculatedEndTime || !calculatedEndTime.isValid()) {
          message.error('Invalid date or time components.');
          setIsSubmitting(false);
          return;
      }

      const finalStartTime = finalStartTimeLocal.toDate();
      const finalEndTime = calculatedEndTime.toDate();

      const updateData = {
          appointmentId: appointment.id,
          customerUserId: customerId,
          serviceId: values.serviceId,
          placeId: values.placeId,
          queueId: values.queueId,
          startTime: finalStartTime,
          endTime: finalEndTime,
          notes: values.notes || null,
          status: values.status, // Send the selected status
      };

      console.log('Updating appointment data:', updateData);
      if (values.status === 'completed' && appointment.status !== 'completed') {
        await completeAppointmentAction(updateData);
      }
      await updateAppointmentAction(updateData);
      
      // Determine success message based on what was done (backend handles logic)
      if (values.status === 'canceled' && appointment.status !== 'canceled') {
          message.success('Appointment canceled and customer credits refunded!');
      } else if (values.status === 'completed' && appointment.status !== 'completed') {
          // Note: Credit refund for completion still happens via the separate completeAppointment action if you keep it.
          // If updateAppointment handles completion refund, update this message.
          message.success('Appointment marked as completed!'); // Adjust if updateAppointment refunds
      } else if (values.status !== appointment.status) {
          message.success(`Appointment status updated to ${values.status}!`);
      } else {
          message.success('Appointment details updated successfully!');
      }

      onSuccess();
      onClose();

    } catch (error: any) {
      console.error('Action failed:', error);
      message.error(`Operation failed: ${error.message || 'Unknown error'}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Calculate dynamic status options
  const availableStatusOptions = useMemo(() => {
    const currentStatus = appointment?.status || 'pending';
    return allowedStatusTransitions[currentStatus] || [currentStatus]; // Fallback to current status
  }, [appointment?.status]);

  // Use query loading states for initial data, use isSubmitting for action loading
  const isQueryLoading = isLoadingServices || isLoadingPlaces || isLoadingQueues;
  const hasError = servicesError || placesError || queuesError;

  // Popconfirm needed if changing status TO completed OR TO canceled (and not already canceled/noshow)
  const requiresConfirmation = 
      (statusValue === 'completed' && appointment?.status !== 'completed') || 
      (statusValue === 'canceled' && appointment?.status !== 'canceled' && appointment?.status !== 'noshow');
  
  const popconfirmTitle = statusValue === 'completed' 
      ? "Complete Appointment?"
      : statusValue === 'canceled'
      ? "Cancel Appointment?"
      : "Confirm Changes?"; // Default title
      
  const popconfirmDescription = statusValue === 'completed'
      ? "Completing marks it as finished. This action cannot be undone. Proceed?" // Removed mention of credits as backend handles it
      : statusValue === 'canceled'
      ? "Canceling will refund credits to the customer. This cannot be undone. Proceed?"
      : "Are you sure you want to save these changes?"; // Default description

  return (
    <Modal
      title="Edit Appointment"
      open={isOpen}
      onCancel={onClose}
      footer={null} // Use custom footer buttons inside the Form
      destroyOnClose // Reset form state when modal is closed
      width={700} // Adjust width as needed
    >
      <Spin spinning={isQueryLoading || isSubmitting}>
        {hasError && (
          <Alert
            message="Error loading data"
            description={servicesError?.message || placesError?.message || queuesError?.message || 'Could not load necessary data.'}
            type="error"
            showIcon
            className='mb-4'
          />
        )}
        {!hasError && appointment && (
          <Form
            form={form}
            layout="vertical"
            onFinish={handleFinish}
            initialValues={{ status: appointment.status || 'pending' }} // Use actual initial status
          >
            {/* Hidden field to store customerId if needed by form logic, otherwise use state */}
            <Form.Item name="customerId" hidden>
              <Input />
            </Form.Item>

            {/* Customer Display (Read-only) */}
            <Form.Item label="Customer" name="customerDisplay">
              <Input readOnly placeholder="Customer details" />
            </Form.Item>

            <Row gutter={16}>
              <Col xs={24} sm={12}>
                {/* Place Selection */}
                <Form.Item
                  label="Place"
                  name="placeId"
                  rules={[{ required: true, message: 'Please select a place' }]}
                >
                  <Select<number> placeholder="Select Place" loading={isLoadingPlaces} allowClear>
                    {places?.map((place) => (
                      <Option key={place.id} value={place.id}>
                        {place.name}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} sm={12}>
                {/* Queue Selection */}
                <Form.Item
                  label="Queue"
                  name="queueId"
                  rules={isOriginalQueueSet ? [] : [{ required: true, message: 'Please select a queue' }]}
                >
                  <Select<number>
                    placeholder="Select Queue"
                    loading={isLoadingQueues}
                    disabled={isOriginalQueueSet || !watchedPlaceId || isLoadingQueues}
                    allowClear={!isOriginalQueueSet}
                    options={queueOptions}
                  >
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col xs={24} sm={12}>
                {/* Service Selection */}
                <Form.Item
                  label="Service"
                  name="serviceId"
                  rules={[{ required: true, message: 'Please select a service' }]}
                >
                  <Select<number> placeholder="Select Service" loading={isLoadingServices} allowClear>
                    {services?.map((service) => (
                      <Option key={service.id} value={service.id}>
                        {service.title} ({service.duration} min)
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} sm={12}>
                {/* Status Selection */}
                <Form.Item
                  label="Status"
                  name="status"
                  rules={[{ required: true, message: 'Please select a status' }]}
                >
                  <Select<string> placeholder="Select Status">
                    {/* Map over dynamically calculated options */}
                    {availableStatusOptions.map(status => (
                      <Option key={status} value={status}>
                        {status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ')}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col xs={24} sm={8}>
                {/* Date Selection */}
                <Form.Item
                  label="Date"
                  name="appointmentDate"
                  rules={[{ required: true, message: 'Please select a date' }]}
                >
                  <DatePicker format="YYYY-MM-DD" style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col xs={24} sm={8}>
                {/* Start Time Selection */}
                <Form.Item
                  label="Start Time"
                  name="startTime"
                  rules={[{ required: true, message: 'Please select a start time' }]}
                >
                  <TimePicker
                    format="HH:mm"
                    minuteStep={15}
                    style={{ width: '100%' }}
                    // Optionally disable past times or link to opening hours
                  />
                </Form.Item>
              </Col>
              <Col xs={24} sm={8}>
                {/* Calculated End Time Display */}
                <Form.Item label="End Time (Calculated)">
                  <TimePicker
                    value={calculatedEndTime}
                    format="HH:mm"
                    style={{ width: '100%' }}
                    disabled // Display only
                    minuteStep={15} // Match step for consistency
                  />
                </Form.Item>
              </Col>
            </Row>

            {/* Notes */}
            <Form.Item label="Notes (Optional)" name="notes">
              <Input.TextArea rows={3} placeholder="Add any relevant notes here" />
            </Form.Item>

            {/* Modal Footer Buttons with Conditional Popconfirm */}
            <div style={{ textAlign: 'right', marginTop: '24px' }}>
              <Space>
                <Button onClick={onClose} disabled={isSubmitting}>
                  Cancel
                </Button>
                {requiresConfirmation ? (
                  <Popconfirm
                    title={popconfirmTitle}
                    description={popconfirmDescription}
                    onConfirm={() => form.submit()} // Submit form on confirm
                    okText="Yes, Proceed"
                    cancelText="No"
                  >
                    <Button type="primary" loading={isSubmitting} >
                      Save Changes
                    </Button>
                  </Popconfirm>
                ) : (
                  <Button type="primary" htmlType="submit" loading={isSubmitting}>
                    Save Changes
                  </Button>
                )}
              </Space>
            </div>
          </Form>
        )}
      </Spin>
    </Modal>
  );
};

export default EditAppointmentModal;