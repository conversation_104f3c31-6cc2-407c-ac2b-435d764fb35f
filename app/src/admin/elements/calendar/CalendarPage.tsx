import React, { useEffect, useRef, useState, useMemo } from 'react';
import { type AuthUser } from 'wasp/auth';
import Breadcrumb from '../../layout/Breadcrumb';
import DefaultLayout from '../../layout/DefaultLayout';
import { useRedirectHomeUnlessUserIsAdmin } from '../../useRedirectHomeUnlessUserIsAdmin';
import { useQuery } from 'wasp/client/operations';
import { getServices, getAppointments } from 'wasp/client/operations';
import { type Service, type Appointment, type User, type CustomerFolder, type SProvidingPlace } from 'wasp/entities';
import NewAppointmentModal from './NewAppointmentModal';
import EditAppointmentModal, { AppointmentWithDetails } from './EditAppointmentModal';
// Import ReactDOMServer to render icons to string for FullCalendar
import ReactDOMServer from 'react-dom/server';

// Import FullCalendar Core and plugins
import { Calendar, DateSelectArg, EventClickArg, EventApi, EventInput } from '@fullcalendar/core';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin, { DateClickArg } from '@fullcalendar/interaction';
import LoadingSpinner from '../../layout/LoadingSpinner';

// Import the custom CSS file
import './CalendarPage.css';

// Import Ant Design components & Icons
import { Checkbox, List, Typography, Space, Divider, TimePicker, Select, Collapse } from 'antd'; 
import { 
    ClockCircleOutlined, 
    CheckCircleOutlined, 
    CheckSquareOutlined, // Using CheckSquare for Completed 
    CloseCircleOutlined 
} from '@ant-design/icons';
import dayjs from 'dayjs'; // Import dayjs for TimePicker

const { Title, Text } = Typography;

// Define possible appointment statuses for filtering
const APPOINTMENT_STATUSES = ['pending', 'confirmed', 'completed', 'canceled']; 

// Type for the raw data potentially returned by the query *before* select
// (includes might be typed as optional or 'any' by default inference)
// We define this to explicitly type the 'data' param in 'select'
type RawAppointmentData = Appointment & {
  service?: Service | null; // Make includes optional for the raw type
  customerFolder?: (CustomerFolder & { customer?: User | null }) | null; // Nested include
  place?: SProvidingPlace | null; // Include place as well based on backend query
}

// Keep FetchedAppointment as the desired *output* type after select
type FetchedAppointment = Appointment & {
  service: { title: string, color: string | null } | null;
  customerFolder: {
    id: number;
    customer: { id: string; firstName: string | null; lastName: string | null; mobileNumber: string | null; } | null;
  } | null;
  // We can add place here if needed by the EditModal later
  place: SProvidingPlace | null; // Add place to FetchedAppointment
};

// Helper function to get a status icon component
const getStatusIcon = (status: string | undefined | null): React.ReactElement | null => {
  switch (status) {
    case 'pending': return <ClockCircleOutlined className="status-icon status-icon-pending" />; 
    case 'confirmed': return <CheckCircleOutlined className="status-icon status-icon-confirmed" />;
    case 'completed': return <CheckSquareOutlined className="status-icon status-icon-completed" />;
    case 'canceled': return <CloseCircleOutlined className="status-icon status-icon-canceled" />;
    default: return null; 
  }
};

// Format function stores status icon *component* in extendedProps
const formatAppointmentsForCalendar = (appointments: FetchedAppointment[] | undefined | null): EventInput[] => {
  if (!Array.isArray(appointments)) return [];
  return appointments.filter(app => app.status !== 'canceled' && app.status !== 'noShow').map(app => {
    // Use dayjs to format the start time in the local timezone
    const localStartTime = app.expectedAppointmentStartTime 
      ? dayjs(app.expectedAppointmentStartTime).format('HH:mm') 
      : ''; // Handle null case
    // Optionally format end time too if needed in title
    // const localEndTime = app.expectedAppointmentEndTime 
    //   ? dayjs(app.expectedAppointmentEndTime).format('HH:mm') 
    //   : '';

    // --- DEBUG LOG ---
    console.log(`[formatAppointments] App ID: ${app.id}, Original Start: ${app.expectedAppointmentStartTime?.toISOString()}, Formatted Local: ${localStartTime}`);
    // --- END DEBUG LOG ---

    const eventTitle = `${localStartTime} ${app.customerFolder?.customer?.firstName || ''} ${app.customerFolder?.customer?.lastName || ''} - ${app.service?.title || 'Appointment'}`.trim();

    // --- DEBUG LOG ---
    console.log(`[formatAppointments] Generated Title: ${eventTitle}`);
    // --- END DEBUG LOG ---

    return {
      id: app.id.toString(),
      // Prepend the formatted local start time to the title
      title: eventTitle, 
      start: app.expectedAppointmentStartTime || undefined,
      end: app.expectedAppointmentEndTime || undefined,
      backgroundColor: app.service?.color || '#3788d8',
      borderColor: app.service?.color || '#3788d8',
      extendedProps: {
        appointmentData: app,
        statusIconComponent: getStatusIcon(app.status) // Store React component here
      }
    }
  });
};

// --- Helper Functions for GCD --- 
function gcd(a: number, b: number): number {
  if (b === 0) {
    return a;
  }
  return gcd(b, a % b);
}

function calculateGcdOfList(numbers: number[]): number {
  if (!numbers || numbers.length === 0) {
    return 0; // Indicate no valid GCD
  }
  // Filter out non-positive numbers as GCD is typically defined for positive integers
  const positiveNumbers = numbers.filter(n => n > 0);
  if (positiveNumbers.length === 0) {
      return 0;
  }

  let result = positiveNumbers[0];
  for (let i = 1; i < positiveNumbers.length; i++) {
    result = gcd(result, positiveNumbers[i]);
    // Optimization: if GCD becomes 1, it will remain 1
    if (result === 1) {
      return 1;
    }
  }
  return result;
}

// --- Default Interval Options --- 
const DEFAULT_INTERVAL_OPTIONS = [
  { value: '00:05:00', label: '5 minutes' },
  { value: '00:10:00', label: '10 minutes' },
  { value: '00:15:00', label: '15 minutes' },
  { value: '00:20:00', label: '20 minutes' },
  { value: '00:25:00', label: '25 minutes' },
  { value: '00:30:00', label: '30 minutes' },
  { value: '01:00:00', label: '60 minutes' },
];

// --- Format Minutes to HH:mm:ss --- 
function formatMinutesToHHMMSS(minutes: number): string {
    const h = Math.floor(minutes / 60);
    const m = minutes % 60;
    const hh = String(h).padStart(2, '0');
    const mm = String(m).padStart(2, '0');
    return `${hh}:${mm}:00`;
}

const CalendarPage = ({ user }: { user: AuthUser }) => {
  useRedirectHomeUnlessUserIsAdmin({ user });
  const mainCalendarRef = useRef<HTMLDivElement>(null);
  const miniCalendarRef = useRef<HTMLDivElement>(null);
  const mainCalendarInstance = useRef<Calendar | null>(null); 

  const { data: services, isLoading: isLoadingServices } = useQuery(getServices);
  const { 
      data: appointmentsData, // This will now be FetchedAppointment[]
      isLoading: isLoadingAppointments,
      error: appointmentsError,
      refetch: refetchAppointments
  } = useQuery(getAppointments, undefined, {
      // Explicitly type 'data' and perform mapping/validation in select
      select: (data: RawAppointmentData[] | undefined): FetchedAppointment[] => {
          if (!data) return [];
          // Ensure the data conforms to FetchedAppointment, handling potential missing includes
          return data.map(app => ({
              ...app,
              // Explicitly construct the nested objects, providing null defaults
              service: app.service ? { title: app.service.title, color: app.service.color } : null,
              customerFolder: app.customerFolder ? {
                  id: app.customerFolder.id,
                  customer: app.customerFolder.customer ? {
                      id: app.customerFolder.customer.id,
                      firstName: app.customerFolder.customer.firstName,
                      lastName: app.customerFolder.customer.lastName,
                      mobileNumber: app.customerFolder.customer.mobileNumber,
                  } : null,
              } : null,
              place: app.place || null, // Map place
          })) as FetchedAppointment[]; // Assert the final type
      }
  });

  // --- State for Modal --- 
  const [isNewModalOpen, setIsNewModalOpen] = useState(false);
  const [selectedStartDate, setSelectedStartDate] = useState<Date | null>(null);
  const [selectedEndDate, setSelectedEndDate] = useState<Date | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [appointmentToEdit, setAppointmentToEdit] = useState<FetchedAppointment | null>(null);
  const [selectedServiceIds, setSelectedServiceIds] = useState<number[]>([]); // Use number[] for Checkbox.Group value
  const [selectedStatusFilters, setSelectedStatusFilters] = useState<string[]>([]); // Use string[] for Checkbox.Group value

  // --- State for Calendar View Customization --- 
  const [minTime, setMinTime] = useState('08:00:00');
  const [maxTime, setMaxTime] = useState('19:00:00');
  const [slotInterval, setSlotInterval] = useState('00:30:00'); // Default 30 mins

  // --- Calculate Dynamic Slot Interval Options --- 
  const slotIntervalOptions = useMemo(() => {
    // if (!services || services.length === 0) {
    //     console.log("No services, using default intervals");
    //     return DEFAULT_INTERVAL_OPTIONS;
    // }

    // const validDurations = services
    //     .map(s => s.duration)
    //     .filter((d): d is number => typeof d === 'number' && d > 0 && Number.isInteger(d));

    // if (validDurations.length === 0) {
    //     console.log("No valid service durations, using default intervals");
    //     return DEFAULT_INTERVAL_OPTIONS;
    // }

    // const finalGcd = calculateGcdOfList(validDurations);
    // console.log("Calculated GCD of durations:", finalGcd);

    // if (finalGcd <= 0) {
    //     console.log("Invalid GCD, using default intervals");
    //     return DEFAULT_INTERVAL_OPTIONS;
    // }

    // // Define potential intervals in minutes we might want to offer
    // const potentialIntervalsMinutes = [15, 30, 45, 60]; 
    
    // const allowedIntervalsMinutes = potentialIntervalsMinutes.filter(interval => 
    //     interval >= finalGcd && interval % finalGcd === 0
    // );

    // // If filtering results in no options (e.g., GCD > 60), fall back
    // if (allowedIntervalsMinutes.length === 0) {
    //     console.log("No standard intervals possible with GCD, using default intervals");
    //     // Alternative: could offer just the GCD if it's reasonable, e.g., <= 60?
    //     // if (finalGcd <= 60) { return [{ value: formatMinutesToHHMMSS(finalGcd), label: `${finalGcd} minutes` }] }
    //     return DEFAULT_INTERVAL_OPTIONS;
    // }

    // const options = allowedIntervalsMinutes.map(minutes => ({
    //     value: formatMinutesToHHMMSS(minutes),
    //     label: `${minutes} minutes`,
    // }));
    // console.log("Generated dynamic intervals:", options);
    return DEFAULT_INTERVAL_OPTIONS;

  }, [services]); // Recalculate when services data changes

  // --- Effect to validate/reset slotInterval when options change --- 
  useEffect(() => {
      const currentSelectionIsValid = slotIntervalOptions.some(option => option.value === slotInterval);
      if (!currentSelectionIsValid && slotIntervalOptions.length > 0) {
          console.log(`Current interval ${slotInterval} invalid, resetting to ${slotIntervalOptions[0].value}`);
          setSlotInterval(slotIntervalOptions[0].value); // Reset to the first available option
      }
  }, [slotIntervalOptions, slotInterval]); // Run when options or selection change

  // --- Modal Handlers ---
  const openNewModal = () => setIsNewModalOpen(true);
  const closeNewModal = () => {
    setIsNewModalOpen(false);
    setSelectedStartDate(null);
    setSelectedEndDate(null);
    mainCalendarInstance.current?.unselect();
  };
  const openEditModal = (appointment: FetchedAppointment) => {
      setAppointmentToEdit(appointment);
      setIsEditModalOpen(true);
  };
  const closeEditModal = () => {
      setIsEditModalOpen(false);
      setAppointmentToEdit(null);
  };
  const handleAppointmentSuccess = () => {
    console.log("Appointment created/updated - refetching events...");
    refetchAppointments();
  };

  // Update handler for Ant Design Checkbox.Group (Services)
  const handleServiceFilterChange = (checkedValues: number[]) => {
    setSelectedServiceIds(checkedValues);
  };

  // Update handler for Ant Design Checkbox.Group (Status)
  const handleStatusFilterChange = (checkedValues: string[]) => {
    setSelectedStatusFilters(checkedValues);
  };

  const handleMinTimeChange = (time: dayjs.Dayjs | null, timeString: string) => {
      if (timeString) {
        setMinTime(`${timeString}:00`); // Append seconds
      }
  };

  const handleMaxTimeChange = (time: dayjs.Dayjs | null, timeString: string) => {
      if (timeString) {
          setMaxTime(`${timeString}:00`); // Append seconds
      }
  };

  const handleSlotIntervalChange = (value: string) => {
      setSlotInterval(value);
  };

  const isLoading = isLoadingServices || isLoadingAppointments;

  // --- Effects --- 

  // Initialize Calendars Effect
  useEffect(() => {
    let miniCalendarInstance: Calendar | null = null;

    if (mainCalendarRef.current && !mainCalendarInstance.current) { 
      mainCalendarInstance.current = new Calendar(mainCalendarRef.current, {
        plugins: [dayGridPlugin, timeGridPlugin, interactionPlugin],
        initialView: 'timeGridWeek',
        headerToolbar: {
          left: 'prev,next today',
          center: 'title',
          right: 'dayGridMonth,timeGridWeek,timeGridDay'
        },
        events: [],
        editable: true,
        selectable: true, 
        selectMirror: true,
        dayMaxEvents: true, 
        allDaySlot: false, 
        // Use state for these options
        slotMinTime: minTime,
        slotMaxTime: maxTime,
        slotDuration: slotInterval,
        // Try duration string format for slotLabelInterval
        slotLabelInterval: '01:00:00', 
        // Add height: 'auto' to prevent stretching
        height: 'auto', 

        // --- Custom Event Rendering with Icon SVG --- 
        eventContent: (arg) => {
          // const iconComponent = arg.event.extendedProps.statusIconComponent as React.ReactElement | null;
          // // Render icon component to SVG string, handle potential errors
          // let iconHtml = '';
          // if (iconComponent) {
          //     try {
          //         const svgString = ReactDOMServer.renderToStaticMarkup(iconComponent);
          //         // Style the container for the SVG
          //         iconHtml = `<div class="fc-event-status-icon" style="position: absolute; top: 2px; right: 2px; width: 1em; height: 1em; line-height: 1; z-index: 90;">${svgString}</div>`;
          //     } catch (error) {
          //         console.error("Error rendering status icon to SVG:", error);
          //         // Fallback or leave empty
          //     }
          // }

          // // Standard event time and title rendering provided by FullCalendar
          // const timeHtml = arg.timeText ? `<div class="fc-event-time">${arg.timeText}</div>` : '';
          // const titleHtml = `<div class="fc-event-title">${arg.event.title}</div>`;

          // // Wrap everything in a relative container to position the icon
          // return { html: `<div style="position: relative; width: 100%; height: 100%; overflow: hidden;">${timeHtml}${titleHtml}${iconHtml}</div>` }; // Added overflow: hidden
        
          // --- TEMPORARY SIMPLIFICATION FOR DEBUGGING ---
          console.log('[eventContent] Rendering title:', arg.event.title); // Log title being used
          return { html: `<div class='fc-event-title'>${arg.event.title}</div>` }; 
          // --- END TEMPORARY SIMPLIFICATION ---
        },

        select: (selectInfo: DateSelectArg) => {
          console.log('Selected start:', selectInfo.start);
          console.log('Selected end:', selectInfo.end);
          setSelectedStartDate(selectInfo.start);
          setSelectedEndDate(selectInfo.end);
          openNewModal();
        },

        eventClick: (clickInfo: EventClickArg) => {
          console.log('Event clicked:', clickInfo.event.title, clickInfo.event.extendedProps);
          const appointmentData = clickInfo.event.extendedProps.appointmentData as FetchedAppointment;
          if (appointmentData) {
              openEditModal(appointmentData);
          } else {
              console.error("Appointment data not found in event's extendedProps");
          }
        },
      });
      mainCalendarInstance.current.render();
    }

    if (miniCalendarRef.current) {
      miniCalendarInstance = new Calendar(miniCalendarRef.current, {
        plugins: [dayGridPlugin, interactionPlugin],
        initialView: 'dayGridMonth',
        headerToolbar: {
          left: 'prev',
          center: 'title',
          right: 'next'
        },
        aspectRatio: 1,
        dayCellContent: (arg) => arg.dayNumberText.replace(/^0+/, ''),
        dateClick: (clickInfo: DateClickArg) => {
          if (mainCalendarInstance.current) {
            mainCalendarInstance.current.gotoDate(clickInfo.dateStr);
          }
        },
      });
      miniCalendarInstance.render();
    }

    return () => {
      miniCalendarInstance?.destroy();
    };
  }, []); // Runs once on mount

  // Effect to dynamically update calendar options when state changes
  useEffect(() => {
      if (mainCalendarInstance.current) {
          mainCalendarInstance.current.setOption('slotMinTime', minTime);
          mainCalendarInstance.current.setOption('slotMaxTime', maxTime);
          mainCalendarInstance.current.setOption('slotDuration', slotInterval);
      }
  }, [minTime, maxTime, slotInterval]); // Re-run when these state variables change

  // Update Calendar Events Effect
  useEffect(() => {
    const calendar = mainCalendarInstance.current;
    if (calendar && appointmentsData) { 
        let filteredAppointments = appointmentsData; 

        // Apply service filter if any services are selected
        if (selectedServiceIds.length > 0) { // Check length instead of size
            filteredAppointments = filteredAppointments?.filter(app =>
                app.serviceId && selectedServiceIds.includes(app.serviceId) // Use includes with array
            );
        }

        // Apply status filter if any statuses are selected
        if (selectedStatusFilters.length > 0) { // Check length instead of size
            filteredAppointments = filteredAppointments?.filter(app =>
                app.status && selectedStatusFilters.includes(app.status) // Use includes with array
            );
        }

        const formattedEvents = formatAppointmentsForCalendar(filteredAppointments as FetchedAppointment[] | undefined);
        // --- DEBUG LOG ---
        console.log(`Updating calendar with ${formattedEvents.length} events. First event title (if any):`, formattedEvents[0]?.title);
        // --- END DEBUG LOG ---
        console.log(`Updating calendar with ${formattedEvents.length} events (Service filter: ${selectedServiceIds.length > 0}, Status filter: ${selectedStatusFilters.length > 0})`);
        calendar.setOption('events', formattedEvents);
    } else if (calendar) {
        calendar.setOption('events', []);
    }
  }, [appointmentsData, selectedServiceIds, selectedStatusFilters]);

  // Effect to fix Mini Calendar Title color in Dark Mode after render
  useEffect(() => {
    // Only run if not loading and dark mode is potentially active
    // We check for the .dark class on the body or html element
    const isDarkMode = document.documentElement.classList.contains('dark') || document.body.classList.contains('dark');

    if (!isLoading && isDarkMode) {
        // Give the DOM a brief moment to settle after loading finishes
        const timer = setTimeout(() => {
            const miniTitleElement = document.querySelector('#mini-calendar-container .fc-toolbar-title h2'); // Target potential h2
            if (miniTitleElement) {
                (miniTitleElement as HTMLElement).style.color = 'rgba(255, 255, 255, 0.9)';
            } else {
                // Fallback to targeting the container if h2 not found
                const miniTitleContainer = document.querySelector('#mini-calendar-container .fc-toolbar-title');
                if (miniTitleContainer) {
                    (miniTitleContainer as HTMLElement).style.color = 'rgba(255, 255, 255, 0.9)';
                }
            }
        }, 100); // 100ms delay, adjust if needed

        return () => clearTimeout(timer); // Cleanup timer on unmount or re-run
    }
  }, [isLoading]); // Re-run when loading state changes

  // --- Define Collapse Items --- 
  const collapseItems = useMemo(() => [
      // Calendar Config Panel
      {
        key: '1',
        label: 'Calendar Config',
        children: (
          <Space direction="vertical" style={{ width: '100%' }} size="middle">
              <div>
                   <Text>Start Time:</Text>
                   <TimePicker value={dayjs(minTime, 'HH:mm:ss')} onChange={handleMinTimeChange} format="HH:mm" minuteStep={15} style={{ width: '100%' }}/>
              </div>
              <div>
                   <Text>End Time:</Text>
                   <TimePicker value={dayjs(maxTime, 'HH:mm:ss')} onChange={handleMaxTimeChange} format="HH:mm" minuteStep={15} style={{ width: '100%' }}/>
               </div>
               <div>
                   <Text>Time Slot Interval:</Text>
                   <Select 
                       value={slotInterval} 
                       onChange={handleSlotIntervalChange} 
                       style={{ width: '100%' }}
                       options={slotIntervalOptions} // Use dynamic options
                       disabled={slotIntervalOptions.length <= 1} // Disable if only one option
                   />
               </div>
          </Space>
        )
      },
      // Services Panel
      {
        key: '2',
        label: 'Services',
        children: (
          <> { /* Use Fragment or div if needed */ }
              {isLoadingServices ? (
                  <Text>Loading services...</Text>
              ) : (
                  <Checkbox.Group style={{ width: '100%' }} onChange={handleServiceFilterChange} value={selectedServiceIds}>
                      <Space direction="vertical" style={{ width: '100%' }}>
                          {services?.map((service: Service) => (
                              <Checkbox key={service.id} value={service.id}>
                                  <span className="inline-block w-3 h-3 rounded-full" style={{ backgroundColor: service.color || '#888', verticalAlign: 'middle' }}></span>
                                  <span style={{ marginLeft: '8px', verticalAlign: 'middle' }}>{service.title}</span>
                              </Checkbox>
                          ))}
                          {(!services || services.length === 0) && <Text type="secondary">No services found.</Text>}
                      </Space>
                  </Checkbox.Group>
              )}
          </>
        )
      },
      // Status Panel (renamed from Other Filters)
      {
        key: '3',
        label: 'Status',
        children: (
          <>
              <Checkbox.Group style={{ width: '100%' }} onChange={handleStatusFilterChange} value={selectedStatusFilters}>
                  <Space direction="vertical" style={{ width: '100%' }}>
                      {APPOINTMENT_STATUSES.map(status => (
                          <Checkbox key={status} value={status} style={{ textTransform: 'capitalize' }}>
                              {getStatusIcon(status)} 
                              <span style={{ marginLeft: '12px' }}>{status}</span>
                          </Checkbox>
                      ))}
                  </Space>
              </Checkbox.Group>
          </>
        )
      }
  ], [services, isLoadingServices, selectedServiceIds, selectedStatusFilters, minTime, maxTime, slotInterval, slotIntervalOptions, handleMinTimeChange, handleMaxTimeChange, handleSlotIntervalChange, handleServiceFilterChange, handleStatusFilterChange]); // Add dependencies for useMemo

  return (
    <DefaultLayout user={user}>
      <div className='mx-auto max-w-[1600px] p-4 md:p-6 xl:p-7.5'> 
        <Breadcrumb pageName='Calendar' />

        {isLoading && <div className="p-4"><LoadingSpinner /></div>}
        {appointmentsError && (
            <div className="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400" role="alert">
                <span className="font-medium">Error!</span> Failed to load appointments: {appointmentsError.message}
            </div>
        )}

        <div className="grid grid-cols-1 gap-4 md:grid-cols-4 md:gap-6 xl:grid-cols-5 xl:gap-7.5"> 
             <div className="col-span-1 flex flex-col gap-4 md:gap-6">
                 {/* Mini Calendar Container - Remove explicit border/bg/shadow, add padding */}
                <div className="mini-calendar-wrapper"> {/* Removed border/bg/shadow classes */} 
                     <div ref={miniCalendarRef} id="mini-calendar-container"></div>
                 </div>
                {/* <Divider className="my-0 dark:border-strokedark" /> REMOVE Dividers */}

                 {/* Use Collapse with calculated items */} 
                 <Collapse 
                     items={collapseItems} 
                     bordered={false} 
                     defaultActiveKey={['1', '2', '3']} 
                     className="custom-collapse" 
                 />
            </div>

             {/* Main Calendar Section - Remove explicit border/bg/shadow */} 
            <div className="md:col-span-3 xl:col-span-4">
                 {/* Removed container div styling, keep padding if needed */} 
                 <div className='p-0' id="main-calendar-wrapper"> 
                     <div ref={mainCalendarRef}></div>
                 </div>
            </div>
        </div>
      </div>

      {/* Render the New Appointment Modal */}
      <NewAppointmentModal 
        isOpen={isNewModalOpen}
        onClose={closeNewModal}
        startDate={selectedStartDate}
        endDate={selectedEndDate}
        onSuccess={handleAppointmentSuccess}
      />
      {/* Render the Edit Appointment Modal */}
      <EditAppointmentModal 
          isOpen={isEditModalOpen}
          onClose={closeEditModal}
          appointment={appointmentToEdit as AppointmentWithDetails | null}
          onSuccess={handleAppointmentSuccess}
       />
    </DefaultLayout>
  );
};

export default CalendarPage;
