import React, { useState, useEffect, useMemo } from 'react';
import { type AuthUser } from 'wasp/auth';
import { useRedirectHomeUnlessUserIsAdmin } from '../../useRedirectHomeUnlessUserIsAdmin';
import DefaultLayout from '../../layout/DefaultLayout';
import Breadcrumb from '../../layout/Breadcrumb';

// Import necessary Wasp queries/actions
import {
  useAction,
  createAdvertisement,
  updateAdvertisement,
  deleteAdvertisement,
  useQuery,
  getAdvertisements,
  createFile
} from 'wasp/client/operations';
import { type Advertisement } from 'wasp/entities';

// Import Ant Design components
import Button from 'antd/es/button';
import Input from 'antd/es/input';
import Form from 'antd/es/form';
import Spin from 'antd/es/spin';
import Alert from 'antd/es/alert';
import message from 'antd/es/message';
import Modal from 'antd/es/modal';
import Table from 'antd/es/table';
import Typography from 'antd/es/typography';
import Switch from 'antd/es/switch';
import Tag from 'antd/es/tag';
import Space from 'antd/es/space';
import Upload from 'antd/es/upload';
import Image from 'antd/es/image';

import {
  DeleteOutlined,
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  EyeOutlined,
  LinkOutlined,
  UploadOutlined,
  InboxOutlined
} from '@ant-design/icons';

const { Title } = Typography;
const { confirm } = Modal;

const AdminAdvertisementsPage = ({ user }: { user: AuthUser }) => {
  useRedirectHomeUnlessUserIsAdmin({ user });
  const [addForm] = Form.useForm();
  const [editForm] = Form.useForm();

  // --- State ---
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingAdvertisement, setEditingAdvertisement] = useState<Advertisement | null>(null);
  const [filterText, setFilterText] = useState('');
  const [filterActive, setFilterActive] = useState<boolean | undefined>(undefined);
  const [uploadingBackground, setUploadingBackground] = useState(false);
  const [uploadingPng, setUploadingPng] = useState(false);
  const [backgroundImageId, setBackgroundImageId] = useState<string | null>(null);
  const [pngImageId, setPngImageId] = useState<string | null>(null);

  // --- Actions ---
  const createAdvertisementAction = useAction(createAdvertisement);
  const updateAdvertisementAction = useAction(updateAdvertisement);
  const deleteAdvertisementAction = useAction(deleteAdvertisement);
  const createFileAction = useAction(createFile);

  // --- Queries ---
  const {
    data: advertisementsData,
    isLoading: isLoadingAdvertisements,
    error: advertisementsError,
    refetch: refetchAdvertisements
  } = useQuery(getAdvertisements, { 
    page: 1, 
    limit: 50,
    ...(filterActive !== undefined && { isActive: filterActive })
  });

  const advertisements = advertisementsData?.advertisements || [];

  // --- Filtered Data ---
  const filteredAdvertisements = useMemo(() => {
    if (!advertisements) return [];
    if (!filterText) return advertisements;
    return advertisements.filter(ad =>
      ad.title.toLowerCase().includes(filterText.toLowerCase()) ||
      ad.subtitle?.toLowerCase().includes(filterText.toLowerCase()) ||
      ad.description?.toLowerCase().includes(filterText.toLowerCase())
    );
  }, [advertisements, filterText]);

  // --- Handlers ---
  const openAddModal = () => setIsAddModalOpen(true);
  const closeAddModal = () => {
    setIsAddModalOpen(false);
    addForm.resetFields();
    setBackgroundImageId(null);
    setPngImageId(null);
  };

  const openEditModal = (advertisement: Advertisement) => {
    setEditingAdvertisement(advertisement);
    editForm.setFieldsValue({
      title: advertisement.title,
      subtitle: advertisement.subtitle,
      description: advertisement.description,
      callToActionText: advertisement.callToActionText,
      callToActionLink: advertisement.callToActionLink,
      isExternal: advertisement.isExternal,
      isActive: advertisement.isActive,
      sortOrder: advertisement.sortOrder,
    });
    setBackgroundImageId(advertisement.backgroundImageId || null);
    setPngImageId(advertisement.pngImageId || null);
    setIsEditModalOpen(true);
  };

  const closeEditModal = () => {
    setIsEditModalOpen(false);
    setEditingAdvertisement(null);
    editForm.resetFields();
    setBackgroundImageId(null);
    setPngImageId(null);
  };

  const handleCreateAdvertisementSubmit = async (values: any) => {
    setIsSubmitting(true);
    try {
      const submitData = {
        ...values,
        backgroundImageId: backgroundImageId,
        pngImageId: pngImageId,
      };
      await createAdvertisementAction(submitData);
      message.success('Advertisement created successfully!');
      closeAddModal();
      refetchAdvertisements();
    } catch (error: any) {
      message.error(`Failed to create advertisement: ${error.message || 'Unknown error'}`);
      console.error(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUpdateAdvertisementSubmit = async (values: any) => {
    if (!editingAdvertisement) return;

    setIsSubmitting(true);
    try {
      const submitData = {
        id: editingAdvertisement.id,
        ...values,
        backgroundImageId: backgroundImageId,
        pngImageId: pngImageId,
      };
      await updateAdvertisementAction(submitData);
      message.success('Advertisement updated successfully!');
      closeEditModal();
      refetchAdvertisements();
    } catch (error: any) {
      message.error(`Failed to update advertisement: ${error.message || 'Unknown error'}`);
      console.error(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const showDeleteConfirm = (advertisement: Advertisement) => {
    confirm({
      title: 'Confirm Deletion',
      content: `Are you sure you want to delete the advertisement "${advertisement.title}"? This action cannot be undone.`,
      okText: 'Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: async () => {
        setIsSubmitting(true);
        try {
          await deleteAdvertisementAction({ id: advertisement.id });
          message.success(`Advertisement "${advertisement.title}" deleted successfully!`);
          refetchAdvertisements();
        } catch (error: any) {
          message.error(`Failed to delete advertisement: ${error.message || 'Unknown error'}`);
          console.error(error);
        } finally {
          setIsSubmitting(false);
        }
      },
      confirmLoading: isSubmitting,
    });
  };

  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilterText(e.target.value);
  };

  const handleActiveFilterChange = (value: boolean | undefined) => {
    setFilterActive(value);
  };

  // Image upload handlers
  const handleBackgroundImageUpload = async (file: File) => {
    setUploadingBackground(true);
    try {
      const result = await createFileAction({
        fileType: file.type as 'image/jpeg' | 'image/png',
        fileName: file.name,
      });

      // Upload the file to S3
      const formData = new FormData();
      Object.entries(result.s3UploadFields).forEach(([key, value]) => {
        formData.append(key, value);
      });
      formData.append('file', file);

      const uploadResponse = await fetch(result.s3UploadUrl, {
        method: 'POST',
        body: formData,
      });

      if (uploadResponse.ok) {
        // Extract file ID from the upload fields
        const fileId = result.s3UploadFields.key.split('/').pop()?.split('.')[0];
        if (fileId) {
          setBackgroundImageId(fileId);
          message.success('Background image uploaded successfully!');
        }
      } else {
        throw new Error('Upload failed');
      }
    } catch (error: any) {
      message.error(`Failed to upload background image: ${error.message || 'Unknown error'}`);
      console.error(error);
    } finally {
      setUploadingBackground(false);
    }
    return false; // Prevent default upload behavior
  };

  const handlePngImageUpload = async (file: File) => {
    setUploadingPng(true);
    try {
      const result = await createFileAction({
        fileType: file.type as 'image/jpeg' | 'image/png',
        fileName: file.name,
      });

      // Upload the file to S3
      const formData = new FormData();
      Object.entries(result.s3UploadFields).forEach(([key, value]) => {
        formData.append(key, value);
      });
      formData.append('file', file);

      const uploadResponse = await fetch(result.s3UploadUrl, {
        method: 'POST',
        body: formData,
      });

      if (uploadResponse.ok) {
        // Extract file ID from the upload fields
        const fileId = result.s3UploadFields.key.split('/').pop()?.split('.')[0];
        if (fileId) {
          setPngImageId(fileId);
          message.success('PNG image uploaded successfully!');
        }
      } else {
        throw new Error('Upload failed');
      }
    } catch (error: any) {
      message.error(`Failed to upload PNG image: ${error.message || 'Unknown error'}`);
      console.error(error);
    } finally {
      setUploadingPng(false);
    }
    return false; // Prevent default upload behavior
  };

  // --- Table Columns ---
  const columns = [
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
      sorter: (a: Advertisement, b: Advertisement) => a.title.localeCompare(b.title),
      render: (text: string, record: Advertisement) => (
        <div>
          <div className="font-medium">{text}</div>
          {record.subtitle && (
            <div className="text-sm text-gray-500">{record.subtitle}</div>
          )}
        </div>
      ),
    },
    {
      title: 'Call to Action',
      dataIndex: 'callToActionText',
      key: 'callToActionText',
      render: (text: string, record: Advertisement) => (
        <div>
          <div>{text}</div>
          <a 
            href={record.callToActionLink} 
            target="_blank" 
            rel="noopener noreferrer"
            className="text-xs text-blue-500 hover:text-blue-700"
          >
            <LinkOutlined /> {record.isExternal ? 'External' : 'Internal'} Link
          </a>
        </div>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? 'Active' : 'Inactive'}
        </Tag>
      ),
      sorter: (a: Advertisement, b: Advertisement) => Number(a.isActive) - Number(b.isActive),
    },
    {
      title: 'Sort Order',
      dataIndex: 'sortOrder',
      key: 'sortOrder',
      sorter: (a: Advertisement, b: Advertisement) => a.sortOrder - b.sortOrder,
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: any, record: Advertisement) => (
        <Space>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => openEditModal(record)}
          >
            Edit
          </Button>
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => showDeleteConfirm(record)}
          >
            Delete
          </Button>
        </Space>
      ),
    },
  ];

  // --- Render Logic ---
  if (isLoadingAdvertisements && !advertisements) {
    return (
      <DefaultLayout user={user}>
        <Breadcrumb pageName='Advertisements' />
        <div className="p-4 flex justify-center items-center h-64">
          <Spin size="large" />
        </div>
      </DefaultLayout>
    );
  }

  if (advertisementsError) {
    return (
      <DefaultLayout user={user}>
         <Breadcrumb pageName='Advertisements' />
         <Alert
           message="Error Loading Advertisements"
           description={advertisementsError.message || 'Unknown error'}
           type="error"
           showIcon
           className="m-4"
         />
      </DefaultLayout>
    );
  }

  return (
    <DefaultLayout user={user}>
      <div className='w-full'>
        <Breadcrumb pageName='Advertisements' />

        <div className="mb-4 flex flex-col sm:flex-row justify-between items-center gap-4">
          <div className="flex flex-col sm:flex-row gap-2">
            <Input
              placeholder="Filter by title, subtitle, or description"
              prefix={<SearchOutlined />}
              value={filterText}
              onChange={handleFilterChange}
              style={{ width: '100%', maxWidth: 300 }}
              allowClear
            />
            <Button.Group>
              <Button 
                type={filterActive === undefined ? 'primary' : 'default'}
                onClick={() => handleActiveFilterChange(undefined)}
              >
                All
              </Button>
              <Button 
                type={filterActive === true ? 'primary' : 'default'}
                onClick={() => handleActiveFilterChange(true)}
              >
                Active
              </Button>
              <Button 
                type={filterActive === false ? 'primary' : 'default'}
                onClick={() => handleActiveFilterChange(false)}
              >
                Inactive
              </Button>
            </Button.Group>
          </div>
          <Button
            type='primary'
            icon={<PlusOutlined />}
            onClick={openAddModal}
            className="w-full sm:w-auto"
          >
            Add New Advertisement
          </Button>
        </div>

        <Table
          columns={columns}
          dataSource={filteredAdvertisements}
          rowKey="id"
          loading={isLoadingAdvertisements}
          pagination={{ 
            pageSize: 10, 
            showSizeChanger: true, 
            pageSizeOptions: ['10', '20', '50'],
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} advertisements`
          }}
          locale={{ emptyText: 'No advertisements found.' }}
          scroll={{ x: 'max-content' }}
        />

        {/* Add Advertisement Modal */}
        <Modal
          title="Add New Advertisement"
          open={isAddModalOpen}
          onCancel={closeAddModal}
          width={800}
          footer={[
            <Button key="back" onClick={closeAddModal} disabled={isSubmitting}>
              Cancel
            </Button>,
            <Button
              key="submit"
              type="primary"
              loading={isSubmitting}
              onClick={() => addForm.submit()}
            >
              Add Advertisement
            </Button>,
          ]}
        >
          <Form
            form={addForm}
            layout="vertical"
            onFinish={handleCreateAdvertisementSubmit}
            requiredMark={false}
          >
            <Form.Item
              name="title"
              label="Title"
              rules={[{ required: true, message: 'Please enter advertisement title', whitespace: true }]}
            >
              <Input placeholder='e.g., Wall Painting Service' disabled={isSubmitting} />
            </Form.Item>

            <Form.Item
              name="subtitle"
              label="Subtitle (Optional)"
            >
              <Input placeholder='e.g., Make your wall stylish' disabled={isSubmitting} />
            </Form.Item>

            <Form.Item
              name="description"
              label="Description (Optional)"
            >
              <Input.TextArea
                rows={3}
                placeholder='Detailed description of the advertisement'
                disabled={isSubmitting}
              />
            </Form.Item>

            <Form.Item
              name="callToActionText"
              label="Call to Action Text"
              rules={[{ required: true, message: 'Please enter call to action text', whitespace: true }]}
            >
              <Input placeholder='e.g., Book Now' disabled={isSubmitting} />
            </Form.Item>

            <Form.Item
              name="callToActionLink"
              label="Call to Action Link"
              rules={[
                { required: true, message: 'Please enter call to action link' },
                { type: 'url', message: 'Please enter a valid URL' }
              ]}
            >
              <Input placeholder='e.g., https://example.com/book' disabled={isSubmitting} />
            </Form.Item>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Form.Item
                name="isExternal"
                label="External Link"
                valuePropName="checked"
                initialValue={false}
              >
                <Switch disabled={isSubmitting} />
              </Form.Item>

              <Form.Item
                name="isActive"
                label="Active"
                valuePropName="checked"
                initialValue={true}
              >
                <Switch disabled={isSubmitting} />
              </Form.Item>

              <Form.Item
                name="sortOrder"
                label="Sort Order"
                initialValue={0}
              >
                <Input type="number" min={0} disabled={isSubmitting} />
              </Form.Item>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Form.Item label="Background Image (Optional)">
                <Upload
                  beforeUpload={handleBackgroundImageUpload}
                  showUploadList={false}
                  accept="image/jpeg,image/png"
                  disabled={isSubmitting}
                >
                  <Button
                    icon={<UploadOutlined />}
                    loading={uploadingBackground}
                    disabled={isSubmitting}
                  >
                    {backgroundImageId ? 'Change Background Image' : 'Upload Background Image'}
                  </Button>
                </Upload>
                {backgroundImageId && (
                  <div className="mt-2">
                    <Tag color="green">Background image uploaded</Tag>
                    <Button
                      type="link"
                      size="small"
                      onClick={() => setBackgroundImageId(null)}
                      disabled={isSubmitting}
                    >
                      Remove
                    </Button>
                  </div>
                )}
              </Form.Item>

              <Form.Item label="PNG Image (Optional)">
                <Upload
                  beforeUpload={handlePngImageUpload}
                  showUploadList={false}
                  accept="image/png"
                  disabled={isSubmitting}
                >
                  <Button
                    icon={<UploadOutlined />}
                    loading={uploadingPng}
                    disabled={isSubmitting}
                  >
                    {pngImageId ? 'Change PNG Image' : 'Upload PNG Image'}
                  </Button>
                </Upload>
                {pngImageId && (
                  <div className="mt-2">
                    <Tag color="green">PNG image uploaded</Tag>
                    <Button
                      type="link"
                      size="small"
                      onClick={() => setPngImageId(null)}
                      disabled={isSubmitting}
                    >
                      Remove
                    </Button>
                  </div>
                )}
              </Form.Item>
            </div>
          </Form>
        </Modal>

        {/* Edit Advertisement Modal */}
        <Modal
          title="Edit Advertisement"
          open={isEditModalOpen}
          onCancel={closeEditModal}
          width={800}
          footer={[
            <Button key="back" onClick={closeEditModal} disabled={isSubmitting}>
              Cancel
            </Button>,
            <Button
              key="submit"
              type="primary"
              loading={isSubmitting}
              onClick={() => editForm.submit()}
            >
              Update Advertisement
            </Button>,
          ]}
        >
          <Form
            form={editForm}
            layout="vertical"
            onFinish={handleUpdateAdvertisementSubmit}
            requiredMark={false}
          >
            <Form.Item
              name="title"
              label="Title"
              rules={[{ required: true, message: 'Please enter advertisement title', whitespace: true }]}
            >
              <Input placeholder='e.g., Wall Painting Service' disabled={isSubmitting} />
            </Form.Item>

            <Form.Item
              name="subtitle"
              label="Subtitle (Optional)"
            >
              <Input placeholder='e.g., Make your wall stylish' disabled={isSubmitting} />
            </Form.Item>

            <Form.Item
              name="description"
              label="Description (Optional)"
            >
              <Input.TextArea
                rows={3}
                placeholder='Detailed description of the advertisement'
                disabled={isSubmitting}
              />
            </Form.Item>

            <Form.Item
              name="callToActionText"
              label="Call to Action Text"
              rules={[{ required: true, message: 'Please enter call to action text', whitespace: true }]}
            >
              <Input placeholder='e.g., Book Now' disabled={isSubmitting} />
            </Form.Item>

            <Form.Item
              name="callToActionLink"
              label="Call to Action Link"
              rules={[
                { required: true, message: 'Please enter call to action link' },
                { type: 'url', message: 'Please enter a valid URL' }
              ]}
            >
              <Input placeholder='e.g., https://example.com/book' disabled={isSubmitting} />
            </Form.Item>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Form.Item
                name="isExternal"
                label="External Link"
                valuePropName="checked"
              >
                <Switch disabled={isSubmitting} />
              </Form.Item>

              <Form.Item
                name="isActive"
                label="Active"
                valuePropName="checked"
              >
                <Switch disabled={isSubmitting} />
              </Form.Item>

              <Form.Item
                name="sortOrder"
                label="Sort Order"
              >
                <Input type="number" min={0} disabled={isSubmitting} />
              </Form.Item>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Form.Item label="Background Image (Optional)">
                <Upload
                  beforeUpload={handleBackgroundImageUpload}
                  showUploadList={false}
                  accept="image/jpeg,image/png"
                  disabled={isSubmitting}
                >
                  <Button
                    icon={<UploadOutlined />}
                    loading={uploadingBackground}
                    disabled={isSubmitting}
                  >
                    {backgroundImageId ? 'Change Background Image' : 'Upload Background Image'}
                  </Button>
                </Upload>
                {backgroundImageId && (
                  <div className="mt-2">
                    <Tag color="green">Background image uploaded</Tag>
                    <Button
                      type="link"
                      size="small"
                      onClick={() => setBackgroundImageId(null)}
                      disabled={isSubmitting}
                    >
                      Remove
                    </Button>
                  </div>
                )}
              </Form.Item>

              <Form.Item label="PNG Image (Optional)">
                <Upload
                  beforeUpload={handlePngImageUpload}
                  showUploadList={false}
                  accept="image/png"
                  disabled={isSubmitting}
                >
                  <Button
                    icon={<UploadOutlined />}
                    loading={uploadingPng}
                    disabled={isSubmitting}
                  >
                    {pngImageId ? 'Change PNG Image' : 'Upload PNG Image'}
                  </Button>
                </Upload>
                {pngImageId && (
                  <div className="mt-2">
                    <Tag color="green">PNG image uploaded</Tag>
                    <Button
                      type="link"
                      size="small"
                      onClick={() => setPngImageId(null)}
                      disabled={isSubmitting}
                    >
                      Remove
                    </Button>
                  </div>
                )}
              </Form.Item>
            </div>
          </Form>
        </Modal>
      </div>
    </DefaultLayout>
  );
};

export default AdminAdvertisementsPage;
