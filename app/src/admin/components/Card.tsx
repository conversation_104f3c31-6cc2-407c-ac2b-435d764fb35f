import React from 'react';

interface CardProps {
  title?: React.ReactNode;
  children: React.ReactNode;
  className?: string;
  titleClassName?: string;
  bodyClassName?: string;
  extra?: any;
}

const CustomCard: React.FC<CardProps> = ({
  title,
  children,
  className = '',
  titleClassName = '',
  bodyClassName = '',
  extra = null,
}) => {
  return (
    <div
      className={`bg-white dark:bg-boxdark border border-stroke dark:border-strokedark rounded-md shadow-default ${className}`}
    >
      {title && (
        <div
          className={`border-b border-stroke dark:border-strokedark px-6 py-4 ${titleClassName}`}
        >
          <h4 className='font-medium text-black dark:text-white m-0 p-0'>{title}</h4>
          {extra && <div className='float-right'>{extra}</div>}
        </div>
      )}
      <div className={`p-6 ${bodyClassName}`}>{children}</div>
    </div>
  );
};

export default CustomCard;