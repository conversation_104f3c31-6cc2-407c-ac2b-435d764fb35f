/**
 * Admin Logger utility for tracking admin actions and debugging
 */

interface LogEntry {
  timestamp: Date;
  level: 'info' | 'warn' | 'error';
  action: string;
  userId?: string;
  details?: any;
  error?: any;
}

class AdminLogger {
  private logs: LogEntry[] = [];
  
  private createLogEntry(level: LogEntry['level'], action: string, userId?: string, details?: any, error?: any): LogEntry {
    return {
      timestamp: new Date(),
      level,
      action,
      userId,
      details,
      error
    };
  }
  
  info(action: string, userId?: string, details?: any) {
    const entry = this.createLogEntry('info', action, userId, details);
    this.logs.push(entry);
    console.log(`[ADMIN-INFO] ${action}`, {
      userId,
      details,
      timestamp: entry.timestamp
    });
  }
  
  warn(action: string, userId?: string, details?: any) {
    const entry = this.createLogEntry('warn', action, userId, details);
    this.logs.push(entry);
    console.warn(`[ADMIN-WARN] ${action}`, {
      userId,
      details,
      timestamp: entry.timestamp
    });
  }
  
  error(action: string, error: any, userId?: string, details?: any) {
    const entry = this.createLogEntry('error', action, userId, details, error);
    this.logs.push(entry);
    console.error(`[ADMIN-ERROR] ${action}`, {
      error: error.message || error,
      stack: error.stack,
      userId,
      details,
      timestamp: entry.timestamp
    });
  }
  
  // Get recent logs for debugging
  getRecentLogs(limit: number = 50): LogEntry[] {
    return this.logs.slice(-limit);
  }
  
  // Clear old logs to prevent memory issues
  clearOldLogs(daysOld: number = 7) {
    const cutoff = new Date();
    cutoff.setDate(cutoff.getDate() - daysOld);
    
    this.logs = this.logs.filter(log => log.timestamp > cutoff);
  }
}

export const adminLogger = new AdminLogger();

// Admin action types for consistent logging
export const AdminActions = {
  // Authentication
  LOGIN_SUCCESS: 'login_success',
  LOGIN_FAILED: 'login_failed',
  LOGIN_ATTEMPT: 'login_attempt',
  
  // Provider Management
  PROVIDERS_FETCHED: 'providers_fetched',
  PROVIDER_APPROVED: 'provider_approved',
  PROVIDER_REJECTED: 'provider_rejected',
  PROVIDER_STATUS_UPDATED: 'provider_status_updated',
  
  // Customer Management
  CUSTOMERS_FETCHED: 'customers_fetched',
  CUSTOMER_DETAILS_VIEWED: 'customer_details_viewed',
  
  // Admin User Management
  ADMIN_USER_CREATED: 'admin_user_created',
  ADMIN_USER_CREATION_FAILED: 'admin_user_creation_failed',
  
  // Category Management
  CATEGORIES_FETCHED: 'categories_fetched',
  CATEGORY_CREATED: 'category_created',
  CATEGORY_UPDATED: 'category_updated',
  CATEGORY_DELETED: 'category_deleted',
  
  // System
  SYSTEM_ERROR: 'system_error',
  UNAUTHORIZED_ACCESS: 'unauthorized_access',
} as const;

export type AdminActionType = typeof AdminActions[keyof typeof AdminActions]; 