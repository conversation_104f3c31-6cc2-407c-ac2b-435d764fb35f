import { HttpError, prisma } from 'wasp/server';
import type { Conversation, Message, User, Participant, MessageStatus, Notification } from 'wasp/entities';
import type { Prisma } from '@prisma/client';
import { getIoInstance } from '../server/webSocket'; // Correct import for WebSocket server instance

// Rely on <PERSON><PERSON>'s type inference for args and context for now.

interface SendMessageArgs {
  conversationId: number;
  content: string;
  hasAttachments?: boolean; // Assuming attachments are handled separately or links are in content
}

export const sendMessage = async (args: SendMessageArgs, context: any): Promise<Message> => {
  if (!context.user?.id) {
    throw new HttpError(401, 'User not authenticated.');
  }
  const currentUserId = context.user.id;
  const senderDomain = 'user'; // Assuming 'user' domain

  const { conversationId, content, hasAttachments } = args;
  if (!conversationId || typeof conversationId !== 'number') {
    throw new HttpError(400, 'Valid Conversation ID is required.');
  }
  if (!content || typeof content !== 'string' || content.trim() === '') {
    throw new HttpError(400, 'Message content cannot be empty.');
  }

  try {
    // 1. Verify the sender is a participant of the conversation and can write.
    const participant = await context.entities.Participant.findFirst({
      where: {
        conversationId: conversationId,
        userId: currentUserId,
        userDomain: senderDomain,
      },
    });

    if (!participant) {
      throw new HttpError(403, 'You are not a participant in this conversation.');
    }
    if (!participant.canWrite) {
      throw new HttpError(403, 'You do not have permission to send messages in this conversation.');
    }

    // 2. Create the Message record.
    const newMessage = await context.entities.Message.create({
      data: {
        conversationId: conversationId,
        senderId: currentUserId,
        senderDomain: senderDomain,
        content: content,
        hasAttachments: hasAttachments || false,
      },
      include: {
        sender: {
          select: { id: true, firstName: true, lastName: true, email: true }
        }
      }
    });

    // 3. Update the Conversation's lastMessageId and updatedAt timestamp.
    await context.entities.Conversation.update({
      where: { id: conversationId },
      data: {
        lastMessageId: newMessage.id,
        updatedAt: new Date(),
      },
    });

    // 4. Get all participants to update their unread counts and create message statuses
    const conversationParticipants = await context.entities.Participant.findMany({
      where: { conversationId: conversationId },
      select: { id: true, userId: true, userDomain: true }
    });

    // Create message status for sender as SENT
    await context.entities.MessageStatus.create({
      data: {
        messageId: newMessage.id,
        userId: currentUserId,
        status: 'SENT',
        timestamp: new Date(),
      }
    });

    // Update unread count and create DELIVERED status for other participants
    for (const p of conversationParticipants) {
      if (p.userId !== currentUserId) {
        // Increment unread count
        await context.entities.Participant.update({
          where: { id: p.id },
          data: { unreadCount: { increment: 1 } },
        });

        // Create DELIVERED status
        await context.entities.MessageStatus.create({
          data: {
            messageId: newMessage.id,
            userId: p.userId,
            status: 'DELIVERED',
            timestamp: new Date(),
          }
        });
      }
    }

    // 5. Emit WebSocket event
    const io = getIoInstance();
    if (io) {
      // const room = `user:${}`;
      const conversationUsers = await context.entities.Participant.findMany({
        where: { conversationId: conversationId },
        select: { userId: true }
      });
      for (const user of conversationUsers) {
          const room = `user_${user.userId}`;
          const messagePayload = {
            id: newMessage.id,
            conversationId: newMessage.conversationId,
            senderId: newMessage.senderId,
            senderDomain: newMessage.senderDomain,
            content: newMessage.content,
            hasAttachments: newMessage.hasAttachments,
            createdAt: newMessage.createdAt.toISOString(),
            sender: newMessage.sender ? {
              id: newMessage.sender.id,
              firstName: newMessage.sender.firstName,
              lastName: newMessage.sender.lastName,
            } : null,
          };
        io.to(room).emit('newMessage', messagePayload);
        console.log(`Message sent and emitted to room: ${room}`);
      }
      // io.to(room).emit('newMessage', messagePayload);
      // console.log(`Message sent and emitted to room: ${room}`);
    } else {
      console.warn('WebSocket IO instance not available. Cannot emit newMessage.');
    }

    return newMessage as Message;
  } catch (error) {
    if (error instanceof HttpError) {
      throw error;
    }
    console.error('Failed to send message:', error);
    throw new HttpError(500, 'Failed to send message.');
  }
};

interface StartConversationArgs {
  otherUserIds: string[]; // IDs of other users to include
  name?: string;           // Optional: Name for group conversations
  isGroup?: boolean;       // Defaults to false (1-on-1 chat)
}

// Define a more specific type for what Prisma returns with the include
type PrismaParticipantWithUser = Prisma.ParticipantGetPayload<{
  include: { user: { select: { id: true, firstName: true, lastName: true, email: true } } }
}>;

type PrismaConversationWithDetails = Prisma.ConversationGetPayload<{
  include: {
    participants: { include: { user: { select: { id: true, firstName: true, lastName: true, email: true } } } },
    owner: { select: { id: true, firstName: true, lastName: true, email: true } },
    lastMessage: false // Assuming lastMessage is not needed or handled differently here
  }
}>;

export const startConversation = async (args: StartConversationArgs, context: any): Promise<Conversation> => {
  if (!context.user?.id) {
    throw new HttpError(401, 'User not authenticated.');
  }
  const currentUserId = context.user.id;
  const currentUserDomain = 'user'; // Assuming 'user' domain

  const { otherUserIds, name, isGroup = false } = args;

  if (!otherUserIds || otherUserIds.length === 0) {
    throw new HttpError(400, 'At least one other user ID must be provided.');
  }
  if (isGroup && (!name || name.trim() === '')) {
    throw new HttpError(400, 'Group name is required for group conversations.');
  }
  if (!isGroup && otherUserIds.length > 1) {
    throw new HttpError(400, 'Cannot create a 1-on-1 chat with multiple users. Use isGroup: true.');
  }

  try {
    // For 1-on-1 chats, check if a conversation already exists
    if (!isGroup && otherUserIds.length === 1) {
      const otherUserId = otherUserIds[0];
      // Use context.entities for this findFirst call
      const existingConversation = await context.entities.Conversation.findFirst({
        where: {
          isGroup: false,
          participants: {
            every: {
              OR: [
                { userId: currentUserId, userDomain: currentUserDomain },
                { userId: otherUserId, userDomain: 'user' },
              ],
            },
          },
        },
        include: {
          participants: { include: { user: { select: { id: true, firstName: true, lastName: true, email: true} } } },
          lastMessage: { include: { sender: true } },
          owner: {select: { id: true, firstName: true, lastName: true, email: true} },
        }
      });

      if (existingConversation && (existingConversation.participants as PrismaParticipantWithUser[]).length === 2) {
        console.log('Found existing 1-on-1 conversation:', existingConversation.id);
        return existingConversation as Conversation;
      }
    }

    // Create new conversation using context.entities
    const newConversation = await context.entities.Conversation.create({
      data: {
        name: isGroup ? name : null,
        isGroup: isGroup,
        ownerId: currentUserId,
        ownerDomain: currentUserDomain,
        participants: {
          create: [currentUserId, ...otherUserIds].map(userId => ({
            userId: userId,
            userDomain: 'user',
            canWrite: true,
            canDownload: true,
            unreadCount: 0,
          }))
        }
      },
      include: {
        participants: {
          include: {
            user: { select: { id: true, firstName: true, lastName: true, email: true } }
          }
        },
        owner: { select: { id: true, firstName: true, lastName: true, email: true } },
        lastMessage: false, // We don't need last message for a new conversation
      }
    }) as PrismaConversationWithDetails; // Cast to the more specific Prisma type

    // Emit WebSocket event to all participants
    const io = getIoInstance();
    if (io && context.webSocket) {
      let displayName = newConversation.name;
      if (!newConversation.isGroup && newConversation.participants.length > 0) {
        const otherParticipant = (newConversation.participants as PrismaParticipantWithUser[]).find(
          (p: PrismaParticipantWithUser) => p.userId !== currentUserId
        );
        if (otherParticipant?.user) {
          displayName = `${otherParticipant.user.firstName || ''} ${otherParticipant.user.lastName || ''}`.trim() || otherParticipant.user.email || 'User';
        }
      }

      const conversationPayload = {
        id: newConversation.id,
        name: newConversation.name,
        isGroup: newConversation.isGroup,
        ownerDomain: newConversation.ownerDomain,
        ownerId: newConversation.ownerId,
        createdAt: newConversation.createdAt.toISOString(),
        updatedAt: newConversation.updatedAt.toISOString(),
        lastMessageId: newConversation.lastMessageId,
        unread: 0,
        displayName: displayName!,
        displayImage: null,
        otherParticipantIds: (newConversation.participants as PrismaParticipantWithUser[])
          .filter((p: PrismaParticipantWithUser) => p.userId !== currentUserId)
          .map((p: PrismaParticipantWithUser) => p.userId),
        participants: (newConversation.participants as PrismaParticipantWithUser[]).map((p: PrismaParticipantWithUser) => ({
          id: p.id,
          userId: p.userId,
          userDomain: p.userDomain,
          canWrite: p.canWrite,
          canDownload: p.canDownload,
          unreadCount: p.unreadCount,
          user: p.user ? {
            id: p.user.id,
            firstName: p.user.firstName,
            lastName: p.user.lastName,
            email: p.user.email
          } : null
        })),
        lastMessage: null,
        owner: newConversation.owner ? {
          id: newConversation.owner.id,
          firstName: newConversation.owner.firstName,
          lastName: newConversation.owner.lastName,
          email: newConversation.owner.email
        } : null,
      };

      (newConversation.participants as PrismaParticipantWithUser[]).forEach((participant: PrismaParticipantWithUser) => {
        const room = `user_${participant.userId}`;
        io.to(room).emit('conversationStarted', conversationPayload);
        console.log(`Emitted 'conversationStarted' to room ${room}`);
      });
    } else {
      console.warn('WebSocket IO instance not available. Cannot emit conversationStarted.');
    }

    return newConversation as Conversation;
  } catch (error) {
    if (error instanceof HttpError) {
      throw error;
    }
    console.error('Failed to start conversation:', error);
    throw new HttpError(500, 'Failed to start conversation.');
  }
};

interface MarkMessageAsReadArgs {
  messageId: number;
  conversationId: number; // Add conversationId for efficient participant lookup
}

export const markMessageAsRead = async (args: MarkMessageAsReadArgs, context: any): Promise<MessageStatus | null> => {
  if (!context.user?.id) {
    throw new HttpError(401, 'User not authenticated.');
  }
  const currentUserId = context.user.id;
  const currentUserDomain = 'user'; // Assuming 'user' domain

  const { messageId, conversationId } = args;

  if (!messageId || typeof messageId !== 'number') {
    throw new HttpError(400, 'Valid Message ID is required.');
  }
  if (!conversationId || typeof conversationId !== 'number') {
    throw new HttpError(400, 'Valid Conversation ID is required.');
  }

  try {
    let successfullyMarkedAsRead = false;
    let statusForEmitter: MessageStatus | null = null;

    // Use the globally imported prisma for the transaction
    await prisma.$transaction(async (txPrisma: Prisma.TransactionClient) => {
      // 1. Find the message to ensure it exists and get its sender for potential notification
      const message = await txPrisma.message.findUnique({
        where: { id: messageId },
        select: { senderId: true, senderDomain: true, conversationId: true }
      });

      if (!message) {
        throw new HttpError(404, 'Message not found.');
      }
      // Ensure the message belongs to the provided conversationId for sanity
      if (message.conversationId !== conversationId) {
         throw new HttpError(400, 'Message does not belong to the specified conversation.');
      }

      // 2. Find or Create/Update MessageStatus for the current user.
      const existingStatus = await txPrisma.messageStatus.findUnique({
        where: {
          messageId_userId: { 
            messageId: messageId,
            userId: currentUserId,
          }
        }
      });

      let updatedOrCreatedStatus: MessageStatus;

      if (existingStatus) {
        if (existingStatus.status === 'READ') {
          statusForEmitter = existingStatus;
          return; // Already read, no change needed
        }
        updatedOrCreatedStatus = await txPrisma.messageStatus.update({
          where: { id: existingStatus.id },
          data: { status: 'READ', timestamp: new Date() },
        });
      } else {
        updatedOrCreatedStatus = await txPrisma.messageStatus.create({
          data: {
            messageId: messageId,
            userId: currentUserId,
            status: 'READ',
            timestamp: new Date(),
          },
        });
      }
      statusForEmitter = updatedOrCreatedStatus;

      // 3. Decrement unreadCount for the current participant in this conversation.
      if (!existingStatus || existingStatus.status !== 'READ') {
        const participant = await txPrisma.participant.findFirst({
            where: {
                conversationId: conversationId,
                userId: currentUserId,
                userDomain: currentUserDomain,
            }
        });

        if (participant && participant.unreadCount > 0) {
            await txPrisma.participant.update({
                where: { id: participant.id },
                data: { unreadCount: { decrement: 1 } },
            });
        } else if (participant && participant.unreadCount === 0) {
            console.log(`Participant ${currentUserId} in convo ${conversationId} already has 0 unread. Message ${messageId} marked read.`);
        }
        successfullyMarkedAsRead = true; 
      }
    }); // End transaction

    if (successfullyMarkedAsRead) {
        // Re-fetch the status using context.entities to ensure consistency if possible, 
        // or stick to txPrisma if this is part of a pattern for this action.
        // For now, using the global prisma, assuming it should be available post-transaction.
        const committedStatus = await context.entities.MessageStatus.findUnique({
             where: {
                messageId_userId: {
                    messageId: messageId,
                    userId: currentUserId,
                }
            },
        });

        if (committedStatus && committedStatus.status === 'READ') {
            const io = getIoInstance();
            if (io && context.webSocket) { 
                // Use context.entities for this read operation as well
                const dbMessage = await context.entities.Message.findUnique({ 
                    where: {id: messageId}, 
                    select: { senderId: true, senderDomain: true, conversationId: true }
                });
                if (dbMessage) {
                    const payload = {
                        messageId: messageId,
                        conversationId: dbMessage.conversationId,
                        readerId: currentUserId,
                        readAt: new Date().toISOString(),
                    };
                    const senderRoom = `user_${dbMessage.senderId}`;
                    io.to(senderRoom).emit('messageRead', payload); 
                    console.log(`Emitted 'messageRead' to room ${senderRoom} for message ${messageId}`);
                    
                    
                    const conversationRoom = `conversation:${dbMessage.conversationId}`;
                    io.to(conversationRoom).emit('messageReadReceipt', payload);
                    console.log(`Emitted 'messageReadReceipt' to room ${conversationRoom} for message ${messageId}`);
                }
            }
        }
        return committedStatus as MessageStatus | null; 
    } else if (statusForEmitter) {
        return statusForEmitter;
    }

    return null; 

  } catch (error) {
    if (error instanceof HttpError) {
      throw error;
    }
    console.error('Failed to mark message as read:', error);
    throw new HttpError(500, 'Failed to mark message as read.');
  }
}; 