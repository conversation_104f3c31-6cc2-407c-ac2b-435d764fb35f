import { createSlice } from '@reduxjs/toolkit';

const initialState = {
    testVar: "test"
};


const testSlice = createSlice({
  name: 'testSlice',
  initialState,
  reducers: {
    updateTestVar(state, action) {
      const { testVar } = action.payload;
      state.testVar = testVar;
    },
  },
});
  
export const { updateTestVar } = testSlice.actions;


const testReducer = testSlice.reducer;

export default testReducer;