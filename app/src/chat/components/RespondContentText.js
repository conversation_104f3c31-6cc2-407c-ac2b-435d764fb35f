import React from 'react';

const RespondContentText = ({ text, time }) => {
  return (
    <div style={{
      display:"flex",
      gap:15,
      alignItems:"center"
    }}>
      <div className="bg-separator-light d-inline-block rounded-md py-3 px-3 ps-7 text-dark position-relative">
        <span className="text">{text} </span>
        <div style={{
          width:"100%",
          display:"flex",
          justifyContent:"flex-end"
        }}>
          {/* <span className="position-absolute text-extra-small text-white opacity-75 b-2 s-2 time">{time}</span> */}
          <span className='text-extra-small text-dark opacity-75'>{time}</span>
        </div>
      </div>
      
    </div>
  );
};
export default RespondContentText;
