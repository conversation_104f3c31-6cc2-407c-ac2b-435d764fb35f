import React, { useEffect, useState } from 'react';
import { Spinner } from 'react-bootstrap';
import Lightbox from 'react-image-lightbox';
import 'react-image-lightbox/style.css';
import ImageFetcher from './ImageFetcher';
import AuthClientStore from '../../../../AuthClientStore'; 

const RespondContentAttachment = ({ attachments, time }) => {
  const [photoIndex, setPhotoIndex] = useState(0);
  const [isOpen, setIsOpen] = useState(false);
  
  const [token ,setToken] = useState(null);
  useEffect(() => {
    setToken(AuthClientStore.getAccessToken())
  },[])

  const openLightbox = (index) => {
    setPhotoIndex(index);
    setIsOpen(true);
  };

  useEffect(() => {
    setToken(AuthClientStore.getAccessToken())
  },[])


  if (attachments && attachments.length > 0) {
    return (
      <>
        {attachments.map((attachment, aIndex) => {
          return (
            <div key={`m.attachment.${aIndex}`} className="d-inline-block sh-11 me-2 position-relative pb-4 rounded-md bg-separator-light text-alternate">
              <div className="lightbox h-100 attachment cursor-pointer">
                {/* <img src={attachment} className="h-100 rounded-md-top" alt={attachment} /> */}
                <ImageFetcher className="h-100 rounded-md-top" style={{
                  height:"100%"
                }} token={token} key={aIndex} imageUrl={attachment} />
              </div>
              <span className="position-absolute text-extra-small text-alternate opacity-75 b-2 e-2 time">{time}</span>
            </div>
          );
        })}
      </>
    );
  }
  return <></>;
};
export default RespondContentAttachment;
