import AuthClientStore from '../../../../AuthClientStore';
import React, { useEffect, useState } from 'react';
import 'react-image-lightbox/style.css';
import { Image, Skeleton, Spin } from 'antd';
import ImageFetcher from './ImageFetcher';
import { ExclamationCircleOutlined } from '@ant-design/icons';
const MessageContentAttachment = ({ attachments, time, isLoading, error }) => {

  const [token ,setToken] = useState(null);
  useEffect(() => {
    setToken(AuthClientStore.getAccessToken())
  },[])


  if (attachments && attachments.length > 0) {
    return (
      <>
        {isLoading && <Skeleton.Image active={true} />}
        {!isLoading && error && <div style={{
          display:"flex",
          gap:15,
          alignItems:"center"
        }}>
          <ExclamationCircleOutlined style={{color:"var(--danger)"}} />
          <Skeleton.Image />
        </div>}
        {!isLoading && !error && attachments.map((attachment, aIndex) => {
          return (
            <div key={`m.attachment.${aIndex}`} className="d-inline-block sh-11 ms-2 position-relative pb-4 bg-primary rounded-md">
              <div className="lightbox h-100 attachment cursor-pointer">
                {/* <img src={attachment} className="h-100 rounded-md-top" alt={attachment} /> */}
                <ImageFetcher className="h-100 rounded-md-top" style={{
                  height:"100%"
                }} token={token} key={aIndex} imageUrl={attachment} />   
              </div>
              <span className="position-absolute text-extra-small text-white opacity-75 b-2 s-2 time">{time}</span>
            </div>
          );
        })}
      </>
    );
  }
  return <></>;
};
export default MessageContentAttachment;
