import React, { useRef, useState } from 'react';
import { Card, Button, Form } from 'react-bootstrap';
import CsLineIcons from '../../../../cs-line-icons/CsLineIcons';
import { useSelector, useDispatch } from 'react-redux';
import useChatService from '../../../../services/chatService';
import { Skeleton } from 'antd';
import { useIntl } from 'react-intl'; // Import useIntl

const ChatInput = () => {
  const { formatMessage: f } = useIntl(); // Use formatMessage
  const refFileUpload = useRef(null);
  const [text, setText] = useState('');
  const {currentLang} = useSelector(state => state.lang)
  const dispatch = useDispatch();
  const { selectedChat, attachmentLoading, messageLoading } = useSelector((state) => state.chat);
  const { addAttachmentsToChat, addTextToChat } = useChatService();

  const onAttachButtonClick = () => {
    if (refFileUpload) {
      refFileUpload.current.dispatchEvent(new MouseEvent('click'));
    }
  };

  const addAttachment = (event) => {
    if (event.target.files && event.target.files[0]) {
      const reader = new FileReader();
      reader.onload = (loadEvent) => {
        dispatch(addAttachmentsToChat({ chat: selectedChat, attachments: event.target.files }));
      };
      reader.readAsDataURL(event.target.files[0]);
    }
  };

  const addText = () => {
    if (text !== '' && text.length > 0) {
      dispatch(addTextToChat({ chat: selectedChat, text }));
      setText('');
    }
  };

  const newTextOnKeyDown = (event) => {
    if (event.key.toLowerCase() === 'enter' && !event.shiftKey) {
      event.preventDefault();
      addText();
    }
  };

  const newTextOnChange = (event) => {
    setText(event.target.value);
  };

  return (
    <Card>
      <Card.Body className="p-0 d-flex flex-row align-items-center px-3 py-3">
        {attachmentLoading && <Skeleton.Image style={{ width: 80, height: 80 }} active={true} />}
        <Form.Control
          as="textarea"
          className="me-3 border-0 ps-2 py-2"
          rows="1"
          placeholder={f({ id: 'chat-input.placeholder.message' })} // Translated placeholder
          value={text}
          onChange={newTextOnChange}
          onKeyDown={newTextOnKeyDown}
          disabled={attachmentLoading || messageLoading}
        />
        <div className="d-flex flex-row">
          <Form.Control type="file" ref={refFileUpload} className="file-upload d-none" onChange={addAttachment} />
          <Button
            disabled={attachmentLoading}
            variant="outline-primary"
            className="btn-icon btn-icon-only mb-1 rounded-xl"
            onClick={onAttachButtonClick}
          >
            <CsLineIcons icon="attachment" />
          </Button>
          <Button
            disabled={messageLoading}
            variant="primary"
            className="btn-icon btn-icon-only mb-1 rounded-xl ms-1"
            onClick={addText}
          >
            <CsLineIcons icon={currentLang?.code == "AR" ? "chevron-left":"chevron-right"} />
          </Button>
        </div>
      </Card.Body>
    </Card>
  );
};

export default ChatInput;