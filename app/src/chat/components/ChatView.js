import React, { useRef, useEffect, useState } from 'react';
import CsLineIcons from '../../../../cs-line-icons/CsLineIcons';
import { Card, Row, Col, OverlayTrigger, Tooltip, Button } from 'react-bootstrap';
import { useSelector, useDispatch } from 'react-redux';
import { OverlayScrollbarsComponent } from 'overlayscrollbars-react';
import { useIntl } from 'react-intl';
import {UserAddOutlined } from '@ant-design/icons';
import RespondContentContainer from './RespondContentContainer';
import RespondContentText from './RespondContentText';
import RespondContentAttachment from './RespondContentAttachment';

import MessageContentContainer from './MessageContentContainer';
import MessageContentText from './MessageContentText';
import MessageContentAttachment from './MessageContentAttachment';
import ChatInput from './ChatInput';
import { chatChangeMode, chatSetCurrentCall } from '../chatSlice';
import AddParticipant from '../../../../layout/modals/addParticipant';

const ChatView = () => {
  const dispatch = useDispatch();
  const { formatMessage: f } = useIntl();
  const [participantModal, setParticipantModal] = useState(false);
  const refChatArea = useRef(null);
  const { selectedChat } = useSelector((state) => state.chat);
  const { currentUser } = useSelector((state) => state.auth);

  useEffect(() => {
    console.log("selected chat is ",selectedChat)
    if (selectedChat && refChatArea.current) {
      refChatArea.current.scrollTop = refChatArea.current.scrollHeight;
    }
    return () => {};
  }, [selectedChat]);

  const onCallClick = () => {
    dispatch(chatChangeMode('call'));
    dispatch(chatSetCurrentCall({ name: selectedChat.name, thumb: selectedChat.thumb, time: 0 }));
  };

  const onVideoCallClick = () => {
    dispatch(chatChangeMode('call'));
    dispatch(chatSetCurrentCall({ name: selectedChat.name, thumb: selectedChat.thumb, time: 0 }));
  };

  if (selectedChat != null) {
    const { name, thumb, status, last } = selectedChat;
    return (
      <div className="d-flex flex-column w-100" id="chatMode">
        {selectedChat?.isGroup && <AddParticipant activateModal={participantModal} setActivateModal={setParticipantModal} />}
        <Card className=" mb-2">
          {selectedChat && (
            <Card.Body className="d-flex flex-column w-100 position-relative">
              <div className="d-flex flex-row align-items-center mb-3" style={{
                justifyContent:"space-between"
              }}>
                <Row className=" g-0 sh-6 align-self-start" id="contactTitle">
                  <Col xs="auto">
                    <div className="sh-6 sw-6 d-inline-block position-relative">
                      <img src={thumb} style={{height:"40px", width:"40px"}} className="img-fluid rounded-xl border border-2 border-foreground profile" alt={name} />
                      {status === 'Online' && <i className="p-1 border border-1 border-foreground bg-primary position-absolute rounded-xl e-0 t-0 status" />}
                    </div>
                  </Col>
                  <Col>
                    <div style={{
                      padding:'0px !important',
                      paddingInlineEnd:"0.5rem !important"
                    }} className="d-flex flex-row pt-0 pb-0 pe-0 pe-0 ps-2 h-100 align-items-center justify-content-between">
                      <div className="d-flex flex-column ps-2">
                        <div className="name">{name}</div>
                        { !selectedChat?.isGroup && <div className="text-small text-muted last">{f({ id: "chatview.lastseen" })} {last}</div>}
                        { selectedChat?.isGroup && <div className="text-small text-muted last">{f({ id: "chatview.participantsNumber" })} {selectedChat?.pcount}</div>}
                      </div>
                    </div>
                  </Col>
                </Row>
                <div style={{
                  display:"flex",
                  gap:"5px",
                  alignItems:"center"
                }}>
                  {selectedChat?.isGroup && <Button onClick={() => setParticipantModal(true)} variant="outline-primary" className="btn-icon btn-icon-only">
                    <UserAddOutlined />
                  </Button>}
              
                  <OverlayTrigger delay={{ show: 250, hide: 0 }} placement="top" overlay={<Tooltip id="tooltip-top">{f({ id: "chatview.call" })}</Tooltip>}>
                    <Button variant="outline-primary" className="btn-icon btn-icon-only" onClick={onCallClick}>
                      <CsLineIcons icon="phone" />
                    </Button>
                  </OverlayTrigger>
                  <OverlayTrigger delay={{ show: 250, hide: 0 }} placement="top" overlay={<Tooltip id="tooltip-top">{f({ id: "chatview.videocall" })}</Tooltip>}>
                    <Button variant="outline-primary" className="btn-icon btn-icon-only" onClick={onVideoCallClick}>
                      <CsLineIcons icon="video" />
                    </Button>
                  </OverlayTrigger>
                </div>
                  
              </div>

              <div className="separator-light mb-3" />
              <div className="mb-n2 scroll-out" ref={refChatArea} style={{
                height:"450px",
                overflowY:"auto",
                overflowX:"hidden",
                padding:"15px"
              }}>
              {selectedChat.messages &&
                    selectedChat.messages.map((message, mIndex) => {
                      const { text, time, type, attachments, isLoading, error } = message;
                      if (type === 'response') {
                        return (
                          <RespondContentContainer key={`message${mIndex}`} message={message} user={{ name: selectedChat.name, thumb: selectedChat.thumb }}>
                            <>
                              {text !== '' && <RespondContentText text={text} time={time} />}
                              {attachments && attachments.length > 0 && <RespondContentAttachment attachments={attachments} time={time} />}
                            </>
                          </RespondContentContainer>
                        );
                      }
                      return (
                        <MessageContentContainer key={`message${mIndex}`} message={message} user={{ name: currentUser.name, thumb: currentUser.profilePicture ?? '/img/profile/profile-11.webp' }}>
                          <>
                            {text !== '' && <MessageContentText text={text} time={time} isLoading={isLoading} error={error} />}
                            {attachments && attachments.length > 0 && <MessageContentAttachment attachments={attachments} time={time} isLoading={isLoading} error={error} />}
                          </>
                        </MessageContentContainer>
                      );
                    })}
              </div>
            </Card.Body>
          )}
        </Card>
        <ChatInput />
      </div>
    );
  }
  return <></>;
};
export default ChatView;
