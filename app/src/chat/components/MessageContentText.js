import { Skel<PERSON>, Spin } from 'antd';
import React from 'react';
import { ExclamationCircleOutlined } from '@ant-design/icons';
const MessageContentText = ({ text, time, isLoading, error }) => {
  return (
    <div style={{
      display:"flex",
      gap:15,
      alignItems:"center"
    }}>
      {isLoading && <Spin size='small' />}
      {!isLoading && error && <ExclamationCircleOutlined style={{color:"var(--danger)"}} />}
      <div className="bg-gradient-light d-inline-block rounded-md py-3 px-3 ps-7 text-white position-relative">
        <span className="text">{text} </span>
        <div style={{
          width:"100%",
          display:"flex",
          justifyContent:"flex-end"
        }}>
          {/* <span className="position-absolute text-extra-small text-white opacity-75 b-2 s-2 time">{time}</span> */}
          <span className='text-extra-small text-white opacity-75'>{time}</span>
        </div>
      </div>
      
    </div>
    
  );
};
export default MessageContentText;
