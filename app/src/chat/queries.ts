import { HttpError } from 'wasp/server';
import type { Message, User, Participant, Conversation as PrismaConversation } from 'wasp/entities'; // Renamed to avoid conflict
import { Prisma } from '@prisma/client';

// Type for the raw data selected from Prisma for each participation
interface FetchedParticipant {
  unreadCount: number;
  conversation: {
    id: number;
    name: string | null;
    isGroup: boolean;
    ownerDomain: string;
    ownerId: string;
    createdAt: Date;
    updatedAt: Date;
    lastMessageId: number | null;
    participants: Array<{
      id: number;
      userId: string;
      userDomain: string;
      canWrite: boolean;
      canDownload: boolean;
      unreadCount: number;
      user: {
        id: string;
        firstName: string | null;
        lastName: string | null;
        email: string | null;
      };
    }>;
    lastMessage: {
      id: number;
      content: string;
      createdAt: Date;
      senderId: string;
      sender: {
        id: string;
        firstName: string | null;
        lastName: string | null;
      };
    } | null;
    owner: {
      id: string;
      firstName: string | null;
      lastName: string | null;
    };
  } | null; // Conversation can be null if a participant somehow exists without one, though unlikely with schema
}

// User details for participants or message senders
export interface AugmentedChatUser extends Record<string, any> {
  id: string;
  firstName: string | null;
  lastName: string | null;
  email?: string | null; // Make email optional for sender if not always present
}

// Participant details within a conversation
export interface AugmentedParticipant extends Record<string, any> {
  id: number;
  userId: string;
  userDomain: string;
  canWrite: boolean;
  canDownload: boolean;
  unreadCount: number;
  user: AugmentedChatUser;
}

// Message details within a conversation
export interface AugmentedMessageInfo extends Record<string, any> {
  id: number;
  content: string;
  createdAt: string; // ISO string
  senderId: string;
  sender: AugmentedChatUser;
}

// Interface for the plain data structure we want to return (frontend-facing)
export interface AugmentedConversation extends Record<string, any> {
  id: number;
  name: string | null;
  isGroup: boolean;
  ownerDomain: string;
  ownerId: string;
  createdAt: string; // ISO string
  updatedAt: string; // ISO string
  lastMessageId: number | null;
  unread: number; // Current user's unread count for this conversation
  displayName: string;
  displayImage: string | null;
  otherParticipantIds: string[];
  lastMessageContent?: string | null;
  lastMessageSenderName?: string;
  lastMessageCreatedAt?: string | null; // ISO string
  participants: AugmentedParticipant[];
  lastMessage: AugmentedMessageInfo | null;
  owner: AugmentedChatUser;
}

interface GetConversationsArgs { /* No arguments expected */ }

export const getConversations = async (args: GetConversationsArgs, context: any): Promise<AugmentedConversation[]> => {

  if (!context.user) {
    throw new HttpError(401, 'User not authenticated. Please login.');
  }
  const currentUserId = context?.user?.id;

  const participations: FetchedParticipant[] = await context.entities.Participant.findMany({
    where: { userId: currentUserId },
    select: {
      unreadCount: true,
      conversation: {
        select: {
          id: true,
          name: true,
          isGroup: true,
          ownerDomain: true,
          ownerId: true,
          createdAt: true,
          updatedAt: true,
          lastMessageId: true,
          participants: {
            select: {
              id: true,
              userId: true,
              userDomain: true,
              canWrite: true,
              canDownload: true,
              unreadCount: true,
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
            },
          },
          lastMessage: {
            select: {
              id: true,
              content: true,
              createdAt: true,
              senderId: true,
              sender: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                },
              },
            },
          },
          owner: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
            },
          },
        },
      },
    },
    orderBy: {
      conversation: {
        updatedAt: 'desc',
      },
    },
  });

  const conversationsToReturn: AugmentedConversation[] = participations
    .filter((p): p is FetchedParticipant & { conversation: NonNullable<FetchedParticipant['conversation']> } => p.conversation !== null)
    .map((p: FetchedParticipant & { conversation: NonNullable<FetchedParticipant['conversation']> }) => {
      const conv = p.conversation; // Now TypeScript knows conv is not null
      
      type ParticipantUser = NonNullable<NonNullable<FetchedParticipant['conversation']>['participants']>[0]['user'];
      type ConversationParticipant = NonNullable<NonNullable<FetchedParticipant['conversation']>['participants']>[0];

      const otherParticipantsUsers: ParticipantUser[] = conv.participants
        .filter((participant: ConversationParticipant) => participant.userId !== currentUserId)
        .map((op: ConversationParticipant) => op.user);

      let displayName = conv.name;
      let displayImage: string | null = null;
      const otherParticipantIds = otherParticipantsUsers.map((opUser: ParticipantUser) => opUser.id);

      if (!conv.isGroup && otherParticipantsUsers.length > 0) {
        const otherUser = otherParticipantsUsers[0];
        displayName = `${otherUser.firstName || ''} ${otherUser.lastName || ''}`.trim() || otherUser.email || 'User';
      }

      return {
        id: conv.id,
        name: conv.name,
        isGroup: conv.isGroup,
        ownerDomain: conv.ownerDomain,
        ownerId: conv.ownerId,
        createdAt: conv.createdAt.toISOString(),
        updatedAt: conv.updatedAt.toISOString(),
        lastMessageId: conv.lastMessageId,
        unread: p.unreadCount,
        displayName: displayName!,
        displayImage: displayImage,
        otherParticipantIds: otherParticipantIds,
        lastMessageContent: conv.lastMessage?.content,
        lastMessageSenderName: conv.lastMessage?.sender ?
          `${conv.lastMessage.sender.firstName || ''} ${conv.lastMessage.sender.lastName || ''}`.trim() : 'N/A',
        lastMessageCreatedAt: conv.lastMessage?.createdAt.toISOString(),
        participants: conv.participants.map((participant: ConversationParticipant) => ({
          id: participant.id,
          userId: participant.userId,
          userDomain: participant.userDomain,
          canWrite: participant.canWrite,
          canDownload: participant.canDownload,
          unreadCount: participant.unreadCount,
          user: {
              id: participant.user.id,
              firstName: participant.user.firstName,
              lastName: participant.user.lastName,
              email: participant.user.email
          }
        })),
        lastMessage: conv.lastMessage ? {
          id: conv.lastMessage.id,
          content: conv.lastMessage.content,
          createdAt: conv.lastMessage.createdAt.toISOString(),
          senderId: conv.lastMessage.senderId,
          sender: {
              id: conv.lastMessage.sender.id,
              firstName: conv.lastMessage.sender.firstName,
              lastName: conv.lastMessage.sender.lastName,
          }
        } : null,
        owner: {
          id: conv.owner.id,
          firstName: conv.owner.firstName,
          lastName: conv.owner.lastName,
        },
      };
    });

  return conversationsToReturn;
};

interface GetMessagesArgs {
  conversationId: number;
}

export const getMessages = async (args: GetMessagesArgs, context: any): Promise<Message[]> => {
  const { conversationId } = args;
  if (!context.user) {
    throw new HttpError(401);
  }
  console.log('Fetching messages for conversation:', conversationId, 'and user:', context.user.id);
  
  if (!conversationId || typeof conversationId !== 'number') {
    throw new HttpError(400, 'Valid Conversation ID is required.');
  }

  // First verify the user is a participant of this conversation
  const participant = await context.entities.Participant.findFirst({
    where: {
      conversationId: conversationId,
      userId: context.user.id,
    },
  });

  if (!participant) {
    throw new HttpError(403, 'You are not a participant in this conversation.');
  }

  // Fetch messages with sender information
  const messages = await context.entities.Message.findMany({
    where: {
      conversationId: conversationId,
    },
    include: {
      sender: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
        },     
      },
      statuses: {
        select: {
          status: true,
          userId: true,
        },
      },
    },
    orderBy: {
      createdAt: 'asc', // Order from oldest to newest
    },
  });

  console.log('Messages fetched:', messages);

  return messages;
}; 