/*

Acorn - Bootstrap 5 Html Laravel 8 .Net 5 Admin Template

Table of Contents
|
+-- utils
|   |
|   \-- mixins
|   \-- sizes
|   \-- positions
|   \-- shared
|   
+-- themes
|   |
|   \-- rest
|   \-- dark.blue
|   \-- dark.green
|   \-- dark.red
|   \-- dark.purple
|   \-- dark.pink
|   \-- light.blue
|   \-- light.green
|   \-- light.red
|   \-- light.purple
|   \-- light.pink
|   
+-- bootstrap
|   |
|   \-- accordion
|   \-- alert
|   \-- background
|   \-- badge
|   \-- border
|   \-- button
|   \-- card
|   \-- dropdown
|   \-- form
|   \-- grid
|   \-- inputgroup
|   \-- modal
|   \-- nav
|   \-- offcanvas
|   \-- progress
|   \-- popover
|   \-- spinner
|   \-- texts
|   \-- tables
|   \-- toast
|   \-- tooltip
|   
+-- plugins
|   |
|   \-- autosize
|   \-- autosuggest
|   \-- calendar
|   \-- contextmenu
|   \-- datatable
|   \-- datepicker
|   \-- dropzone
|   \-- editor
|   \-- glide
|   \-- inputspinner
|   \-- lightbox
|   \-- list
|   \-- player
|   \-- notification
|   \-- progressbar
|   \-- rating
|   \-- scrollbar
|   \-- search
|   \-- select
|   \-- slider
|   \-- steps
|   \-- tags
|   \-- tour
|   \-- validation
|   \-- wizard
|   
+-- layout
    |
    \-- base
    \-- typography
    \-- main
    \-- nav.primary
    \-- nav.side
    \-- footer
    \-- print
    \-- settings
 
*/

@import 'utils/mixins';
@import 'utils/sizes';
@import 'utils/positions';
@import 'utils/shared';

@import 'themes/shared';
@import 'themes/dark.blue';
@import 'themes/light.blue';

@import 'bootstrap/accordion';
@import 'bootstrap/alert';
@import 'bootstrap/background';
@import 'bootstrap/badge';
@import 'bootstrap/border';
@import 'bootstrap/button';
@import 'bootstrap/card';
@import 'bootstrap/dropdown';
@import 'bootstrap/form';
@import 'bootstrap/grid';
@import 'bootstrap/inputgroup';
@import 'bootstrap/modal';
@import 'bootstrap/nav';
@import 'bootstrap/offcanvas';
@import 'bootstrap/progress';
@import 'bootstrap/popover';
@import 'bootstrap/spinner';
@import 'bootstrap/texts';
@import 'bootstrap/tables';
@import 'bootstrap/toast';
@import 'bootstrap/tooltip';
@import 'plugins/glide';
@import 'plugins/tour';

@import 'layout/base';
@import 'layout/typography';
@import 'layout/main';
@import 'layout/nav.primary';
@import 'layout/nav.side';
@import 'layout/footer';
@import 'layout/print';
@import 'layout/settings';
