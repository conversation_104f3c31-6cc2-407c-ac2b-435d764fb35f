/*
*
* Nav Primary
*
* Template styles for both vertical and horizontal navigation as well as mobile menu.
*
*/

html[data-placement="horizontal"],
html[data-placement="vertical"] {
  .nav-container {
    position: fixed;
    top: 0;
    bottom: 0;
    z-index: 1001;
    background-repeat: no-repeat;
    background-size: cover;
    width: var(--nav-size);
    background-position: center;
    background-image: linear-gradient(
      160deg,
      var(--gradient-1),
      var(--gradient-1),
      var(--gradient-2),
      var(--gradient-3)
    );

    .nav-shadow {
      width: 100%;
      height: 100%;
      position: absolute;
      box-shadow: var(--menu-shadow);
      pointer-events: none;
      z-index: 1001;
    }

    .mobile-buttons-container {
      display: none;
    }

    .menu-container {
      .menu {
        display: none;
        margin: 0 auto;
        padding: 0;
        list-style: none;

        ul {
          list-style: none;
        }

        /* All li items - all of the items */
        li {
          a {
            text-decoration: initial;
            position: relative;
            width: 100%;
            transition: opacity var(--transition-time);
            display: inline-block;
            &:after {
              content: none;
            }
            .icon,
            .label {
              vertical-align: middle;
            }
          }
        }

        /* Only top level li items - main menu items */
        & > li {
          margin: 0;
          position: relative;

          a {
            color: var(--light-text);
          }

          .icon {
            margin-right: 8px;
            display: inline-block;
            margin-top: -1px;
            margin-left: -1px;
          }
        }

        /* Only sub level li items - sub menu items */
        & > li li {
          padding: 0;
          a {
            padding: 0.5rem 1rem;
            .label {
              margin-right: 1rem;
            }
          }
        }

        &.show {
          display: flex;
        }
      }

      &.os-host {
        padding-left: 0;
        padding-right: 0;
      }
    }

    .menu-icons {
      display: flex !important;
      justify-content: center;
      cursor: pointer;

      .list-inline-item {
        margin-left: 0;
        margin-right: 0;
      }

      i {
        font-size: 18px !important;
      }

      & > li {
        height: 38px;
      }

      & > li > a {
        color: var(--light-text);
        font-size: 18px;
        padding: 0.25rem 0.5rem;
        transition: opacity var(--transition-time);
        &:hover {
          @include shadow-basic();
          background: rgba(255, 255, 255, 0.05);
        }
      }

      .notification-dot {
        width: 3px;
        height: 3px;
        background: var(--light-text);
        top: -2px;
        right: 0;
      }
    }

    .user-container {
      flex-direction: column;

      .user {
        text-align: center;
        transition: opacity var(--transition-time);
        border-radius: var(--border-radius-md);
        &::after {
          content: initial;
        }

        &:hover {
          @include shadow-basic();
          background: rgba(255, 255, 255, 0.05);
        }

        .profile {
          margin: 0 auto;
          margin-bottom: 5px;
          width: 60px;
          height: 60px;
          border-radius: var(--border-radius-xl);
        }

        .name {
          color: var(--light-text);
          line-height: 1;
        }
      }

      .user-menu {
        i {
          margin-right: 3px;
          line-height: 1.2;
        }
      }
    }

    .language-switch-container {
      order: 2;
      display: flex !important;

      .dropdown-menu {
        min-width: 65px;
      }

      .language-button {
        padding: 0 8px;
        padding: 5px 14px;
        border-radius: var(--border-radius-md);
        &:hover {
          @include shadow-basic();
          background: rgba(255, 255, 255, 0.05);
        }
      }
    }

    .logo {
      img,
      .img {
        width: 100px;
        min-height: 35px;
        object-position: left;
        object-fit: cover;
        transition: width var(--transition-time), height var(--transition-time);
      }

      .img {
        background-repeat: no-repeat;
      }
    }

    .dropdown-menu {
      border: initial;
      margin-top: initial;
      border-radius: var(--border-radius-md);
      @include shadow-deep;
      a {
        color: var(--alternate);
      }

      &.wide {
        width: calc(var(--nav-size) - calc(var(--main-spacing-vertical)));
        padding: 20px 30px 20px 30px;
      }
    }
  }
}

/*
* Horizontal
* Styles for horizontal menu
*/
html[data-placement="horizontal"] {
  .nav-container {
    height: var(--nav-size-slim);
    right: 0;
    left: 0;
    width: 100%;
    justify-content: center;
    flex-direction: row;
    padding-left: var(--main-spacing-horizontal);
    padding-right: var(--main-spacing-horizontal);
    border-top-right-radius: 0;
    border-top-left-radius: 0;
    border-bottom-right-radius: var(--border-radius-lg);
    border-bottom-left-radius: var(--border-radius-lg);

    .nav-content {
      flex-direction: row;
      align-items: center;
      width: 100%;
    }

    .nav-shadow {
      border-top-right-radius: 0;
      border-top-left-radius: 0;
      border-bottom-right-radius: var(--border-radius-lg);
      border-bottom-left-radius: var(--border-radius-lg);
    }

    .menu-container {
      margin: initial;
      height: 100%;

      .menu {
        margin-left: 5rem;
        margin-right: 2rem;
        height: 100%;

        @include respond-below(xl) {
          margin-left: 2rem;
          margin-right: 2rem;
        }

        @include respond-below(lg) {
          margin-left: 0.75rem;
          margin-right: 0.75rem;
        }

        /* All li items - all of the items main and sub */
        li {
          a {
            white-space: nowrap;
          }
        }

        /* Only top level li items - main menu items */
        & > li {
          & > a {
            padding: 0 1.5rem;
            height: 100%;
            display: inline-flex;
            align-items: center;
            @include respond-below(xl) {
              padding: 0 1rem;
            }
            @include respond-below(lg) {
              padding: 0 0.75rem;
            }
            &.active,
            &:hover {
              @include shadow-basic();
              background: rgba(255, 255, 255, 0.05);
            }
          }

          & > a {
            border-bottom-left-radius: var(--border-radius-md);
            border-bottom-right-radius: var(--border-radius-md);
          }
          .label {
            max-width: 100%;
          }
        }

        /* Only sub level li items - sub menu items */
        & ul li ul {
          left: calc(100% + 20px) !important;
        }
        /* All ul items except first one */
        ul {
          left: calc(100% + 1rem);
          background: var(--foreground);
          position: absolute;
          top: 0;
          border-top-left-radius: var(--border-radius-md);
          border-top-right-radius: var(--border-radius-md);
          border-bottom-right-radius: var(--border-radius-md);
          border-bottom-left-radius: var(--border-radius-md);
          border: initial;
          padding: 1rem;

          @include shadow-deep;
          a {
            color: var(--body);
            border-radius: var(--border-radius-sm);

            &.active .label::after {
              background: var(--primary);
            }

            &.active {
              background: rgba(var(--separator-light-rgb), 0.5);
              color: var(--primary);
            }

            // transition: color var(--transition-time);
            &:hover {
              color: var(--primary);
              opacity: 1;
              background: rgba(var(--separator-light-rgb), 0.5);
            }
          }
        }

        /* Only second level of ul items */
        & > li > ul {
          border-bottom-right-radius: var(--border-radius-md);
          border-bottom-left-radius: var(--border-radius-md);
          border-top-left-radius: var(--border-radius-md);
          border-top-right-radius: var(--border-radius-md);
        }
      }
    }

    .logo {
      margin-top: 0;
      margin-bottom: 0;
      text-align: initial;
    }

    .user-container {
      flex-direction: row;
      order: 3;
      align-items: center;
      height: 100%;

      .user {
        align-items: center;
        height: 100%;
        padding: 0 8px;
        margin-right: -8px;
        border-radius: initial;
        border-bottom-left-radius: var(--border-radius-md);
        border-bottom-right-radius: var(--border-radius-md);

        .profile {
          order: 1;
          width: var(--input-height);
          height: var(--input-height);
          margin-bottom: 0;
        }
        .name {
          margin-right: 10px;
          margin-bottom: 0;
        }
      }
    }

    .language-switch-container {
      order: 2;
      height: 100%;
      margin-right: 10px;

      @include respond-below(lg) {
        margin-right: 5px;
      }

      .dropdown-menu {
        min-width: 65px;
      }

      .language-button {
        height: 100%;
        padding: 0 8px;
        border-radius: initial;
        border-bottom-left-radius: var(--border-radius-md);
        border-bottom-right-radius: var(--border-radius-md);
        padding-top: 2px;
        &:hover {
          @include shadow-basic();
          background: rgba(255, 255, 255, 0.05);
        }
      }
    }

    .menu-icons {
      order: 1;
      margin: 0;
      margin-right: 10px;
      margin-left: auto;
      height: 100%;

      li {
        height: 100%;

        & > a {
          height: 100%;
          display: inline-flex;
          align-items: center;
          border-bottom-left-radius: var(--border-radius-md);
          border-bottom-right-radius: var(--border-radius-md);
        }
      }

      @include respond-below(lg) {
        margin-right: 5px;
      }
    }
  }
}

/*
* Vertical
* Styles for vertical and mobile menu
*/
html[data-placement="vertical"] .nav-container,
html[data-placement="horizontal"] .nav-container.mobile-side-ready,
html[data-placement="vertical"] .nav-container.mobile-side-ready {
  top: 0;
  width: var(--nav-size);
  height: 100%;
  padding-left: initial;
  padding-right: initial;
  border-top-left-radius: 0;
  border-top-right-radius: var(--border-radius-lg);
  border-bottom-right-radius: var(--border-radius-lg);
  border-bottom-left-radius: 0;
  flex-direction: column;
  justify-content: initial;

  .nav-shadow {
    border-top-left-radius: 0;
    border-top-right-radius: var(--border-radius-lg);
    border-bottom-right-radius: var(--border-radius-lg);
    border-bottom-left-radius: 0;
  }

  .nav-content {
    flex-direction: column;
    height: 100%;
    padding-top: var(--main-spacing-vertical);
    padding-bottom: var(--main-spacing-vertical);
    align-items: center;
    padding-right: initial !important;
    .mobile-buttons-container {
      display: none;
    }

    .menu-icons {
      display: flex !important;
      justify-content: center;
      margin: 0;
      order: 3;
      height: auto;

      li {
        & > a {
          padding: 0.25rem 0.5rem;
          border-radius: var(--border-radius-md);
          height: 100%;
          display: inline-flex;
          align-items: center;
        }
      }
    }

    .logo {
      margin-bottom: 20px;
      text-align: center;
      a {
        overflow: hidden;
        display: inline-block;
        width: 100px;
      }
    }

    .language-switch-container {
      height: auto;
      margin-right: initial;
      display: flex !important;

      @include respond-below(lg) {
        margin-right: initial;
      }
      .language-button {
        height: auto;
        padding: 5px 14px;
        border-radius: var(--border-radius-md);
      }
    }

    .user-container {
      order: 1;
      display: flex !important;
      flex-direction: column;
      height: auto;
      min-height: 100px;
      .user {
        flex-direction: column;
        margin-bottom: 5px;
        height: auto;
        padding: 10px;
        border-radius: var(--border-radius-md);
        margin-right: 0;

        .name {
          margin-right: 0;
        }

        .profile {
          order: initial;
          width: 50px;
          height: 50px;
          margin-bottom: 5px;
        }
      }
    }

    .menu-container {
      display: flex !important;
      align-self: flex-start;
      order: 3;
      margin-top: 2rem;
      margin-bottom: 1rem;
      width: 16rem;
      margin-left: 1rem;
      margin-right: 1rem;

      .menu {
        width: 100%;
        margin-left: 0;
        margin-right: 0;
        padding-left: 1rem;
        padding-right: 1rem;
        cursor: pointer;

        ul {
          box-shadow: initial !important;
          background: initial;
          position: initial;
          border-radius: initial;
          padding: initial;
          padding-left: 2rem;
        }

        /* All li items - all of the items main and sub */
        li {
          a {
            border-radius: var(--border-radius-lg);
            width: 100%;
            color: var(--light-text);
            //transition: color var(--transition-time);
            margin-bottom: 0.1rem;
            margin-top: 0.1rem;

            .icon {
              color: var(--light-text);
            }

            &.active {
              background: rgba(var(--light-text-rgb), 0.1);
              @include shadow-basic();
            }

            &:hover {
              background: rgba(var(--light-text-rgb), 0.1);
              color: var(--light-text);
            }
          }
        }

        /* Only top level li items - main menu items */
        & > li {
          margin: 0;
          padding: 0;

          a {
            padding: 0.75rem 1rem;
          }
        }

        /* Only sub level li items - sub menu items */
        & > li li {
          a {
            padding: 0.5rem 1rem;
          }

          ul {
            padding-left: 1rem;
          }
        }

        &.show {
          display: inline-block;
        }
      }
    }
  }

  .btn {
    transition-property: background-color, background-image, background;
  }
}

html[data-placement="horizontal"]:not([data-mobile="true"]) {
  .menu-icons,
  .user-container {
    .user {
      .name {
        @include respond-below(lg) {
          display: none;
        }
      }
    }
  }
}

// Mobile Menu
html[data-placement="vertical"][data-dimension="mobile"],
html[data-placement="horizontal"][data-dimension="mobile"] {
  .nav-container {
    .mobile-buttons-container {
      margin-left: auto;
      height: 100%;
      align-items: center;
      display: flex;

      & > a,
      & > .dropdown > a {
        color: var(--light-text);
        padding: 0.25rem 0.5rem;
        height: 100%;
        align-items: center;
        display: flex;
        border-bottom-left-radius: var(--border-radius-md);
        border-bottom-right-radius: var(--border-radius-md);
        &:hover {
          @include shadow-basic();
          background: rgba(255, 255, 255, 0.05);
        }

        i {
          font-size: 18px;
        }
      }

      & > .dropdown {
        height: 100%;
      }
    }

    .menu-icons,
    .language-switch-container,
    .user-container,
    .menu-container {
      display: none !important;
    }

    // Hiding menu to top
    &.mobile-top-out {
      transition-property: top;
      transition-duration: var(--transition-time-short);
      top: -100px;
    }

    &.mobile-side-ready {
      transition-property: left;
      transition-duration: initial;
      left: calc(var(--nav-size) * -1);
    }

    &.mobile-side-in {
      transition-property: left;
      transition-duration: var(--transition-time);
      left: 0;
    }

    &.mobile-side-out {
      transition-property: left;
      transition-duration: var(--transition-time-short);
      left: calc(var(--nav-size) * -1);
    }

    &.mobile-top-ready {
      transition-property: none;
      transition-duration: initial;
      top: -100px;
    }

    &.mobile-top-in {
      transition-property: top;
      transition-duration: var(--transition-time);
      top: 0;
    }
  }
}

// Unpinned vertical menu
@mixin vertical-menu-unpinned() {
  width: var(--nav-size-slim);
  .nav-content {
    .menu-container {
      align-self: center;
      overflow-x: hidden;
      width: 80px;
      margin-left: 0;
      margin-right: 0;
      .menu a {
        white-space: nowrap;
        &:before,
        .label {
          transition: initial;
          opacity: 0;
        }
      }
    }

    .logo a {
      width: 30px;
    }

    .menu-icons,
    .language-switch-container {
      visibility: hidden;
      opacity: 0;
    }

    .user {
      .profile {
        width: 30px;
        height: 30px;
      }

      .name {
        visibility: hidden;
        opacity: 0;
      }
    }
  }
}

html[data-placement="vertical"][data-behaviour="unpinned"]:not([data-menu-animate="show"]) .nav-container {
  @include vertical-menu-unpinned();
}

html[data-placement="vertical"][data-behaviour="unpinned"] {
  .nav-container .nav-content .menu-container .menu ul {
    transition-duration: initial;
  }
}

html[data-placement="vertical"][data-behaviour="unpinned"][data-menu-animate="hidden"],
html[data-placement="vertical"][data-behaviour="unpinned"][data-menu-animate="show"] {
  .nav-container .nav-content .menu-container .menu ul {
    transition-duration: var(--transition-time);
  }
}

html[data-placement="vertical"][data-behaviour="unpinned"][data-menu-animate="hidden"] .nav-container,
html[data-placement="vertical"][data-behaviour="unpinned"][data-menu-animate="show"] .nav-container {
  .nav-content {
    .menu-container {
      .menu a {
        &:before,
        .label {
          transition: opacity var(--transition-time);
        }
      }
    }
  }
}

// Semihidden vertical menu animation
html[data-placement="vertical"][data-behaviour="unpinned"][data-menu-animate="hidden"] .nav-container {
  transition-duration: var(--transition-time);
  transition-property: width;

  .nav-content {
    .menu-container {
      transition-duration: var(--transition-time);
      transition-property: opacity, width;
    }

    .logo a {
      transition-duration: var(--transition-time);
      transition-property: width;
    }

    .user {
      .profile {
        transition-duration: var(--transition-time);
        transition-property: width, height;
      }
    }
  }
}

// Semihidden vertical menu show
html[data-placement="vertical"][data-behaviour="unpinned"][data-menu-animate="show"] .nav-container {
  transition-duration: var(--transition-time);
  transition-property: width;
  .nav-content {
    .menu-container {
      width: 16rem;
      margin-left: 1rem;
      margin-right: 1rem;
      overflow-x: hidden;
      transition-duration: var(--transition-time);
      transition-property: opacity, width;
      align-self: center;

      .menu a {
        white-space: nowrap;
        transition-delay: 0.1s;
        &:before,
        .label {
          transition-delay: 0.1s;
        }
      }
    }

    .menu-icons,
    .language-switch-container {
      visibility: visible;
      opacity: 1;
      transition-duration: var(--transition-time-short);
      transition-property: opacity;
      transition-delay: var(--transition-time-short);
    }

    .user {
      .profile {
        transition-duration: var(--transition-time);
        transition-property: width, height;
      }

      .name {
        visibility: visible;
        opacity: 1;
        transition-duration: var(--transition-time-short);
        transition-property: opacity;
        transition-delay: var(--transition-time-short);
      }
    }

    .logo a {
      transition-duration: var(--transition-time);
      transition-property: width;
    }
  }
}

// Horizontal opacity animation
.opacityIn {
  animation-duration: var(--transition-time);
}

@keyframes opacityIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
  0% {
    opacity: 0;
  }
}

.opacityIn {
  animation-name: opacityIn;
}

// Scrollbar
html[data-placement="vertical"] .nav-container,
html[data-placement="horizontal"] .nav-container.mobile-side-ready,
html[data-placement="vertical"] .nav-container.mobile-side-ready {
  .menu-container {
    &.os-theme-dark > .os-scrollbar > .os-scrollbar-track > .os-scrollbar-handle {
      background: rgba(var(--light-text-rgb), 0.3);
    }

    &.os-theme-dark > .os-scrollbar:hover > .os-scrollbar-track > .os-scrollbar-handle {
      background: rgba(var(--light-text-rgb), 0.4);
    }

    &.os-theme-dark > .os-scrollbar > .os-scrollbar-track > .os-scrollbar-handle.active {
      background: rgba(var(--light-text-rgb), 0.4);
    }

    &.os-theme-dark > .os-scrollbar > .os-scrollbar-track,
    .os-theme-light > .os-scrollbar > .os-scrollbar-track {
      background: rgba(var(--light-text-rgb), 0.1);
    }
  }
}

/*
Arrows collapse for vertical menu
*/
.menu {
  a[data-bs-toggle="collapse"] {
    &:before {
      content: "";
      position: absolute;
      width: 5px;
      height: 5px;
      border-top: 1px solid var(--light-text);
      border-right: 1px solid var(--light-text);
      transform: rotate(45deg);
      bottom: initial;
      top: 14px;
      left: initial;
      right: 1rem;
      top: calc(50% - 3px);
    }

    &[aria-expanded="true"] {
      &:before {
        transform: rotate(135deg);
        top: 13px;
        top: calc(50% - 4px);
      }
    }
  }
}

/*
Arrows collapse for horizontal menu
*/
.menu {
  a.dropdown-toggle {
    &:before {
      content: "";
      width: 5px;
      height: 5px;
      border-top: 1px solid var(--light-text);
      border-right: 1px solid var(--light-text);
      transform: rotate(45deg);
      order: 3;
      margin-left: 5px;
      margin-top: 3px;
    }
  }

  li.dropdown.show {
    & > a.dropdown-toggle {
      &:before {
        content: "";
        transform: rotate(135deg);
        margin-top: 1px;
      }
    }
  }

  // Only top level
  & > li > a.show {
    &:before {
      content: "";
      transform: rotate(135deg);
      margin-top: 1px;
    }
  }

  // Only sub level
  & > li li {
    a.dropdown-toggle {
      &:before {
        position: absolute;
        top: calc(50% - 2px);
        border-top: 1px solid var(--alternate);
        border-right: 1px solid var(--alternate);
        margin-top: initial;
        right: 12px;
      }
    }

    &.dropdown.show {
      & > a.dropdown-toggle {
        &:before {
          content: "";
          top: calc(50% - 3px);
          margin-top: initial;
          right: 11px;
        }
      }
    }
  }
}

// Color, Pin & Dropdown
#colorButton .light {
  display: none;
}

#pinButton.disabled {
  cursor: default;
  i {
    opacity: 0.5;
  }
}

.notification-dropdown .scroll {
  height: 185px;
}

html[data-color*="light"] {
  #colorButton .light {
    display: inline-block;
  }
  #colorButton .dark {
    display: none;
  }
}

html[data-color*="dark"] {
  #colorButton .light {
    display: none;
  }
  #colorButton .dark {
    display: inline-block;
  }
}

html:not([data-scrollspy="true"]) {
  #scrollSpyButton,
  #scrollSpyDropdown {
    display: none !important;
  }
}

// Pinning
html[data-behaviour="pinned"] {
  .pin-button {
    .pin {
      display: none;
    }

    .unpin {
      display: inline-block;
    }
  }
}

html[data-behaviour="unpinned"] {
  .pin-button {
    .pin {
      display: inline-block;
    }

    .unpin {
      display: none;
    }
  }
}

html[data-placement="horizontal"][data-behaviour="unpinned"]:not([data-mobile="true"]) {
  .nav-container {
    transition-property: top;
    transition-duration: var(--transition-time-short);
  }
}

html[data-placement="horizontal"][data-behaviour="unpinned"][data-menu-animate="hidden"]:not([data-mobile="true"]) {
  .nav-container {
    top: -100px;
  }
}

// Mega Menu
html[data-placement="horizontal"]:not([data-mobile="true"]) {
  .nav-container {
    .menu {
      // First
      .mega > ul.show {
        display: flex;
        flex-wrap: wrap;
        padding: 1rem 0;
        width: calc(100% - calc(var(--main-spacing-horizontal) * 2));

        & > li > a {
          color: var(--primary) !important;
          background: initial !important;
        }

        a {
          padding: 0.35rem 1rem !important;
          &.dropdown-toggle {
            background: initial !important;
            pointer-events: none;
            cursor: default;
            &:before {
              border: initial !important;
            }
          }
        }
      }

      // All ul tags except first one
      .mega > ul ul {
        position: static !important;
        box-shadow: initial !important;
      }

      // Second level li
      .mega > ul > li {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
      }

      .mega > ul > li > ul {
        padding: initial;
      }

      .mega > ul > li ul {
        padding-top: initial;
        padding-bottom: initial;
      }
    }
  }
}

html[data-placement="horizontal"][data-mobile="true"] {
  .nav-container .menu > li > ul {
    margin-top: 0;
  }

  .nav-container .menu > li > a {
    display: inline-block;
  }
}

// Nav Dark Light Override
html[data-navcolor="light"] {
  .nav-shadow {
    box-shadow: var(--menu-shadow-navcolor) !important;
  }

  .nav-container {
    background: var(--background-navcolor-light);
  }

  .nav-container .menu-container .menu a {
    color: var(--alternate) !important;

    &.active,
    &:hover {
      background: rgba(0, 0, 0, 0.05) !important;
    }

    .icon {
      color: var(--alternate) !important;
    }
  }

  .nav-container .menu-icons > li > a {
    color: var(--alternate);
    &:hover {
      background: rgba(0, 0, 0, 0.05) !important;
    }
  }

  .nav-container .language-switch-container .language-button {
    color: var(--alternate) !important;
    &:hover {
      background: rgba(0, 0, 0, 0.05) !important;
    }
  }

  .nav-container .mobile-buttons-container > a,
  .nav-container .mobile-buttons-container > .dropdown > a {
    color: var(--primary) !important;
    &:hover {
      background: rgba(0, 0, 0, 0.05) !important;
    }
  }

  .nav-container .menu-container .menu > li > a .icon {
    color: var(--primary);
  }

  .nav-container .user-container .user .name {
    color: var(--alternate);
  }

  .nav-container .user-container .user {
    &:hover {
      background: rgba(0, 0, 0, 0.05) !important;
    }
  }

  .nav-container .nav-content .menu-container .menu ul a {
    color: var(--alternate);
  }

  .nav-container .menu-container .menu a.active .label::after {
    background: var(--primary);
  }

  .menu a[data-bs-toggle="collapse"]:before {
    border-color: var(--alternate);
  }

  .menu a.dropdown-toggle:before {
    border-top: 1px solid var(--alternate);
    border-right: 1px solid var(--alternate);
  }

  .menu-container {
    &.os-theme-dark > .os-scrollbar > .os-scrollbar-track > .os-scrollbar-handle {
      background: rgba(var(--alternate-rgb), 0.3) !important;
    }

    &.os-theme-dark > .os-scrollbar:hover > .os-scrollbar-track > .os-scrollbar-handle {
      background: rgba(var(--alternate-rgb), 0.4) !important;
    }

    &.os-theme-dark > .os-scrollbar > .os-scrollbar-track > .os-scrollbar-handle.active {
      background: rgba(var(--alternate-rgb), 0.4) !important;
    }

    &.os-theme-dark > .os-scrollbar > .os-scrollbar-track,
    .os-theme-light > .os-scrollbar > .os-scrollbar-track {
      background: rgba(var(--alternate-rgb), 0.1) !important;
    }
  }
}

html[data-navcolor="dark"] {
  .nav-shadow {
    box-shadow: var(--menu-shadow-navcolor);
  }

  .nav-container {
    background: var(--background-navcolor-dark);
  }

  .nav-container .menu-container .menu > li > a {
    color: var(--light-text);
  }

  .nav-container .language-switch-container .language-button {
    color: var(--light-text) !important;
    &:hover {
      background: rgba(255, 255, 255, 0.05) !important;
    }
  }

  .nav-container .mobile-buttons-container > a,
  .nav-container .mobile-buttons-container > .dropdown > a {
    color: var(--light-text) !important;
    &:hover {
      background: rgba(255, 255, 255, 0.05) !important;
    }
  }

  .nav-container .menu-icons > li > a {
    color: var(--light-text);
  }

  .nav-container .menu-container .menu > li > a .icon {
    color: var(--light-text);
  }

  .nav-container .user-container .user .name {
    color: var(--light-text);
  }

  .nav-container .menu-container .menu a.active .label::after {
    background: var(--light-text);
  }

  .nav-container .menu-container .menu ul {
    background: var(--background-navcolor-dark) !important;
  }

  .nav-container .menu-container .menu a {
    color: var(--light-text) !important;
    &.active,
    &:hover {
      background: rgba(255, 255, 255, 0.05) !important;
    }
  }
}

.collapsing {
  transition-duration: var(--transition-time);
}

.logo-default {
  width: 100px;
  min-height: 35px;
  object-position: left;
  object-fit: cover;
  background-repeat: no-repeat;
}

// Setting logo path values manually because of Safari url and css variable bug
html[data-color="light-blue"],
html[data-color="light-green"],
html[data-color="light-red"],
html[data-color="light-pink"],
html[data-color="light-purple"],
html[data-color="dark-blue"],
html[data-color="dark-green"],
html[data-color="dark-red"],
html[data-color="dark-pink"],
html[data-color="dark-purple"] {
  .logo .img {
    background-image: url(../img/logo/logo-light.svg);
  }
}

html[data-color="light-blue"] {
  .logo-default {
    background-image: url(../img/logo/logo-blue-light.svg);
  }
}

html[data-color="light-green"] {
  .logo-default {
    background-image: url(../img/logo/logo-green-light.svg);
  }
}

html[data-color="light-red"] {
  .logo-default {
    background-image: url(../img/logo/logo-red-light.svg);
  }
}

html[data-color="light-pink"] {
  .logo-default {
    background-image: url(../img/logo/logo-pink-light.svg);
  }
}

html[data-color="light-purple"] {
  .logo-default {
    background-image: url(../img/logo/logo-purple-light.svg);
  }
}

html[data-color="dark-blue"] {
  .logo-default {
    background-image: url(../img/logo/logo-blue-dark.svg);
  }
}

html[data-color="dark-green"] {
  .logo-default {
    background-image: url(../img/logo/logo-green-dark.svg);
  }
}

html[data-color="dark-red"] {
  .logo-default {
    background-image: url(../img/logo/logo-red-dark.svg);
  }
}

html[data-color="dark-pink"] {
  .logo-default {
    background-image: url(../img/logo/logo-pink-dark.svg);
  }
}

html[data-color="dark-purple"] {
  .logo-default {
    background-image: url(../img/logo/logo-purple-dark.svg);
  }
}

html[data-color="light-blue"][data-navcolor="light"],
html[data-color="dark-blue"][data-navcolor="light"] {
  .logo .img {
    background-image: url(../img/logo/logo-blue-light.svg);
  }
}

html[data-color="light-green"][data-navcolor="light"],
html[data-color="dark-green"][data-navcolor="light"] {
  .logo .img {
    background-image: url(../img/logo/logo-green-light.svg);
  }
}

html[data-color="light-red"][data-navcolor="light"],
html[data-color="dark-red"][data-navcolor="light"] {
  .logo .img {
    background-image: url(../img/logo/logo-red-light.svg);
  }
}

html[data-color="light-pink"][data-navcolor="light"],
html[data-color="dark-pink"][data-navcolor="light"] {
  .logo .img {
    background-image: url(../img/logo/logo-pink-light.svg);
  }
}

html[data-color="light-purple"][data-navcolor="light"],
html[data-color="dark-purple"][data-navcolor="light"] {
  .logo .img {
    background-image: url(../img/logo/logo-purple-light.svg);
  }
}

html[data-color="light-blue"][data-navcolor="dark"],
html[data-color="dark-blue"][data-navcolor="dark"] {
  .logo .img {
    background-image: url(../img/logo/logo-blue-dark.svg);
  }
}

html[data-color="light-green"][data-navcolor="dark"],
html[data-color="dark-green"][data-navcolor="dark"] {
  .logo .img {
    background-image: url(../img/logo/logo-green-dark.svg);
  }
}

html[data-color="light-red"][data-navcolor="dark"],
html[data-color="dark-red"][data-navcolor="dark"] {
  .logo .img {
    background-image: url(../img/logo/logo-red-dark.svg);
  }
}

html[data-color="light-pink"][data-navcolor="dark"],
html[data-color="dark-pink"][data-navcolor="dark"] {
  .logo .img {
    background-image: url(../img/logo/logo-pink-dark.svg);
  }
}

html[data-color="light-purple"][data-navcolor="dark"],
html[data-color="dark-purple"][data-navcolor="dark"] {
  .logo .img {
    background-image: url(../img/logo/logo-purple-dark.svg);
  }
}

.menu-container .label,
.user-container .name {
  font-family: var(--font-heading);
  font-size: 13px;
}
