/*
*
* Card
*
* Bootstrap card styles.
*
*/

.card {
  @include shadow-basic;
  background: var(--foreground);
  border-radius: var(--border-radius-lg);
  border: initial;
  &.no-shadow {
    box-shadow: initial !important;
  }

  .card-body,
  .card-footer,
  .card-header {
    padding: var(--card-spacing);
  }

  .half-padding {
    &.card-body,
    &.card-footer,
    &.card-header {
      padding: calc(var(--card-spacing) / 2);
    }
  }

  &.sm {
    .card-body,
    .card-footer,
    .card-header {
      padding: var(--card-spacing-sm);
    }
  }

  .card-header .handle {
    cursor: default;
  }

  .card-header {
    background: var(--foreground);
    border-color: rgba(var(--separator-rgb), 0.5);
  }

  .card-footer {
    background: initial;
    border-color: rgba(var(--separator-rgb), 0.3);
  }

  .card-img {
    border-radius: var(--border-radius-lg);
  }
  .card-img-top {
    width: 100%;
    border-radius: initial;
    border-top-left-radius: var(--border-radius-lg);
    border-top-right-radius: var(--border-radius-lg);
  }

  .card-img-overlay {
    background: rgba(0, 0, 0, 0.5);
    border-radius: var(--border-radius-lg);
  }

  .card-img-bottom {
    width: 100%;
    border-radius: initial;
    border-bottom-left-radius: var(--border-radius-lg);
    border-bottom-right-radius: var(--border-radius-lg);
  }

  .card-img-left {
    border-radius: initial;
    border-top-left-radius: var(--border-radius-lg);
    border-bottom-left-radius: var(--border-radius-lg);
    border-bottom-right-radius: initial;
    border-top-right-radius: initial;

    @include respond-below(sm) {
      border-radius: initial;
      border-bottom-left-radius: initial;
      border-top-left-radius: var(--border-radius-lg);
      border-top-right-radius: var(--border-radius-lg);
      border-bottom-right-radius: initial;
    }
  }

  [class*='card-img-horizontal'] {
    border-radius: initial;
    border-top-left-radius: var(--border-radius-lg);
    border-bottom-left-radius: var(--border-radius-lg);
    border-bottom-right-radius: initial;
    border-top-right-radius: initial;
    height: 100%;
  }

  .card-img-horizontal-sm {
    @include respond-below(xs) {
      border-radius: initial;
      border-bottom-left-radius: initial;
      border-top-left-radius: var(--border-radius-lg);
      border-top-right-radius: var(--border-radius-lg);
      border-bottom-right-radius: initial;
    }
  }

  .card-img-horizontal-md {
    @include respond-below(sm) {
      border-radius: initial;
      border-bottom-left-radius: initial;
      border-top-left-radius: var(--border-radius-lg);
      border-top-right-radius: var(--border-radius-lg);
      border-bottom-right-radius: initial;
    }
  }

  .card-img-horizontal-lg {
    @include respond-below(md) {
      border-radius: initial;
      border-bottom-left-radius: initial;
      border-top-left-radius: var(--border-radius-lg);
      border-top-right-radius: var(--border-radius-lg);
      border-bottom-right-radius: initial;
    }
  }

  .card-img-horizontal-xl {
    @include respond-below(lg) {
      border-radius: initial;
      border-bottom-left-radius: initial;
      border-top-left-radius: var(--border-radius-lg);
      border-top-right-radius: var(--border-radius-lg);
      border-bottom-right-radius: initial;
    }
  }

  .card-footer {
    border-bottom-right-radius: var(--border-radius-lg);
    border-bottom-left-radius: var(--border-radius-lg);
  }

  .card[class*='border'] {
    border: 1px solid var(--separator);
  }

  .card-header:first-child {
    border-top-left-radius: var(--border-radius-lg);
    border-top-right-radius: var(--border-radius-lg);
  }

  .card-img-overlay {
    padding: var(--card-spacing);
  }

  .card-top-buttons {
    right: 0;
    top: 0;

    @include respond-below(xs) {
      padding: 0.35rem;
    }
  }
}

.card.active,
.card.selected,
.card.activatable.context-menu-active {
  @include shadow-and-border-active-before;
}

.card .card {
  border-radius: var(--border-radius-md);
}

.card-top-buttons {
  position: absolute;
  right: 0;
  top: 0;

  .btn {
    color: var(--primary) !important;
  }
}

.card-deck {
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-flow: row wrap;
  flex-flow: row wrap;
  margin-right: -20px;
  margin-left: -20px;

  @include respond-below(sm) {
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    margin-right: initial;
    margin-left: initial;
  }

  .card {
    margin-right: 20px;
    margin-left: 20px;

    @include respond-below(sm) {
      margin-right: initial;
      margin-left: initial;
    }
  }
}

.bg-primary {
  .card-header {
    background-color: var(--primary);
  }
}

.bg-secondary {
  .card-header {
    background-color: var(--secondary);
  }
}

.bg-tertiary {
  .card-header {
    background-color: var(--tertiary);
  }
}

.bg-quaternary {
  .card-header {
    background-color: var(--quaternary);
  }
}

.bg-warning {
  .card-header {
    background-color: var(--warning);
  }
}

.bg-danger {
  .card-header {
    background-color: var(--danger);
  }
}

.bg-success {
  .card-header {
    background-color: var(--success);
  }
}

.bg-info {
  .card-header {
    background-color: var(--info);
  }
}

.bg-light {
  background-color: var(--background-light) !important;
  .card-header {
    background-color: var(--background-light) !important;
    border-color: rgba(var(--muted-rgb), 0.6);
  }
  .card-footer {
    border-color: rgba(var(--muted-rgb), 0.6);
  }
}

.bg-dark {
  .card-header {
    background-color: var(--dark);
  }
}

.border-primary.card {
  border: 1px solid var(--primary);
  .card-header {
    border-color: var(--primary);
  }
}

.border-secondary {
  border: 1px solid var(--secondary);
  .card-header {
    border-color: var(--secondary);
  }
}

.border-tertiary {
  border: 1px solid var(--tertiary);
  .card-header {
    border-color: var(--tertiary);
  }
}

.border-quaternary {
  border: 1px solid var(--quaternary);
  .card-header {
    border-color: var(--quaternary);
  }
}

.border-info {
  border: 1px solid var(--info);
  .card-header {
    border-color: var(--info);
  }
}

.border-success {
  border: 1px solid var(--success);
  .card-header {
    border-color: var(--success);
  }
}

.border-danger {
  border: 1px solid var(--danger);
  .card-header {
    border-color: var(--danger);
  }
}

.border-light {
  border: 1px solid var(--light);
  .card-header {
    border-color: var(--light);
  }
}

.border-dark {
  border: 1px solid var(--dark);
  .card-header {
    border-color: var(--dark);
  }
}

.border-warning {
  border: 1px solid var(--warning);
  .card-header {
    border-color: var(--warning);
  }
}

.p-card {
  padding: var(--card-spacing);
}

.pe-card {
  padding-right: var(--card-spacing);
}

.ps-card {
  padding-left: var(--card-spacing);
}

.pt-card {
  padding-top: var(--card-spacing);
}

.pb-card {
  padding-bottom: var(--card-spacing);
}

.m-card {
  margin: var(--card-spacing);
}

.mb-card {
  margin-bottom: var(--card-spacing);
}

.mt-card {
  margin-top: var(--card-spacing);
}

.ms-card {
  margin-left: var(--card-spacing);
}

.me-card {
  margin-right: var(--card-spacing);
}

.list-group-item.active {
  background: var(--primary);
  border-color: var(--primary);
  color: var(--light-text);

  * {
    color: var(--light-text);
  }
}

.list-group-item-action:hover,
.list-group-item-action:focus {
  background: var(--primary);
  border-color: var(--primary);
  color: var(--light-text);

  * {
    color: var(--light-text);
  }
}

.list-group-item {
  background: var(--foreground);
  color: var(--body);
  border-color: rgba(var(--separator-rgb), 0.5);
  padding: var(--card-spacing-sm) var(--card-spacing);

  &.list-group-item-primary {
    background: var(--primary);
  }
  &.list-group-item-secondary {
    background: var(--secondary);
  }
  &.list-group-item-success {
    background: var(--success);
  }
  &.list-group-item-danger {
    background: var(--danger);
  }
  &.list-group-item-warning {
    background: var(--warning);
  }
  &.list-group-item-info {
    background: var(--info);
  }
  &.list-group-item-light {
    background: var(--light);
  }
  &.list-group-item-dark {
    background: var(--dark);
    color: var(--light);
  }
}

.list-group-item.disabled,
.list-group-item:disabled {
  color: var(--muted);
  background: var(--foreground);
}

.list-group-item:first-child {
  border-top-left-radius: var(--border-radius-md);
  border-top-right-radius: var(--border-radius-md);
}
.list-group-item:last-child {
  border-bottom-right-radius: var(--border-radius-md);
  border-bottom-left-radius: var(--border-radius-md);
}

.list-group-horizontal > .list-group-item:first-child {
  border-top-left-radius: var(--border-radius-md);
  border-bottom-left-radius: var(--border-radius-md);
  border-top-right-radius: initial;
  border-bottom-right-radius: initial;
}

.list-group-horizontal > .list-group-item:last-child {
  border-top-left-radius: initial;
  border-bottom-left-radius: initial;
  border-top-right-radius: var(--border-radius-md);
  border-bottom-right-radius: var(--border-radius-md);
}

@include respond-above(xs) {
  .list-group-horizontal-sm > .list-group-item:first-child {
    border-top-left-radius: var(--border-radius-md);
    border-bottom-left-radius: var(--border-radius-md);
    border-top-right-radius: initial;
    border-bottom-right-radius: initial;
  }
  .list-group-horizontal-sm > .list-group-item:last-child {
    border-top-left-radius: initial;
    border-bottom-left-radius: initial;
    border-top-right-radius: var(--border-radius-md);
    border-bottom-right-radius: var(--border-radius-md);
  }
}

@include respond-above(sm) {
  .list-group-horizontal-md > .list-group-item:first-child {
    border-top-left-radius: var(--border-radius-md);
    border-bottom-left-radius: var(--border-radius-md);
    border-top-right-radius: initial;
    border-bottom-right-radius: initial;
  }
  .list-group-horizontal-md > .list-group-item:last-child {
    border-top-left-radius: initial;
    border-bottom-left-radius: initial;
    border-top-right-radius: var(--border-radius-md);
    border-bottom-right-radius: var(--border-radius-md);
  }
}

@include respond-above(md) {
  .list-group-horizontal-lg > .list-group-item:first-child {
    border-top-left-radius: var(--border-radius-md);
    border-bottom-left-radius: var(--border-radius-md);
    border-top-right-radius: initial;
    border-bottom-right-radius: initial;
  }
  .list-group-horizontal-lg > .list-group-item:last-child {
    border-top-left-radius: initial;
    border-bottom-left-radius: initial;
    border-top-right-radius: var(--border-radius-md);
    border-bottom-right-radius: var(--border-radius-md);
  }
}

@include respond-above(lg) {
  .list-group-horizontal-xl > .list-group-item:first-child {
    border-top-left-radius: var(--border-radius-md);
    border-bottom-left-radius: var(--border-radius-md);
    border-top-right-radius: initial;
    border-bottom-right-radius: initial;
  }
  .list-group-horizontal-xl > .list-group-item:last-child {
    border-top-left-radius: initial;
    border-bottom-left-radius: initial;
    border-top-right-radius: var(--border-radius-md);
    border-bottom-right-radius: var(--border-radius-md);
  }
}

@include respond-above(xl) {
  .list-group-horizontal-xxl > .list-group-item:first-child {
    border-top-left-radius: var(--border-radius-md);
    border-bottom-left-radius: var(--border-radius-md);
    border-top-right-radius: initial;
    border-bottom-right-radius: initial;
  }
  .list-group-horizontal-xxl > .list-group-item:last-child {
    border-top-left-radius: initial;
    border-bottom-left-radius: initial;
    border-top-right-radius: var(--border-radius-md);
    border-bottom-right-radius: var(--border-radius-md);
  }
}

.card.hover-border-primary {
  cursor: pointer;
  &:hover {
    @include shadow-and-border-active-before;
  }
}

.hover-img-scale-up {
  overflow: hidden;
  backface-visibility: hidden;
  transform: scale(1);
  img.scale {
    transition: transform var(--transition-time);
  }
  &:hover img.scale {
    transform: scale(1.1);
  }
}

.hover-img-scale-down {
  overflow: hidden;
  backface-visibility: hidden;
  transform: scale(1);
  img.scale {
    transition: transform var(--transition-time);
    transform: scale(1.15);
  }
  &:hover img.scale {
    transform: scale(1.05);
  }
}

.hover-reveal {
  .reveal-content {
    opacity: 0;
    transition: opacity var(--transition-time);
  }

  &:hover {
    .reveal-content {
      opacity: 1;
    }
  }
}

@include respond-below(md) {
  .hover-reveal {
    .reveal-content {
      opacity: 1;
    }
  }
}

.hover-reveal-buttons {
  .lower-opacity {
    transition-property: opacity;
    transition-duration: var(--transition-time);
  }

  &:hover {
    .lower-opacity {
      opacity: 0.35;
    }
  }
}
