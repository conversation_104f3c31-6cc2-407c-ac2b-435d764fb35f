/*
*
* Mixins
*
* Root breakpoints and Sass functions.
*
*/

@mixin shadow-and-border() {
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.03) !important;
}

@mixin shadow-and-border-active() {
  box-shadow: inset 0 0 0 1px rgba(var(--primary-rgb), 0.5), 0 4px 10px rgba(0, 0, 0, 0.03) !important;
}

@mixin shadow-and-border-active-before() {
  &:after {
    box-shadow: inset 0 0 0 1px rgba(var(--primary-rgb), 0.5), 0 4px 10px rgba(0, 0, 0, 0.03) !important;
    content: '';
    display: block;
    height: 100%;
    position: absolute;
    top: 0;
    width: 100%;
    border-radius: var(--border-radius-lg);
    z-index: 0;
    pointer-events: none;
  }
}

@mixin shadow-basic() {
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.03) !important;
}

@mixin shadow-deep() {
  box-shadow: 0 6px 10px rgba(0, 0, 0, 0.1) !important;
}

// Responsive Breakpoints
$breakpoints-reverse: (
  xs: 575.98px,
  sm: 767.98px,
  md: 991.98px,
  lg: 1199.98px,
  xl: 1399.98px,
);

$breakpoints: (
  xs: 576px,
  sm: 768px,
  md: 992px,
  lg: 1200px,
  xl: 1400px,
);

:root {
  --sm: 576px;
  --md: 768px;
  --lg: 992px;
  --xl: 1200px;
  --xxl: 1400px;
}

@mixin respond-above($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    $breakpoint-value: map-get($breakpoints, $breakpoint);
    @media (min-width: ($breakpoint-value)) {
      @content;
    }
  } @else {
    @warn "Invalid breakpoint: #{$breakpoint}.";
  }
}

@mixin respond-below($breakpoint) {
  @if map-has-key($breakpoints-reverse, $breakpoint) {
    $breakpoint-value: map-get($breakpoints-reverse, $breakpoint);

    @media (max-width: ($breakpoint-value)) {
      @content;
    }
  } @else {
    @warn "Invalid breakpoint: #{$breakpoint}.";
  }
}

@mixin respond-both($max-breakpoint, $min-breakpoint) {
  @if map-has-key($breakpoints-reverse, $max-breakpoint) and map-has-key($breakpoints, $min-breakpoint) {
    $max-value: map-get($breakpoints-reverse, $max-breakpoint);
    $min-value: map-get($breakpoints, $min-breakpoint);

    @media (max-width: $max-value) and (min-width: $min-value) {
      @content;
    }
  } @else {
    @warn "Invalid breakpoint: #{$max-breakpoint} #{$min-breakpoint}.";
  }
}

// Color enccoding for usage in svg
@function encodecolor($string: null) {
  @if $string {
    @if type-of($string) == 'color' {
      $hex: str-slice(ie-hex-str($string), 4);
      $string: unquote('#{$hex}');
    }

    $string: '%23' + $string;
    @return $string;
  } @else {
    @return '';
  }
}
