import { HttpError } from 'wasp/server';
import type { Message, User, Participant, Conversation, MessageStatus } from 'wasp/entities';
import { getConversations as getConversationsQuery } from './queries';
import { getMessages as getMessagesQuery } from './queries';
import { sendMessage as sendMessageAction } from './actions';
import { markMessageAsRead as markMessageAsReadAction } from './actions';
import { startConversation as startConversationAction } from './actions';
import type { PrismaClient } from '@prisma/client';
// Types for request/response
interface MobileConversation {
  id: number;
  name: string | null;
  isGroup: boolean;
  displayName: string;
  displayImage: string | null;
  lastMessage: {
    content: string | null;
    senderName: string | null;
    createdAt: string | null;
  } | null;
  unreadCount: number;
  updatedAt: string;
}

interface MobileMessage {
  id: number;
  content: string;
  createdAt: string;
  sender: {
    id: string;
    name: string;
  };
  status: 'SENT' | 'DELIVERED' | 'READ';
}

interface MobileStartConversationRequest {
  otherUserIds: string[];
  name?: string;
  isGroup?: boolean;
}

interface MobileStartConversationResponse {
  id: number;
  name: string | null;
  isGroup: boolean;
  displayName: string;
  createdAt: string;
  updatedAt: string;
}

// Extended Message type that includes sender information
interface MessageWithSender extends Message {
  sender: {
    id: string;
    firstName: string | null;
    lastName: string | null;
  };
  statuses: {
    status: string;
    userId: string;
  }[];
}

interface ApiContext {
  user?: User; 
  entities: {
    User: PrismaClient['user']; 
    Conversation: PrismaClient['conversation']; 
    Participant: PrismaClient['participant']; 
    Message: PrismaClient['message'];
    MessageStatus: PrismaClient['messageStatus']; 
  }
}

// GET /api/auth/mobile/conversations
export const mobileGetConversations = async (req: any, res: any, context: ApiContext): Promise<MobileConversation[]> => {
  try {
    console.log('Fetching conversations for user:', context.user);
    const conversations = await getConversationsQuery({}, context);
    
    return res.status(200).json(conversations.map(conv => ({
      id: conv.id,
      name: conv.name,
      isGroup: conv.isGroup,
      displayName: conv.displayName,
      displayImage: conv.displayImage,
      lastMessage: conv.lastMessage ? {
        content: conv.lastMessage.content,
        senderName: `${conv.lastMessage.sender.firstName || ''} ${conv.lastMessage.sender.lastName || ''}`.trim() || 'Unknown',
        createdAt: conv.lastMessage.createdAt
      } : null,
      unreadCount: conv.unread,
      updatedAt: conv.updatedAt
    })));
  } catch (error: any) {
    console.error('Failed to get conversations:', error);
    throw new HttpError(500, error.message || 'Failed to get conversations');
  }
};

// GET /mobile/messages/:conversationId
export const mobileGetMessages = async (req: any, res: any, context: any): Promise<MobileMessage[]> => {
  const conversationId = parseInt(req.params.conversationId);
  if (isNaN(conversationId)) {
    throw new HttpError(400, 'Invalid conversation ID');
  }

  try {
    const messages = await getMessagesQuery({ conversationId }, context) as MessageWithSender[];
    
    return res.status(200).json(messages.map(msg => ({
      id: msg.id,
      content: msg.content,
      createdAt: msg.createdAt.toISOString(),
      sender: {
        id: msg.senderId,
        name: msg.sender ? `${msg.sender.firstName || ''} ${msg.sender.lastName || ''}`.trim() || 'Unknown' : 'Unknown'
      },
      status:  msg.statuses.filter(status => status.userId === context.user?.id).map(status => status.status)[0], // TODO: Implement proper message status tracking
      statuses: msg.statuses
    })));
  } catch (error: any) {
    console.error(`Failed to get messages for conversation ${conversationId}:`, error);
    throw new HttpError(500, error.message || 'Failed to get messages');
  }
};

// POST /mobile/messages
export const mobileSendMessage = async (req: any, res: any, context: any): Promise<MobileMessage> => {
  const { conversationId, content } = req.body;
  
  if (!conversationId || !content) {
    throw new HttpError(400, 'Conversation ID and content are required');
  }

  try {
    const message = await sendMessageAction({
      conversationId,
      content,
      hasAttachments: false
    }, context) as MessageWithSender;

    return res.status(201).json({
      id: message.id,
      content: message.content,
      createdAt: message.createdAt.toISOString(),
      sender: {
        id: message.senderId,
        name: message.sender ? `${message.sender.firstName || ''} ${message.sender.lastName || ''}`.trim() || 'Unknown' : 'Unknown'
      },
      status: 'SENT'
    });
  } catch (error: any) {
    console.error('Failed to send message:', error);
    throw new HttpError(500, error.message || 'Failed to send message');
  }
};

// POST /mobile/messages/read
export const mobileMarkMessageAsRead = async (req: any, res: any, context: any): Promise<{ success: boolean }> => {
  const { messageId, conversationId } = req.body;
  
  if (!messageId || !conversationId) {
    throw new HttpError(400, 'Message ID and conversation ID are required');
  }

  try {
    await markMessageAsReadAction({
      messageId,
      conversationId
    }, context);

    return res.status(200).json({ success: true }) as { success: boolean };
  } catch (error: any) {
    console.error('Failed to mark message as read:', error);
    throw new HttpError(500, error.message || 'Failed to mark message as read');
  }
};

// POST /mobile/conversations/start
export const mobileStartConversation = async (req: any, res: any, context: any): Promise<MobileStartConversationResponse> => {
  const { otherUserIds, name, isGroup = false } = req.body;
  
  if (!otherUserIds || !Array.isArray(otherUserIds) || otherUserIds.length === 0) {
    throw new HttpError(400, 'At least one other user ID must be provided');
  }

  // Validate that all otherUserIds are strings
  if (!otherUserIds.every(id => typeof id === 'string')) {
    throw new HttpError(400, 'All user IDs must be strings');
  }

  // For group conversations, require a name
  if (isGroup && (!name || typeof name !== 'string' || name.trim() === '')) {
    throw new HttpError(400, 'Group name is required for group conversations');
  }

  // For 1-on-1 conversations, ensure only one other user
  if (!isGroup && otherUserIds.length > 1) {
    throw new HttpError(400, 'Cannot create a 1-on-1 chat with multiple users. Use isGroup: true for group conversations');
  }

  try {
    const conversation = await startConversationAction({
      otherUserIds,
      name: name?.trim() || undefined,
      isGroup
    }, context);

    return res.status(201).json({
      id: conversation.id,
      name: conversation.name,
      isGroup: conversation.isGroup,
      displayName: conversation.name || (isGroup ? 'Group Chat' : 'Direct Message'),
      createdAt: conversation.createdAt.toISOString(),
      updatedAt: conversation.updatedAt.toISOString()
    });
  } catch (error: any) {
    console.error('Failed to start conversation:', error);
    
    // Handle specific error cases
    if (error.message?.includes('already exists')) {
      throw new HttpError(409, 'A conversation with these participants already exists');
    }
    
    throw new HttpError(500, error.message || 'Failed to start conversation');
  }
}; 