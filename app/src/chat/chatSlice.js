import { createSlice } from '@reduxjs/toolkit';
// import { SERVICE_URL } from '../../config.js';
import axios from 'axios';
const SERVICE_URL = 'https://message-test.adscloud.org/api'
const initialState = {
  items: [],
  loading: false,
  attachmentLoading:false,
  messageLoading:false,
  selectedChat: null,
  currentMode: 'chat', // chat - call
  selectedTab: 'messages',
  currentCall: null,
};

const chatSlice = createSlice({
  name: 'chat',
  initialState,
  reducers: {
    receiveService(state, action) {
      const { items, loading } = action.payload;
      state.items = items;
      state.loading = loading;
    },
    setLoading(state, action) {
      state.loading = action.payload;
    },
    setAttachmentLoading(state, action) {
      state.attachmentLoading = action.payload;
    },
    setMessageLoading(state, action) {
      state.messageLoading = action.payload;
    },
    chatSetSelectedTab(state, action) {
      state.selectedTab = action.payload;
    },
    chatSetSelectedChat(state, action) {
      if (action.payload && action.payload.messages && action.payload.messages.length > 0) {
        state.selectedTab = 'messages';
      }
      state.selectedChat = action.payload;
    },
    chatSetCurrentCall(state, action) {
      state.currentCall = action.payload;
    },
    chatChangeMode(state, action) {
      state.currentMode = action.payload;
    },
  },
});

// const { setLoading, receiveService } = chatSlice.actions;

export const { setMessageLoading , setAttachmentLoading , setLoading, receiveService , chatChangeMode, chatSetSelectedChat, chatSetSelectedTab, chatSetCurrentCall } = chatSlice.actions;

export const getItems = () => async (dispatch, getState) => {
  const state = getState();
  dispatch(setLoading(true));
  let items = initialState.items;
  try {
    const response = await axios.get(`${SERVICE_URL}/conversations`);
    if(response.status == 200) {
      items = response.data;
      if (state.chat.selectedChat === null) dispatch(chatSetSelectedChat(items?.filter((x) => x.messages.length > 0)[0]));
    }
  } catch (error) {
    console.log(error)
  }
  
  dispatch(receiveService({ items, loading: false }));
};

export const addTextToChat =
  ({ chat, text }) =>
  async (dispatch) => {
    if (text !== '' && text.length > 0) {
      dispatch(setLoading(true));
      const response = await axios.put(`${SERVICE_URL}/apps/chat/addText`, { id: chat.id, text });
      const items = response.data;
      if(items) {
        dispatch(receiveService({ items, loading: false }));
        dispatch(chatSetSelectedChat(items?.filter((x) => x.id === chat.id)[0]));
      }
    }
  };

export const addAttachmentsToChat =
  ({ chat, attachments }) =>
  async (dispatch) => {
    if (attachments !== null && attachments.length > 0) {
      dispatch(setLoading(true));
      const response = await axios.put(`${SERVICE_URL}/apps/chat/addAttachments`, { id: chat.id, attachments });
      const items = response.data;
      if(items) {
        dispatch(receiveService({ items, loading: false }));
        dispatch(chatSetSelectedChat(items?.filter((x) => x.id === chat.id)[0]));
      }

    }
  };

export const selectChat =
  ({ chat }) =>
  async (dispatch) => {
    if (chat !== null) {
      let items = initialState.items;
      dispatch(chatSetSelectedChat(items?.filter((x) => x.id === chat.id)[0]));
      // dispatch(setLoading(true));
      // const response = await axios.put(`${SERVICE_URL}/apps/chat/read`, { id: chat.id });
      // const items = response.data;
      // if(items) {
      //   dispatch(receiveService({ items, loading: false }));
      //   dispatch(chatSetSelectedChat(items?.filter((x) => x.id === chat.id)[0]));
      // }

    } else {
      dispatch(chatSetSelectedChat(null));
    }
  };

export const createChat =
  ({ item }) =>
  async (dispatch) => {
    dispatch(setLoading(true));

    const response = await axios.post(`${SERVICE_URL}/apps/chat`, { item });
    const items = response.data;
    if(items) {
      dispatch(receiveService({ items, loading: false }));
    }

  };

export const updateChat =
  ({ item }) =>
  async (dispatch) => {
    dispatch(setLoading(true));
    const response = await axios.put(`${SERVICE_URL}/apps/chat`, { item });
    const items = response.data;
    if(items) {
      dispatch(receiveService({ items, loading: false }));
    }

  };

export const deleteChat =
  ({ ids }) =>
  async (dispatch) => {
    const response = await axios.delete(`${SERVICE_URL}/apps/chat`, { ids });
    const items = response.data;
    if(items){
      dispatch(receiveService({ items, loading: false }));
    }

  };

const chatReducer = chatSlice.reducer;

export default chatReducer;
