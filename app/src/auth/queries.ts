import { HttpError } from 'wasp/server';
import { type User } from 'wasp/entities';
import { type CheckUserExistence } from 'wasp/server/operations'; // Wasp will generate this type
import { z } from 'zod';
import { ensureArgsSchemaOrThrowHttpError } from '../server/validation';

const checkUserExistenceInputSchema = z.object({
  email: z.string().email().optional(),
  mobileNumber: z.string().min(5).optional(), // Basic validation for mobile
}).refine(data => data.email || data.mobileNumber, {
  message: "Either email or mobileNumber must be provided",
  path: ["email", "mobileNumber"], // Optional: points to fields causing error
});

export type CheckUserExistenceInput = z.infer<typeof checkUserExistenceInputSchema>;

// interface CheckUserExistenceResult {  // Commenting out for now
//   exists: boolean;
//   message: string;
//   foundBy: Array<'email' | 'mobileNumber'> | null;
// }

export const checkUserExistence: CheckUserExistence<
  CheckUserExistenceInput, 
  { // Using an object literal type directly for the return payload
    exists: boolean;
    message: string;
    foundBy: Array<'email' | 'mobileNumber'> | null;
  }
> = async (args, context) => {
  const { email, mobileNumber } = ensureArgsSchemaOrThrowHttpError(checkUserExistenceInputSchema, args);

  if (!context.entities.User) {
    throw new HttpError(500, 'User entity not configured for this query.');
  }

  const foundByArray: Array<'email' | 'mobileNumber'> = []; // Renamed to avoid conflict with field name
  let userExists = false;

  if (email) {
    const userByEmail = await context.entities.User.findUnique({
      where: { email , role: 'CUSTOMER' , isPhoneVerified: true },
      select: { id: true } // Select only id for existence check
    });
    if (userByEmail) {
      userExists = true;
      foundByArray.push('email');
    }
  }

  if (mobileNumber) {
    const userByMobile = await context.entities.User.findUnique({
      where: { mobileNumber , role: 'CUSTOMER' , isPhoneVerified: true },
      select: { id: true } // Select only id for existence check
    });
    if (userByMobile) {
      userExists = true;
      if (!foundByArray.includes('mobileNumber')) { 
        foundByArray.push('mobileNumber');
      }
    }
  }

  if (userExists) {
    return {
      exists: true,
      message: `User found by: ${foundByArray.join(', ')}.`,
      foundBy: foundByArray,
    };
  }

  return {
    exists: false,
    message: "User not found with the provided details.",
    foundBy: null,
  };
}; 