import { type MiddlewareConfigFn } from 'wasp/server';
// import cors from 'cors'; // Only needed if explicitly configuring (Option 2)

// This function will be imported into main.wasp
export const authApiNamespaceMiddleware: MiddlewareConfigFn = (middlewareConfig) => {
  // <PERSON><PERSON>'s default middleware (which includes CORS) should already be somewhat configured.
  // The WASP_ALLOWED_CORS_ORIGINS environment variable is intended to be respected by <PERSON><PERSON>'s default CORS setup.
  // By returning the existing middlewareConfig, we ensure <PERSON>p's defaults (including its CORS middleware) are applied.
  
  console.log("Applying default middleware to /api/auth namespace, relying on <PERSON><PERSON>'s default CORS handling and WASP_ALLOWED_CORS_ORIGINS.");

  // If WASP_ALLOWED_CORS_ORIGINS is set (e.g., to "http://localhost:54601"), 
  // Was<PERSON>'s default cors middleware (part of the default stack) should pick it up.
  // This function effectively ensures that the default middleware stack is indeed applied to this namespace.

  // Example of more explicit configuration (Option 2 - if the above doesn't work):
  /*
  console.log("Applying explicit CORS middleware to /api/auth namespace.");
  const corsOptions = {
    origin: (process.env.WASP_ALLOWED_CORS_ORIGINS || "http://localhost:3000").split(',').map(o => o.trim()),
    // Example: origin: 'http://localhost:54601',
    credentials: true, // If you need to support cookies/auth headers
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization"], // Add other headers your client might send
  };
  // Make sure you have `cors` package installed: npm install cors
  // And types: npm install --save-dev @types/cors
  // Then import it at the top: import cors from 'cors';
  middlewareConfig.set('cors', cors(corsOptions)); 
  */

  return middlewareConfig;
}; 