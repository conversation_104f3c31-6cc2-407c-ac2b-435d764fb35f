import { type MiddlewareConfigFn } from 'wasp/server';
import { prisma } from 'wasp/server';
import { HttpError } from 'wasp/server';

export const mobileAuthMiddleware: MiddlewareConfigFn = (middlewareConfig) => {
  // Add custom auth middleware for mobile API
  middlewareConfig.set('mobileAuth', async (req: any, res: any, next: any) => {
    try {
      const authHeader = req.headers.authorization;
      if (!authHeader) {
        throw new HttpError(401, 'No authorization header');
      }

      const [type, token] = authHeader.split(' ');
      if (type !== 'Bearer' || !token) {
        throw new HttpError(401, 'Invalid authorization header format');
      }

      // Find the session and associated user
      const auth = await prisma.auth.findFirst({
        where: {
          id: token
        },
        include: {
          user: true
        }
      });

      if (!auth || !auth.user) {
        throw new HttpError(401, 'Invalid or expired session');
      }

      // Add user to request context
      req.user = auth.user;
      req.auth = auth;
      next();
    } catch (error: any) {
      console.error('Mobile auth middleware error:', error);
      res.status(error.statusCode || 401).json({
        error: error.message || 'Authentication failed'
      });
    }
  });

  return middlewareConfig;
}; 