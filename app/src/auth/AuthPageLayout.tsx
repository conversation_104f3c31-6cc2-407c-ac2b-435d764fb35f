
import { ConfigProvider, theme as antdTheme } from 'antd';
import { ReactNode, useMemo } from 'react';
import { useColorMode , ColorModeProvider } from '../client/hooks/useColorMode';
const LayoutWrapper = ({children} : {children: ReactNode }) => {
  return (
    <ColorModeProvider>
      {children}
    </ColorModeProvider>
  );
};

export function AuthPageLayout({children} : {children: ReactNode }) {
  const { colorMode } = useColorMode();
  const themeConfig = useMemo(() => ({
    algorithm: colorMode === 'dark' ? antdTheme.darkAlgorithm : antdTheme.defaultAlgorithm,
  }), [colorMode]);
  return (
    <LayoutWrapper>
      <ConfigProvider theme={themeConfig}>
          <div className='flex min-h-full flex-col justify-center items-center pt-10 sm:px-6 lg:px-8'>
            <div className='sm:mx-auto sm:w-full sm:max-w-md'>
            <div className=''>
              <div className='-mt-8'>
                { children }
              </div>
            </div>
          </div>
        </div>
      </ConfigProvider>
    </LayoutWrapper>
  );
}
