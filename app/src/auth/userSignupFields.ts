import { z } from 'zod';
import { defineUserSignupFields } from 'wasp/auth/providers/types';
import { type User } from 'wasp/entities';

const adminEmails = process.env.ADMIN_EMAILS?.split(',') || [];
enum Role {
  ADMIN = 'ADMIN',
  USER = 'USER',
  CUSTOMER = 'CUSTOMER',
  CLIENT = 'CLIENT',
}
// Updated schema to include optional role, firstName, lastName
const emailDataSchema = z.object({
  email: z.string().email(),
  // These fields might be passed as additional data during signup
  role: z.nativeEnum(Role).optional(),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
});

export const getEmailUserFields = defineUserSignupFields({
  email: (data) => {
    // Only parse the required email part initially for basic info
    const basicData = z.object({ email: z.string().email() }).parse(data);
    return basicData.email;
  },
  username: (data) => {
    // Using email as username
    const basicData = z.object({ email: z.string().email() }).parse(data);
    return basicData.email;
  },
  isAdmin: (data) => {
    const basicData = z.object({ email: z.string().email() }).parse(data);
    return adminEmails.includes(basicData.email);
  },
  // Add field definitions for role, firstName, lastName
  role: (data): Role => {
    // Try to parse the extended data, default role to CLIENT if not provided
    const extendedData = emailDataSchema.safeParse(data);
    // Ensure the return value matches the Role enum
    const roleValue = extendedData.success ? extendedData.data.role : undefined;
    return roleValue === Role.ADMIN || roleValue === Role.USER || roleValue === Role.CUSTOMER || roleValue === Role.CLIENT ? roleValue : Role.CLIENT;
  },
  firstName: (data): string | any => {
    const extendedData = emailDataSchema.safeParse(data);
    return extendedData.success ? extendedData.data.firstName : undefined;
  },
  lastName: (data): string | any => {
    const extendedData = emailDataSchema.safeParse(data);
    return extendedData.success ? extendedData.data.lastName : undefined;
  }
});

const githubDataSchema = z.object({
  profile: z.object({
    emails: z
      .array(
        z.object({
          email: z.string(),
          verified: z.boolean(),
        })
      )
      .min(1, 'You need to have an email address associated with your GitHub account to sign up.'),
    login: z.string(),
  }),
});

export const getGitHubUserFields = defineUserSignupFields({
  email: (data) => {
    const githubData = githubDataSchema.parse(data);
    return getGithubEmailInfo(githubData).email;
  },
  username: (data) => {
    const githubData = githubDataSchema.parse(data);
    return githubData.profile.login;
  },
  isAdmin: (data) => {
    const githubData = githubDataSchema.parse(data);
    const emailInfo = getGithubEmailInfo(githubData);
    if (!emailInfo.verified) {
      return false;
    }
    return adminEmails.includes(emailInfo.email);
  },
});

// We are using the first email from the list of emails returned by GitHub.
// If you want to use a different email, you can modify this function.
function getGithubEmailInfo(githubData: z.infer<typeof githubDataSchema>) {
  return githubData.profile.emails[0];
}

// NOTE: if we don't want to access users' emails, we can use scope ["user:read"]
// instead of ["user"] and access args.profile.username instead
export function getGitHubAuthConfig() {
  return {
    scopes: ['user'],
  };
}

const googleDataSchema = z.object({
  profile: z.object({
    email: z.string(),
    email_verified: z.boolean(),
  }),
});

export const getGoogleUserFields = defineUserSignupFields({
  email: (data) => {
    const googleData = googleDataSchema.parse(data);
    return googleData.profile.email;
  },
  username: (data) => {
    const googleData = googleDataSchema.parse(data);
    return googleData.profile.email;
  },
  isAdmin: (data) => {
    const googleData = googleDataSchema.parse(data);
    if (!googleData.profile.email_verified) {
      return false;
    }
    return adminEmails.includes(googleData.profile.email);
  },
});

export function getGoogleAuthConfig() {
  return {
    scopes: ['profile', 'email'], // must include at least 'profile' for Google
  };
}

const discordDataSchema = z.object({
  profile: z.object({
    username: z.string(),
    email: z.string().email().nullable(),
    verified: z.boolean().nullable(),
  }),
});

export const getDiscordUserFields = defineUserSignupFields({
  email: (data) => {
    const discordData = discordDataSchema.parse(data);
    // Users need to have an email for payment processing.
    if (!discordData.profile.email) {
      throw new Error('You need to have an email address associated with your Discord account to sign up.');
    }
    return discordData.profile.email;
  },
  username: (data) => {
    const discordData = discordDataSchema.parse(data);
    return discordData.profile.username;
  },
  isAdmin: (data) => {
    const discordData = discordDataSchema.parse(data);
    if (!discordData.profile.email || !discordData.profile.verified) {
      return false;
    }
    return adminEmails.includes(discordData.profile.email);
  },
});

export function getDiscordAuthConfig() {
  return {
    scopes: ['identify', 'email'],
  };
}
