import { Link as WaspRouterLink, routes } from 'wasp/client/router';
import { useAuth } from 'wasp/client/auth';
import { AuthPageLayout } from './AuthPageLayout';
import { useState, useMemo, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import type { ProviderCategory } from 'wasp/entities';
import { Form, Input, Button, Select, Alert, Typography, Spin, Space, Divider, Modal, message, Segmented } from 'antd';
import { MailOutlined, LockOutlined, UserAddOutlined, LoginOutlined, PhoneOutlined, UserOutlined, ShopOutlined } from '@ant-design/icons';
import Card from 'antd/es/card/Card';

const { Title, Text } = Typography;

const BASE_URL = 'https://dapi-test.adscloud.org:8443';

// Types for API responses
interface RequestOtpResponse {
  message: string;
  providerContext?: {
    isProviderRegistration: boolean;
    providerCategoryId: number;
    businessName?: string;
  };
}

interface VerifyOtpResponse {
  message: string;
  user: {
    id: number;
    email: string;
    firstName: string;
    lastName: string;
    role: string;
  };
  provider: {
    id: number;
    userId: number;
    providerCategoryId: number;
    title: string;
    phone: string;
    isSetupComplete: boolean;
  };
  sessionId: string;
}

export function Signup() {
  const { data: user } = useAuth();
  const [form] = Form.useForm();
  const [otpForm] = Form.useForm();
  const [selectedParentId, setSelectedParentId] = useState<string | undefined>(undefined);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [categories, setCategories] = useState<ProviderCategory[]>([]);
  const [isLoadingCategories, setIsLoadingCategories] = useState<boolean>(true);
  const [categoriesError, setCategoriesError] = useState<string | null>(null);
  const [showOtpModal, setShowOtpModal] = useState<boolean>(false);
  const [otpLoading, setOtpLoading] = useState<boolean>(false);
  const [registrationData, setRegistrationData] = useState<any>(null);
  const [registrationMethod, setRegistrationMethod] = useState<'email' | 'mobile'>('mobile');
  const navigate = useNavigate();

  // Fetch provider categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch(`${BASE_URL}/api/auth/provider/categories`);
        if (!response.ok) {
          throw new Error('Failed to fetch categories');
        }
        const data = await response.json();
        setCategories(data);
      } catch (error: any) {
        setCategoriesError(error.message);
        message.error('Failed to load categories');
      } finally {
        setIsLoadingCategories(false);
      }
    };

    fetchCategories();
  }, []);

  const parentCategories = useMemo(() => {
    if (!Array.isArray(categories)) return [];
    return categories.filter((cat) => cat.parentId === null);
  }, [categories]);

  const childCategories = useMemo(() => {
    if (!Array.isArray(categories) || !selectedParentId) return [];
    const parentIdNum = parseInt(selectedParentId, 10);
    return categories.filter((cat) => cat.parentId === parentIdNum);
  }, [categories, selectedParentId]);

  // Redirect if user is already logged in
  useEffect(() => {
    if (user) {
      navigate(routes.AdminRoute.to);
    }
  }, [user, navigate]);

  const onFinish = async (values: any) => {
    setSubmitError(null);
    setIsLoading(true);

    const { email, password, firstName, lastName, phoneNumber, businessName, parentCategory, childCategory } = values;
    const finalProviderCategoryId = childCategory ? parseInt(childCategory, 10) : parseInt(parentCategory, 10);

    try {
      let requestOtpResponse;
      
      if (registrationMethod === 'email') {
        // Step 1: Request Email OTP
        requestOtpResponse = await fetch(`${BASE_URL}/api/auth/request-email-otp`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email,
            firstName,
            lastName,
            password,
            isProviderRegistration: true,
            providerCategoryId: finalProviderCategoryId,
            businessName,
            phone: phoneNumber,
          }),
        });
      } else {
        // Step 1: Request Phone OTP
        requestOtpResponse = await fetch(`${BASE_URL}/api/auth/request-otp`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            phoneNumber,
            firstName,
            lastName,
            isProviderRegistration: true,
            providerCategoryId: finalProviderCategoryId,
            businessName,
          }),
        });
      }

      if (!requestOtpResponse.ok) {
        const errorData = await requestOtpResponse.json();
        throw new Error(errorData.message || 'Failed to send OTP');
      }

      const otpData: RequestOtpResponse = await requestOtpResponse.json();
      message.success(otpData.message);

      // Store registration data for OTP verification
      setRegistrationData({
        email,
        password,
        firstName,
        lastName,
        phoneNumber,
        businessName,
        providerCategoryId: finalProviderCategoryId,
        registrationMethod,
        identifier: registrationMethod === 'email' ? email : phoneNumber,
      });

      // Show OTP modal
      setShowOtpModal(true);
    } catch (error: any) {
      setSubmitError(error.message || 'An unknown error occurred during registration.');
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleOtpVerification = async (values: any) => {
    if (!registrationData) {
      message.error('Registration data not found. Please start over.');
      return;
    }

    setOtpLoading(true);

    try {
      const verifyResponse = await fetch(`${BASE_URL}/api/auth/provider/verify-otp-register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          otp: values.otp,
          identifier: registrationData.identifier,
          password: registrationData.password,
          firstName: registrationData.firstName,
          lastName: registrationData.lastName,
          providerCategoryId: registrationData.providerCategoryId,
          businessName: registrationData.businessName,
          phone: registrationData.phoneNumber,
          email: registrationData.email,
        }),
      });

      if (!verifyResponse.ok) {
        const errorData = await verifyResponse.json();
        throw new Error(errorData.message || 'Failed to verify OTP');
      }

      const verifyData: VerifyOtpResponse = await verifyResponse.json();
      message.success('Registration successful! Redirecting to login...');
      
      // Close modal and redirect to login
      setShowOtpModal(false);
      navigate(routes.LoginRoute.to + '?signedUp=true&provider=true');
    } catch (error: any) {
      message.error(error.message || 'OTP verification failed');
    } finally {
      setOtpLoading(false);
    }
  };

  const handleParentChange = (value: string) => {
    setSelectedParentId(value);
    // Reset child category when parent changes
    form.setFieldsValue({ childCategory: undefined });
  };

  const handleOtpModalCancel = () => {
    setShowOtpModal(false);
    setRegistrationData(null);
    otpForm.resetFields();
  };

  // Render null or a loading indicator while redirecting
  if (user) {
    return <Spin tip="Redirecting..." style={{ 
      position: 'fixed', 
      top: '50%', 
      left: '50%', 
      transform: 'translate(-50%, -50%)',
      zIndex: 9999
    }} />;
  }

  return (
    <AuthPageLayout>
      <div className="flex justify-center items-center min-h-screen w-full">
        <Card style={{ 
          width: '100%', 
          maxWidth: 650, 
          marginTop: "-80px", 
          borderRadius: '1rem', 
          boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)' 
        }}>
          <Card.Meta 
            title="Create a Provider Account" 
            description="Register as a Service Provider to manage your services and appointments." 
          />

          <Form
            form={form}
            name="signup"
            onFinish={onFinish}
            layout="vertical"
            requiredMark={false}
            style={{ marginTop: '50px' }}
          >
            {submitError && (
              <Form.Item>
                <Alert 
                  message={submitError} 
                  type="error" 
                  showIcon 
                  closable 
                  onClose={() => setSubmitError(null)} 
                />
              </Form.Item>
            )}

            <Form.Item label="Registration Method">
              <Segmented
                value={registrationMethod}
                onChange={(value: string | number) => {
                  setRegistrationMethod(value as 'email' | 'mobile');
                  // Reset relevant form fields when switching methods
                  form.setFieldsValue({ 
                    email: undefined, 
                    phoneNumber: undefined 
                  });
                }}
                options={[
                  {
                    label: 'Mobile Number',
                    value: 'mobile',
                    icon: <PhoneOutlined />,
                  },
                  {
                    label: 'Email Address',
                    value: 'email',
                    icon: <MailOutlined />,
                  },
                ]}
                block
                size="large"
              />
            </Form.Item>

            <Form.Item
              name="firstName"
              label="First Name"
              rules={[{ required: true, message: 'Please input your first name!' }]}
            >
              <Input prefix={<UserOutlined />} size="large" placeholder="First Name" />
            </Form.Item>

            <Form.Item
              name="lastName"
              label="Last Name"
              rules={[{ required: true, message: 'Please input your last name!' }]}
            >
              <Input prefix={<UserOutlined />} size="large" placeholder="Last Name" />
            </Form.Item>

            {registrationMethod === 'mobile' ? (
              <Form.Item
                name="phoneNumber"
                label="Phone Number"
                rules={[{ required: true, message: 'Please input your phone number!' }]}
              >
                <Input prefix={<PhoneOutlined />} size="large" placeholder="+213 XX XX XX XX" />
              </Form.Item>
            ) : (
              <Form.Item
                name="email"
                label="Email Address"
                rules={[
                  { required: true, message: 'Please input your Email!' },
                  { type: 'email', message: 'Please enter a valid email address.' },
                ]}
              >
                <Input prefix={<MailOutlined />} size="large" placeholder="<EMAIL>" />
              </Form.Item>
            )}

            {registrationMethod === 'email' && (
              <Form.Item
                name="phoneNumber"
                label="Phone Number (Optional)"
              >
                <Input prefix={<PhoneOutlined />} size="large" placeholder="+213 XX XX XX XX" />
              </Form.Item>
            )}

            {registrationMethod === 'mobile' && (
              <Form.Item
                name="email"
                label="Email Address (Optional)"
                rules={[
                  { type: 'email', message: 'Please enter a valid email address.' },
                ]}
              >
                <Input prefix={<MailOutlined />} size="large" placeholder="<EMAIL>" />
              </Form.Item>
            )}

            <Form.Item
              name="password"
              label="Password"
              rules={[
                { required: true, message: 'Please input your Password!' },
                { min: 8, message: 'Password must be at least 8 characters long.' },
              ]}
              hasFeedback
            >
              <Input.Password prefix={<LockOutlined />} size="large" placeholder="Password (min. 8 characters)" />
            </Form.Item>

            <Form.Item
              name="businessName"
              label="Business Name (Optional)"
            >
              <Input prefix={<ShopOutlined />} size="large" placeholder="Your Business Name" />
            </Form.Item>

            <Form.Item
              name="parentCategory"
              label="Domain"
              rules={[{ required: true, message: 'Please select your primary domain!' }]}
            >
              <Select
                placeholder="-- Select primary category --"
                loading={isLoadingCategories}
                onChange={handleParentChange}
                disabled={isLoadingCategories || !!categoriesError}
                showSearch
                optionFilterProp="label"
                size="large"
                filterOption={(input: string, option: any) => 
                  (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                }
                options={[
                  ...(categoriesError ? [{ value: "error", label: "Error loading categories", disabled: true }] : []),
                  ...parentCategories.map((category: ProviderCategory) => ({
                    key: category.id,
                    value: String(category.id),
                    label: category.title,
                  }))
                ]}
              />
            </Form.Item>

            {selectedParentId && childCategories.length > 0 && (
              <Form.Item
                name="childCategory"
                label="Service"
                rules={[{ required: true, message: 'Please select your specific service!' }]}
              >
                <Select
                  placeholder="-- Select specific category --"
                  showSearch
                  optionFilterProp="label"
                  size="large"
                  filterOption={(input: string, option: any) => 
                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                  }
                  options={childCategories.map((category: ProviderCategory) => ({
                    key: category.id,
                    value: String(category.id),
                    label: category.title,
                  }))}
                />
              </Form.Item>
            )}

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                size="large"
                loading={isLoading}
                block
              >
                {isLoading ? 'Creating Account...' : 'Create Account'}
              </Button>
            </Form.Item>
          </Form>

          <Divider />
          
          <div style={{ marginTop: '1.5rem', textAlign: 'center' }}>
            <Text type="secondary" style={{ display: 'block', textAlign: 'center', marginBottom: '1rem' }}>
              Already have an account?
            </Text>
            <Space direction="vertical" style={{ width: '100%' }} size="middle">
              <WaspRouterLink to={routes.LoginRoute.to} style={{ width: '100%' }}>
                <Button type="default" block size="large">
                  Log in
                  <LoginOutlined style={{ marginLeft: '0.5em' }}/>
                </Button>
              </WaspRouterLink>
            </Space>
          </div>
        </Card>
      </div>

             {/* OTP Verification Modal */}
       <Modal
         title={
           <div style={{ textAlign: 'center', paddingBottom: '8px' }}>
             <Text strong style={{ fontSize: '18px', color: '#262626' }}>
               Verify Your {registrationMethod === 'email' ? 'Email Address' : 'Phone Number'}
             </Text>
           </div>
         }
         open={showOtpModal}
         onCancel={handleOtpModalCancel}
         footer={null}
         maskClosable={false}
         destroyOnClose
         centered
         width={420}
         styles={{
           body: { padding: '24px 24px 16px 24px' },
           header: { borderBottom: 'none', paddingBottom: '0' }
         }}
       >
         <div style={{ textAlign: 'center', marginBottom: '32px' }}>
           <Text type="secondary" style={{ 
             fontSize: '14px', 
             lineHeight: '1.5',
             display: 'block',
             color: '#8c8c8c'
           }}>
             We've sent a 6-digit verification code to your {registrationMethod === 'email' ? 'email address' : 'phone number'}. 
             Please enter it below to complete your registration.
           </Text>
         </div>
        
        <Form
          form={otpForm}
          name="otpVerification"
          onFinish={handleOtpVerification}
          layout="vertical"
        >
          <Form.Item
            name="otp"
            label={
              <Text strong style={{ fontSize: '14px', color: '#262626' }}>
                Verification Code
              </Text>
            }
            rules={[
              { required: true, message: 'Please enter the verification code!' },
              { len: 6, message: 'Verification code must be 6 digits!' },
              { pattern: /^\d{6}$/, message: 'Verification code must contain only numbers!' }
            ]}
            style={{ marginBottom: '24px' }}
          >
            <Input.OTP 
              length={6} 
              formatter={(str: any) => str.toUpperCase()} 
              style={{ 
                display: 'flex', 
                justifyContent: 'center',
                gap: '8px'
              }}
              size="large"
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: '16px' }}>
            <Button
              type="primary"
              htmlType="submit"
              loading={otpLoading}
              block
              size="large"
              style={{
                height: '48px',
                fontSize: '16px',
                fontWeight: '500',
                borderRadius: '6px',
              }}
            >
              {otpLoading ? 'Verifying...' : 'Verify & Register'}
            </Button>
          </Form.Item>
        </Form>

        <div style={{ textAlign: 'center', marginTop: '8px' }}>
          <Button 
            type="link" 
            onClick={handleOtpModalCancel}
            disabled={otpLoading}
            style={{
              padding: '0',
              height: 'auto',
              fontSize: '14px',
              color: '#1890ff'
            }}
          >
            Cancel and start over
          </Button>
        </div>
      </Modal>
    </AuthPageLayout>
  );
}
