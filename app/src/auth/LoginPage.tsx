import { Link as WaspRouterLink, routes } from 'wasp/client/router';
import { useAuth } from 'wasp/client/auth';
import { AuthPageLayout } from './AuthPageLayout';
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Form, Input, Button, Alert, Typography, Spin, Space, Divider, message } from 'antd';
import { MailOutlined, LockOutlined, UserAddOutlined, QuestionCircleOutlined, PhoneOutlined } from '@ant-design/icons';
import Card from 'antd/es/card/Card';
import { login } from 'wasp/client/auth';

// Define interfaces for the form values
interface LoginFormValues {
  email: string;
  password: string;
}

const { Title, Text } = Typography;

export default function Login() {
  const { data: user } = useAuth();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  useEffect(() => {
    if (user) {
      navigate(routes.AdminRoute.to);
    }
  }, [user, navigate]);

  // Use the defined interface for the values parameter
  const onFinish = async (values: LoginFormValues) => {
    setSubmitError(null);
    setIsLoading(true);
    
    try {
      console.log('Login attempt with email:', values.email);
      
      // Use Wasp's standard login function for email auth
      await login({ 
        email: values.email, 
        password: values.password 
      });

      console.log('Login successful');
      
      // Success message
      message.success('Successfully logged in!');
      
      // Wasp handles the session automatically, redirect to admin
      navigate(routes.AdminRoute.to);
      
    } catch (error: any) {
      console.error('Login error:', error);
      setSubmitError(error.message || 'An unknown error occurred during login.');
    } finally {
      setIsLoading(false);
    }
  };

  if (user) {
    return <Spin tip="Redirecting..." fullscreen />;
  }

  return (
    <AuthPageLayout>
      <div className="flex justify-center items-center min-h-screen w-full">
        <Card style={{ 
          width: '100%', 
          maxWidth: 500, 
          marginTop: "-80px",
          borderRadius: '1rem', 
          boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)' 
        }}>
          <Card.Meta title="Log in to your account" description="Access your dashboard." />
          <Form
            form={form}
            name="login"
            onFinish={onFinish}
            layout="vertical"
            requiredMark={false}
            style={{ marginTop: '50px' }}
          >
            {submitError && (
              <Form.Item>
                <Alert message={submitError} type="error" showIcon closable onClose={() => setSubmitError(null)} />
              </Form.Item>
            )}

            <Form.Item
              name="email"
              label="Email"
              rules={[
                { required: true, message: 'Please input your Email!' },
                { type: 'email', message: 'Please enter a valid email address!' },
              ]}
            >
              <Input prefix={<MailOutlined />} size="large" placeholder="<EMAIL>" />
            </Form.Item>

            <Form.Item
              name="password"
              label="Password"
              rules={[{ required: true, message: 'Please input your Password!' }]}
            >
              <Input.Password prefix={<LockOutlined />} size="large" placeholder="Password" />
            </Form.Item>
            
            <Form.Item style={{ textAlign: 'right', marginTop: '-10px', marginBottom: '24px' }}>
              <Button type="link" style={{ padding: '0px' }} href={routes.RequestPasswordResetRoute.to}>
                Forgot password?
              </Button>
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                size="large"
                htmlType="submit"
                loading={isLoading}
                block
              >
                {isLoading ? 'Logging in...' : 'Log In'}
              </Button>
            </Form.Item>
          </Form>
          <Divider />
          <div style={{ marginTop: '1.5rem', textAlign: 'center' }}>
            <Text type="secondary" style={{ display: 'block', textAlign: 'center', marginBottom: '1rem' }}>
              Don't have an account?
            </Text>
            <div style={{ width: '100%' , display:'flex' , gap:"10px" }}>
              <WaspRouterLink to={routes.SignupRoute.to} style={{flex:"1"}}>
                <Button type="default" block size="large">
                  Partner
                  <UserAddOutlined style={{ marginLeft: '0.5em' }}/>
                </Button>
              </WaspRouterLink>

              <WaspRouterLink to={routes.CustomerSignupRoute.to} style={{flex:"1"}}>
                <Button type="default" block size="large"> 
                  Customer
                  <UserAddOutlined style={{ marginLeft: '0.5em' }}/>
                </Button>
              </WaspRouterLink>
            </div>
          </div>
        </Card>
      </div>
    </AuthPageLayout>
  );
}
