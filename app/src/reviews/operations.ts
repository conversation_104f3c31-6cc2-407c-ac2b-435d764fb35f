import { HttpError, prisma } from 'wasp/server';
import { type Review, type User, type SProvider, type Appointment } from 'wasp/entities';
import { type AuthUser } from 'wasp/auth';
import type { CreateReview, UpdateReview, DeleteReview, GetReviewsForProvider, GetReviewsByCustomer } from 'wasp/server/operations';
import { z } from 'zod';
import { ensureArgsSchemaOrThrowHttpError } from '../server/validation';
import { Prisma, Role } from '@prisma/client';
import { createNotificationEntry } from '../notifications/operations';

// Define the context type
type Context = {
  user?: AuthUser;
  entities: {
    Review: Prisma.ReviewDelegate;
    User: Prisma.UserDelegate;
    SProvider: Prisma.SProviderDelegate;
    Appointment: Prisma.AppointmentDelegate;
    Notification: Prisma.NotificationDelegate;
  };
};

// Schema for creating a review
const createReviewSchema = z.object({
  rating: z.number().int().min(1).max(5),
  comment: z.string().optional(),
  providerId: z.number().int().positive(),
  appointmentId: z.number().int().positive().optional(),
});

type CreateReviewInput = z.infer<typeof createReviewSchema>;

// Create Review Action
export const createReview = async (rawArgs: CreateReviewInput, context: Context) => {
  if (!context.user) {
    throw new HttpError(401, 'User not authenticated.');
  }

  const args = ensureArgsSchemaOrThrowHttpError(createReviewSchema, rawArgs);

  try {
    // If appointmentId is provided, verify it exists and belongs to the user
    if (args.appointmentId) {
      const appointment = await context.entities.Appointment.findFirst({
        where: {
          id: args.appointmentId,
          customerFolder: { userId: context.user.id },
          status: 'completed', // Only completed appointments can be reviewed
        },
      });

      if (!appointment) {
        throw new HttpError(404, 'Appointment not found or not eligible for review.');
      }

      // Check if this appointment already has a review
      const existingReview = await context.entities.Review.findFirst({
        where: { appointmentId: args.appointmentId },
      });

      if (existingReview) {
        throw new HttpError(400, 'This appointment has already been reviewed.');
      }
    }

    // Verify the provider exists
    const provider = await context.entities.SProvider.findUnique({
      where: { id: args.providerId },
    });

    if (!provider) {
      throw new HttpError(404, 'Provider not found.');
    }

    // Create the review within a transaction to update provider stats
    const [review] = await prisma.$transaction(async (tx) => {
      // Create the review
      const newReview = await tx.review.create({
        data: {
          rating: args.rating,
          comment: args.comment,
          customerId: context.user!.id,
          providerId: args.providerId,
          appointmentId: args.appointmentId,
        },
        include: {
          customer: {
            select: {
              firstName: true,
              lastName: true,
            },
          },
        },
      });

      // Update provider's average rating and total reviews
      const reviews = await tx.review.findMany({
        where: { providerId: args.providerId },
        select: { rating: true },
      });

      const totalReviews = reviews.length;
      const averageRating = reviews.reduce((acc, rev) => acc + rev.rating, 0) / totalReviews;

      await tx.sProvider.update({
        where: { id: args.providerId },
        data: {
          averageRating,
          totalReviews,
        },
      });

      // Create notification for the provider
      await createNotificationEntry(tx.notification, {
        userId: provider.userId,
        type: 'NEW_REVIEW',
        title: 'New Review Received',
        message: `${newReview.customer.firstName} ${newReview.customer.lastName} left a ${args.rating}-star review${args.comment ? ' with a comment' : ''}.`,
        link: `/admin/reviews`,
        actorId: context.user!.id,
      });

      return [newReview];
    });

    return review;
  } catch (error: any) {
    console.error('Failed to create review:', error);
    throw new HttpError(500, error.message || 'Failed to create review.');
  }
};

// Schema for updating a review
const updateReviewSchema = z.object({
  reviewId: z.number().int().positive(),
  rating: z.number().int().min(1).max(5),
  comment: z.string().optional(),
});

type UpdateReviewInput = z.infer<typeof updateReviewSchema>;

// Update Review Action
export const updateReview = async (rawArgs: UpdateReviewInput, context: Context) => {
  if (!context.user) {
    throw new HttpError(401, 'User not authenticated.');
  }

  const args = ensureArgsSchemaOrThrowHttpError(updateReviewSchema, rawArgs);

  try {
    // Verify the review exists and belongs to the user
    const existingReview = await context.entities.Review.findFirst({
      where: {
        id: args.reviewId,
        customerId: context.user.id,
      },
    });

    if (!existingReview) {
      throw new HttpError(404, 'Review not found or unauthorized.');
    }

    // Update the review within a transaction to update provider stats
    const [updatedReview] = await prisma.$transaction(async (tx) => {
      const review = await tx.review.update({
        where: { id: args.reviewId },
        data: {
          rating: args.rating,
          comment: args.comment,
        },
      });

      // Update provider's average rating
      const reviews = await tx.review.findMany({
        where: { providerId: existingReview.providerId },
        select: { rating: true },
      });

      const totalReviews = reviews.length;
      const averageRating = reviews.reduce((acc, rev) => acc + rev.rating, 0) / totalReviews;

      await tx.sProvider.update({
        where: { id: existingReview.providerId },
        data: {
          averageRating,
        },
      });

      return [review];
    });

    return updatedReview;
  } catch (error: any) {
    console.error('Failed to update review:', error);
    throw new HttpError(500, error.message || 'Failed to update review.');
  }
};

// Schema for deleting a review
const deleteReviewSchema = z.object({
  reviewId: z.number().int().positive(),
});

type DeleteReviewInput = z.infer<typeof deleteReviewSchema>;

// Delete Review Action
export const deleteReview = async (rawArgs: DeleteReviewInput, context: Context) => {
  if (!context.user) {
    throw new HttpError(401, 'User not authenticated.');
  }

  const args = ensureArgsSchemaOrThrowHttpError(deleteReviewSchema, rawArgs);

  try {
    // Verify the review exists and belongs to the user or user is admin
    const existingReview = await context.entities.Review.findFirst({
      where: {
        id: args.reviewId,
        OR: [
          { customerId: context.user.id },
          { customer: { isAdmin: true } },
        ],
      },
    });

    if (!existingReview) {
      throw new HttpError(404, 'Review not found or unauthorized.');
    }

    // Delete the review within a transaction to update provider stats
    const [deletedReview] = await prisma.$transaction(async (tx) => {
      const review = await tx.review.delete({
        where: { id: args.reviewId },
      });

      // Update provider's average rating and total reviews
      const reviews = await tx.review.findMany({
        where: { providerId: existingReview.providerId },
        select: { rating: true },
      });

      const totalReviews = reviews.length;
      const averageRating = totalReviews > 0
        ? reviews.reduce((acc, rev) => acc + rev.rating, 0) / totalReviews
        : null;

      await tx.sProvider.update({
        where: { id: existingReview.providerId },
        data: {
          averageRating,
          totalReviews,
        },
      });

      return [review];
    });

    return deletedReview;
  } catch (error: any) {
    console.error('Failed to delete review:', error);
    throw new HttpError(500, error.message || 'Failed to delete review.');
  }
};

// Schema for getting provider reviews
const getProviderReviewsSchema = z.object({
  providerId: z.number().int().positive(),
  skip: z.number().int().min(0).optional(),
  take: z.number().int().min(1).max(50).optional(),
});

type GetProviderReviewsInput = z.infer<typeof getProviderReviewsSchema>;

// Get Reviews for Provider Query
export const getReviewsForProvider = async (rawArgs: GetProviderReviewsInput, context: Context) => {
  const args = ensureArgsSchemaOrThrowHttpError(getProviderReviewsSchema, rawArgs);

  try {
    const reviews = await context.entities.Review.findMany({
      where: { providerId: args.providerId },
      include: {
        customer: {
          select: {
            firstName: true,
            lastName: true,
          },
        },
        appointment: {
          select: {
            service: {
              select: {
                title: true,
              },
            },
          },
        },
      },
      orderBy: { createdAt: 'desc' },
      skip: args.skip,
      take: args.take ?? 10,
    });

    return reviews;
  } catch (error: any) {
    console.error('Failed to get provider reviews:', error);
    throw new HttpError(500, error.message || 'Failed to get reviews.');
  }
};

// Get Reviews by Customer Query
export const getReviewsByCustomer = async (_args: void, context: Context) => {
  if (!context.user) {
    throw new HttpError(401, 'User not authenticated.');
  }

  try {
    const reviews = await context.entities.Review.findMany({
      where: { customerId: context.user.id },
      include: {
        provider: {
          select: {
            title: true,
            user: {
              select: {
                firstName: true,
                lastName: true,
              },
            },
          },
        },
        appointment: {
          select: {
            service: {
              select: {
                title: true,
              },
            },
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    return reviews;
  } catch (error: any) {
    console.error('Failed to get customer reviews:', error);
    throw new HttpError(500, error.message || 'Failed to get reviews.');
  }
}; 