import { Request, Response } from 'express';
import { createReview, getReviewsForProvider } from './operations';
import { HttpError } from 'wasp/server';
import { type User } from 'wasp/entities';
import { Prisma } from '@prisma/client';
import { type AuthUser } from 'wasp/auth';

type Context = {
  user?: AuthUser;
  entities: {
    Review: Prisma.ReviewDelegate;
    User: Prisma.UserDelegate;
    SProvider: Prisma.SProviderDelegate;
    Appointment: Prisma.AppointmentDelegate;
    Notification: Prisma.NotificationDelegate;
  };
};

export const handleCreateReview = async (
  req: Request,
  res: Response,
  context: Context
) => {
  try {
    const review = await createReview(req.body, context);
    res.json(review);
  } catch (error: any) {
    if (error instanceof HttpError) {
      res.status(error.statusCode).json({ message: error.message });
    } else {
      res.status(500).json({ message: 'Internal server error' });
    }
  }
};

export const handleGetProviderReviews = async (
  req: Request,
  res: Response,
  context: Context
) => {
  try {
    const providerId = parseInt(req.params.providerId);
    if (isNaN(providerId)) {
      res.status(400).json({ message: 'Invalid provider ID' });
      return;
    }

    const skip = req.query.skip ? parseInt(req.query.skip as string) : undefined;
    const take = req.query.take ? parseInt(req.query.take as string) : undefined;

    const reviews = await getReviewsForProvider(
      { providerId, skip, take },
      context
    );
    res.json(reviews);
  } catch (error: any) {
    if (error instanceof HttpError) {
      res.status(error.statusCode).json({ message: error.message });
    } else {
      res.status(500).json({ message: 'Internal server error' });
    }
  }
}; 