import React, { useState, useMemo, useEffect } from 'react';
import { useQuery, useAction } from 'wasp/client/operations';
import { 
  getCustomerAppointments, 
  cancelAppointment,
  getRescheduleRequests,
  respondToRescheduleRequest,
  createRescheduleRequest,
  getQueueSwapRequests,
  respondToQueueSwapRequest,
  requestQueueSwap,
  getQueueAvailability,
  getProviderAvailability
} from 'wasp/client/operations';
import { Link } from 'wasp/client/router';
import { formatDate, isToday } from '../../utils/dateUtils'; // Assuming you have a date formatting utility
import { 
  Segmented, 
  Spin, 
  Alert, 
  Card, 
  Tag, 
  Empty, 
  Space, 
  Typography, 
  Button, 
  Popconfirm, 
  message, 
  QRCode,
  Modal,
  DatePicker,
  Input,
  Badge,
  Drawer,
  Collapse
} from 'antd';
import { 
  ClockCircleOutlined, 
  UserOutlined, 
  EnvironmentOutlined, 
  CalendarOutlined, 
  InfoCircleOutlined, 
  DeleteOutlined,
  SwapOutlined
} from '@ant-design/icons';
import { QueueStatusDisplay } from './QueueStatusDisplay'; // Import the new component
import { useSocket } from 'wasp/client/webSocket'; // Import useSocket
import dayjs from 'dayjs';

const { Text } = Typography;

// Define filter options
const filterOptions = ['Upcoming', 'Past', 'Canceled'];

const getStatusTagColor = (status) => {
  switch (status?.toLowerCase()) {
    case 'confirmed': return 'success';
    case 'pending': return 'warning';
    case 'canceled':
    case 'noshow': return 'error';
    default: return 'default';
  }
};

// Add new component for reschedule requests
const RescheduleRequestDrawer = ({ visible, onClose, appointment, onSuccess }) => {
  const [loading, setLoading] = useState(false);
  const [reason, setReason] = useState('');
  const [selectedSlot, setSelectedSlot] = useState(null); // { startTime, queueId }

  const createRescheduleRequestAction = useAction(createRescheduleRequest);

  // Fetch slots using useQuery (now using getProviderAvailability)
  const {
    data: availableSlots = [],
    isLoading: slotsLoading,
    error: slotsError,
    refetch: refetchSlots,
  } = useQuery(
    getProviderAvailability,
    appointment && visible
      ? {
          sProvidingPlaceId: appointment.placeId,
          serviceId: appointment.serviceId,
          startDate: dayjs().format('YYYY-MM-DD'),
          endDate: dayjs().add(7, 'day').format('YYYY-MM-DD'),
          // queueId: appointment.queueId, // Omit to get all queues
        }
      : null,
    { enabled: !!appointment && visible }
  );
  console.log('[RescheduleRequestDrawer] appointment', appointment);
  console.log('[RescheduleRequestDrawer] availableSlots', availableSlots);
  // Reset selection and reason when opening
  useEffect(() => {
    if (visible) {
      setSelectedSlot(null);
      setReason('');
    }
  }, [visible, appointment]);

  const handleSubmit = async () => {
    if (!selectedSlot || !reason) {
      message.error('Please select a slot and provide a reason');
      return;
    }

    setLoading(true);
    try {
      const startTime = dayjs(selectedSlot.startTime);
      const endTime = startTime.add(appointment.service.duration, 'minute');
      await createRescheduleRequestAction({
        appointmentId: appointment.id,
        suggestedStartTime: startTime.toDate(),
        suggestedEndTime: endTime.toDate(),
        reason,
        // If your backend/action supports queueId, pass it here:
        queueId: selectedSlot.queueId,
      });
      message.success('Reschedule request sent successfully');
      onSuccess();
      onClose();
    } catch (error) {
      message.error(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Helper: Render slots grouped by day
  const renderSlots = () => {
    if (slotsLoading) return <Spin />;
    if (slotsError) return <div style={{ color: 'red' }}>{slotsError.message || String(slotsError)}</div>;
    if (!availableSlots.length) return <div>No available slots found.</div>;

    // We'll need a queueId -> queueName map for display
    // Let's build it from all slots (if queue names are not included, you may need to fetch them separately)
    // For now, just show the queueId (or you can enhance this if you have queue names in the slot data)
    return (
      <Collapse accordion>
        {availableSlots.map(day => (
          <Collapse.Panel header={dayjs(day.date).format('dddd, MMM D')} key={day.date}>
            <Space wrap>
              {day.slots
                .filter(slot => !slot.isBooked)
                .map(slot => (
                  <Button
                    key={slot.startTime + '-' + slot.queueId}
                    type={selectedSlot && selectedSlot.startTime === slot.startTime && selectedSlot.queueId === slot.queueId ? 'primary' : 'default'}
                    onClick={() => setSelectedSlot({ startTime: slot.startTime, queueId: slot.queueId })}
                  >
                    {dayjs(slot.startTime).format('HH:mm')} (Queue {slot.queueId})
                  </Button>
                ))}
            </Space>
          </Collapse.Panel>
        ))}
      </Collapse>
    );
  };

  return (
    <Drawer
      title="Request Reschedule"
      placement="right"
      onClose={onClose}
      open={visible}
      width={400}
    >
      <Space direction="vertical" className="w-full">
        <Alert
          message="Current Appointment Details"
          description={
            <Space direction="vertical">
              <Text>{appointment?.service?.title}</Text>
              <Text>
                {dayjs(appointment?.expectedAppointmentStartTime).format('MMM D, HH:mm')} - 
                {dayjs(appointment?.expectedAppointmentEndTime).format('HH:mm')}
              </Text>
            </Space>
          }
          type="info"
        />

        <Typography.Title level={5}>Select a New Time</Typography.Title>
        {renderSlots()}

        <Typography.Title level={5}>Reason for Rescheduling</Typography.Title>
        <Input.TextArea
          rows={4}
          placeholder="Please provide a reason for rescheduling"
          value={reason}
          onChange={(e) => setReason(e.target.value)}
        />

        <Button
          type="primary"
          block
          onClick={handleSubmit}
          loading={loading}
          disabled={!selectedSlot || !reason}
        >
          Submit Request
        </Button>
      </Space>
    </Drawer>
  );
};

// Add new component for viewing reschedule requests
const RescheduleRequestsModal = ({ visible, onClose, requests, onRespond }) => {
  const respondToRescheduleRequestAction = useAction(respondToRescheduleRequest);
  const [loading, setLoading] = useState(false);

  const handleResponse = async (requestId, accept) => {
    setLoading(true);
    try {
      await respondToRescheduleRequestAction({
        requestId,
        accept
      });
      message.success(`Request ${accept ? 'accepted' : 'rejected'}`);
      onRespond();
    } catch (error) {
      message.error(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title="Reschedule Requests"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={600}
    >
      {requests.length === 0 ? (
        <Empty description="No reschedule requests" />
      ) : (
        <Space direction="vertical" className="w-full">
          {requests.map((request) => (
            <Card key={request.id} size="small">
              <Space direction="vertical" className="w-full">
                <div className="flex justify-between">
                  <Text strong>{request.appointment.service.title}</Text>
                  <Tag color={request.status === 'pending' ? 'gold' : request.status === 'accepted' ? 'green' : 'red'}>
                    {request.status}
                  </Tag>
                </div>
                
                <Space direction="vertical">
                  <Text type="secondary">
                    Current: {dayjs(request.appointment.expectedAppointmentStartTime).format('MMM D, HH:mm')}
                  </Text>
                  <Text type="secondary">
                    Suggested: {dayjs(request.suggestedStartTime).format('MMM D, HH:mm')}
                  </Text>
                  {request.reason && (
                    <Text type="secondary">Reason: {request.reason}</Text>
                  )}
                </Space>

                {request.status === 'pending' && (
                  <Space className="w-full justify-end">
                    <Button
                      size="small"
                      type="primary"
                      onClick={() => handleResponse(request.id, true)}
                      loading={loading}
                    >
                      Accept
                    </Button>
                    <Button
                      size="small"
                      danger
                      onClick={() => handleResponse(request.id, false)}
                      loading={loading}
                    >
                      Reject
                    </Button>
                  </Space>
                )}
              </Space>
            </Card>
          ))}
        </Space>
      )}
    </Modal>
  );
};

const MyAppointmentsPage = ({user}) => {
  const { data: appointments, isLoading, error, refetch } = useQuery(getCustomerAppointments);
  const { socket } = useSocket(); // Get socket instance
  const cancelAppointmentAction = useAction(cancelAppointment, {
    onSuccess: (result) => { // Access result if needed (e.g., containing queueId)
      message.success('Appointment canceled successfully!');
      refetch();
      // ADDED: Notify server about potential queue change
      // We need the queueId of the cancelled appointment.
      // Assuming the action returns the cancelled appointment or relevant data
      // Or find it from the original appointments list if the action doesn't return it.
      const cancelledAppt = appointments?.find(appt => appt.id === result?.id); // Adjust based on actual return value
      if (socket && cancelledAppt?.queueId) {
        console.log('Emitting notifyQueueChange after cancellation for queue:', cancelledAppt.queueId);
        socket.emit('notifyQueueChange', { queueId: cancelledAppt.queueId });
      }
    },
    onError: (error) => {
      message.error(`Failed to cancel appointment: ${error.message}`);
    }
  });
  const [filterStatus, setFilterStatus] = useState(filterOptions[0]); // Default to 'Upcoming'

  const filteredAppointments = useMemo(() => {
    if (!appointments) return [];

    const now = new Date();

    return appointments.filter(appt => {
      const endTime = appt.expectedAppointmentEndTime ? new Date(appt.expectedAppointmentEndTime) : null;
      const isCancelled = appt.status === 'canceled' || appt.status === 'noshow';
      const isCompleted = appt.status === 'completed';

      switch (filterStatus) {
        case 'Upcoming':
          // Show if not cancelled, not completed, and ends in the future
          return !isCancelled && !isCompleted && endTime && endTime > now;
        case 'Past':
          // Show if completed OR (not cancelled and ends in the past)
          return isCompleted || (!isCancelled && endTime && endTime <= now);
        case 'Canceled':
          // Show if status is canceled or noshow
          return isCancelled;
        default:
          return true; // Should not happen
      }
    });
  }, [appointments, filterStatus]);

  const handleCancel = (appointmentId) => {
    cancelAppointmentAction({ appointmentId });
  };

  const isCancellable = (appt) => {
    const cancellableStatuses = ['pending', 'confirmed'];
    const endTime = appt.expectedAppointmentEndTime ? new Date(appt.expectedAppointmentEndTime) : null;
    const now = new Date();
    return cancellableStatuses.includes(appt.status) && endTime && endTime > now;
  };

  // --- Function to check if an appointment is eligible for initiating a swap ---
  const isSwappable = (appt) => {
    const swappableStatuses = ['pending', 'confirmed'];
    const startTime = appt.expectedAppointmentStartTime ? new Date(appt.expectedAppointmentStartTime) : null;
    const now = new Date();
    // Must be a future appointment and have a queueId
    return swappableStatuses.includes(appt.status) && startTime && startTime > now && appt.queueId !== null;
  };

  // --- Handler for requesting a queue swap ---
  const handleRequestSwap = (appointmentId) => {
    Modal.confirm({
      title: 'Request Position Swap?',
      content: 'Are you sure you want to request to swap positions with the next appointment in the queue?',
      onOk: async () => {
        try {
          // await requestQueueSwapAction({ appointment1Id: appointmentId });
          message.success('Swap request sent successfully!');
          refetchQueueSwaps(); // Refetch to see the new request (if relevant to current user)
        } catch (err) {
          message.error(err.message || 'Failed to send swap request.');
        }
      },
      okText: 'Yes, Request Swap',
      cancelText: 'No',
    });
  };

  // Add new queries and state
  const { 
    data: rescheduleRequests = [], 
    refetch: refetchRequests 
  } = useQuery(getRescheduleRequests);

  // --- State and Query for Queue Swap ---
  const { 
    data: rawQueueSwapRequests = [], 
    isLoading: isLoadingQueueSwaps, 
    error: errorQueueSwaps, 
    refetch: refetchQueueSwaps 
  } = useQuery(getQueueSwapRequests);


  console.log('[MyAppointmentsPage] rawQueueSwapRequests', rawQueueSwapRequests);
  const requestQueueSwapAction = useAction(requestQueueSwap); // Added action

  const actionableQueueSwapRequestsForCustomer = useMemo(() => {
    console.log('[MyAppointmentsPage]  socket?.user?.id ', socket);
    if (!rawQueueSwapRequests) return [];
    
    return rawQueueSwapRequests.filter(
      (r) => 
        r.status === 'pending_customer2_approval' && 
        r.appointment2?.customerFolder?.userId === user.id
    );
  }, [rawQueueSwapRequests, socket?.user?.id]);

  const [isQueueSwapRequestsVisible, setIsQueueSwapRequestsVisible] = useState(false);
  // --- End State and Query for Queue Swap ---

  const [selectedAppointmentForReschedule, setSelectedAppointmentForReschedule] = useState(null);
  const [isRescheduleRequestsVisible, setIsRescheduleRequestsVisible] = useState(false);
  console.log('rescheduleRequests', rescheduleRequests);
  // Update refetch function to include new data
  const refetchAll = () => {
    refetch();
    refetchRequests();
    refetchQueueSwaps(); // Added
  };

  // Add button to view reschedule requests
  const renderRescheduleRequestsButton = () => {
    const pendingCount = rescheduleRequests.filter(r => r.status === 'pending').length;
    
    return (
      <Button
        type="default"
        icon={<SwapOutlined />}
        onClick={() => setIsRescheduleRequestsVisible(true)}
      >
        Reschedule Requests
        {pendingCount > 0 && (
          <Badge count={pendingCount} offset={[10, -5]} />
        )}
      </Button>
    );
  };

  // Update the card actions to include reschedule option
  const renderCardActions = (appt) => {
    const actions = [];

    if (isCancellable(appt)) {
      actions.push(
        <Popconfirm
          title="Cancel Appointment?"
          description="Are you sure you want to cancel this appointment?"
          onConfirm={() => handleCancel(appt.id)}
          okText="Yes, Cancel"
          cancelText="No"
          key="cancel"
        >
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            loading={cancelAppointmentAction.isLoading}
          >
            Cancel
          </Button>
        </Popconfirm>
      );

      // Add reschedule option
      actions.push(
        <Button
          type="text"
          icon={<SwapOutlined />}
          onClick={() => setSelectedAppointmentForReschedule(appt)}
          key="reschedule"
        >
          Request Reschedule
        </Button>
      );
    }

    // --- Add Request Swap Button ---
    if (isSwappable(appt)) {
      actions.push(
        <Button
          type="text"
          icon={<SwapOutlined />} // Consider a different icon to distinguish from reschedule
          onClick={() => handleRequestSwap(appt.id)}
          key="requestSwap"
          loading={requestQueueSwapAction.isLoading} // Added loading state
        >
          Request Swap
        </Button>
      );
    }
    // --- End Add Request Swap Button ---

    return actions;
  };

  return (
    <div className="mt-10 px-4 sm:px-6 lg:px-8">
      <div className="mx-auto max-w-7xl bg-white rounded-lg shadow-lg p-6 sm:p-8 dark:bg-gray-800">
        <div className="mb-8 flex justify-between items-center">
        <h1 className="text-2xl sm:text-3xl font-bold mb-6 text-gray-800 border-b pb-4 dark:text-white">My Appointments</h1>
          <Space>
            {renderRescheduleRequestsButton()}
            {/* --- Button for Queue Swap Requests --- */}
            {(() => {
              const pendingSwapRequestsForCustomer = actionableQueueSwapRequestsForCustomer.length; // Use filtered length
              return (
                <Button
                  type="default"
                  icon={<SwapOutlined />}
                  onClick={() => setIsQueueSwapRequestsVisible(true)}
                >
                  Swap Requests
                  {pendingSwapRequestsForCustomer > 0 && (
                    <Badge count={pendingSwapRequestsForCustomer} offset={[10, -5]} />
                  )}
                </Button>
              );
            })()}
            {/* --- End Button for Queue Swap Requests --- */}
          </Space>
        </div>
        
        
        <div className="mb-8 flex justify-between items-center" style={{ width: '100%' }}>
          <Segmented
            options={filterOptions}
            value={filterStatus}
            onChange={(value) => setFilterStatus(value)}
            size="large"
            block
            style={{ width: '100%' }}
          />
        </div>

        {isLoading && (
          <div className="text-center p-10"><Spin size="large" /></div>
        )}

        {error && (
          <Alert message="Error Loading Appointments" description={error.message} type="error" showIcon closable />
        )}

        {!isLoading && !error && (
          filteredAppointments.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 items-stretch">
              {filteredAppointments.map((appt) => (
                <Card
                  key={appt.id}
                  title={appt?.service?.title || 'Service Name Missing'}
                  type="inner"
                  bordered={false}
                  className="shadow-md hover:shadow-lg transition-shadow duration-300 flex flex-col h-full"
                  bodyStyle={{ flexGrow: 1 }}
                  extra={<Tag color={getStatusTagColor(appt.status)} className="capitalize">{appt.status}</Tag>}
                  actions={renderCardActions(appt)}
                >
                  <Space direction="vertical" size="middle" className="w-full">
                    <div className="flex items-center justify-center text-gray-700">
                      <QRCode value={`${appt.id}-${appt.service?.title}`} size={200} />
                    </div>
                    <div className="flex items-center text-gray-700">
                      <UserOutlined className="mr-2 text-lg text-blue-600" />
                      <Text strong className="mr-2">Provider:</Text>
                      <Text>{appt.customerFolder?.provider?.title || `${appt.customerFolder?.provider?.user?.firstName || ''} ${appt.customerFolder?.provider?.user?.lastName || ''}`.trim() || 'N/A'}</Text>
                    </div>
                    <div className="flex items-center text-gray-700">
                      <EnvironmentOutlined className="mr-2 text-lg text-blue-600" />
                      <Text strong className="mr-2">Location:</Text>
                      <Text>{appt.place?.name || 'N/A'}</Text>
                    </div>
                    {appt.place?.address && (
                      <div className="flex items-start text-gray-700 pl-6">
                        <Text className="text-sm">{`${appt.place.address}${appt.place.city ? `, ${appt.place.city}` : ''}`}</Text>
                      </div>
                    )}
                    <div className="flex items-center text-gray-700">
                      <CalendarOutlined className="mr-2 text-lg text-blue-600" />
                      <Text strong className="mr-2">Date:</Text>
                      <Text>{formatDate(appt.expectedAppointmentStartTime, { dateStyle: 'long' })}</Text>
                    </div>
                    <div className="flex items-center text-gray-700">
                      <ClockCircleOutlined className="mr-2 text-lg text-blue-600" />
                      <Text strong className="mr-2">Time:</Text>
                      <Text>{`${formatDate(appt.expectedAppointmentStartTime, { timeStyle: 'short' })} - ${formatDate(appt.expectedAppointmentEndTime, { timeStyle: 'short' })}`}</Text>
                    </div>
                    {appt.notes && (
                      <div className="flex items-start text-gray-700 mt-2 pt-2 border-t border-gray-200">
                        <InfoCircleOutlined className="mr-2 text-lg text-blue-600 mt-1" />
                        <div>
                            <Text strong>Notes:</Text>
                            <Text className="block whitespace-pre-wrap">{appt.notes}</Text>
                        </div>
                      </div>
                    )}
                  </Space>
                  <QueueStatusDisplay 
                    appointment={appt} 
                    isRelevantForQueueStatus={filterStatus === 'Upcoming' && !!appt.queueId && isToday(appt.expectedAppointmentStartTime)} 
                  />
                </Card>
              ))}
            </div>
          ) : (
            <div className="py-10">
              <Empty description={`No ${filterStatus.toLowerCase()} appointments found.`} />
            </div>
          )
        )}

        {/* Optional: Link to book new appointment */}
        {/* <div className="mt-8 border-t pt-6">
          <Link to="/find-provider" className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">Book New Appointment</Link>
        </div> */}
      </div>

      {/* Add new components */}
      <RescheduleRequestDrawer
        visible={!!selectedAppointmentForReschedule}
        onClose={() => setSelectedAppointmentForReschedule(null)}
        appointment={selectedAppointmentForReschedule}
        onSuccess={refetchAll}
      />

      <RescheduleRequestsModal
        visible={isRescheduleRequestsVisible}
        onClose={() => setIsRescheduleRequestsVisible(false)}
        requests={rescheduleRequests}
        onRespond={refetchAll}
      />

      {/* --- Queue Swap Requests Modal --- */}
      <QueueSwapRequestsModal
        visible={isQueueSwapRequestsVisible}
        onClose={() => setIsQueueSwapRequestsVisible(false)}
        requests={actionableQueueSwapRequestsForCustomer} // Pass filtered requests
        onRespond={refetchAll}
        currentUser={user} // Pass current user from socket context
      />
      {/* --- End Queue Swap Requests Modal --- */}
    </div>
  );
};

// --- New Component: QueueSwapRequestsModal ---
const QueueSwapRequestsModal = ({ visible, onClose, requests, onRespond, currentUser }) => {
  const respondToQueueSwapAction = useAction(respondToQueueSwapRequest);
  const [loadingRequestId, setLoadingRequestId] = useState(null);

  const handleResponse = async (requestId, accept) => {
    setLoadingRequestId(requestId);
    try {
      await respondToQueueSwapAction({
        swapRequestId: requestId, 
        accept: accept,
        status: accept ? 'approved' : 'rejected_by_customer2',
      });
      message.success(`Swap request ${accept ? 'approved' : 'rejected'}`);
      onRespond();
    } catch (error) {
      message.error(error.message || 'Failed to respond to swap request');
    } finally {
      setLoadingRequestId(null);
    }
  };

  return (
    <Modal
      title="Queue Swap Requests"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={700}
    >
      {requests.length === 0 ? (
        <Empty description="No pending queue swap requests for you." />
      ) : (
        <Space direction="vertical" className="w-full">
          {requests.map((req) => {
            // Add console.log here for debugging
            console.log(
              '[QueueSwapRequestsModal] Request ID:', req.id, 
              'Appt2 Customer UserID:', req.appointment2?.customerFolder?.userId, 
              'Current User ID:', currentUser?.id
            );
            const isCurrentUserCustomer2 = req.appointment2?.customerFolder?.userId === currentUser?.id;
            const canRespond = req.status === 'pending_customer2_approval' && isCurrentUserCustomer2;

            return (
              <Card key={req.id} size="small" className="mb-2">
                <Typography.Title level={5}>
                  Incoming Swap Request for Your Appointment: {req.appointment2?.service?.title}
                </Typography.Title>
                <Text type="secondary">
                  Your current time: {dayjs(req.appointment2?.expectedAppointmentStartTime).format('MMM D, YYYY HH:mm')}
                </Text>
                <br />
                <Text strong>
                  User {req.requestedBy?.firstName || 'Someone'} (with appointment: {req.appointment1?.service?.title} at {dayjs(req.appointment1?.expectedAppointmentStartTime).format('MMM D, YYYY HH:mm')}) wants to swap with you.
                </Text>
                <br />
                <Text strong>
                  If you accept, your new time will be: {dayjs(req.appointment1?.expectedAppointmentStartTime).format('MMM D, YYYY HH:mm')}
                </Text>
                <br />
                <Tag>Status: {req.status}</Tag>
                {req.notes && <Text block type="secondary">Notes from requester: {req.notes}</Text>}

                {canRespond && (
                  <Space className="w-full justify-end mt-2">
                    <Button
                      size="small"
                      type="primary"
                      onClick={() => handleResponse(req.id, true)}
                      loading={loadingRequestId === req.id}
                    >
                      Accept Swap
                    </Button>
                    <Button
                      size="small"
                      danger
                      onClick={() => handleResponse(req.id, false)}
                      loading={loadingRequestId === req.id}
                    >
                      Reject Swap
                    </Button>
                  </Space>
                )}
              </Card>
            );
          })}
        </Space>
      )}
    </Modal>
  );
};
// --- End New Component: QueueSwapRequestsModal ---

export default MyAppointmentsPage; 