import React, { useState, useEffect } from 'react';
import { useSocket, useSocketListener } from 'wasp/client/webSocket';
import { type Appointment } from 'wasp/entities'; // Assuming we pass the relevant appointment

// Define the expected shape of the queue status update from the server
interface QueueStatus {
  position: number | null;
  estimatedStartTime: string | null;
  estimatedWaitMinutes: number | null;
  error?: string;
}

interface QueueStatusDisplayProps {
  // We need details to identify the specific queue this component tracks
  // Passing the whole appointment might be easiest if it includes queueId
  appointment: Pick<Appointment, 'id' | 'queueId' | 'expectedAppointmentStartTime'>;
  // We only want to show status for appointments that are *today* and relevant
  isRelevantForQueueStatus: boolean;
}

export const QueueStatusDisplay: React.FC<QueueStatusDisplayProps> = ({ appointment, isRelevantForQueueStatus }) => {
  const { socket, isConnected } = useSocket();
  const [status, setStatus] = useState<QueueStatus>({ 
    position: null, 
    estimatedStartTime: null, 
    estimatedWaitMinutes: null 
  });
  const [isLoading, setIsLoading] = useState(true);

  // Effect to request initial status when connected and relevant
  useEffect(() => {
    // Only request if this appointment is relevant (e.g., for today) and we are connected
    if (isRelevantForQueueStatus && isConnected && socket && appointment.queueId) {
      console.log(`Requesting queue status for appointment ${appointment.id}, queue ${appointment.queueId}`);
      // NOTE: The server currently doesn't use queueId in requestQueueStatus,
      // it calculates for *all* relevant queues for the user.
      // This emit triggers the server to calculate and send back updates for all queues,
      // including the one this component cares about.
      socket.emit('requestQueueStatus');
      setIsLoading(true); // Show loading while waiting for the first update
    } else if (!isRelevantForQueueStatus) {
      setIsLoading(false); // Not relevant, not loading
    }
  }, [isConnected, socket, appointment.id, appointment.queueId, isRelevantForQueueStatus]);

  // Listener for updates from the server
  useSocketListener('queueUpdate', (update) => {
    // Check if the update is for the queue this component instance cares about
    if (update.queueId === appointment.queueId) {
      console.log(`Received queue update for queue ${appointment.queueId}:`, update);
      setStatus({
        position: update.position,
        estimatedStartTime: update.estimatedStartTime,
        estimatedWaitMinutes: update.estimatedWaitMinutes,
        error: update.error,
      });
      setIsLoading(false); // Received an update, no longer loading initial state
    }
  });

  // Don't render anything if this appointment isn't relevant for queue status
  if (!isRelevantForQueueStatus || !appointment.queueId) {
    return null;
  }

  return (
    <div className="mt-2 p-2 border border-neutral-300 rounded bg-neutral-50 text-sm">
      <strong className="font-medium">Live Queue Status:</strong>
      {!isConnected ? (
        <span className="ml-2 text-yellow-600">🟡 Connecting...</span>
      ) : isLoading ? (
        <span className="ml-2 text-gray-500">⏳ Loading status...</span>
      ) : status.error ? (
        <span className="ml-2 text-red-600">❌ Error: {status.error}</span>
      ) : status.position !== null ? (
        <span className="ml-2">
          You are <strong className="text-blue-600">#{status.position}</strong> in the queue.
          {status.estimatedWaitMinutes !== null && (
            <span className="ml-1 text-gray-700">
              (Est. wait: ~{status.estimatedWaitMinutes} min)
            </span>
          )}
        </span>
      ) : (
        <span className="ml-2 text-gray-500">Not currently in queue or status unavailable.</span>
      )}
    </div>
  );
}; 