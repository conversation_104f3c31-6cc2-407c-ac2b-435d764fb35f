import React from 'react';
import { useAuth } from 'wasp/client/auth';
// import { Link } from 'wasp/client/router'; // Keep if you plan to add the edit link
import { Descriptions, Spin, Alert, Card, Typography } from 'antd';

const { Title } = Typography;

const MyProfilePage = () => {
  const { data: user, isLoading, error } = useAuth();

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Spin size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="mt-10 px-6">
        <Alert message="Error" description={error.message} type="error" showIcon />
      </div>
    );
  }

  if (!user) {
    // This case should ideally be handled by authRequired: true in main.wasp
    // but adding a fallback message is good practice.
    return (
       <div className="mt-10 px-6">
         <Alert message="Authentication Error" description="User not found. Please log in again." type="warning" showIcon />
       </div>
    );
  }

  const items = [
    {
      key: '1',
      label: 'First Name',
      children: user.firstName || '-',
    },
    {
      key: '2',
      label: 'Last Name',
      children: user.lastName || '-',
    },
    {
      key: '3',
      label: 'Email',
      children: user.email || '-',
    },
    {
      key: '4',
      label: 'Mobile Number',
      children: user.mobileNumber || '-',
    },
    {
      key: '5',
      label: 'Credits',
      children: user.credits ?? '-', // Use nullish coalescing for 0 credits
    },
    {
      key: '6',
      label: 'Role',
      children: user.role ? user.role.charAt(0).toUpperCase() + user.role.slice(1).toLowerCase() : '-',
    },
    // Add more fields as needed, e.g., user.nationalId
    // {
    //   key: '7',
    //   label: 'National ID',
    //   children: user.nationalId || '-',
    // },
  ];

  return (
    <div className="mt-10 px-6">
      <Card className="mx-auto max-w-4xl">
        <Title level={2} style={{ marginBottom: '24px', borderBottom: '1px solid #f0f0f0', paddingBottom: '16px' }}>
          My Profile
        </Title>
        <Descriptions bordered column={{ xxl: 2, xl: 2, lg: 2, md: 1, sm: 1, xs: 1 }} items={items} />
        {/* Optional: Add an edit button or link */}
        {/* <div style={{ marginTop: '24px', borderTop: '1px solid #f0f0f0', paddingTop: '16px' }}>
          <Link to="/edit-profile">
            <Button type="primary">Edit Profile</Button>
          </Link>
        </div> */}
      </Card>
    </div>
  );
};

export default MyProfilePage;