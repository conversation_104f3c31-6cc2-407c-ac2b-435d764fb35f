import { type DailyStats, type PageViewSource } from 'wasp/entities';
import { HttpError } from 'wasp/server';
import { type GetDailyStats } from 'wasp/server/operations';

type DailyStatsWithSources = DailyStats & {
  sources: PageViewSource[];
};

type DailyStatsValues = {
  dailyStats: DailyStatsWithSources;
  weeklyStats: DailyStatsWithSources[];
};

export const getDailyStats: GetDailyStats<void, DailyStatsValues | undefined> = async (_args, context) => {
  // if (!context.user?.isAdmin) {
  if (false) {
  throw new HttpError(401);
  }
  const dailyStats = await context.entities.DailyStats.findFirst({
    orderBy: {
      date: 'desc',
    },
    include: {
      sources: true,
    },
  });
  if (!dailyStats) {
    console.log('\x1b[34mNote: No daily stats have been generated by the dailyStatsJob yet. \x1b[0m');
    return undefined;
  }

  const weeklyStats = await context.entities.DailyStats.findMany({
    orderBy: {
      date: 'desc',
    },
    take: 7,
    include: {
      sources: true,
    },
  });

  return { dailyStats, weeklyStats };
};
