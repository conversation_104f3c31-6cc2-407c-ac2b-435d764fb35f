import type { User, SProvider, SProvidingPlace, Service, Queue, ProviderCategory, Address } from 'wasp/entities';

export interface ProfileCompletionData {
  user: User;
  provider: SProvider & {
    category?: ProviderCategory | null;
    providingPlaces?: (SProvidingPlace & {
      detailedAddress?: Address;
      queues?: Queue[];
    })[];
    services?: Service[];
    queues?: Queue[];
  };
}

export interface CompletionBreakdown {
  profilePicture: {
    completed: boolean;
    percentage: number;
    details: string;
  };
  providerInfo: {
    completed: boolean;
    percentage: number;
    details: string;
    requiredFields: {
      title: boolean;
      phone: boolean;
      presentation: boolean;
      category: boolean;
    };
  };
  providingPlaces: {
    completed: boolean;
    percentage: number;
    details: string;
    count: number;
    validPlaces: number;
  };
  services: {
    completed: boolean;
    percentage: number;
    details: string;
    count: number;
  };
  queues: {
    completed: boolean;
    percentage: number;
    details: string;
    count: number;
    activeQueues: number;
  };
}

export interface ProfileCompletionResult {
  overallPercentage: number;
  overallCompleted: boolean;
  breakdown: CompletionBreakdown;
  nextSteps: string[];
  criticalMissing: string[];
  shouldMarkAsComplete: boolean;
}

/**
 * Calculate the profile completion percentage for a provider
 */
export function calculateProfileCompletion(data: ProfileCompletionData): ProfileCompletionResult {
  const { user, provider } = data;
  
  // Safety guard for null/undefined data
  if (!user || !provider) {
    return {
      overallPercentage: 0,
      overallCompleted: false,
      breakdown: {
        profilePicture: { completed: false, percentage: 0, details: 'User data not available' },
        providerInfo: { completed: false, percentage: 0, details: 'Provider data not available', requiredFields: { title: false, phone: false, presentation: false, category: false } },
        providingPlaces: { completed: false, percentage: 0, details: 'Provider data not available', count: 0, validPlaces: 0 },
        services: { completed: false, percentage: 0, details: 'Provider data not available', count: 0 },
        queues: { completed: false, percentage: 0, details: 'Provider data not available', count: 0, activeQueues: 0 }
      },
      nextSteps: ['Complete provider profile setup'],
      criticalMissing: ['Provider data not available'],
      shouldMarkAsComplete: false
    };
  }
  
  // Define weights for each section (total should be 100)
  const weights = {
    profilePicture: 10,
    providerInfo: 30,
    providingPlaces: 25,
    services: 20,
    queues: 15
  };

  // 1. Profile Picture Check
  const profilePictureResult = checkProfilePicture(provider);
  
  // 2. Provider Information Check
  const providerInfoResult = checkProviderInfo(provider);
  
  // 3. Providing Places Check
  const providingPlacesResult = checkProvidingPlaces(provider.providingPlaces || []);
  
  // 4. Services Check
  const servicesResult = checkServices(provider.services || []);
  
  // 5. Queues Check
  const queuesResult = checkQueues(provider.providingPlaces?.flatMap(place => place.queues || []) || []);

  // Calculate weighted overall percentage
  const overallPercentage = Math.round(
    (profilePictureResult.percentage * weights.profilePicture / 100) +
    (providerInfoResult.percentage * weights.providerInfo / 100) +
    (providingPlacesResult.percentage * weights.providingPlaces / 100) +
    (servicesResult.percentage * weights.services / 100) +
    (queuesResult.percentage * weights.queues / 100)
  );

  // Determine if profile is considered complete (80% threshold)
  const overallCompleted = overallPercentage >= 80;

  const shouldMarkAsComplete = overallPercentage === 100 && !provider.isSetupComplete;

  // Generate next steps and critical missing items
  const nextSteps = generateNextSteps({
    profilePicture: profilePictureResult,
    providerInfo: providerInfoResult,
    providingPlaces: providingPlacesResult,
    services: servicesResult,
    queues: queuesResult
  });

  const criticalMissing = generateCriticalMissing({
    profilePicture: profilePictureResult,
    providerInfo: providerInfoResult,
    providingPlaces: providingPlacesResult,
    services: servicesResult,
    queues: queuesResult
  });

  return {
    overallPercentage,
    overallCompleted,
    breakdown: {
      profilePicture: profilePictureResult,
      providerInfo: providerInfoResult,
      providingPlaces: providingPlacesResult,
      services: servicesResult,
      queues: queuesResult
    },
    nextSteps,
    criticalMissing,
    shouldMarkAsComplete,
  };
}

function checkProfilePicture(provider: SProvider) {
  const hasProfilePicture = !!provider.logoId;
  
  return {
    completed: hasProfilePicture,
    percentage: hasProfilePicture ? 100 : 0,
    details: hasProfilePicture ? 'Logo uploaded' : 'No Logo uploaded'
  };
}

function checkProviderInfo(provider: SProvider & { category?: ProviderCategory | null }) {
  // Add null/undefined safety guard
  if (!provider) {
    return {
      completed: false,
      percentage: 0,
      details: 'Provider information not available',
      requiredFields: {
        title: false,
        phone: false,
        presentation: false,
        category: false
      }
    };
  }

  const checks = {
    title: !!provider.title && provider.title.trim().length > 0,
    phone: !!provider.phone && provider.phone.trim().length > 0,
    presentation: !!provider.presentation && provider.presentation.trim().length > 10,
    category: !!provider.category
  };

  const completedFields = Object.values(checks).filter(Boolean).length;
  const totalFields = Object.keys(checks).length;
  const percentage = Math.round((completedFields / totalFields) * 100);

  const missingFields: string[] = [];
  if (!checks.title) missingFields.push('Business title/name');
  if (!checks.phone) missingFields.push('Contact phone number');
  if (!checks.presentation) missingFields.push('Business description/presentation');
  if (!checks.category) missingFields.push('Business category');

  return {
    completed: percentage === 100,
    percentage,
    details: missingFields.length === 0 
      ? 'All provider information completed' 
      : `Missing: ${missingFields.join(', ')}`,
    requiredFields: checks
  };
}

function checkProvidingPlaces(places: (SProvidingPlace & { detailedAddress?: Address , queues?: Queue[]})[]) {
  if (places.length === 0) {
    return {
      completed: false,
      percentage: 0,
      details: 'No service locations added',
      count: 0,
      validPlaces: 0
    };
  }

  // Check each place for required information
  const validPlaces = places.filter(place => 
    !!place.name && 
    !!place.detailedAddress && 
    !!place.detailedAddress.address && 
    !!place.detailedAddress.city
  );

  const percentage = places.length > 0 ? Math.round((validPlaces.length / places.length) * 100) : 0;

  let details = '';
  if (validPlaces.length === places.length) {
    details = `All ${places.length} location(s) have complete information`;
  } else {
    details = `${validPlaces.length} of ${places.length} location(s) have complete information`;
  }

  return {
    completed: validPlaces.length > 0 && validPlaces.length === places.length,
    percentage: Math.max(percentage, places.length > 0 ? 20 : 0), // Give some credit for having places
    details,
    count: places.length,
    validPlaces: validPlaces.length
  };
}

function checkServices(services: Service[]) {
  if (services.length === 0) {
    return {
      completed: false,
      percentage: 0,
      details: 'No services added',
      count: 0
    };
  }

  // Check for valid services (must have title and duration)
  const validServices = services.filter(service => 
    !!service.title && 
    service.duration > 0
  );

  const hasMinimumServices = validServices.length >= 1;
  const percentage = hasMinimumServices ? 100 : 0;

  return {
    completed: hasMinimumServices,
    percentage,
    details: hasMinimumServices 
      ? `${validServices.length} service(s) configured`
      : 'No valid services configured',
    count: services.length
  };
}

function checkQueues(queues: Queue[]) {
  if (queues.length === 0) {
    return {
      completed: false,
      percentage: 0,
      details: 'No queues/booking slots created',
      count: 0,
      activeQueues: 0
    };
  }

  const activeQueues = queues.filter(queue => queue.isActive);
  const hasMinimumQueues = activeQueues.length >= 1;
  const percentage = hasMinimumQueues ? 100 : 0;

  return {
    completed: hasMinimumQueues,
    percentage,
    details: hasMinimumQueues
      ? `${activeQueues.length} active queue(s) available for booking`
      : 'No active queues available for booking',
    count: queues.length,
    activeQueues: activeQueues.length
  };
}

function generateNextSteps(breakdown: CompletionBreakdown): string[] {
  const steps: string[] = [];

  if (!breakdown.profilePicture.completed) {
    steps.push('Upload a professional profile picture');
  }

  if (!breakdown.providerInfo.completed) {
    const missing = Object.entries(breakdown.providerInfo.requiredFields)
      .filter(([_, completed]) => !completed)
      .map(([field]) => {
        switch (field) {
          case 'title': return 'business title';
          case 'phone': return 'contact phone';
          case 'presentation': return 'business description';
          case 'category': return 'business category';
          default: return field;
        }
      });
    steps.push(`Complete provider information: ${missing.join(', ')}`);
  }

  if (!breakdown.providingPlaces.completed) {
    if (breakdown.providingPlaces.count === 0) {
      steps.push('Add at least one service location');
    } else {
      steps.push('Complete location information (name, address, city)');
    }
  }

  if (!breakdown.services.completed) {
    steps.push('Create at least one service offering');
  }

  if (!breakdown.queues.completed) {
    if (breakdown.queues.count === 0) {
      steps.push('Set up booking queues/time slots');
    } else {
      steps.push('Activate at least one queue for customer booking');
    }
  }

  return steps;
}

function generateCriticalMissing(breakdown: CompletionBreakdown): string[] {
  const critical: string[] = [];

  // Critical items are those that prevent the provider from accepting bookings
  if (breakdown.providingPlaces.count === 0) {
    critical.push('No service locations');
  }

  if (breakdown.services.count === 0) {
    critical.push('No services offered');
  }

  if (breakdown.queues.activeQueues === 0) {
    critical.push('No active booking queues');
  }

  if (!breakdown.providerInfo.requiredFields.title) {
    critical.push('No business name');
  }

  if (!breakdown.providerInfo.requiredFields.category) {
    critical.push('No business category');
  }

  return critical;
}

/**
 * Get a user-friendly completion status message
 */
export function getCompletionStatusMessage(result: ProfileCompletionResult): string {
  if (result.overallPercentage === 100) {
    return 'Your profile is complete! You\'re ready to accept bookings.';
  } else if (result.overallPercentage >= 80) {
    return 'Your profile is mostly complete. Consider finishing the remaining items.';
  } else if (result.overallPercentage >= 50) {
    return 'You\'re halfway there! Complete the remaining sections to start accepting bookings.';
  } else if (result.overallPercentage >= 25) {
    return 'Good start! Continue building your profile to attract customers.';
  } else {
    return 'Let\'s get started! Complete your profile to begin accepting bookings.';
  }
}

/**
 * Get color class for completion percentage (for UI styling)
 */
export function getCompletionColor(percentage: number): string {
  if (percentage >= 80) return 'text-green-600';
  if (percentage >= 50) return 'text-yellow-600';
  return 'text-red-600';
}

/**
 * Get priority level for each section
 */
export function getSectionPriority(sectionName: keyof CompletionBreakdown): 'high' | 'medium' | 'low' {
  const priorities: Record<keyof CompletionBreakdown, 'high' | 'medium' | 'low'> = {
    providerInfo: 'high',
    providingPlaces: 'high',
    services: 'high',
    queues: 'medium',
    profilePicture: 'low'
  };
  
  return priorities[sectionName];
} 