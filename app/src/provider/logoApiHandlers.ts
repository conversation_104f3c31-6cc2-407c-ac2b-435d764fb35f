import { type Request, type Response } from 'express';
import { HttpError } from 'wasp/server';
import { type File } from 'wasp/entities';
import * as z from 'zod';
import { createFile } from 'wasp/server/operations';
import { getDownloadFileSignedURLFromS3 } from '../file-upload/s3Utils';

// Image file types only for provider logos (subset of ALLOWED_FILE_TYPES)
const IMAGE_FILE_TYPES = [
  'image/jpeg',
  'image/png'
] as const;

// Validation schemas
const uploadProviderLogoApiSchema = z.object({
  fileType: z.enum(IMAGE_FILE_TYPES),
  fileName: z.string().min(1, 'File name is required'),
});

type UploadProviderLogoApiInput = z.infer<typeof uploadProviderLogoApiSchema>;

/**
 * POST /api/auth/provider/logo
 * Upload a logo for the authenticated provider
 */
export const handleUploadProviderLogo = async (req: Request, res: Response, context: any) => {
  try {
    // Check authentication
    if (!context.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    // Validate request body
    const validationResult = uploadProviderLogoApiSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        message: 'Invalid request data. Only image files are allowed for provider logos.',
        errors: validationResult.error.format()
      });
    }

    const { fileType, fileName } = validationResult.data;

    // Check if user has a provider profile
    const provider = await context.entities.SProvider.findUnique({
      where: { userId: context.user.id },
      include: { logo: true }
    });

    if (!provider) {
      return res.status(404).json({
        success: false,
        message: 'Provider profile not found'
      });
    }

    // Create the file record using existing operation
    const result = await createFile({ fileType, fileName }, context);

    // Get the created file record to get its ID
    const fileRecord = await context.entities.File.findFirst({
      where: {
        userId: context.user.id,
        name: fileName,
        type: fileType,
        key: result.s3UploadFields.key || result.s3UploadUrl.split('/').pop()
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    if (!fileRecord) {
      throw new HttpError(500, 'Failed to create file record');
    }

    // Update provider's logo reference
    const updatedProvider = await context.entities.SProvider.update({
      where: { id: provider.id },
      data: { logoId: fileRecord.id },
      include: {
        logo: true
      }
    });

    return res.status(200).json({
      success: true,
      message: 'Provider logo upload URL generated successfully',
      data: {
        uploadUrl: result.s3UploadUrl,
        uploadFields: result.s3UploadFields,
        file: {
          id: fileRecord.id,
          name: fileName,
          type: fileType,
          key: fileRecord.key
        },
        provider: {
          id: updatedProvider.id,
          logoId: updatedProvider.logoId
        }
      }
    });

  } catch (error: any) {
    console.error('[API] Error in provider logo upload:', error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error.message || 'Failed to generate provider logo upload URL';
    return res.status(statusCode).json({
      success: false,
      message
    });
  }
};

/**
 * DELETE /api/auth/provider/logo
 * Remove the logo for the authenticated provider
 */
export const handleRemoveProviderLogo = async (_req: Request, res: Response, context: any) => {
  try {
    // Check authentication
    if (!context.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    // Check if user has a provider profile
    const provider = await context.entities.SProvider.findUnique({
      where: { userId: context.user.id },
      include: { logo: true }
    });

    if (!provider) {
      return res.status(404).json({
        success: false,
        message: 'Provider profile not found'
      });
    }

    if (!provider.logoId) {
      return res.status(404).json({
        success: false,
        message: 'No logo found'
      });
    }

    // Remove the logo reference from provider
    await context.entities.SProvider.update({
      where: { id: provider.id },
      data: { logoId: null }
    });

    // Optionally delete the file record (you might want to keep it for audit purposes)
    // await context.entities.File.delete({
    //   where: { id: provider.logoId }
    // });

    return res.status(200).json({
      success: true,
      message: 'Provider logo removed successfully'
    });

  } catch (error: any) {
    console.error('[API] Error removing provider logo:', error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error.message || 'Failed to remove provider logo';
    return res.status(statusCode).json({
      success: false,
      message
    });
  }
};

/**
 * GET /api/auth/provider/logo
 * Get the logo for the authenticated provider
 */
export const handleGetProviderLogo = async (_req: Request, res: Response, context: any) => {
  try {
    // Check authentication
    if (!context.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    // Get current provider with logo
    const provider = await context.entities.SProvider.findUnique({
      where: { userId: context.user.id },
      include: { logo: true }
    });

    if (!provider) {
      return res.status(404).json({
        success: false,
        message: 'Provider not found'
      });
    }

    // If no logo, return null data
    if (!provider.logo) {
      return res.status(200).json({
        success: true,
        data: {
          hasLogo: false,
          logo: null
        }
      });
    }

    // Get download URL for the logo
    let downloadUrl: string | null = null;
    try {
      downloadUrl = await getDownloadFileSignedURLFromS3({ key: provider.logo.key });
    } catch (error) {
      console.warn('[API] Could not generate download URL for provider logo:', error);
      // We can still return the data without the URL if S3 fails
    }

    return res.status(200).json({
      success: true,
      data: {
        hasLogo: true,
        logo: {
          id: provider.logo.id,
          name: provider.logo.name,
          type: provider.logo.type,
          key: provider.logo.key,
          downloadUrl,
          createdAt: provider.logo.createdAt
        }
      }
    });

  } catch (error: any) {
    console.error('[API] Error getting provider logo:', error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error.message || 'Failed to fetch provider logo';
    return res.status(statusCode).json({
      success: false,
      message
    });
  }
};