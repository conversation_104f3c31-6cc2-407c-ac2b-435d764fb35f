import React, { useState, useEffect } from 'react';
import { Progress, List, Tag, Space, Divider, Button } from 'antd';
import { 
  UserOutlined, 
  ShopOutlined, 
  EnvironmentOutlined, 
  AppstoreOutlined, 
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  DownOutlined,
  UpOutlined,
  CloseOutlined
} from '@ant-design/icons';
import type { User, SProvider } from 'wasp/entities';
import { 
  calculateProfileCompletion, 
  getCompletionStatusMessage, 
  getCompletionColor,
  getSectionPriority,
  type ProfileCompletionData,
  type CompletionBreakdown 
} from '../utils/profileCompletion';

const { Title, Text, Paragraph } = Typography;
import { Typography } from 'antd';
import Card from 'antd/es/card/Card';

interface ProfileCompletionCardProps {
  data: ProfileCompletionData;
  showDetails?: boolean;
  onActionClick?: (section: string, action: string) => void;
  defaultExpanded?: boolean;
  onClose?: () => void;
}

export const ProfileCompletionCard: React.FC<ProfileCompletionCardProps> = ({
  data,
  showDetails = true,
  onActionClick,
  defaultExpanded = false,
  onClose
}) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);

  // Generate localStorage key based on user ID
  const getStorageKey = () => `profileCompletion_dismissed_${data.user.id}`;

  const handleClose = () => {
    // Save closed state to localStorage
    localStorage.setItem(getStorageKey(), 'true');
    onClose?.();
  };
  // Add error boundary and null checks
  if (!data || !data.user || !data.provider) {
    return (
      <Card title="Profile Completion" className="mb-4">
        <div className="text-center text-gray-500">
          Unable to load profile completion data
        </div>
      </Card>
    );
  }

  let completion;
  try {
    completion = calculateProfileCompletion(data);
  } catch (error) {
    console.error('Error calculating profile completion:', error);
    return (
      <Card title="Profile Completion" className="mb-4">
        <div className="text-center text-red-500">
          Error calculating profile completion
        </div>
      </Card>
    );
  }

  const { overallPercentage, breakdown, nextSteps, criticalMissing } = completion;

  const getSectionIcon = (section: string) => {
    switch (section) {
      case 'profilePicture': return <UserOutlined />;
      case 'providerInfo': return <ShopOutlined />;
      case 'providingPlaces': return <EnvironmentOutlined />;
      case 'services': return <AppstoreOutlined />;
      case 'queues': return <ClockCircleOutlined />;
      default: return <CheckCircleOutlined />;
    }
  };

  const getPriorityColor = (priority: 'high' | 'medium' | 'low') => {
    switch (priority) {
      case 'high': return 'red';
      case 'medium': return 'orange';
      case 'low': return 'blue';
      default: return 'gray';
    }
  };

  const getSectionTitle = (section: string) => {
    switch (section) {
      case 'profilePicture': return 'Profile Picture';
      case 'providerInfo': return 'Business Information';
      case 'providingPlaces': return 'Service Locations';
      case 'services': return 'Services';
      case 'queues': return 'Booking Queues';
      default: return section;
    }
  };

  return (
    <Card 
      title={
        <Space>
          <CheckCircleOutlined />
          Profile Completion
        </Space>
      }
      extra={
        <Space>
          <Tag color={overallPercentage >= 80 ? 'green' : overallPercentage >= 50 ? 'orange' : 'red'}>
            {overallPercentage}% Complete
          </Tag>
          <Button
            type="text"
            size="small"
            icon={isExpanded ? <UpOutlined /> : <DownOutlined />}
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? 'Hide Details' : 'Show Details'}
          </Button>
          {overallPercentage === 100 && (
            <Button
              type="text"
              size="small"
              icon={<CloseOutlined />}
              onClick={handleClose}
              title="Dismiss completion card"
            />
          )}
        </Space>
      }
    >
      {/* Overall Progress */}
      <div className="mb-6">
        <Progress
          percent={overallPercentage}
          strokeColor={{
            '0%': '#ff4d4f',
            '50%': '#faad14',
            '100%': '#52c41a',
          }}
          format={(percent: number) => `${percent}%`}
        />
        <Paragraph className="mt-2 mb-0">
          {getCompletionStatusMessage(completion)}
        </Paragraph>
        
        {/* Show critical items preview when collapsed */}
        {!isExpanded && criticalMissing.length > 0 && (
          <div className="mt-3">
            <Text type="danger" className="text-sm">
              <ExclamationCircleOutlined className="mr-1" />
              {criticalMissing.length} critical item{criticalMissing.length > 1 ? 's' : ''} need{criticalMissing.length === 1 ? 's' : ''} attention
            </Text>
          </div>
        )}
      </div>

      {/* Collapsible Details Section */}
      {isExpanded && (
        <>
          {/* Critical Missing Items */}
          {criticalMissing.length > 0 && (
            <>
              <Divider />
              <div className="mb-4">
                <Title level={5} className="text-red-600 mb-2">
                  <ExclamationCircleOutlined /> Critical Missing Items
                </Title>
                <List
                  size="small"
                  dataSource={criticalMissing}
                  renderItem={(item) => (
                    <List.Item>
                      <Text type="danger">• {item}</Text>
                    </List.Item>
                  )}
                />
              </div>
            </>
          )}

          {/* Section Breakdown */}
          {showDetails && (
            <>
              <Divider />
              <Title level={5}>Section Details</Title>
              <Space direction="vertical" className="w-full" size="middle">
                {(Object.entries(breakdown) as Array<[keyof CompletionBreakdown, CompletionBreakdown[keyof CompletionBreakdown]]>).map(([sectionKey, sectionData]) => {
                  const priority = getSectionPriority(sectionKey);
                  const sectionTitle = getSectionTitle(sectionKey);
                  
                  return (
                    <Card
                      key={sectionKey}
                      size="small"
                      className="mb-2"
                      title={
                        <Space>
                          {getSectionIcon(sectionKey)}
                          <span>{sectionTitle}</span>
                          <Tag color={getPriorityColor(priority)} size="small">
                            {priority} priority
                          </Tag>
                        </Space>
                      }
                      extra={
                        <Space>
                          {sectionData.completed ? (
                            <CheckCircleOutlined className="text-green-600" />
                          ) : (
                            <ExclamationCircleOutlined className="text-orange-600" />
                          )}
                          <Text>{sectionData.percentage}%</Text>
                        </Space>
                      }
                    >
                      <Progress
                        percent={sectionData.percentage}
                        size="small"
                        showInfo={false}
                        strokeColor={sectionData.completed ? '#52c41a' : '#faad14'}
                      />
                      <Text type="secondary" className="text-sm">
                        {sectionData.details}
                      </Text>
                      
                      {/* Additional details for specific sections */}
                      {'count' in sectionData && (
                        <div className="mt-2">
                          <Text className="text-xs text-gray-500">
                            {sectionKey === 'providingPlaces' && 'validPlaces' in sectionData && 
                              `${sectionData.validPlaces}/${sectionData.count} locations complete`
                            }
                            {sectionKey === 'services' && 
                              `${sectionData.count} service(s) created`
                            }
                            {sectionKey === 'queues' && 'activeQueues' in sectionData && 
                              `${sectionData.activeQueues}/${sectionData.count} queue(s) active`
                            }
                          </Text>
                        </div>
                      )}
                    </Card>
                  );
                })}
              </Space>
            </>
          )}

          {/* Next Steps */}
          {nextSteps.length > 0 && (
            <>
              <Divider />
              <Title level={5}>Next Steps</Title>
              <List
                size="small"
                dataSource={nextSteps}
                renderItem={(step, index) => (
                  <List.Item
                    actions={onActionClick ? [
                      <Button 
                        key="action"
                        type="link" 
                        size="small"
                        onClick={() => onActionClick('nextStep', String(step))}
                      >
                        Take Action
                      </Button>
                    ] : []}
                  >
                    <Text>
                      <span className="text-gray-500 mr-2">{index + 1}.</span>
                      {step}
                    </Text>
                  </List.Item>
                )}
              />
            </>
          )}
        </>
      )}
    </Card>
  );
};

// Utility function to check if completion card was dismissed
export const isProfileCompletionCardDismissed = (userId: string): boolean => {
  if (typeof window === 'undefined') return false; // SSR safety
  return localStorage.getItem(`profileCompletion_dismissed_${userId}`) === 'true';
};

// Utility function to reset dismissal (useful for testing or when user wants to see it again)
export const resetProfileCompletionCardDismissal = (userId: string): void => {
  if (typeof window === 'undefined') return; // SSR safety
  localStorage.removeItem(`profileCompletion_dismissed_${userId}`);
};

export default ProfileCompletionCard; 