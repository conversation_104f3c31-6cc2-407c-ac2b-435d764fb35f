import React from 'react';
import { Button, message } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';
import { resetProfileCompletionCardDismissal } from './ProfileCompletionCard';

interface ProfileCompletionManagerProps {
  userId: string;
  onReset?: () => void;
}

/**
 * Utility component to manage profile completion card visibility
 * Useful for testing or allowing users to bring back the dismissed card
 */
export const ProfileCompletionManager: React.FC<ProfileCompletionManagerProps> = ({
  userId,
  onReset
}) => {
  const handleReset = () => {
    resetProfileCompletionCardDismissal(userId);
    message.success('Profile completion card has been reset and will show again on next page load');
    onReset?.();
    
    // Optionally reload the page to show the card immediately
    window.location.reload();
  };

  return (
    <Button
      type="default"
      size="small"
      icon={<ReloadOutlined />}
      onClick={handleReset}
      title="Show profile completion card again"
    >
      Reset Profile Completion Card
    </Button>
  );
};

export default ProfileCompletionManager; 