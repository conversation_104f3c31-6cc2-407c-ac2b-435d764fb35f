import React from 'react';
import { useQuery } from 'wasp/client/operations';
import { getUserServiceProvider } from 'wasp/client/operations';
import { Spin, Alert } from 'antd';
import type { AuthUser } from 'wasp/auth';
import ProfileCompletionCard from './ProfileCompletionCard';
import type { ProfileCompletionData } from '../utils/profileCompletion';

interface ProfileCompletionExampleProps {
  user: AuthUser;
}

export const ProfileCompletionExample: React.FC<ProfileCompletionExampleProps> = ({ user }) => {
  // Fetch the provider data with all necessary relations
  const { 
    data: providerData, 
    isLoading, 
    error 
  } = useQuery(getUserServiceProvider, 
    { userId: user.id },
    { 
      enabled: !!user.id,
      // Include all relations needed for completion calculation
      select: (data: any) => data ? {
        ...data,
        providingPlaces: data.providingPlaces || [],
        services: data.services || [],
        queues: data.queues || []
      } : null
    }
  );

  const handleActionClick = (section: string, action: string) => {
    console.log(`Action clicked: ${section} - ${action}`);
    
    // Navigate to appropriate section or show modal
    switch (section) {
      case 'nextStep':
        if (action.includes('profile picture')) {
          // Navigate to profile picture upload
          console.log('Navigate to profile picture upload');
        } else if (action.includes('provider information')) {
          // Navigate to provider info form
          console.log('Navigate to provider info form');
        } else if (action.includes('service location')) {
          // Navigate to locations management
          console.log('Navigate to locations management');
        } else if (action.includes('service')) {
          // Navigate to services management
          console.log('Navigate to services management');
        } else if (action.includes('queue')) {
          // Navigate to queues management
          console.log('Navigate to queues management');
        }
        break;
      default:
        console.log('Unknown action:', section, action);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-48">
        <Spin size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message="Error loading provider data"
        description="Unable to calculate profile completion. Please try again."
        type="error"
        showIcon
      />
    );
  }

  if (!providerData) {
    return (
      <Alert
        message="No provider profile found"
        description="Please create a provider profile first."
        type="warning"
        showIcon
      />
    );
  }

  // Prepare data for the completion calculator
  const completionData: ProfileCompletionData = {
    user: user as any, // Cast to User type for compatibility
    provider: providerData as any // Cast with necessary relations
  };

  return (
    <div className="max-w-4xl mx-auto p-4">
      <ProfileCompletionCard
        data={completionData}
        showDetails={true}
        onActionClick={handleActionClick}
      />
    </div>
  );
};

export default ProfileCompletionExample;

// Usage in other components:
/*
import ProfileCompletionExample from '@src/provider/components/ProfileCompletionExample';

// In your provider dashboard or account page:
<ProfileCompletionExample user={user} />
*/ 