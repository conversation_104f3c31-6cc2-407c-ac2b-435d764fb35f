import React, { useState, useRef, useEffect } from 'react';
import { Avatar, Button, Upload, Modal, Progress, message, Space, Tooltip } from 'antd';
import { ShopOutlined, CameraOutlined, DeleteOutlined, UploadOutlined } from '@ant-design/icons';
import type { SProvider } from 'wasp/entities';
import { getDownloadFileSignedURL, useQuery } from 'wasp/client/operations';
import axios from 'axios';
import { cn } from '../../client/cn';

// Helper function to get auth headers
const getAuthHeaders = () => {
  const sessionId = localStorage.getItem('wasp:sessionId');
  if (!sessionId) return {};
  
  // Remove quotes if present and trim whitespace
  const cleanSessionId = sessionId.replace(/^"(.*)"$/, '$1').trim();
  console.log("sessionId : ", cleanSessionId);
  
  return { Authorization: `Bearer ${cleanSessionId}` };
};

// Image file types only for provider logos
const IMAGE_FILE_TYPES = ['image/jpeg', 'image/png'] as const;
const MAX_FILE_SIZE_BYTES = 5 * 1024 * 1024; // 5MB

interface ProviderLogoUploadProps {
  provider: SProvider;
  onUploadSuccess?: (fileId: string) => void;
  onRemoveSuccess?: () => void;
  size?: number;
  showUploadButton?: boolean;
}

export const ProviderLogoUpload: React.FC<ProviderLogoUploadProps> = ({
  provider,
  onUploadSuccess,
  onRemoveSuccess,
  size = 120,
  showUploadButton = true
}) => {
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [logoKey, setLogoKey] = useState<string>('');
  const [refreshKey, setRefreshKey] = useState<number>(0); // Force query refresh
  const fileInputRef = useRef<HTMLInputElement>(null);
  const BASE_URL = 'https://dapi-test.adscloud.org:8443'

  // Fetch file data when logoId changes
  useEffect(() => {
    if (provider.logoId) {
      // Use the proper API endpoint to fetch file data
      const authHeaders = getAuthHeaders();
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      };
      
      if (authHeaders.Authorization) {
        headers.Authorization = authHeaders.Authorization;
      }
      
      fetch(`${BASE_URL}/api/auth/files/${provider.logoId}`, {
        headers
      })
        .then(res => res.json())
        .then(response => {
          console.log('Logo file fetch response:', response);
          if (response.success && response.data?.key) {
            console.log('Setting logoKey to:', response.data.key);
            setLogoKey(response.data.key);
          } else {
            console.log('No logo key found in response');
            setLogoKey('');
          }
        })
        .catch(err => {
          console.log('Could not fetch logo file data:', err);
          // If we can't fetch the file, clear the key
          setLogoKey('');
        });
    } else {
      setLogoKey('');
    }
  }, [provider.logoId]);

  // Get current logo URL
  const { data: logoUrl, isLoading: isLoadingUrl, error: urlError, refetch: refetchUrl } = useQuery(
    getDownloadFileSignedURL,
    { key: logoKey },
    { 
      enabled: !!logoKey,
      retry: false,
      refetchOnWindowFocus: false,
    }
  );

  // Debug logging
  useEffect(() => {
    console.log('Provider logo debug:', {
      logoId: provider.logoId,
      logoKey,
      logoUrl,
      isLoadingUrl,
      urlError
    });
  }, [provider.logoId, logoKey, logoUrl, isLoadingUrl, urlError]);

  const validateFile = (file: File) => {
    if (file.size > MAX_FILE_SIZE_BYTES) {
      message.error(`File size exceeds ${MAX_FILE_SIZE_BYTES / 1024 / 1024}MB limit.`);
      return false;
    }

    if (!IMAGE_FILE_TYPES.includes(file.type as any)) {
      message.error('Only JPEG and PNG images are allowed for provider logos.');
      return false;
    }

    return true;
  };

  const handleFileSelect = (file: File) => {
    if (!validateFile(file)) {
      return;
    }

    setSelectedFile(file);
    
    // Create preview URL
    const reader = new FileReader();
    reader.onload = (e) => {
      setPreviewUrl(e.target?.result as string);
    };
    reader.readAsDataURL(file);
    
    setShowUploadModal(true);
  };

  const uploadProviderLogo = async () => {
    if (!selectedFile) return;

    try {
      setUploading(true);
      setUploadProgress(0);

      // Call our provider logo upload API
      const response = await axios.post(`${BASE_URL}/api/auth/provider/logo`, {
        fileType: selectedFile.type,
        fileName: selectedFile.name,
      }, {
        headers: getAuthHeaders()
      });

      if (!response.data.success) {
        throw new Error(response.data.message || 'Upload failed');
      }

      const { uploadUrl, uploadFields, file } = response.data.data;

      // Upload file to S3
      const formData = new FormData();
      Object.entries(uploadFields || {}).forEach(([key, value]) => {
        formData.append(key, value as string);
      });
      formData.append('file', selectedFile);

      await axios.post(uploadUrl, formData, {
        onUploadProgress: (progressEvent) => {
          if (progressEvent.total) {
            const percentage = Math.round((progressEvent.loaded / progressEvent.total) * 100);
            setUploadProgress(percentage);
          }
        },
      });

      message.success('Provider logo uploaded successfully!');
      onUploadSuccess?.(file.id);
      setShowUploadModal(false);
      
      // Update local state immediately
      setLogoKey(file.key);
      
      // Update the provider object locally (this will be reflected in the parent component)
      Object.assign(provider, { logoId: file.id });
      
      // Force refetch of the signed URL for the new image
      setTimeout(() => {
        refetchUrl();
      }, 100);
      
    } catch (error: any) {
      console.error('Provider logo upload error:', error);
      message.error(error.response?.data?.message || 'Failed to upload provider logo');
    } finally {
      setUploading(false);
      setUploadProgress(0);
      setSelectedFile(null);
      setPreviewUrl(null);
    }
  };

  const removeProviderLogo = async () => {
    try {
      const response = await axios.delete(`${BASE_URL}/api/auth/provider/logo`, {
        headers: getAuthHeaders()
      });
      
      if (!response.data.success) {
        throw new Error(response.data.message || 'Remove failed');
      }

      message.success('Provider logo removed successfully!');
      onRemoveSuccess?.();
      
      // Update local state immediately
      setLogoKey('');
      
      // Update the provider object locally
      Object.assign(provider, { logoId: null });
      
    } catch (error: any) {
      console.error('Provider logo removal error:', error);
      message.error(error.response?.data?.message || 'Failed to remove provider logo');
    }
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const currentLogoSrc = logoUrl || undefined;

  return (
    <div className="flex flex-col items-center space-y-4">
      {/* Provider Logo Display */}
      <div className="relative group">
        <Avatar
          size={size}
          src={currentLogoSrc}
          icon={<ShopOutlined />}
          className="shadow-lg border-2 border-white dark:border-gray-600"
        />
        
        {/* Hover overlay */}
        <div className="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer"
             onClick={handleUploadClick}>
          <CameraOutlined className="text-white text-xl" />
        </div>
      </div>

      {/* Upload/Remove Buttons */}
      {showUploadButton && (
        <Space>
          <Button
            type="default"
            icon={<UploadOutlined />}
            onClick={handleUploadClick}
            size="small"
          >
            {currentLogoSrc ? 'Change' : 'Upload'}
          </Button>
          
          {provider.logoId && (
            <Tooltip title="Remove provider logo">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                onClick={removeProviderLogo}
                size="small"
              />
            </Tooltip>
          )}
        </Space>
      )}

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept={IMAGE_FILE_TYPES.join(',')}
        onChange={(e) => {
          const file = e.target.files?.[0];
          if (file) {
            handleFileSelect(file);
          }
        }}
        style={{ display: 'none' }}
      />

      {/* Upload Confirmation Modal */}
      <Modal
        title="Upload Provider Logo"
        open={showUploadModal}
        onCancel={() => {
          setShowUploadModal(false);
          setSelectedFile(null);
          setPreviewUrl(null);
        }}
        footer={[
          <Button key="cancel" onClick={() => {
            setShowUploadModal(false);
            setSelectedFile(null);
            setPreviewUrl(null);
          }}>
            Cancel
          </Button>,
          <Button 
            key="upload" 
            type="primary" 
            loading={uploading}
            onClick={uploadProviderLogo}
          >
            {uploading ? `Uploading ${uploadProgress}%` : 'Upload'}
          </Button>,
        ]}
        destroyOnClose
      >
        <div className="flex flex-col items-center space-y-4">
          {previewUrl && (
            <Avatar
              size={120}
              src={previewUrl}
              className="shadow-lg border-2 border-gray-200"
            />
          )}
          
          {selectedFile && (
            <div className="text-center">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                <strong>File:</strong> {selectedFile.name}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                <strong>Size:</strong> {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
              </p>
            </div>
          )}
          
          {uploading && (
            <div className="w-full">
              <Progress percent={uploadProgress} />
            </div>
          )}
        </div>
      </Modal>
    </div>
  );
}; 