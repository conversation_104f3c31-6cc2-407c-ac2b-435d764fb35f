import { HttpError } from 'wasp/server';
import { searchProviders , getProviderCategories, getProviderAvailability, createCustomerAppointment, getUserServiceProvider, extendAppointment } from 'wasp/server/operations';
import { z } from 'zod';
import type { Request, Response } from 'express';
import { type ProviderCategory, SProvider, SProvidingPlace, Service, Queue } from 'wasp/entities';
import { type DailyAvailabilityResult } from './operations';
import { calculateProfileCompletion, type ProfileCompletionResult } from './utils/profileCompletion';
import { getTranslatedString } from '../server/translations';
import { LanguageCode } from '@prisma/client';
import { prisma } from 'wasp/server';

/**
 * Helper function to translate provider category fields
 */
const translateProviderCategoryFields = async (
  category: any,
  targetLanguage: LanguageCode
) => {
  const promises: Promise<string | null>[] = [];

  // Translate ProviderCategory fields
  promises.push(
    getTranslatedString(prisma, 'ProviderCategory', String(category.id), 'title', targetLanguage),
    getTranslatedString(prisma, 'ProviderCategory', String(category.id), 'description', targetLanguage)
  );

  const [translatedTitle, translatedDescription] = await Promise.all(promises);

  return {
    title: translatedTitle || category.title,
    description: translatedDescription || category.description
  };
};

// This schema matches the input for the searchProviders Wasp ACTION
const searchProvidersApiInputSchema = z.object({
  categoryId: z.coerce.number().int().optional(),
  q: z.string().optional(),
  city: z.string().optional(),
  skip: z.coerce.number().int().nonnegative().optional(),
  take: z.coerce.number().int().positive().optional(),
  targetLanguage: z.string().optional(),
});

export const handleSearchProviders = async (req: Request, res: Response, context: any) => {
  try {
    const validatedQuery = searchProvidersApiInputSchema.safeParse(req.query);

    if (!validatedQuery.success) {
      return res.status(400).json({ message: "Invalid query parameters", errors: validatedQuery.error.format() });
    }

    const args = validatedQuery.data;

    const results = await searchProviders(args, context);

    res.status(200).json(results);
  } catch (error: any) {
    console.error("Error in handleSearchProviders API:", error);
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({ message: error.message });
    }
    return res.status(500).json({ message: error.message || 'Failed to search providers via API.' });
  }
};

// Handler for getProviderCategories with translation support
export const handleGetProviderCategories = async (req: Request, res: Response, context: any) => {
  try {
    // Parse language parameter from query string
    const lang = req.query.lang as string;
    let targetLanguage: LanguageCode = LanguageCode.EN; // Default to English

    // Validate and set target language
    if (lang && Object.values(LanguageCode).includes(lang.toUpperCase() as LanguageCode)) {
      targetLanguage = lang.toUpperCase() as LanguageCode;
    }

    console.log(`[handleGetProviderCategories] Target language: ${targetLanguage}`);

    // Get categories without translation first
    const categories = await getProviderCategories({}, context as any);

    // Apply translations in parallel for all categories
    const translationPromises = categories.map(category =>
      translateProviderCategoryFields(category, targetLanguage)
    );
    const translatedFields = await Promise.all(translationPromises);

    // Transform to response format with translated values
    const translatedCategories = categories.map((category, index) => {
      const translated = translatedFields[index];

      return {
        ...category,
        title: translated.title,
        description: translated.description
      };
    });

    res.status(200).json(translatedCategories);
  } catch (error: any) {
    console.error("Error in handleGetProviderCategories API:", error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    res.status(statusCode).json({ message: error.message || 'Failed to fetch provider categories' });
  }
};

// --- API Handler for getProviderAvailability ---

const getProviderAvailabilityParamsSchema = z.object({
  sProvidingPlaceId: z.string().regex(/^\d+$/, "sProvidingPlaceId must be a number").transform(Number),
  serviceId: z.string().regex(/^\d+$/, "serviceId must be a number").transform(Number),
  startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "startDate must be in YYYY-MM-DD format"),
  endDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "endDate must be in YYYY-MM-DD format"),
  queueId: z.string().regex(/^\d+$/, "queueId must be a number").transform(Number).optional(),
});

export const handleGetProviderAvailability = async (req: Request, res: Response, context: any) => {
  try {
    const parsedQuery = getProviderAvailabilityParamsSchema.safeParse(req.query);
    if (!parsedQuery.success) {
      // Use ZodError's issues to provide a more detailed message
      const errorDetails = parsedQuery.error.issues.map(issue => `${issue.path.join('.')} - ${issue.message}`).join('; ');
      throw new HttpError(400, 'Invalid query parameters: ' + errorDetails);
    }

    const args = parsedQuery.data;

    const availabilityResults: DailyAvailabilityResult[] = await getProviderAvailability(args, context);
    res.json(availabilityResults);

  } catch (error: any) {
    console.error("[API] Failed to get provider availability:", error);
    const statusCode = error instanceof HttpError ? error.statusCode :
                   (error.name === 'ZodError' ? 400 : 500);
    const message = error instanceof HttpError ? error.message :
                   (error.name === 'ZodError' ? 'Invalid input: ' + JSON.stringify(error.errors) :
                   'Failed to fetch provider availability');
    res.status(statusCode).json({ message });
  }
};

// --- END API Handler for getProviderAvailability ---

// --- API Handler for createCustomerAppointment ---

// This schema should align with createCustomerAppointmentInputSchema in provider/operations.ts
// but receive dates as strings initially, then coerce them.
const createCustomerAppointmentApiSchema = z.object({
  placeId: z.number().int().positive(),
  serviceId: z.number().int().positive(),
  queueId: z.number().int().positive(),
  startTime: z.string().datetime({ message: "startTime must be a valid ISO 8601 date-time string" }).transform(val => new Date(val)),
  endTime: z.string().datetime({ message: "endTime must be a valid ISO 8601 date-time string" }).transform(val => new Date(val)),
  // notes: z.string().optional().nullable(), // If notes were part of the action
}).refine(data => data.endTime > data.startTime, {
  message: "End time must be after start time",
  path: ["endTime"],
});

export const handleCreateCustomerAppointment = async (req: Request, res: Response, context: any) => {
  if (!context.user) {
    // This should ideally be caught by Wasp's auth: true on the API route,
    // but an explicit check is good practice.
    return res.status(401).json({ message: "User not authenticated." });
  }

  try {
    const parsedBody = createCustomerAppointmentApiSchema.safeParse(req.body);
    if (!parsedBody.success) {
      const errorDetails = parsedBody.error.issues.map(issue => `${issue.path.join('.')} - ${issue.message}`).join('; ');
      throw new HttpError(400, 'Invalid request body: ' + errorDetails);
    }

    const args = parsedBody.data;

    // Ensure context.user is passed if the action relies on it directly
    // The createCustomerAppointment action in Wasp should automatically have access to context.user
    const newAppointment = await createCustomerAppointment(args, context);

    res.status(201).json(newAppointment);

  } catch (error: any) {
    console.error("[API] Failed to create customer appointment:", error);
    const statusCode = error instanceof HttpError ? error.statusCode :
                   (error.name === 'ZodError' ? 400 : 500);
    const message = error instanceof HttpError ? error.message :
                   (error.name === 'ZodError' ? 'Invalid input: ' + JSON.stringify(error.errors) :
                   'Failed to create customer appointment');
    res.status(statusCode).json({ message });
  }
};

// --- END API Handler for createCustomerAppointment --- 

// --- API Handler for getProviderProfileCompletion ---

export const handleGetProviderProfileCompletion = async (req: Request, res: Response, context: any) => {
  if (!context.user) {
    return res.status(401).json({ message: "User not authenticated." });
  }

  try {
    // Fetch the provider data with all necessary includes
    let provider = await getUserServiceProvider({ userId: context.user.id }, context);

    if (!provider) {
      throw new HttpError(404, 'Provider profile not found');
    }

    // Calculate profile completion using the existing utility
    const completionResult: ProfileCompletionResult = calculateProfileCompletion({
      user: context.user,
      provider: provider,
    });

    // If completion is 100% and not yet marked, update the provider
    if (completionResult.shouldMarkAsComplete) {
      // Re-fetch the provider with all includes after update
      provider = await context.entities.SProvider.update({
        where: { id: provider.id },
        data: { isSetupComplete: true },
        include: {
          category: true,
          providingPlaces: {
            include: {
              detailedAddress: true,
              queues: true,
            },
          },
          services: true,
          logo: true, // ensure logo is included as it is part of completion
        },
      });

      // Recalculate with the updated provider to ensure the response is consistent
      const newCompletionResult = calculateProfileCompletion({
        user: context.user,
        provider: provider,
      });

      return res.status(200).json({
        success: true,
        data: newCompletionResult,
        message: 'Profile completion calculated and status updated successfully.',
      });
    }


    res.status(200).json({
      success: true,
      data: completionResult,
      message: 'Profile completion calculated successfully',
    });
  } catch (error: any) {
    console.error('[API] Failed to calculate profile completion:', error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error instanceof HttpError ? error.message : 'Failed to calculate profile completion';
    res.status(statusCode).json({
      success: false,
      message,
      error: process.env.NODE_ENV === 'development' ? error.stack : undefined,
    });
  }
};

// --- END API Handler for getProviderProfileCompletion ---

// --- API Handler for extendAppointment ---

const extendAppointmentApiSchema = z.object({
  appointmentId: z.number().int().positive(),
  extensionMinutes: z.number().int().positive().max(60, "Extension cannot exceed 60 minutes"),
});

export const handleExtendAppointment = async (req: Request, res: Response, context: any) => {
  console.log("[API] handleExtendAppointment called with:", req.body);
  console.log("[API] User context:", context.user?.id);
  
  if (!context.user) {
    return res.status(401).json({ message: "User not authenticated." });
  }

  try {
    const parsedBody = extendAppointmentApiSchema.safeParse(req.body);
    if (!parsedBody.success) {
      const errorDetails = parsedBody.error.issues.map(issue => `${issue.path.join('.')} - ${issue.message}`).join('; ');
      console.error("[API] Validation failed:", errorDetails);
      throw new HttpError(400, 'Invalid request body: ' + errorDetails);
    }

    const args = parsedBody.data;
    console.log("[API] Validated args:", args);

    // Call the extendAppointment operation
    const extendedAppointment = await extendAppointment(args, context);

    console.log("[API] Successfully extended appointment");
    res.status(200).json(extendedAppointment);

  } catch (error: any) {
    console.error("[API] Failed to extend appointment:", error);
    console.error("[API] Error details:", {
      name: error.name,
      message: error.message,
      statusCode: error.statusCode,
      stack: error.stack
    });
    
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error instanceof HttpError ? error.message : 'Failed to extend appointment';
    
    res.status(statusCode).json({ message });
  }
};

// --- END API Handler for extendAppointment ---

// --- API Handler for getProviderDashboardMetrics ---

export const handleGetProviderDashboardMetrics = async (req: Request, res: Response, context: any) => {
  if (!context.user) {
    return res.status(401).json({ message: "User not authenticated." });
  }

  try {
    // Get the provider for the authenticated user
    const provider = await context.entities.SProvider.findUnique({
      where: { userId: context.user.id },
      select: { id: true }
    });

    if (!provider) {
      throw new HttpError(404, 'Provider profile not found');
    }

    const providerId = provider.id;
    const now = new Date();
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const todayEnd = new Date(todayStart.getTime() + 24 * 60 * 60 * 1000);

    const weekStart = new Date(todayStart);
    weekStart.setDate(todayStart.getDate() - todayStart.getDay());

    const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);

    // Get today's appointments with status breakdown
    const todayAppointments = await context.entities.Appointment.findMany({
      where: {
        customerFolder: { sProviderId: providerId },
        expectedAppointmentStartTime: {
          gte: todayStart,
          lt: todayEnd
        }
      },
      select: { status: true }
    });

    // Count appointments by status for today
    const todayStats = {
      total: todayAppointments.length,
      completed: todayAppointments.filter((apt: any) => apt.status === 'completed').length,
      pending: todayAppointments.filter((apt: any) => apt.status === 'pending').length,
      confirmed: todayAppointments.filter((apt: any) => apt.status === 'confirmed').length,
      cancelled: todayAppointments.filter((apt: any) => apt.status === 'canceled').length
    };

    // Get weekly appointments and calculate revenue
    const weeklyAppointments = await context.entities.Appointment.findMany({
      where: {
        customerFolder: { sProviderId: providerId },
        expectedAppointmentStartTime: {
          gte: weekStart,
          lt: todayEnd
        }
      },
      include: {
        service: { select: { price: true } },
        customerFolder: { select: { userId: true } }
      }
    });

    // Calculate weekly stats
    const weeklyRevenue = weeklyAppointments
      .filter((apt: any) => apt.status === 'completed')
      .reduce((sum: number, apt: any) => sum + (apt.service.price || 0), 0);

    const weeklyNewCustomers = new Set(
      weeklyAppointments.map((apt: any) => apt.customerFolder.userId)
    ).size;

    // Get monthly appointments and calculate revenue
    const monthlyAppointments = await context.entities.Appointment.findMany({
      where: {
        customerFolder: { sProviderId: providerId },
        expectedAppointmentStartTime: {
          gte: monthStart,
          lt: todayEnd
        }
      },
      include: {
        service: { select: { price: true } },
        customerFolder: { select: { userId: true } }
      }
    });

    // Calculate monthly stats
    const monthlyRevenue = monthlyAppointments
      .filter((apt: any) => apt.status === 'completed')
      .reduce((sum: number, apt: any) => sum + (apt.service.price || 0), 0);

    const monthlyNewCustomers = new Set(
      monthlyAppointments.map((apt: any) => apt.customerFolder.userId)
    ).size;

    const metrics = {
      todayAppointments: todayStats,
      weeklyStats: {
        appointments: weeklyAppointments.length,
        revenue: weeklyRevenue,
        newCustomers: weeklyNewCustomers
      },
      monthlyStats: {
        appointments: monthlyAppointments.length,
        revenue: monthlyRevenue,
        newCustomers: monthlyNewCustomers
      }
    };

    res.status(200).json(metrics);
  } catch (error: any) {
    console.error('[API] Failed to get provider dashboard metrics:', error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error instanceof HttpError ? error.message : 'Failed to get dashboard metrics';
    res.status(statusCode).json({ message });
  }
};

// --- END API Handler for getProviderDashboardMetrics ---

// --- API Handler for getProviderTodayAppointments ---

export const handleGetProviderTodayAppointments = async (req: Request, res: Response, context: any) => {
  if (!context.user) {
    return res.status(401).json({ message: "User not authenticated." });
  }

  try {
    // Get the provider for the authenticated user
    const provider = await context.entities.SProvider.findUnique({
      where: { userId: context.user.id },
      select: { id: true }
    });

    if (!provider) {
      throw new HttpError(404, 'Provider profile not found');
    }

    const providerId = provider.id;
    const now = new Date();
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const todayEnd = new Date(todayStart.getTime() + 24 * 60 * 60 * 1000);

    // Get today's appointments with customer and service details
    const appointments = await context.entities.Appointment.findMany({
      where: {
        customerFolder: { sProviderId: providerId },
        expectedAppointmentStartTime: {
          gte: todayStart,
          lt: todayEnd
        }
      },
      include: {
        customerFolder: {
          include: {
            customer: {
              select: { firstName: true, lastName: true }
            }
          }
        },
        service: {
          select: { title: true, duration: true }
        }
      },
      orderBy: {
        expectedAppointmentStartTime: 'asc'
      }
    });

    // Transform the data to match the expected response format
    const transformedAppointments = appointments.map((appointment: any) => ({
      id: appointment.id,
      customer: {
        firstName: appointment.customerFolder.customer.firstName || '',
        lastName: appointment.customerFolder.customer.lastName || ''
      },
      service: {
        title: appointment.service.title,
        duration: appointment.service.duration
      },
      expectedAppointmentStartTime: appointment.expectedAppointmentStartTime,
      status: appointment.status
    }));

    res.status(200).json({ appointments: transformedAppointments });
  } catch (error: any) {
    console.error('[API] Failed to get provider today appointments:', error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error instanceof HttpError ? error.message : 'Failed to get today appointments';
    res.status(statusCode).json({ message });
  }
};

// --- END API Handler for getProviderTodayAppointments ---

// --- API Handler for getProviderRevenueChart ---

const revenueChartParamsSchema = z.object({
  period: z.enum(['week', 'month']).default('week')
});

export const handleGetProviderRevenueChart = async (req: Request, res: Response, context: any) => {
  if (!context.user) {
    return res.status(401).json({ message: "User not authenticated." });
  }

  try {
    const parsedQuery = revenueChartParamsSchema.safeParse(req.query);
    if (!parsedQuery.success) {
      const errorDetails = parsedQuery.error.issues.map(issue => `${issue.path.join('.')} - ${issue.message}`).join('; ');
      throw new HttpError(400, 'Invalid query parameters: ' + errorDetails);
    }

    const { period } = parsedQuery.data;

    // Get the provider for the authenticated user
    const provider = await context.entities.SProvider.findUnique({
      where: { userId: context.user.id },
      select: { id: true }
    });

    if (!provider) {
      throw new HttpError(404, 'Provider profile not found');
    }

    const providerId = provider.id;
    const now = new Date();

    let labels: string[] = [];
    let startDate: Date;
    let endDate: Date;

    if (period === 'week') {
      // Get current week (Sunday to Saturday)
      const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      startDate = new Date(todayStart);
      startDate.setDate(todayStart.getDate() - todayStart.getDay()); // Start of week (Sunday)
      endDate = new Date(startDate);
      endDate.setDate(startDate.getDate() + 7); // End of week

      labels = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    } else {
      // Get current month
      startDate = new Date(now.getFullYear(), now.getMonth(), 1);
      endDate = new Date(now.getFullYear(), now.getMonth() + 1, 1);

      // Generate labels for each day of the month
      const daysInMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0).getDate();
      labels = Array.from({ length: daysInMonth }, (_, i) => (i + 1).toString());
    }

    // Get appointments for the period
    const appointments = await context.entities.Appointment.findMany({
      where: {
        customerFolder: { sProviderId: providerId },
        status: 'completed',
        expectedAppointmentStartTime: {
          gte: startDate,
          lt: endDate
        }
      },
      include: {
        service: { select: { price: true } }
      }
    });

    // Initialize data array with zeros
    const data = new Array(labels.length).fill(0);

    // Calculate revenue for each period
    appointments.forEach((appointment: any) => {
      if (appointment.expectedAppointmentStartTime && appointment.service.price) {
        const appointmentDate = new Date(appointment.expectedAppointmentStartTime);
        let index: number;

        if (period === 'week') {
          // Get day of week (0 = Sunday, 1 = Monday, etc.)
          index = appointmentDate.getDay();
        } else {
          // Get day of month (1-based)
          index = appointmentDate.getDate() - 1;
        }

        if (index >= 0 && index < data.length) {
          data[index] += appointment.service.price;
        }
      }
    });

    res.status(200).json({ labels, data });
  } catch (error: any) {
    console.error('[API] Failed to get provider revenue chart:', error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error instanceof HttpError ? error.message : 'Failed to get revenue chart data';
    res.status(statusCode).json({ message });
  }
};

// --- END API Handler for getProviderRevenueChart ---