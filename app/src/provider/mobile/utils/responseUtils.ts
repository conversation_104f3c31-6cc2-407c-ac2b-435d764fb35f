/**
 * Response utilities for Provider Mobile API
 * Standardized response formatting following Wasp patterns
 */

import type { Response } from 'express';
import { HttpError } from 'wasp/server';
import type { ApiResponse, ApiError, PaginatedResponse } from '../types';

/**
 * Send a successful API response
 */
export function sendSuccess<T>(
  res: Response,
  data: T,
  message?: string,
  statusCode: number = 200
): void {
  const response: ApiResponse<T> = {
    success: true,
    data,
    message
  };
  res.status(statusCode).json(response);
}

/**
 * Send a successful response for created resources
 */
export function sendCreated<T>(
  res: Response,
  data: T,
  message: string = 'Resource created successfully'
): void {
  sendSuccess(res, data, message, 201);
}

/**
 * Send a successful response for updated resources
 */
export function sendUpdated<T>(
  res: Response,
  data: T,
  message: string = 'Resource updated successfully'
): void {
  sendSuccess(res, data, message, 200);
}

/**
 * Send a successful response for deleted resources
 */
export function sendDeleted(
  res: Response,
  message: string = 'Resource deleted successfully'
): void {
  const response: ApiResponse = {
    success: true,
    message
  };
  res.status(200).json(response);
}

/**
 * Send an error response
 */
export function sendError(
  res: Response,
  error: ApiError | Error | HttpError | any,
  defaultMessage: string = 'An error occurred'
): void {
  let statusCode = 500;
  let message = defaultMessage;
  let details: any = undefined;

  if (error instanceof HttpError) {
    statusCode = error.statusCode;
    message = error.message;
    details = error.data;
  } else if (error instanceof Error) {
    message = error.message;
    // Check for specific error types
    if (error.name === 'ZodError') {
      statusCode = 400;
      details = (error as any).errors;
    }
  } else if (typeof error === 'object' && error !== null) {
    statusCode = error.statusCode || 500;
    message = error.message || defaultMessage;
    details = error.details;
  }

  const response: ApiResponse = {
    success: false,
    message,
    errors: details ? [details] : undefined
  };

  res.status(statusCode).json(response);
}

/**
 * Send a validation error response
 */
export function sendValidationError(
  res: Response,
  errors: any[],
  message: string = 'Validation failed'
): void {
  const response: ApiResponse = {
    success: false,
    message,
    errors
  };
  res.status(400).json(response);
}

/**
 * Send an unauthorized error response
 */
export function sendUnauthorized(
  res: Response,
  message: string = 'Unauthorized access'
): void {
  const response: ApiResponse = {
    success: false,
    message
  };
  res.status(401).json(response);
}

/**
 * Send a forbidden error response
 */
export function sendForbidden(
  res: Response,
  message: string = 'Access forbidden'
): void {
  const response: ApiResponse = {
    success: false,
    message
  };
  res.status(403).json(response);
}

/**
 * Send a not found error response
 */
export function sendNotFound(
  res: Response,
  message: string = 'Resource not found'
): void {
  const response: ApiResponse = {
    success: false,
    message
  };
  res.status(404).json(response);
}

/**
 * Send a conflict error response
 */
export function sendConflict(
  res: Response,
  message: string = 'Resource conflict'
): void {
  const response: ApiResponse = {
    success: false,
    message
  };
  res.status(409).json(response);
}

/**
 * Send a paginated response
 */
export function sendPaginated<T>(
  res: Response,
  data: T[],
  total: number,
  page: number,
  limit: number,
  message?: string
): void {
  const totalPages = Math.ceil(total / limit);
  const hasNext = page < totalPages;
  const hasPrev = page > 1;

  const response: ApiResponse<PaginatedResponse<T>> = {
    success: true,
    data: {
      data,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext,
        hasPrev
      }
    },
    message
  };

  res.status(200).json(response);
}

/**
 * Format Zod validation errors for API response
 */
export function formatZodErrors(zodError: any): any[] {
  if (!zodError.errors) return [];
  
  return zodError.errors.map((error: any) => ({
    field: error.path.join('.'),
    message: error.message,
    code: error.code
  }));
}

/**
 * Log API errors with context
 */
export function logApiError(
  endpoint: string,
  error: any,
  context?: any
): void {
  console.error(`[Provider Mobile API] Error in ${endpoint}:`, {
    error: error.message || error,
    stack: error.stack,
    context
  });
}

/**
 * Async error handler wrapper for API routes
 */
export function asyncHandler(
  fn: (req: any, res: any, context: any) => Promise<void>
) {
  return async (req: any, res: any, context: any) => {
    try {
      await fn(req, res, context);
    } catch (error) {
      logApiError(req.originalUrl || req.url, error, {
        method: req.method,
        params: req.params,
        query: req.query,
        body: req.body
      });
      sendError(res, error);
    }
  };
}
