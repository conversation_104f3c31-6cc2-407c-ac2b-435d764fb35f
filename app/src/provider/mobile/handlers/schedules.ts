import { Request, Response } from 'express';
import { 
  sendSuccess, 
  sendError, 
  sendCreated,
  asyncH<PERSON><PERSON>,
  validateAndExtract,
  type ProviderApiContext,
  type ScheduleResponse,
  type CreateScheduleRequest,
  type UpdateScheduleRequest,
  getSchedulesSchema,
  createScheduleSchema,
  updateScheduleSchema
} from '../index';

// Import existing operations
import { getSProviderOpenings, updateSProviderOpenings } from '../../operations';

/**
 * GET /api/providers/schedules
 * Get provider schedules with filtering options
 */
export const getProviderSchedules = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Validate query parameters
    const queryData = validateAndExtract(getSchedulesSchema, { query: req.query });
    
    // Get provider schedules using existing operation
    const openings = await getSProviderOpenings({}, context);
    
    // Transform openings to schedule format
    const schedules: ScheduleResponse[] = openings.map(opening => ({
      id: opening.id,
      dayOfWeek: getDayOfWeekNumber(opening.dayOfWeek),
      dayName: opening.dayOfWeek,
      isActive: opening.isActive,
      timeSlots: (opening as any).hours?.map((hour: any) => ({
        startTime: hour.timeFrom,
        endTime: hour.timeTo
      })) || [],
      locationId: opening.sProvidingPlaceId,
      type: opening.type || 'regular',
      effectiveFrom: opening.createdAt,
      effectiveTo: null // Not available in current schema
    }));

    // Apply filters if provided
    let filteredSchedules = schedules;

    if (queryData.query.locationId) {
      filteredSchedules = filteredSchedules.filter(s => s.locationId === queryData.query.locationId);
    }

    sendSuccess(res, filteredSchedules, 'Provider schedules retrieved successfully');
  } catch (error: any) {
    console.error('[getProviderSchedules] Error:', error);
    sendError(res, error, 'Failed to retrieve provider schedules');
  }
});

/**
 * POST /api/providers/schedules
 * Create new provider schedule
 */
export const createProviderSchedule = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Validate request data
    const scheduleData = validateAndExtract(createScheduleSchema, req.body);
    
    // Transform to the format expected by updateSProviderOpenings
    const updateData = {
      placeId: scheduleData.locationId,
      schedule: [{
        dayOfWeek: getDayOfWeekName(scheduleData.dayOfWeek),
        isActive: scheduleData.isActive ?? true,
        hours: scheduleData.timeSlots?.map(slot => ({
          timeFrom: slot.startTime,
          timeTo: slot.endTime
        })) || []
      }]
    };

    // Use existing operation to update/create schedule
    await updateSProviderOpenings(updateData, context);
    
    // Get the updated schedules to return the created one
    const openings = await getSProviderOpenings({}, context);
    const createdOpening = openings.find(o => 
      o.dayOfWeek === getDayOfWeekName(scheduleData.dayOfWeek) && 
      o.sProvidingPlaceId === scheduleData.locationId
    );

    if (!createdOpening) {
      return sendError(res, { statusCode: 500, message: 'Failed to create schedule' });
    }

    const response: ScheduleResponse = {
      id: createdOpening.id,
      dayOfWeek: scheduleData.dayOfWeek,
      dayName: createdOpening.dayOfWeek,
      isActive: createdOpening.isActive,
      timeSlots: (createdOpening as any).hours?.map((hour: any) => ({
        startTime: hour.timeFrom,
        endTime: hour.timeTo
      })) || [],
      locationId: createdOpening.sProvidingPlaceId,
      type: createdOpening.type || 'regular',
      effectiveFrom: createdOpening.createdAt,
      effectiveTo: null
    };

    sendCreated(res, response, 'Provider schedule created successfully');
  } catch (error: any) {
    console.error('[createProviderSchedule] Error:', error);
    sendError(res, error, 'Failed to create provider schedule');
  }
});

/**
 * PATCH /api/providers/schedules/:id
 * Update existing provider schedule
 */
export const updateProviderSchedule = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Get schedule ID from URL parameters
    const scheduleId = parseInt(req.params.id);
    if (!scheduleId || isNaN(scheduleId)) {
      return sendError(res, { statusCode: 400, message: 'Invalid schedule ID' });
    }

    // Validate request data
    const updateData = validateAndExtract(updateScheduleSchema, req.body);
    
    // Get current schedules to find the one being updated
    const currentOpenings = await getSProviderOpenings({}, context);
    const currentOpening = currentOpenings.find(o => o.id === scheduleId);
    
    if (!currentOpening) {
      return sendError(res, { statusCode: 404, message: 'Schedule not found' });
    }

    // Prepare update data with merged values
    const scheduleUpdateData = {
      placeId: currentOpening.sProvidingPlaceId,
      schedule: [{
        dayOfWeek: updateData.dayOfWeek !== undefined ? getDayOfWeekName(updateData.dayOfWeek) : currentOpening.dayOfWeek,
        isActive: updateData.isActive !== undefined ? updateData.isActive : currentOpening.isActive,
        hours: updateData.timeSlots ? updateData.timeSlots.map(slot => ({
          timeFrom: slot.startTime,
          timeTo: slot.endTime
        })) : (currentOpening as any).hours?.map((hour: any) => ({
          timeFrom: hour.timeFrom,
          timeTo: hour.timeTo
        })) || []
      }]
    };

    // Update using existing operation
    await updateSProviderOpenings(scheduleUpdateData, context);
    
    // Get the updated schedule to return
    const updatedOpenings = await getSProviderOpenings({}, context);
    const updatedOpening = updatedOpenings.find(o => o.id === scheduleId);

    if (!updatedOpening) {
      return sendError(res, { statusCode: 500, message: 'Failed to update schedule' });
    }

    const response: ScheduleResponse = {
      id: updatedOpening.id,
      dayOfWeek: getDayOfWeekNumber(updatedOpening.dayOfWeek),
      dayName: updatedOpening.dayOfWeek,
      isActive: updatedOpening.isActive,
      timeSlots: (updatedOpening as any).hours?.map((hour: any) => ({
        startTime: hour.timeFrom,
        endTime: hour.timeTo
      })) || [],
      locationId: updatedOpening.sProvidingPlaceId,
      type: updatedOpening.type || 'regular',
      effectiveFrom: updatedOpening.createdAt,
      effectiveTo: null
    };

    sendSuccess(res, response, 'Provider schedule updated successfully');
  } catch (error: any) {
    console.error('[updateProviderSchedule] Error:', error);
    sendError(res, error, 'Failed to update provider schedule');
  }
});

/**
 * DELETE /api/providers/schedules/:id
 * Delete provider schedule
 */
export const deleteProviderSchedule = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Get schedule ID from URL parameters
    const scheduleId = parseInt(req.params.id);
    if (!scheduleId || isNaN(scheduleId)) {
      return sendError(res, { statusCode: 400, message: 'Invalid schedule ID' });
    }

    // Get current schedules to find the one being deleted
    const currentOpenings = await getSProviderOpenings({}, context);
    const currentOpening = currentOpenings.find(o => o.id === scheduleId);
    
    if (!currentOpening) {
      return sendError(res, { statusCode: 404, message: 'Schedule not found' });
    }

    // Delete by setting isActive to false and removing all hours
    const deleteData = {
      placeId: currentOpening.sProvidingPlaceId,
      schedule: [{
        dayOfWeek: currentOpening.dayOfWeek,
        isActive: false,
        hours: [] // Remove all time slots
      }]
    };

    // Update using existing operation
    await updateSProviderOpenings(deleteData, context);
    
    const response: ScheduleResponse = {
      id: currentOpening.id,
      dayOfWeek: getDayOfWeekNumber(currentOpening.dayOfWeek),
      dayName: currentOpening.dayOfWeek,
      isActive: false,
      timeSlots: [],
      locationId: currentOpening.sProvidingPlaceId,
      type: currentOpening.type || 'regular',
      effectiveFrom: currentOpening.createdAt,
      effectiveTo: new Date()
    };

    sendSuccess(res, response, 'Provider schedule deleted successfully');
  } catch (error: any) {
    console.error('[deleteProviderSchedule] Error:', error);
    sendError(res, error, 'Failed to delete provider schedule');
  }
});

// Helper functions
function getDayOfWeekNumber(dayName: string): number {
  const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  return days.indexOf(dayName);
}

function getDayOfWeekName(dayNumber: number): string {
  const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  return days[dayNumber] || 'Monday';
}
