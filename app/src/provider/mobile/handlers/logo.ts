import { type Request, type Response } from 'express';
import { HttpError } from 'wasp/server';
import { type File, type SProvider } from 'wasp/entities';
import * as z from 'zod';
import { createFile } from 'wasp/server/operations';
import { getDownloadFileSignedURLFromS3 } from '../../../file-upload/s3Utils';

// Mobile-specific validation schemas
const uploadLogoMobileSchema = z.object({
  fileType: z.enum(['image/jpeg', 'image/png']),
  fileName: z.string().min(1, 'File name is required'),
  fileSize: z.number().optional(),
});

const logoResponseSchema = {
  id: z.string(),
  name: z.string(),
  type: z.string(),
  key: z.string(),
  uploadUrl: z.string().optional(),
  downloadUrl: z.string().optional(),
};

/**
 * GET /api/auth/providers/mobile/logo
 * Get current provider logo information for mobile
 */
export const getProviderLogo = async (req: Request, res: Response, context: any) => {
  try {
    console.log('[Mobile API] Getting provider logo for user:', context.user?.id);

    if (!context.user) {
      return res.status(401).json({
        success: false,
        error: 'AUTHENTICATION_REQUIRED',
        message: 'Authentication required'
      });
    }

    // Get provider with logo information
    const provider = await context.entities.SProvider.findUnique({
      where: { userId: context.user.id },
      include: { 
        logo: true,
        category: true 
      }
    });

    if (!provider) {
      return res.status(404).json({
        success: false,
        error: 'PROVIDER_NOT_FOUND',
        message: 'Provider profile not found'
      });
    }

    // If no logo, return null logo data
    if (!provider.logo) {
      return res.status(200).json({
        success: true,
        data: {
          provider: {
            id: provider.id,
            title: provider.title,
            hasLogo: false
          },
          logo: null
        }
      });
    }

    // Get download URL for the logo
    let downloadUrl: string | null = null;
    try {
      downloadUrl = await getDownloadFileSignedURLFromS3({ key: provider.logo.key });
    } catch (error) {
      console.warn('[Mobile API] Could not generate download URL:', error);
    }

    return res.status(200).json({
      success: true,
      data: {
        provider: {
          id: provider.id,
          title: provider.title,
          hasLogo: true
        },
        logo: {
          id: provider.logo.id,
          name: provider.logo.name,
          type: provider.logo.type,
          key: provider.logo.key,
          downloadUrl,
          createdAt: provider.logo.createdAt
        }
      }
    });

  } catch (error: any) {
    console.error('[Mobile API] Error getting provider logo:', error);
    return res.status(500).json({
      success: false,
      error: 'INTERNAL_SERVER_ERROR',
      message: error.message || 'Failed to fetch provider logo'
    });
  }
};

/**
 * POST /api/auth/providers/mobile/logo
 * Upload provider logo for mobile
 */
export const uploadProviderLogo = async (req: Request, res: Response, context: any) => {
  try {
    console.log('[Mobile API] Uploading provider logo for user:', context.user?.id);
    console.log('[Mobile API] Request body:', req.body);

    if (!context.user) {
      return res.status(401).json({
        success: false,
        error: 'AUTHENTICATION_REQUIRED',
        message: 'Authentication required'
      });
    }

    // Validate request body
    const validationResult = uploadLogoMobileSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        error: 'VALIDATION_ERROR',
        message: 'Invalid request data',
        details: validationResult.error.format()
      });
    }

    const { fileType, fileName, fileSize } = validationResult.data;

    // Additional file size validation
    if (fileSize && fileSize > 5 * 1024 * 1024) { // 5MB limit
      return res.status(400).json({
        success: false,
        error: 'FILE_TOO_LARGE',
        message: 'File size exceeds 5MB limit'
      });
    }

    // Check if user has a provider profile
    const provider = await context.entities.SProvider.findUnique({
      where: { userId: context.user.id },
      include: { logo: true }
    });

    if (!provider) {
      return res.status(404).json({
        success: false,
        error: 'PROVIDER_NOT_FOUND',
        message: 'Provider profile not found'
      });
    }

    // Create the file record and get S3 upload URL
    const uploadResult = await createFile({ fileType, fileName }, context);

    if (!uploadResult || !uploadResult.s3UploadUrl) {
      throw new HttpError(500, 'Failed to generate upload URL');
    }

    // Get the created file record
    const fileRecord = await context.entities.File.findFirst({
      where: {
        userId: context.user.id,
        name: fileName,
        type: fileType,
        key: uploadResult.s3UploadFields.key || uploadResult.s3UploadUrl.split('/').pop()
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    if (!fileRecord) {
      throw new HttpError(500, 'Failed to create file record');
    }

    // Update provider's logo reference
    await context.entities.SProvider.update({
      where: { id: provider.id },
      data: { logoId: fileRecord.id }
    });

    console.log('[Mobile API] Logo upload initialized successfully');

    return res.status(200).json({
      success: true,
      message: 'Logo upload URL generated successfully',
      data: {
        uploadUrl: uploadResult.s3UploadUrl,
        uploadFields: uploadResult.s3UploadFields,
        file: {
          id: fileRecord.id,
          name: fileName,
          type: fileType,
          key: fileRecord.key
        },
        instructions: {
          method: 'POST',
          url: uploadResult.s3UploadUrl,
          fields: uploadResult.s3UploadFields,
          fileFieldName: 'file'
        }
      }
    });

  } catch (error: any) {
    console.error('[Mobile API] Error uploading provider logo:', error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    return res.status(statusCode).json({
      success: false,
      error: 'UPLOAD_ERROR',
      message: error.message || 'Failed to upload provider logo'
    });
  }
};

/**
 * DELETE /api/auth/providers/mobile/logo
 * Remove provider logo for mobile
 */
export const removeProviderLogo = async (req: Request, res: Response, context: any) => {
  try {
    console.log('[Mobile API] Removing provider logo for user:', context.user?.id);

    if (!context.user) {
      return res.status(401).json({
        success: false,
        error: 'AUTHENTICATION_REQUIRED',
        message: 'Authentication required'
      });
    }

    // Get provider with logo information
    const provider = await context.entities.SProvider.findUnique({
      where: { userId: context.user.id },
      include: { logo: true }
    });

    if (!provider) {
      return res.status(404).json({
        success: false,
        error: 'PROVIDER_NOT_FOUND',
        message: 'Provider profile not found'
      });
    }

    if (!provider.logoId) {
      return res.status(404).json({
        success: false,
        error: 'NO_LOGO_FOUND',
        message: 'No logo found to remove'
      });
    }

    // Remove the logo reference from provider
    await context.entities.SProvider.update({
      where: { id: provider.id },
      data: { logoId: null }
    });

    console.log('[Mobile API] Logo removed successfully');

    return res.status(200).json({
      success: true,
      message: 'Provider logo removed successfully',
      data: {
        providerId: provider.id,
        removedLogoId: provider.logoId
      }
    });

  } catch (error: any) {
    console.error('[Mobile API] Error removing provider logo:', error);
    return res.status(500).json({
      success: false,
      error: 'REMOVAL_ERROR',
      message: error.message || 'Failed to remove provider logo'
    });
  }
};

/**
 * POST /api/auth/providers/mobile/logo/confirm
 * Confirm logo upload completion for mobile (optional endpoint for post-upload verification)
 */
export const confirmLogoUpload = async (req: Request, res: Response, context: any) => {
  try {
    console.log('[Mobile API] Confirming logo upload for user:', context.user?.id);

    if (!context.user) {
      return res.status(401).json({
        success: false,
        error: 'AUTHENTICATION_REQUIRED',
        message: 'Authentication required'
      });
    }

    const { fileId } = req.body;

    if (!fileId) {
      return res.status(400).json({
        success: false,
        error: 'MISSING_FILE_ID',
        message: 'File ID is required'
      });
    }

    // Verify the file exists and belongs to the user
    const file = await context.entities.File.findFirst({
      where: {
        id: fileId,
        userId: context.user.id
      }
    });

    if (!file) {
      return res.status(404).json({
        success: false,
        error: 'FILE_NOT_FOUND',
        message: 'File not found or access denied'
      });
    }

    // Get provider and verify logo is set
    const provider = await context.entities.SProvider.findUnique({
      where: { userId: context.user.id },
      include: { logo: true }
    });

    if (!provider || provider.logoId !== fileId) {
      return res.status(400).json({
        success: false,
        error: 'LOGO_MISMATCH',
        message: 'Logo file does not match provider record'
      });
    }

    // Generate download URL for confirmation
    let downloadUrl: string | null = null;
    try {
      downloadUrl = await getDownloadFileSignedURLFromS3({ key: file.key });
    } catch (error) {
      console.warn('[Mobile API] Could not generate download URL for confirmation:', error);
    }

    return res.status(200).json({
      success: true,
      message: 'Logo upload confirmed successfully',
      data: {
        provider: {
          id: provider.id,
          title: provider.title,
          hasLogo: true
        },
        logo: {
          id: file.id,
          name: file.name,
          type: file.type,
          key: file.key,
          downloadUrl,
          createdAt: file.createdAt
        }
      }
    });

  } catch (error: any) {
    console.error('[Mobile API] Error confirming logo upload:', error);
    return res.status(500).json({
      success: false,
      error: 'CONFIRMATION_ERROR',
      message: error.message || 'Failed to confirm logo upload'
    });
  }
}; 