/**
 * Provider Service Category API Handlers
 * Handles provider service category management endpoints
 */

import type { Request, Response } from 'express';
import { 
  sendSuccess, 
  sendError, 
  sendCreated,
  asyncHandler,
  type ProviderApiContext 
} from '../index';
import { 
  createServiceCategorySchema, 
  updateServiceCategorySchema,
  validateAndExtract 
} from '../utils/validationUtils';
import { 
  getServiceCategories as getServiceCategoriesOp,
  createServiceCategory as createServiceCategoryOp,
  updateServiceCategory as updateServiceCategoryOp,
  deleteServiceCategory as deleteServiceCategoryOp
} from '../../operations';
import type { ServiceCategoryResponse, CreateServiceCategoryRequest, UpdateServiceCategoryRequest } from '../types';

/**
 * GET /api/providers/service-categories
 * Get all provider service categories
 */
export const getProviderServiceCategories = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Call the operation to get service categories
    const categories = await getServiceCategoriesOp(undefined, context);
    
    // Transform to response format
    const response: ServiceCategoryResponse[] = categories.map(category => ({
      id: category.id,
      title: category.title,
      sProviderId: category.sProviderId
    }));

    sendSuccess(res, response, 'Provider service categories retrieved successfully');
  } catch (error: any) {
    console.error('[getProviderServiceCategories] Error:', error);
    sendError(res, error, 'Failed to retrieve provider service categories');
  }
});

/**
 * POST /api/providers/service-categories
 * Create a new provider service category
 */
export const createProviderServiceCategory = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Validate request data
    const categoryData = validateAndExtract(createServiceCategorySchema, req.body);
    
    // Call the operation to create the service category
    const newCategory = await createServiceCategoryOp(categoryData, context);
    
    // Transform to response format
    const response: ServiceCategoryResponse = {
      id: newCategory.id,
      title: newCategory.title,
      sProviderId: newCategory.sProviderId
    };

    sendCreated(res, response, 'Provider service category created successfully');
  } catch (error: any) {
    console.error('[createProviderServiceCategory] Error:', error);
    sendError(res, error, 'Failed to create provider service category');
  }
});

/**
 * PUT /api/providers/service-categories/:id
 * Update a provider service category
 */
export const updateProviderServiceCategory = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Get category ID from URL parameters
    const categoryId = parseInt(req.params.id);
    if (!categoryId || isNaN(categoryId)) {
      return sendError(res, { statusCode: 400, message: 'Invalid category ID' });
    }

    // Validate request data
    const updateData = validateAndExtract(updateServiceCategorySchema, req.body);
    
    // Call the operation to update the service category
    const updatedCategory = await updateServiceCategoryOp({
      categoryId,
      ...updateData
    }, context);
    
    // Transform to response format
    const response: ServiceCategoryResponse = {
      id: updatedCategory.id,
      title: updatedCategory.title,
      sProviderId: updatedCategory.sProviderId
    };

    sendSuccess(res, response, 'Provider service category updated successfully');
  } catch (error: any) {
    console.error('[updateProviderServiceCategory] Error:', error);
    sendError(res, error, 'Failed to update provider service category');
  }
});

/**
 * DELETE /api/providers/service-categories/:id
 * Delete a provider service category
 */
export const deleteProviderServiceCategory = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Get category ID from URL parameters
    const categoryId = parseInt(req.params.id);
    if (!categoryId || isNaN(categoryId)) {
      return sendError(res, { statusCode: 400, message: 'Invalid category ID' });
    }

    // Call the operation to delete the service category
    await deleteServiceCategoryOp({ categoryId }, context);

    sendSuccess(res, { id: categoryId }, 'Provider service category deleted successfully');
  } catch (error: any) {
    console.error('[deleteProviderServiceCategory] Error:', error);
    sendError(res, error, 'Failed to delete provider service category');
  }
});
