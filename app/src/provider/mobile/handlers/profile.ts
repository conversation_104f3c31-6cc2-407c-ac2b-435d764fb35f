/**
 * Provider Profile API Handlers
 * Handles provider profile management endpoints
 */

import type { Request, Response } from 'express';
import {
  sendSuc<PERSON>,
  sendError,
  asyncHandler,
  validateAndExtract,
  updateProviderProfileSchema,
  type ProviderProfileResponse,
  type UpdateProviderProfileRequest
} from '../index';
import { getUserServiceProvider, updateSProvider } from '../../operations';
import { getTranslatedString, translateAndStore } from '../../../server/translations';
import { LanguageCode } from '@prisma/client';
import { prisma } from 'wasp/server';

/**
 * Helper function to translate provider profile fields
 */
const translateProviderProfileFields = async (
  provider: any,
  targetLanguage: LanguageCode,
  context: any
) => {
  const promises: Promise<string | null>[] = [];

  // Translate SProvider fields
  promises.push(
    getTranslatedString(prisma, 'SProvider', String(provider.id), 'title', targetLanguage),
    getTranslatedString(prisma, 'SProvider', String(provider.id), 'presentation', targetLanguage)
  );

  const [translatedTitle, translatedPresentation] = await Promise.all(promises);

  return {
    title: translatedTitle || provider.title,
    presentation: translatedPresentation || provider.presentation
  };
};

/**
 * Helper function to save translations for provider profile fields
 */
const saveProviderProfileTranslations = async (provider: any, context: any) => {
  try {
    // Save translations for SProvider fields
    if (provider.title) {
      await translateAndStore(
        prisma,
        String(provider.id),
        'SProvider',
        'title',
        provider.title
      );
    }

    if (provider.presentation) {
      await translateAndStore(
        prisma,
        String(provider.id),
        'SProvider',
        'presentation',
        provider.presentation
      );
    }

    console.log(`[saveProviderProfileTranslations] Successfully saved translations for provider ID: ${provider.id}`);
  } catch (error) {
    console.error(`[saveProviderProfileTranslations] Error saving translations for provider ID: ${provider.id}`, error);
    // Don't throw error to avoid breaking the main operation
  }
};

/**
 * GET /api/providers/profile
 * Get authenticated provider's profile
 */
export const getProviderProfile = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Fetch provider profile using existing operation
    const provider = await getUserServiceProvider({ userId: context.user.id }, context);

    if (!provider) {
      return sendError(res, { statusCode: 404, message: 'Provider profile not found' });
    }

    // Get user's preferred language (fallback to EN)
    const userPreferredLanguage = context.user.preferedLanguage || LanguageCode.EN;

    // Apply translations to the provider profile
    const translated = await translateProviderProfileFields(provider, userPreferredLanguage, context);

    // Format response according to API standards
    const response: ProviderProfileResponse = {
      id: provider.id,
      userId: provider.userId,
      title: translated.title || undefined,
      phone: provider.phone || undefined,
      presentation: translated.presentation || undefined,
      isVerified: provider.isVerified,
      isSetupComplete: provider.isSetupComplete,
      category: provider.category ? {
        id: provider.category.id,
        title: provider.category.title
      } : undefined,
      averageRating: provider.averageRating || undefined,
      totalReviews: provider.totalReviews || 0
    };

    sendSuccess(res, response, 'Provider profile retrieved successfully');
  } catch (error: any) {
    console.error('[getProviderProfile] Error:', error);
    sendError(res, error, 'Failed to retrieve provider profile');
  }
});

/**
 * PUT /api/providers/profile
 * Update authenticated provider's profile
 */
export const updateProviderProfile = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Validate request data
    const updateData = validateAndExtract(updateProviderProfileSchema, req.body);
    console.log('provider updateData', updateData);
    // Update provider using existing operation
    const updatedProvider = await updateSProvider(updateData, context);

    // Save translations for the updated provider profile
    await saveProviderProfileTranslations(updatedProvider, context);

    console.log('provider updatedProvider', updatedProvider);

    // Fetch updated provider with category info
    const providerWithCategory = await getUserServiceProvider({ userId: context.user.id }, context);

    // Get user's preferred language (fallback to EN)
    const userPreferredLanguage = context.user.preferedLanguage || LanguageCode.EN;

    // Apply translations to the updated provider profile
    const translated = await translateProviderProfileFields(updatedProvider, userPreferredLanguage, context);

    // Format response according to API standards
    const response: ProviderProfileResponse = {
      id: updatedProvider.id,
      userId: updatedProvider.userId,
      title: translated.title || undefined,
      phone: updatedProvider.phone || undefined,
      presentation: translated.presentation || undefined,
      isVerified: updatedProvider.isVerified,
      isSetupComplete: updatedProvider.isSetupComplete,
      category: providerWithCategory?.category ? {
        id: providerWithCategory.category.id,
        title: providerWithCategory.category.title
      } : undefined,
      averageRating: updatedProvider.averageRating || undefined,
      totalReviews: updatedProvider.totalReviews || 0
    };

    sendSuccess(res, response, 'Provider profile updated successfully');
  } catch (error: any) {
    console.error('[updateProviderProfile] Error:', error);
    sendError(res, error, 'Failed to update provider profile');
  }
});
