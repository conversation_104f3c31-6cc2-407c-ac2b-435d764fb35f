/**
 * Provider Reschedule Management API Handlers
 * Handles appointment reschedule request management endpoints
 */

import type { Request, Response } from 'express';
import { 
  sendSuccess, 
  sendError, 
  sendCreated,
  asyncHandler,
  type ProviderApiContext 
} from '../index';
import { 
  getReschedulesSchema,
  createRescheduleSchema,
  respondToRescheduleSchema,
  validateAndExtract 
} from '../utils/validationUtils';
import { 
  getRescheduleRequests as getRescheduleRequestsOp,
  createRescheduleRequest as createRescheduleRequestOp,
  respondToRescheduleRequest as respondToRescheduleRequestOp
} from '../../operations';
import type { RescheduleRequestResponse, CreateRescheduleRequest, RespondToRescheduleRequest } from '../types';

/**
 * GET /api/providers/reschedules
 * Get all provider reschedule requests with filtering
 */
export const getProviderReschedules = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Validate query parameters
    const queryData = validateAndExtract(getReschedulesSchema, req.query);
    
    // Call the operation to get reschedule requests
    const rescheduleRequests = await getRescheduleRequestsOp(undefined, context);
    
    // Filter by status if specified
    let filteredRequests = rescheduleRequests;
    if (queryData.status !== 'all') {
      filteredRequests = rescheduleRequests.filter(request => request.status === queryData.status);
    }

    // Apply date filtering if provided
    if (queryData.from) {
      const fromDate = new Date(queryData.from);
      filteredRequests = filteredRequests.filter(request => 
        new Date(request.createdAt) >= fromDate
      );
    }

    if (queryData.to) {
      const toDate = new Date(queryData.to);
      filteredRequests = filteredRequests.filter(request => 
        new Date(request.createdAt) <= toDate
      );
    }

    // Apply pagination
    const page = queryData.page || 1;
    const limit = queryData.limit || 20;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedRequests = filteredRequests.slice(startIndex, endIndex);

    // Transform to response format
    const response: RescheduleRequestResponse[] = paginatedRequests.map(request => ({
      id: request.id,
      appointmentId: request.appointmentId,
      requestedById: request.requestedById,
      respondedById: request.respondedById,
      suggestedStartTime: request.suggestedStartTime,
      suggestedEndTime: request.suggestedEndTime,
      reason: request.reason,
      status: request.status,
      responseTime: request.responseTime,
      responseNote: request.responseNote,
      createdAt: request.createdAt,
      appointment: {
        id: (request as any).appointment.id,
        service: {
          id: (request as any).appointment.service.id,
          title: (request as any).appointment.service.title
        },
        customer: {
          id: (request as any).appointment.customerFolder.customer.id,
          firstName: (request as any).appointment.customerFolder.customer.firstName,
          lastName: (request as any).appointment.customerFolder.customer.lastName
        }
      }
    }));

    const responseData = {
      data: response,
      pagination: {
        total: filteredRequests.length,
        page,
        limit,
        totalPages: Math.ceil(filteredRequests.length / limit),
        hasNext: endIndex < filteredRequests.length,
        hasPrev: page > 1
      }
    };

    sendSuccess(res, responseData, 'Provider reschedule requests retrieved successfully');
  } catch (error: any) {
    console.error('[getProviderReschedules] Error:', error);
    sendError(res, error, 'Failed to retrieve provider reschedule requests');
  }
});

/**
 * GET /api/providers/reschedules/:id
 * Get a single provider reschedule request by ID
 */
export const getProviderReschedule = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Get reschedule ID from URL parameters
    const rescheduleId = parseInt(req.params.id);
    if (!rescheduleId || isNaN(rescheduleId)) {
      return sendError(res, { statusCode: 400, message: 'Invalid reschedule ID' });
    }

    // Get all reschedule requests and find the specific one
    const rescheduleRequests = await getRescheduleRequestsOp(undefined, context);
    const rescheduleRequest = rescheduleRequests.find(request => request.id === rescheduleId);

    if (!rescheduleRequest) {
      return sendError(res, { statusCode: 404, message: 'Reschedule request not found' });
    }

    // Transform to response format
    const response: RescheduleRequestResponse = {
      id: rescheduleRequest.id,
      appointmentId: rescheduleRequest.appointmentId,
      requestedById: rescheduleRequest.requestedById,
      respondedById: rescheduleRequest.respondedById,
      suggestedStartTime: rescheduleRequest.suggestedStartTime,
      suggestedEndTime: rescheduleRequest.suggestedEndTime,
      reason: rescheduleRequest.reason,
      status: rescheduleRequest.status,
      responseTime: rescheduleRequest.responseTime,
      responseNote: rescheduleRequest.responseNote,
      createdAt: rescheduleRequest.createdAt,
      appointment: {
        id: (rescheduleRequest as any).appointment.id,
        service: {
          id: (rescheduleRequest as any).appointment.service.id,
          title: (rescheduleRequest as any).appointment.service.title
        },
        customer: {
          id: (rescheduleRequest as any).appointment.customerFolder.customer.id,
          firstName: (rescheduleRequest as any).appointment.customerFolder.customer.firstName,
          lastName: (rescheduleRequest as any).appointment.customerFolder.customer.lastName
        }
      }
    };

    sendSuccess(res, response, 'Provider reschedule request retrieved successfully');
  } catch (error: any) {
    console.error('[getProviderReschedule] Error:', error);
    sendError(res, error, 'Failed to retrieve provider reschedule request');
  }
});

/**
 * POST /api/providers/appointments/:id/reschedule
 * Create a new reschedule request for an appointment
 */
export const createRescheduleRequest = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Get appointment ID from URL parameters
    const appointmentId = parseInt(req.params.id);
    if (!appointmentId || isNaN(appointmentId)) {
      return sendError(res, { statusCode: 400, message: 'Invalid appointment ID' });
    }

    // Validate request data
    const rescheduleData = validateAndExtract(createRescheduleSchema, req.body);
    
    // Calculate end time based on start time (assuming 1 hour duration for now)
    const startTime = new Date(rescheduleData.newStartTime);
    const endTime = new Date(startTime.getTime() + 60 * 60 * 1000); // Add 1 hour
    
    // Call the operation to create reschedule request
    const newRescheduleRequest = await createRescheduleRequestOp({
      appointmentId,
      suggestedStartTime: startTime,
      suggestedEndTime: endTime,
      reason: rescheduleData.reason
    }, context);
    
    // Transform to response format (simplified)
    const response = {
      id: newRescheduleRequest.id,
      appointmentId: newRescheduleRequest.appointmentId,
      suggestedStartTime: newRescheduleRequest.suggestedStartTime,
      suggestedEndTime: newRescheduleRequest.suggestedEndTime,
      reason: newRescheduleRequest.reason,
      status: newRescheduleRequest.status,
      createdAt: newRescheduleRequest.createdAt
    };

    sendCreated(res, response, 'Reschedule request created successfully');
  } catch (error: any) {
    console.error('[createRescheduleRequest] Error:', error);
    sendError(res, error, 'Failed to create reschedule request');
  }
});

/**
 * PATCH /api/providers/reschedules/:id/respond
 * Respond to a reschedule request (approve/reject)
 */
export const respondToReschedule = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Get reschedule ID from URL parameters
    const rescheduleId = parseInt(req.params.id);
    if (!rescheduleId || isNaN(rescheduleId)) {
      return sendError(res, { statusCode: 400, message: 'Invalid reschedule ID' });
    }

    // Validate request data
    const responseData = validateAndExtract(respondToRescheduleSchema, req.body);
    
    // Call the operation to respond to reschedule request
    const updatedRescheduleRequest = await respondToRescheduleRequestOp({
      requestId: rescheduleId,
      accept: responseData.response === 'approve',
      responseNote: responseData.notes
    }, context);
    
    // Transform to response format (simplified)
    const response = {
      id: updatedRescheduleRequest.id,
      status: updatedRescheduleRequest.status,
      responseTime: updatedRescheduleRequest.responseTime,
      responseNote: updatedRescheduleRequest.responseNote
    };

    sendSuccess(res, response, 'Reschedule request response recorded successfully');
  } catch (error: any) {
    console.error('[respondToReschedule] Error:', error);
    sendError(res, error, 'Failed to respond to reschedule request');
  }
});
