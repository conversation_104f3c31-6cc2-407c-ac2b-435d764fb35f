/**
 * Provider Queue Limit API Handlers
 * Handles queue limit and usage information endpoints
 */

import type { Request, Response } from 'express';
import {
  sendSuccess,
  sendError,
  asyncHandler,
  type ProviderApiContext
} from '../index';
import { checkQueueCreationLimit } from '../../operations';

/**
 * GET /api/providers/queues/limits
 * Get current queue usage and limits for the authenticated provider
 */
export const getQueueLimits = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Get queue limit information
    const queueLimitInfo = await checkQueueCreationLimit(context.user.id, context);

    const response = {
      currentCount: queueLimitInfo.currentCount,
      maxAllowed: queueLimitInfo.maxAllowed,
      canCreateMore: queueLimitInfo.canCreate,
      remaining: queueLimitInfo.maxAllowed - queueLimitInfo.currentCount,
      usagePercentage: Math.round((queueLimitInfo.currentCount / queueLimitInfo.maxAllowed) * 100)
    };

    sendSuccess(res, response, 'Queue limit information retrieved successfully');
  } catch (error: any) {
    console.error('[getQueueLimits] Error:', error);
    sendError(res, error, 'Failed to retrieve queue limit information');
  }
});

/**
 * GET /api/providers/queues/can-create
 * Check if the authenticated provider can create a new queue
 */
export const canCreateQueue = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Check if user can create more queues
    const queueLimitInfo = await checkQueueCreationLimit(context.user.id, context);

    const response = {
      canCreate: queueLimitInfo.canCreate,
      reason: queueLimitInfo.message || (queueLimitInfo.canCreate ? 'Queue creation allowed' : 'Unknown limitation'),
      currentCount: queueLimitInfo.currentCount,
      maxAllowed: queueLimitInfo.maxAllowed
    };

    sendSuccess(res, response, queueLimitInfo.canCreate ? 'Queue creation allowed' : 'Queue creation not allowed');
  } catch (error: any) {
    console.error('[canCreateQueue] Error:', error);
    sendError(res, error, 'Failed to check queue creation eligibility');
  }
}); 