/**
 * Provider Location API Handlers
 * Handles provider location management endpoints
 */

import type { Request, Response } from 'express';
import {
  sendSuccess,
  sendError,
  sendCreated,
  asyncHandler,
  validateAndExtract,
  createLocationSchema,
  updateLocationSchema,
  type ProviderApiContext,
  type LocationResponse,
  type CreateLocationRequest,
  type UpdateLocationRequest
} from '../index';
import type { SProvidingPlace } from 'wasp/entities';
import { addSProvidingPlace, getSProvidingPlaces, updateSProvidingPlace, deleteSProvidingPlace } from '../../operations';
import { getTranslatedString, translateAndStore } from '../../../server/translations';
import { LanguageCode } from '@prisma/client';
import { prisma } from 'wasp/server';

/**
 * Helper function to translate location fields
 */
const translateLocationFields = async (
  location: any,
  targetLanguage: LanguageCode,
  context: any
) => {
  const promises: Promise<string | null>[] = [];

  // Translate SProvidingPlace fields
  promises.push(
    getTranslatedString(prisma, 'SProvidingPlace', String(location.id), 'name', targetLanguage),
    getTranslatedString(prisma, 'SProvidingPlace', String(location.id), 'shortName', targetLanguage)
  );

  // Translate Address fields if detailedAddress exists
  if (location.detailedAddress?.id) {
    promises.push(
      getTranslatedString(prisma, 'Address', String(location.detailedAddress.id), 'address', targetLanguage),
      getTranslatedString(prisma, 'Address', String(location.detailedAddress.id), 'city', targetLanguage),
      getTranslatedString(prisma, 'Address', String(location.detailedAddress.id), 'country', targetLanguage)
    );
  } else {
    promises.push(Promise.resolve(null), Promise.resolve(null), Promise.resolve(null));
  }

  const [translatedName, translatedShortName, translatedAddress, translatedCity, translatedCountry] =
    await Promise.all(promises);

  return {
    name: translatedName || location.name,
    shortName: translatedShortName || location.shortName,
    address: translatedAddress,
    city: translatedCity,
    country: translatedCountry
  };
};

/**
 * Helper function to save translations for location fields
 */
const saveLocationTranslations = async (location: any, context: any) => {
  try {
    // Save translations for SProvidingPlace fields
    if (location.name) {
      await translateAndStore(
        prisma,
        String(location.id),
        'SProvidingPlace',
        'name',
        location.name
      );
    }

    if (location.shortName) {
      await translateAndStore(
        prisma,
        String(location.id),
        'SProvidingPlace',
        'shortName',
        location.shortName
      );
    }

    // Save translations for Address fields if detailedAddress exists
    if (location.detailedAddress?.id) {
      if (location.detailedAddress.address) {
        await translateAndStore(
          prisma,
          String(location.detailedAddress.id),
          'Address',
          'address',
          location.detailedAddress.address
        );
      }

      if (location.detailedAddress.city) {
        await translateAndStore(
          prisma,
          String(location.detailedAddress.id),
          'Address',
          'city',
          location.detailedAddress.city
        );
      }

      if (location.detailedAddress.country) {
        await translateAndStore(
          prisma,
          String(location.detailedAddress.id),
          'Address',
          'country',
          location.detailedAddress.country
        );
      }
    }

    console.log(`[saveLocationTranslations] Successfully saved translations for location ID: ${location.id}`);
  } catch (error) {
    console.error(`[saveLocationTranslations] Error saving translations for location ID: ${location.id}`, error);
    // Don't throw error to avoid breaking the main operation
  }
};

/**
 * GET /api/providers/locations
 * Get all provider locations
 */
export const getProviderLocations = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Get user's preferred language (fallback to EN)
    const userPreferredLanguage = context.user.preferedLanguage || LanguageCode.EN;

    // Get provider locations using existing operation
    const locations = await getSProvidingPlaces({}, context) as (SProvidingPlace & { queues: any[] })[];

    // Apply translations in parallel for all locations
    const translationPromises = locations.map(location =>
      translateLocationFields(location, userPreferredLanguage, context)
    );
    const translatedFields = await Promise.all(translationPromises);

    // Format response with translated values
    const response: LocationResponse[] = locations.map((location, index) => {
      const translated = translatedFields[index];

      return {
        id: location.id,
        name: translated.name,
        shortName: translated.shortName || undefined,
        address: translated.address || (location as any).detailedAddress?.address || location.address || undefined,
        city: translated.city || (location as any).detailedAddress?.city || location.city || undefined,
        mobile: location.mobile || undefined,
        isMobileHidden: location.isMobileHidden || false,
        fax: location.fax || undefined,
        floor: location.floor || undefined,
        parking: location.parking || false,
        elevator: location.elevator || false,
        handicapAccess: location.handicapAccess || false,
        timezone: location.timezone || undefined,
        queues: location.queues || [],
        // Geographic and postal information
        country: translated.country || (location as any).detailedAddress?.country || undefined,
        postalCode: (location as any).detailedAddress?.postalCode || undefined,
        latitude: (location as any).detailedAddress?.latitude || undefined,
        longitude: (location as any).detailedAddress?.longitude || undefined,
        // Opening hours (unchanged)
        openingHours: (location as any).openings?.map((opening: any) => ({
          dayOfWeek: opening.dayOfWeek,
          isActive: opening.isActive,
          hours: opening.hours.map((hour: any) => ({
            timeFrom: hour.timeFrom,
            timeTo: hour.timeTo
          }))
        })) || []
      };
    });

    sendSuccess(res, response, 'Provider locations retrieved successfully');
  } catch (error: any) {
    console.error('[getProviderLocations] Error:', error);
    sendError(res, error, 'Failed to retrieve provider locations');
  }
});

/**
 * GET /api/providers/locations/:id
 * Get a specific provider location by ID
 */
export const getProviderLocation = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Get location ID from URL parameters
    const locationId = parseInt(req.params.id);
    if (!locationId || isNaN(locationId)) {
      return sendError(res, { statusCode: 400, message: 'Invalid location ID' });
    }

    // Get provider locations using existing operation
    const locations = await getSProvidingPlaces({}, context) as (SProvidingPlace & { queues: any[] })[];
    const location = locations.find(loc => loc.id === locationId);

    if (!location) {
      return sendError(res, { statusCode: 404, message: 'Location not found' });
    }

    // Get user's preferred language (fallback to EN)
    const userPreferredLanguage = context.user.preferedLanguage || LanguageCode.EN;

    // Apply translations to the location
    const translated = await translateLocationFields(location, userPreferredLanguage, context);

    // Format response according to API standards
    const response: LocationResponse = {
      id: location.id,
      name: translated.name,
      shortName: translated.shortName || undefined,
      address: translated.address || (location as any).detailedAddress?.address || location.address || undefined,
      city: translated.city || (location as any).detailedAddress?.city || location.city || undefined,
      mobile: location.mobile || undefined,
      isMobileHidden: location.isMobileHidden || false,
      fax: location.fax || undefined,
      floor: location.floor || undefined,
      parking: location.parking || false,
      elevator: location.elevator || false,
      handicapAccess: location.handicapAccess || false,
      timezone: location.timezone || undefined,
      queues: location.queues|| [],
      // Geographic and postal information
      country: translated.country || (location as any).detailedAddress?.country || undefined,
      postalCode: (location as any).detailedAddress?.postalCode || undefined,
      latitude: (location as any).detailedAddress?.latitude || undefined,
      longitude: (location as any).detailedAddress?.longitude || undefined,
      // Opening hours
      openingHours: (location as any).openings?.map((opening: any) => ({
        dayOfWeek: opening.dayOfWeek,
        isActive: opening.isActive,
        hours: opening.hours.map((hour: any) => ({
          timeFrom: hour.timeFrom,
          timeTo: hour.timeTo
        }))
      })) || []
    };

    sendSuccess(res, response, 'Provider location retrieved successfully');
  } catch (error: any) {
    console.error('[getProviderLocation] Error:', error);
    sendError(res, error, 'Failed to retrieve provider location');
  }
});

/**
 * POST /api/providers/locations
 * Create a new provider location
 */
export const createProviderLocation = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Validate request data
    const locationData = validateAndExtract(createLocationSchema, req.body);

    // Create location using existing operation
    const newLocation = await addSProvidingPlace(locationData, context);

    // Save translations for the newly created location
    await saveLocationTranslations(newLocation, context);

    // Fetch the created location with opening hours to include in response
    const locationWithOpenings = await getSProvidingPlaces({}, context) as (SProvidingPlace & { queues: any[] })[];
    const createdLocationWithOpenings = locationWithOpenings.find(loc => loc.id === newLocation.id);

    // Get user's preferred language (fallback to EN)
    const userPreferredLanguage = context.user.preferedLanguage || LanguageCode.EN;

    // Apply translations to the created location
    const translated = await translateLocationFields(newLocation, userPreferredLanguage, context);

    // Format response according to API standards
    const response: LocationResponse = {
      id: newLocation.id,
      name: translated.name,
      shortName: translated.shortName || undefined,
      address: translated.address || (newLocation as any).detailedAddress?.address || newLocation.address || undefined,
      city: translated.city || (newLocation as any).detailedAddress?.city || newLocation.city || undefined,
      mobile: newLocation.mobile || undefined,
      isMobileHidden: newLocation.isMobileHidden || false,
      fax: newLocation.fax || undefined,
      floor: newLocation.floor || undefined,
      parking: newLocation.parking || false,
      elevator: newLocation.elevator || false,
      handicapAccess: newLocation.handicapAccess || false,
      timezone: newLocation.timezone || undefined,
      // Geographic and postal information
      country: translated.country || (newLocation as any).detailedAddress?.country || undefined,
      postalCode: (newLocation as any).detailedAddress?.postalCode || undefined,
      latitude: (newLocation as any).detailedAddress?.latitude || undefined,
      longitude: (newLocation as any).detailedAddress?.longitude || undefined,
      // Opening hours
      openingHours: createdLocationWithOpenings ? (createdLocationWithOpenings as any).openings?.map((opening: any) => ({
        dayOfWeek: opening.dayOfWeek,
        isActive: opening.isActive,
        hours: opening.hours.map((hour: any) => ({
          timeFrom: hour.timeFrom,
          timeTo: hour.timeTo
        }))
      })) || [] : [],
      // Queues
      queues: createdLocationWithOpenings?.queues?.map((queue: any) => ({
        id: queue.id,
        name: queue.name,
        shortName: queue.shortName || undefined,
        address: (queue as any).detailedAddress?.address || queue.address || undefined,
        city: (queue as any).detailedAddress?.city || queue.city || undefined,
      })) || []
    };

    sendCreated(res, response, 'Provider location created successfully');
  } catch (error: any) {
    console.error('[createProviderLocation] Error:', error);
    sendError(res, error, 'Failed to create provider location');
  }
});

/**
 * PUT /api/providers/locations/:id
 * Update a provider location
 */
export const updateProviderLocation = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Get location ID from URL parameters
    const locationId = parseInt(req.params.id);
    if (!locationId || isNaN(locationId)) {
      return sendError(res, { statusCode: 400, message: 'Invalid location ID' });
    }

    // Validate request data
    const updateData = validateAndExtract(updateLocationSchema, req.body);

    // Get current location to merge with update data
    const currentLocations = await getSProvidingPlaces({}, context) as (SProvidingPlace & { queues: any[] })[];
    const currentLocation = currentLocations.find(loc => loc.id === locationId);

    if (!currentLocation) {
      return sendError(res, { statusCode: 404, message: 'Location not found' });
    }

    // Merge current data with update data, ensuring required fields are present
    const mergedData = {
      placeId: locationId,
      name: updateData.name || currentLocation.name,
      shortName: updateData.shortName !== undefined ? updateData.shortName : (currentLocation.shortName || undefined),
      address: updateData.address !== undefined ? updateData.address : (currentLocation.address || undefined),
      city: updateData.city !== undefined ? updateData.city : (currentLocation.city || undefined),
      mobile: updateData.mobile !== undefined ? updateData.mobile : (currentLocation.mobile || undefined),
      fax: updateData.fax !== undefined ? updateData.fax : (currentLocation.fax || undefined),
      floor: updateData.floor !== undefined ? updateData.floor : (currentLocation.floor || undefined),
      parking: updateData.parking !== undefined ? updateData.parking : (currentLocation.parking || false),
      elevator: updateData.elevator !== undefined ? updateData.elevator : (currentLocation.elevator || false),
      handicapAccess: updateData.handicapAccess !== undefined ? updateData.handicapAccess : (currentLocation.handicapAccess || false),
      timezone: updateData.timezone !== undefined ? updateData.timezone : (currentLocation.timezone || undefined),
      latitude: updateData.latitude !== undefined ? updateData.latitude : undefined,
      longitude: updateData.longitude !== undefined ? updateData.longitude : undefined,
      postalCode: updateData.postalCode !== undefined ? updateData.postalCode : undefined,
      country: 'Algeria', // Default country as per operation
      // Add opening hours support
      openingHours: updateData.openingHours !== undefined ? updateData.openingHours : undefined
    };

    // Call the operation to update the location
    const updatedLocation = await updateSProvidingPlace(mergedData, context);

    // Save translations for the updated location
    await saveLocationTranslations(updatedLocation, context);

    // Fetch the updated location with opening hours to include in response
    const locationWithOpenings = await getSProvidingPlaces({}, context) as (SProvidingPlace & { queues: any[] })[];
    const updatedLocationWithOpenings = locationWithOpenings.find(loc => loc.id === locationId);

    // Get user's preferred language (fallback to EN)
    const userPreferredLanguage = context.user.preferedLanguage || LanguageCode.EN;

    // Apply translations to the updated location
    const translated = await translateLocationFields(updatedLocation, userPreferredLanguage, context);

    // Format response according to API standards
    const response: LocationResponse = {
      id: updatedLocation.id,
      name: translated.name,
      shortName: translated.shortName || undefined,
      address: translated.address || (updatedLocation as any).detailedAddress?.address || updatedLocation.address || undefined,
      city: translated.city || (updatedLocation as any).detailedAddress?.city || updatedLocation.city || undefined,
      mobile: updatedLocation.mobile || undefined,
      isMobileHidden: updatedLocation.isMobileHidden || false,
      fax: updatedLocation.fax || undefined,
      floor: updatedLocation.floor || undefined,
      parking: updatedLocation.parking || false,
      elevator: updatedLocation.elevator || false,
      handicapAccess: updatedLocation.handicapAccess || false,
      timezone: updatedLocation.timezone || undefined,
      // Geographic and postal information
      country: translated.country || (updatedLocation as any).detailedAddress?.country || undefined,
      postalCode: (updatedLocation as any).detailedAddress?.postalCode || undefined,
      latitude: (updatedLocation as any).detailedAddress?.latitude || undefined,
      longitude: (updatedLocation as any).detailedAddress?.longitude || undefined,
      // Opening hours
      openingHours: updatedLocationWithOpenings ? (updatedLocationWithOpenings as any).openings?.map((opening: any) => ({
        dayOfWeek: opening.dayOfWeek,
        isActive: opening.isActive,
        hours: opening.hours.map((hour: any) => ({
          timeFrom: hour.timeFrom,
          timeTo: hour.timeTo
        }))
      })) || [] : [],
      // Queues
      queues: updatedLocationWithOpenings?.queues?.map((queue: any) => ({
        id: queue.id,
        name: queue.name,
        shortName: queue.shortName || undefined,
        address: (queue as any).detailedAddress?.address || queue.address || undefined,
        city: (queue as any).detailedAddress?.city || queue.city || undefined,
      })) || []
    };

    sendSuccess(res, response, 'Provider location updated successfully');
  } catch (error: any) {
    console.error('[updateProviderLocation] Error:', error);
    sendError(res, error, 'Failed to update provider location');
  }
});

/**
 * DELETE /api/providers/locations/:id
 * Delete a provider location
 */
export const deleteProviderLocation = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Get location ID from URL parameters
    const locationId = parseInt(req.params.id);
    if (!locationId || isNaN(locationId)) {
      return sendError(res, { statusCode: 400, message: 'Invalid location ID' });
    }

    // Call the operation to delete the location
    const result = await deleteSProvidingPlace({
      placeId: locationId
    }, context);

    sendSuccess(res, { id: locationId }, result.message);
  } catch (error: any) {
    console.error('[deleteProviderLocation] Error:', error);
    sendError(res, error, 'Failed to delete provider location');
  }
});
