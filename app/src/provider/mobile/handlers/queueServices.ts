/**
 * Provider Queue Service Assignment API Handlers
 * Handles queue-service assignment management endpoints
 */

import type { Request, Response } from 'express';
import { 
  sendSuccess, 
  sendError, 
  sendCreated,
  asyncHandler,
  type ProviderApiContext 
} from '../index';
import { 
  assignServiceToQueueSchema, 
  removeServiceFromQueueSchema,
  validateAndExtract 
} from '../utils/validationUtils';
import { 
  getQueueServices as getQueueServicesOp,
  assignServiceToQueue as assignServiceToQueueOp,
  removeServiceFromQueue as removeServiceFromQueueOp
} from '../../operations';
import type { QueueServiceResponse, AssignServiceToQueueRequest, RemoveServiceFromQueueRequest } from '../types';

/**
 * GET /api/providers/queues/:queueId/services
 * Get all services assigned to a queue
 */
export const getQueueServices = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Get queue ID from URL parameters
    const queueId = parseInt(req.params.queueId);
    if (!queueId || isNaN(queueId)) {
      return sendError(res, { statusCode: 400, message: 'Invalid queue ID' });
    }

    // Call the operation to get queue services
    const queueServices = await getQueueServicesOp({ queueId }, context);
    
    // Transform to response format
    const response: QueueServiceResponse[] = queueServices.map(qs => ({
      queueId: qs.queueId,
      serviceId: qs.serviceId,
      service: {
        id: qs.service.id,
        title: qs.service.title,
        duration: qs.service.duration,
        color: qs.service.color
      }
    }));

    sendSuccess(res, response, 'Queue services retrieved successfully');
  } catch (error: any) {
    console.error('[getQueueServices] Error:', error);
    sendError(res, error, 'Failed to retrieve queue services');
  }
});

/**
 * POST /api/providers/queues/:queueId/services
 * Assign a service to a queue
 */
export const assignServiceToQueue = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Get queue ID from URL parameters
    const queueId = parseInt(req.params.queueId);
    if (!queueId || isNaN(queueId)) {
      return sendError(res, { statusCode: 400, message: 'Invalid queue ID' });
    }

    // Validate request data
    const assignmentData = validateAndExtract(assignServiceToQueueSchema, req.body);
    
    // Call the operation to assign service to queue
    const assignment = await assignServiceToQueueOp({
      queueId,
      serviceId: assignmentData.serviceId
    }, context);
    
    // Transform to response format
    const response: QueueServiceResponse = {
      queueId: assignment.queueId,
      serviceId: assignment.serviceId,
      service: {
        id: assignment.service.id,
        title: assignment.service.title,
        duration: assignment.service.duration,
        color: assignment.service.color
      }
    };

    sendCreated(res, response, 'Service assigned to queue successfully');
  } catch (error: any) {
    console.error('[assignServiceToQueue] Error:', error);
    sendError(res, error, 'Failed to assign service to queue');
  }
});

/**
 * DELETE /api/providers/queues/:queueId/services/:serviceId
 * Remove a service from a queue
 */
export const removeServiceFromQueue = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Get queue ID and service ID from URL parameters
    const queueId = parseInt(req.params.queueId);
    const serviceId = parseInt(req.params.serviceId);
    
    if (!queueId || isNaN(queueId)) {
      return sendError(res, { statusCode: 400, message: 'Invalid queue ID' });
    }
    
    if (!serviceId || isNaN(serviceId)) {
      return sendError(res, { statusCode: 400, message: 'Invalid service ID' });
    }

    // Call the operation to remove service from queue
    const result = await removeServiceFromQueueOp({
      queueId,
      serviceId
    }, context);

    sendSuccess(res, { queueId, serviceId }, result.message);
  } catch (error: any) {
    console.error('[removeServiceFromQueue] Error:', error);
    sendError(res, error, 'Failed to remove service from queue');
  }
});
