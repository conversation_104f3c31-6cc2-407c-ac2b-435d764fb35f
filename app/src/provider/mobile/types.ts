/**
 * TypeScript interfaces and types for Provider Mobile API
 * Following Wasp framework patterns and conventions
 */

import type { Request, Response } from 'express';
import type { User, SProvider, SProvidingPlace, Service, Queue, Appointment, CustomerFolder } from 'wasp/entities';

// Base API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: any[];
}

export interface ApiError {
  message: string;
  code?: string;
  statusCode: number;
  details?: any;
}

// Provider Context Type for API Handlers (using any for now to avoid type conflicts)
export interface ProviderApiContext {
  user: any;
  entities: any;
}

// API Handler Type
export type ApiHandler = (req: Request, res: Response, context: ProviderApiContext) => Promise<void>;

// Provider Profile Types
export interface ProviderProfileResponse {
  id: number;
  userId: string;
  title?: string;
  phone?: string;
  presentation?: string;
  isVerified: boolean;
  isSetupComplete: boolean;
  category?: {
    id: number;
    title: string;
  };
  averageRating?: number;
  totalReviews: number;
}

export interface UpdateProviderProfileRequest {
  title?: string;
  phone?: string;
  presentation?: string;
  providerCategoryId?: number;
}

// Location Types
export interface LocationResponse {
  id: number;
  name: string;
  shortName?: string;
  address?: string;
  city?: string;
  mobile?: string;
  isMobileHidden: boolean;
  fax?: string;
  floor?: string;
  parking: boolean;
  elevator: boolean;
  handicapAccess: boolean;
  timezone?: string;
  queues: any[];
  // Geographic and postal information
  country?: string;
  postalCode?: string;
  latitude?: number;
  longitude?: number;
  // Opening hours
  openingHours?: OpeningHoursDay[];
}

export interface OpeningHoursInterval {
  timeFrom: string;
  timeTo: string;
}

export interface OpeningHoursDay {
  dayOfWeek: string;
  isActive: boolean;
  hours: OpeningHoursInterval[];
}

export interface CreateLocationRequest {
  name: string;
  shortName?: string;
  address?: string;
  city?: string;
  mobile?: string;
  isMobileHidden?: boolean;
  fax?: string;
  floor?: string;
  parking?: boolean;
  elevator?: boolean;
  handicapAccess?: boolean;
  timezone?: string;
  // Address details
  country?: string;
  postalCode?: string;
  latitude?: number;
  longitude?: number;
  // Opening hours
  openingHours?: OpeningHoursDay[];
}

export interface UpdateLocationRequest extends Partial<CreateLocationRequest> {}

// Service Category Types
export interface ServiceCategoryResponse {
  id: number;
  title: string;
  sProviderId: number;
}

export interface CreateServiceCategoryRequest {
  title: string;
}

export interface UpdateServiceCategoryRequest {
  title?: string;
}

// Service Types
export interface ServiceResponse {
  id: number;
  title: string;
  duration: number;
  price: number;
  pointsRequirements: number;
  isPublic: boolean;
  deliveryType: string;
  servedRegions: string[];
  description?: string;
  color: string;
  acceptOnline: boolean;
  acceptNew: boolean;
  notificationOn: boolean;
}

export interface CreateServiceRequest {
  title: string;
  duration: number;
  price: number;
  pointsRequirements?: number;
  isPublic?: boolean;
  deliveryType: string;
  servedRegions?: string[] | null;
  description?: string;
  color: string;
  acceptOnline?: boolean;
  acceptNew?: boolean;
  notificationOn?: boolean;
}

export interface UpdateServiceRequest extends Partial<CreateServiceRequest> {}

// Queue Types
export interface QueueResponse {
  id: number;
  title: string;
  isActive: boolean;
  sProvidingPlaceId: number;
  services: Array<{
    id: number;
    title: string;
  }>;
  openingHours?: OpeningHoursDay[];
}

export interface CreateQueueRequest {
  title: string;
  sProvidingPlaceId: number;
  isActive?: boolean;
  serviceIds: number[];
  openingHours?: OpeningHoursDay[];
}

export interface UpdateQueueRequest {
  title?: string;
  isActive?: boolean;
  serviceIds?: number[];
  openingHours?: OpeningHoursDay[];
}

// Queue Service Assignment Types
export interface QueueServiceResponse {
  queueId: number;
  serviceId: number;
  service: {
    id: number;
    title: string;
    duration: number;
    color?: string;
  };
}

export interface AssignServiceToQueueRequest {
  serviceId: number;
}

export interface RemoveServiceFromQueueRequest {
  serviceId: number;
}

// Reschedule Management Types
export interface RescheduleRequestResponse {
  id: number;
  appointmentId: number;
  requestedById: string;
  respondedById: string;
  suggestedStartTime: Date;
  suggestedEndTime: Date;
  reason?: string;
  status: 'pending' | 'accepted' | 'rejected';
  responseTime?: Date;
  responseNote?: string;
  createdAt: Date;
  appointment: {
    id: number;
    service: {
      id: number;
      title: string;
    };
    customer: {
      id: string;
      firstName?: string;
      lastName?: string;
    };
  };
}

export interface CreateRescheduleRequest {
  newStartTime: string; // ISO date string
  reason?: string;
  notifyCustomer?: boolean;
}

export interface RespondToRescheduleRequest {
  response: 'approve' | 'reject';
  notes?: string;
  notifyCustomer?: boolean;
}

// Schedule Management Types
export interface TimeSlot {
  startTime: string; // HH:MM format
  endTime: string;   // HH:MM format
}

export interface ScheduleResponse {
  id: number;
  dayOfWeek: number; // 0-6 (Sunday-Saturday)
  dayName: string;   // Full day name
  isActive: boolean;
  timeSlots: TimeSlot[];
  locationId: number;
  type: string;      // 'regular', 'holiday', etc.
  effectiveFrom: Date;
  effectiveTo: Date | null;
}

export interface CreateScheduleRequest {
  dayOfWeek: number; // 0-6 (Sunday-Saturday)
  timeSlots?: TimeSlot[];
  locationId: number;
  isActive?: boolean;
  type?: string;
  effectiveFrom?: string; // ISO date string
  effectiveTo?: string;   // ISO date string
}

export interface UpdateScheduleRequest {
  dayOfWeek?: number; // 0-6 (Sunday-Saturday)
  timeSlots?: TimeSlot[];
  isActive?: boolean;
  type?: string;
  effectiveFrom?: string; // ISO date string
  effectiveTo?: string;   // ISO date string
}

// Customer Types
export interface CustomerResponse {
  id: string;
  firstName: string;
  lastName: string;
  mobileNumber: string;
  email?: string;
  nationalId?: string;
  notes?: string;
  appointmentCount: number;
  createdAt: Date;
}

export interface CreateCustomerRequest {
  firstName: string;
  lastName: string;
  mobileNumber: string;
  email?: string;
  nationalId?: string;
  notes?: string;
}

export interface UpdateCustomerRequest {
  firstName?: string;
  lastName?: string;
  mobileNumber?: string;
  email?: string;
  nationalId?: string;
  notes?: string;
}

// File/Image Types
export interface FileResponse {
  id: string;
  name: string;
  type: string;
  url: string;
}

// Provider Types
export interface ProviderResponse {
  id: number;
  title?: string;
  phone?: string;
  presentation?: string;
  isVerified: boolean;
  category?: {
    id: number;
    title: string;
    description?: string;
  } | null;
  user: {
    id: string;
    firstName?: string;
    lastName?: string;
    email?: string;
    profilePicture?: FileResponse | null;
  };
  logo?: FileResponse | null;
}

// Appointment Types
export interface AppointmentResponse {
  id: number;
  status: string;
  expectedAppointmentStartTime?: Date;
  expectedAppointmentEndTime?: Date;
  realAppointmentStartTime?: Date;
  realAppointmentEndTime?: Date;
  notes?: string;
  service: ServiceResponse;
  place: LocationResponse;
  queue?: QueueResponse;
  customer: {
    id: string;
    firstName?: string;
    lastName?: string;
    profilePicture?: FileResponse | null;
  };
  provider?: ProviderResponse | null;
}

export interface CreateAppointmentRequest {
  customerId: string;
  serviceId: number;
  queueId: number;
  expectedStartTime: string; // ISO date string
  notes?: string;
}

export interface UpdateAppointmentRequest {
  serviceId?: number;
  placeId?: number;
  queueId?: number;
  startTime?: Date;
  endTime?: Date;
  notes?: string;
  status?: string;
}

// Pagination Types
export interface PaginationParams {
  page?: number;
  limit?: number;
  skip?: number;
  take?: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Filter Types
export interface ServiceFilters extends PaginationParams {
  isActive?: boolean;
  categoryId?: number;
  search?: string;
  minDuration?: number;
  maxDuration?: number;
}

export interface AppointmentFilters extends PaginationParams {
  status?: string;
  startDate?: string;
  endDate?: string;
  serviceId?: number;
  queueId?: number;
}

export interface LocationFilters extends PaginationParams {
  search?: string;
  city?: string;
}
