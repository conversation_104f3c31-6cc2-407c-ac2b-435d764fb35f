/**
 * Authentication middleware for Provider Mobile API
 * Following Wasp framework patterns
 */

import type { Request, Response } from 'express';
import { HttpError } from 'wasp/server';
import { sendUnauthorized, sendForbidden } from '../utils/responseUtils';
import type { ProviderApiContext } from '../types';

/**
 * Verify that the user is authenticated and has provider role
 */
export function requireProviderAuth(req: Request, res: Response, next: Function, context: any) {
  // Check if user is authenticated (handled by <PERSON><PERSON>'s auth: true)
  if (!context.user) {
    return sendUnauthorized(res, 'Authentication required');
  }

  // Check if user has the correct role (CLIENT for providers)
  if (context.user.role !== 'CLIENT') {
    return sendForbidden(res, 'Provider access required');
  }

  next();
}

/**
 * Get provider ID from authenticated user
 */
export async function getProviderFromUser(userId: string, entities: any): Promise<any> {
  const provider = await entities.SProvider.findUnique({
    where: { userId },
    select: { 
      id: true, 
      userId: true, 
      title: true, 
      isVerified: true, 
      isSetupComplete: true 
    }
  });

  if (!provider) {
    throw new HttpError(404, 'Provider profile not found for this user');
  }

  return provider;
}

/**
 * Middleware to attach provider information to request
 */
export async function attachProviderInfo(req: Request, res: Response, next: Function, context: any) {
  try {
    if (!context.user) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const provider = await getProviderFromUser(context.user.id, context.entities);
    
    // Attach provider info to context for use in handlers
    context.provider = provider;
    
    next();
  } catch (error: any) {
    if (error instanceof HttpError) {
      if (error.statusCode === 404) {
        return sendForbidden(res, 'Provider profile not found');
      }
      return res.status(error.statusCode).json({ 
        success: false, 
        message: error.message 
      });
    }
    
    console.error('Error in attachProviderInfo middleware:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Internal server error' 
    });
  }
}

/**
 * Verify provider owns a resource by checking provider ID
 */
export async function verifyProviderOwnership(
  providerId: number,
  resourceProviderId: number,
  resourceType: string = 'resource'
): Promise<void> {
  if (providerId !== resourceProviderId) {
    throw new HttpError(403, `You do not have permission to access this ${resourceType}`);
  }
}

/**
 * Verify provider owns a location
 */
export async function verifyLocationOwnership(
  providerId: number,
  locationId: number,
  entities: any
): Promise<any> {
  const location = await entities.SProvidingPlace.findFirst({
    where: {
      id: locationId,
      sProviderId: providerId
    },
    select: { id: true, sProviderId: true, name: true }
  });

  if (!location) {
    throw new HttpError(404, 'Location not found or you do not have permission to access it');
  }

  return location;
}

/**
 * Verify provider owns a service
 */
export async function verifyServiceOwnership(
  providerId: number,
  serviceId: number,
  entities: any
): Promise<any> {
  const service = await entities.Service.findFirst({
    where: {
      id: serviceId,
      sProviderId: providerId
    },
    select: { id: true, sProviderId: true, title: true }
  });

  if (!service) {
    throw new HttpError(404, 'Service not found or you do not have permission to access it');
  }

  return service;
}

/**
 * Verify provider owns a queue
 */
export async function verifyQueueOwnership(
  providerId: number,
  queueId: number,
  entities: any
): Promise<any> {
  const queue = await entities.Queue.findFirst({
    where: {
      id: queueId,
      sProvidingPlace: {
        sProviderId: providerId
      }
    },
    include: {
      sProvidingPlace: {
        select: { id: true, sProviderId: true }
      }
    }
  });

  if (!queue) {
    throw new HttpError(404, 'Queue not found or you do not have permission to access it');
  }

  return queue;
}

/**
 * Verify provider owns an appointment
 */
export async function verifyAppointmentOwnership(
  providerId: number,
  appointmentId: number,
  entities: any
): Promise<any> {
  const appointment = await entities.Appointment.findFirst({
    where: {
      id: appointmentId,
      customerFolder: {
        sProviderId: providerId
      }
    },
    include: {
      customerFolder: {
        select: { id: true, sProviderId: true }
      },
      service: {
        select: { id: true, title: true }
      },
      place: {
        select: { id: true, name: true }
      }
    }
  });

  if (!appointment) {
    throw new HttpError(404, 'Appointment not found or you do not have permission to access it');
  }

  return appointment;
}

/**
 * Check if provider setup is complete
 */
export function requireCompleteSetup(req: Request, res: Response, next: Function, context: any) {
  if (!context.provider) {
    return sendForbidden(res, 'Provider information not available');
  }

  if (!context.provider.isSetupComplete) {
    return sendForbidden(res, 'Provider setup must be completed before accessing this resource');
  }

  next();
}

/**
 * Middleware factory for resource ownership verification
 */
export function requireResourceOwnership(
  resourceType: 'location' | 'service' | 'queue' | 'appointment',
  idParam: string = 'id'
) {
  return async (req: Request, res: Response, next: Function, context: any) => {
    try {
      if (!context.provider) {
        return sendForbidden(res, 'Provider information not available');
      }

      const resourceId = parseInt(req.params[idParam]);
      if (isNaN(resourceId)) {
        return res.status(400).json({
          success: false,
          message: `Invalid ${resourceType} ID`
        });
      }

      let resource;
      const providerId = context.provider.id;

      switch (resourceType) {
        case 'location':
          resource = await verifyLocationOwnership(providerId, resourceId, context.entities);
          break;
        case 'service':
          resource = await verifyServiceOwnership(providerId, resourceId, context.entities);
          break;
        case 'queue':
          resource = await verifyQueueOwnership(providerId, resourceId, context.entities);
          break;
        case 'appointment':
          resource = await verifyAppointmentOwnership(providerId, resourceId, context.entities);
          break;
        default:
          throw new HttpError(400, 'Invalid resource type');
      }

      // Attach resource to context for use in handlers
      context[resourceType] = resource;
      
      next();
    } catch (error: any) {
      if (error instanceof HttpError) {
        return res.status(error.statusCode).json({
          success: false,
          message: error.message
        });
      }
      
      console.error(`Error in ${resourceType} ownership verification:`, error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  };
}
