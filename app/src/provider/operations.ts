import { HttpError, prisma } from 'wasp/server';
import { SProvider, type ProviderCategory, SProvidingPlace, ServiceCategory, Opening, OpeningHours, Service, User, CustomerFolder, Appointment, AppointmentHistory, Queue, QueueOpening, QueueOpeningHours, RescheduleRequest } from 'wasp/entities'; // Added RescheduleRequest
import type { GetProviderCategories,ExtendAppointment, GetUserServiceProvider, AddSProvidingPlace, GetSProvidingPlaces, UpdateSProvidingPlace , CreateProviderCustomer, CreateAppointment, GetAppointments, UpdateAppointment, GetProviderAvailability } from 'wasp/server/operations'; // Keep existing types, added GetProviderAvailability
import { z } from 'zod';
import { ensureArgsSchemaOrThrowHttpError } from '../server/validation'; // Assuming validation helper exists
import { LanguageCode, Prisma } from '@prisma/client'; // Import Prisma
import type { CompleteAppointment, NoShowAppointment } from 'wasp/server/operations'; // Import the generated type
import dayjs from 'dayjs'; // Import dayjs
import utc from 'dayjs/plugin/utc.js';
import timezone from 'dayjs/plugin/timezone.js';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter.js'; // Import the missing plugin
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore.js'; // Import the missing plugin
import { getIoInstance, broadcastQueueStateUpdate } from '../server/webSocket'; // Added import
import { createNotificationEntry } from '../notifications/operations';
import { translateAndStore, deleteTranslations, getTranslatedString } from '../server/translations'; 
// import { type UpdateAppointmentHistory } from 'wasp/server/operations';
// import { assertProviderDashboardAccess } from '../auth/utils'; // Corrected path

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);



// --- GetProviderCategories Query (existing code) ---
// Update input type to accept targetLanguage
const getProviderCategoriesInputSchema = z.object({
  targetLanguage: z.nativeEnum(LanguageCode).optional(),
});

type GetProviderCategoriesInput = z.infer<typeof getProviderCategoriesInputSchema>;

export const getProviderCategories = async (rawArgs: GetProviderCategoriesInput, context: any): Promise<ProviderCategory[]> => {
  const args = ensureArgsSchemaOrThrowHttpError(getProviderCategoriesInputSchema, rawArgs ?? {});
  const targetLanguage = args.targetLanguage;
  console.log(`[getProviderCategories] targetLanguage: ${targetLanguage}`);
  const categories = await context.entities.ProviderCategory.findMany({
    orderBy: {
      // title: 'asc', // Ordering by original title might be less relevant if translated
      id: 'asc', // Order by ID for consistency
    },
    // Optionally include children or parent if you want to build a hierarchy
    // include: { children: true, parent: true }
  });

  if (targetLanguage) {
    for (const category of categories) {
      const translatedTitle = await getTranslatedString(
        context.entities, // Pass the entities object from context
        'ProviderCategory',
        String(category.id),
        'title',
        targetLanguage
      );
      if (translatedTitle) {
        category.title = translatedTitle;
      }
    }
  }
  // If sorting by translated title is desired, it might be better to do it client-side
  // or fetch all translations and sort in JS if performance allows.
  // For now, returning as fetched (ordered by ID or original title depending on above choice).
  return categories;
}; 




export const getUserServiceProvider: GetUserServiceProvider<{ userId: string }, any | null> = async (args, context) => {
  const { userId } = args;
  if (!userId) {
      throw new HttpError(400, 'User ID is required.');
  }
  console.log("Fetching SProvider for userId:", userId);
  const serviceProvider = await context.entities.SProvider.findUnique({
    where: { userId },
    include: {
      category: true,
      providingPlaces: {
        include: {
          detailedAddress: true,
          queues: {
            include: {
              services: true,
            }
          }
        }
      },
      services: true,
      queues: {
        include: {
          services: true,
        }
      },
    },
  });
  console.log("Fetched SProvider:", serviceProvider);
  return serviceProvider;
};

// --- Update SProvider Action --- 

// Input validation schema
const updateSProviderInputSchema = z.object({
    title: z.string().optional(),
    phone: z.string().optional(),
    presentation: z.string().optional(),
    address: z.string().optional(),
    city: z.string().optional(),
    postalCode: z.string().optional(),
    country: z.string().optional(),
    latitude: z.number().optional(),
    longitude: z.number().optional(),
});

type UpdateSProviderData = z.infer<typeof updateSProviderInputSchema>;

export const updateSProvider = async (rawArgs: UpdateSProviderData, context: any): Promise<SProvider> => {
    if (!context.user) {
        throw new HttpError(401); // User must be logged in
    }

    // Validate input using the helper (args are already validated)
    const args = ensureArgsSchemaOrThrowHttpError(updateSProviderInputSchema, rawArgs);
    const userId = context.user.id;

    // Find the existing SProvider for the user
    const existingProvider = await context.entities.SProvider.findUnique({
        where: { userId: userId },
    });

    if (!existingProvider) {
        throw new HttpError(404, 'Service provider profile not found for this user.');
    }

    // Update the SProvider record
    try {
        const updatedProvider = await context.entities.SProvider.update({
            where: { userId: userId },
            data: {
                title: args.title,
                phone: args.phone,
                presentation: args.presentation,
            },
        });

        if (updatedProvider.title) { // Or args.title if you prefer the input value that was definitely provided
            await translateAndStore(
                context.entities,
                String(updatedProvider.id),
                'SProvider',
                'title',
                updatedProvider.title 
            );
        }   
        if (updatedProvider.presentation) { // Or args.title if you prefer the input value that was definitely provided
            await translateAndStore(
                context.entities,
                String(updatedProvider.id),
                'SProvider',
                'presentation',
                updatedProvider.presentation 
            );
        }  
        return updatedProvider;
    } catch (error: any) {
        console.error(`Failed to update SProvider for user ${userId}:`, error);
        throw new HttpError(500, error.message || 'Failed to update provider profile.');
    }
};

// --- Add SProvidingPlace Action ---

// Input validation schema for adding a place
const addSProvidingPlaceInputSchema = z.object({
    name: z.string().min(1, "Place name is required"),
    shortName: z.string().optional(),
    
    mobile: z.string().optional(),
    // isMobileHidden: z.boolean().default(false), // Currently not in form
    fax: z.string().optional(),
    floor: z.string().optional(),
    parking: z.boolean().default(false),
    elevator: z.boolean().default(false),
    handicapAccess: z.boolean().default(false),
    timezone: z.string().optional(), // Add timezone to schema
    // added for new address
    address: z.string().optional(),
    city: z.string().optional(),
    postalCode: z.string().optional(),
    country: z.string().optional(),
    latitude: z.number().optional(),
    longitude: z.number().optional(),
    // Opening hours
    openingHours: z.array(z.object({
        dayOfWeek: z.string().min(1, "Day of week is required"),
        isActive: z.boolean().default(true),
        hours: z.array(z.object({
            timeFrom: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format"),
            timeTo: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format"),
        })),
    })).optional(),
});

type AddSProvidingPlaceData = z.infer<typeof addSProvidingPlaceInputSchema>;

// Type annotation will be inferred by Wasp after restart
export const addSProvidingPlace = async (rawArgs: AddSProvidingPlaceData, context: any): Promise<SProvidingPlace> => {
    if (!context.user) {
        throw new HttpError(401);
    }

    const args = ensureArgsSchemaOrThrowHttpError(addSProvidingPlaceInputSchema, rawArgs);
    const userId = context.user.id;

    // 1. Find the SProvider ID for the logged-in user
    const provider = await context.entities.SProvider.findUnique({
        where: { userId: userId },
        select: { id: true }, // Only need the ID
    });

    if (!provider) {
        throw new HttpError(404, 'Service provider profile not found for this user. Cannot add place.');
    }

    // 2. Create the new SProvidingPlace and default openings within a transaction
    try {
        const newPlaceWithOpenings = await prisma.$transaction(async (tx: Prisma.TransactionClient) => {
            // Create the place
            const newPlace = await tx.sProvidingPlace.create({
                data: {
                    provider: {
                        connect: {
                            id: provider.id
                        }
                    },
                    name: args.name,
                    shortName: args.shortName,
                    mobile: args.mobile,
                    fax: args.fax,
                    floor: args.floor,
                    parking: args.parking,
                    elevator: args.elevator,
                    handicapAccess: args.handicapAccess,
                    timezone: args.timezone,
                    detailedAddress: {
                        create: {
                            address: args.address!,
                            city: args.city!,
                            country: args.country! || "Algeria",
                            postalCode: args.postalCode!,
                            latitude: args.latitude!,
                            longitude: args.longitude!,
                        }
                    }
                },
                include: {
                    detailedAddress: true,
                }
            });

            // Create opening hours
            if (args.openingHours && args.openingHours.length > 0) {
                // Use provided opening hours
                for (const openingDay of args.openingHours) {
                    const opening = await tx.opening.create({
                        data: {
                            sProvidingPlaceId: newPlace.id,
                            dayOfWeek: openingDay.dayOfWeek,
                            type: 'regular',
                            isActive: openingDay.isActive,
                        },
                    });

                    // Create opening hours intervals
                    for (const hourInterval of openingDay.hours) {
                        await tx.openingHours.create({
                            data: {
                                openingId: opening.id,
                                timeFrom: hourInterval.timeFrom,
                                timeTo: hourInterval.timeTo,
                            },
                        });
                    }
                }
            } else {
                // Create default schedule (e.g., Mon-Fri 9-5)
                const defaultDays = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"];
                const defaultTimeFrom = "09:00";
                const defaultTimeTo = "17:00";

                // Create default Openings and OpeningHours
                for (const day of defaultDays) {
                    const opening = await tx.opening.create({
                        data: {
                            sProvidingPlaceId: newPlace.id,
                            dayOfWeek: day,
                            type: 'regular',
                            isActive: true, // Default to active
                        },
                    });

                    await tx.openingHours.create({
                        data: {
                            openingId: opening.id,
                            timeFrom: defaultTimeFrom,
                            timeTo: defaultTimeTo,
                        },
                    });
                }

                // Optionally create inactive openings for Sat/Sun
                const weekendDays = ["Saturday", "Sunday"];
                 for (const day of weekendDays) {
                     await tx.opening.create({
                         data: {
                             sProvidingPlaceId: newPlace.id,
                             dayOfWeek: day,
                             type: 'regular',
                             isActive: false, // Default weekend to inactive
                         },
                         // No hours needed for inactive days
                     });
                 }
            }

             if (newPlace.name) { // Or args.title if you prefer the input value that was definitely provided
                await translateAndStore(
                    context.entities,
                    String(newPlace.id),
                    'SProvidingPlace',
                    'name',
                    newPlace.name 
                );
            }

            if(newPlace.detailedAddress?.address) {
                await translateAndStore(
                    context.entities,
                    String(newPlace.id),
                    'SProvidingPlace',
                    'detailedAddress.address',
                    newPlace.detailedAddress.address 
                );
            }

            return newPlace; // Return the created place
        });

        return newPlaceWithOpenings;
    } catch (error: any) {
        console.error(`Failed to add SProvidingPlace or default openings for user ${userId}:`, error);
        // Handle specific transaction errors if needed
        throw new HttpError(500, error.message || 'Failed to add new place and default schedule.');
    }
};

// --- Get SProvidingPlaces Query ---

// Type annotation will be inferred by Wasp after restart
export const getSProvidingPlaces = async (args: unknown, context: any): Promise<SProvidingPlace[]> => {
    if (!context.user) {
        throw new HttpError(401);
    }

    const userId = context.user.id;

    // 1. Find the SProvider ID for the logged-in user
    const provider = await context.entities.SProvider.findUnique({
        where: { userId: userId },
        select: { id: true }, // Only need the ID
    });

    // 2. If provider doesn't exist, return empty array (or throw error? depends on expected behavior)
    if (!provider) {
        // Or throw new HttpError(404, 'Service provider profile not found.');
        return [];
    }

    // 3. Fetch all places linked to this provider
    const places = await context.entities.SProvidingPlace.findMany({
        where: { sProviderId: provider.id },
        include: {
            detailedAddress: true,
            queues: {
                include: {
                    services: true,
                    openings: {
                        include: {
                            hours: true
                        }
                    }
                }
            },
            openings: {
                where: { type: 'regular' },
                include: {
                    hours: {
                        orderBy: { timeFrom: 'asc' }
                    }
                },
                orderBy: [
                    // Custom ordering for days of week
                    { dayOfWeek: 'asc' }
                ]
            }
        },
        orderBy: {
            createdAt: 'desc', // Or order by name, etc.
        },
    });
    console.log("Fetched SProvidingPlaces:", places);

    return places;
};

// --- Update SProvidingPlace Action ---

// Input validation schema for updating a place
// Note: Mirrors addSProvidingPlaceInputSchema but includes placeId
const updateSProvidingPlaceInputSchema = z.object({
    placeId: z.number(), // ID of the place to update
    name: z.string().min(1, "Place name is required"),
    shortName: z.string().optional(),
    address: z.string().optional(), // Add address
    city: z.string().optional(),    // Add city
    mobile: z.string().optional(),
    fax: z.string().optional(),
    floor: z.string().optional(),
    parking: z.boolean().default(false),
    elevator: z.boolean().default(false),
    handicapAccess: z.boolean().default(false),
    timezone: z.string().optional(),
    latitude: z.number().optional(),
    longitude: z.number().optional(),
    postalCode: z.string().optional(),
    country: z.string().optional(),
    // Opening hours
    openingHours: z.array(z.object({
        dayOfWeek: z.string().min(1, "Day of week is required"),
        isActive: z.boolean().default(true),
        hours: z.array(z.object({
            timeFrom: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format"),
            timeTo: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format"),
        })),
    })).optional(),
});

type UpdateSProvidingPlaceData = z.infer<typeof updateSProvidingPlaceInputSchema>;

// Type annotation will be inferred by Wasp after restart
export const updateSProvidingPlace = async (rawArgs: UpdateSProvidingPlaceData, context: any): Promise<SProvidingPlace> => {
    if (!context.user) {
        throw new HttpError(401);
    }

    const args = ensureArgsSchemaOrThrowHttpError(updateSProvidingPlaceInputSchema, rawArgs);
    const userId = context.user.id;

    // 1. Verify ownership: Check if the place exists and belongs to the user's provider
    const placeToUpdate = await context.entities.SProvidingPlace.findFirst({
        where: {
            id: args.placeId,
            provider: { // Check the related SProvider
                userId: userId, // Matches the logged-in user
            },
        },
    });

    if (!placeToUpdate) {
        throw new HttpError(404, 'Providing place not found or you do not have permission to update it.');
    }

    // 2. Update the SProvidingPlace
    try {
        // Prepare the data for updating
        const updateData: any = {
            name: args.name,
            shortName: args.shortName,
            address: args.address,
            city: args.city,
            mobile: args.mobile,
            fax: args.fax,
            floor: args.floor,
            parking: args.parking,
            elevator: args.elevator,
            handicapAccess: args.handicapAccess,
            timezone: args.timezone,
        };

        // Only update detailedAddress if address-related fields are provided
        if (args.address !== undefined || args.city !== undefined || args.country !== undefined || 
            args.postalCode !== undefined || args.latitude !== undefined || args.longitude !== undefined) {
            
            // Get current detailed address to merge with new data
            const currentPlace = await context.entities.SProvidingPlace.findUnique({
                where: { id: args.placeId },
                include: { detailedAddress: true }
            });

            if (currentPlace?.detailedAddress) {
                updateData.detailedAddress = {
                    update: {
                        address: args.address !== undefined ? args.address : currentPlace.detailedAddress.address,
                        city: args.city !== undefined ? args.city : currentPlace.detailedAddress.city,
                        country: args.country !== undefined ? args.country : (currentPlace.detailedAddress.country || "Algeria"),
                        postalCode: args.postalCode !== undefined ? args.postalCode : currentPlace.detailedAddress.postalCode,
                        latitude: args.latitude !== undefined ? args.latitude : currentPlace.detailedAddress.latitude,
                        longitude: args.longitude !== undefined ? args.longitude : currentPlace.detailedAddress.longitude,
                    }
                };
            }
        }

        const updatedPlace = await context.entities.SProvidingPlace.update({
            where: { id: args.placeId },
            data: updateData,
            include: {
                detailedAddress: true,
            }
        });

        // 3. Update opening hours if provided
        if (args.openingHours && args.openingHours.length > 0) {
            // Delete existing opening hours for this place
            const existingOpenings = await context.entities.Opening.findMany({
                where: {
                    sProvidingPlaceId: args.placeId,
                    type: 'regular'
                },
                include: {
                    hours: true
                }
            });

            // Delete existing opening hours and openings
            for (const opening of existingOpenings) {
                await context.entities.OpeningHours.deleteMany({
                    where: { openingId: opening.id }
                });
                await context.entities.Opening.delete({
                    where: { id: opening.id }
                });
            }

            // Create new opening hours
            for (const openingDay of args.openingHours) {
                const opening = await context.entities.Opening.create({
                    data: {
                        sProvidingPlaceId: args.placeId,
                        dayOfWeek: openingDay.dayOfWeek,
                        type: 'regular',
                        isActive: openingDay.isActive,
                    },
                });

                // Create opening hours intervals
                for (const hourInterval of openingDay.hours) {
                    await context.entities.OpeningHours.create({
                        data: {
                            openingId: opening.id,
                            timeFrom: hourInterval.timeFrom,
                            timeTo: hourInterval.timeTo,
                        },
                    });
                }
            }
        }
        if (updatedPlace.name) { // Or args.title if you prefer the input value that was definitely provided
            await translateAndStore(
                context.entities,
                String(updatedPlace.id),
                'SProvidingPlace',
                'name',
                updatedPlace.name 
            );
        }

        if(updatedPlace.detailedAddress?.address) {
            await translateAndStore(
                context.entities,
                String(updatedPlace.id),
                'SProvidingPlace',
                'detailedAddress.address',
                updatedPlace.detailedAddress.address 
            );
        }
        return updatedPlace;
    } catch (error: any) {
        console.error(`Failed to update SProvidingPlace ${args.placeId} for user ${userId}:`, error);
        throw new HttpError(500, error.message || 'Failed to update place.');
    }
};

// --- Delete SProvidingPlace Action ---

const deleteSProvidingPlaceInputSchema = z.object({
    placeId: z.number().int().positive("Valid place ID is required"),
});

type DeleteSProvidingPlaceData = z.infer<typeof deleteSProvidingPlaceInputSchema>;

export const deleteSProvidingPlace = async (rawArgs: DeleteSProvidingPlaceData, context: any): Promise<{ success: boolean; message: string }> => {
    if (!context.user) {
        throw new HttpError(401);
    }

    const args = ensureArgsSchemaOrThrowHttpError(deleteSProvidingPlaceInputSchema, rawArgs);
    const userId = context.user.id;

    // 1. Verify ownership: Check if the place exists and belongs to the user's provider
    const placeToDelete = await context.entities.SProvidingPlace.findFirst({
        where: {
            id: args.placeId,
            provider: { // Check the related SProvider
                userId: userId, // Matches the logged-in user
            },
        },
        include: {
            openings: {
                include: {
                    hours: {
                        select: { id: true }
                    }
                }
            },
            queues: {
                select: { id: true, isActive: true }
            },
            appointments: {
                where: {
                    status: { notIn: ['canceled', 'completed', 'noshow'] },
                    expectedAppointmentStartTime: { gte: new Date() } // Future appointments
                },
                select: { id: true }
            }
        }
    });

    if (!placeToDelete) {
        throw new HttpError(404, 'Providing place not found or you do not have permission to delete it.');
    }

    // 2. Check for dependencies that prevent deletion
    const activeQueues = placeToDelete.queues.filter((queue: any) => queue.isActive);
    const futureAppointments = placeToDelete.appointments;

    if (activeQueues.length > 0) {
        throw new HttpError(409, `Cannot delete location: It has ${activeQueues.length} active queue(s). Please deactivate or delete the queues first.`);
    }

    if (futureAppointments.length > 0) {
        throw new HttpError(409, `Cannot delete location: It has ${futureAppointments.length} future appointment(s). Please cancel or reschedule the appointments first.`);
    }

    // 3. Delete the SProvidingPlace
    try {
        await deleteTranslations(context.entities, String(args.placeId), 'SProvidingPlace');
        // Delete all opening hours
        await context.entities.OpeningHours.deleteMany({
            where: { opening: { sProvidingPlaceId: args.placeId } }
        });
        // Delete all openings
        await context.entities.Opening.deleteMany({
            where: { sProvidingPlaceId: args.placeId }
        });
        
        await context.entities.SProvidingPlace.delete({
            where: { id: args.placeId },
        });

        return {
            success: true,
            message: 'Location deleted successfully'
        };
    } catch (error: any) {
        console.error(`Failed to delete SProvidingPlace ${args.placeId} for user ${userId}:`, error);
        if (error.code === 'P2025') { // Record to delete does not exist
            throw new HttpError(404, 'Location not found (already deleted?).');
        }
        throw new HttpError(500, error.message || 'Failed to delete location.');
    }
};

// --- Create ServiceCategory Action ---

// Input validation schema for creating a service category
const createServiceCategoryInputSchema = z.object({
    title: z.string().min(1, "Category title is required"),
});

type CreateServiceCategoryData = z.infer<typeof createServiceCategoryInputSchema>;

// Type annotation will be inferred by Wasp after restart
export const createServiceCategory = async (rawArgs: CreateServiceCategoryData, context: any): Promise<ServiceCategory> => {
    if (!context.user) {
        throw new HttpError(401);
    }

    const args = ensureArgsSchemaOrThrowHttpError(createServiceCategoryInputSchema, rawArgs);
    const userId = context.user.id;

    // 1. Find the SProvider ID for the logged-in user
    const provider = await context.entities.SProvider.findUnique({
        where: { userId: userId },
        select: { id: true }, // Only need the ID
    });

    if (!provider) {
        throw new HttpError(404, 'Service provider profile not found for this user. Cannot add category.');
    }

    // 2. Check if a category with the same title already exists for this provider
    const existingCategory = await context.entities.ServiceCategory.findFirst({
        where: {
            title: args.title,
            sProviderId: provider.id,
        },
    });

    if (existingCategory) {
        throw new HttpError(400, `A service category named "${args.title}" already exists.`);
    }

    // 3. Create the new ServiceCategory linked to the SProvider
    try {
        const newCategory = await context.entities.ServiceCategory.create({
            data: {
                sProviderId: provider.id, // Link to the provider
                title: args.title,
            },
        });

        if (newCategory.title) { // Or args.title if you prefer the input value that was definitely provided
            await translateAndStore(
                context.entities,
                String(newCategory.id),
                'ServiceCategory',
                'title',
                newCategory.title 
            );
        }
        return newCategory;
    } catch (error: any) {
        console.error(`Failed to create ServiceCategory for user ${userId}:`, error);
        // Prisma unique constraint violation check (just in case, although we checked above)
        if (error.code === 'P2002' && error.meta?.target?.includes('title') && error.meta?.target?.includes('sProviderId')) {
             throw new HttpError(400, `A service category named "${args.title}" already exists (concurrent creation?).`);
        }
        throw new HttpError(500, error.message || 'Failed to create new service category.');
    }
};

// --- Get ServiceCategories Query ---

// Type annotation will be inferred by Wasp after restart
export const getServiceCategories = async (args: unknown, context: any): Promise<ServiceCategory[]> => {
    if (!context.user) {
        throw new HttpError(401);
    }

    const userId = context.user.id;

    // 1. Find the SProvider ID for the logged-in user
    const provider = await context.entities.SProvider.findUnique({
        where: { userId: userId },
        select: { id: true }, // Only need the ID
    });

    // 2. If provider doesn't exist, return empty array (or throw error?)
    if (!provider) {
        // Depending on desired behavior, you might want to return an empty array
        // or throw an error if a provider profile is expected.
        // throw new HttpError(404, 'Service provider profile not found.');
         return [];
    }

    // 3. Fetch all service categories linked to this provider
    const categories = await context.entities.ServiceCategory.findMany({
        where: { sProviderId: provider.id },
        orderBy: {
            title: 'asc',
        },
    });

    return categories;
};

// --- Update ServiceCategory Action ---

const updateServiceCategoryInputSchema = z.object({
    categoryId: z.number().int().positive("Valid category ID is required"),
    title: z.string().min(1, "Category title is required").optional(),
});

type UpdateServiceCategoryData = z.infer<typeof updateServiceCategoryInputSchema>;

export const updateServiceCategory = async (rawArgs: UpdateServiceCategoryData, context: any): Promise<ServiceCategory> => {
    if (!context.user) {
        throw new HttpError(401);
    }

    const args = ensureArgsSchemaOrThrowHttpError(updateServiceCategoryInputSchema, rawArgs);
    const userId = context.user.id;

    // 1. Verify ownership: Check if the category exists and belongs to the user's provider
    const categoryToUpdate = await context.entities.ServiceCategory.findFirst({
        where: {
            id: args.categoryId,
            provider: { // Check the related SProvider
                userId: userId, // Matches the logged-in user
            },
        },
    });

    if (!categoryToUpdate) {
        throw new HttpError(404, 'Service category not found or you do not have permission to update it.');
    }

    // 2. Check if a category with the same title already exists for this provider (if title is being updated)
    if (args.title && args.title !== categoryToUpdate.title) {
        const existingCategory = await context.entities.ServiceCategory.findFirst({
            where: {
                title: args.title,
                sProviderId: categoryToUpdate.sProviderId,
                id: { not: args.categoryId }, // Exclude current category
            },
        });

        if (existingCategory) {
            throw new HttpError(400, `A service category named "${args.title}" already exists.`);
        }
    }

    // 3. Update the ServiceCategory
    try {
        const updatedCategory = await context.entities.ServiceCategory.update({
            where: { id: args.categoryId },
            data: {
                ...(args.title && { title: args.title }),
            },
        });

        // Update translation if title changed
        if (args.title && args.title !== categoryToUpdate.title) {
            await translateAndStore(
                context.entities,
                String(updatedCategory.id),
                'ServiceCategory',
                'title',
                updatedCategory.title
            );
        }

        return updatedCategory;
    } catch (error: any) {
        console.error(`Failed to update ServiceCategory ${args.categoryId} for user ${userId}:`, error);
        throw new HttpError(500, error.message || 'Failed to update service category.');
    }
};

// --- Delete ServiceCategory Action ---

// Input validation schema for deleting a service category
const deleteServiceCategoryInputSchema = z.object({
    categoryId: z.number(), // ID of the category to delete
});

type DeleteServiceCategoryData = z.infer<typeof deleteServiceCategoryInputSchema>;

// Type annotation will be inferred by Wasp after restart
export const deleteServiceCategory = async (rawArgs: DeleteServiceCategoryData, context: any): Promise<ServiceCategory> => {
    if (!context.user) {
        throw new HttpError(401);
    }

    const args = ensureArgsSchemaOrThrowHttpError(deleteServiceCategoryInputSchema, rawArgs);
    const userId = context.user.id;

    // 1. Verify ownership: Check if the category exists and belongs to the user's provider
    const categoryToDelete = await context.entities.ServiceCategory.findFirst({
        where: {
            id: args.categoryId,
            provider: { // Check the related SProvider
                userId: userId, // Matches the logged-in user
            },
        },
    });

    if (!categoryToDelete) {
        throw new HttpError(404, 'Service category not found or you do not have permission to delete it.');
    }

    // TODO: Add check here if category is associated with any Services? Prevent deletion?
    // const associatedServices = await context.entities.Service.count({
    //     where: { serviceCategoryId: args.categoryId },
    // });
    // if (associatedServices > 0) {
    //     throw new HttpError(400, 'Cannot delete category: It is currently associated with one or more services.');
    // }

    // 2. Delete the ServiceCategory
    await deleteTranslations(context.entities, String(args.categoryId), 'ServiceCategory');
    try {
        const deletedCategory = await context.entities.ServiceCategory.delete({
            where: { id: args.categoryId },
        });
        return deletedCategory; // Return the deleted category data
    } catch (error: any) {
        console.error(`Failed to delete ServiceCategory ${args.categoryId} for user ${userId}:`, error);
        // Handle potential errors, e.g., if the category was already deleted
        if (error.code === 'P2025') { // Record to delete does not exist.
             throw new HttpError(404, 'Service category not found (already deleted?).');
        }
        throw new HttpError(500, error.message || 'Failed to delete service category.');
    }
};

// --- Get SProviderOpenings Query ---

// Type annotation will be inferred by Wasp after restart
export const getSProviderOpenings = async (args: unknown, context: any): Promise<Opening[]> => {
    if (!context.user) {
        throw new HttpError(401);
    }
    const userId = context.user.id;

    // 1. Find the SProvider for the user
    const provider = await context.entities.SProvider.findUnique({
        where: { userId: userId },
        select: { id: true },
    });
    if (!provider) {
        // Consider if provider profile is required to view/edit settings
        // throw new HttpError(404, 'Service provider profile not found.');
        return []; // Or return empty if settings page might be accessible without full profile
    }

    // 2. Find the SProvidingPlace (assuming one place per provider for now based on UI)
    // If multiple places are supported later, this needs placeId input
    const place = await context.entities.SProvidingPlace.findFirst({
        where: { sProviderId: provider.id },
        select: { id: true },
    });

    if (!place) {
        // No place found for this provider yet
        return [];
    }

    // 3. Fetch all 'regular' openings and their hours for this place
    const openings = await context.entities.Opening.findMany({
        where: {
            sProvidingPlaceId: place.id,
            type: 'regular', // Assuming we manage 'regular' hours here
        },
        include: {
            hours: {
                orderBy: { timeFrom: 'asc' }, // Order hours within the day
            },
        },
        orderBy: {
            // Define a consistent order for days if needed, e.g., by a custom order field or explicit mapping
            // For now, Prisma doesn't guarantee order based on string names like 'Monday'
            // We might need to sort this on the client-side based on day names
             id: 'asc' // Placeholder ordering
        },
    });

    // TODO: Consider ensuring all 7 days exist, creating them if necessary?
    // Or handle missing days on the client-side. For now, returning what exists.

    return openings;
};


// --- Update SProviderOpenings Action ---

const openingHoursInputSchema = z.object({
    timeFrom: z.string().regex(/^\d{2}:\d{2}$/, "Time must be in HH:mm format"),
    timeTo: z.string().regex(/^\d{2}:\d{2}$/, "Time must be in HH:mm format"),
});

const dayOpeningInputSchema = z.object({
    dayOfWeek: z.string(), // e.g., "Monday"
    hours: z.array(openingHoursInputSchema),
    isActive: z.boolean().default(true), // Allow marking a day as inactive
});

const updateSProviderOpeningsInputSchema = z.object({
    placeId: z.number(),
    schedule: z.array(dayOpeningInputSchema), // Array representing the week's schedule
});

type UpdateSProviderOpeningsData = z.infer<typeof updateSProviderOpeningsInputSchema>;

// Helper function to get day order (can be customized)
const getDayOrder = (day: string): number => {
    const order = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"];
    return order.indexOf(day);
}

export const updateSProviderOpenings = async (rawArgs: UpdateSProviderOpeningsData, context: any): Promise<{ success: boolean }> => {
    if (!context.user) {
        throw new HttpError(401);
    }

    const args = ensureArgsSchemaOrThrowHttpError(updateSProviderOpeningsInputSchema, rawArgs);
    const userId = context.user.id;

    // 1. Verify ownership of the place
    const place = await context.entities.SProvidingPlace.findFirst({
        where: {
            id: args.placeId,
            provider: { userId: userId },
        },
        select: { id: true },
    });

    if (!place) {
        throw new HttpError(404, 'Providing place not found or you do not have permission to update it.');
    }

    // 2. Perform updates within a transaction
    try {
        await prisma.$transaction(async (tx: Prisma.TransactionClient) => {
            // Process each day in the provided schedule
            for (const daySchedule of args.schedule) {
                // Find or create the 'regular' Opening record for this day and place
                const opening = await tx.opening.upsert({
                    where: {
                        sProvidingPlaceId_dayOfWeek_type: {
                            sProvidingPlaceId: place.id,
                            dayOfWeek: daySchedule.dayOfWeek,
                            type: 'regular',
                        }
                    },
                    update: {
                        isActive: daySchedule.isActive, // Update active status
                    },
                    create: {
                        sProvidingPlaceId: place.id,
                        dayOfWeek: daySchedule.dayOfWeek,
                        type: 'regular',
                        isActive: daySchedule.isActive,
                    },
                });

                // Delete existing hours for this opening
                await tx.openingHours.deleteMany({
                    where: { openingId: opening.id },
                });

                // Create new hours if the day is active and hours are provided
                if (daySchedule.isActive && daySchedule.hours.length > 0) {
                    // Validate time ranges (basic check: from < to)
                    const validatedHours = daySchedule.hours
                        .filter(h => h.timeFrom < h.timeTo) // Basic validation
                        .map(h => ({
                            openingId: opening.id,
                            timeFrom: h.timeFrom,
                            timeTo: h.timeTo,
                        }));

                     if (validatedHours.length !== daySchedule.hours.length) {
                         console.warn(`Some invalid time ranges skipped for ${daySchedule.dayOfWeek}`);
                         // Optionally throw an error if strict validation is needed:
                         // throw new Error(`Invalid time range detected for ${daySchedule.dayOfWeek}: 'from' time must be before 'to' time.`);
                     }
                    
                    if (validatedHours.length > 0) {
                        await tx.openingHours.createMany({
                            data: validatedHours,
                        });
                    }
                }
            }
             // Optional: Clean up days not included in the input?
             // Or assume the input contains all 7 days.
             // If input might be partial, need logic to handle days not present.
        });

        return { success: true };

    } catch (error: any) {
        console.error(`Failed to update openings for place ${args.placeId} and user ${userId}:`, error);
        // Handle specific errors like transaction failures if necessary
        throw new HttpError(500, error.message || 'Failed to update opening hours.');
    }
};

// --- Create Service Action ---

const serviceInputSchemaBase = z.object({
    title: z.string().min(1, "Service title is required"),
    duration: z.number().int().positive("Duration must be a positive number (minutes)"),
    color: z.string().regex(/^#[0-9A-Fa-f]{6}$/, "Color must be a valid hex color"),
    acceptOnline: z.boolean().default(true),
    acceptNew: z.boolean().default(true),
    notificationOn: z.boolean().default(true),
    pointsRequirements: z.number().int().min(0, "Points requirements cannot be negative").default(1),
    // Add these fields as optional for now since they may not exist in DB yet
    price: z.number().min(0, "Price cannot be negative").optional(),
    isPublic: z.boolean().optional(),
    deliveryType: z.enum(['at_location', 'at_customer', 'both']).optional(),
    servedRegions: z.array(z.string().min(1, "Region name cannot be empty")).optional().nullable(),
    description: z.string().max(1000, "Description too long").optional(),
});

const createServiceInputSchema = serviceInputSchemaBase;

type CreateServiceData = z.infer<typeof createServiceInputSchema>;

// Type annotation inferred by Wasp
export const createService = async (rawArgs: CreateServiceData, context: any): Promise<Service> => {
    if (!context.user) {
        throw new HttpError(401);
    }
    const args = ensureArgsSchemaOrThrowHttpError(createServiceInputSchema, rawArgs);
    console.log("createServiceARGS", args);
    const userId = context.user.id;

    // 1. Find provider ID
    const provider = await context.entities.SProvider.findUnique({
        where: { userId: userId },
        select: { id: true },
    });
    if (!provider) {
        throw new HttpError(404, 'Service provider profile not found.');
    }

    // 2. Create Service
    try {
        const newService = await context.entities.Service.create({
            data: {
                sProviderId: provider.id,
                title: args.title,
                duration: args.duration,
                color: args.color,
                acceptOnline: args.acceptOnline,
                acceptNew: args.acceptNew,
                notificationOn: args.notificationOn,
                pointsRequirements: args.pointsRequirements,
                price: args.price,
                isPublic: args.isPublic,
                deliveryType: args.deliveryType,
                servedRegions: args.servedRegions && args.servedRegions?.length > 0 ? args.servedRegions.join(",") : "[]",
                description: args.description,
            },
        });

        if (newService.title) {
            await translateAndStore(
                context.prisma,
                String(newService.id),
                'Service',
                'title',
                newService.title
            );
        }
        return newService;
    } catch (error: any) {
        console.error(`Failed to create Service for user ${userId}:`, error);
        // Handle potential Prisma errors, e.g., unique constraints if any
        throw new HttpError(500, error.message || 'Failed to create service.');
    }
};

// --- Get Services Query ---

// Type annotation inferred by Wasp
export const getServices = async (args: unknown, context: any): Promise<Service[]> => {
    if (!context.user) {
        throw new HttpError(401);
    }
    const userId = context.user.id;

    // 1. Find provider ID
    const provider = await context.entities.SProvider.findUnique({
        where: { userId: userId },
        select: { id: true },
    });
    if (!provider) {
        return []; // No provider, no services
    }

    // 2. Fetch services linked to this provider
    const services = await context.entities.Service.findMany({
        where: { sProviderId: provider.id },
        orderBy: {
            title: 'asc',
        },
    });

    console.log("services", services);
    return services;
};

// --- Update Service Action ---

const updateServiceInputSchema = serviceInputSchemaBase.extend({
    serviceId: z.number().int().positive(), // ID of the service to update
    servedRegions: z.array(z.string()).nullable().transform(val => val === null ? [] : val),
});

type UpdateServiceData = z.infer<typeof updateServiceInputSchema>;

// Type annotation inferred by Wasp
export const updateService = async (rawArgs: UpdateServiceData, context: any): Promise<Service> => {
    if (!context.user) {
        throw new HttpError(401);
    }
    const args = ensureArgsSchemaOrThrowHttpError(updateServiceInputSchema, rawArgs);
    const userId = context.user.id;

    // 1. Verify ownership of the service
    const serviceToUpdate = await context.entities.Service.findFirst({
        where: {
            id: args.serviceId,
            provider: { userId: userId }, // Check ownership via provider relation
        },
        select: { id: true, sProviderId: true }, // Select provider ID for category check
    });

    if (!serviceToUpdate) {
        throw new HttpError(404, 'Service not found or you do not have permission to update it.');
    }

    // 2. Update Service
    try {
        const updatedService = await context.entities.Service.update({
            where: { id: args.serviceId }, // Already verified ownership
            data: {
                title: args.title,
                duration: args.duration,
                color: args.color,
                acceptOnline: args.acceptOnline,
                acceptNew: args.acceptNew,
                notificationOn: args.notificationOn,
                pointsRequirements: args.pointsRequirements,
                servedRegions: JSON.stringify(args.servedRegions),
                description: args.description,
                price: args.price,
                isPublic: args.isPublic,
                deliveryType: args.deliveryType,
            },
        });

        if (updatedService.title) { // Or args.title if you prefer the input value
            const d = await context.entities.Translation.findFirst({
                where: {
                    refId: String(updatedService.id),
                    tableName: 'Service',
                    refField: 'title',
                },
            });
            console.log("d", d);
            await translateAndStore(
                context.entities,
                String(updatedService.id),
                'Service',
                'title',
                updatedService.title
            );
        }

        return updatedService;
    } catch (error: any) {
        console.error(`Failed to update Service ${args.serviceId} for user ${userId}:`, error);
        throw new HttpError(500, error.message || 'Failed to update service.');
    }
};

// --- Delete Service Action ---

const deleteServiceInputSchema = z.object({
    serviceId: z.number().int().positive(),
});

type DeleteServiceData = z.infer<typeof deleteServiceInputSchema>;

// Type annotation inferred by Wasp - Explicit return type Promise<Service>
export const deleteService = async (rawArgs: DeleteServiceData, context: any): Promise<Service> => {
    if (!context.user) {
        throw new HttpError(401);
    }
    const args = ensureArgsSchemaOrThrowHttpError(deleteServiceInputSchema, rawArgs);
    const userId = context.user.id;

    // 1. Verify ownership
    const serviceToDelete = await context.entities.Service.findFirst({
        where: {
            id: args.serviceId,
            provider: { userId: userId },
        },
    });

    if (!serviceToDelete) {
        throw new HttpError(404, 'Service not found or you do not have permission to delete it.');
    }

    // Check if service is associated with any appointments (regardless of status)
    // We need to check ALL appointments because of foreign key constraints
    const associatedAppointments = await context.entities.Appointment.count({
        where: {
            serviceId: args.serviceId
        },
    });

    if (associatedAppointments > 0) {
        // Get appointment details for better error message
        const appointmentDetails = await context.entities.Appointment.findMany({
            where: { serviceId: args.serviceId },
            select: {
                id: true,
                status: true,
                expectedAppointmentStartTime: true,
                customerFolder: {
                    select: {
                        customer: {
                            select: {
                                firstName: true,
                                lastName: true
                            }
                        }
                    }
                }
            },
            take: 5 // Limit to first 5 for error message
        });

        const activeAppointments = appointmentDetails.filter(app =>
            !['canceled', 'completed', 'noshow'].includes(app.status)
        );

        if (activeAppointments.length > 0) {
            throw new HttpError(400,
                `Cannot delete service: It has ${activeAppointments.length} active appointment(s). ` +
                `Please cancel or complete these appointments first.`
            );
        } else {
            throw new HttpError(400,
                `Cannot delete service: It has ${associatedAppointments} historical appointment(s). ` +
                `Services with appointment history cannot be deleted to maintain data integrity.`
            );
        }
    }

    // 2. Delete Service
    try {
        await deleteTranslations(context.entities, String(args.serviceId), 'Service');
        const deletedService = await context.entities.Service.delete({
            where: { id: args.serviceId }, // Ownership verified
        });
        return deletedService;
    } catch (error: any) {
        console.error(`Failed to delete Service ${args.serviceId} for user ${userId}:`, error);

        if (error.code === 'P2025') { // Record to delete does not exist.
            throw new HttpError(404, 'Service not found (already deleted?).');
        }

        if (error.code === 'P2003') { // Foreign key constraint failed
            // This should not happen if our appointment check above is working correctly
            // But provide a helpful message just in case
            throw new HttpError(400,
                'Cannot delete service: It is still referenced by appointments or other records. ' +
                'Please ensure all related appointments are properly handled first.'
            );
        }

        // Handle other potential errors
        throw new HttpError(500, error.message || 'Failed to delete service.');
    }
};

// --- Get Provider Customers Query ---

// Type annotation inferred by Wasp
// Returns User objects linked via CustomerFolder to the current provider
export const getProviderCustomers = async (args: unknown, context: any): Promise<any[]> => {
    if (!context.user) {
        throw new HttpError(401);
    }
    const userId = context.user.id;

    // 1. Find the SProvider ID for the logged-in user
    const provider = await context.entities.SProvider.findUnique({
        where: { userId: userId },
        select: { id: true },
    });

    if (!provider) {
        // If the user is not a service provider, they have no customers in this context
        return [];
    }

    // 2. Find all CustomerFolders linked to this provider, including appointments
    // Define the expected type for findMany result including the nested customer and appointments
    type FolderWithCustomerAndAppointments = CustomerFolder & { 
        customer: User;
        appointments: Appointment[];
    };
    
    // Get appointment counts for this specific provider-customer relationship
    const customerFolders = await context.entities.CustomerFolder.findMany({
        where: {
            sProviderId: provider.id, // This ensures we only get appointments for this provider
            deleted: false // Exclude soft deleted customers
        },
        include: {
            customer: true, // Select the related User object
            appointments: {
                select: {
                    id: true // Only select ID to minimize data transfer
                }
            }
        },
        orderBy: {
            createdAt: 'asc'
        },
    }) as FolderWithCustomerAndAppointments[]; // Use the defined type

    // console.log("customerFolders", customerFolders);
    // 3. Extract the User objects from the folders
    // const customers = customerFolders.map(folder => folder.customer);

    return customerFolders;
};

// --- Create Provider Customer Action ---

const createProviderCustomerInputSchema = z.object({
    firstName: z.string().min(1, "First name is required"),
    lastName: z.string().min(1, "Last name is required"),
    mobileNumber: z.string().min(1, "Mobile number is required"), // Add more specific mobile validation
    email: z.string().email("Invalid email format").optional(),
    nationalId: z.string().optional().nullable(),
    notes: z.string().optional().nullable(),
});

type CreateProviderCustomerData = z.infer<typeof createProviderCustomerInputSchema>;

// Type annotation inferred by Wasp
export const createProviderCustomer: CreateProviderCustomer<CreateProviderCustomerData, CustomerFolder> = async (rawArgs, context): Promise<CustomerFolder> => {
    if (!context.user || !context.user.id) {
        throw new HttpError(401, "User not authenticated");
    }
    
    const providerUserId = context.user.id;
    const args = ensureArgsSchemaOrThrowHttpError(createProviderCustomerInputSchema, rawArgs);

    // 1. Find the SProvider ID for the logged-in user
    const provider = await context.entities.SProvider.findUnique({
        where: { userId: providerUserId },
        select: { id: true },
    });

    if (!provider) {
        throw new HttpError(403, "User is not a registered service provider.");
    }

    console.log("provider", provider);

    // 2. Validate and potentially format mobile number
    // const validatedMobile = ensureValidMobileNumber(args.mobileNumber); // Use helper if available - COMMENTED OUT
    const validatedMobile = args.mobileNumber; // Placeholder: Use raw number for now
    console.log("validatedMobile", validatedMobile);
    console.log("args.email", args.email);
    console.log("args.nationalId", args.nationalId);
    // 3. Use transaction for atomicity
    try {
        const customerFolder = await prisma.$transaction(async (tx: Prisma.TransactionClient) => {
            // 3a. Check if a user with any of the provided identifiers already exists
            
            // Explicitly type the array to hold Prisma User where conditions
            const orConditions: Prisma.UserWhereInput[] = [];
            
            // Mobile is mandatory per schema, always add it
            if (validatedMobile) {
                orConditions.push({ mobileNumber: validatedMobile });
            }
            // Only add email condition if email is provided
            if (args.email) {
                orConditions.push({ email: args.email });
            }
            // Only add nationalId condition if nationalId is provided
            if (args.nationalId) {
                orConditions.push({ nationalId: args.nationalId });
            }

            let customerUser: User | null = null;
            // Only query if we have at least one valid identifier condition
            if (orConditions.length > 0) { 
                customerUser = await tx.user.findFirst({
                    where: {
                        AND: [
                            { role: 'CUSTOMER' }, // Ensure the found user has the correct role
                            { OR: orConditions }  // Match based on the provided identifiers
                        ]
                    },
                });
            }

            console.log("Found existing customerUser:", customerUser);

            // 3b. If user doesn't exist, create them
            if (!customerUser) {
                console.log("No existing user found, creating new user...");
                try {

                    
                    customerUser = await tx.user.create({
                        data: {
                            firstName: args.firstName,
                            lastName: args.lastName,
                            mobileNumber: validatedMobile,
                            email: args.email || null, // Use null if empty string was passed
                            nationalId: args.nationalId || null, // Use null if empty string was passed
                            role: 'CUSTOMER', // Assign CUSTOMER role explicitly
                        },
                    });
                    console.log("Created new customerUser:", customerUser);
                } catch (createError: any) {
                    // Handle potential errors during creation, especially unique constraints
                    console.error("Error creating user:", createError);
                    // Re-throw to be caught by the outer catch block for unified error handling
                    throw createError; 
                }
            } else {
                console.log("Using existing customerUser:", customerUser.id);
            }

            // 3c. Check if a CustomerFolder already exists for this provider and user
            const existingFolder = await tx.customerFolder.findUnique({
                 where: {
                     sProviderId_userId: {
                         sProviderId: provider.id,
                         userId: customerUser.id // Use the ID of the found/created user
                     }
                 },
            });

            console.log("existingFolder", existingFolder);

            if (existingFolder) {
                if (existingFolder.deleted) {
                    // Customer exists but is soft deleted
                    throw new HttpError(409, "Customer exists in archive. If you want to restore it, please use the restore customer endpoint.");
                } else {
                    console.log("Customer folder already exists for this provider and user.");
                    return existingFolder; // Return existing folder
                }
            }

            // 3d. Create the CustomerFolder linking provider and user
            const newFolder = await tx.customerFolder.create({
                data: {
                    sProviderId: provider.id,
                    userId: customerUser.id,
                    notes: args.notes,
                },
            });

            console.log("newFolder", newFolder);

            return newFolder;
        });

        return customerFolder;

    } catch (error: any) {
        console.error(`Failed to create customer folder for provider ${provider.id}:`, error);
         if (error.code === 'P2002') { 
            const target = error.meta?.target;
            if (target?.includes('mobileNumber')) {
                throw new HttpError(409, "A user with this mobile number already exists (concurrent creation?). Please try again.");
            } else if (target?.includes('email')) {
                throw new HttpError(409, "A user with this email address already exists.");
            } else if (target?.includes('sProviderId') && target?.includes('userId')) {
                throw new HttpError(409, "Customer folder link already exists for this provider and user (concurrent creation?). Please try again.");
            } else {
                throw new HttpError(409, "A conflict occurred due to duplicate data. Please check the inputs.");
            }
        }
        throw new HttpError(500, error.message || 'Failed to add customer.');
    }
};

// --- Update Provider Customer Action ---

const updateProviderCustomerInputSchema = z.object({
    customerUserId: z.string().uuid("Invalid Customer User ID format"),
    firstName: z.string().min(1, "First name is required").optional(),
    lastName: z.string().min(1, "Last name is required").optional(),
    mobileNumber: z.string().min(1, "Mobile number is required").optional(),
    email: z.string().email("Invalid email format").optional(),
    nationalId: z.string().optional().nullable(),
    notes: z.string().optional().nullable(),
});

type UpdateProviderCustomerData = z.infer<typeof updateProviderCustomerInputSchema>;

export const updateProviderCustomer = async (rawArgs: UpdateProviderCustomerData, context: any): Promise<any> => {
    if (!context.user || !context.user.id) {
        throw new HttpError(401, "User not authenticated");
    }

    const providerUserId = context.user.id;
    const args = ensureArgsSchemaOrThrowHttpError(updateProviderCustomerInputSchema, rawArgs);

    // 1. Find the SProvider ID for the logged-in user
    const provider = await context.entities.SProvider.findUnique({
        where: { userId: providerUserId },
        select: { id: true },
    });

    if (!provider) {
        throw new HttpError(404, 'Service provider profile not found for this user.');
    }

    try {
        const result = await prisma.$transaction(async (tx: Prisma.TransactionClient) => {
            // 1. Verify the customer folder exists and belongs to this provider
            const customerFolder = await tx.customerFolder.findUnique({
                where: {
                    sProviderId_userId: {
                        sProviderId: provider.id,
                        userId: args.customerUserId
                    }
                },
                include: {
                    customer: true
                }
            });

            if (!customerFolder) {
                throw new HttpError(404, 'Customer not found for this provider.');
            }

            // 2. Prepare update data for the User entity
            const updateData: any = {};
            if (args.firstName !== undefined) updateData.firstName = args.firstName;
            if (args.lastName !== undefined) updateData.lastName = args.lastName;
            if (args.mobileNumber !== undefined) updateData.mobileNumber = args.mobileNumber;
            if (args.email !== undefined) updateData.email = args.email;
            if (args.nationalId !== undefined) updateData.nationalId = args.nationalId;

            // 3. Update the User entity if there are user fields to update
            let updatedUser = customerFolder.customer;
            if (Object.keys(updateData).length > 0) {
                updatedUser = await tx.user.update({
                    where: { id: args.customerUserId },
                    data: updateData
                });
            }

            // 4. Update the CustomerFolder notes if provided
            let updatedFolder = customerFolder;
            if (args.notes !== undefined) {
                updatedFolder = await tx.customerFolder.update({
                    where: {
                        sProviderId_userId: {
                            sProviderId: provider.id,
                            userId: args.customerUserId
                        }
                    },
                    data: { notes: args.notes },
                    include: {
                        customer: true
                    }
                });
            }

            return {
                ...updatedFolder,
                customer: updatedUser
            };
        });

        return result;

    } catch (error: any) {
        console.error(`Failed to update customer ${args.customerUserId} for provider ${provider.id}:`, error);
        if (error instanceof HttpError) {
            throw error;
        }
        if (error.code === 'P2002') {
            const target = error.meta?.target;
            if (target?.includes('mobileNumber')) {
                throw new HttpError(409, "A user with this mobile number already exists.");
            } else if (target?.includes('email')) {
                throw new HttpError(409, "A user with this email address already exists.");
            }
        }
        throw new HttpError(500, error.message || 'Failed to update customer.');
    }
};

// --- Soft Delete Provider Customer Action ---

const softDeleteProviderCustomerInputSchema = z.object({
    customerUserId: z.string().uuid("Invalid Customer User ID format"),
});

type SoftDeleteProviderCustomerData = z.infer<typeof softDeleteProviderCustomerInputSchema>;

export const softDeleteProviderCustomer = async (rawArgs: SoftDeleteProviderCustomerData, context: any): Promise<any> => {
    if (!context.user || !context.user.id) {
        throw new HttpError(401, "User not authenticated");
    }

    const providerUserId = context.user.id;
    const args = ensureArgsSchemaOrThrowHttpError(softDeleteProviderCustomerInputSchema, rawArgs);

    // 1. Find the SProvider ID for the logged-in user
    const provider = await context.entities.SProvider.findUnique({
        where: { userId: providerUserId },
        select: { id: true },
    });

    if (!provider) {
        throw new HttpError(403, "User is not a registered service provider.");
    }

    try {
        // 2. Find the CustomerFolder for this provider and customer
        const customerFolder = await context.entities.CustomerFolder.findUnique({
            where: {
                sProviderId_userId: {
                    sProviderId: provider.id,
                    userId: args.customerUserId
                }
            },
            include: {
                customer: true
            }
        });

        if (!customerFolder) {
            throw new HttpError(404, "Customer not found for this provider.");
        }

        if (customerFolder.deleted) {
            throw new HttpError(409, "Customer is already deleted.");
        }

        // 3. Soft delete the customer folder
        const updatedFolder = await context.entities.CustomerFolder.update({
            where: {
                sProviderId_userId: {
                    sProviderId: provider.id,
                    userId: args.customerUserId
                }
            },
            data: {
                deleted: true,
                updatedAt: new Date()
            },
            include: {
                customer: true
            }
        });

        return updatedFolder;

    } catch (error: any) {
        console.error(`Failed to soft delete customer ${args.customerUserId} for provider ${provider.id}:`, error);
        if (error instanceof HttpError) {
            throw error;
        }
        throw new HttpError(500, error.message || 'Failed to delete customer.');
    }
};

// --- Restore Provider Customer Action ---

const restoreProviderCustomerInputSchema = z.object({
    email: z.string().email("Invalid email format").optional(),
    mobileNumber: z.string().min(1, "Mobile number is required").optional(),
    nationalId: z.string().optional(),
}).refine(data => data.email || data.mobileNumber || data.nationalId, {
    message: "At least one identifier (email, mobileNumber, or nationalId) is required",
    path: ["email"], // Path of error
});

type RestoreProviderCustomerData = z.infer<typeof restoreProviderCustomerInputSchema>;

export const restoreProviderCustomer = async (rawArgs: RestoreProviderCustomerData, context: any): Promise<any> => {
    if (!context.user || !context.user.id) {
        throw new HttpError(401, "User not authenticated");
    }

    const providerUserId = context.user.id;
    const args = ensureArgsSchemaOrThrowHttpError(restoreProviderCustomerInputSchema, rawArgs);

    // 1. Find the SProvider ID for the logged-in user
    const provider = await context.entities.SProvider.findUnique({
        where: { userId: providerUserId },
        select: { id: true },
    });

    if (!provider) {
        throw new HttpError(403, "User is not a registered service provider.");
    }

    try {
        // 2. Find the customer user by the provided identifiers
        const orConditions: any[] = [];

        if (args.email) {
            orConditions.push({ email: args.email });
        }
        if (args.mobileNumber) {
            orConditions.push({ mobileNumber: args.mobileNumber });
        }
        if (args.nationalId) {
            orConditions.push({ nationalId: args.nationalId });
        }

        const customerUser = await context.entities.User.findFirst({
            where: {
                AND: [
                    { role: 'CUSTOMER' },
                    { OR: orConditions }
                ]
            }
        });

        if (!customerUser) {
            throw new HttpError(404, "Customer not found with the provided identifiers.");
        }

        // 3. Find the CustomerFolder for this provider and customer (including deleted ones)
        const customerFolder = await context.entities.CustomerFolder.findUnique({
            where: {
                sProviderId_userId: {
                    sProviderId: provider.id,
                    userId: customerUser.id
                }
            },
            include: {
                customer: true
            }
        });

        if (!customerFolder) {
            throw new HttpError(404, "Customer folder not found for this provider.");
        }

        if (!customerFolder.deleted) {
            throw new HttpError(409, "Customer is not deleted and doesn't need to be restored.");
        }

        // 4. Restore the customer folder
        const updatedFolder = await context.entities.CustomerFolder.update({
            where: {
                sProviderId_userId: {
                    sProviderId: provider.id,
                    userId: customerUser.id
                }
            },
            data: {
                deleted: false,
                updatedAt: new Date()
            },
            include: {
                customer: true
            }
        });

        return updatedFolder;

    } catch (error: any) {
        console.error(`Failed to restore customer for provider ${provider.id}:`, error);
        if (error instanceof HttpError) {
            throw error;
        }
        throw new HttpError(500, error.message || 'Failed to restore customer.');
    }
};

// --- Create Appointment Action ---

const createAppointmentInputSchema = z.object({
    customerUserId: z.string().uuid("Invalid Customer User ID format"), 
    serviceId: z.number().int().positive("Invalid Service ID"),
    placeId: z.number().int().positive("Invalid Place ID"),
    queueId: z.number().int().positive("Queue ID is required"), // ADDED queueId
    startTime: z.date({ coerce: true }), // Coerce string/number to Date
    endTime: z.date({ coerce: true }),   // Coerce string/number to Date
    notes: z.string().optional().nullable(),
}).refine(data => data.endTime > data.startTime, {
    message: "End time must be after start time",
    path: ["endTime"], // Path of error
});

type CreateAppointmentData = z.infer<typeof createAppointmentInputSchema>;

// Adjust context type if needed (Wasp usually handles this)
type ProviderContext = {
  user?: Partial<import('wasp/entities').User> & { id: string, credits?: number }; 
  entities: {
    Appointment: any;
    CustomerFolder: any;
    Service: any;
    SProvidingPlace: any;
    User: any; // Need User entity for updates
    SProvider: any; // Need SProvider entity
    Queue: any; // ADDED Queue entity access
    QueueOpening: any; // ADDED QueueOpening for validation
    QueueOpeningHours: any; // ADDED QueueOpeningHours for validation
  };
};

export const createAppointment: CreateAppointment<CreateAppointmentData, Appointment> = async (
  rawArgs,
    context: any // Use any for context as temporary fix
) => {
    if (!context.user?.id || typeof context.user.credits !== 'number') {
        throw new HttpError(401, "User not authenticated or credit data missing.");
    }
    const providerUserId = context.user.id;
    const providerCredits = context.user.credits;

    const args = ensureArgsSchemaOrThrowHttpError(createAppointmentInputSchema, rawArgs);
      console.log(`[createAppointment Provider] User: ${providerUserId} booking for Customer: ${args.customerUserId} on Queue: ${args.queueId}`);

    const PROVIDER_CREDIT_COST = 1; // Define the cost for the provider per booking

    if (providerCredits < PROVIDER_CREDIT_COST) {
        throw new HttpError(402, `Provider has insufficient credits to create appointment. Required: ${PROVIDER_CREDIT_COST}`);
    }

    try {
        const newAppointment = await prisma.$transaction(async (tx) => {
              // 1. Get Provider SProvider ID & Verify Place Ownership (implicitly done by queue check)
            const provider = await tx.sProvider.findUnique({
                where: { userId: providerUserId },
                select: { id: true },
            });
              if (!provider) throw new HttpError(403, "User is not a registered service provider.");
  
              console.log("queue where clause", {
                id: args.queueId,
                sProvidingPlaceId: args.placeId,
                isActive: true,
               });
              // 2. Validate Queue:
              //    - Belongs to the correct SProvidingPlace
              //    - Is active
              //    - Offers the specified Service
              const queue = await tx.queue.findFirst({
                  where: {
                      id: args.queueId,
                      sProvidingPlaceId: args.placeId,
                      isActive: true,
                    //   services: { some: { id: args.serviceId } }
                  },
                  select: { id: true, sProvidingPlace: { select: { timezone: true } } } // Need timezone for opening validation
              });
              if (!queue) {
                  throw new HttpError(400, "Invalid Queue selected: Does not exist, is inactive, doesn't belong to the place, or doesn't offer the selected service.");
              }
              const queueTimezone = queue.sProvidingPlace.timezone || 'UTC';
  
              // 3. Verify Service belongs to Provider (redundant if queue check passes, but keep for safety)
            const service = await tx.service.findFirst({
                where: { id: args.serviceId, sProviderId: provider.id },
                select: { id: true, duration: true }
            });
              if (!service) throw new HttpError(400, "Selected service is invalid for this provider.");
  
              // 4. Validate Time Slot against Queue Openings
              // This requires fetching QueueOpeningHours for the specific queue, day, and checking if the slot fits.
              // Similar logic as in getQueueAvailability but focused on a single slot.
              const startDayjs = dayjs.utc(args.startTime);
              const endDayjs = dayjs.utc(args.endTime);
              const dayOfWeek = startDayjs.format('dddd'); // UTC day name
  
              const queueOpeningsForDay = await tx.queueOpening.findMany({
                  where: {
                      queueId: args.queueId,
                      dayOfWeek: dayOfWeek,
                      type: 'regular',
                      isActive: true,
                  },
                  include: { hours: true }
              });
  
              let isSlotValid = false;
              for (const opening of queueOpeningsForDay) {
                  for (const hours of opening.hours) {
                      // Convert opening times to UTC based on queue's timezone for the specific date
                      const isoDateString = startDayjs.format('YYYY-MM-DD');
                      const openingStartTz = dayjs.tz(`${isoDateString}T${hours.timeFrom}:00`, queueTimezone).utc();
                      const openingEndTz = dayjs.tz(`${isoDateString}T${hours.timeTo}:00`, queueTimezone).utc();
  
                      // Check if the appointment slot [startTime, endTime) is fully contained within [openingStart, openingEnd)
                      if (startDayjs.isSameOrAfter(openingStartTz) && endDayjs.isSameOrBefore(openingEndTz)) {
                          isSlotValid = true;
                          break;
                      }
                  }
                  if (isSlotValid) break;
              }
  
              if (!isSlotValid) {
                  throw new HttpError(400, `The selected time slot [${args.startTime.toISOString()} - ${args.endTime.toISOString()}] is outside the queue's opening hours for ${dayOfWeek}.`);
              }
  
              // 5. Find Customer Folder
            const customerFolder = await tx.customerFolder.findUnique({
                where: { sProviderId_userId: { sProviderId: provider.id, userId: args.customerUserId } },
                select: { id: true }
            });
              if (!customerFolder) throw new HttpError(404, "Customer record not found for this provider.");
  
              // 6. Check for Overlapping Appointments *on this specific queue*
              const overlappingAppointments = await tx.appointment.findMany({
                  where: {
                      queueId: args.queueId, // Check only this queue
                      status: { notIn: ['canceled', 'noshow'] },
                      expectedAppointmentStartTime: { lt: args.endTime },
                      expectedAppointmentEndTime: { gt: args.startTime },
                  },
                  select: { id: true }
              });
              if (overlappingAppointments.length > 0) {
                  throw new HttpError(409, "This time slot is already booked on the selected queue.");
              }

            // --- Perform Updates --- 
              // 7. Decrement Provider Credits
            await tx.user.update({
                where: { id: providerUserId },
                data: { credits: { decrement: PROVIDER_CREDIT_COST } }
            });
            
              // 8. Create the Appointment, linking to queueId
            const createdAppointment = await tx.appointment.create({
                data: {
                    customerFolderId: customerFolder.id,
                      placeId: args.placeId,
                    serviceId: service.id,
                      queueId: args.queueId, // SAVE the queueId
                    expectedAppointmentStartTime: args.startTime,
                    expectedAppointmentEndTime: args.endTime,
                    serviceDuration: service.duration,
                    notes: args.notes,
                    status: 'confirmed', // Provider bookings are often confirmed by default
                },
                include: { // Include all data needed for notifications
                    service: { select: { title: true } },
                    place: { select: { name: true } },
                    customerFolder: {
                      select: {
                        userId: true, // Customer's User ID
                        provider: { // SProvider
                          select: {
                            userId: true, // Provider's User ID
                            title: true, // Provider's business name/title
                          }
                        }
                      }
                    },
                    // Potentially include queue title if needed for messages
                    // queue: { select: { title: true } },
                  }
            });

            if (!createdAppointment.customerFolder?.userId || !createdAppointment.customerFolder.provider?.userId) {
                console.error("Missing customer or provider user ID for new appointment. Skipping notifications.");
                // Potentially throw an error if this is critical
              } else {
                const serviceTitle = createdAppointment.service?.title || 'the selected service';
                const providerName = createdAppointment.customerFolder.provider?.title || 'the provider';
                const appointmentDateTime = createdAppointment.expectedAppointmentStartTime
                  ? new Date(createdAppointment.expectedAppointmentStartTime).toLocaleString([], { dateStyle: 'medium', timeStyle: 'short' })
                  : 'the scheduled time';
                const placeName = createdAppointment.place?.name || 'the clinic'; // if you include place
        
                // To get the customer's name for the provider's notification:
                // const customerUser = await tx.user.findUnique({ where: {id: createdAppointment.customerFolder.userId }});
                // const customerDisplayName = customerUser?.firstName ? `${customerUser.firstName} ${customerUser.lastName || ''}`.trim() : 'A customer';
        
                // Notification for the Customer
                await createNotificationEntry(tx.notification, {
                  userId: createdAppointment.customerFolder.userId,
                  type: 'APPOINTMENT_BOOKED_SUCCESS_CUSTOMER', // Or APPOINTMENT_CONFIRMED_CUSTOMER if status is 'confirmed'
                  title: 'Appointment Booked Successfully', // Or 'Appointment Confirmed'
                  message: `Your appointment for ${serviceTitle} with ${providerName} at ${placeName} on ${appointmentDateTime} has been successfully booked.`,
                  link: `/my-appointments/${createdAppointment.id}`, // Link to the specific appointment details
                  actorId: context.user.id, // User who booked (could be provider staff or customer if self-booking)
                });
                if(context.user.id !== createdAppointment.customerFolder.provider.userId)
                    await createNotificationEntry(tx.notification, {
                    userId: createdAppointment.customerFolder.provider.userId,
                    type: 'NEW_APPOINTMENT_BOOKED_PROVIDER',
                    title: 'New Appointment Booked',
                    message: `New appointment: A customer has booked ${serviceTitle} at ${placeName} on ${appointmentDateTime}.`,
                    // Consider adding customer name: `message: New appointment: ${customerDisplayName} has booked...`
                    link: `/admin/appointments?appointmentId=${createdAppointment.id}`, // Link to provider's dashboard, perhaps highlighting new one
                    actorId: context.user.id, // User who performed the booking action
                    });
              }
            
            console.log(`Appointment ${createdAppointment.id} created by provider ${providerUserId} on Queue ${args.queueId}. Deducted ${PROVIDER_CREDIT_COST} provider credits.`);
            return createdAppointment;
        });

        return newAppointment;

    } catch (error: any) {
        console.error(`[createAppointment Provider] Transaction failed for provider ${providerUserId}:`, error);
        if (error instanceof HttpError) {
            throw error;
        }
        throw new HttpError(500, error.message || 'Failed to create appointment.');
    }
};

// --- Get Appointments Query ---

export const getAppointments: GetAppointments<void, Appointment[]> = async (_args: void, context: any) => { // Use `any` for context until Wasp injects proper type
    if (!context.user?.id) { // Added null/undefined check for user
       throw new HttpError(401, "User not authenticated");
    }
  
  const provider = await context.entities.SProvider.findUnique({
      where: { userId: context.user.id }, // Use non-null assertion as we checked above
    select: { id: true },
  });

  if (!provider) {
    throw new HttpError(404, 'Provider profile not found for this user.');
  }
  const providerId = provider.id;

  return context.entities.Appointment.findMany({
    where: {
      customerFolder: {
        sProviderId: providerId,
      },
      // Add more filters if needed, e.g., date range
    },
    include: {
      service: {
        include: {
          provider: {
            include: {
              user: {
                include: {
                  profilePicture: true // Include user's profile picture
                }
              },
              logo: true, // Include provider's logo
              category: true // Include provider category
            }
          }
        }
      },
      customerFolder: {
        select: { // Select specific fields from CustomerFolder
          userId: true, // Include the userId directly
          customer: { // Include customer details nested within the folder
            select: {
              id: true,
              firstName: true,
              lastName: true,
              profilePicture: true // Include customer's profile picture
            }
          }
        }
      },
      place: { // Select specific fields from Place
        select: { id: true, name: true }
        },
        queue: { // INCLUDE Queue details
            select: { id: true, title: true }
      }
    },
    orderBy: {
      expectedAppointmentStartTime: 'asc',
    },
  });
};


// Interface for the completed appointment data needed by the shift handler
interface CompletedAppointmentForShift {
    id: number;
    queueId: number;
    service: { duration: number };
    realAppointmentStartTime: Date;
    realAppointmentEndTime: Date;
    expectedAppointmentStartTime: Date; // Used to scope subsequent appointments to the same day
    expectedAppointmentEndTime: Date; // Used to find appointments after this one
    // Add other fields if necessary for history or other logic
    placeId: number;
    serviceId: number;
    status: string; // Should be 'completed' when passed
    notes?: string | null;
    type: string;
}

async function handleAppointmentShift(
    tx: Prisma.TransactionClient, // Prisma.TransactionClient type
    completedAppointment: CompletedAppointmentForShift,
    changerUserId: string
): Promise<void> {
    if (!completedAppointment.realAppointmentStartTime || !completedAppointment.realAppointmentEndTime || !completedAppointment.queueId || !completedAppointment.service || !completedAppointment.type) {
        console.log('completedAppointment', completedAppointment);
        console.warn(`[handleAppointmentShift] Cannot calculate shift for appointment ${completedAppointment.id} due to missing critical data (real start/end time, queueId, or service, or type).`);
        return;
    }
    let timeShiftMinutes = 0;
    let actualDuration = 0;
    let expectedDuration = 0;

    if(completedAppointment.type === 'started') {
        timeShiftMinutes = dayjs(completedAppointment.realAppointmentStartTime).diff(dayjs(completedAppointment.expectedAppointmentStartTime), 'minute');
        actualDuration = dayjs(completedAppointment.realAppointmentEndTime).diff(dayjs(completedAppointment.realAppointmentStartTime), 'minute');
        expectedDuration = completedAppointment.service.duration;
    } else if(completedAppointment.type === 'completed') {
        timeShiftMinutes = dayjs(completedAppointment.realAppointmentEndTime).diff(dayjs(completedAppointment.expectedAppointmentEndTime), 'minute');
        actualDuration = dayjs(completedAppointment.realAppointmentEndTime).diff(dayjs(completedAppointment.realAppointmentStartTime), 'minute');
        expectedDuration = completedAppointment.service.duration;
    } else if(completedAppointment.type === 'extended') {
        const appointmentHistory = await tx.appointmentHistory.findFirst({
            where: {
                appointmentId: completedAppointment.id,
                changeReason: 'extended',
            },
            orderBy: {
                createdAt: 'desc'
            }
        });
        
        if(!appointmentHistory) {
            console.warn(`[handleAppointmentShift] No appointment history found for appointment ${completedAppointment.id}.`);
            return;
        }
        timeShiftMinutes = dayjs(appointmentHistory.newEndTime).diff(dayjs(appointmentHistory.previousEndTime), 'minute');
        actualDuration = dayjs(appointmentHistory.newEndTime).diff(dayjs(appointmentHistory.previousEndTime), 'minute');
        expectedDuration = completedAppointment.service.duration;
        // For extended appointments, we only care about the difference between new and old end times
        // timeShiftMinutes = dayjs(completedAppointment.realAppointmentEndTime).diff(dayjs(completedAppointment.expectedAppointmentEndTime), 'minute');
        // actualDuration = dayjs(completedAppointment.realAppointmentEndTime).diff(dayjs(completedAppointment.realAppointmentStartTime), 'minute');
        // expectedDuration = completedAppointment.service.duration;
        console.log(`[handleAppointmentShift] Appointment ${completedAppointment.id} extended by ${timeShiftMinutes} minutes.`);
    } else {
        console.warn(`[handleAppointmentShift] Cannot calculate shift for appointment ${completedAppointment.id} due to missing critical data (real start/end time, queueId, or service, or type).`);
        return;
    }
    // const timeShiftMinutes = dayjs(completedAppointment.realAppointmentEndTime).diff(dayjs(completedAppointment.expectedAppointmentEndTime), 'minute');
    // const actualDuration = dayjs(completedAppointment.realAppointmentEndTime).diff(dayjs(completedAppointment.realAppointmentStartTime), 'minute');
    // const expectedDuration = completedAppointment.service.duration;
    
    if (timeShiftMinutes === 0) {
        console.log(`[handleAppointmentShift] No time shift needed for appointments after ${completedAppointment.id} in queue ${completedAppointment.queueId}.`);
        return;
    }

    if(timeShiftMinutes > 0) {
        console.log(`[handleAppointmentShift] Appointment ${completedAppointment.id} (Queue ${completedAppointment.queueId}) completed. Actual: ${actualDuration}min, Expected: ${expectedDuration}min. Initiating shift of ${timeShiftMinutes}min.`);
    }

    console.log(`[handleAppointmentShift] Appointment ${completedAppointment.id} (Queue ${completedAppointment.queueId}) completed. Actual: ${actualDuration}min, Expected: ${expectedDuration}min. Initiating shift of ${timeShiftMinutes}min.`);

    const dayOfCompletedAppointment = dayjs.utc(completedAppointment.expectedAppointmentStartTime);

    const queue = await tx.queue.findFirst({
        where: {
            id: completedAppointment.queueId,
            sProvidingPlaceId: completedAppointment.placeId,
            isActive: true,
          //   services: { some: { id: args.serviceId } }
        },
        select: { id: true, sProvidingPlace: { select: { timezone: true } } } // Need timezone for opening validation
    });
    
    if (!queue) {
        throw new HttpError(400, "Invalid Queue selected: Does not exist, is inactive, doesn't belong to the place, or doesn't offer the selected service.");
    }

    const queueTimezone = queue.sProvidingPlace.timezone || 'UTC';

    const startDayjs = dayjs.utc(completedAppointment.expectedAppointmentStartTime);
    const endDayjs = dayjs.utc(completedAppointment.expectedAppointmentEndTime);
    const dayOfWeek = startDayjs.format('dddd'); // UTC day name

    const queueOpeningsForDay = await tx.queueOpening.findMany({
        where: {
            queueId: completedAppointment.queueId,
            dayOfWeek: dayOfWeek,
            type: 'regular',
            isActive: true,
        },
        include: { hours: true }
    });

    let whereExpectedAppointmentStartTime = {};
    let isSlotValid = false;
    for (const opening of queueOpeningsForDay) {
        for (const hours of opening.hours) {
            const isoDateString = startDayjs.format('YYYY-MM-DD');
            const openingStartTz = dayjs.tz(`${isoDateString}T${hours.timeFrom}:00`, queueTimezone).utc();
            const openingEndTz = dayjs.tz(`${isoDateString}T${hours.timeTo}:00`, queueTimezone).utc();

            // Check if the appointment slot [startTime, endTime) is fully contained within [openingStart, openingEnd)
            if (startDayjs.isSameOrAfter(openingStartTz) && endDayjs.isSameOrBefore(openingEndTz)) {
                isSlotValid = true;
                whereExpectedAppointmentStartTime = {
                    gte: openingStartTz,
                    lt: openingEndTz,
                };
                break;
            }
        }
        if (isSlotValid) break;
    }

    // Fetch subsequent appointments in the same queue on the same day
    const subsequentAppointments = await tx.appointment.findMany({
        where: {
            queueId: completedAppointment.queueId,
            status: { in: ['pending', 'confirmed'] }, // Only shift active, non-started appointments    
            expectedAppointmentStartTime: {
                // gt: completedAppointment.expectedAppointmentEndTime, // Strictly after the one just completed
                // gte: dayOfCompletedAppointment.startOf('day').toDate(),
                lt: dayOfCompletedAppointment.endOf('day').toDate(),
                ...whereExpectedAppointmentStartTime
            },
            id: { not: completedAppointment.id }
        },
        orderBy: { expectedAppointmentStartTime: 'asc' },
        include: {
            customerFolder: {
                select: {
                    userId: true,
                    provider: {
                        select: {
                            userId: true
                        }
                    }
                }
            },
            service: {
                select: {
                    duration: true,
                    title: true
                }
            },
            place: {
                select: {
                    name: true
                }
            },
        },
        // Select all fields needed for history or further processing
        // include: { service: { select: { duration: true } } } // Not strictly needed if we just shift times
    });

    if (subsequentAppointments.length === 0) {
        console.log(`[handleAppointmentShift] No subsequent appointments to shift for appointment ${completedAppointment.id} in queue ${completedAppointment.queueId} on ${dayOfCompletedAppointment.format('YYYY-MM-DD')}.`);
        return;
    }
    console.log(`[handleAppointmentShift] Found ${subsequentAppointments.length} subsequent appointments to shift.`);

    if(timeShiftMinutes < 0 && completedAppointment.type === 'completed') {
        console.log(`[handleAppointmentShift] Appointment ${completedAppointment.id} (Queue ${completedAppointment.queueId}) completed. Actual: ${actualDuration}min, Expected: ${expectedDuration}min. Initiating shift of ${timeShiftMinutes}min.`);
        return;
    }

    // After getting queue and timezone info, add a helper function to check if a time slot is within opening hours
    const isTimeSlotWithinOpeningHours = async (
        startTime: dayjs.Dayjs,
        endTime: dayjs.Dayjs,
        queueId: number,
        queueTimezone: string
    ): Promise<boolean> => {
        const dayOfWeek = startTime.format('dddd');
        const queueOpenings = await tx.queueOpening.findMany({
            where: {
                queueId: queueId,
                dayOfWeek: dayOfWeek,
                type: 'regular',
                isActive: true,
            },
            include: { hours: true }
        });

        for (const opening of queueOpenings) {
            for (const hours of opening.hours) {
                const isoDateString = startTime.format('YYYY-MM-DD');
                const openingStartTz = dayjs.tz(`${isoDateString}T${hours.timeFrom}:00`, queueTimezone).utc();
                const openingEndTz = dayjs.tz(`${isoDateString}T${hours.timeTo}:00`, queueTimezone).utc();

                if (startTime.isSameOrAfter(openingStartTz) && endTime.isSameOrBefore(openingEndTz)) {
                    return true;
                }
            }
        }
        return false;
    };

    let timeleftToshift = timeShiftMinutes;
    let prevDude = {
        expectedAppointmentStartTime: completedAppointment.expectedAppointmentEndTime,
        expectedAppointmentEndTime: completedAppointment.expectedAppointmentEndTime,
    }

    const isWithinHours = await isTimeSlotWithinOpeningHours(
        dayjs(completedAppointment.realAppointmentStartTime),
        dayjs(completedAppointment.realAppointmentEndTime),
        completedAppointment.queueId,
        queueTimezone
    );

    await tx.appointment.update({
        where: { id: completedAppointment.id },
        data: {
            expectedAppointmentStartTime: completedAppointment.realAppointmentStartTime,
            expectedAppointmentEndTime: completedAppointment.realAppointmentEndTime,
            // Mark as overflowed if outside opening hours
            isOverflowed: !isWithinHours,
            ...((!isWithinHours) ? {
                overflowReason: 'shifted_beyond_hours',
                overflowProcessingStatus: 'pending',
                overflowDetectedAt: new Date()
            } : {})
        },
    });
    if(timeShiftMinutes > 0){
        for (const appt of subsequentAppointments) {

            const previousStartTime = appt.expectedAppointmentStartTime;
            const previousEndTime = appt.expectedAppointmentEndTime;

            const diff = dayjs(appt.expectedAppointmentStartTime).diff(dayjs(prevDude.expectedAppointmentEndTime), 'minute');
            console.log(`[handleAppointmentShift] Diff: ${diff} minutes. Time left to shift: ${timeleftToshift} minutes.`);
            if(diff > 0) {
                timeleftToshift -= diff;
            }

            if(timeleftToshift <= 0) {
                console.log(`[handleAppointmentShift] Time left to shift is 0. Breaking.`);
                break;
            }

            const newStartTime = dayjs(appt.expectedAppointmentStartTime).add(timeleftToshift, 'minutes');
            const newEndTime = dayjs(appt.expectedAppointmentEndTime).add(timeleftToshift, 'minutes');

            // Check if the new time slot would be outside opening hours
            const isWithinHours = await isTimeSlotWithinOpeningHours(
                newStartTime,
                newEndTime,
                completedAppointment.queueId,
                queueTimezone
            );

            prevDude = {
                expectedAppointmentStartTime: appt.expectedAppointmentStartTime!,
                expectedAppointmentEndTime: appt.expectedAppointmentEndTime!,
            }
            // Update the appointment with overflow status if needed
            await tx.appointment.update({
                where: { id: appt.id },
                data: {
                    expectedAppointmentStartTime: newStartTime.toDate(),
                    expectedAppointmentEndTime: newEndTime.toDate(),
                    // Mark as overflowed if outside opening hours
                    isOverflowed: !isWithinHours,
                    ...((!isWithinHours) ? {
                        overflowReason: 'shifted_beyond_hours',
                        overflowProcessingStatus: 'pending',
                        overflowDetectedAt: new Date()
                    } : {})
                },
            });

            // Create history record
            await tx.appointmentHistory.create({
                data: {
                    appointmentId: appt.id,
                    changedByUserId: changerUserId,
                    previousStartTime: previousStartTime!,
                    previousEndTime: previousEndTime!,
                    newStartTime: newStartTime.toDate(),
                    newEndTime: newEndTime.toDate(),
                    previousStatus: appt.status,
                    newStatus: appt.status,
                    previousMotifId: appt.serviceId,
                    newMotifId: appt.serviceId,
                    previousAgendaId: appt.queueId ?? 0,
                    newAgendaId: appt.queueId ?? 0,
                    changeReason: !isWithinHours 
                        ? `Automatic time shift by ${timeShiftMinutes}min due to completion of appointment ${completedAppointment.id}. Appointment now outside opening hours.`
                        : `Automatic time shift by ${timeShiftMinutes}min due to completion of appointment ${completedAppointment.id}.`,
                },
            });
            const customerUserId = appt.customerFolder?.userId;
            // const providerUserId = appt.customerFolder?.provider?.userId;
            // const serviceTitle = appt.service?.title;
            // const placeName = appt.place?.name;
            // Send notifications
            const notificationType = 'APPOINTMENT_SHIFTED_DUE_TO_DELAY';
            const notificationTitle = 'Appointment for ${serviceTitle} at ${placeName} Shifted Due to Delay';
            const notificationMessage = `Your appointment has been shifted with ${timeleftToshift} minutes due to a delay. Please check the new time and reschedule if necessary.`;
            const notificationLink = `/my-appointments/${appt.id}`;
            if (notificationType && notificationTitle && notificationMessage) {
                await createNotificationEntry(tx.notification, {
                    userId: customerUserId,
                    type: notificationType,
                    title: notificationTitle,
                    message: notificationMessage,
                    link: notificationLink,
                    actorId: appt.customerFolder?.provider?.userId, // The provider/admin who made the update
                });
            }

            console.log(`[handleAppointmentShift] Shifted appointment ${appt.id} from ${previousStartTime?.toISOString()} to ${newStartTime.toDate().toISOString()}. ${!isWithinHours ? 'OVERFLOW DETECTED' : ''}`);
        }
    }
}

// --- Extend Appointment Action ---

const extendAppointmentInputSchema = z.object({
    appointmentId: z.number().int().positive(),
    extensionMinutes: z.number().int().positive().max(60, "Extension cannot exceed 60 minutes"),
});

type ExtendAppointmentData = z.infer<typeof extendAppointmentInputSchema>;

export const extendAppointment: ExtendAppointment<ExtendAppointmentData, Appointment> = async (
    rawArgs: ExtendAppointmentData,
    context: any
): Promise<Appointment> => {
    if (!context.user?.id) {
        throw new HttpError(401, "User not authenticated");
    }
    const providerUserId = context.user.id;
    console.log("extendAppointment", rawArgs);
    const args = ensureArgsSchemaOrThrowHttpError(extendAppointmentInputSchema, rawArgs);
    
    try {
        const extendedAppointmentResult = await prisma.$transaction(async (tx) => {
            // 1. Find the appointment and verify it's in progress
            const appointment = await tx.appointment.findUnique({
                where: { id: args.appointmentId },
                include: {
                    service: { select: { duration: true } },
                    place: { select: { sProviderId: true, provider: { select: { userId: true } } } },
                    customerFolder: { select: { userId: true } },
                    queue: { select: { id: true } }
                }
            });
            
            if (!appointment) {
                throw new HttpError(404, "Appointment not found");
            }
            
            if (appointment.place?.provider?.userId !== providerUserId) {
                throw new HttpError(403, "User not authorized to extend this appointment");
            }
            
            if (appointment.status !== 'InProgress') {
                throw new HttpError(400, "Only in-progress appointments can be extended");
            }
            
            if (!appointment.realAppointmentStartTime) {
                throw new HttpError(400, "Appointment has no recorded start time");
            }
            
            // 2. Calculate new expected end time
            // const currentEndTime = dayjs(appointment.expectedAppointmentEndTime!).isBefore(dayjs()) 
            //     ? new Date() 
            //     : appointment.expectedAppointmentEndTime!;

            const currentEndTime =  new Date();
            const newEndTime = dayjs(currentEndTime).add(args.extensionMinutes, 'minutes').toDate();
            
            // 3. Update the appointment with new end time
            const updatedAppointment = await tx.appointment.update({
                where: { id: args.appointmentId },
                data: {
                    expectedAppointmentEndTime: newEndTime
                },
                include: {
                    service: { select: { duration: true, title: true } },
                    place: { select: { name: true } },
                    customerFolder: { 
                        select: { 
                            userId: true,
                            provider: { select: { userId: true } }
                        }
                    },
                    queue: { select: { id: true } }
                }
            });
            
            // 4. Create history record
            await tx.appointmentHistory.create({
                data: {
                    appointmentId: args.appointmentId,
                    changedByUserId: providerUserId,
                    previousStartTime: appointment.expectedAppointmentStartTime!,
                    previousEndTime: appointment.expectedAppointmentEndTime!,
                    newStartTime: appointment.expectedAppointmentStartTime!,
                    newEndTime: newEndTime,
                    previousStatus: appointment.status,
                    newStatus: appointment.status,
                    previousMotifId: appointment.serviceId,
                    newMotifId: appointment.serviceId,
                    previousAgendaId: appointment.queueId ?? 0,
                    newAgendaId: appointment.queueId ?? 0,
                    changeReason: `extended`
                }
            });
            
            // 5. Notify customer about extension
            const customerUserId = appointment.customerFolder?.userId;
            if (customerUserId) {
                const serviceTitle = updatedAppointment.service?.title || 'your service';
                const placeName = updatedAppointment.place?.name || 'our location';
                
                await createNotificationEntry(tx.notification, {
                    userId: customerUserId,
                    type: 'APPOINTMENT_EXTENDED',
                    title: `Appointment Extended`,
                    message: `Your appointment for ${serviceTitle} at ${placeName} has been extended by ${args.extensionMinutes} minutes.`,
                    link: `/my-appointments/${args.appointmentId}`,
                    actorId: providerUserId
                });
            }
            
            // 6. Handle shifting of subsequent appointments
            await handleAppointmentShift(tx, {
                id: appointment.id,
                queueId: appointment.queueId!,
                service: { duration: appointment.service!.duration },
                realAppointmentStartTime: appointment.realAppointmentStartTime,
                realAppointmentEndTime: newEndTime,
                expectedAppointmentStartTime: appointment.expectedAppointmentStartTime!,
                expectedAppointmentEndTime: newEndTime,
                placeId: appointment.placeId,
                serviceId: appointment.serviceId,
                status: appointment.status,
                notes: appointment.notes,
                type: 'extended' // New type for extended appointments
            }, providerUserId);
            
            return updatedAppointment;
        });
        
        return extendedAppointmentResult;
    } catch (error: any) {
        console.error(`[extendAppointment] Failed for appointment ${args.appointmentId}:`, error);
        if (error instanceof HttpError) {
            throw error;
        }
        throw new HttpError(500, error.message || 'Failed to extend appointment');
    }
};


// --- Update Appointment Action ---

// Update schema to include optional queueId
const updateAppointmentInputSchema = z.object({
    appointmentId: z.number().int().positive(),
    customerUserId: z.string().uuid("Invalid Customer User ID format"),
    serviceId: z.number().int().positive("Invalid Service ID"),
    placeId: z.number().int().positive("Invalid Place ID"),
    queueId: z.number().int().positive("Queue ID must be a positive integer").optional(), // Allow changing queue
    startTime: z.date({ coerce: true }),
    endTime: z.date({ coerce: true }),
    notes: z.string().optional().nullable(),
    status: z.string(), // Status is now required for logic
}).refine(data => data.endTime > data.startTime, {
    message: "End time must be after start time",
    path: ["endTime"],
});

type UpdateAppointmentData = z.infer<typeof updateAppointmentInputSchema>;

// Mapping of current status to allowed next statuses
const allowedStatusTransitions: Record<string, string[]> = {
    pending: ['pending', 'confirmed', 'completed', 'canceled', 'noshow', 'InProgress'],
    confirmed: ['confirmed', 'completed', 'canceled', 'noshow', 'InProgress'],
    InProgress: ['InProgress', 'completed', 'canceled', 'noshow'], // Can update notes, or move to final states
    completed: ['completed'],
    canceled: ['canceled'],
    noshow: ['noshow'],
};

export const updateAppointment: UpdateAppointment<UpdateAppointmentData, Appointment> = async (rawArgs: any, context: any) => { // Use any for context/rawArgs as temporary fix
    if (!context.user?.id) {
        throw new HttpError(401, "User not authenticated");
    }
    const providerUserId = context.user.id;
    const changerUserId = context.user.id;

    const args = ensureArgsSchemaOrThrowHttpError(updateAppointmentInputSchema, rawArgs);
    const newStatus = args.status;

    try {
        const updatedAppointmentResult = await prisma.$transaction(async (tx) => {
            // 1. Fetch current appointment details
            const currentAppointment = await tx.appointment.findUnique({
                where: { id: args.appointmentId },
                include: {
                    place: { select: { sProviderId: true, provider: { select: { userId: true }} } },
                    service: { select: { pointsRequirements: true, duration: true } }, // Include duration
                    customerFolder: { select: { userId: true } },
                    queue: { select: { id: true } } // Include current queue ID
                }
            });

            if (!currentAppointment) throw new HttpError(404, "Appointment not found.");
            if (currentAppointment.place?.provider?.userId !== providerUserId) {
                throw new HttpError(403, "User not authorized to update this appointment.");
            }
            if (!currentAppointment.service || !currentAppointment.customerFolder?.userId || !currentAppointment.queueId || !currentAppointment.expectedAppointmentStartTime || !currentAppointment.expectedAppointmentEndTime) {
                throw new HttpError(500, "Appointment data incomplete (missing service, customer link, queue, or expected times).");
            }

            const currentStatus = currentAppointment.status;
            const customerId = currentAppointment.customerFolder.userId;
            const creditsToRefund = currentAppointment.service.pointsRequirements;

            // --- VALIDATIONS ---
            // 2. Status Transition Logic
            if (newStatus !== currentStatus) {
                const allowedTransitions = allowedStatusTransitions[currentStatus] || [];
                if (!allowedTransitions.includes(newStatus)) {
                    throw new HttpError(400, `Invalid status transition from ${currentStatus} to ${newStatus}.`);
                }
            }
            
            // 3. Queue, Service, Place Validation (if changed)
            let targetQueueId = args.queueId ?? currentAppointment.queueId; 
            if (targetQueueId === null || targetQueueId === undefined) {
                 throw new HttpError(400, "Cannot update appointment: Target Queue ID is missing.");
            }

            const queueData = await tx.queue.findFirst({
                where: { id: targetQueueId, sProvidingPlaceId: args.placeId, isActive: true },
                select: { id: true, sProvidingPlace: { select: { timezone: true } } }
            });
            if (!queueData) {
                throw new HttpError(400, "Invalid target Queue, Place, or Service combination: Queue might be inactive, not belong to the place, or not offer the service.");
            }
            const queueTimezone = queueData.sProvidingPlace.timezone || 'UTC';

            // 4. Time Slot Validation against Target Queue Openings (if time or queue changed)
            const timeOrQueueChanged = args.startTime.toISOString() !== currentAppointment.expectedAppointmentStartTime?.toISOString() ||
                                       args.endTime.toISOString() !== currentAppointment.expectedAppointmentEndTime?.toISOString() ||
                                       (args.queueId !== undefined && args.queueId !== currentAppointment.queueId);

            if (timeOrQueueChanged && (newStatus === 'pending' || newStatus === 'confirmed' || newStatus === 'InProgress')) { // Only validate for non-final states
                const startDayjs = dayjs.utc(args.startTime);
                const endDayjs = dayjs.utc(args.endTime);
                const dayOfWeek = startDayjs.format('dddd');

                const queueOpeningsForDay = await tx.queueOpening.findMany({
                    where: { queueId: targetQueueId, dayOfWeek: dayOfWeek, type: 'regular', isActive: true },
                    include: { hours: true }
                });

                let isSlotValid = false;
                for (const opening of queueOpeningsForDay) {
                    for (const hours of opening.hours) {
                        const isoDateString = startDayjs.format('YYYY-MM-DD');
                        const openingStartTz = dayjs.tz(`${isoDateString}T${hours.timeFrom}:00`, queueTimezone).utc();
                        const openingEndTz = dayjs.tz(`${isoDateString}T${hours.timeTo}:00`, queueTimezone).utc();
                        if (startDayjs.isSameOrAfter(openingStartTz) && endDayjs.isSameOrBefore(openingEndTz)) {
                            isSlotValid = true;
                            break;
                        }
                    }
                    if (isSlotValid) break;
                }
                if (!isSlotValid) {
                    throw new HttpError(400, `The selected time slot is outside the target queue\'s opening hours for ${dayOfWeek}.`);
                }

                // 5. Conflict Check on Target Queue (if time or queue changed)
                const overlappingAppointments = await tx.appointment.findMany({
                    where: {
                        id: { not: args.appointmentId }, // Exclude self
                        queueId: targetQueueId,
                        status: { notIn: ['canceled', 'noshow', 'completed'] }, // Consider InProgress as conflicting too
                        expectedAppointmentStartTime: { lt: args.endTime },
                        expectedAppointmentEndTime: { gt: args.startTime },
                    },
                    select: { id: true }
                });
                if (overlappingAppointments.length > 0) {
                    throw new HttpError(409, "This time slot is already booked on the target queue.");
                }
            }

            // --- PERFORM UPDATES ---
            let appointmentStatusUpdate: Partial<Appointment> & { realAppointmentStartTime?: Date, realAppointmentEndTime?: Date } = {};
            let shiftDataForPrevious: CompletedAppointmentForShift | null = null;
            let shiftDataForCurrent: CompletedAppointmentForShift | null = null;
            let shiftDataForLateStart: CompletedAppointmentForShift | null = null; // ADDED: For initial late start shift
            
            // Logic for "InProgress" and "completed" status changes
            if (newStatus === 'InProgress' && currentStatus !== 'InProgress') {
                const realStartTimeForInProgress = new Date(); // Capture the exact moment
                appointmentStatusUpdate.realAppointmentStartTime = realStartTimeForInProgress;
                appointmentStatusUpdate.expectedAppointmentStartTime = realStartTimeForInProgress;
                appointmentStatusUpdate.expectedAppointmentEndTime = dayjs(appointmentStatusUpdate.realAppointmentStartTime!).add(currentAppointment.service.duration, 'minutes').toDate();
                console.log(`[updateAppointment] Appointment ${args.appointmentId} moving to InProgress. Actual start time: ${appointmentStatusUpdate.realAppointmentStartTime.toISOString()}`);

                // Calculate initial delay if started late
                // const initialDelayMinutes = dayjs(realStartTimeForInProgress).diff(dayjs(currentAppointment.expectedAppointmentStartTime), 'minute');
                // if (initialDelayMinutes > 0) {
                //     console.log(`[updateAppointment] Appt ${args.appointmentId} starting InProgress ${initialDelayMinutes} mins late. Preparing initial shift.`);
                    
                // }

                shiftDataForLateStart = {
                    id: currentAppointment.id,
                    queueId: currentAppointment.queueId!,
                    service: { duration: currentAppointment.service.duration },
                    // These times are crafted so handleAppointmentShift calculates timeShiftMinutes = initialDelayMinutes
                    realAppointmentStartTime: appointmentStatusUpdate.realAppointmentStartTime!, // Use original expected start for context
                    realAppointmentEndTime: dayjs(appointmentStatusUpdate.realAppointmentStartTime!).add(currentAppointment.service.duration, 'minutes').toDate(), // Original expected end + delay
                    expectedAppointmentStartTime: currentAppointment.expectedAppointmentStartTime!, // Original expected start
                    expectedAppointmentEndTime: currentAppointment.expectedAppointmentEndTime!,
                    placeId: currentAppointment.placeId,
                    serviceId: currentAppointment.serviceId,
                    status: 'InProgress', // Status it's moving to
                    notes: currentAppointment.notes,
                    type: 'started',
                };

                // Check if a previous appointment in the same queue/day was 'InProgress' and auto-complete it
                const dayOfCurrentAppointment = dayjs.utc(currentAppointment.expectedAppointmentStartTime);
                const previousInProgressAppt = await tx.appointment.findFirst({
                    where: {
                        queueId: currentAppointment.queueId,
                        status: 'InProgress',
                        id: { not: currentAppointment.id },
                        expectedAppointmentStartTime: {
                            lt: currentAppointment.expectedAppointmentStartTime, // Before the current one
                            gte: dayOfCurrentAppointment.startOf('day').toDate(),
                            // lt: dayOfCurrentAppointment.endOf('day').toDate(),
                        }
                    },
                    orderBy: { expectedAppointmentStartTime: 'desc' },
                    include: { service: { select: { duration: true } } }
                });

                if (previousInProgressAppt && previousInProgressAppt.realAppointmentStartTime && previousInProgressAppt.service) {
                    const effectiveEndTimeForPrev = appointmentStatusUpdate.realAppointmentStartTime;
                    await tx.appointment.update({
                        where: { id: previousInProgressAppt.id },
                        data: { status: 'completed', realAppointmentEndTime: effectiveEndTimeForPrev }
                    });
                    await tx.appointmentHistory.create({
                        data: {
                            appointmentId: previousInProgressAppt.id, changedByUserId: changerUserId,
                            previousStatus: 'InProgress', newStatus: 'completed',
                            previousStartTime: previousInProgressAppt.expectedAppointmentStartTime!, newStartTime: previousInProgressAppt.expectedAppointmentStartTime!,
                            previousEndTime: previousInProgressAppt.expectedAppointmentEndTime!, newEndTime: previousInProgressAppt.expectedAppointmentEndTime!,
                            changeReason: `Auto-completed due to next appointment ${args.appointmentId} starting.`,
                            // Fill other history fields as needed
                            previousMotifId: previousInProgressAppt.serviceId, newMotifId: previousInProgressAppt.serviceId,
                            previousAgendaId: previousInProgressAppt.queueId ?? 0,
                            newAgendaId: previousInProgressAppt.queueId ?? 0,
                        }
                    });
                    console.log(`[updateAppointment] Auto-completed previous InProgress appointment ${previousInProgressAppt.id}.`);
                    // shiftDataForPrevious = {
                }
            } else if (newStatus === 'completed' && currentStatus === 'InProgress') {
                appointmentStatusUpdate.realAppointmentEndTime = new Date();
                console.log(`[updateAppointment] Appointment ${args.appointmentId} moving from InProgress to Completed. End time: ${appointmentStatusUpdate.realAppointmentEndTime.toISOString()}`);
                if (currentAppointment.realAppointmentStartTime && currentAppointment.service) {
                     shiftDataForCurrent = {
                        id: currentAppointment.id,
                        queueId: currentAppointment.queueId!,
                        service: { duration: currentAppointment.service.duration },
                        realAppointmentStartTime: currentAppointment.realAppointmentStartTime,
                        realAppointmentEndTime: appointmentStatusUpdate.realAppointmentEndTime,
                        expectedAppointmentStartTime: currentAppointment.expectedAppointmentStartTime, // Use the potentially updated start time from args
                        expectedAppointmentEndTime: currentAppointment.expectedAppointmentEndTime,   // Use the potentially updated end time from args
                        placeId: args.placeId,
                        serviceId: args.serviceId,
                        status: 'completed',
                        notes: args.notes,
                        type: 'completed',
                    };
                } else {
                    console.warn(`[updateAppointment] Cannot prepare shift data for ${args.appointmentId}: missing realAppointmentStartTime or service info from currentAppointment.`);
                }
            }


            // Handle Credit Refund on Cancellation (if applicable)
            if (newStatus === 'canceled' && currentStatus !== 'canceled' && currentStatus !== 'noshow') {
                console.log(`Provider canceling appointment ${args.appointmentId}. Refunding ${creditsToRefund} credits to customer ${customerId}.`);
                appointmentStatusUpdate = { ...appointmentStatusUpdate, status: 'canceled', canceledAt: new Date() };
                await tx.user.update({ where: { id: customerId }, data: { credits: { increment: creditsToRefund } } });
                 // Provider also gets a credit back if they cancel (assuming they paid one initially)
                await tx.user.update({ where: { id: providerUserId }, data: { credits: { increment: 1 }}});

            } else if (newStatus !== currentStatus) { // General status update if not cancellation
                appointmentStatusUpdate = { ...appointmentStatusUpdate, status: newStatus };
                 if (newStatus === 'completed' && !appointmentStatusUpdate.realAppointmentEndTime) {
                     appointmentStatusUpdate.realAppointmentEndTime = new Date(); // Ensure end time is set
                 }
                 if (newStatus === 'noshow' && currentStatus !== 'noshow') { 
                    await tx.user.update({
                        where: { id: providerUserId },
                        data: { credits: { increment: 1 } } // Provider gets credit for no-show
                    });
                    // Customer does not get refund for no-show.
                }
            }

            // Fetch correct service duration if service changed
            let serviceDuration = currentAppointment.service.duration;
             if (args.serviceId !== currentAppointment.serviceId) {
                 const newService = await tx.service.findUnique({ where: { id: args.serviceId }, select: { duration: true }});
                 if (!newService) throw new HttpError(400, "Target service not found.");
                 serviceDuration = newService.duration;
             }

            // Create History Record (only if relevant fields changed)
            const changedFields = args.startTime.toISOString() !== currentAppointment.expectedAppointmentStartTime?.toISOString() ||
                args.endTime.toISOString() !== currentAppointment.expectedAppointmentEndTime?.toISOString() ||
                                  args.status !== currentStatus ||
                                  args.notes !== currentAppointment.notes ||
                                  args.serviceId !== currentAppointment.serviceId ||
                                  args.placeId !== currentAppointment.placeId ||
                                  targetQueueId !== currentAppointment.queueId;

            if (changedFields || (newStatus === 'InProgress' && currentStatus !== 'InProgress') || (newStatus === 'completed' && currentStatus === 'InProgress')) {
                await tx.appointmentHistory.create({
                    data: {
                        appointmentId: currentAppointment.id,
                        changedByUserId: changerUserId,
                        previousStartTime: currentAppointment.expectedAppointmentStartTime!,
                        previousEndTime: currentAppointment.expectedAppointmentEndTime!,
                        newStartTime: args.startTime,
                        newEndTime: args.endTime,
                        previousStatus: currentStatus,
                        newStatus: args.status, // Use final newStatus from args
                        previousMotifId: currentAppointment.serviceId,
                        newMotifId: args.serviceId,
                        previousAgendaId: currentAppointment.queueId ?? 0, 
                        newAgendaId: targetQueueId, // targetQueueId is now guaranteed to be a number
                        changeReason: "Updated by provider", // TODO: More specific reason if possible
                    }
                });
            }

            // Update the Appointment itself
            const finalAppointmentData: Prisma.AppointmentUpdateInput = {
                placeId: args.placeId,
                serviceId: args.serviceId,
                queueId: targetQueueId,
                // expectedAppointmentStartTime: args.startTime,
                // expectedAppointmentStartTime: appointmentStatusUpdate.realAppointmentStartTime,
                // expectedAppointmentEndTime: args.endTime,
                // expectedAppointmentEndTime: dayjs(appointmentStatusUpdate.realAppointmentStartTime).add(serviceDuration, 'minutes').toDate(),
                serviceDuration: serviceDuration, // Use the correct service duration
                notes: args.notes,
                ...appointmentStatusUpdate // Contains status and potentially realStart/EndTimes
            };
            
            // Remove undefined fields from finalAppointmentData to avoid Prisma errors
            Object.keys(finalAppointmentData).forEach(key => 
                (finalAppointmentData as any)[key] === undefined && delete (finalAppointmentData as any)[key]
            );


            const updated = await tx.appointment.update({
                where: { id: args.appointmentId },
                data: finalAppointmentData,
                include: { 
                    service: { select: {duration: true, title:true } },
                    place: { select: { name: true } }, // If place name is used in messages
                    customerFolder: {
                      select: {
                        userId: true, // Customer's User ID
                        provider: { // SProvider
                          select: {
                            userId: true, // Provider's User ID
                            title: true,  // Provider's business name
                          }
                        }
                      }
                    },
                } // Ensure service.duration is available for shiftDataForCurrent
            });


            if (!updated.customerFolder?.userId) {
                console.error("Missing customer user ID for updated appointment. Skipping notifications.");
            } else {
                const serviceTitle = updated.service?.title || 'the service';
                const providerName = updated.customerFolder.provider?.title || 'the provider';
                const customerUserId = updated.customerFolder.userId;
                let notificationType = '', notificationTitle = '', notificationMessage = '', notificationLink = '';
        
                // 1. Status Change Notifications
                if (newStatus !== currentStatus) {
                  const appointmentDateTime = updated.expectedAppointmentStartTime
                    ? new Date(updated.expectedAppointmentStartTime).toLocaleString([], { dateStyle: 'medium', timeStyle: 'short' })
                    : 'your appointment';
        
                  if (updated.status === 'confirmed' && currentStatus === 'pending') {
                    notificationType = 'APPOINTMENT_CONFIRMED_CUSTOMER';
                    notificationTitle = 'Appointment Confirmed';
                    notificationMessage = `Great news! Your appointment for ${serviceTitle} with ${providerName} on ${appointmentDateTime} has been confirmed.`;
                    notificationLink = `/my-appointments/${updated.id}`;
                  } else if (updated.status === 'completed') {
                    notificationType = 'APPOINTPOINTMENT_COMPLETED_CUSTOMER';
                    notificationTitle = 'Appointment Completed';
                    notificationMessage = `Your appointment for ${serviceTitle} with ${providerName} on ${appointmentDateTime} has been marked as completed. We hope to see you again soon!`;
                    notificationLink = `/my-appointments/${updated.id}`;
                  } else if (updated.status === 'noshow') {
                    notificationType = 'APPOINTMENT_NO_SHOW_CUSTOMER';
                    notificationTitle = 'Appointment Missed';
                    notificationMessage = `You were marked as a no-show for your appointment for ${serviceTitle} with ${providerName} on ${appointmentDateTime}. Please contact us if you have any questions.`;
                    notificationLink = `/my-appointments/${updated.id}`;
                  } else if (updated.status === 'canceled') {
                    notificationType = 'APPOINTMENT_CANCELED_CUSTOMER';
                    notificationTitle = 'Appointment Canceled';
                    notificationMessage = `Your appointment for ${serviceTitle} with ${providerName} on ${appointmentDateTime} has been canceled. Please contact us if you have any questions.`;
                    notificationLink = `/my-appointments/${updated.id}`;
                  }
                  // Add more status change notifications here (e.g., if provider cancels, etc.)
                }
        
                // 2. Reschedule Notification (Time Change)
                // Check if start time was provided in args and is different from the previous start time
                // const newStartTimeProvided = args.expectedAppointmentStartTime;
                // const oldStartTime = previousStartTime; // From existingAppointment fetched before transaction
        
                // if (newStartTimeProvided && new Date(newStartTimeProvided).getTime() !== new Date(oldStartTime).getTime()) {
                //     const oldDateTimeStr = new Date(oldStartTime).toLocaleString([], { dateStyle: 'medium', timeStyle: 'short' });
                //     const newDateTimeStr = new Date(newStartTimeProvided).toLocaleString([], { dateStyle: 'medium', timeStyle: 'short' });
        
                //     notificationType = 'APPOINTMENT_RESCHEDULED_BY_PROVIDER_CUSTOMER';
                //     notificationTitle = 'Appointment Rescheduled';
                //     notificationMessage = `Your appointment for ${serviceTitle} with ${providerName} has been rescheduled from ${oldDateTimeStr} to ${newDateTimeStr}.`;
                //     notificationLink = `/my-appointments/${updated.id}`;
                // }
                
                // Send the notification if one was generated
                if (notificationType && notificationTitle && notificationMessage) {
                  await createNotificationEntry(tx.notification, {
                    userId: customerUserId,
                    type: notificationType,
                    title: notificationTitle,
                    message: notificationMessage,
                    link: notificationLink,
                    actorId: context.user.id, // The provider/admin who made the update
                  });
                }
            }






            // Perform shifts after the main update
            // if (shiftDataForPrevious) {
                // console.log(`[updateAppointment] Triggering shift for previous auto-completed appointment ${shiftDataForPrevious.id}.`);
                // await handleAppointmentShift(tx, shiftDataForPrevious, changerUserId);
            // }
            // ADDED: Trigger shift for late start
            if (shiftDataForLateStart) {
                console.log(`[updateAppointment] Triggering initial shift for start of appointment ${shiftDataForLateStart.id}.`);
                await handleAppointmentShift(tx, shiftDataForLateStart, changerUserId);
            }
            
            if (shiftDataForCurrent) {
                 console.log(`[updateAppointment] Triggering shift for current completed appointment ${shiftDataForCurrent.id}.`);
                 // Ensure all necessary fields are correctly populated in shiftDataForCurrent from `updated` and `currentAppointment`
                 const dataForCurrentShift: CompletedAppointmentForShift = {
                    id: updated.id,
                    queueId: updated.queueId!,
                    service: { duration: updated.service.duration }, // service was included in the update's return
                    realAppointmentStartTime: currentAppointment.realAppointmentStartTime!, // from the original 'InProgress' state
                    realAppointmentEndTime: (finalAppointmentData.realAppointmentEndTime as Date)!, // from the 'completed' update
                    expectedAppointmentStartTime: updated.expectedAppointmentStartTime!,
                    expectedAppointmentEndTime: updated.expectedAppointmentEndTime!,
                    placeId: updated.placeId,
                    serviceId: updated.serviceId,
                    status: updated.status,
                    notes: updated.notes,
                    type: 'completed',
                };
                await handleAppointmentShift(tx, shiftDataForCurrent, changerUserId);
            }
            

            return updated;
        });
        
        // ---- After the transaction: Notify queue change ----
        if (updatedAppointmentResult && updatedAppointmentResult.queueId) {
            const io = getIoInstance();
            if (io) {
                console.log(`[updateAppointment Operation] Appointment ${updatedAppointmentResult.id} updated (status: ${updatedAppointmentResult.status}), triggering server-side queue notification for queue ${updatedAppointmentResult.queueId}`);
                // The 'context' for broadcastQueueStateUpdate needs entities, which 'context' from operation has.
                await broadcastQueueStateUpdate(updatedAppointmentResult.queueId, context, io);
            } else {
                console.warn('[updateAppointment Operation] Socket.IO instance not available. Cannot notify queue change.');
            }
        }

        return updatedAppointmentResult;

    } catch (error: any) {
        console.error(`Failed to update appointment ${args.appointmentId}:`, error);
        if (error instanceof HttpError) {
             throw error;
       }
        throw new HttpError(500, error.message || 'Failed to update appointment.');
    }
};

// --- Complete Appointment Action (Provider) ---

const completeAppointmentInputSchema = z.object({
    appointmentId: z.number().int().positive(),
  });
  
  type CompleteAppointmentData = z.infer<typeof completeAppointmentInputSchema>;
  
  export const completeAppointment: CompleteAppointment<CompleteAppointmentData, Appointment> = async (
    rawArgs: CompleteAppointmentData,
    context: any // Use a more specific context type if available
  ): Promise<Appointment> => {
    if (!context.user) {
      throw new HttpError(401, 'User not authenticated.');
    }
    // Ensure the user performing this action is a provider (adjust role check if needed)
    // Note: We verify ownership based on the appointment later, but an initial role check can be useful.
    // if (context.user.role !== 'PROVIDER_ROLE') { // Replace 'PROVIDER_ROLE' with your actual role name
    //   throw new HttpError(403, 'Only providers can complete appointments.');
    // }
  
    const args = ensureArgsSchemaOrThrowHttpError(completeAppointmentInputSchema, rawArgs);
    const providerUserId = context.user.id;
  
    try {
      const PROVIDER_CREDIT_COST = 1; // Define the cost for the provider per booking
      // Use transaction for atomic update of appointment and user credits
      const updatedAppointmentTransactionResult = await prisma.$transaction(async (tx) => {
        // 1. Find the appointment, related service, provider, and customer
        const appointment = await tx.appointment.findUnique({
          where: { id: args.appointmentId },
          include: {
            service: { select: { pointsRequirements: true, duration: true , title: true } }, // Added duration
            place: { select: { sProviderId: true, name: true } }, // To verify provider ownership via place
            customerFolder: { include: { customer: { select: { id: true } } } } // Get customer ID
          }
        });
  
        if (!appointment) {
          throw new HttpError(404, 'Appointment not found.');
        }
  
        if (!appointment.service || !appointment.place || !appointment.customerFolder?.customer || !appointment.queueId || !appointment.expectedAppointmentStartTime || !appointment.expectedAppointmentEndTime) {
            console.error(`Data inconsistency for appointment ${args.appointmentId}. Missing service, place, customer, queueId, or expected times.`);
            throw new HttpError(500, 'Cannot complete appointment due to missing related data.');
        }
  
        // 2. Verify Provider Ownership
        const provider = await tx.sProvider.findUnique({
            where: { id: appointment.place.sProviderId },
            select: { userId: true }
        });
        if (provider?.userId !== providerUserId) {
            throw new HttpError(403, 'You are not authorized to complete this appointment.');
        }
  
        // 3. Check if appointment status allows completion
        // Allow completing 'confirmed', 'pending' or 'InProgress' appointments
        const completableStatuses = ['confirmed', 'pending', 'InProgress']; 
        if (!completableStatuses.includes(appointment.status)) {
            throw new HttpError(400, `Cannot complete appointment with status: ${appointment.status}.`);
        }
        
        const customerId = appointment.customerFolder.customer.id;
        const creditsToRefund = appointment.service.pointsRequirements;
        const realAppointmentEndTime = new Date();
        let realAppointmentStartTime = appointment.realAppointmentStartTime;
  
        // If completing directly from pending/confirmed, assume it started on time or now.
        if (appointment.status !== 'InProgress' || !realAppointmentStartTime) {
            realAppointmentStartTime = appointment.expectedAppointmentStartTime ?? new Date(); 
            // If expectedStartTime is in future, maybe clamp to now? For simplicity, use expected or now.
            if (dayjs(realAppointmentStartTime).isAfter(dayjs())) {
              realAppointmentStartTime = new Date();
            }
        }
  
  
        // 4. Update Appointment Status
        const updatedAppt = await tx.appointment.update({
          where: { id: args.appointmentId },
          data: {
            status: 'completed',
            realAppointmentStartTime: realAppointmentStartTime, // Ensure it's set
            realAppointmentEndTime: realAppointmentEndTime, 
          },
           include: { service: {select: {duration: true} } } // For shift handler
        });
  
        await tx.appointmentHistory.create({
              data: {
                  appointmentId: updatedAppt.id,
                  changedByUserId: providerUserId,
                  previousStatus: appointment.status, // Original status before completion
                  newStatus: 'completed',
                  changeReason: "Completed by provider",
                  previousStartTime: appointment.expectedAppointmentStartTime!, 
                  previousEndTime: appointment.expectedAppointmentEndTime!,
                  newStartTime: updatedAppt.expectedAppointmentStartTime!, // Times might have shifted
                  newEndTime: updatedAppt.expectedAppointmentEndTime!,
                  previousMotifId: appointment.serviceId, newMotifId: appointment.serviceId,
                  previousAgendaId: appointment.queueId ?? 0,
                  newAgendaId: appointment.queueId ?? 0,
              }
          });
  
        // 5. Refund Credits to Customer
        await tx.user.update({
          where: { id: customerId },
          data: {
            credits: {
              increment: creditsToRefund
            }
          }
        });
  
        // 6. Decrement Provider Credits (Provider pays to complete?) - This logic might need review
        // This seems counter-intuitive. Usually provider earns or uses a credit to book, not to complete.
        // For now, keeping existing logic.
        await tx.user.update({
            where: { id: providerUserId },
            data: { credits: { decrement: PROVIDER_CREDIT_COST } }
        });

        const customerUserId = appointment.customerFolder?.userId;
        // const providerUserId = appointment.customerFolder?.provider?.userId;
        const serviceTitle = appointment.service?.title;
        const placeName = appointment.place?.name;

        const notificationType = 'APPOINTMENT_COMPLETED_CUSTOMER';
        const notificationTitle = 'Appointment Completed';
        const notificationMessage = `Your appointment for ${serviceTitle} at ${placeName} has been completed. We hope to see you again soon!`;
        const notificationLink = `/my-appointments/${updatedAppt.id}`;
        if (notificationType && notificationTitle && notificationMessage) {
            await createNotificationEntry(tx.notification, {
                userId: customerUserId,
                type: notificationType,
                title: notificationTitle,
                message: notificationMessage,
                link: notificationLink,
                actorId: providerUserId, // The provider/admin who made the update
            });
        }
        
        console.log(`Appointment ${updatedAppt.id} completed by ${providerUserId}. Refunded ${creditsToRefund} credits to customer ${customerId}. Provider credits changed.`);
  
        // 7. Trigger shift calculation if it was InProgress or needs it
        if (realAppointmentStartTime && appointment.service) {
            const shiftData: CompletedAppointmentForShift = {
                id: updatedAppt.id,
                queueId: updatedAppt.queueId!,
                service: { duration: updatedAppt.service.duration }, // service from include
                realAppointmentStartTime: realAppointmentStartTime,
                realAppointmentEndTime: realAppointmentEndTime,
                expectedAppointmentStartTime: updatedAppt.expectedAppointmentStartTime!,
                expectedAppointmentEndTime: updatedAppt.expectedAppointmentEndTime!,
                placeId: updatedAppt.placeId,
                serviceId: updatedAppt.serviceId,
                status: 'completed',
                notes: updatedAppt.notes,
                type: 'completed',
            };
            console.log(`[completeAppointment] Triggering shift calculation for ${updatedAppt.id}.`);
            await handleAppointmentShift(tx, shiftData, providerUserId);
        } else {
            console.warn(`[completeAppointment] Shift calculation skipped for ${updatedAppt.id} due to missing real start time or service duration.`);
        }
  
        return updatedAppt;
      });
  
      // ---- After the transaction: Notify queue change ----
      if (updatedAppointmentTransactionResult && updatedAppointmentTransactionResult.queueId) {
          const io = getIoInstance();
          if (io) {
              console.log(`[completeAppointment Operation] Appointment ${updatedAppointmentTransactionResult.id} completed, triggering server-side queue notification for queue ${updatedAppointmentTransactionResult.queueId}`);
              await broadcastQueueStateUpdate(updatedAppointmentTransactionResult.queueId, context, io);
          } else {
              console.warn('[completeAppointment Operation] Socket.IO instance not available. Cannot notify queue change.');
          }
      }

      return updatedAppointmentTransactionResult; // This was the intended return for the outer function
  
    } catch (error: any) {
      console.error(`Failed to complete appointment ${args.appointmentId} by provider ${providerUserId}:`, error);
      if (error instanceof HttpError) {
          throw error; // Re-throw HttpErrors
      }
      if (error.code === 'P2025') { // Prisma code for record not found during update
        throw new HttpError(404, 'Appointment, Provider, or Customer not found during update.');
      }
      throw new HttpError(500, error.message || 'Failed to complete appointment and refund credits.');
    }
  };
  




// --- Search Providers Query ---

// Update input schema for pagination
const searchProvidersInputSchema = z.object({
  categoryId: z.number().int().optional(),
  q: z.string().optional(),
  city: z.string().optional(),
  skip: z.number().int().nonnegative().optional(), // Optional: default 0
  take: z.number().int().positive().optional(), // Optional: default (e.g., 6)+
  targetLanguage: z.string().optional(),
});

type SearchProvidersInput = z.infer<typeof searchProvidersInputSchema>;

// --- TYPE DEFINITIONS FOR NEAREST SLOT & UPDATED SEARCH RESULT ---
// These should be placed globally, before the searchProviders function.
// Ensure `Service` and `Queue` are in scope from `wasp/entities` import at the top.

type MinimalServiceInfo = Pick<Service, 'id' | 'title' | 'duration'>;
type MinimalQueueInfo = Pick<Queue, 'id' | 'title'>;

type NearestSlotInfo = {
  datetime: Date;
  service: MinimalServiceInfo;
  queue: MinimalQueueInfo;
};

// The types AvailabilitySlot and DailyAvailabilityResult are defined later in this file,
// in conjunction with the getProviderAvailability function. TypeScript handles this ordering for type definitions.
// REMOVED Duplicate DailyAvailabilityResult definition from here.

// Updated return type for searchProviders
// This uses MinimalServiceInfo and NearestSlotInfo defined above.
// SProvider, User, ProviderCategory, SProvidingPlace, Queue are from wasp/entities.

type SearchProviderResultWithNearestSlot = SProvider & {
  user: User;
  category?: ProviderCategory | null;
  services: Service[]; 
  providingPlaces: (SProvidingPlace & {
      queues: (Pick<Queue, 'id' | 'title' | 'isActive'> & { services: MinimalServiceInfo[] })[];
  })[];
  nearestSlot?: NearestSlotInfo | null;
};

// NOTE: The `searchProvidersInputSchema` and `SearchProvidersInput` type alias
// are assumed to be correctly defined *earlier* in the file (e.g., around line 1346-1353 in the full file context).
// The following `export const searchProviders` function should use that existing definition.
// The duplicate definitions that caused errors will be removed by this edit focusing on the function body and its direct type dependencies.

// --- searchProviders: Fix for "Cannot read properties of undefined (reading 'getProviderAvailability')" ---
// The error occurs because getProviderAvailability is not in scope or is undefined at the point where searchProviders calls it.
// This can happen if you have a circular import, or if getProviderAvailability is not hoisted above searchProviders in the file.
// Solution: Move the definition of getProviderAvailability ABOVE searchProviders, or ensure it is imported/defined before use.
// Here, we move the function definition order so getProviderAvailability is defined first, then searchProviders.


// --- Get Provider Availability Query ---

const getProviderAvailabilityInputSchema = z.object({
  sProvidingPlaceId: z.number().int(),
  serviceId: z.number().int(),
  startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Start date must be YYYY-MM-DD"),
  endDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "End date must be YYYY-MM-DD"),
  queueId: z.number().int().optional(),
}).refine(data => new Date(data.endDate) >= new Date(data.startDate), {
    message: "End date must be after or same as start date",
    path: ["endDate"],
});

type GetProviderAvailabilityInput = z.infer<typeof getProviderAvailabilityInputSchema>;

// EXPORT these types for use in apiHandlers.ts
export type AvailabilitySlot = { 
    startTime: Date; 
    queueId: number; 
    isBooked: boolean;
};
export type DailyAvailabilityResult = {
    date: string; 
    slots: AvailabilitySlot[]; 
};

type QueueOpeningWithHours = QueueOpening & {
    hours: QueueOpeningHours[];
};
type QueueWithOpeningsAndHours = Queue & {
    openings: QueueOpeningWithHours[];
};

export const getProviderAvailability = async (
    rawArgs: GetProviderAvailabilityInput, 
    context: any 
): Promise<DailyAvailabilityResult[]> => {
    const args = ensureArgsSchemaOrThrowHttpError(getProviderAvailabilityInputSchema, rawArgs);

    const startDate = dayjs.utc(args.startDate).startOf('day'); 
    const endDate = dayjs.utc(args.endDate).endOf('day'); 
    const endLoopDate = dayjs.utc(args.endDate).startOf('day');

    if (!startDate.isValid() || !endDate.isValid()) {
        throw new HttpError(400, "Invalid start or end date format.");
    }

    try {
        // 1. Fetch Service duration & verify existence
        const service = await context.entities.Service.findUnique({
            where: { id: args.serviceId },
            select: { duration: true },
        });
        if (!service) throw new HttpError(404, "Service not found.");
        const serviceDuration = service.duration;

        // 2. Fetch Place Timezone (needed for converting opening times)
        const providingPlace = await context.entities.SProvidingPlace.findUnique({
            where: { id: args.sProvidingPlaceId },
            select: { timezone: true },
        });
        if (!providingPlace) throw new HttpError(404, "Providing place not found.");
        const placeTimezone = providingPlace.timezone || 'UTC';
        try {
            dayjs.tz('2023-01-01', placeTimezone); // Validate timezone
        } catch (e) {
            throw new HttpError(500, `Invalid timezone configured for the providing place: ${placeTimezone}`);
        }

        // 3. Fetch relevant *Queues* and their openings/hours
        let relQueuesWhereClause: Prisma.QueueWhereInput = {
            sProvidingPlaceId: args.sProvidingPlaceId,
            isActive: true,
        };

        if(args.queueId) {
            relQueuesWhereClause.id = args.queueId;
        }

        const relevantQueues = await context.entities.Queue.findMany({
            where: relQueuesWhereClause,
            include: {
                openings: {
                    where: { isActive: true, type: 'regular' }, 
                    include: {
                        hours: { orderBy: { timeFrom: 'asc' } } 
                    }
                }
            }
        }) as QueueWithOpeningsAndHours[];
        
        // console.log(`[getProviderAvailability] Found ${relevantQueues.length} relevant queues for service ${args.serviceId} at place ${args.sProvidingPlaceId}.`);

        if (relevantQueues.length === 0) {
            console.log(`[getProviderAvailability] No active queues found for service ${args.serviceId} at place ${args.sProvidingPlaceId}.`);
            return [];
        }
        const relevantQueueIds = relevantQueues.map((q: any) => q.id);

        // 4. Fetch Appointments for the *relevant queues* within the date range
        const appointments = await context.entities.Appointment.findMany({
            where: {
                queueId: { in: relevantQueueIds },
                status: { in: ['pending', 'confirmed', 'completed'] },
                expectedAppointmentStartTime: { lt: endDate.toDate() }, 
                expectedAppointmentEndTime: { gt: startDate.toDate() }, 
            },
            select: {
                expectedAppointmentStartTime: true,
                expectedAppointmentEndTime: true,
                queueId: true,
            },
            orderBy: { expectedAppointmentStartTime: 'asc' } 
        });

        // 5. Pre-process booked slots for efficient lookup (Map queueId -> array of {startMs, endMs})
        const bookedSlotsByQueue = new Map<number, { start: number; end: number }[]>();
        appointments.forEach((appt: any) => {
            if (appt.queueId && appt.expectedAppointmentStartTime && appt.expectedAppointmentEndTime) {
                const queueId = appt.queueId;
                if (!bookedSlotsByQueue.has(queueId)) {
                    bookedSlotsByQueue.set(queueId, []);
                }
                bookedSlotsByQueue.get(queueId)?.push({
                    start: appt.expectedAppointmentStartTime.getTime(),
                    end: appt.expectedAppointmentEndTime.getTime(),
                });
            }
        });

        // 6. Calculate Availability Slot by Slot for each relevant queue
        const availability: DailyAvailabilityResult[] = []; 
        let currentDay = startDate;

        while (currentDay.isSameOrBefore(endLoopDate, 'day')) {
            const isoDateString = currentDay.format('YYYY-MM-DD');
            const dayOfWeekName = currentDay.format('dddd');
            const dailySlots: AvailabilitySlot[] = []; 
            // console.log(`[getProviderAvailability] Day of Week Name: ${dayOfWeekName}`);
            for (const queue of relevantQueues) { 
                const queueId = queue.id;
                const queueBookedSlots = bookedSlotsByQueue.get(queueId) || []; 

                const openingInfo = queue.openings.find((o) => o.dayOfWeek === dayOfWeekName);
                const slotsToShow = 1000;
                let currentSlotOpening = 0;
                if (openingInfo && openingInfo.hours.length > 0) {
                    for (const hours of openingInfo.hours) {
                        const openingStartStr = `${isoDateString}T${hours.timeFrom}:00`;
                        const openingEndStr = `${isoDateString}T${hours.timeTo}:00`;
                        let openingStartTimeTz = dayjs.tz(openingStartStr, placeTimezone);
                        const openingEndTimeTz = dayjs.tz(openingEndStr, placeTimezone);

                        if (!openingStartTimeTz.isValid() || !openingEndTimeTz.isValid()) {
                            // console.warn(`[getProviderAvailability] Invalid opening hours ${hours.timeFrom}-${hours.timeTo} for queue ${queueId} on ${isoDateString} in timezone ${placeTimezone}. Skipping.`);
                            continue;
                        }

                        let currentSlotStartTimeTz = openingStartTimeTz; 
                        while (currentSlotStartTimeTz.add(serviceDuration, 'minutes').isSameOrBefore(openingEndTimeTz)) {
                            const currentSlotEndTimeTz = currentSlotStartTimeTz.add(serviceDuration, 'minutes');
                            const potentialStartMs = currentSlotStartTimeTz.valueOf();
                            const potentialEndMs = currentSlotEndTimeTz.valueOf();
                            let isBooked = false;
                            for (const booked of queueBookedSlots) {
                                if (booked.start < potentialEndMs && booked.end > potentialStartMs) {
                                    isBooked = true;
                                    break;
                                }
                            }
                            if (currentSlotOpening < slotsToShow) {
                                dailySlots.push({
                                    startTime: currentSlotStartTimeTz.toDate(),
                                    queueId: queueId,
                                    isBooked: isBooked 
                                });
                                currentSlotOpening++;
                            }
                            currentSlotStartTimeTz = currentSlotStartTimeTz.add(serviceDuration, 'minutes'); 
                        }
                    }
                }
            }
            dailySlots.sort((a, b) => a.startTime.getTime() - b.startTime.getTime());
            availability.push({
                date: isoDateString,
                slots: dailySlots, 
            });
            currentDay = currentDay.add(1, 'day');
        }
        return availability;

    } catch (error: any) {
        console.error("[getProviderAvailability] Failed:", error);
        if (error instanceof HttpError) {
            throw error;
        }
         throw new HttpError(500, "An unexpected error occurred while fetching availability.");
    }
};


// --- Search Providers Query ---

export const searchProviders = async (rawArgs: SearchProvidersInput, context: any): Promise<any[]> => {
    // Return Promise<any[]> for now to bypass complex type issue with linter

    const args = ensureArgsSchemaOrThrowHttpError(searchProvidersInputSchema, rawArgs ?? {});
    const skip = args.skip ?? 0;
    const take = args.take ?? 6;
    const whereConditions: Prisma.SProviderWhereInput = {};

    console.log(`[searchProviders] Args: ${args}`);
    const targetLanguage = args.targetLanguage ?? LanguageCode.EN;

    console.log(`[searchProviders] Target language: ${targetLanguage}`);

    // whereConditions.isVerified = true;
    // whereConditions.isSetupComplete = true;

    if (args.categoryId) {
        whereConditions.providerCategoryId = args.categoryId;
    }

    if (args.q && args.q.trim().length > 0) {
        const searchQuery = args.q.trim();
        whereConditions.OR = [
            { title: { contains: searchQuery, mode: 'insensitive' } },
            { user: { firstName: { contains: searchQuery, mode: 'insensitive' } } },
            { user: { lastName: { contains: searchQuery, mode: 'insensitive' } } },
            { services: { some: { title: { contains: searchQuery, mode: 'insensitive' } } } },
            { category: { title: { contains: searchQuery, mode: 'insensitive' } } },
            { providingPlaces: { some: { name: { contains: searchQuery, mode: 'insensitive' } } } },
            { providingPlaces: { some: { queues: { some: { title: { contains: searchQuery, mode: 'insensitive' } } } } } },
        ];
    }

    const andConditions: Prisma.SProviderWhereInput[] = [];
    if (args.city) {
        andConditions.push({ providingPlaces: { some: { city: { contains: args.city, mode: 'insensitive' } } } });
    }
    andConditions.push({ user: { credits: { gt: 0 } } });
    andConditions.push({ isSetupComplete: true });
    if (andConditions.length > 0) {
        if (whereConditions.AND) {
             if (Array.isArray(whereConditions.AND)) {
                 whereConditions.AND = whereConditions.AND.concat(andConditions);
             } else {
                 whereConditions.AND = [whereConditions.AND, ...andConditions];
             }
        } else {
            whereConditions.AND = andConditions;
        }
    }

    try {
        const providersData = await context.entities.SProvider.findMany({
            where: whereConditions,
            include: {
                user: true,
                category: true,
                reviewsReceived: {
                    select: {
                        rating: true,
                        comment: true,
                        createdAt: true,
                    },
                },
                services: { 
                    select: { 
                        id:true, title:true, duration:true, color: true, serviceCategoryId:true, 
                        minDuration:true, maxDuration:true, queue:true, acceptOnline:true, 
                        acceptNew:true, notificationOn:true, pointsRequirements: true, 
                        sProviderId: true, createdAt: true, updatedAt: true
                    }
                },
                providingPlaces: {
                    include: {
                        queues: {
                            where: { isActive: true },
                            select: {
                                id: true,
                                title: true,
                                isActive: true,
                                services: { 
                                    select: { id: true, title: true, duration: true }
                                }
                            }
                        }
                    }
                },
            },
            orderBy: {
                title: 'asc',
            },
            skip: skip,
            take: take,
        });
        console.log(`[searchProviders] Found ${providersData.length} providers`);
        const providers = providersData as SearchProviderResultWithNearestSlot[];
        console.log(`[searchProviders] Target language: ${targetLanguage}`);
        if (targetLanguage) {
            for (const provider of providers) {
                // Translate SProvider.title
                if (provider.title) {
                    const translatedSProviderTitle = await getTranslatedString(context.entities, 'SProvider', String(provider.id), 'title', targetLanguage as LanguageCode);
                    console.log(`[searchProviders] Translated SProvider.title: ${translatedSProviderTitle}`);
                    if (translatedSProviderTitle) provider.title = translatedSProviderTitle;
                }
                // Translate SProvider.presentation (if it's part of the SearchProviderResultWithNearestSlot and selected)
                // if (provider.presentation) { ... }


                // Translate ProviderCategory.title
                if (provider.category?.title) {
                    const translatedCategoryTitle = await getTranslatedString(context.entities, 'ProviderCategory', String(provider.category.id), 'title', targetLanguage as LanguageCode);
                    console.log(`[searchProviders] Translated ProviderCategory.title: ${translatedCategoryTitle}`);
                    if (translatedCategoryTitle) provider.category.title = translatedCategoryTitle;
                }

                for (const service of provider.services) {
                    if (service.title) {
                        const translatedServiceTitle = await getTranslatedString(context.entities, 'Service', String(service.id), 'title', targetLanguage as LanguageCode);
                        console.log(`[searchProviders] Translated Service.title: ${translatedServiceTitle}`);   
                        if (translatedServiceTitle) service.title = translatedServiceTitle;
                    }
                }

                for (const place of provider.providingPlaces) {
                    if (place.name) {
                        const translatedPlaceName = await getTranslatedString(context.entities, 'SProvidingPlace', String(place.id), 'name', targetLanguage as LanguageCode);
                        console.log(`[searchProviders] Translated SProvidingPlace.name: ${translatedPlaceName}`);
                        if (translatedPlaceName) place.name = translatedPlaceName;
                    }
                    for (const queue of place.queues) {
                        if (queue.title) {
                            const translatedQueueTitle = await getTranslatedString(context.entities, 'Queue', String(queue.id), 'title', targetLanguage as LanguageCode);
                            console.log(`[searchProviders] Translated Queue.title: ${translatedQueueTitle}`);
                            if (translatedQueueTitle) queue.title = translatedQueueTitle;
                        }

                        for (const service of queue.services) {
                            console.log(`[searchProviders] Service.title: ${service.title}`);
                            if (service.title) {
                                const translatedServiceTitle = await getTranslatedString(context.entities, 'Service', String(service.id), 'title', targetLanguage as LanguageCode);
                                console.log(`[searchProviders] Translated Service.title: ${translatedServiceTitle}`);
                                if (translatedServiceTitle) service.title = translatedServiceTitle;
                            }
                        }
                    }
                }
            }
        }


        providers.sort((a, b) => {
            if (a.isLowPriorityInSearchResult === b.isLowPriorityInSearchResult) return 0;
            return a.isLowPriorityInSearchResult ? 1 : -1;
        });

        const now = dayjs.utc();
        const startDateString = now.format('YYYY-MM-DD');
        const endDateString = now.add(7, 'days').format('YYYY-MM-DD');

        for (const provider of providers) {
            let overallNearestSlot: NearestSlotInfo | null = null;

            for (const place of provider.providingPlaces) {
                if (!place.queues || place.queues.length === 0) continue;

                for (const queue of place.queues) {
                    if (!queue.isActive || !queue.services || queue.services.length === 0) continue;

                    for (const service of queue.services) { 
                        try {
                            const availabilityArgs = {
                                sProvidingPlaceId: place.id,
                                serviceId: service.id,
                                startDate: startDateString,
                                endDate: endDateString,
                                queueId: queue.id
                            };
                            
                            // --- FIX: getProviderAvailability is now always in scope above ---
                            const dailyAvailabilityResultsFromOperation = await getProviderAvailability(availabilityArgs, context) as DailyAvailabilityResult[];
                            // --------------------------------------------------------------
                            // console.log(dailyAvailabilityResultsFromOperation)
                            for (const dailyResult of dailyAvailabilityResultsFromOperation) {
                                for (const slot of dailyResult.slots) {
                                    if (!slot.isBooked && dayjs(slot.startTime).isAfter(now)) {
                                        const currentSlotInfo: NearestSlotInfo = {
                                            datetime: slot.startTime,
                                            service: service, 
                                            queue: { id: queue.id, title: queue.title }
                                        };

                                        if (!overallNearestSlot || dayjs(currentSlotInfo.datetime).isBefore(overallNearestSlot.datetime)) {
                                            overallNearestSlot = currentSlotInfo;
                                        }
                                    }
                                }
                            }
                        } catch (err: any) {
                            // If getProviderAvailability throws, log the error with details
                            console.warn(`[searchProviders] Error fetching availability for provider ${provider.id}, place ${place.id}, queue ${queue.id}, service ${service.id}: ${err?.message ?? err}`);
                        }
                    }
                }
            }
            provider.nearestSlot = overallNearestSlot;
        }
        return providers;

    } catch (error: any) {
        console.error("Failed to search providers:", error);
        return [];
    }
};

// --- Early Closure Action (stub, to be implemented in detail next) ---
export async function closeEarlyForToday(args: any, context: any) {
     // Implementation will go here
     // Args: { providerId, actionType: 'cancel_all' | 'reschedule', reason?: string }
     // Logic: find today's remaining appointments, cancel or mark for reschedule, handle credits/penalties
     return { success: false, message: 'Not implemented yet' };
}

// --- Create Customer Appointment Action ---

// Input validation schema
const createCustomerAppointmentInputSchema = z.object({
    // providerId: z.number().int(), // We get provider via placeId
    placeId: z.number().int(),
    serviceId: z.number().int(),
    queueId: z.number().int().positive("Queue ID is required"), // ADDED queueId
    startTime: z.date({ coerce: true }), // Expecting ISO string or Date from client
    endTime: z.date({ coerce: true }),   // Expecting ISO string or Date from client
    // notes: z.string().optional().nullable(), // Optional notes from customer?
}).refine(data => data.endTime > data.startTime, {
    message: "End time must be after start time",
    path: ["endTime"],
});

type CreateCustomerAppointmentData = z.infer<typeof createCustomerAppointmentInputSchema>;

// Type annotation should be inferred by Wasp using the definition in main.wasp
export const createCustomerAppointment = async (rawArgs: CreateCustomerAppointmentData, context: any): Promise<Appointment> => {
    // Validate context and customer user
    if (!context.user?.id || typeof context.user.credits !== 'number') {
        throw new HttpError(401, "Customer not authenticated or credit data missing.");
    }
    if (context.user.role !== 'CUSTOMER') {
        throw new HttpError(403, "Only customers can book appointments.");
    }

    const customerUserId = context.user.id;
    const customerCredits = context.user.credits;

    const args = ensureArgsSchemaOrThrowHttpError(createCustomerAppointmentInputSchema, rawArgs);
    console.log(`[createCustomerAppointment Customer] User: ${customerUserId} attempting to book for Queue: ${args.queueId} with args:`, args); // Updated log

    const PROVIDER_CREDIT_COST = 1; // Define provider cost per booking

    try {
        const newAppointment = await prisma.$transaction(async (tx) => {
            // 1. Fetch Place, Provider User ID + Credits, and Queue Timezone
            const place = await tx.sProvidingPlace.findUnique({
                where: { id: args.placeId },
                include: {
                    provider: {
                        include: {
                            user: { select: { id: true, credits: true } }
                            }
                    },
                    queues: { // Include queue here to validate it belongs to the place
                        where: { id: args.queueId },
                        select: { id: true, isActive: true, sProvidingPlace: { select: { timezone: true } } } // Fetch timezone via nested relation
                    }
                }
            });
            if (!place) throw new HttpError(404, "Selected location not found.");
            if (!place.provider?.user) throw new HttpError(500, "Provider user data not found for the selected location.");
            if (place.queues.length === 0) throw new HttpError(400, "Selected Queue does not belong to the specified Place.");
            const queue = place.queues[0]; // Get the matched queue
            if (!queue.isActive) throw new HttpError(400, "Selected Queue is not active.");
            const queueTimezone = queue.sProvidingPlace.timezone || 'UTC';


            const providerId = place.provider.id;
            const providerUserId = place.provider.user.id;
            const providerCredits = place.provider.user.credits;

            // 1a. Check Provider Credits
            if (typeof providerCredits !== 'number' || providerCredits < PROVIDER_CREDIT_COST) {
                throw new HttpError(402, `Provider has insufficient credits to accept this booking. Required: ${PROVIDER_CREDIT_COST}`);
            }

            // 2. Fetch Service & Validate if offered by Queue
            const service = await tx.service.findFirst({
                where: {
                    id: args.serviceId,
                    sProviderId: providerId,
                },
                select: { id: true, duration: true, pointsRequirements: true },
            });
            if (!service) throw new HttpError(400, "Selected service is invalid for this provider or not offered by the selected queue.");

            const customerCreditCost = service.pointsRequirements;

            // 3. Check Customer Credits
            if (customerCredits < customerCreditCost) {
                throw new HttpError(402, `Insufficient credits to book. Required: ${customerCreditCost}, Available: ${customerCredits}`);
            }

            // 4. Validate Time Slot against *Queue* Openings
            const startDayjs = dayjs.utc(args.startTime);
            const endDayjs = dayjs.utc(args.endTime);
            const dayOfWeek = startDayjs.format('dddd'); // UTC day name

            const queueOpeningsForDay = await tx.queueOpening.findMany({
                where: {
                    queueId: args.queueId,
                    dayOfWeek: dayOfWeek,
                    type: 'regular',
                    isActive: true,
                },
                include: { hours: true }
            });

            let isSlotValid = false;
            for (const opening of queueOpeningsForDay) {
                for (const hours of opening.hours) {
                    const isoDateString = startDayjs.format('YYYY-MM-DD');
                    const openingStartTz = dayjs.tz(`${isoDateString}T${hours.timeFrom}:00`, queueTimezone).utc();
                    const openingEndTz = dayjs.tz(`${isoDateString}T${hours.timeTo}:00`, queueTimezone).utc();
                    if (startDayjs.isSameOrAfter(openingStartTz) && endDayjs.isSameOrBefore(openingEndTz)) {
                        isSlotValid = true;
                        break;
                    }
                }
                if (isSlotValid) break;
            }
            if (!isSlotValid) {
                throw new HttpError(400, `The selected time slot is outside the queue's opening hours for ${dayOfWeek}.`);
            }

            // 5. Find or Create CustomerFolder (No change)
            const customerFolder = await tx.customerFolder.upsert({
                where: { sProviderId_userId: { sProviderId: providerId, userId: customerUserId } },
                update: { isActive: true },
                create: { sProviderId: providerId, userId: customerUserId },
                select: { id: true },
            });

            // 6. Check for Overlapping Appointments *on this specific queue*
            const overlappingAppointments = await tx.appointment.findMany({
                where: {
                    queueId: args.queueId, // Check only this queue
                    status: { notIn: ['canceled', 'noshow'] },
                    expectedAppointmentStartTime: { lt: args.endTime },
                    expectedAppointmentEndTime: { gt: args.startTime },
                },
                select: { id: true }
            });
            if (overlappingAppointments.length > 0) {
                throw new HttpError(409, "This time slot is already booked on the selected queue.");
            }

            // 7. Check Customer Schedule Conflict (No change needed here, checks across all provider appointments)
            const overlappingCustomerAppointments = await tx.appointment.findMany({
                where: {
                    customerFolder: { userId: customerUserId },
                    status: { notIn: ['canceled', 'noshow'] },
                    expectedAppointmentStartTime: { lt: args.endTime },
                    expectedAppointmentEndTime: { gt: args.startTime },
                },
                select: { id: true },
            });
            if (overlappingCustomerAppointments.length > 0) {
                throw new HttpError(409, "You already have another appointment booked during this time.");
            }

            // --- Perform Updates --- 

            // 8. Decrement Customer Credits
            await tx.user.update({
                where: { id: customerUserId },
                data: { credits: { decrement: customerCreditCost } }
            });

            // 9. Decrement Provider Credits
            await tx.user.update({
                where: { id: providerUserId },
                data: { credits: { decrement: PROVIDER_CREDIT_COST } }
            });

            // 10. Create the Appointment, linking to queueId
            const createdAppointment = await tx.appointment.create({
                data: {
                    customerFolderId: customerFolder.id,
                    placeId: place.id,
                    serviceId: service.id,
                    queueId: args.queueId, // SAVE the queueId
                    expectedAppointmentStartTime: args.startTime,
                    expectedAppointmentEndTime: args.endTime,
                    serviceDuration: service.duration, // Use fetched service duration
                    status: 'pending', // Customer bookings usually start as pending
                    // bookingCreditCost: customerCreditCost // Optional tracking
                },
                include: {  
                    service: { select: { title: true } },
                    place: { select: { name: true } },
                    customerFolder: { select: { userId: true , customer: { select: { id: true, firstName: true, lastName: true } } } }
                }
            });

            // 11. Create Notification for Customer
            const notificationType = 'APPOINTMENT_BOOKED_CUSTOMER';
            const notificationTitle = 'Appointment Booked';
            const notificationMessage = `A Customer (${createdAppointment.customerFolder?.customer?.firstName} ${createdAppointment.customerFolder?.customer?.lastName}) has booked an appointment for ${createdAppointment.service?.title} at ${createdAppointment.place?.name}. Please confirm or reschedule if necessary.`;
            const notificationLink = `/admin/appointments`;
            if (notificationType && notificationTitle && notificationMessage) {
                await createNotificationEntry(tx.notification, {
                    userId: providerUserId,
                    type: notificationType,
                    title: notificationTitle,
                    message: notificationMessage,
                    link: notificationLink,
                    actorId: customerUserId, // The provider/admin who made the update
                });
            }
            
            

            console.log(`[createCustomerAppointment Customer] Booked Appt ID: ${createdAppointment.id} on Queue ${args.queueId}. Deducted ${customerCreditCost} customer credits & ${PROVIDER_CREDIT_COST} provider credits.`); // Updated log
            return createdAppointment;
        });

        return newAppointment;

    } catch (error: any) {
        console.error("[createCustomerAppointment Customer] Transaction failed:", error);
        if (error instanceof HttpError) {
            throw error; // Re-throw known HttpErrors
        }
        throw new HttpError(500, error.message || "An unexpected error occurred while booking the appointment.");
    }
};


// --- No Show Appointment Action (Provider) ---

const noShowAppointmentInputSchema = z.object({
  appointmentId: z.number().int().positive(),
});

type NoShowAppointmentData = z.infer<typeof noShowAppointmentInputSchema>;

export const noShowAppointment: NoShowAppointment<NoShowAppointmentData, Appointment> = async (
  rawArgs: NoShowAppointmentData,
  context: any // Use specific context type if available
): Promise<Appointment> => {
  if (!context.user) {
    throw new HttpError(401, 'User not authenticated.');
  }
  const providerUserId = context.user.id;
  const args = ensureArgsSchemaOrThrowHttpError(noShowAppointmentInputSchema, rawArgs);

  try {
    const updatedAppointmentTransactionResult = await prisma.$transaction(async (tx) => {
      // 1. Find appointment, verify ownership, check status
      const appointment = await tx.appointment.findUnique({
        where: { id: args.appointmentId },
        include: {
          place: { select: { sProviderId: true } }, // For ownership check
          customerFolder: { select: { userId: true } }, // Needed for history
          service: { select: { duration: true } }
        }
      });

      if (!appointment) throw new HttpError(404, 'Appointment not found.');
      if (!appointment.place || !appointment.customerFolder?.userId || !appointment.expectedAppointmentStartTime || !appointment.expectedAppointmentEndTime) {
         throw new HttpError(500, 'Appointment data incomplete.');
      }

      const provider = await tx.sProvider.findUnique({
          where: { id: appointment.place.sProviderId }, select: { userId: true }
      });
      if (provider?.userId !== providerUserId) {
          throw new HttpError(403, 'You are not authorized to modify this appointment.');
      }

      // 2. Status Check: Allow marking confirmed/pending/InProgress appointments as no-show
      const noShowableStatuses = ['confirmed', 'pending', 'InProgress']; 
      if (!noShowableStatuses.includes(appointment.status)) {
          throw new HttpError(400, `Cannot mark appointment with status '${appointment.status}' as No Show.`);
      }
       // Optional Time Check: Only allow after expected end time?
       // if (dayjs(appointment.expectedAppointmentEndTime).isAfter(dayjs())) {
       //     throw new HttpError(400, 'Cannot mark appointment as No Show before its scheduled end time.');
       // }

      const previousStatus = appointment.status;

      // 3. Update Appointment Status
      const updatedAppt = await tx.appointment.update({
        where: { id: args.appointmentId },
        data: {
          status: 'noshow',
          // If it was 'InProgress', set realAppointmentEndTime to now or expectedEndTime?
          // For simplicity, 'noshow' implies it didn't effectively end.
          // realAppointmentEndTime: previousStatus === 'InProgress' ? (appointment.realAppointmentStartTime ? new Date() : appointment.expectedAppointmentEndTime) : undefined,
        },
        include: {
          service: { select: { duration: true, title: true } },
          place: { select: { name: true } },
          customerFolder: { select: { userId: true, provider: { select: { userId: true } } } }
        }
      });

      const customerUserId = updatedAppt.customerFolder?.userId;
    //   const providerUserId = updatedAppt.customerFolder?.provider?.userId;
      const serviceTitle = updatedAppt.service?.title;
      const placeName = updatedAppt.place?.name;    
      const notificationType = 'APPOINTMENT_NOSHOW_PROVIDER';
      const notificationTitle = 'You missed your appointment!';
      const notificationMessage = `Your appointment for ${serviceTitle} at ${placeName} has been marked as No Show. please contact the provider to reschedule or cancel if necessary.`;
      const notificationLink = `/my-appointments/${updatedAppt.id}`;
      if (notificationType && notificationTitle && notificationMessage) {
        await createNotificationEntry(tx.notification, {
          userId: customerUserId,
          type: notificationType,
          title: notificationTitle,
          message: notificationMessage, 
          link: notificationLink,
          actorId: providerUserId, // The provider/admin who made the update
        });
      }

      // Provider gets a credit back for a no-show (assuming they spent one to create/hold the slot)
      await tx.user.update({
          where: { id: providerUserId },
          data: { credits: { increment: 1 } }
      });
      // Customer does NOT get their credits back for a no-show.

      // 4. Create History Record
      await tx.appointmentHistory.create({
          data: {
              appointmentId: updatedAppt.id,
              changedByUserId: providerUserId,
              previousStatus: previousStatus,
              newStatus: 'noshow',
              previousStartTime: appointment.expectedAppointmentStartTime!, 
              previousEndTime: appointment.expectedAppointmentEndTime!,
              newStartTime: appointment.expectedAppointmentStartTime!, 
              newEndTime: appointment.expectedAppointmentEndTime!,   
              changeReason: "Marked as No Show by provider",
              previousMotifId: appointment.serviceId, newMotifId: appointment.serviceId,
              previousAgendaId: appointment.queueId ?? 0,
              newAgendaId: appointment.queueId ?? 0,
          }
      });

      console.log(`Appointment ${updatedAppt.id} marked as No Show by provider ${providerUserId}. Provider credit incremented.`);
      
      // If the no-show appointment was 'InProgress', subsequent appointments might need shifting
      // as if it completed "on time" (according to its expected duration from its real start time).
      if (previousStatus === 'InProgress' && appointment.realAppointmentStartTime && appointment.service) {
        //   const effectiveEndTimeForNoShow = dayjs(appointment.realAppointmentStartTime).add(appointment.service.duration, 'minutes').toDate();
        //   const shiftData: CompletedAppointmentForShift = {
        //       id: appointment.id,
        //       queueId: appointment.queueId!,
        //       service: { duration: appointment.service.duration },
        //       realAppointmentStartTime: appointment.realAppointmentStartTime,
        //       realAppointmentEndTime: effectiveEndTimeForNoShow, // Effective end time for shift calculation
        //       expectedAppointmentStartTime: appointment.expectedAppointmentStartTime!,
        //       expectedAppointmentEndTime: appointment.expectedAppointmentEndTime!, // This is the original, not effective end
        //       placeId: appointment.placeId,
        //       serviceId: appointment.serviceId,
        //       status: 'noshow', // original status that led to this
        //       notes: appointment.notes,
        //       type: 'completed',
        //   };
        //   console.log(`[noShowAppointment] InProgress appointment ${appointment.id} marked NoShow. Triggering shift based on expected duration from real start.`);
        //   await handleAppointmentShift(tx, shiftData, providerUserId);
      }

      return updatedAppt;
    });

    // ---- After the transaction: Notify queue change ----
    if (updatedAppointmentTransactionResult && updatedAppointmentTransactionResult.queueId) {
        const io = getIoInstance();
        if (io) {
            console.log(`[noShowAppointment Operation] Appointment ${updatedAppointmentTransactionResult.id} marked NO SHOW, triggering server-side queue notification for queue ${updatedAppointmentTransactionResult.queueId}`);
            await broadcastQueueStateUpdate(updatedAppointmentTransactionResult.queueId, context, io);
        } else {
            console.warn('[noShowAppointment Operation] Socket.IO instance not available. Cannot notify queue change.');
        }
    }

    return updatedAppointmentTransactionResult; // This was the intended return for the outer function

  } catch (error: any) {
    console.error(`Failed to mark appointment ${args.appointmentId} as No Show by provider ${providerUserId}:`, error);
    if (error instanceof HttpError) throw error;
    if (error.code === 'P2025') throw new HttpError(404, 'Record not found during update.');
    throw new HttpError(500, error.message || 'Failed to mark appointment as No Show.');
  }
};

// Query to get overflowed appointments
export const getOverflowedAppointments = async (_args: void, context: any): Promise<Appointment[]> => {
    if (!context.user?.id) {
        throw new HttpError(401, "User not authenticated");
    }

    const provider = await context.entities.SProvider.findUnique({
        where: { userId: context.user.id },
        select: { id: true },
    });

    if (!provider) {
        throw new HttpError(404, 'Provider profile not found for this user.');
    }

    return context.entities.Appointment.findMany({
        where: {
            customerFolder: {
                sProviderId: provider.id,
            },
            isOverflowed: true,
            overflowProcessingStatus: 'pending',
            status: { notIn: ['canceled', 'completed', 'noshow'] },
        },
        include: {
            service: true,
            customerFolder: {
                select: {
                    userId: true,
                    customer: {
                        select: { id: true, firstName: true, lastName: true }
                    }
                }
            },
            place: {
                select: { id: true, name: true }
            },
            queue: {
                select: { id: true, title: true }
            }
        },
        orderBy: {
            expectedAppointmentStartTime: 'asc',
        },
    });
};

// Input schema for handling overflowed appointments
const handleOverflowedAppointmentInputSchema = z.object({
    appointmentId: z.number().int().positive(),
    action: z.enum(['cancel', 'confirm_overtime', 'request_reschedule']), // Added request_reschedule
    reason: z.string().optional(),
    // New fields for rescheduling
    suggestedStartTime: z.date().optional(),
    suggestedEndTime: z.date().optional(),
});

type HandleOverflowedAppointmentData = z.infer<typeof handleOverflowedAppointmentInputSchema>;

// Action to handle overflowed appointments
export const handleOverflowedAppointment = async (
    rawArgs: HandleOverflowedAppointmentData,
    context: any
): Promise<Appointment> => {
    if (!context.user?.id) {
        throw new HttpError(401, "User not authenticated");
    }
    const providerUserId = context.user.id;

    const args = ensureArgsSchemaOrThrowHttpError(handleOverflowedAppointmentInputSchema, rawArgs);

    try {
        const result = await prisma.$transaction(async (tx) => {
            // 1. Get the appointment and verify ownership
            const appointment = await tx.appointment.findUnique({
                where: { id: args.appointmentId },
                include: {
                    customerFolder: {
                        select: {
                            userId: true,
                            provider: {
                                select: { userId: true }
                            }
                        }
                    },
                    service: { select: { pointsRequirements: true } }
                }
            });

            if (!appointment) {
                throw new HttpError(404, "Appointment not found");
            }

            if (appointment.customerFolder?.provider?.userId !== providerUserId) {
                throw new HttpError(403, "Not authorized to handle this appointment");
            }

            if (!appointment.isOverflowed || appointment.overflowProcessingStatus !== 'pending') {
                throw new HttpError(400, "Appointment is not in an overflow pending state");
            }

            const customerId = appointment.customerFolder.userId;
            const customerCreditRefund = appointment.service?.pointsRequirements || 0;

            let updatedAppointment;
            
            if (args.action === 'cancel') {
                // Cancel the appointment and refund customer credits
                updatedAppointment = await tx.appointment.update({
                    where: { id: args.appointmentId },
                    data: {
                        status: 'canceled',
                        overflowProcessingStatus: 'canceled',
                        overflowProcessedAt: new Date(),
                        canceledAt: new Date(),
                    }
                });

                // Refund customer credits
                await tx.user.update({
                    where: { id: customerId },
                    data: { credits: { increment: customerCreditRefund } }
                });

                // Deduct provider credits as penalty (1 credit)
                await tx.user.update({
                    where: { id: providerUserId },
                    data: { credits: { decrement: 1 } }
                });

            } else if (args.action === 'confirm_overtime') {
                // Mark the appointment as confirmed for overtime
                updatedAppointment = await tx.appointment.update({
                    where: { id: args.appointmentId },
                    data: {
                        overflowProcessingStatus: 'confirmed_overtime',
                        overflowProcessedAt: new Date(),
                    }
                });
            } else if (args.action === 'request_reschedule') {
                if (!args.suggestedStartTime || !args.suggestedEndTime) {
                    throw new HttpError(400, "Suggested times are required for rescheduling");
                }

                // Create reschedule request
                await tx.rescheduleRequest.create({
                    data: {
                        appointmentId: args.appointmentId,
                        requestedById: providerUserId,
                        respondedById: customerId,
                        suggestedStartTime: args.suggestedStartTime,
                        suggestedEndTime: args.suggestedEndTime,
                        reason: args.reason,
                        status: 'pending',
                        expiresAt: dayjs().add(24, 'hours').toDate(), // Request expires in 24 hours
                    }
                });

                // Update appointment status
                updatedAppointment = await tx.appointment.update({
                    where: { id: args.appointmentId },
                    data: {
                        overflowProcessingStatus: 'reschedule_requested',
                        overflowProcessedAt: new Date(),
                    }
                });
            }

            // Create history record
            await tx.appointmentHistory.create({
                data: {
                    appointmentId: args.appointmentId,
                    changedByUserId: providerUserId,
                    previousStatus: appointment.status,
                    newStatus: updatedAppointment!.status,
                    previousStartTime: appointment.expectedAppointmentStartTime!,
                    previousEndTime: appointment.expectedAppointmentEndTime!,
                    newStartTime: updatedAppointment!.expectedAppointmentStartTime!,
                    newEndTime: updatedAppointment!.expectedAppointmentEndTime!,
                    changeReason: args.action === 'cancel'
                        ? `Appointment canceled due to overflow: ${args.reason || 'No reason provided'}`
                        : args.action === 'confirm_overtime'
                            ? `Provider confirmed overtime work for overflowed appointment`
                            : `Rescheduled: ${args.reason || 'No reason provided'}`,
                    previousMotifId: appointment.serviceId,
                    newMotifId: appointment.serviceId,
                    previousAgendaId: appointment.queueId ?? 0,
                    newAgendaId: appointment.queueId ?? 0,
                }
            });

            return updatedAppointment!;
        });

        return result;

    } catch (error: any) {
        console.error(`Failed to handle overflowed appointment ${args.appointmentId}:`, error);
        if (error instanceof HttpError) throw error;
        throw new HttpError(500, error.message || "Failed to process overflowed appointment");
    }
};

// Query to get rescheduling requests
export const getRescheduleRequests = async (_args: void, context: any): Promise<any[]> => {
    if (!context.user?.id) {
        throw new HttpError(401, "User not authenticated");
    }
    const userId = context.user.id;

    return context.entities.RescheduleRequest.findMany({
        where: {
            OR: [
                // { requestedById: userId },
                { respondedById: userId },
            ],
            status: { not: 'expired' }
        },
        include: {
            appointment: {
                include: {
                    service: true,
                    customerFolder: {
                        include: {
                            customer: {
                                select: { id: true, firstName: true, lastName: true }
                            }
                        }
                    },
                    place: true,
                    queue: true
                }
            },
            requestedBy: {
                select: { id: true, firstName: true, lastName: true }
            },
            respondedBy: {
                select: { id: true, firstName: true, lastName: true }
            }
        },
        orderBy: { createdAt: 'desc' }
    });
};

// Input schema for creating a reschedule request
const createRescheduleRequestSchema = z.object({
    appointmentId: z.number().int().positive(),
    suggestedStartTime: z.date(),
    suggestedEndTime: z.date(),
    reason: z.string().optional(),
}).refine(data => data.suggestedEndTime > data.suggestedStartTime, {
    message: "End time must be after start time",
    path: ["suggestedEndTime"],
});

// Action to create a reschedule request
export const createRescheduleRequest = async (
    rawArgs: z.infer<typeof createRescheduleRequestSchema>,
    context: any
): Promise<any> => {
    if (!context.user?.id) {
        throw new HttpError(401, "User not authenticated");
    }
    const userId = context.user.id;
    
    const args = ensureArgsSchemaOrThrowHttpError(createRescheduleRequestSchema, rawArgs);

    try {
        return await prisma.$transaction(async (tx) => {
            // Get appointment and verify access rights
            const appointment = await tx.appointment.findUnique({
                where: { id: args.appointmentId },
                include: {
                    customerFolder: {
                        include: {
                            provider: { select: { userId: true } },
                            customer: { select: { id: true } }
                        }
                    }
                }
            });

            console.log('appointment', appointment);

            if (!appointment) {
                throw new HttpError(404, "Appointment not found");
            }

            // Determine if user is the provider or customer
            const isProvider = appointment.customerFolder.provider.userId === userId;
            const isCustomer = appointment.customerFolder.customer.id === userId;

            console.log('isProvider', isProvider);
            console.log('isCustomer', isCustomer);

            if (!isProvider && !isCustomer) {
                throw new HttpError(403, "Not authorized to request reschedule for this appointment");
            }

            // Set the responderId based on who's making the request
            const responderId = isProvider 
                ? appointment.customerFolder.customer.id 
                : appointment.customerFolder.provider.userId;

            console.log(`Creating RescheduleRequest: requestedById=${userId}, isProvider=${isProvider}, isCustomer=${isCustomer}, determinedResponderId=${responderId}, customerIdFromAppointment=${appointment.customerFolder.customer.id}, providerUserIdFromAppointment=${appointment.customerFolder.provider.userId}`); // Added more details

            // Create the reschedule request
            const request = await tx.rescheduleRequest.create({
                data: {
                    appointmentId: args.appointmentId,
                    requestedById: userId,
                    respondedById: responderId,
                    suggestedStartTime: args.suggestedStartTime,
                    suggestedEndTime: args.suggestedEndTime,
                    reason: args.reason,
                    expiresAt: dayjs().add(24, 'hours').toDate(),
                }
            });

            return request;
        });
    } catch (error: any) {
        console.error("Failed to create reschedule request:", error);
        if (error instanceof HttpError) throw error;
        throw new HttpError(500, error.message || "Failed to create reschedule request");
    }
};

// Input schema for responding to a reschedule request
const respondToRescheduleRequestSchema = z.object({
    requestId: z.number().int().positive(),
    accept: z.boolean(),
    responseNote: z.string().optional(),
});

// Action to respond to a reschedule request
export const respondToRescheduleRequest = async (
    rawArgs: z.infer<typeof respondToRescheduleRequestSchema>,
    context: any
): Promise<any> => {
    if (!context.user?.id) {
        throw new HttpError(401, "User not authenticated");
    }
    const userId = context.user.id;
    
    const args = ensureArgsSchemaOrThrowHttpError(respondToRescheduleRequestSchema, rawArgs);

    try {
        return await prisma.$transaction(async (tx) => {
            // Get the request and verify it's pending and not expired
            const request = await tx.rescheduleRequest.findUnique({
                where: { id: args.requestId },
                include: {
                    appointment: {
                        include: {
                            customerFolder: true,
                            service: true
                        }
                    }
                }
            });

            if (!request) {
                throw new HttpError(404, "Reschedule request not found");
            }

            if (request.status !== 'pending') {
                throw new HttpError(400, "Request has already been processed");
            }

            if (dayjs().isAfter(request.expiresAt)) {
                throw new HttpError(400, "Request has expired");
            }

            if (request.respondedById !== userId) {
                throw new HttpError(403, "Not authorized to respond to this request");
            }

            // Update the request status
            const updatedRequest = await tx.rescheduleRequest.update({
                where: { id: args.requestId },
                data: {
                    status: args.accept ? 'accepted' : 'rejected',
                    responseTime: new Date(),
                    responseNote: args.responseNote
                }
            });

            if (args.accept) {
                // Update the appointment with new times
                const updatedAppointment = await tx.appointment.update({
                    where: { id: request.appointmentId },
                    data: {
                        expectedAppointmentStartTime: request.suggestedStartTime,
                        expectedAppointmentEndTime: request.suggestedEndTime,
                        isOverflowed: false, // Clear overflow status if it was set
                        overflowProcessingStatus: null,
                        overflowProcessedAt: new Date()
                    }
                });

                // Create appointment history record
                await tx.appointmentHistory.create({
                    data: {
                        appointmentId: request.appointmentId,
                        changedByUserId: userId,
                        previousStartTime: request.appointment.expectedAppointmentStartTime!,
                        previousEndTime: request.appointment.expectedAppointmentEndTime!,
                        newStartTime: request.suggestedStartTime,
                        newEndTime: request.suggestedEndTime,
                        previousStatus: request.appointment.status,
                        newStatus: request.appointment.status,
                        changeReason: `Rescheduled: ${request.reason || 'No reason provided'}`,
                        previousMotifId: request.appointment.serviceId,
                        newMotifId: request.appointment.serviceId,
                        previousAgendaId: request.appointment.queueId ?? 0,
                        newAgendaId: request.appointment.queueId ?? 0,
                    }
                });
            }

            return updatedRequest;
        });
    } catch (error: any) {
        console.error("Failed to respond to reschedule request:", error);
        if (error instanceof HttpError) throw error;
        throw new HttpError(500, error.message || "Failed to process reschedule response");
    }
};

// --- Queue Limit Utility Function ---

/**
 * Checks if a user can create more queues based on their subscription limit
 * @param userId - The user ID to check
 * @param context - The application context with entities
 * @returns Object with canCreate flag and current/max counts
 */
export const checkQueueCreationLimit = async (userId: string, context: any): Promise<{
    canCreate: boolean;
    currentCount: number;
    maxAllowed: number;
    message?: string;
}> => {
    // Get user's queue limit
    const user = await context.entities.User.findUnique({
        where: { id: userId },
        select: { queues: true },
    });
    if (!user) {
        throw new HttpError(404, 'User not found.');
    }

    // Get provider for this user
    const provider = await context.entities.SProvider.findUnique({
        where: { userId: userId },
        select: { id: true },
    });
    if (!provider) {
        throw new HttpError(404, 'Service provider profile not found.');
    }

    // Get current queue count for this provider
    const currentCount = await context.entities.Queue.count({
        where: {
            sProvidingPlace: {
                sProviderId: provider.id
            }
        }
    });

    const canCreate = currentCount < user.queues;
    const message = canCreate 
        ? undefined 
        : `Queue limit reached. You can create up to ${user.queues} queue(s). Please upgrade your subscription to create more queues.`;

    return {
        canCreate,
        currentCount,
        maxAllowed: user.queues,
        message
    };
};

// --- Create Queue Action ---

const createQueueInputSchema = z.object({
    title: z.string().min(1, "Queue title is required"),
    sProvidingPlaceId: z.number().int().positive("Valid location is required"),
    isActive: z.boolean().default(true),
    serviceIds: z.array(z.number().int().positive()).min(1, "At least one service must be assigned"),
    openingHours: z.array(z.object({
        dayOfWeek: z.string().min(1, "Day of week is required"),
        isActive: z.boolean().default(true),
        hours: z.array(z.object({
            timeFrom: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format"),
            timeTo: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format"),
        })),
    })).optional(),
});

type CreateQueueData = z.infer<typeof createQueueInputSchema>;

export const createProviderQueue = async (rawArgs: CreateQueueData, context: any): Promise<Queue> => {
    if (!context.user) {
        throw new HttpError(401);
    }
    const args = ensureArgsSchemaOrThrowHttpError(createQueueInputSchema, rawArgs);
    const userId = context.user.id;

    // 1. Find provider ID and user queue limit
    const provider = await context.entities.SProvider.findUnique({
        where: { userId: userId },
        select: { id: true },
    });
    if (!provider) {
        throw new HttpError(404, 'Service provider profile not found.');
    }

    // 2. Check user's queue limit and current queue count
    const queueLimitCheck = await checkQueueCreationLimit(userId, context);
    if (!queueLimitCheck.canCreate) {
        throw new HttpError(403, queueLimitCheck.message || 'Queue limit exceeded.');
    }

    // 3. Verify provider owns the location
    const location = await context.entities.SProvidingPlace.findFirst({
        where: {
            id: args.sProvidingPlaceId,
            sProviderId: provider.id,
        },
        select: { id: true },
    });
    if (!location) {
        throw new HttpError(404, 'Location not found or you do not have permission to create queues for it.');
    }

    // 4. Verify all services belong to this provider
    const services = await context.entities.Service.findMany({
        where: {
            id: { in: args.serviceIds },
            sProviderId: provider.id,
        },
        select: { id: true },
    });
    if (services.length !== args.serviceIds.length) {
        throw new HttpError(400, 'One or more services do not belong to this provider.');
    }

    // 5. Create Queue
    try {
        const newQueue = await context.entities.Queue.create({
            data: {
                title: args.title,
                sProvidingPlaceId: args.sProvidingPlaceId,
                isActive: args.isActive,
                services: {
                    connect: services.map((service: any) => ({ id: service.id })),
                },
                // Note: Service-Queue relationship might be handled differently
                // depending on your schema. This assumes a many-to-many relationship.
            },
            include: {
                sProvidingPlace: {
                    select: { id: true, name: true }
                },
                services: {
                    select: { id: true, title: true }
                },
                openings: {
                    include: {
                        hours: true
                    }
                }
            }
        });

        // 6. Create opening hours if provided
        if (args.openingHours && args.openingHours.length > 0) {
            for (const openingHour of args.openingHours) {
                const queueOpening = await context.entities.QueueOpening.create({
                    data: {
                        queueId: newQueue.id,
                        dayOfWeek: openingHour.dayOfWeek,
                        isActive: openingHour.isActive,
                        hours: {
                            create: openingHour.hours.map((hour: any) => ({
                                timeFrom: hour.timeFrom,
                                timeTo: hour.timeTo,
                            })),
                        },
                    },
                    include: {
                        hours: true,
                    },
                });
            }
        }

        // 7. Store translation for queue title
        if (newQueue.title) {
            await translateAndStore(
                context.entities,
                String(newQueue.id),
                'Queue',
                'title',
                newQueue.title
            );
        }

        return newQueue;
    } catch (error: any) {
        console.error(`Failed to create Queue for provider ${provider.id}:`, error);
        throw new HttpError(500, error.message || 'Failed to create queue.');
    }
};

// --- Get Provider Queues Query ---

export const getProviderQueues = async (args: unknown, context: any): Promise<Queue[]> => {
    if (!context.user) {
        throw new HttpError(401);
    }
    const userId = context.user.id;

    // 1. Find provider ID
    const provider = await context.entities.SProvider.findUnique({
        where: { userId: userId },
        select: { id: true },
    });
    if (!provider) {
        return []; // No provider, no queues
    }

    // 2. Fetch queues linked to this provider through their locations
    const queues = await context.entities.Queue.findMany({
        where: {
            sProvidingPlace: {
                sProviderId: provider.id
            }
        },
        include: {
            sProvidingPlace: {
                select: { id: true, name: true }
            },
            services: {
                select: { id: true, title: true }
            },
            openings: {
                include: {
                    hours: true
                }
            }
        },
        orderBy: {
            title: 'asc',
        },
    });
    return queues;
};

// --- Get Queues by Location Query ---

const getQueuesByLocationInputSchema = z.object({
    locationId: z.number().int().positive("Valid location ID is required"),
    isActive: z.boolean().optional(),
    search: z.string().optional(),
    includeServices: z.boolean().default(false),
});

type GetQueuesByLocationData = z.infer<typeof getQueuesByLocationInputSchema>;

export const getQueuesByLocation = async (rawArgs: GetQueuesByLocationData, context: any): Promise<Queue[]> => {
    if (!context.user) {
        throw new HttpError(401);
    }
    const args = ensureArgsSchemaOrThrowHttpError(getQueuesByLocationInputSchema, rawArgs);
    const userId = context.user.id;

    // 1. Find provider ID
    const provider = await context.entities.SProvider.findUnique({
        where: { userId: userId },
        select: { id: true },
    });
    if (!provider) {
        throw new HttpError(404, 'Service provider profile not found.');
    }

    // 2. Verify provider owns the location
    const location = await context.entities.SProvidingPlace.findFirst({
        where: {
            id: args.locationId,
            sProviderId: provider.id,
        },
        select: { id: true },
    });
    if (!location) {
        throw new HttpError(404, 'Location not found or you do not have permission to access it.');
    }

    // 3. Build query filters
    const whereClause: any = {
        sProvidingPlaceId: args.locationId,
    };

    if (args.isActive !== undefined) {
        whereClause.isActive = args.isActive;
    }

    if (args.search) {
        whereClause.title = {
            contains: args.search,
            mode: 'insensitive'
        };
    }

    // 4. Fetch queues with optional service inclusion
    const includeClause: any = {
        sProvidingPlace: {
            select: { id: true, name: true }
        }
    };

    if (args.includeServices) {
        // Note: This depends on your schema structure for Queue-Service relationship
        // Adjust based on your actual schema
        includeClause.services = {
            select: {
                id: true,
                title: true,
                duration: true,
                color: true
            }
        };
    }

    // Always include opening hours
    includeClause.openings = {
        include: {
            hours: true
        }
    };

    const queues = await context.entities.Queue.findMany({
        where: whereClause,
        include: includeClause,
        orderBy: {
            title: 'asc',
        },
    });

    return queues;
};

// --- Update Provider Queue Action ---

const updateProviderQueueInputSchema = z.object({
    queueId: z.number().int().positive("Valid queue ID is required"),
    title: z.string().min(1, "Queue title is required").optional(),
    isActive: z.boolean().optional(),
    serviceIds: z.array(z.number().int().positive()).min(1, "At least one service must be assigned").optional(),
    openingHours: z.array(z.object({
        dayOfWeek: z.string().min(1, "Day of week is required"),
        isActive: z.boolean().default(true),
        hours: z.array(z.object({
            timeFrom: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format"),
            timeTo: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format"),
        })),
    })).optional(),
});

type UpdateProviderQueueData = z.infer<typeof updateProviderQueueInputSchema>;

export const updateProviderQueue = async (rawArgs: UpdateProviderQueueData, context: any): Promise<Queue> => {
    if (!context.user) {
        throw new HttpError(401);
    }

    const args = ensureArgsSchemaOrThrowHttpError(updateProviderQueueInputSchema, rawArgs);
    const userId = context.user.id;

    // 1. Find the SProvider ID for the logged-in user
    const provider = await context.entities.SProvider.findUnique({
        where: { userId: userId },
        select: { id: true },
    });

    if (!provider) {
        throw new HttpError(404, 'Service provider profile not found for this user.');
    }

    // 2. Verify ownership: Check if the queue exists and belongs to the user's provider
    const queueToUpdate = await context.entities.Queue.findFirst({
        where: {
            id: args.queueId,
            sProvidingPlace: {
                provider: { // Check the related SProvider through SProvidingPlace
                    userId: userId, // Matches the logged-in user
                },
            },
        },
        include: {
            services: true,
            sProvidingPlace: true,
            openings: {
                include: {
                    hours: true
                }
            }
        }
    });

    if (!queueToUpdate) {
        throw new HttpError(404, 'Queue not found or you do not have permission to update it.');
    }

    // 3. If serviceIds are provided, verify they belong to the provider
    if (args.serviceIds) {
        const services = await context.entities.Service.findMany({
            where: {
                id: { in: args.serviceIds },
                sProviderId: provider.id,
            },
        });

        if (services.length !== args.serviceIds.length) {
            throw new HttpError(400, 'One or more services not found or do not belong to this provider.');
        }
    }

    // 4. Check if a queue with the same title already exists for this provider (if title is being updated)
    if (args.title && args.title !== queueToUpdate.title) {
        const existingQueue = await context.entities.Queue.findFirst({
            where: {
                title: args.title,
                sProvidingPlace: {
                    sProviderId: provider.id,
                },
                id: { not: args.queueId }, // Exclude current queue
            },
        });

        if (existingQueue) {
            throw new HttpError(400, `A queue named "${args.title}" already exists for this provider.`);
        }
    }

    // 5. Update the Queue
    try {
        const updatedQueue = await context.entities.Queue.update({
            where: { id: args.queueId },
            data: {
                ...(args.title && { title: args.title }),
                ...(args.isActive !== undefined && { isActive: args.isActive }),
                ...(args.serviceIds && {
                    services: {
                        set: [], // Clear existing services
                        connect: args.serviceIds.map(id => ({ id })), // Connect new services
                    },
                }),
            },
            include: {
                services: true,
                sProvidingPlace: true,
                openings: {
                    include: {
                        hours: true
                    }
                }
            }
        });

        // 6. Update opening hours if provided
        if (args.openingHours !== undefined) {
            updatedQueue.openings.forEach(async (opening: any) => {
                for (const hour of opening.hours) {
                    await context.entities.QueueOpeningHours.delete({
                        where: { id: hour.id }
                    });
                }
                await context.entities.QueueOpening.delete({
                    where: { id: opening.id }
                });  
            });

            // // Delete existing opening hours
            // await context.entities.QueueOpening.deleteMany({
            //     where: { queueId: args.queueId }
            // });

            // Create new opening hours
            if (args.openingHours.length > 0) {
                for (const openingHour of args.openingHours) {
                    await context.entities.QueueOpening.create({
                        data: {
                            queueId: args.queueId,
                            dayOfWeek: openingHour.dayOfWeek,
                            isActive: openingHour.isActive,
                            hours: {
                                create: openingHour.hours.map((hour: any) => ({
                                    timeFrom: hour.timeFrom,
                                    timeTo: hour.timeTo,
                                })),
                            },
                        },
                        include: {
                            hours: true,
                        },
                    });
                }
            }
        }

        // 7. Update translation if title changed
        if (args.title && args.title !== queueToUpdate.title) {
            await translateAndStore(
                context.entities,
                String(updatedQueue.id),
                'Queue',
                'title',
                updatedQueue.title
            );
        }

        return updatedQueue;
    } catch (error: any) {
        console.error(`Failed to update Queue ${args.queueId} for user ${userId}:`, error);
        throw new HttpError(500, error.message || 'Failed to update queue.');
    }
};

// --- Delete Provider Queue Action ---

const deleteProviderQueueInputSchema = z.object({
    queueId: z.number().int().positive("Valid queue ID is required"),
});

type DeleteProviderQueueData = z.infer<typeof deleteProviderQueueInputSchema>;

export const deleteProviderQueue = async (rawArgs: DeleteProviderQueueData, context: any): Promise<{ success: boolean; message: string }> => {
    if (!context.user) {
        throw new HttpError(401);
    }

    const args = ensureArgsSchemaOrThrowHttpError(deleteProviderQueueInputSchema, rawArgs);
    const userId = context.user.id;

    // 1. Find the SProvider ID for the logged-in user
    const provider = await context.entities.SProvider.findUnique({
        where: { userId: userId },
        select: { id: true },
    });

    if (!provider) {
        throw new HttpError(404, 'Service provider profile not found for this user.');
    }

    // 2. Verify ownership: Check if the queue exists and belongs to the user's provider
    const queueToDelete = await context.entities.Queue.findFirst({
        where: {
            id: args.queueId,
            sProvidingPlace: {
                provider: { // Check the related SProvider through SProvidingPlace
                    userId: userId, // Matches the logged-in user
                },
            },
        },
        include: {
            openings: {
                include: {
                    hours: true
                }
            },
            appointments: {
                where: {
                    status: { notIn: ['canceled', 'completed', 'noshow'] },
                    expectedAppointmentStartTime: { gte: new Date() } // Future appointments
                },
                select: { id: true }
            }
        }
    });

    if (!queueToDelete) {
        throw new HttpError(404, 'Queue not found or you do not have permission to delete it.');
    }

    // 3. Check for dependencies that prevent deletion
    const futureAppointments = queueToDelete.appointments;

    if (futureAppointments.length > 0) {
        throw new HttpError(409, `Cannot delete queue: It has ${futureAppointments.length} future appointment(s). Please cancel or reschedule the appointments first.`);
    }

    // 4. Delete the Queue
    try {
        for (const opening of queueToDelete.openings) {
            for (const hour of opening.hours) {
                await context.entities.QueueOpeningHours.delete({
                    where: { id: hour.id }
                });
            }
            await context.entities.QueueOpening.delete({
                where: { id: opening.id }
            });
        }

        await context.entities.Queue.delete({
            where: { id: args.queueId },
        });

        return {
            success: true,
            message: 'Queue deleted successfully'
        };
    } catch (error: any) {
        console.error(`Failed to delete Queue ${args.queueId} for user ${userId}:`, error);
        if (error.code === 'P2025') { // Record to delete does not exist
            throw new HttpError(404, 'Queue not found (already deleted?).');
        }
        throw new HttpError(500, error.message || 'Failed to delete queue.');
    }
};

// --- Get Queue Services Action ---

const getQueueServicesInputSchema = z.object({
    queueId: z.number().int().positive("Valid queue ID is required"),
});

type GetQueueServicesData = z.infer<typeof getQueueServicesInputSchema>;

export const getQueueServices = async (rawArgs: GetQueueServicesData, context: any): Promise<any[]> => {
    if (!context.user) {
        throw new HttpError(401);
    }

    const args = ensureArgsSchemaOrThrowHttpError(getQueueServicesInputSchema, rawArgs);
    const userId = context.user.id;

    // 1. Find the SProvider ID for the logged-in user
    const provider = await context.entities.SProvider.findUnique({
        where: { userId: userId },
        select: { id: true },
    });

    if (!provider) {
        throw new HttpError(404, 'Service provider profile not found for this user.');
    }

    // 2. Verify ownership: Check if the queue exists and belongs to the user's provider
    const queue = await context.entities.Queue.findFirst({
        where: {
            id: args.queueId,
            sProvidingPlace: {
                provider: { // Check the related SProvider through SProvidingPlace
                    userId: userId, // Matches the logged-in user
                },
            },
        },
        include: {
            services: {
                select: {
                    id: true,
                    title: true,
                    duration: true,
                    color: true
                }
            }
        }
    });

    if (!queue) {
        throw new HttpError(404, 'Queue not found or you do not have permission to access it.');
    }

    // 3. Return the services assigned to this queue
    return queue.services.map((service: any) => ({
        queueId: args.queueId,
        serviceId: service.id,
        service: {
            id: service.id,
            title: service.title,
            duration: service.duration,
            color: service.color
        }
    }));
};

// --- Assign Service to Queue Action ---

const assignServiceToQueueInputSchema = z.object({
    queueId: z.number().int().positive("Valid queue ID is required"),
    serviceId: z.number().int().positive("Valid service ID is required"),
});

type AssignServiceToQueueData = z.infer<typeof assignServiceToQueueInputSchema>;

export const assignServiceToQueue = async (rawArgs: AssignServiceToQueueData, context: any): Promise<any> => {
    if (!context.user) {
        throw new HttpError(401);
    }

    const args = ensureArgsSchemaOrThrowHttpError(assignServiceToQueueInputSchema, rawArgs);
    const userId = context.user.id;

    // 1. Find the SProvider ID for the logged-in user
    const provider = await context.entities.SProvider.findUnique({
        where: { userId: userId },
        select: { id: true },
    });

    if (!provider) {
        throw new HttpError(404, 'Service provider profile not found for this user.');
    }

    // 2. Verify ownership: Check if the queue exists and belongs to the user's provider
    const queue = await context.entities.Queue.findFirst({
        where: {
            id: args.queueId,
            sProvidingPlace: {
                provider: { // Check the related SProvider through SProvidingPlace
                    userId: userId, // Matches the logged-in user
                },
            },
        },
        include: {
            services: {
                select: { id: true }
            }
        }
    });

    if (!queue) {
        throw new HttpError(404, 'Queue not found or you do not have permission to access it.');
    }

    // 3. Verify the service belongs to the provider
    const service = await context.entities.Service.findFirst({
        where: {
            id: args.serviceId,
            sProviderId: provider.id,
        },
        select: {
            id: true,
            title: true,
            duration: true,
            color: true
        }
    });

    if (!service) {
        throw new HttpError(404, 'Service not found or you do not have permission to access it.');
    }

    // 4. Check if service is already assigned to this queue
    const isAlreadyAssigned = queue.services.some((s: any) => s.id === args.serviceId);
    if (isAlreadyAssigned) {
        throw new HttpError(409, 'Service is already assigned to this queue.');
    }

    // 5. Assign the service to the queue
    try {
        await context.entities.Queue.update({
            where: { id: args.queueId },
            data: {
                services: {
                    connect: { id: args.serviceId }
                }
            }
        });

        return {
            queueId: args.queueId,
            serviceId: args.serviceId,
            service: {
                id: service.id,
                title: service.title,
                duration: service.duration,
                color: service.color
            }
        };
    } catch (error: any) {
        console.error(`Failed to assign service ${args.serviceId} to queue ${args.queueId}:`, error);
        throw new HttpError(500, error.message || 'Failed to assign service to queue.');
    }
};

// --- Remove Service from Queue Action ---

const removeServiceFromQueueInputSchema = z.object({
    queueId: z.number().int().positive("Valid queue ID is required"),
    serviceId: z.number().int().positive("Valid service ID is required"),
});

type RemoveServiceFromQueueData = z.infer<typeof removeServiceFromQueueInputSchema>;

export const removeServiceFromQueue = async (rawArgs: RemoveServiceFromQueueData, context: any): Promise<{ success: boolean; message: string }> => {
    if (!context.user) {
        throw new HttpError(401);
    }

    const args = ensureArgsSchemaOrThrowHttpError(removeServiceFromQueueInputSchema, rawArgs);
    const userId = context.user.id;

    // 1. Find the SProvider ID for the logged-in user
    const provider = await context.entities.SProvider.findUnique({
        where: { userId: userId },
        select: { id: true },
    });

    if (!provider) {
        throw new HttpError(404, 'Service provider profile not found for this user.');
    }

    // 2. Verify ownership: Check if the queue exists and belongs to the user's provider
    const queue = await context.entities.Queue.findFirst({
        where: {
            id: args.queueId,
            sProvidingPlace: {
                provider: { // Check the related SProvider through SProvidingPlace
                    userId: userId, // Matches the logged-in user
                },
            },
        },
        include: {
            services: {
                select: { id: true }
            }
        }
    });

    if (!queue) {
        throw new HttpError(404, 'Queue not found or you do not have permission to access it.');
    }

    // 3. Check if service is assigned to this queue
    const isAssigned = queue.services.some((s: any) => s.id === args.serviceId);
    if (!isAssigned) {
        throw new HttpError(404, 'Service is not assigned to this queue.');
    }

    // 4. Check if this is the last service in the queue
    if (queue.services.length === 1) {
        throw new HttpError(409, 'Cannot remove the last service from a queue. A queue must have at least one service.');
    }

    // 5. Remove the service from the queue
    try {
        await context.entities.Queue.update({
            where: { id: args.queueId },
            data: {
                services: {
                    disconnect: { id: args.serviceId }
                }
            }
        });

        return {
            success: true,
            message: 'Service removed from queue successfully'
        };
    } catch (error: any) {
        console.error(`Failed to remove service ${args.serviceId} from queue ${args.queueId}:`, error);
        throw new HttpError(500, error.message || 'Failed to remove service from queue.');
    }
};