import { type Request, type Response } from 'express';
import { HttpError } from 'wasp/server';
import { type File } from 'wasp/entities';
import * as z from 'zod';
import { createFile } from 'wasp/server/operations';
import { ALLOWED_FILE_TYPES } from './validation';

// Validation schemas
const uploadFileApiSchema = z.object({
  fileType: z.enum(ALLOWED_FILE_TYPES),
  fileName: z.string().min(1, 'File name is required'),
});

const getUserFilesApiSchema = z.object({
  page: z.coerce.number().int().positive().optional().default(1),
  limit: z.coerce.number().int().positive().max(100).optional().default(10),
});

type UploadFileApiInput = z.infer<typeof uploadFileApiSchema>;
type GetUserFilesApiInput = z.infer<typeof getUserFilesApiSchema>;

/**
 * POST /api/auth/files/upload
 * Upload a file for the authenticated user
 */
export const handleFileUpload = async (req: Request, res: Response, context: any) => {
  try {
    // Check authentication
    if (!context.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    // Validate request body
    const validationResult = uploadFileApiSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        message: 'Invalid request data',
        errors: validationResult.error.format()
      });
    }

    const { fileType, fileName } = validationResult.data;

    // Use existing createFile operation
    const result = await createFile({ fileType, fileName }, context);

    return res.status(200).json({
      success: true,
      message: 'File upload URL generated successfully',
      data: {
        uploadUrl: result.s3UploadUrl,
        uploadFields: result.s3UploadFields,
        fileName,
        fileType
      }
    });

  } catch (error: any) {
    console.error('[API] Error in file upload:', error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error.message || 'Failed to generate file upload URL';
    return res.status(statusCode).json({
      success: false,
      message
    });
  }
};

/**
 * GET /api/auth/files
 * Get all files belonging to the authenticated user
 */
export const handleGetUserFiles = async (req: Request, res: Response, context: any) => {
  try {
    // Check authentication
    if (!context.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    // Validate query parameters
    const validationResult = getUserFilesApiSchema.safeParse(req.query);
    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        message: 'Invalid query parameters',
        errors: validationResult.error.format()
      });
    }

    const { page, limit } = validationResult.data;

    // Get all files for the user using Prisma directly
    const allFiles = await context.entities.File.findMany({
      where: {
        user: {
          id: context.user.id,
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Implement pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedFiles = allFiles.slice(startIndex, endIndex);

    // Format file data for API response
    const formattedFiles = paginatedFiles.map((file: File) => ({
      id: file.id,
      name: file.name,
      type: file.type,
      key: file.key,
      uploadUrl: file.uploadUrl,
      createdAt: file.createdAt,
      userId: file.userId
    }));

    // Calculate pagination metadata
    const totalFiles = allFiles.length;
    const totalPages = Math.ceil(totalFiles / limit);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    return res.status(200).json({
      success: true,
      message: 'Files retrieved successfully',
      data: {
        files: formattedFiles,
        pagination: {
          page,
          limit,
          total: totalFiles,
          totalPages,
          hasNext,
          hasPrev
        }
      }
    });

  } catch (error: any) {
    console.error('[API] Error getting user files:', error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error.message || 'Failed to retrieve user files';
    return res.status(statusCode).json({
      success: false,
      message
    });
  }
};

/**
 * GET /api/auth/files/:fileId
 * Get a specific file by ID for the authenticated user
 */
export const handleGetUserFileById = async (req: Request, res: Response, context: any) => {
  try {
    // Check authentication
    if (!context.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const fileId = req.params.fileId;

    if (!fileId) {
      return res.status(400).json({
        success: false,
        message: 'File ID is required'
      });
    }

    // Get the file by ID for the authenticated user
    const file = await context.entities.File.findFirst({
      where: {
        id: fileId,
        userId: context.user.id, // Ensure the file belongs to the authenticated user
      },
    });

    if (!file) {
      return res.status(404).json({
        success: false,
        message: 'File not found or access denied'
      });
    }

    // Format file data for API response
    const formattedFile = {
      id: file.id,
      name: file.name,
      type: file.type,
      key: file.key,
      uploadUrl: file.uploadUrl,
      createdAt: file.createdAt,
      userId: file.userId
    };

    return res.status(200).json({
      success: true,
      message: 'File retrieved successfully',
      data: formattedFile
    });

  } catch (error: any) {
    console.error('[API] Error getting user file by ID:', error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error.message || 'Failed to retrieve file';
    return res.status(statusCode).json({
      success: false,
      message
    });
  }
};
