# Provider Queues API Dynamic Translation Implementation

## Overview
Successfully implemented dynamic translation for Provider Queues API endpoints based on user's preferred language using the Translation table in the database.

## Implementation Details

### Modified Files
- `app/src/provider/mobile/handlers/queues.ts`

### Key Changes

#### 1. Added Translation Imports
```typescript
import { getTranslatedString, translateAndStore } from '../../../server/translations';
import { LanguageCode } from '@prisma/client';
import { prisma } from 'wasp/server';
```

#### 2. Created Translation Helper Functions

**Translation Retrieval Function:**
```typescript
const translateQueueFields = async (
  queue: any, 
  targetLanguage: LanguageCode, 
  context: any
) => {
  const promises: Promise<string | null>[] = [];
  
  // Translate Queue fields
  promises.push(
    getTranslatedString(prisma, 'Queue', String(queue.id), 'title', targetLanguage)
  );
  
  const [translatedTitle] = await Promise.all(promises);
    
  return {
    title: translatedTitle || queue.title
  };
};
```

**Translation Storage Function:**
```typescript
const saveQueueTranslations = async (queue: any, context: any) => {
  // Save translations for Queue fields
  if (queue.title) {
    await translateAndStore(prisma, String(queue.id), 'Queue', 'title', queue.title);
  }
};
```

#### 3. Updated API Handlers

**Modified Handlers:**
- `getProviderQueues` - Get all provider queues with translations
- `createProviderQueue` - Create queue with automatic translation storage
- `updateProviderQueue` - Update queue with translation updates
- `getQueuesByLocation` - Get queues by location with translations

**Translation Logic Added:**
1. Get user's preferred language from `context.user.preferedLanguage`
2. Fallback to `LanguageCode.EN` if no preference set
3. Apply translations using parallel Promise.all execution
4. Save translations automatically during create/update operations
5. Use translated values in response with fallback to original values

### Translation Table Mapping

#### Queue Fields
- `title` → Translation table: `tableName='Queue'`, `refField='title'`

### Enhanced API Endpoints

#### GET /api/auth/providers/queues
- ✅ Retrieves all provider queues
- ✅ **NEW**: Returns translated content based on user's preferred language
- ✅ **NEW**: Parallel translation processing for optimal performance

#### POST /api/auth/providers/queues
- ✅ Creates new provider queue
- ✅ **NEW**: Automatically saves translations for all translatable fields
- ✅ **NEW**: Returns translated response based on user's preferred language

#### PUT /api/auth/providers/queues/:id
- ✅ Updates existing provider queue
- ✅ **NEW**: Automatically saves translations for updated fields
- ✅ **NEW**: Returns translated response based on user's preferred language

#### GET /api/auth/providers/locations/:locationId/queues
- ✅ Retrieves queues by location
- ✅ **NEW**: Returns translated content based on user's preferred language

### Translation Storage Process

1. **Automatic Language Detection**: Uses `detectLanguage()` to identify source language
2. **Multi-language Translation**: Automatically translates to all supported languages (EN, AR, FR)
3. **Database Storage**: Stores all translations in the Translation table with proper references
4. **Error Handling**: Graceful error handling that doesn't break main operations

### Performance Optimizations

1. **Parallel Translation Queries**: Uses `Promise.all` to execute all translation queries simultaneously
2. **Batch Processing**: Processes all queues' translations in parallel
3. **Graceful Fallbacks**: Falls back to original values if translations are unavailable
4. **Conditional Processing**: Only processes translations when needed

### Error Handling

1. **Missing Translations**: Falls back to original field values
2. **Translation Service Errors**: Logs errors but doesn't break API response
3. **No Preferred Language**: Defaults to English (EN)
4. **Queue Errors**: Maintains API functionality even if translation fails

### API Response Structure

The APIs now return dynamically translated content based on user's `preferedLanguage`:

```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "title": "Translated Queue Title",
      "isActive": true,
      "sProvidingPlaceId": 1,
      "services": [
        {
          "id": 1,
          "title": "Service Title"
        }
      ],
      "openingHours": [
        {
          "dayOfWeek": "Monday",
          "isActive": true,
          "hours": [
            {
              "timeFrom": "09:00",
              "timeTo": "17:00"
            }
          ]
        }
      ]
    }
  ],
  "message": "Provider queues retrieved successfully"
}
```

### Fields with Translation Support

**Translatable Fields:**
- `title` - Queue name/title

**Non-translatable Fields:**
- `isActive` - Queue status
- `sProvidingPlaceId` - Location reference
- `services` - Associated services (handled separately)
- `openingHours` - Operating hours

### Integration with Queue Management

The translation implementation integrates seamlessly with:
- **Queue Limits**: Translation doesn't affect queue creation limits
- **Service Assignment**: Service titles can be translated separately
- **Opening Hours**: Schedule management remains unaffected
- **Location Association**: Queue-location relationships maintained

### Testing Status
- ✅ Compilation successful
- ✅ TypeScript validation passed
- ✅ Wasp development server starts without errors
- ✅ Translation storage integration working
- ✅ All API endpoints maintain backward compatibility
- ✅ Translation retrieval and storage working correctly

### Future Enhancements
1. Add translation support for service titles in queue responses
2. Implement bulk translation updates when queue data changes
3. Add translation management endpoints for providers
4. Consider adding translation status indicators in responses
5. Add translation deletion when queues are removed

## Usage
The APIs now provide complete translation lifecycle management:

1. **Create Queue**: POST request automatically saves and translates all text fields
2. **Update Queue**: PUT request automatically updates translations for changed fields  
3. **Retrieve Queues**: GET requests return content in user's preferred language
4. **Location Filtering**: Location-based queue retrieval supports translations
5. **Automatic Processing**: No additional parameters required - translation is handled transparently

### Example Workflow
1. Provider creates queue with Arabic title → System detects Arabic, translates to EN/FR, stores all versions
2. English user requests queues → System returns English translations
3. Provider updates queue title → System updates translations for the changed field
4. French user requests queues → System returns French translations

### Business Benefits
- **Multilingual Support**: Providers can organize queues in their preferred language
- **Customer Experience**: Customers see queue names in their preferred language
- **Automatic Translation**: No manual translation work required from providers
- **Consistent Experience**: All users see content in their preferred language

The implementation follows the same patterns as the Location, Services, and Profile translation systems and integrates seamlessly with the existing translation infrastructure.
