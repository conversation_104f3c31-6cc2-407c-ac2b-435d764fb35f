/**
 * Test script to verify case-insensitive status normalization
 */

// Import the normalization function
const { normalizeAppointmentStatus } = require('./src/provider/mobile/utils/validationUtils.ts');

// Test cases
const testCases = [
  { input: 'pending', expected: 'pending' },
  { input: 'PENDING', expected: 'pending' },
  { input: 'Pending', expected: 'pending' },
  { input: 'confirmed', expected: 'confirmed' },
  { input: 'CONFIRMED', expected: 'confirmed' },
  { input: 'inprogress', expected: 'InProgress' },
  { input: 'inProgress', expected: 'InProgress' },
  { input: 'INPROGRESS', expected: 'InProgress' },
  { input: 'InProgress', expected: 'InProgress' },
  { input: 'completed', expected: 'completed' },
  { input: 'COMPLETED', expected: 'completed' },
  { input: 'canceled', expected: 'canceled' },
  { input: 'cancelled', expected: 'canceled' },
  { input: 'CANCELLED', expected: 'canceled' },
  { input: 'noshow', expected: 'noshow' },
  { input: 'NOSHOW', expected: 'noshow' },
  { input: 'invalid', expected: null },
  { input: 'random', expected: null }
];

console.log('Testing status normalization...\n');

let passed = 0;
let failed = 0;

testCases.forEach(({ input, expected }, index) => {
  try {
    const result = normalizeAppointmentStatus(input);
    if (result === expected) {
      console.log(`✅ Test ${index + 1}: "${input}" → "${result}" (PASS)`);
      passed++;
    } else {
      console.log(`❌ Test ${index + 1}: "${input}" → "${result}" (expected "${expected}") (FAIL)`);
      failed++;
    }
  } catch (error) {
    console.log(`❌ Test ${index + 1}: "${input}" → Error: ${error.message} (FAIL)`);
    failed++;
  }
});

console.log(`\nResults: ${passed} passed, ${failed} failed`);

if (failed === 0) {
  console.log('🎉 All tests passed!');
  process.exit(0);
} else {
  console.log('💥 Some tests failed!');
  process.exit(1);
}
