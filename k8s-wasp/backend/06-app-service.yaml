apiVersion: v1
kind: Service
metadata:
  name: dalti-saas-api-service # Renamed from dalti-saas-app-service
  namespace: dalti
  labels:
    app: dalti-saas-api # Updated label
spec:
  selector:
    app: dalti-saas-api # Updated selector to match Deployment
  ports:
  - name: http
    port: 80 # Service listens on port 80
    protocol: TCP
    targetPort: 8080 # Forwards traffic to container port 8080 (where Wasp server runs)
  type: ClusterIP # Internal service, exposed via Ingress 