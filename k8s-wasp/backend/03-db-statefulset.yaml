apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: dalti-saas-postgres
  namespace: dalti
  labels:
    app: dalti-saas-postgres
spec:
  selector:
    matchLabels:
      app: dalti-saas-postgres
  serviceName: "dalti-saas-postgres" # Headless service for stable network identifiers
  replicas: 1
  template:
    metadata:
      labels:
        app: dalti-saas-postgres
    spec:
      # Add this securityContext for volume group ownership
      securityContext:
            runAsUser: 65534  # UID for nobody
            runAsGroup: 65534
      containers:
        - name: dalti-saas-postgres
          image: postgres:16 # Using PostgreSQL 16, adjust if needed
          ports:
            - containerPort: 5432
              name: postgresql
          env:
            - name: POSTGRES_DB
              valueFrom:
                configMapKeyRef:
                  name: dalti-saas-config # Reference the ConfigMap created earlier
                  key: POSTGRES_DB
            - name: POSTGRES_USER
              valueFrom:
                configMapKeyRef:
                  name: dalti-saas-config # Reference the ConfigMap created earlier
                  key: POSTGRES_USER
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: dalti-saas-secrets # Reference the Secret created earlier
                  key: POSTGRES_PASSWORD
            # - name: PGDATA # Define the data directory within the container
            #   value: /var/lib/postgresql/data/pgdata
          resources:
            requests:
              memory: "512Mi"
              cpu: "250m"
            limits:
              memory: "1Gi"
              cpu: "500m"
          volumeMounts:
            - name: dalti-postgres-storage # Mount the persistent volume
              mountPath: /var/lib/postgresql/data
              readOnly: false
          # securityContext: # Optional: If needed for your storage permissions
          #   runAsUser: 999 # Often the UID for postgres user inside the container
          #   runAsGroup: 999 # Often the GID for postgres group inside the container
          #   fsGroup: 999
  volumeClaimTemplates:
  - metadata:
      name: dalti-postgres-storage
    spec:
      accessModes: [ "ReadWriteOnce" ] # Suitable for StatefulSet with single replica
      resources:
        requests:
          storage: 5Gi # Increased storage slightly from example
      storageClassName: nfs-sc # Using the storage class from your example 