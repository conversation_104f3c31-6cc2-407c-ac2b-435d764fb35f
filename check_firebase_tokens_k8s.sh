#!/bin/bash

# Firebase Token Database Checker Script for Kubernetes Production
# This script helps you inspect Firebase FCM tokens in your production PostgreSQL database

echo "🔥 Firebase Token Database Inspector (Kubernetes Production)"
echo "============================================================"

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    echo "❌ kubectl is not installed or not in PATH"
    exit 1
fi

# Check if we can connect to the cluster
if ! kubectl cluster-info &> /dev/null; then
    echo "❌ Cannot connect to Kubernetes cluster. Check your kubeconfig."
    exit 1
fi

echo "✅ Connected to Kubernetes cluster"

# Find the PostgreSQL pod
POSTGRES_POD=$(kubectl get pods -n dalti -l app=dalti-saas-postgres -o jsonpath='{.items[0].metadata.name}' 2>/dev/null)

if [ -z "$POSTGRES_POD" ]; then
    echo "❌ No PostgreSQL pod found in 'dalti' namespace. Check if the database is running."
    echo "   Try: kubectl get pods -n dalti"
    exit 1
fi

echo "✅ Found PostgreSQL pod: $POSTGRES_POD"

# Database credentials (from your Kubernetes secrets - base64 decoded)
DB_USER="daltiuser"
DB_NAME="daltidb"
# Note: In production, you should get the password from the secret
# DB_PASSWORD can be retrieved with: kubectl get secret dalti-saas-secrets -n dalti -o jsonpath='{.data.POSTGRES_PASSWORD}' | base64 -d

echo "📊 Database: $DB_NAME"
echo ""

# Function to run SQL queries in Kubernetes
run_k8s_query() {
    kubectl exec -n dalti $POSTGRES_POD -- psql -U $DB_USER -d $DB_NAME -c "$1" 2>/dev/null
}

# 1. Check if we can connect to the database
echo "🔗 Testing database connection..."
CONNECTION_TEST=$(run_k8s_query "SELECT 1;" 2>&1)
if [[ $CONNECTION_TEST == *"ERROR"* ]] || [[ $CONNECTION_TEST == *"FATAL"* ]]; then
    echo "❌ Cannot connect to database. Error: $CONNECTION_TEST"
    echo "   You may need to provide the database password or check permissions."
    exit 1
fi
echo "✅ Database connection successful"
echo ""

# 2. Check total FCM tokens
echo "📱 FCM Token Summary:"
echo "===================="
run_k8s_query "SELECT COUNT(*) as total_tokens FROM \"UserDeviceToken\";"
echo ""

# 3. Check tokens by device type
echo "📊 Tokens by Device Type:"
echo "========================="
run_k8s_query "SELECT \"deviceType\", COUNT(*) as count FROM \"UserDeviceToken\" GROUP BY \"deviceType\";"
echo ""

# 4. Check unique users with tokens
echo "👥 Users with FCM Tokens:"
echo "========================="
run_k8s_query "SELECT COUNT(DISTINCT \"userId\") as unique_users_with_tokens FROM \"UserDeviceToken\";"
echo ""

# 5. Check user details with token counts
echo "👤 User Details with Token Counts:"
echo "=================================="
run_k8s_query "SELECT 
    COALESCE(u.\"firstName\", 'N/A') as first_name,
    COALESCE(u.\"lastName\", 'N/A') as last_name,
    COALESCE(u.\"email\", 'N/A') as email,
    COALESCE(u.\"mobileNumber\", 'N/A') as mobile,
    COUNT(udt.\"id\") as token_count,
    STRING_AGG(DISTINCT udt.\"deviceType\", ', ') as device_types
FROM \"User\" u 
JOIN \"UserDeviceToken\" udt ON u.\"id\" = udt.\"userId\" 
GROUP BY u.\"id\", u.\"firstName\", u.\"lastName\", u.\"email\", u.\"mobileNumber\" 
ORDER BY token_count DESC
LIMIT 10;"
echo ""

# 6. Check recent tokens
echo "🕒 Recent FCM Tokens (Last 10):"
echo "==============================="
run_k8s_query "SELECT 
    \"deviceType\",
    LEFT(\"token\", 30) || '...' as token_preview,
    \"createdAt\"::date as created_date,
    \"updatedAt\"::date as updated_date
FROM \"UserDeviceToken\" 
ORDER BY \"createdAt\" DESC 
LIMIT 10;"
echo ""

# 7. Check notifications
echo "🔔 Notification Summary:"
echo "======================="
run_k8s_query "SELECT COUNT(*) as total_notifications FROM \"Notification\";"
echo ""

# 8. Check recent notifications
echo "📬 Recent Notifications (Last 5):"
echo "================================="
run_k8s_query "SELECT 
    LEFT(\"title\", 40) || '...' as title_preview,
    \"type\",
    \"isRead\",
    \"createdAt\"::date as created_date
FROM \"Notification\" 
ORDER BY \"createdAt\" DESC 
LIMIT 5;"
echo ""

# 9. Check token age distribution
echo "📅 Token Age Distribution:"
echo "========================="
run_k8s_query "SELECT 
    CASE 
        WHEN \"createdAt\" > NOW() - INTERVAL '1 day' THEN 'Last 24 hours'
        WHEN \"createdAt\" > NOW() - INTERVAL '7 days' THEN 'Last week'
        WHEN \"createdAt\" > NOW() - INTERVAL '30 days' THEN 'Last month'
        ELSE 'Older than 30 days'
    END as age_group,
    COUNT(*) as token_count
FROM \"UserDeviceToken\"
GROUP BY age_group
ORDER BY 
    CASE 
        WHEN age_group = 'Last 24 hours' THEN 1
        WHEN age_group = 'Last week' THEN 2
        WHEN age_group = 'Last month' THEN 3
        ELSE 4
    END;"
echo ""

# 10. Check for potential issues
echo "⚠️  Potential Issues Check:"
echo "=========================="

# Check for duplicate tokens
DUPLICATE_COUNT=$(run_k8s_query "SELECT COUNT(*) FROM (SELECT \"token\", COUNT(*) FROM \"UserDeviceToken\" GROUP BY \"token\" HAVING COUNT(*) > 1) as duplicates;" | grep -o '[0-9]*' | head -1)
if [ "$DUPLICATE_COUNT" -gt 0 ]; then
    echo "❌ Found $DUPLICATE_COUNT duplicate tokens"
else
    echo "✅ No duplicate tokens found"
fi

# Check for very old tokens (might be stale)
OLD_TOKEN_COUNT=$(run_k8s_query "SELECT COUNT(*) FROM \"UserDeviceToken\" WHERE \"createdAt\" < NOW() - INTERVAL '90 days';" | grep -o '[0-9]*' | head -1)
if [ "$OLD_TOKEN_COUNT" -gt 0 ]; then
    echo "⚠️  Found $OLD_TOKEN_COUNT tokens older than 90 days (might be stale)"
else
    echo "✅ No very old tokens found"
fi

echo ""
echo "🎯 Production Quick Actions:"
echo "============================"
echo "To test Firebase setup: curl -H 'Authorization: Bearer YOUR_JWT' https://dapi.adscloud.org/api/auth/notifications/test/firebase-setup"
echo "To send test notification: curl -X POST -H 'Authorization: Bearer YOUR_JWT' -H 'Content-Type: application/json' -d '{\"title\":\"Test\",\"body\":\"Test message\"}' https://dapi.adscloud.org/api/auth/notifications/test/send"
echo ""
echo "📊 Kubernetes Info:"
echo "=================="
echo "Namespace: dalti"
echo "PostgreSQL Pod: $POSTGRES_POD"
echo "Database: $DB_NAME"
echo "User: $DB_USER"
echo ""
echo "✅ Production Firebase token inspection complete!"
